{"id": "672b34385bc90e11c98c4f1c", "lastUpdBy": "62bc5008d4958d013d97c7a6", "createUser": "<EMAIL>", "permissionActions": ["View", "Edit", "Delete", "Reset", "Start", "Stop"], "deduplicWriteMode": "intelligent", "desc": "", "increHysteresis": false, "increOperationMode": false, "increSyncConcurrency": false, "processorThreadNum": 1, "increaseReadSize": 1, "readBatchSize": 500, "writeBatchSize": 0, "writeBatchWaitMs": 0, "isAutoCreateIndex": true, "isFilter": false, "isOpenAutoDDL": false, "isSchedule": false, "isStopOnError": true, "name": "任务 41", "shareCdcEnable": false, "statuses": [], "status": "edit", "type": "initial_sync+cdc", "writeThreadSize": 8, "editVersion": "1730884692503", "syncType": "sync", "transformProcess": 0, "planStartDateFlag": false, "accessNodeType": "AUTOMATIC_PLATFORM_ALLOCATION", "accessNodeProcessIdList": [], "accessNodeProcessId": "", "transformUuid": "1730884692525", "transformed": true, "transformDagHash": 0, "enforceShareCdc": true, "pageVersion": "1730884653975", "dag": {"edges": [{"disabled": false, "source": "4afcabd5-0966-4111-8039-dc9dddfe24a6", "target": "513aea8b-1373-499d-9306-ab1e0fd3f7ac"}, {"disabled": false, "source": "6ce9ad39-aed0-44e1-a112-446cb7cafccc", "target": "513aea8b-1373-499d-9306-ab1e0fd3f7ac"}], "nodes": [{"tableName": "POLICY", "isFilter": false, "maxTransactionDuration": 12, "existDataProcessMode": "keepData", "xmlIncludeFile": false, "esFragmentNum": 3, "nodeConfig": {"hashSplit": false, "maxSplit": 20, "batchReadThreadSize": 4, "maximumQueueSize": 800}, "cdcMode": "logCdc", "cdcPollingFields": [{"field": "", "defaultValue": ""}], "cdcPollingInterval": 500, "cdcPollingBatchSize": 1000, "enableCustomCommand": false, "incrementExactlyOnceEnableTimeWindowDay": 3, "connectionId": "6641e5fcdea40f2b5753b2e2", "databaseType": "Mysql", "ddlConfiguration": "FILTER", "readBatchSize": 100, "increaseReadSize": 1, "writeStrategy": "updateOrInsert", "noPkSyncMode": "ADD_HASH", "type": "table", "catalog": "data", "isTransformed": false, "alarmSettings": [{"type": "DATANODE", "open": true, "key": "DATANODE_AVERAGE_HANDLE_CONSUME", "sort": 4, "notify": ["SYSTEM", "EMAIL"], "interval": 300, "unit": "SECOND"}], "alarmRules": [{"key": "DATANODE_AVERAGE_HANDLE_CONSUME", "point": 12, "equalsFlag": 1, "ms": 5000}], "id": "4afcabd5-0966-4111-8039-dc9dddfe24a6", "name": "POLICY", "elementType": "Node", "attrs": {"position": [-397, 225], "connectionName": "mysql local INSURANCE", "connectionType": "source_and_target", "accessNodeProcessId": "", "pdkType": "pdk", "pdkHash": "a5af410b12afca476edf4a650c133ddf135bf76542a67787ed6f7f7d53ba712", "capabilities": [{"type": 11, "id": "run_raw_command_function"}, {"type": 11, "id": "execute_command_function"}, {"type": 11, "id": "batch_read_function"}, {"type": 11, "id": "stream_read_function"}, {"type": 11, "id": "batch_count_function"}, {"type": 11, "id": "timestamp_to_stream_offset_function"}, {"type": 11, "id": "write_record_function"}, {"type": 11, "id": "query_by_advance_filter_function"}, {"type": 11, "id": "create_table_v2_function"}, {"type": 11, "id": "clear_table_function"}, {"type": 11, "id": "drop_table_function"}, {"type": 11, "id": "create_index_function"}, {"type": 11, "id": "query_indexes_function"}, {"type": 11, "id": "alter_field_attributes_function"}, {"type": 11, "id": "alter_field_name_function"}, {"type": 11, "id": "drop_field_function"}, {"type": 11, "id": "new_field_function"}, {"type": 11, "id": "count_by_partition_filter_function"}, {"type": 11, "id": "transaction_begin_function"}, {"type": 11, "id": "transaction_commit_function"}, {"type": 11, "id": "transaction_rollback_function"}, {"type": 11, "id": "get_table_info_function"}, {"type": 11, "id": "get_table_names_function"}, {"type": 11, "id": "error_handle_function"}, {"type": 20, "id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"type": 20, "id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"type": 20, "id": "api_server_supported"}, {"type": 20, "id": "source_incremental_update_event_have_before"}, {"type": 20, "id": "illegal_date_acceptable"}, {"type": 20, "id": "batch_read_hash_split"}, {"type": 10, "id": "new_field_event"}, {"type": 10, "id": "alter_field_name_event"}, {"type": 10, "id": "alter_field_attributes_event"}, {"type": 10, "id": "drop_field_event"}, {"type": 11, "id": "query_hash_by_advance_filter_function"}], "hasCreated": false, "accessNodeType": "AUTOMATIC_PLATFORM_ALLOCATION"}, "disabled": false}, {"mergeProperties": [{"mergeType": "updateOrInsert", "tableName": "POLICY", "id": "4afcabd5-0966-4111-8039-dc9dddfe24a6", "children": [{"mergeType": "updateWrite", "joinKeys": [{"source": "CUSTOMER_ID", "target": "CUSTOMER_ID"}], "tableName": "CUSTOMER", "targetPath": "CUSTOMER", "id": "6ce9ad39-aed0-44e1-a112-446cb7cafccc", "children": [], "isArray": false, "enableUpdateJoinKeyValue": false}], "isArray": false, "enableUpdateJoinKeyValue": false}], "mergeMode": "main_table_first", "concurrentNum": 2, "type": "merge_table_processor", "catalog": "processor", "isTransformed": false, "alarmSettings": [{"type": "PROCESSNODE", "open": true, "key": "PROCESSNODE_AVERAGE_HANDLE_CONSUME", "sort": 1, "notify": ["SYSTEM", "EMAIL"], "interval": 300, "unit": "SECOND"}], "alarmRules": [{"key": "PROCESSNODE_AVERAGE_HANDLE_CONSUME", "point": 60, "equalsFlag": 1, "ms": 30000}], "externalStorageId": "662383ddd73b211d2753257a", "id": "513aea8b-1373-499d-9306-ab1e0fd3f7ac", "name": "主从合并", "elementType": "Node", "attrs": {"position": [-117, 225]}, "disabled": false}, {"tableName": "CUSTOMER", "isFilter": false, "maxTransactionDuration": 12, "existDataProcessMode": "keepData", "xmlIncludeFile": false, "esFragmentNum": 3, "nodeConfig": {"hashSplit": false, "maxSplit": 20, "batchReadThreadSize": 4, "maximumQueueSize": 800}, "cdcMode": "logCdc", "cdcPollingFields": [{"field": "", "defaultValue": ""}], "cdcPollingInterval": 500, "cdcPollingBatchSize": 1000, "enableCustomCommand": false, "incrementExactlyOnceEnableTimeWindowDay": 3, "connectionId": "6641e5fcdea40f2b5753b2e2", "databaseType": "Mysql", "ddlConfiguration": "FILTER", "readBatchSize": 100, "increaseReadSize": 1, "writeStrategy": "updateOrInsert", "noPkSyncMode": "ADD_HASH", "type": "table", "catalog": "data", "isTransformed": false, "alarmSettings": [{"type": "DATANODE", "open": true, "key": "DATANODE_AVERAGE_HANDLE_CONSUME", "sort": 4, "notify": ["SYSTEM", "EMAIL"], "interval": 300, "unit": "SECOND"}], "alarmRules": [{"key": "DATANODE_AVERAGE_HANDLE_CONSUME", "point": 12, "equalsFlag": 1, "ms": 5000}], "id": "6ce9ad39-aed0-44e1-a112-446cb7cafccc", "name": "CUSTOMER", "elementType": "Node", "attrs": {"position": [-397, 314.5], "connectionName": "mysql local INSURANCE", "connectionType": "source_and_target", "accessNodeProcessId": "", "pdkType": "pdk", "pdkHash": "a5af410b12afca476edf4a650c133ddf135bf76542a67787ed6f7f7d53ba712", "capabilities": [{"type": 11, "id": "run_raw_command_function"}, {"type": 11, "id": "execute_command_function"}, {"type": 11, "id": "batch_read_function"}, {"type": 11, "id": "stream_read_function"}, {"type": 11, "id": "batch_count_function"}, {"type": 11, "id": "timestamp_to_stream_offset_function"}, {"type": 11, "id": "write_record_function"}, {"type": 11, "id": "query_by_advance_filter_function"}, {"type": 11, "id": "create_table_v2_function"}, {"type": 11, "id": "clear_table_function"}, {"type": 11, "id": "drop_table_function"}, {"type": 11, "id": "create_index_function"}, {"type": 11, "id": "query_indexes_function"}, {"type": 11, "id": "alter_field_attributes_function"}, {"type": 11, "id": "alter_field_name_function"}, {"type": 11, "id": "drop_field_function"}, {"type": 11, "id": "new_field_function"}, {"type": 11, "id": "count_by_partition_filter_function"}, {"type": 11, "id": "transaction_begin_function"}, {"type": 11, "id": "transaction_commit_function"}, {"type": 11, "id": "transaction_rollback_function"}, {"type": 11, "id": "get_table_info_function"}, {"type": 11, "id": "get_table_names_function"}, {"type": 11, "id": "error_handle_function"}, {"type": 20, "id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"type": 20, "id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"type": 20, "id": "api_server_supported"}, {"type": 20, "id": "source_incremental_update_event_have_before"}, {"type": 20, "id": "illegal_date_acceptable"}, {"type": 20, "id": "batch_read_hash_split"}, {"type": 10, "id": "new_field_event"}, {"type": 10, "id": "alter_field_name_event"}, {"type": 10, "id": "alter_field_attributes_event"}, {"type": 10, "id": "drop_field_event"}, {"type": 11, "id": "query_hash_by_advance_filter_function"}], "hasCreated": false, "accessNodeType": "AUTOMATIC_PLATFORM_ALLOCATION"}, "disabled": false}]}, "shareCache": false, "canOpenInspect": false, "isAutoInspect": false, "creator": "<EMAIL>", "showInspectTips": false, "alarmSettings": [{"type": "TASK", "open": true, "key": "TASK_STATUS_ERROR", "sort": 1, "notify": ["SYSTEM", "EMAIL"], "interval": 300, "unit": "SECOND"}, {"type": "TASK", "open": true, "key": "TASK_FULL_COMPLETE", "sort": 3, "notify": ["SYSTEM"], "interval": 300, "unit": "SECOND"}, {"type": "TASK", "open": true, "key": "TASK_INCREMENT_START", "sort": 4, "notify": ["SYSTEM"], "interval": 300, "unit": "SECOND"}, {"type": "TASK", "open": true, "key": "TASK_INCREMENT_DELAY", "sort": 6, "notify": ["SYSTEM"], "interval": 300, "unit": "SECOND"}], "alarmRules": [{"key": "TASK_INCREMENT_DELAY", "point": 60, "equalsFlag": 1, "ms": 60000}], "testTaskId": "672b34385bc90e11c98c4f1a", "transformTaskId": "672b34385bc90e11c98c4f1b", "stopRetryTimes": 0, "delayTime": 0, "doubleActive": false, "oldVersionTimezone": false, "timeDifference": 0, "autoInspect": false, "testTask": false, "cdctask": false, "snapShotInterrupt": false, "previewTask": false, "deduceSchemaTask": false, "normalTask": true, "_deleted": false, "createTime": "2024-11-06T09:17:44.445+00:00", "last_updated": "2024-11-06T09:18:12.682+00:00", "user_id": "62bc5008d4958d013d97c7a6"}