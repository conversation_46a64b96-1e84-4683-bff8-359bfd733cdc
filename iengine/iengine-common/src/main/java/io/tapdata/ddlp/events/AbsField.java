package io.tapdata.ddlp.events;

import io.tapdata.ddlp.DDLOperator;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * DDL事件 - 列操作
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2021/12/11 下午6:04 Create
 */
@Setter
@Getter
public abstract class AbsField extends AbsStruct {
	private String name;

	protected AbsField() {
	}

	protected AbsField(DDLOperator op, String ddl, List<String> namespace, String name) {
		super(op, ddl, namespace);
		setName(name);
	}
}
