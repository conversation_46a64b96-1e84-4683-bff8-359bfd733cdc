package io.tapdata.flow.engine.V2.util;

import com.hazelcast.config.Config;
import com.hazelcast.persistence.ConstructType;
import com.hazelcast.persistence.PersistenceStorage;
import com.hazelcast.persistence.config.*;
import com.hazelcast.persistence.store.StoreLogger;
import com.mongodb.ConnectionString;
import com.tapdata.constant.ConnectorConstant;
import com.tapdata.constant.MongodbUtil;
import com.tapdata.constant.OsUtil;
import com.tapdata.entity.Connections;
import com.tapdata.mongo.ClientMongoOperator;
import com.tapdata.tm.commons.dag.Edge;
import com.tapdata.tm.commons.dag.Node;
import com.tapdata.tm.commons.dag.logCollector.HazelCastImdgNode;
import com.tapdata.tm.commons.dag.logCollector.LogCollectorNode;
import com.tapdata.tm.commons.dag.nodes.CacheNode;
import com.tapdata.tm.commons.dag.nodes.DataParentNode;
import com.tapdata.tm.commons.dag.nodes.DatabaseNode;
import com.tapdata.tm.commons.dag.nodes.TableNode;
import com.tapdata.tm.commons.externalStorage.ExternalStorageDto;
import com.tapdata.tm.commons.externalStorage.ExternalStorageType;
import com.tapdata.tm.commons.task.dto.TaskDto;
import io.tapdata.error.ExternalStorageExCode_26;
import io.tapdata.exception.TapCodeException;
import io.tapdata.flow.engine.V2.node.NodeTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.json.JsonWriterSettings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * <AUTHOR>
 * @Description
 * @create 2022-09-14 10:19
 **/
public class ExternalStorageUtil {
	public static final String EXTERNAL_STORAGE_TABLE_NAME_PREFIX = "ExternalStorage_";
	private final static String LOG_PREFIX = "[Hazelcast IMDG Persistence] - ";
	private static final Logger logger = LogManager.getLogger(ExternalStorageUtil.class);
	public static final int DEFAULT_IN_MEM_SIZE = 1000;
	public static final String DEFAULT_MAX_SIZE_POLICY = "USED_HEAP_SIZE";
	public static final int DEFAULT_WRITE_DELAY_SECONDS = 0;

	public synchronized static void initHZMapStorage(ExternalStorageDto externalStorageDto, String referenceId, String name, Config config) {
		addConfig(externalStorageDto, ConstructType.IMAP, name);
		try {
			PersistenceStorage.getInstance().initMapStoreConfig(referenceId, config, name);
			logger.info("Init IMap store config succeed, name: " + name);
		} catch (Exception e) {
			throw new RuntimeException(LOG_PREFIX + "Init hazelcast IMap persistence failed. " + e.getMessage(), e);
		}
	}

	public synchronized static void initHZRingBufferStorage(ExternalStorageDto externalStorageDto, String referenceId, String name, Config config, StoreLogger storeLogger) {
		addConfig(externalStorageDto, ConstructType.RINGBUFFER, name, storeLogger);
		try {
			PersistenceStorage.getInstance().initRingBufferConfig(referenceId, config, name);
			logger.info("Init RingBuffer store config succeed, name: " + name);
		} catch (Exception e) {
			throw new RuntimeException(LOG_PREFIX + "Init hazelcast RingBuffer persistence failed. " + e.getMessage(), e);
		}
	}

	public synchronized static void initHZRingBufferStorage(ExternalStorageDto externalStorageDto, String referenceId, String name, Config config, PersistenceStorage.SequenceMode sequenceMode, StoreLogger storeLogger) {
		addConfig(externalStorageDto, ConstructType.RINGBUFFER, name, storeLogger);
		try {
			PersistenceStorage.getInstance().initRingBufferConfig(referenceId, config, name, sequenceMode);
			logger.info("Init RingBuffer store config succeed, name: " + name);
		} catch (Exception e) {
			throw new RuntimeException(LOG_PREFIX + "Init hazelcast RingBuffer persistence failed. " + e.getMessage(), e);
		}
	}

	public synchronized static void initStateMap(ExternalStorageDto externalStorageDto, String referenceId, String name, Config config) {
		addConfig(externalStorageDto, ConstructType.RINGBUFFER, name);
		try {
			PersistenceStorage.getInstance().initMapStoreConfig(referenceId, config, name);
			logger.info("Init state IMap store config succeed, name: " + name);
		} catch (Exception e) {
			throw new RuntimeException(LOG_PREFIX + "Init hazelcast RingBuffer persistence failed. " + e.getMessage(), e);
		}
	}

	private static void addConfig(ExternalStorageDto externalStorageDto, ConstructType constructType, String constructName) {
		addConfig(externalStorageDto, constructType, constructName, null);
	}

	private static void addConfig(ExternalStorageDto externalStorageDto, ConstructType constructType, String constructName, StoreLogger storeLogger) {
		if (null == externalStorageDto) throw new IllegalArgumentException("External storage dto cannot be null");
		PersistenceStorageAbstractConfig persistenceConfig = getPersistenceConfig(externalStorageDto, constructType, constructName);
		if (null != storeLogger) {
			persistenceConfig.setLogger(storeLogger);
		}
		checkConfigOverrideAndLogger(persistenceConfig);
		PersistenceStorage.getInstance().addConfig(persistenceConfig);
		logger.info("Added hazelcast persistence config: " + persistenceConfig);
	}

	private static PersistenceStorageAbstractConfig getPersistenceConfig(ExternalStorageDto externalStorageDto, ConstructType constructType, String constructName) {
		PersistenceStorageAbstractConfig persistenceStorageAbstractConfig;
		ExternalStorageType externalStorageType;
		try {
			externalStorageType = ExternalStorageType.valueOf(externalStorageDto.getType());
		} catch (IllegalArgumentException e) {
			throw new RuntimeException("Nonsupport external storage type: " + externalStorageDto.getType());
		}
		// Set properties
		switch (externalStorageType) {
			case memory:
				persistenceStorageAbstractConfig = PersistenceInMemConfig.create(constructType, constructName);
				break;
			case mongodb:
				persistenceStorageAbstractConfig = getMongoDBConfig(externalStorageDto, constructType, constructName);
				break;
			case rocksdb:
				persistenceStorageAbstractConfig = getRocksDBConfig(externalStorageDto, constructType, constructName);
				break;
			case httptm:
				persistenceStorageAbstractConfig = getHttpTMConfig(externalStorageDto, constructType, constructName);
				break;
			default:
				throw new RuntimeException("Nonsupport external storage type: " + externalStorageDto.getType());
		}
		persistenceStorageAbstractConfig.setMaxSizePolicy(StringUtils.isEmpty(externalStorageDto.getMaxSizePolicy()) ? DEFAULT_MAX_SIZE_POLICY : externalStorageDto.getMaxSizePolicy());
		persistenceStorageAbstractConfig.setInMemSize(externalStorageDto.getInMemSize() != null ? externalStorageDto.getInMemSize() : DEFAULT_IN_MEM_SIZE);
		persistenceStorageAbstractConfig.setWriteDelaySeconds(externalStorageDto.getWriteDelaySeconds() != null ? externalStorageDto.getWriteDelaySeconds() : DEFAULT_WRITE_DELAY_SECONDS);
		return persistenceStorageAbstractConfig;
	}

	private static PersistenceRocksDBConfig getRocksDBConfig(ExternalStorageDto externalStorageDto, ConstructType constructType, String constructName) {
		String rocksdbPath = externalStorageDto.getUri();
		if (StringUtils.isBlank(rocksdbPath)) {
			throw new IllegalArgumentException(LOG_PREFIX + "Init hazelcast persist config failed. RocksDB path cannot be empty");
		}
		String tapdataWorkDir = System.getenv("TAPDATA_WORK_DIR");
		if (com.tapdata.manager.common.utils.StringUtils.isBlank(tapdataWorkDir)) {
			tapdataWorkDir = System.getProperty("user.dir");
		}
		rocksdbPath = tapdataWorkDir + rocksdbPath;
		if (OsUtil.isWindows()) {
			rocksdbPath = rocksdbPath.replace("/","\\");

		}
		File file = new File(rocksdbPath);
		file.getParentFile().mkdirs();
		PersistenceRocksDBConfig rocksDBConfig = PersistenceRocksDBConfig.create(constructType, constructName)
				.path(rocksdbPath);
		return rocksDBConfig;
	}

	private static PersistenceMongoDBConfig getMongoDBConfig(ExternalStorageDto externalStorageDto, ConstructType constructType, String constructName) {
		String uri = externalStorageDto.getUri();
		ConnectionString mongoClientURI;
		try {
			mongoClientURI = MongodbUtil.verifyMongoDBUriWithDB(uri);
		} catch (Exception e) {
			throw new IllegalArgumentException(LOG_PREFIX + "Init hazelcast persistence failed" + e.getMessage());
		}
		String table = externalStorageDto.getTable();
		boolean exclusiveCollection = false;
		if (StringUtils.isBlank(table)) {
			table = EXTERNAL_STORAGE_TABLE_NAME_PREFIX + constructName;
			exclusiveCollection = true;
		}
		if (StringUtils.isBlank(table)) {
			throw new IllegalArgumentException(LOG_PREFIX + "Init hazelcast persistence failed. Collection name cannot be empty");
		}
		PersistenceMongoDBConfig mongoDBConfig = PersistenceMongoDBConfig.create(constructType, constructName)
				.uri(uri)
				.database(mongoClientURI.getDatabase())
				.collection(table)
				.exclusiveCollection(exclusiveCollection)
				.ssl(externalStorageDto.isSsl())
				.sslCA(externalStorageDto.getSslCA())
				.sslKey(externalStorageDto.getSslKey())
				.sslPass(externalStorageDto.getSslPass())
				.sslValidate(externalStorageDto.isSslValidate())
				.checkServerIdentity(externalStorageDto.isCheckServerIdentity());
		return mongoDBConfig;
	}

	private static PersistenceHttpConfig getHttpTMConfig(ExternalStorageDto externalStorageDto, ConstructType constructType, String constructName) {
		if (CollectionUtils.isEmpty(externalStorageDto.getBaseURLs())) {
			throw new RuntimeException(LOG_PREFIX + "Base url cannot be empty");
		}
		if (StringUtils.isBlank(externalStorageDto.getAccessToken())) {
			throw new IllegalArgumentException(LOG_PREFIX + "Access token cannot be empty");
		}
		PersistenceHttpConfig httpConfig = PersistenceHttpConfig.create(constructType, constructName, externalStorageDto.getBaseURLs(), externalStorageDto.getAccessToken())
				.connectTimeoutMs(externalStorageDto.getConnectTimeoutMs())
				.readTimeoutMs(externalStorageDto.getReadTimeoutMs());
		return httpConfig;
	}

	private static void checkConfigOverrideAndLogger(PersistenceStorageAbstractConfig persistenceStorageAbstractConfig) {
		PersistenceStorage persistenceStorage = PersistenceStorage.getInstance();
		PersistenceStorageAbstractConfig existingConfig = persistenceStorage.getPersistenceStorageConfig(persistenceStorageAbstractConfig.getConstructType(), persistenceStorageAbstractConfig.getName());
		if (null != existingConfig && !persistenceStorageAbstractConfig.equals(existingConfig)) {
			logger.info(LOG_PREFIX + "Existing persistence config will be override\n old: " + existingConfig + "\n new: " + persistenceStorageAbstractConfig);
		}
	}

	public static Map<String, ExternalStorageDto> getExternalStorageMap(TaskDto taskDto, ClientMongoOperator clientMongoOperator) {
		Map<String, ExternalStorageDto> externalStorageDtoMap = new HashMap<>();
		com.tapdata.tm.commons.dag.DAG dag = taskDto.getDag();
		List<Node> nodes = dag.getNodes();
		if (CollectionUtils.isEmpty(nodes)) {
			logger.warn(String.format("Init external storage config failed. Task [%s] not have any node", taskDto.getName()));
			return externalStorageDtoMap;
		}
		Set<String> ids = new HashSet<>();
		String syncType = taskDto.getSyncType();
		switch (syncType) {
			case TaskDto.SYNC_TYPE_SYNC:
			case TaskDto.SYNC_TYPE_MIGRATE:
				for (Node node : nodes) {
					if (StringUtils.isNotBlank(node.getExternalStorageId())) {
						ids.add(node.getExternalStorageId());
					}
				}
				break;
			case TaskDto.SYNC_TYPE_LOG_COLLECTOR:
				Node logCollectorNode = nodes.stream().filter(node -> node.getType().equals(NodeTypeEnum.LOG_COLLECTOR.type)).findFirst().orElse(null);
				if (null == logCollectorNode) {
					logger.warn("Init external storage config failed. Not found log collector node in task");
					break;
				}
				if (logCollectorNode instanceof LogCollectorNode) {
					List<String> connectionIds = ((LogCollectorNode) logCollectorNode).getConnectionIds();
					if (CollectionUtils.isNotEmpty(connectionIds)) {
						String connectionId = connectionIds.get(0);
						Query connQuery = Query.query(where("_id").is(connectionId));
						connQuery.fields().include("_id").include("shareCDCExternalStorageId");
						Connections connection = clientMongoOperator.findOne(connQuery, ConnectorConstant.CONNECTION_COLLECTION, Connections.class);
						String shareCDCExternalStorageId = connection.getShareCDCExternalStorageId();
						if (StringUtils.isNotBlank(shareCDCExternalStorageId)) {
							ids.add(shareCDCExternalStorageId);
						}
					}
				}
				break;
			default:
				break;
		}
		Criteria criteria = new Criteria().orOperator(
				// Get system inner config with constant name. Reference: manager/tm/src/main/resources/init/idaas/2.10-1.json
				where("name").is(ConnectorConstant.TAPDATA_MONGO_DB_EXTERNAL_STORAGE_NAME),
				// Get default config
				where("defaultStorage").is(true),
				where("_id").in(ids)
		);
		List<ExternalStorageDto> externalStorageDtoList = clientMongoOperator.find(Query.query(criteria), ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
		if (CollectionUtils.isEmpty(externalStorageDtoList)) {
			throw new RuntimeException(String.format("Not found any external storage config: %s", criteria.getCriteriaObject().toJson(JsonWriterSettings.builder().indent(true).build())));
		}
		logger.info("Task init external storage configs completed: {}", externalStorageDtoList.stream().map(ExternalStorageDto::getName).collect(Collectors.joining(",")));
		externalStorageDtoMap = externalStorageDtoList.stream().collect(Collectors.toMap(e -> e.getId().toHexString(), e -> e));
		return externalStorageDtoMap;
	}

	public static ExternalStorageDto getExternalStorage(
			Map<String, ExternalStorageDto> externalStorageDtoMap,
			Node node,
			ClientMongoOperator clientMongoOperator,
			List<Node> nodes,
			Connections connections
	) {
		ExternalStorageDto externalStorageDto;
		if (MapUtils.isEmpty(externalStorageDtoMap)) {
			throw new RuntimeException("External storage map cannot be empty");
		}
		if (node instanceof TableNode || node instanceof DatabaseNode || node instanceof LogCollectorNode) {
			if (null == connections) {
				throw new RuntimeException("Init node " + node.getName() + "(id: " + node.getId() + ", type: " + node.getClass().getSimpleName() + ") external storage failed, connection is null");
			}
			externalStorageDto = getPdkStateMapExternalStorage(externalStorageDtoMap, node, connections);
		} else if (node instanceof HazelCastImdgNode) {
			externalStorageDto = getShareCDCExternalStorage(externalStorageDtoMap, node, clientMongoOperator, nodes);
		} else {
			externalStorageDto = getExternalStorageDto(externalStorageDtoMap, node);
		}
		if (null == externalStorageDto) {
			externalStorageDto = getDefaultExternalStorage(externalStorageDtoMap, node);
		}
		return externalStorageDto;
	}

	public static ExternalStorageDto getTargetNodeExternalStorage(
			Node node,
			List<Edge> edges,
			ClientMongoOperator clientMongoOperator,
			List<Node> nodes
	){
		ExternalStorageDto externalStorageDto = new ExternalStorageDto();
		Edge targetEdge = edges.stream().filter(e -> e.getSource().equals(node.getId())).findFirst().orElse(null);
		if(targetEdge != null){
			Node targetNode = nodes.stream().filter(n -> n.getId().equals(targetEdge.getTarget())).findFirst().orElse(null);
			if(targetNode instanceof DataParentNode){
				String connectionId = ((DataParentNode) targetNode).getConnectionId();
				Query connQuery = Query.query(Criteria.where("_id").is(connectionId));
				Connections connection = clientMongoOperator.findOne(connQuery, ConnectorConstant.CONNECTION_COLLECTION, Connections.class);
				if(connection.getDatabase_type().equals("MongoDB") || connection.getDatabase_type().equals("MongoDB Atlas")){
					Map<String, Object> config =connection.getConfig();
					String uri = MongodbUtil.getUri(config);
					externalStorageDto.setName(ConnectorConstant.TARGET_MONGO_DB_EXTERNAL_STORAGE_NAME);
					externalStorageDto.setUri(uri);
					externalStorageDto.setType("mongodb");
					if(connection.getSsl()){
						externalStorageDto.setSsl(connection.getSsl());
						externalStorageDto.setSslCA(connection.getSslCA());
						externalStorageDto.setSslKey(connection.getSslKey());
						externalStorageDto.setSslPass(connection.getSslPass());
						externalStorageDto.setSslValidate(connection.getSslValidate());
					}
				}
			}
		}
		return externalStorageDto;
	}


	public static ExternalStorageDto getExternalStorage(Node node) {
		ClientMongoOperator clientMongoOperator = ConnectorConstant.clientMongoOperator;
		return getExternalStorage(node, null, clientMongoOperator, null);
	}

	public static ExternalStorageDto getExternalStorage(
			@NotNull Node node,
			List<Node> nodes,
			@NotNull ClientMongoOperator clientMongoOperator,
			Connections connections
	) {
		ExternalStorageDto externalStorageDto;
		if (node instanceof TableNode || node instanceof DatabaseNode || node instanceof LogCollectorNode) {
			if (null == connections) {
				throw new RuntimeException("Init node " + node.getName() + "(id: " + node.getId() + ", type: " + node.getClass().getSimpleName() + ") external storage failed, connection is null");
			}
			externalStorageDto = getPdkStateMapExternalStorage(node, connections, clientMongoOperator);
		} else if (node instanceof HazelCastImdgNode) {
			externalStorageDto = getShareCDCExternalStorage(node, nodes, clientMongoOperator);
		} else if (node instanceof CacheNode) {
			externalStorageDto = getShareCacheExternalStorageDto(node, clientMongoOperator);
		} else {
			externalStorageDto = getExternalStorageDto(node, clientMongoOperator);
		}
		if (null == externalStorageDto) {
			externalStorageDto = getDefaultExternalStorage(node, clientMongoOperator);
		}
		return externalStorageDto;
	}

	private static ExternalStorageDto getShareCacheExternalStorageDto(@NotNull Node node, @NotNull ClientMongoOperator clientMongoOperator) {
		ExternalStorageDto externalStorageDto = getExternalStorageDto(node, clientMongoOperator);
		if (externalStorageDto != null && node instanceof CacheNode) {
			externalStorageDto.setInMemSize(((CacheNode) node).getMaxMemory());
			externalStorageDto.setMaxSizePolicy(DEFAULT_MAX_SIZE_POLICY);
		}
		return externalStorageDto;
	}

	@Nullable
	private static ExternalStorageDto getDefaultExternalStorage(
			@NotNull Map<String, ExternalStorageDto> externalStorageDtoMap,
			@NotNull Node node
	) {
		ExternalStorageDto externalStorageDto;
		externalStorageDto = externalStorageDtoMap.values().stream().filter(ExternalStorageDto::isDefaultStorage).findFirst().orElse(null);
		if (null == externalStorageDto) {
			externalStorageDto = externalStorageDtoMap.values().stream().filter(e -> e.getName().equals(ConnectorConstant.TAPDATA_MONGO_DB_EXTERNAL_STORAGE_NAME)).findFirst().orElse(null);
		}
		if (null != externalStorageDto) {
			logger.info("Node {}(id: {}, type: {}) use default external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
		}
		return externalStorageDto;
	}

	@Nullable
	private static ExternalStorageDto getDefaultExternalStorage(
			@NotNull Node node,
			@NotNull ClientMongoOperator clientMongoOperator
	) {
		ExternalStorageDto externalStorageDto;
		Query query = Query.query(where("defaultStorage").is(true));
		externalStorageDto = clientMongoOperator.findOne(query, ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
		if (null == externalStorageDto) {
			query = Query.query(where("name").is(ConnectorConstant.TAPDATA_MONGO_DB_EXTERNAL_STORAGE_NAME));
			externalStorageDto = clientMongoOperator.findOne(query, ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
		}
		if (null != externalStorageDto) {
			logger.info("Node {}(id: {}, type: {}) use default external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
		}
		return externalStorageDto;
	}

	@Nullable
	private static ExternalStorageDto getExternalStorageDto(
			@NotNull Map<String, ExternalStorageDto> externalStorageDtoMap,
			@NotNull Node node
	) {
		ExternalStorageDto externalStorageDto = null;
		String externalStorageId = node.getExternalStorageId();
		if (StringUtils.isNotBlank(externalStorageId)) {
			externalStorageDto = externalStorageDtoMap.get(externalStorageId);
			if (null != externalStorageDto) {
				logger.info("Node {}(id: {}, type: {}) use external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
			}
		}
		return externalStorageDto;
	}

	@Nullable
	private static ExternalStorageDto getExternalStorageDto(
			@NotNull Node node,
			@NotNull ClientMongoOperator clientMongoOperator
	) {
		ExternalStorageDto externalStorageDto = null;
		String externalStorageId = node.getExternalStorageId();
		if (StringUtils.isNotBlank(externalStorageId)) {
			Query query = Query.query(where("_id").is(externalStorageId));
			externalStorageDto = clientMongoOperator.findOne(query, ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
			if (null != externalStorageDto) {
				logger.info("Node {}(id: {}, type: {}) use external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
			}
		}
		return externalStorageDto;
	}

	@Nullable
	private static ExternalStorageDto getShareCDCExternalStorage(
			@NotNull Map<String, ExternalStorageDto> externalStorageDtoMap,
			@NotNull Node node,
			@NotNull ClientMongoOperator clientMongoOperator,
			List<Node> nodes
	) {
		ExternalStorageDto externalStorageDto = null;
		// Find source log collector node
		if (CollectionUtils.isEmpty(nodes)) {
			throw new RuntimeException(String.format("Init node %s(id: %s, type: %s) external storage failed, node list is empty", node.getName(), node.getId(), node.getClass().getSimpleName()));
		}
		Node logCollectorNode = nodes.stream().filter(n -> n instanceof LogCollectorNode).findFirst().orElse(null);
		if (logCollectorNode instanceof LogCollectorNode) {
			// Get the external storage config of the pre-log-collector node
			List<String> connectionIds = ((LogCollectorNode) logCollectorNode).getConnectionIds();
			if (CollectionUtils.isNotEmpty(connectionIds)) {
				String connectionId = connectionIds.get(0);
				Query connQuery = Query.query(Criteria.where("_id").is(connectionId));
				connQuery.fields().include("_id").include("shareCDCExternalStorageId");
				Connections logCollectorNodeConn = clientMongoOperator.findOne(connQuery, ConnectorConstant.CONNECTION_COLLECTION, Connections.class);
				if (null != logCollectorNodeConn && StringUtils.isNotBlank(logCollectorNodeConn.getShareCDCExternalStorageId())) {
					externalStorageDto = externalStorageDtoMap.get(logCollectorNodeConn.getShareCDCExternalStorageId());
					if (null != externalStorageDto) {
						logger.info("Node {}(id: {}, type: {}) use external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
					}
				}
			}
		}
		return externalStorageDto;
	}

	@Nullable
	private static ExternalStorageDto getShareCDCExternalStorage(
			@NotNull Node node,
			List<Node> nodes,
			@NotNull ClientMongoOperator clientMongoOperator
	) {
		ExternalStorageDto externalStorageDto = null;
		// Find source log collector node
		if (CollectionUtils.isEmpty(nodes)) {
			throw new RuntimeException(String.format("Init node %s(id: %s, type: %s) external storage failed, node list is empty", node.getName(), node.getId(), node.getClass().getSimpleName()));
		}
		Node logCollectorNode = nodes.stream().filter(n -> n instanceof LogCollectorNode).findFirst().orElse(null);
		if (logCollectorNode instanceof LogCollectorNode) {
			// Get the external storage config of the pre-log-collector node
			List<String> connectionIds = ((LogCollectorNode) logCollectorNode).getConnectionIds();
			if (CollectionUtils.isNotEmpty(connectionIds)) {
				String connectionId = connectionIds.get(0);
				Query connQuery = Query.query(Criteria.where("_id").is(connectionId));
				connQuery.fields().include("_id").include("shareCDCExternalStorageId");
				Connections logCollectorNodeConn = clientMongoOperator.findOne(connQuery, ConnectorConstant.CONNECTION_COLLECTION, Connections.class);
				if (null != logCollectorNodeConn && StringUtils.isNotBlank(logCollectorNodeConn.getShareCDCExternalStorageId())) {
					Query query = Query.query(where("_id").is(logCollectorNodeConn.getShareCDCExternalStorageId()));
					externalStorageDto = clientMongoOperator.findOne(query, ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
					if (null != externalStorageDto) {
						logger.info("Node {}(id: {}, type: {}) use external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
					}
				}
			}
		}
		return externalStorageDto;
	}

	public static ExternalStorageDto getPdkStateMapExternalStorage(
			@NotNull Map<String, ExternalStorageDto> externalStorageDtoMap,
			@NotNull Node node,
			@NotNull Connections connections
	) {
		ExternalStorageDto externalStorageDto = null;
		if (node instanceof TableNode || node instanceof DatabaseNode || node instanceof LogCollectorNode) {
			if ("pdk".equals(connections.getPdkType())) {
				// External storage for pdk
				externalStorageDto = externalStorageDtoMap.values().stream().filter(e -> e.getName().equals(ConnectorConstant.TAPDATA_MONGO_DB_EXTERNAL_STORAGE_NAME)).findFirst().orElse(null);
			}
			if (null != externalStorageDto) {
				logger.info("Node {}(id: {}, type: {}) use external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
			}
		}
		return externalStorageDto;
	}

	public static ExternalStorageDto getPdkStateMapExternalStorage(
			@NotNull Node node,
			@NotNull Connections connections,
			@NotNull ClientMongoOperator clientMongoOperator
	) {
		ExternalStorageDto externalStorageDto = null;
		if (node instanceof TableNode || node instanceof DatabaseNode || node instanceof LogCollectorNode) {
			if ("pdk".equals(connections.getPdkType())) {
				// External storage for pdk
				Query query = Query.query(where("name").is(ConnectorConstant.TAPDATA_MONGO_DB_EXTERNAL_STORAGE_NAME));
				externalStorageDto = clientMongoOperator.findOne(query, ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
			}
			if (null != externalStorageDto) {
				logger.info("Node {}(id: {}, type: {}) use external storage config: {}", node.getName(), node.getId(), node.getClass().getSimpleName(), externalStorageDto.getName());
			}
		}
		return externalStorageDto;
	}

	public static ExternalStorageDto getDefaultExternalStorage() {
		ClientMongoOperator clientMongoOperator = ConnectorConstant.clientMongoOperator;
		Query query = Query.query(where("defaultStorage").is(true));
		return clientMongoOperator.findOne(query, ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
	}

	public static ExternalStorageDto getTapdataOrDefaultExternalStorage() {
		ClientMongoOperator clientMongoOperator = ConnectorConstant.clientMongoOperator;
		if (null == clientMongoOperator) {
			throw new TapCodeException(ExternalStorageExCode_26.UNKNOWN_ERROR, "Get tapdata or default external storage failed, client mongo operator is null");
		}
		Query query = Query.query(new Criteria().orOperator(
				where("name").is(ConnectorConstant.TAPDATA_MONGO_DB_EXTERNAL_STORAGE_NAME),
				where("defaultStorage").is(true)
		));
		List<ExternalStorageDto> externalStorages = clientMongoOperator.find(query, ConnectorConstant.EXTERNAL_STORAGE_COLLECTION, ExternalStorageDto.class);
		if (CollectionUtils.isEmpty(externalStorages)) {
			throw new TapCodeException(ExternalStorageExCode_26.CANNOT_FOUND_EXTERNAL_STORAGE_CONFIG, "Query: " + query.getQueryObject().toJson());
		}
		return externalStorages.stream().filter(e -> ConnectorConstant.TAPDATA_MONGO_DB_EXTERNAL_STORAGE_NAME.equals(e.getName())).findFirst()
				.orElse(externalStorages.stream().filter(ExternalStorageDto::isDefaultStorage).findFirst().orElse(null));
	}
}
