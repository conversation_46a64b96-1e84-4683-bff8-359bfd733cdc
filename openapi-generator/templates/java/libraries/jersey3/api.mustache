package {{package}};

import {{invokerPackage}}.ApiException;
import {{invokerPackage}}.ApiClient;
import {{invokerPackage}}.ApiResponse;
import {{invokerPackage}}.Configuration;
import {{invokerPackage}}.Pair;

import {{javaxPackage}}.ws.rs.core.GenericType;

{{#imports}}import {{import}};
{{/imports}}

{{#useBeanValidation}}
import jakarta.validation.constraints.*;
import jakarta.validation.Valid;

{{/useBeanValidation}}
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

{{>generatedAnnotation}}
{{#operations}}
public class {{classname}} {
  private ApiClient apiClient;

  public {{classname}}() {
    this(Configuration.getDefaultApiClient());
  }

  public {{classname}}(ApiClient apiClient) {
    this.apiClient = apiClient;
  }

  /**
   * Get the API client
   *
   * @return API client
   */
  public ApiClient getApiClient() {
    return apiClient;
  }

  /**
   * Set the API client
   *
   * @param apiClient an instance of API client
   */
  public void setApiClient(ApiClient apiClient) {
    this.apiClient = apiClient;
  }

  {{#operation}}
  {{^vendorExtensions.x-group-parameters}}
  /**
   * {{summary}}
   * {{notes}}
   {{#allParams}}
   * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{#defaultValue}}, default to {{.}}{{/defaultValue}}){{/required}}
   {{/allParams}}
   {{#returnType}}
   * @return {{.}}
   {{/returnType}}
   * @throws ApiException if fails to make API call
   {{#responses.0}}
   * @http.response.details
     <table border="1">
       <caption>Response Details</caption>
       <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
       {{#responses}}
       <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
       {{/responses}}
     </table>
   {{/responses.0}}
   {{#isDeprecated}}
   * @deprecated
   {{/isDeprecated}}
   {{#externalDocs}}
   * {{description}}
   * @see <a href="{{url}}">{{summary}} Documentation</a>
   {{/externalDocs}}
   */
  {{#isDeprecated}}
  @Deprecated
  {{/isDeprecated}}
  public {{#returnType}}{{{.}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{operationId}}({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException {
    {{#returnType}}return {{/returnType}}{{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}}){{#returnType}}.getData(){{/returnType}};
  }
  {{/vendorExtensions.x-group-parameters}}

  {{^vendorExtensions.x-group-parameters}}
  /**
   * {{summary}}
   * {{notes}}
   {{#allParams}}
   * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{#defaultValue}}, default to {{.}}{{/defaultValue}}){{/required}}
   {{/allParams}}
   * @return ApiResponse&lt;{{returnType}}{{^returnType}}Void{{/returnType}}&gt;
   * @throws ApiException if fails to make API call
   {{#responses.0}}
   * @http.response.details
     <table border="1">
       <caption>Response Details</caption>
       <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
       {{#responses}}
       <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
       {{/responses}}
     </table>
   {{/responses.0}}
   {{#isDeprecated}}
   * @deprecated
   {{/isDeprecated}}
   {{#externalDocs}}
   * {{description}}
   * @see <a href="{{url}}">{{summary}} Documentation</a>
   {{/externalDocs}}
   */
  {{#isDeprecated}}
  @Deprecated
  {{/isDeprecated}}
  public{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}private{{/vendorExtensions.x-group-parameters}} ApiResponse<{{{returnType}}}{{^returnType}}Void{{/returnType}}> {{operationId}}WithHttpInfo({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException {
    {{#hasRequiredParams}}
    // Check required parameters
    {{#allParams}}
    {{#required}}
    if ({{paramName}} == null) {
      throw new ApiException(400, "Missing the required parameter '{{paramName}}' when calling {{operationId}}");
    }
    {{/required}}
    {{/allParams}}

    {{/hasRequiredParams}}
    {{#hasPathParams}}
    // Path parameters
    String localVarPath = "{{{path}}}"{{#pathParams}}
            .replaceAll({{=% %=}}"\\{%baseName%}"%={{ }}=%, apiClient.escapeString({{{paramName}}}.toString())){{/pathParams}};

    {{/hasPathParams}}
    {{#queryParams}}
    {{#-first}}
    // Query parameters
    List<Pair> localVarQueryParams = new ArrayList<>(
            apiClient.parameterToPairs("{{{collectionFormat}}}", "{{baseName}}", {{paramName}})
    );
    {{/-first}}
    {{^-first}}
    localVarQueryParams.addAll(apiClient.parameterToPairs("{{{collectionFormat}}}", "{{baseName}}", {{paramName}}));
    {{/-first}}
    {{#-last}}

    {{/-last}}
    {{/queryParams}}
    {{#headerParams}}
    {{#-first}}
    // Header parameters
    Map<String, String> localVarHeaderParams = new LinkedHashMap<>();
    {{/-first}}
    {{^required}}if ({{paramName}} != null) {
      {{/required}}localVarHeaderParams.put("{{baseName}}", apiClient.parameterToString({{paramName}}));{{^required}}
    }{{/required}}
    {{#-last}}

    {{/-last}}
    {{/headerParams}}
    {{#cookieParams}}
    {{#-first}}
    // Cookie parameters
    Map<String, String> localVarCookieParams = new LinkedHashMap<>();
    {{/-first}}
    {{^required}}if ({{paramName}} != null) {
      {{/required}}localVarCookieParams.put("{{baseName}}", apiClient.parameterToString({{paramName}}));{{^required}}
    }{{/required}}
    {{#-last}}

    {{/-last}}
    {{/cookieParams}}
    {{#formParams}}
    {{#-first}}
    // Form parameters
    Map<String, Object> localVarFormParams = new LinkedHashMap<>();
    {{/-first}}
    {{^required}}if ({{paramName}} != null) {
      {{/required}}localVarFormParams.put("{{baseName}}", {{paramName}});{{^required}}
    }{{/required}}
    {{#-last}}

    {{/-last}}
    {{/formParams}}
    String localVarAccept = apiClient.selectHeaderAccept({{#produces}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/produces}});
    String localVarContentType = apiClient.selectHeaderContentType({{#consumes}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/consumes}});
    {{#hasAuthMethods}}
    String[] localVarAuthNames = {{=% %=}}new String[] {%#authMethods%"%name%"%^-last%, %/-last%%/authMethods%};%={{ }}=%
    {{/hasAuthMethods}}
    {{#returnType}}
    GenericType<{{{returnType}}}> localVarReturnType = new GenericType<{{{returnType}}}>() {};
    {{/returnType}}
    return apiClient.invokeAPI("{{classname}}.{{operationId}}", {{#hasPathParams}}localVarPath{{/hasPathParams}}{{^hasPathParams}}"{{{path}}}"{{/hasPathParams}}, "{{httpMethod}}", {{#queryParams}}{{#-first}}localVarQueryParams{{/-first}}{{/queryParams}}{{^queryParams}}new ArrayList<>(){{/queryParams}}, {{#bodyParam}}{{paramName}}{{/bodyParam}}{{^bodyParam}}null{{/bodyParam}},
                               {{#headerParams}}{{#-first}}localVarHeaderParams{{/-first}}{{/headerParams}}{{^headerParams}}new LinkedHashMap<>(){{/headerParams}}, {{#cookieParams}}{{#-first}}localVarCookieParams{{/-first}}{{/cookieParams}}{{^cookieParams}}new LinkedHashMap<>(){{/cookieParams}}, {{#formParams}}{{#-first}}localVarFormParams{{/-first}}{{/formParams}}{{^formParams}}new LinkedHashMap<>(){{/formParams}}, localVarAccept, localVarContentType,
                               {{#hasAuthMethods}}localVarAuthNames{{/hasAuthMethods}}{{^hasAuthMethods}}null{{/hasAuthMethods}}, {{#returnType}}localVarReturnType{{/returnType}}{{^returnType}}null{{/returnType}}, {{#bodyParam}}{{#isNullable}}true{{/isNullable}}{{^isNullable}}false{{/isNullable}}{{/bodyParam}}{{^bodyParam}}false{{/bodyParam}});
  }
  {{#vendorExtensions.x-group-parameters}}

  public class API{{operationId}}Request {
    {{#allParams}}
    {{>nullable_var_annotations}}
    private {{{dataType}}} {{paramName}};
    {{/allParams}}

    private API{{operationId}}Request({{#pathParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/pathParams}}) {
      {{#pathParams}}
      this.{{paramName}} = {{paramName}};
      {{/pathParams}}
    }
    {{#allParams}}
    {{^isPathParam}}

    /**
     * Set {{paramName}}
     * @param {{paramName}} {{description}} ({{^required}}optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}{{/required}}{{#required}}required{{/required}})
     * @return API{{operationId}}Request
     */
    public API{{operationId}}Request {{paramName}}({{>nullable_var_annotations}} {{{dataType}}} {{paramName}}) {
      this.{{paramName}} = {{paramName}};
      return this;
    }
    {{/isPathParam}}
    {{/allParams}}

    /**
     * Execute {{operationId}} request
     {{#returnType}}* @return {{.}}{{/returnType}}
     * @throws ApiException if fails to make API call
     {{#responses.0}}
     * @http.response.details
       <table border="1">
       <caption>Response Details</caption>
         <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
         {{#responses}}
         <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
         {{/responses}}
       </table>
     {{/responses.0}}
     {{#isDeprecated}}* @deprecated{{/isDeprecated}}
     */
    {{#isDeprecated}}@Deprecated{{/isDeprecated}}
    public {{{returnType}}}{{^returnType}}void{{/returnType}} execute() throws ApiException {
      {{#returnType}}return {{/returnType}}this.executeWithHttpInfo().getData();
    }

    /**
     * Execute {{operationId}} request with HTTP info returned
     * @return ApiResponse&lt;{{returnType}}{{^returnType}}Void{{/returnType}}&gt;
     * @throws ApiException if fails to make API call
     {{#responses.0}}
     * @http.response.details
       <table border="1">
       <caption>Response Details</caption>
         <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
         {{#responses}}
         <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
         {{/responses}}
       </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated{{/isDeprecated}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public ApiResponse<{{{returnType}}}{{^returnType}}Void{{/returnType}}> executeWithHttpInfo() throws ApiException {
      return {{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});
    }
  }

  /**
   * {{summary}}
   * {{notes}}{{#pathParams}}
   * @param {{paramName}} {{description}} (required){{/pathParams}}
   * @return {{operationId}}Request
   * @throws ApiException if fails to make API call
   {{#isDeprecated}}* @deprecated{{/isDeprecated}}
   {{#externalDocs}}* {{description}}
   * @see <a href="{{url}}">{{summary}} Documentation</a>{{/externalDocs}}
   */
  {{#isDeprecated}}
  @Deprecated
  {{/isDeprecated}}
  public API{{operationId}}Request {{operationId}}({{#pathParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/pathParams}}) throws ApiException {
    return new API{{operationId}}Request({{#pathParams}}{{paramName}}{{^-last}}, {{/-last}}{{/pathParams}});
  }
  {{/vendorExtensions.x-group-parameters}}
  {{/operation}}
}
{{/operations}}
