.jtk-connector {
  cursor: pointer;
  z-index: 4;
  &.jtk-hover,
  &.jtk-dragging {
    z-index: 1000;
  }
  &.running path:not(:last-child) {
    animation-name: running;
    animation-duration: 2s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    stroke-dasharray: 5;
  }
  @keyframes running {
    from {
      stroke-dashoffset: 50;
    }
    to {
      stroke-dashoffset: 0;
    }
  }

  &.connection-disabled {
    $color: rgb(201 205 212 / 50%);
    z-index: 3;
    path:nth-child(2) {
      stroke: $color;
    }
    path:nth-child(3) {
      fill: $color;
      stroke: $color;
    }
  }
}

.jtk-endpoint {
  z-index: 5;
  &.jtk-hover {
    z-index: 1001;
  }
  &.sourcePoint {
    cursor: crosshair;
  }
  &.dropHover {
    transform: scale(1.5);
  }
}

.jtk-overlay {
  z-index: 6;
}

.jtk-drag-select {
  user-select: none;
}
