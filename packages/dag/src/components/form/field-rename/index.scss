.field-processors-tree-warp {
  .clickable {
    color: map.get($color, primary);
  }
  .disable__btn {
    background-color: map.get($bgColor, disable);
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }
  .active__delete {
    color: map.get($color, disable);
  }
  .field-type {
    width: 100px;
  }
  .active__name {
    color: map.get($color, primary);
  }
  .active__name__inner {
    .el-input__inner {
      color: map.get($color, primary);
    }
  }
  .active__type {
    .el-input__inner {
      color: map.get($color, primary);
    }
  }
  .check-all {
    padding-left: 24px;
    margin-right: 8px;
  }
  .field-name {
    flex: 1;
  }

  .field-ops {
    width: 100px;
    text-align: right;
    margin-right: 24px;
  }
  .tree-node {
    //line-height: 43px;
  }
  .field-processor-operation {
    height: 40px;
    line-height: 40px;
    color: #4e5969;
    background-color: #fafafa;
  }
  .field-processor-tree {
    .tree-field-input-wrap {
      position: relative;
    }
    .tree-field-input-primary {
      input {
        color: map.get($color, primary);
      }
    }
    //.tree-field-input .el-input__inner:hover{
    //  border: 1px solid;
    //  border-color: #eceef1
    //}
    .tree-field-input .el-input__wrapper {
      box-shadow: none;
      &:focus-within {
        box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
      }
    }
    .el-tree-node__content {
      height: 50px;
      cursor: pointer;
      border-top: 1px solid;
      border-color: #eceef1;
      border-radius: 0;
    }
    .el-tree-node__expand-icon {
      height: 38px;
    }
    .el-input__prefix,
    .el-input__suffix .el-select__caret {
      color: map.get($color, primary);
    }
    .e-select {
      width: 100px;
    }
    .e-desc {
      width: 100px;
    }
    .e-ops {
      width: 100px;
      text-align: right;
      margin-right: 24px;
    }
    .item {
      width: 32%;
    }
    .e-label {
      flex: 1;
    }
    .e-triangle {
      width: 0;
      height: 0;
      border-right: 5px solid transparent;
      border-left: 5px solid transparent;
      border-bottom: 5px solid transparent;

      -webkit-transform: rotate(-45deg);
      -moz-transform: rotate(-45deg);
      -ms-transform: rotate(-45deg);
      -o-transform: rotate(-45deg);
      transform: rotate(-45deg);

      position: relative;
      left: -3px;
      top: -11px;
    }
    .el-icon-more {
      -webkit-transform: rotate(90deg);
      -moz-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
      -o-transform: rotate(90deg);
      transform: rotate(90deg);
    }
  }

  .invalid-operations-wrap {
    border-style: dashed !important;
  }
}
