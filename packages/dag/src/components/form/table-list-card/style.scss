.table-list-card {
  display: flex;
  flex-direction: column;
  //height: 100px;
  min-height: 100px;
  max-height: calc((100vh - 120px) * 0.618);
  overflow: hidden;
  .el-card__header {
    padding: 8px 16px;
    background: #fafafa;
    color: #333c4a;
    font-size: 13px;
    font-weight: 500;
  }

  .el-card__body {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 0;
    padding: 0;
    position: relative;
    .el-loading-parent--relative {
      position: static !important;
    }
    .table-list-item {
      height: 32px;
      line-height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &:hover {
        background-color: var(--fill-hover);
      }
    }
  }
}
