<template>
  <div class="paper-empty flex flex-column justify-center align-center">
    <svg-animate style="width: 640px" class="opacity-50" trigger="loop">
      <svg
        viewBox="0 0 640 440"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:anim="http://www.w3.org/2000/anim"
        anim=""
        anim:transform-origin="50% 50%"
        anim:duration="0"
        anim:ease="power1.inOut"
      >
        <g id="e0adfdab">
          <g clip-path="url(#7196809a)">
            <rect x="20" y="16" width="600" height="400" rx="8" fill="#F1F2F4"></rect>
            <g id="23d09c1a">
              <path d="M20 16H620V50H20V16Z" fill="white"></path>
              <path d="M20 50H166V416H20V50Z" fill="white"></path>
            </g>
            <rect
              id="89819d08"
              x="32.5"
              y="137.5"
              width="121"
              height="27"
              rx="7.5"
              fill="white"
              stroke="#2C65FF"
            ></rect>
            <rect id="bf39a331" x="32.5" y="88.5" width="121" height="27" rx="7.5" fill="white" stroke="#2C65FF"></rect>
            <rect
              id="2da55362"
              x="32.5"
              y="88.5"
              width="121"
              height="27"
              rx="7.5"
              fill="white"
              stroke="#2C65FF"
              anim=""
              anim:opacity="0|1"
              anim:x="0|400"
              anim:y="0|100"
              anim:delay="1"
              anim:duration="1.3"
            ></rect>
            <rect
              id="03314740"
              x="32.5"
              y="88.5"
              width="121"
              height="27"
              rx="7.5"
              fill="white"
              stroke="#2C65FF"
              anim=""
              anim:opacity="1|1"
              anim:x="0|200"
              anim:y="0|100"
              anim:delay="0"
              anim:duration="0.8"
            ></rect>
            <rect id="5e942723" x="49" y="26" width="87" height="16" rx="8" fill="#F1F2F4"></rect>
            <g
              id="1c901ad0"
              anim=""
              anim:opacity="1|0"
              anim:x="0|200"
              anim:y="0|100"
              anim:delay="0"
              anim:duration="0.8"
            >
              <g id="11e9d912" filter="url(#9f807c3c)">
                <path
                  id="1a34deb7"
                  d="M99.5701 96.7798C100.05 96.5998 101 96.7098 101.25 97.2498C101.5 97.7898 101.65 98.4898 101.66 98.3198C101.641 97.8031 101.685 97.286 101.79 96.7798C101.901 96.4556 102.156 96.201 102.48 96.0898C102.777 95.9958 103.093 95.9752 103.4 96.0298C103.711 96.0937 103.985 96.2723 104.17 96.5298C104.404 97.113 104.536 97.732 104.56 98.3598C104.585 97.8241 104.676 97.2934 104.83 96.7798C104.997 96.5444 105.241 96.3746 105.52 96.2998C105.851 96.2394 106.19 96.2394 106.52 96.2998C106.791 96.3899 107.029 96.5609 107.2 96.7898C107.412 97.32 107.541 97.8801 107.58 98.4498C107.58 98.5898 107.65 98.0598 107.87 97.7098C108.047 97.1852 108.615 96.9031 109.14 97.0798C109.665 97.2566 109.947 97.8252 109.77 98.3498C109.77 98.9998 109.77 98.9698 109.77 99.4098C109.77 99.8498 109.77 100.24 109.77 100.61C109.734 101.195 109.654 101.777 109.53 102.35C109.357 102.857 109.114 103.338 108.81 103.78C108.325 104.32 107.923 104.93 107.62 105.59C107.546 105.918 107.512 106.254 107.52 106.59C107.519 106.9 107.559 107.21 107.64 107.51C107.231 107.554 106.819 107.554 106.41 107.51C106.02 107.45 105.54 106.67 105.41 106.43C105.346 106.301 105.214 106.22 105.07 106.22C104.926 106.22 104.794 106.301 104.73 106.43C104.51 106.81 104.02 107.5 103.73 107.54C103.06 107.62 101.67 107.54 100.59 107.54C100.59 107.54 100.78 106.54 100.36 106.18C99.9401 105.82 99.5301 105.4 99.2201 105.12L98.3901 104.2C97.8048 103.657 97.3765 102.966 97.1501 102.2C96.9401 101.26 96.9601 100.81 97.1501 100.43C97.3439 100.116 97.6465 99.8846 98.0001 99.7798C98.2939 99.7265 98.5963 99.7472 98.8801 99.8398C99.0764 99.922 99.246 100.057 99.3701 100.23C99.6001 100.54 99.6801 100.69 99.5801 100.35C99.4801 100.01 99.2601 99.7598 99.1501 99.3498C98.9359 98.8656 98.8074 98.348 98.7701 97.8198C98.8111 97.346 99.1419 96.9474 99.6001 96.8198"
                  fill="white"
                ></path>
                <path
                  id="848097ee"
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M99.5701 96.7798C100.05 96.5998 101 96.7098 101.25 97.2498C101.5 97.7898 101.65 98.4898 101.66 98.3198C101.641 97.8031 101.685 97.286 101.79 96.7798C101.901 96.4556 102.156 96.201 102.48 96.0898C102.777 95.9958 103.093 95.9752 103.4 96.0298C103.711 96.0937 103.985 96.2723 104.17 96.5298C104.404 97.113 104.536 97.732 104.56 98.3598C104.585 97.8241 104.676 97.2934 104.83 96.7798C104.997 96.5444 105.241 96.3746 105.52 96.2998C105.851 96.2394 106.19 96.2394 106.52 96.2998C106.791 96.3899 107.029 96.5609 107.2 96.7898C107.412 97.32 107.541 97.8801 107.58 98.4498C107.58 98.5898 107.65 98.0598 107.87 97.7098C108.047 97.1852 108.615 96.9031 109.14 97.0798C109.665 97.2566 109.947 97.8252 109.77 98.3498C109.77 98.9998 109.77 98.9698 109.77 99.4098C109.77 99.8498 109.77 100.24 109.77 100.61C109.734 101.195 109.654 101.777 109.53 102.35C109.357 102.857 109.114 103.338 108.81 103.78C108.325 104.32 107.923 104.93 107.62 105.59C107.546 105.918 107.512 106.254 107.52 106.59C107.519 106.9 107.559 107.21 107.64 107.51C107.231 107.554 106.819 107.554 106.41 107.51C106.02 107.45 105.54 106.67 105.41 106.43C105.346 106.301 105.214 106.22 105.07 106.22C104.926 106.22 104.794 106.301 104.73 106.43C104.51 106.81 104.02 107.5 103.73 107.54C103.06 107.62 101.67 107.54 100.59 107.54C100.59 107.54 100.78 106.54 100.36 106.18C99.9401 105.82 99.5301 105.4 99.2201 105.12L98.3901 104.2C97.8048 103.657 97.3765 102.966 97.1501 102.2C96.9401 101.26 96.9601 100.81 97.1501 100.43C97.3439 100.116 97.6465 99.8846 98.0001 99.7798C98.2939 99.7265 98.5963 99.7472 98.8801 99.8398C99.0764 99.922 99.246 100.057 99.3701 100.23C99.6001 100.54 99.6801 100.69 99.5801 100.35C99.4801 100.01 99.2601 99.7598 99.1501 99.3498C98.9359 98.8656 98.8074 98.348 98.7701 97.8198C98.7905 97.3391 99.1107 96.9229 99.5701 96.7798Z"
                  stroke="black"
                  stroke-width="0.75"
                  stroke-linejoin="round"
                ></path>
                <path
                  id="e2e6b77a"
                  d="M107.32 104.456V101.004C107.32 100.797 107.152 100.63 106.945 100.63C106.738 100.63 106.57 100.797 106.57 101.004V104.456C106.57 104.662 106.738 104.83 106.945 104.83C107.152 104.83 107.32 104.662 107.32 104.456Z"
                  fill="black"
                ></path>
                <path
                  id="8dcd3c59"
                  d="M105.34 104.455L105.32 101.001C105.319 100.795 105.15 100.629 104.943 100.63C104.736 100.631 104.569 100.799 104.57 101.005L104.59 104.459C104.592 104.665 104.76 104.831 104.967 104.83C105.175 104.829 105.342 104.661 105.34 104.455Z"
                  fill="black"
                ></path>
                <path
                  id="f95aba4e"
                  d="M102.57 101.01L102.59 104.454C102.592 104.663 102.76 104.831 102.968 104.83C103.175 104.829 103.342 104.659 103.34 104.45L103.32 101.005C103.319 100.797 103.15 100.629 102.943 100.63C102.736 100.631 102.569 100.801 102.57 101.01Z"
                  fill="black"
                ></path>
              </g>
            </g>
            <g
              id="92df7c80"
              anim=""
              anim:opacity="1|0"
              anim:x="0|200"
              anim:y="0|100"
              anim:delay="0"
              anim:duration="0.8"
            >
              <g id="dfebf662" filter="url(#80b9519f)">
                <path
                  id="e615967c"
                  d="M99.5701 96.7798C100.05 96.5998 101 96.7098 101.25 97.2498C101.5 97.7898 101.65 98.4898 101.66 98.3198C101.641 97.8031 101.685 97.286 101.79 96.7798C101.901 96.4556 102.156 96.201 102.48 96.0898C102.777 95.9958 103.093 95.9752 103.4 96.0298C103.711 96.0937 103.985 96.2723 104.17 96.5298C104.404 97.113 104.536 97.732 104.56 98.3598C104.585 97.8241 104.676 97.2934 104.83 96.7798C104.997 96.5444 105.241 96.3746 105.52 96.2998C105.851 96.2394 106.19 96.2394 106.52 96.2998C106.791 96.3899 107.029 96.5609 107.2 96.7898C107.412 97.32 107.541 97.8801 107.58 98.4498C107.58 98.5898 107.65 98.0598 107.87 97.7098C108.047 97.1852 108.615 96.9031 109.14 97.0798C109.665 97.2566 109.947 97.8252 109.77 98.3498C109.77 98.9998 109.77 98.9698 109.77 99.4098C109.77 99.8498 109.77 100.24 109.77 100.61C109.734 101.195 109.654 101.777 109.53 102.35C109.357 102.857 109.114 103.338 108.81 103.78C108.325 104.32 107.923 104.93 107.62 105.59C107.546 105.918 107.512 106.254 107.52 106.59C107.519 106.9 107.559 107.21 107.64 107.51C107.231 107.554 106.819 107.554 106.41 107.51C106.02 107.45 105.54 106.67 105.41 106.43C105.346 106.301 105.214 106.22 105.07 106.22C104.926 106.22 104.794 106.301 104.73 106.43C104.51 106.81 104.02 107.5 103.73 107.54C103.06 107.62 101.67 107.54 100.59 107.54C100.59 107.54 100.78 106.54 100.36 106.18C99.9401 105.82 99.5301 105.4 99.2201 105.12L98.3901 104.2C97.8048 103.657 97.3765 102.966 97.1501 102.2C96.9401 101.26 96.9601 100.81 97.1501 100.43C97.3439 100.116 97.6465 99.8846 98.0001 99.7798C98.2939 99.7265 98.5963 99.7472 98.8801 99.8398C99.0764 99.922 99.246 100.057 99.3701 100.23C99.6001 100.54 99.6801 100.69 99.5801 100.35C99.4801 100.01 99.2601 99.7598 99.1501 99.3498C98.9359 98.8656 98.8074 98.348 98.7701 97.8198C98.8111 97.346 99.1419 96.9474 99.6001 96.8198"
                  fill="white"
                ></path>
                <path
                  id="45b06523"
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M99.5701 96.7798C100.05 96.5998 101 96.7098 101.25 97.2498C101.5 97.7898 101.65 98.4898 101.66 98.3198C101.641 97.8031 101.685 97.286 101.79 96.7798C101.901 96.4556 102.156 96.201 102.48 96.0898C102.777 95.9958 103.093 95.9752 103.4 96.0298C103.711 96.0937 103.985 96.2723 104.17 96.5298C104.404 97.113 104.536 97.732 104.56 98.3598C104.585 97.8241 104.676 97.2934 104.83 96.7798C104.997 96.5444 105.241 96.3746 105.52 96.2998C105.851 96.2394 106.19 96.2394 106.52 96.2998C106.791 96.3899 107.029 96.5609 107.2 96.7898C107.412 97.32 107.541 97.8801 107.58 98.4498C107.58 98.5898 107.65 98.0598 107.87 97.7098C108.047 97.1852 108.615 96.9031 109.14 97.0798C109.665 97.2566 109.947 97.8252 109.77 98.3498C109.77 98.9998 109.77 98.9698 109.77 99.4098C109.77 99.8498 109.77 100.24 109.77 100.61C109.734 101.195 109.654 101.777 109.53 102.35C109.357 102.857 109.114 103.338 108.81 103.78C108.325 104.32 107.923 104.93 107.62 105.59C107.546 105.918 107.512 106.254 107.52 106.59C107.519 106.9 107.559 107.21 107.64 107.51C107.231 107.554 106.819 107.554 106.41 107.51C106.02 107.45 105.54 106.67 105.41 106.43C105.346 106.301 105.214 106.22 105.07 106.22C104.926 106.22 104.794 106.301 104.73 106.43C104.51 106.81 104.02 107.5 103.73 107.54C103.06 107.62 101.67 107.54 100.59 107.54C100.59 107.54 100.78 106.54 100.36 106.18C99.9401 105.82 99.5301 105.4 99.2201 105.12L98.3901 104.2C97.8048 103.657 97.3765 102.966 97.1501 102.2C96.9401 101.26 96.9601 100.81 97.1501 100.43C97.3439 100.116 97.6465 99.8846 98.0001 99.7798C98.2939 99.7265 98.5963 99.7472 98.8801 99.8398C99.0764 99.922 99.246 100.057 99.3701 100.23C99.6001 100.54 99.6801 100.69 99.5801 100.35C99.4801 100.01 99.2601 99.7598 99.1501 99.3498C98.9359 98.8656 98.8074 98.348 98.7701 97.8198C98.7905 97.3391 99.1107 96.9229 99.5701 96.7798Z"
                  stroke="black"
                  stroke-width="0.75"
                  stroke-linejoin="round"
                ></path>
                <path
                  id="d701ff56"
                  d="M107.32 104.456V101.004C107.32 100.797 107.152 100.63 106.945 100.63C106.738 100.63 106.57 100.797 106.57 101.004V104.456C106.57 104.662 106.738 104.83 106.945 104.83C107.152 104.83 107.32 104.662 107.32 104.456Z"
                  fill="black"
                ></path>
                <path
                  id="7c5f7fa9"
                  d="M105.34 104.455L105.32 101.001C105.319 100.795 105.15 100.629 104.943 100.63C104.736 100.631 104.569 100.799 104.57 101.005L104.59 104.459C104.592 104.665 104.76 104.831 104.967 104.83C105.175 104.829 105.342 104.661 105.34 104.455Z"
                  fill="black"
                ></path>
                <path
                  id="7a570e3a"
                  d="M102.57 101.01L102.59 104.454C102.592 104.663 102.76 104.831 102.968 104.83C103.175 104.829 103.342 104.659 103.34 104.45L103.32 101.005C103.319 100.797 103.15 100.629 102.943 100.63C102.736 100.631 102.569 100.801 102.57 101.01Z"
                  fill="black"
                ></path>
              </g>
            </g>
            <g id="54c14fef">
              <path
                d="M477 32C477 28.6863 479.686 26 483 26H491C494.314 26 497 28.6863 497 32C497 35.3137 494.314 38 491 38H483C479.686 38 477 35.3137 477 32Z"
                fill="#C9CDD4"
              ></path>
              <path
                d="M513 32C513 28.6863 515.686 26 519 26H527C530.314 26 533 28.6863 533 32C533 35.3137 530.314 38 527 38H519C515.686 38 513 35.3137 513 32Z"
                fill="#C9CDD4"
              ></path>
              <path
                d="M549 32C549 28.6863 551.686 26 555 26H563C566.314 26 569 28.6863 569 32C569 35.3137 566.314 38 563 38H555C551.686 38 549 35.3137 549 32Z"
                fill="#F3961A"
              ></path>
              <path
                d="M585 32C585 28.6863 587.686 26 591 26H599C602.314 26 605 28.6863 605 32C605 35.3137 602.314 38 599 38H591C587.686 38 585 35.3137 585 32Z"
                fill="#2C65FF"
              ></path>
            </g>
            <path
              id="736e7886"
              d="M38 27L31 34L37.5 40.5M274.65 40H279.55C283.416 40 286.55 36.866 286.55 33C286.55 29.134 283.416 26 279.55 26H274.65C270.784 26 267.65 29.134 267.65 33C267.65 36.866 270.784 40 274.65 40ZM317.55 40H322.45C326.316 40 329.45 36.866 329.45 33C329.45 29.134 326.316 26 322.45 26H317.55C313.684 26 310.55 29.134 310.55 33C310.55 36.866 313.684 40 317.55 40ZM360.45 40H365.35C369.216 40 372.35 36.866 372.35 33C372.35 29.134 369.216 26 365.35 26H360.45C356.584 26 353.45 29.134 353.45 33C353.45 36.866 356.584 40 360.45 40Z"
              stroke="#C9CDD4"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              id="172042fc"
              d="M353.5 203.25C353.086 203.25 352.75 203.586 352.75 204C352.75 204.414 353.086 204.75 353.5 204.75V203.25ZM431.5 204L424 199.67V208.33L431.5 204ZM353.5 204.75H424.75V203.25H353.5V204.75Z"
              fill="#86909C"
              anim=""
              anim:opacity="0|1"
              anim:scale="0.5|1"
              anim:delay="1.6"
              anim:duration="1.8"
            ></path>
          </g>
        </g>
        <defs>
          <filter
            id="7ed4d82b"
            x="0"
            y="0"
            width="640"
            height="440"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            ></feColorMatrix>
            <feOffset dy="4"></feOffset>
            <feGaussianBlur stdDeviation="10"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="out"></feComposite>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"></feColorMatrix>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8510_74651"></feBlend>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8510_74651" result="shape"></feBlend>
          </filter>
          <filter
            id="9f807c3c"
            x="95.825"
            y="95.625"
            width="15.1723"
            height="14.1252"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            ></feColorMatrix>
            <feOffset dy="1"></feOffset>
            <feGaussianBlur stdDeviation="0.4"></feGaussianBlur>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"></feColorMatrix>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8510_74651"></feBlend>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8510_74651" result="shape"></feBlend>
          </filter>
          <filter
            id="80b9519f"
            x="95.825"
            y="95.625"
            width="15.1723"
            height="14.1252"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            ></feColorMatrix>
            <feOffset dy="1"></feOffset>
            <feGaussianBlur stdDeviation="0.4"></feGaussianBlur>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"></feColorMatrix>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8510_74651"></feBlend>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8510_74651" result="shape"></feBlend>
          </filter>
          <clipPath id="7196809a">
            <rect x="20" y="16" width="600" height="400" rx="8" fill="white"></rect>
          </clipPath>
        </defs>
      </svg>
    </svg-animate>
  </div>
</template>

<script>
import '@figmania/webcomponent'
export default {
  name: 'PaperEmpty',
}
</script>

<style lang="scss" scoped>
.paper-empty {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  //background: #fff;

  .hotkey-list {
    font-size: 16px;
    text-align: center;
    line-height: 40px;
    color: #888;

    kbd {
      display: inline-block;
      width: 20px;
      height: 20px;
      padding: 0;
      line-height: 18px;
      font-size: 14px;
      font-family: Arial, monospace;
      text-align: center;
      background: rgba(150, 150, 150, 0.06);
      border: 1px solid rgba(100, 100, 100, 0.1);
      border-radius: 3px;
    }
  }
}
</style>
