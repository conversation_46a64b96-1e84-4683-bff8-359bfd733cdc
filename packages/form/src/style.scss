.#{$formNamespace}-form-item-label-tooltip {
  * {
    cursor: pointer;
  }

  &-content {
    white-space: pre-wrap;
  }
}

.#{$formNamespace}-form-collapse.el-collapse.inset {
  .el-collapse-item__header {
    padding-left: 16px;
  }

  .el-collapse-item__content {
    padding-left: 16px;
    padding-right: 16px;
  }
}

.#{$formNamespace}-form-item.item-control-horizontal {
  .#{$formNamespace}-plus-form-item-control {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.#{$formNamespace}-form-item.danger-description {
  .#{$formNamespace}-form-item-extra {
    color: map.get($color, danger);
  }
}

.#{$formNamespace}-form-collapse {
  .el-collapse-item {
    .el-collapse-item__header {
      font-size: 14px;
    }
  }
}

.#{$formNamespace}-form-collapse.advanced-collapse {
  padding-bottom: 16px;
  border-top: 0;
  border-bottom: 0;
  --collapse-padding-primary: 0;

  .el-collapse-item {
    .el-collapse-item__header {
      font-size: 14px;
    }

    .el-collapse-item__wrap {
      padding: 8px 16px;
      border: 1px solid #ebeef5;
      border-radius: 12px;
    }

    .el-collapse-item__content {
      padding-bottom: 0;
    }
  }
}

.#{$formNamespace}-space-horizontal {
  > .#{$formNamespace}-space-item > .#{$formNamespace}-form-item-layout-horizontal > .#{$formNamespace}-form-item-label {
    * {
      line-height: 32px;
    }

    > .#{$formNamespace}-form-item-label-content {
      min-height: 32px;
    }

    //> .#{$formNamespace}-form-item-label-tooltip {
    //  height: 32px !important;
    //}
  }
}

// TODO 需要观察全局范围的影响
.#{$formNamespace}-form-item-layout-horizontal {
  > .#{$formNamespace}-form-item-label {
    * {
      line-height: 32px;
    }

    > .#{$formNamespace}-form-item-label-content {
      min-height: 32px;
    }
  }
}

.#{$formNamespace}-form-item-layout-vertical {
  > .#{$formNamespace}-form-item-label {
    > .#{$formNamespace}-form-item-label-tooltip {
      height: 40px;
    }
  }

  > .formily-element-form-item-control .formily-element-form-item-control-content .formily-element-form-item-control-content-component .el-switch {
    height: 32px;
    line-height: 32px;
    vertical-align: top;
  }
}

.#{$formNamespace}-form-item-control-content-component {
  font-size: 0;

  > * {
    font-size: 14px;
    vertical-align: middle;
  }
}

.#{$formNamespace}-space-item {
  display: contents;
}

.extra-prefix-bar .#{$formNamespace}-form-item-extra {
  position: relative;
  padding-left: 12px;

  &::before {
    --n-bar-width: 3px;
    content: "";
    width: var(--n-bar-width);
    border-radius: calc(var(--n-bar-width) / 2);
    transition: background-color .3s cubic-bezier(.4, 0, .2, 1);
    left: 0;
    top: 2px;
    bottom: 2px;
    position: absolute;
    background-color: #bcbfc3;
  }
}

.field-select-popper .el-select-dropdown__item {
  padding: 0 16px;
}

.#{$formNamespace}-form-item-label .el-button {
  line-height: 1;

  &.is-text {
    line-height: 22px;
  }

  * {
    line-height: inherit;
  }
}


.#{$formNamespace}-array-table {
  .el-table {
    .cell {
      --btn-space: 2px;
    }
  }
}