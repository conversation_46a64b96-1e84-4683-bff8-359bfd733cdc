$array-table-prefix-cls: '#{$formily-prefix}-form-array-table';

.#{$array-table-prefix-cls} {
  .#{$formily-prefix}-form-item:not(.#{$formily-prefix}-form-item-feedback-layout-popover),
  .#{$namespace}-form-item {
    margin-bottom: 0 !important;
  }

  .auto-width {
    display: inline-block;
    width: auto;

    & + .#{$formily-prefix}-form-array-base-addition {
      display: block;
      margin-top: 0;
      width: auto;
    }
  }

  .#{$formily-prefix}-form-array-base-addition {
    margin-top: 8px;
    width: 100%;
    border: $--border-width-base dashed $--border-color-base;

    &:hover {
      background-color: $--color-white;
      border-color: $--border-color-hover;
    }

    &:active,
    &:focus {
      background-color: $--color-white;
      border-color: $--color-primary;
    }
  }

  .#{$formily-prefix}-form-item-feedback-layout-popover {
    margin-bottom: 0;
  }

  &-inner-asterisk {
    color: $--color-danger;
    font-weight: $--font-weight-primary;
  }
}
