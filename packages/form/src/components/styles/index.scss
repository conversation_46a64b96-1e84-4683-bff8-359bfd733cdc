$formily-prefix: 'formily-element';
$namespace: 'el';
@import '~element-plus/theme-chalk/src/common/var.scss';

@mixin active {
  border-color: $--color-primary;
  outline: 0;
  border-right-width: $--border-width-base !important;
}

@mixin hover {
  border-color: $--border-color-hover;
  outline: 0;
  border-right-width: $--border-width-base !important;
}
.el-select {
  width: 100%;
}
.formily-element-plus-form-item {
  .ace_editor,
  .ace_gutter {
    background-color: #f5f6f8;
    color: #fff;
  }
}

@import './space.scss';
@import './filter-conditions';
@import './preview-sql';
@import './array-base.scss';
@import './array-table.scss';
@import './form-grid.scss';
@import './form-item/index.scss';
