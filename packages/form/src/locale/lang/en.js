export default {
  packages_form_el_select_loading: 'Loading',
  packages_form_el_select_noMatch: 'No matching data found',
  packages_form_el_select_placeholder: 'Select',
  packages_form_message_exists_name: 'Name already exists',
  packages_form_button_edit: 'Edit',
  packages_form_dag_dialog_field_mapping_selected: 'Selected',
  packages_form_dag_dialog_field_mapping_no_data: 'No data yet',
  packages_form_button_reset: 'Reset',
  packages_form_dag_dialog_field_mapping_field: 'Name',
  packages_form_dag_dialog_field_mapping_type: 'Type',
  packages_form_meta_table_default: 'Default',
  packages_form_dag_dialog_field_mapping_range_precision: 'Length Range',
  packages_form_dag_dialog_field_mapping_range_scale: 'Precision Range',
  packages_form_button_cancel: 'Cancel',
  packages_form_button_confirm: 'Confirm',
  packages_form_dag_dialog_field_mapping_tittle_field_name: 'Please modify the field name in the target table',
  packages_form_dag_dialog_field_mapping_tittle_data_type: 'Please modify the field type in the target table',
  packages_form_dag_dialog_field_mapping_tittle_precision: 'Please modify the field length in the target table',
  packages_form_dag_dialog_field_mapping_tittle_scale: 'Please modify the precision in the target table',
  packages_form_dag_dialog_field_mapping_tittle_value:
    'Please modify the default value of the field in the target table',
  packages_form_dag_dialog_field_mapping_error_range:
    'The current value is outside the acceptable range for the field.',
  packages_form_editor_cell_processor_field_form_expression: 'Please enter an expression',
  packages_form_queryBuilder_addCond: 'Filed Condition',
  packages_form_editor_cell_link_copySourceDatabase: 'Need to replicate the schema of the source database',
  packages_form_editor_cell_link_formTip:
    'The copy view, function, and procedure functions are only supported for MySQL to MySQL',
  packages_form_editor_cell_link_migrationObjece: 'Source Tables',
  packages_form_editor_cell_link_chosen: 'Selected',
  packages_form_editor_cell_link_migrationSetting: 'Tables to be copied selection',
  packages_form_dataFlow_changeName: 'Rename',
  packages_form_editor_cell_link_reduction: 'Reduction',
  packages_form_editor_cell_link_searchContent: 'Search',
  packages_form_editor_cell_link_batchRename: 'Batch rename settings',
  packages_form_editor_cell_link_prefixPlaceholder: 'Please enter the prefix',
  packages_form_editor_cell_link_suffixPlaceholder: 'Kindly input the suffix',
  packages_form_editor_cell_link_tableNameExample: 'Table name example',
  packages_form_dataVerify_cancel: 'Cancel',
  packages_form_dataVerify_confirm: 'Confirm',
  packages_form_component_table_selector_candidate_label: 'To be selected',
  packages_form_button_reload: 'Reload',
  packages_form_common_placeholder_search_input: 'Enter your search here...',
  packages_form_component_table_selector_tables_empty:
    'Currently, no table is available. Please click on the upper right corner to reload the table',
  packages_form_component_table_selector_checked_label: 'Selected ',
  packages_form_component_table_selector_bulk_name: 'Paste table name',
  packages_form_component_table_selector_bulk_pick: 'Bulk Pick',
  packages_form_component_table_selector_not_checked: 'Please select a table to proceed',
  packages_form_component_table_selector_clipboard_placeholder:
    'Please enter table names separated by commas, for example: table_a, table_b',
  packages_form_component_table_selector_error: 'Selected tables has exceptions',
  packages_form_component_table_selector_autofix: 'Clear exception tables',
  packages_form_component_table_selector_error_not_exit: 'Table does not exist',
  packages_form_agent_check_error:
    "The agent's current state is abnormal and it is unable to establish a connection. Please check the agent's status's current state is abnormal and cannot create a connection, please check",
  packages_form_connection_reload_schema_confirm_title: 'Reload schema',
  packages_form_connection_reload_schema_confirm_msg:
    'If there are a large number of schemas in this library, it may take a significant amount of time. Be sure to refresh the data source schema',
  packages_form_connection_reload_schema_fail: 'Schema load failed',
  packages_form_formBuilder_file_placeholder: 'Please select a file',
  packages_form_formBuilder_file_button: 'Select',
  packages_form_components_adddatabasebtn_xinjian: 'New',
  packages_form_clipboard_dialog_dialog_yifuzhi: 'Copied',
  packages_form_clipboard_dialog_dialog_fuzhichuangjianming: 'Copy creation command',
  packages_form_clipboard_dialog_dialog_chuangjianmingling: 'Create Command',
  packages_form_clipboard_dialog_dialog_huoquchuangjianming: 'Get the creation command',
  packages_form_ddl_event_checkbox_index_mubiaozanbuzhi:
    'The target does not support Data Definition Language (DDL) operations',
  packages_form_ddl_event_checkbox_index_laiziyuanlianjie: 'Link from source:',
  packages_form_ddl_event_checkbox_index_xiugaibiaoming: 'Modify table name',
  packages_form_ddl_event_checkbox_index_xiugaishujuku: 'Modify database time zone',
  packages_form_ddl_event_checkbox_index_xiugaibiaozifu: 'Modify table character set',
  packages_form_ddl_event_checkbox_index_xinzengziduan: 'Add Field',
  packages_form_ddl_event_checkbox_index_shanchuziduan: 'Delete Field',
  packages_form_ddl_event_checkbox_index_xiugaizhujian: 'Modify primary key',
  packages_form_ddl_event_checkbox_index_qingkongbiao: 'Clear Table',
  packages_form_ddl_event_checkbox_index_shanchubiao: 'Delete Table',
  packages_form_ddl_event_checkbox_index_chuangjianbiao: 'Create Table',
  packages_form_ddl_event_checkbox_index_xiugaiziduanshu: 'Modify field attributes',
  packages_form_ddl_event_checkbox_index_xiugaiziduanming: 'Modify field name',
  packages_form_example_file_index_tiaojianfenzu: 'Conditional grouping',
  packages_form_example_file_index_zhengzebiaodashi: 'regular expression',
  packages_form_example_file_index_timestamp: 'Filter by timestamp type',
  packages_form_example_file_index_huo: 'or',
  packages_form_example_file_index_qie: 'and',
  packages_form_example_file_index_fei: 'Not',
  packages_form_example_file_index_dengyu: 'equal to',
  packages_form_example_file_index_dayudengyuxiao: 'Greater than or equal to, Less than or equal to',
  packages_form_example_file_index_dayuxiaoyu: 'Greater than, Less than',
  packages_form_example_file_index_zhichidefuhao: 'Supported symbols',
  packages_form_example_file_index_shaixuanchusuiyi:
    'Filter out men over 50 years old or people over 30 years old with income under 10,000',
  packages_form_example_file_index_biaodashishili: 'Expression Example',
  packages_form_field_add_del_index_ziduanmingcheng: 'Field Name',
  packages_form_field_mapping_dialog_queding: 'OK',
  packages_form_field_mapping_dialog_quxiao: 'Cancel',
  packages_form_field_mapping_dialog_bianjituiyanjie: 'Please modify or revise the deduction result',
  packages_form_field_mapping_list_xuhao: 'Index',
  packages_form_field_mapping_list_qingshuruziduan: 'Please enter the field name',
  packages_form_field_mapping_list_biaoming: 'Table Name',
  packages_form_field_mapping_list_qingshurubiaoming: 'Please enter the table name',
  packages_form_field_mod_type_index_mubiaoziduanlei: 'Targeted field type',
  packages_form_field_mod_type_index_yuanziduanleixing: 'Source field type',
  packages_form_field_processor_index_xiaoxie: 'Lowercase',
  packages_form_field_processor_index_daxie: 'Uppercase',
  packages_form_field_processor_index_bubian: 'Unchanged',
  packages_form_field_processor_index_snake_to_camel: 'Snake To CamelCase',
  packages_form_field_processor_index_camel_to_snake: 'CamelCase To SnakeName',
  packages_form_field_processor_index_other_commonly_used_conversions: 'Other commonly used conversions',
  packages_form_field_processor_index_daxiaoxie: 'Table name case',
  packages_form_field_processor_filed_name_daxiaoxie: 'Field name processing',
  packages_form_field_processor_index_houzhui: 'Suffix',
  packages_form_field_processor_index_qianzhui: 'Prefix',
  packages_form_field_processor_index_piliangcaozuo: 'Batch Operation',
  packages_form_field_processor_index_caozuo: 'Operation',
  packages_form_field_processor_index_xinziduanming: 'New field name',
  packages_form_field_processor_index_huifu: 'Restore',
  packages_form_field_processor_index_pingbi: 'Mask',
  packages_form_field_rename_index_mubiaoziduanming: 'Targeted field name',
  packages_form_field_rename_index_yuanziduanming: 'Source field name',
  packages_form_field_value_index_varre:
    'var result = record.isTrue ? true : false // Ternary expression,\n The value of result is true or false according to the result of the judgment expression (record.isTrue)',
  packages_form_field_value_index_shili: 'Example:',
  packages_form_field_value_index_ziduanfuzhi: 'Field assignment',
  packages_form_js_processor_index_jieguoshuchu: 'Result Output',
  packages_form_js_processor_index_tiaoshishuru: 'Debug Input',
  packages_form_js_processor_index_shiyunxing: 'Test Run',
  packages_form_js_processor_index_shujuhangshu: 'Number of data rows',
  packages_form_js_processor_index_xuanzebiao: 'Select Form',
  packages_form_js_processor_index_moxingshengming: 'Model declaration',
  packages_form_js_processor_index_jiaoben: 'Script',
  packages_form_js_processor_index_tooltip1:
    'Standard JS nodes can only process and operate data records. If you want to use all system built-in functions and realize external calls (such as network, database, etc.), you can use enhanced JS nodes. ',
  packages_form_js_processor_index_tooltip2:
    'Enhanced JS nodes can use all built-in functions to implement external calls (such as network, database, etc.), but it is currently a Beta version, and performance problems may occur, please use it with caution. If only data records need to be processed and calculated, it is recommended to use standard JS nodes. ',
  packages_form_python_processor_index_tooltip:
    'Python nodes can utilize all built-in functions to implement external calls (such as networking, databases, etc.). However, please use with caution as it is currently in Beta version and may encounter performance issues.',
  packages_form_js_processor_index_qingqiuchaoshiqing: 'The request has timed out, please try again',
  packages_form_js_processor_index_rengzaipinmingjia: 'Please be patient, the page is still loading',
  packages_form_table_rename_index_qingkong: 'Clear',
  packages_form_table_rename_search_text: 'Search Text',
  packages_form_table_rename_replace_with: 'Replace With',
  packages_form_table_rename_index_yixiacaozuojin: 'The following operations will affect all original table names',
  packages_form_table_rename_index_xinbiaoming: 'New table name',
  packages_form_table_rename_index_yuanbiaoming: 'Original table name',
  packages_form_table_rename_index_sousuobiaoming: 'Search table name',
  packages_form_table_rename_rule_config: 'Rule Configuration',
  packages_form_table_rename_invalid_operation: 'The following operation did not match the original table name.',
  packages_form_text_file_reader_index_shangchuanwenjianda: 'The upload file size cannot exceed {val1}KB',
  packages_form_src_index_cronbiao: 'Cron expression format is incorrect',
  packages_form_js_editor_fullscreen: 'Full screen',
  packages_form_js_editor_exit_fullscreen: 'Exit full screen',
  packages_form_field_inference_dialog_cunzaicuowuge: 'There is a formatting error',
  packages_form_field_inference_dialog_cankaogeshiv: 'Reference format: varchar(32)',
  packages_form_field_inference_dialog_xiugaihoudelei: 'Modified Type',
  packages_form_field_inference_dialog_mubiaomorentui: 'Type of deduction',
  packages_form_field_inference_list_caozuochenggong: 'Operation Succeeded',
  packages_form_field_inference_list_geshicuowu: 'Please check the length and precision of the field',
  packages_form_field_inference_list_ziduanzhushi: 'Field Notes',
  packages_form_field_inference_list_feikong: 'Should Not be empty',
  packages_form_field_inference_list_piliangyingyonghui:
    'The Batch application will overwrite with existing batch application rules',
  packages_form_field_inference_list_duidangqiantuiyan: 'Batch adjustment to the current deduction type',
  packages_form_field_inference_list_yaotiaozhengweide: 'Adjust type to:',
  packages_form_field_inference_list_tuiyanchudelei: 'Inference type:',
  packages_form_field_inference_list_ziduanleixingtiao: 'Field type adjustment',
  packages_form_field_inference_main_quanbuhuifumo: 'Restore all defaults',
  packages_form_field_inference_main_gepiliangxiugai: 'The modified batch rule is now active.',
  packages_form_field_inference_main_dangqianyou: 'There is currently',
  packages_form_batch_rule_active:
    '<span class="color-warning px-1 fs-6 fw-bold din-font">{val}</span>batch rule active',
  packages_form_field_type_rules_main_shijikeyongchang:
    'The precise length available will vary depending on the definition of the target database type, so please adjust it accordingly to meet your requirements.',
  packages_form_field_type_rules_main_tianjia: 'Add',
  packages_form_field_type_rules_main_piliangxiugaizi: 'Modify field types in batches',
  packages_form_load_schema_tree_button_title: 'Load Schema',
  packages_form_field_inference_dialog_mubiaoleixingpi: 'Target type batch modification rules',
  packages_form_field_inference_list_ninquerenyaohui:
    'Are you absolutely sure that you wish to restore the current table?',
  packages_form_field_inference_main_ninquerenyaoquan:
    'Are you sure that you want to restore all settings to their default values?',
  packages_form_field_inference_main_ge: '',
  packages_form_qingjianchajiedian: 'Please check node configuration',
  packages_form_load_schema_tree_load_fail: 'Failed to load',
  packages_form_load_schema_tree_form_values_change:
    'After making changes to the configuration, please note that the model must be reloaded in order for the changes to take effect',
  packages_form_batch_add_field_title: 'Batch Add Field',
  packages_form_batch_add_field_prefix: 'Prefix',
  packages_form_batch_add_field_type: 'Type',
  packages_form_batch_add_field_count: 'Count',
  packages_form_batch_add_field_start: 'Start Number',
}
