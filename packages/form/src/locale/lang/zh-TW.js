export default {
  packages_form_el_select_loading: '加載中',
  packages_form_el_select_noMatch: '無匹配資料',
  packages_form_el_select_placeholder: '請選擇',
  packages_form_message_exists_name: '名稱已存在',
  packages_form_button_edit: '編輯',
  packages_form_dag_dialog_field_mapping_selected: '已選中',
  packages_form_dag_dialog_field_mapping_no_data: '暫無數據',
  packages_form_button_reset: '重置',
  packages_form_dag_dialog_field_mapping_field: '字段名',
  packages_form_dag_dialog_field_mapping_type: '字段類型',
  packages_form_meta_table_default: '默認值',
  packages_form_dag_dialog_field_mapping_range_precision: '長度範圍',
  packages_form_dag_dialog_field_mapping_range_scale: '精度範圍',
  packages_form_button_cancel: '取消',
  packages_form_button_confirm: '確定',
  packages_form_dag_dialog_field_mapping_tittle_field_name: '修改目標表字段名稱',
  packages_form_dag_dialog_field_mapping_tittle_data_type: '修改目標表字段類型',
  packages_form_dag_dialog_field_mapping_tittle_precision: '修改目標字段長度',
  packages_form_dag_dialog_field_mapping_tittle_scale: '修改目標表精度',
  packages_form_dag_dialog_field_mapping_tittle_value: '修改目標表字段默認值',
  packages_form_dag_dialog_field_mapping_error_range: '當前值不符合該字段範圍',
  packages_form_editor_cell_processor_field_form_expression: '請輸入表達式',
  packages_form_queryBuilder_addCond: '字段條件',
  packages_form_editor_cell_link_copySourceDatabase: '複製源庫結構類型',
  packages_form_editor_cell_link_formTip: 'View、function、procedure的複制功能僅支持MySQL到MySQL的場景',
  packages_form_editor_cell_link_migrationObjece: '待複製表',
  packages_form_editor_cell_link_chosen: '已選擇',
  packages_form_editor_cell_link_migrationSetting: '複製對象設置',
  packages_form_dataFlow_changeName: '改名',
  packages_form_editor_cell_link_reduction: '還原',
  packages_form_editor_cell_link_searchContent: '搜索內容',
  packages_form_editor_cell_link_batchRename: '批量改名設置',
  packages_form_editor_cell_link_prefixPlaceholder: '請輸入前綴',
  packages_form_editor_cell_link_suffixPlaceholder: '請輸入後綴',
  packages_form_editor_cell_link_tableNameExample: '表名示例',
  packages_form_dataVerify_cancel: '取消',
  packages_form_dataVerify_confirm: '確認',
  packages_form_component_table_selector_candidate_label: '待覆制表',
  packages_form_button_reload: '重新加載schema',
  packages_form_common_placeholder_search_input: '請輸入搜索內容...',
  packages_form_component_table_selector_tables_empty: '您暫時沒有表，請點擊右上角重新加載表',
  packages_form_component_table_selector_checked_label: '已選擇表',
  packages_form_component_table_selector_bulk_name: '粘貼表名',
  packages_form_component_table_selector_bulk_pick: '批量選表',
  packages_form_component_table_selector_not_checked: '您暫時沒有選擇表',
  packages_form_component_table_selector_clipboard_placeholder: '請輸入表名稱並以逗號分隔,例如：table_a,table_b',
  packages_form_component_table_selector_error: '所選表存在異常',
  packages_form_component_table_selector_autofix: '清除異常表',
  packages_form_component_table_selector_error_not_exit: '表不存在',
  packages_form_agent_check_error: 'Agent當前狀態異常無法創建連接，請檢查',
  packages_form_connection_reload_schema_confirm_title: '重新加載 schema',
  packages_form_connection_reload_schema_confirm_msg: '如果此庫的schema過多，可能耗時較長，確定要刷新數據源的schema',
  packages_form_connection_reload_schema_fail: 'Schema 加載失敗',
  packages_form_formBuilder_file_placeholder: '請選擇文件',
  packages_form_formBuilder_file_button: '選擇文件',
  packages_form_components_adddatabasebtn_xinjian: '新建',
  packages_form_clipboard_dialog_dialog_yifuzhi: '已復制',
  packages_form_clipboard_dialog_dialog_fuzhichuangjianming: '複製創建命令',
  packages_form_clipboard_dialog_dialog_chuangjianmingling: '創建命令',
  packages_form_clipboard_dialog_dialog_huoquchuangjianming: '獲取創建命令',
  packages_form_ddl_event_checkbox_index_mubiaozanbuzhi: '目標暫不支持DDL',
  packages_form_ddl_event_checkbox_index_laiziyuanlianjie: '來自源連接：',
  packages_form_ddl_event_checkbox_index_xiugaibiaoming: '修改表名',
  packages_form_ddl_event_checkbox_index_xiugaishujuku: '修改數據庫時區',
  packages_form_ddl_event_checkbox_index_xiugaibiaozifu: '修改表字符集',
  packages_form_ddl_event_checkbox_index_xinzengziduan: '新增字段',
  packages_form_ddl_event_checkbox_index_shanchuziduan: '刪除字段',
  packages_form_ddl_event_checkbox_index_xiugaizhujian: '修改主鍵',
  packages_form_ddl_event_checkbox_index_qingkongbiao: '清空表',
  packages_form_ddl_event_checkbox_index_shanchubiao: '刪除表',
  packages_form_ddl_event_checkbox_index_chuangjianbiao: '創建表',
  packages_form_ddl_event_checkbox_index_xiugaiziduanshu: '修改字段屬性',
  packages_form_ddl_event_checkbox_index_xiugaiziduanming: '修改字段名',
  packages_form_example_file_index_tiaojianfenzu: '條件分組',
  packages_form_example_file_index_zhengzebiaodashi: '正則表達式',
  packages_form_example_file_index_timestamp: '按timestamp類型過濾',
  packages_form_example_file_index_huo: '或',
  packages_form_example_file_index_qie: '且',
  packages_form_example_file_index_fei: '非',
  packages_form_example_file_index_dengyu: '等於',
  packages_form_example_file_index_dayudengyuxiao: '大於等於、小於等於',
  packages_form_example_file_index_dayuxiaoyu: '大於、小於',
  packages_form_example_file_index_zhichidefuhao: '支持的符號',
  packages_form_example_file_index_shaixuanchusuiyi: '篩選出50歲以上的男性或者收入一萬以下的30歲以上的人',
  packages_form_example_file_index_biaodashishili: '表達式示例',
  packages_form_field_add_del_index_ziduanmingcheng: '字段名稱',
  packages_form_field_mapping_dialog_queding: '確 定',
  packages_form_field_mapping_dialog_quxiao: '取 消',
  packages_form_field_mapping_dialog_bianjituiyanjie: '編輯推演結果',
  packages_form_field_mapping_list_xuhao: '序號',
  packages_form_field_mapping_list_qingshuruziduan: '請輸入字段名',
  packages_form_field_mapping_list_biaoming: '表名',
  packages_form_field_mapping_list_qingshurubiaoming: '請輸入表名',
  packages_form_field_mod_type_index_mubiaoziduanlei: '目標字段類型',
  packages_form_field_mod_type_index_yuanziduanleixing: '源字段類型',
  packages_form_field_processor_index_xiaoxie: '转小寫',
  packages_form_field_processor_index_daxie: '转大寫',
  packages_form_field_processor_index_bubian: '不變',
  packages_form_field_processor_index_snake_to_camel: '蛇形命名轉駝峰命名',
  packages_form_field_processor_index_camel_to_snake: '駝峰命名轉蛇形命名',
  packages_form_field_processor_index_other_commonly_used_conversions: '其它常用轉換',
  packages_form_field_processor_index_daxiaoxie: '表名大小寫',
  packages_form_field_processor_filed_name_daxiaoxie: '字段名處理',
  packages_form_field_processor_index_houzhui: '後綴',
  packages_form_field_processor_index_qianzhui: '前綴',
  packages_form_field_processor_index_piliangcaozuo: '批量操作',
  packages_form_field_processor_index_caozuo: '操作',
  packages_form_field_processor_index_xinziduanming: '新字段名',
  packages_form_field_processor_index_huifu: '恢復',
  packages_form_field_processor_index_pingbi: '屏蔽',
  packages_form_field_rename_index_mubiaoziduanming: '目標字段名',
  packages_form_field_rename_index_yuanziduanming: '源字段名',
  packages_form_field_value_index_varre:
    'var result = record.isTrue ? true : false // 三元表達式,\n                  result的值根據判斷表達式（record.isTrue）的結果為 true 或 false',
  packages_form_field_value_index_shili: '示例:',
  packages_form_field_value_index_ziduanfuzhi: '字段賦值',
  packages_form_js_processor_index_jieguoshuchu: '結果輸出',
  packages_form_js_processor_index_tiaoshishuru: '調試輸入',
  packages_form_js_processor_index_shiyunxing: '試運行',
  packages_form_js_processor_index_shujuhangshu: '數據行數',
  packages_form_js_processor_index_xuanzebiao: '選擇表',
  packages_form_js_processor_index_moxingshengming: '模型聲明',
  packages_form_js_processor_index_jiaoben: '腳本',
  packages_form_js_processor_index_tooltip1:
    '標準 JS 節點只能對數據記錄進行處理和運算，如需使用所有的系統內置函數，實現外部調用（如網絡、數據庫等），可使用增強 JS 節點。 ',
  packages_form_js_processor_index_tooltip2:
    '增強 JS 節點可使用所有的內置函數，實現外部調用（如網絡、數據庫等），但目前為Beta版本，可能會出現性能問題，請謹慎使用。如僅需對數據記錄進行處理和運算，推薦使用標準 JS 節點。 ',
  packages_form_python_processor_index_tooltip:
    'Python 節點可使用所有的內建函數，實現外部呼叫（如網路、資料庫等），但目前為Beta版本，可能會出現效能問題，請謹慎使用。',
  packages_form_js_processor_index_qingqiuchaoshiqing: '請求超時，請重試',
  packages_form_js_processor_index_rengzaipinmingjia: '仍在拼命加載中，請耐心等待',
  packages_form_table_rename_index_qingkong: '清空',
  packages_form_table_rename_search_text: '查找文本',
  packages_form_table_rename_replace_with: '替換為',
  packages_form_table_rename_index_yixiacaozuojin: '以下操作對所有原表名生效',
  packages_form_table_rename_index_xinbiaoming: '新表名',
  packages_form_table_rename_index_yuanbiaoming: '原表名',
  packages_form_table_rename_index_sousuobiaoming: '搜索表名',
  packages_form_table_rename_rule_config: '規則配置',
  packages_form_table_rename_invalid_operation: '以下操作已匹配不到原表名',
  packages_form_text_file_reader_index_shangchuanwenjianda: '上傳文件大小不能超過 {val1}KB',
  packages_form_src_index_cronbiao: 'Cron表達式格式有誤',
  packages_form_js_editor_fullscreen: '全屏',
  packages_form_js_editor_exit_fullscreen: '退出全屏',
  packages_form_field_inference_dialog_cunzaicuowuge: '存在錯誤格式',
  packages_form_field_inference_dialog_cankaogeshiv: '參考格式: varchar(32)',
  packages_form_field_inference_dialog_xiugaihoudelei: '修改後的類型',
  packages_form_field_inference_dialog_mubiaomorentui: '推演出的類型',
  packages_form_field_inference_list_caozuochenggong: '操作成功',
  packages_form_field_inference_list_geshicuowu: '請檢查字段的長度和精度',
  packages_form_field_inference_list_ziduanzhushi: '字段註釋',
  packages_form_field_inference_list_feikong: '非空',
  packages_form_field_inference_list_piliangyingyonghui: '批量應用會覆蓋已有批量應用規則',
  packages_form_field_inference_list_duidangqiantuiyan: '對當前推演類型進行批量調整',
  packages_form_field_inference_list_yaotiaozhengweide: '要調整為的類型:',
  packages_form_field_inference_list_tuiyanchudelei: '推演出的類型:',
  packages_form_field_inference_list_ziduanleixingtiao: '字段類型調整',
  packages_form_field_inference_main_quanbuhuifumo: '全部恢復默認',
  packages_form_field_inference_main_gepiliangxiugai: '批量修改規則正在生效',
  packages_form_field_inference_main_dangqianyou: '當前有',
  packages_form_batch_rule_active:
    '<span class="color-warning px-1 fs-6 fw-bold din-font">{val}</span>個批量修改規則正在生效',
  packages_form_field_type_rules_main_shijikeyongchang: '實際可用長度取決於目標數據庫類型定義，請按需設置',
  packages_form_field_type_rules_main_tianjia: '添加',
  packages_form_field_type_rules_main_piliangxiugaizi: '批量修改字段類型',
  packages_form_load_schema_tree_button_title: '加載模型',
  packages_form_field_inference_dialog_mubiaoleixingpi: '目標類型批量修改規則',
  packages_form_field_inference_list_ninquerenyaohui: '您確認要恢復當前表嗎？ ',
  packages_form_field_inference_main_ninquerenyaoquan: '您確認要全部恢復默認嗎？ ',
  packages_form_field_inference_main_ge: '個',
  packages_form_qingjianchajiedian: '請檢查節點配置',
  packages_form_load_schema_tree_load_fail: '加載失敗',
  packages_form_load_schema_tree_form_values_change: '配置更改後需重新加載模型才可生效',
  packages_form_batch_add_field_title: '批量新增',
  packages_form_batch_add_field_prefix: '字段前綴',
  packages_form_batch_add_field_type: '字段類型',
  packages_form_batch_add_field_count: '字段數量',
  packages_form_batch_add_field_start: '開始數字',
}
