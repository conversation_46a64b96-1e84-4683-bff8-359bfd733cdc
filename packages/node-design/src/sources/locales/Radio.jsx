export const RadioGroup = {
  'zh-CN': {
    title: '单选框组',
    settings: {
      'x-component-props': {
        buttonStyle: { title: '按钮风格', dataSource: ['空心', '实心'] },
        optionType: { title: '选项类型', dataSource: ['默认', '按钮'] },
      },
    },
  },
  'en-US': {
    title: 'Radio',
    settings: {
      'x-component-props': {
        buttonStyle: { title: 'Button style', dataSource: ['Hollow', 'Solid'] },
        optionType: { title: 'Option type', dataSource: ['Default', 'Button'] },
      },
    },
  },
}
