export const Image = () => (
  <path d="M872,32 C938.27417,32 992,85.72583 992,152 L992,872 C992,938.27417 938.27417,992 872,992 L152,992 C85.72583,992 32,938.27417 32,872 L32,152 C32,85.72583 85.72583,32 152,32 L872,32 Z M832,112 L192,112 C148.259048,112 112.717181,147.104457 112.010718,190.677054 L112,192 L112,832 C112,875.740952 147.104457,911.282819 190.677054,911.989282 L192,912 L832,912 C875.740952,912 911.282819,876.895543 911.989282,833.322946 L912,832 L912,192 C912,147.81722 876.18278,112 832,112 Z M378.52279,415.050326 C387.989019,409.358413 400.277122,412.418101 405.969035,421.884331 L405.969035,421.884331 L560.846571,679.46141 C561.920687,681.247773 563.267454,682.855244 564.838099,684.225605 C573.161205,691.487375 585.795242,690.626992 593.057011,682.303887 L593.057011,682.303887 L686.973056,574.661651 C688.336422,573.099024 689.934526,571.757945 691.710072,570.686495 C701.167259,564.979571 713.460201,568.019761 719.167124,577.476949 L719.167124,577.476949 L852.543134,798.500051 C855.365729,803.177494 856.857504,808.536895 856.857504,814 C856.857504,830.568542 843.426047,844 826.857504,844 L826.857504,844 L191.580891,844 C186.553452,844 181.606628,842.736548 177.194928,840.32573 C162.655629,832.380582 157.310013,814.153336 165.255161,799.614037 L165.255161,799.614037 L371.278423,422.599809 C372.980511,419.485057 375.480866,416.879393 378.52279,415.050326 Z M699,213 C756.989899,213 804,260.010101 804,318 C804,375.989899 756.989899,423 699,423 C641.010101,423 594,375.989899 594,318 C594,260.010101 641.010101,213 699,213 Z"></path>
)

export const ImageSize = () => (
  <path d="M872,32 C938.27417,32 992,85.72583 992,152 L992,872 C992,938.27417 938.27417,992 872,992 L152,992 C85.72583,992 32,938.27417 32,872 L32,152 C32,85.72583 85.72583,32 152,32 L872,32 Z M832,112 L192,112 C148.259048,112 112.717181,147.104457 112.010718,190.677054 L112,192 L112,832 C112,875.740952 147.104457,911.282819 190.677054,911.989282 L192,912 L832,912 C875.740952,912 911.282819,876.895543 911.989282,833.322946 L912,832 L912,192 C912,147.81722 876.18278,112 832,112 Z M784,200 C806.09139,200 824,217.90861 824,240 L824,440 C824,462.09139 806.09139,480 784,480 C761.90861,480 744,462.09139 744,440 L744,321.86 L350.348,744.001 L440,744 C462.09139,744 480,761.90861 480,784 C480,806.09139 462.09139,824 440,824 L240,824 C217.90861,824 200,806.09139 200,784 L200,584 C200,561.90861 217.90861,544 240,544 C262.09139,544 280,561.90861 280,584 L280,702.137 L673.65,279.999 L584,280 C561.90861,280 544,262.09139 544,240 C544,217.90861 561.90861,200 584,200 L784,200 Z"></path>
)
