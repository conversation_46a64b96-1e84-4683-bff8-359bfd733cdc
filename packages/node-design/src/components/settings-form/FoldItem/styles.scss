@import '~antd/lib/style/themes/default.less';

.dn-fold-item {
  .@{ant-prefix}-formily-item-label-content {
    overflow: visible;
  }

  &-base {
    cursor: pointer;
  }

  &-title {
    display: flex;
    align-items: center;
    margin-left: -10px;
    cursor: pointer;
    .dn-icon {
      transform: translateX(-3px);

      svg {
        transition: all 0.15s ease-in-out;
        transform: rotate(-90deg);
      }
    }

    &.expand {
      .dn-icon {
        svg {
          transform: rotate(0deg);
        }
      }
    }
  }

  &-extra {
    margin-top: -10px;
    margin-left: -10px;
    margin-right: -10px;
    margin-bottom: 10px;
    padding: 10px 10px 0 10px;
    background-color: var(--dn-composite-panel-highlight-bg-color);
  }
}
