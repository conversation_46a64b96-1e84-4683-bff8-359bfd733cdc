@import '../../variables';

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(100%);
  }
}

.animate__slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight;
}

.animate__slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight;
}

.animate__animated {
  animation-delay: 0ms;
  animation-duration: 0.25s;
  animation-fill-mode: forwards;
}

.animate__fadeInUp {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

.#{$prefix-cls}-settings-form-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  .#{$prefix-cls}-node-path {
    flex-grow: 0;
  }

  .#{$prefix-cls}-settings-form-content {
    flex-grow: 1;
    overflow: overlay;
  }

  .formily-element-plus-form-item {
    border-bottom: 1px solid $--border-color-base;
    padding-bottom: 8px;
    margin-bottom: 8px;
    margin-top: 8px;

    * {
      font-size: 14px;
    }

    &-control-content-component {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      & > .el-radio-group {
        display: flex !important;
        width: 100%;
        line-height: inherit;

        .el-radio-button {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-grow: 2;
          &__inner {
            padding: 0 6px !important;
            width: 100%;
            height: 32px;
            line-height: 32px;
          }
        }
      }

      & > .el-slider {
        flex-shrink: 0;
        min-width: 0;
        width: 100%;
      }

      & > .el-select {
        max-width: 140px;
      }
    }
  }
}

.#{$prefix-cls}-settings-form {
  padding: 0 20px;

  &-empty {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    color: #888;
  }
}
