@import '../../../variables';

.#{$prefix-cls}-poly-input {
  display: flex;
  width: 100%;
  align-items: center;

  .#{$prefix-cls}-poly-input-content {
    flex-grow: 2;
    margin-right: 2px;
    display: flex;

    .el-select {
      width: 100%;
    }

    .el-input-number {
      width: 100%;
    }
  }

  .#{$prefix-cls}-poly-input-controller {
    border: 1px solid $--border-color-base;
    //border-radius: 2px;
    cursor: pointer;
    //padding: 0 8px;
    padding-left: 8px;
    padding-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-grow: 0;
    min-width: 33px; // 宽度对齐数字输入框的右侧按钮
    * {
      font-size: 12px;
    }
  }
}
