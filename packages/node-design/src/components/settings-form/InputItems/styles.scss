@import '../../../variables';

.#{$prefix-cls}-input-items {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8px;

  &-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: $--color-text-primary;
    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 8px;
      flex-shrink: 0;
      flex-grow: 0;
      color: $--color-text-primary;
    }

    &-controller {
      min-width: 0;
      flex-shrink: 1;
      flex-grow: 1;

      .#{$formily-prefix}-radio-group {
        display: flex;

        .#{$formily-prefix}-radio-button-wrapper {
          flex-grow: 1;
          display: flex;
          justify-content: center;
        }
      }
    }
    &.vertical {
      flex-direction: column;
      align-items: flex-start;
      .#{$prefix-cls}-input-items-item-controller {
        width: 100%;
      }
    }
  }
}
