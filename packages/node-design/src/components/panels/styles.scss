@import '../../variables';

.#{$prefix-cls}-main-panel {
  display: flex;
  width: 100%;
  height: 100%;
  flex: 1;
  min-height: 0;
  position: relative;
  overflow: hidden;

  &-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    &.root {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    &.absolute {
      position: absolute;
    }
    &.relative {
      position: relative;
    }

    .panel-header {
      $hoverBg: #eef3ff;
      $radius: 4px;

      position: relative;
      height: 48px;

      &-nav {
        width: 48px;
      }

      &-btn {
        width: 30px;
        height: 30px;
        background: #fff;
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: background, color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);

        &:hover {
          color: map.get($color, primary);
          background: $hoverBg;
        }
      }

      &-logo {
        color: map.get($color, primary);
      }

      .title-input-wrap {
        position: relative;
        flex: 1;
        max-width: 214px;
        font-size: 13px;

        &:hover {
          .title-input {
            border-color: #dcdfe6;
          }
          .title-input-icon {
            color: map.get($color, primary);
          }
        }

        .title-input {
          position: relative;
          padding-left: 4px;
          padding-right: 28px;
          width: 230px;
          height: 28px;
          line-height: 28px;
          font-size: 14px;
          outline: none;
          box-shadow: none;
          background: 0 0;
          border: 1px solid transparent;
          border-radius: 4px;
          transition: border-color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);

          &:focus {
            border-color: map.get($color, primary);
            & + .title-input-icon {
              color: map.get($color, primary);
            }
          }
        }

        .title-input-icon {
          position: absolute;
          right: 8px;
          height: 28px;
        }
      }

      &-tools {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }

      .icon-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 30px;
        height: 30px;
        padding: 5px;
        background: #fff;
        border: 1px solid transparent;
        border-radius: $radius;
        transition: background, color 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
        cursor: pointer;

        &:hover,
        &.active {
          color: map.get($color, primary);
          background: $hoverBg;
        }
      }

      .icon-btn + .icon-btn {
        margin-left: 16px;
      }
    }
  }

  &-header {
    display: flex;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
    justify-content: space-between;
    background: var(--dn-main-panel-header-bg-color);
    border-bottom: 1px solid var(--dn-panel-border-color);
    padding: 4px;

    &-logo {
      display: flex;
      align-items: center;
    }

    &-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  &.root {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

.#{$prefix-cls}-composite-panel {
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  position: relative;
  user-select: none;
  z-index: 2;

  &-tabs {
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--dn-panel-border-color);
    background-color: var(--dn-composite-panel-tabs-bg-color);
    z-index: 2;
    position: relative;

    &-pane {
      color: var(--dn-composite-panel-tabs-color);
      min-height: 48px;
      min-width: 48px;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: relative;
      font-size: 20px;
      flex-direction: column;

      &-title {
        font-size: 10px;
        margin-top: 6px;
      }

      &:hover {
        color: var(--dn-composite-panel-tabs-hover-color);
      }

      &.active {
        color: var(--dn-composite-panel-tabs-hover-color);

        &::after {
          position: absolute;
          top: 0;
          left: 0;
          display: block;
          content: '';
          width: 3px;
          height: 100%;
          background-color: var(--dn-composite-panel-tabs-hover-color);
        }
      }
    }

    &-content {
      width: 300px;
      border-right: 1px solid var(--dn-panel-border-color);
      background: var(--dn-composite-panel-tabs-content-bg-color);
      display: flex;
      flex-direction: column;
      height: 100%;
      box-sizing: content-box;

      &.pinning {
        position: absolute;
        z-index: 1;
        left: 100%;
        top: 0;
        border-right: 1px solid transparent;
        box-shadow: -2px 5px 10px rgba(102, 102, 102, 0.42);
      }
    }

    &-header {
      padding: 14px 7px;
      color: var(--dn-composite-panel-tabs-header-color);
      line-height: 18px;
      font-size: 16px;
      border-bottom: 1px solid var(--dn-panel-border-color);
      display: flex;
      justify-content: space-between;

      &-actions {
        display: flex;
        align-items: center;

        & > * {
          margin-right: 8px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      &-pin {
        transition: all 0.15s ease-in-out;

        &:hover {
          transform: scale(1.1);
        }
      }

      &-title {
        font-size: 14px;
      }

      &-close {
        transition: all 0.15s ease-in-out;

        &:hover {
          transform: rotate(90deg);
        }
      }
    }

    &-body {
      flex-grow: 2;
      flex-shrink: 2;
      overflow: overlay;
      overflow-x: hidden;
      height: 100%;
    }
  }

  &.direction-right {
    flex-direction: row-reverse;

    .#{$prefix-cls}-composite-panel-tabs-pane.active:after {
      left: auto;
      right: -1px;
    }

    .#{$prefix-cls}-composite-panel-tabs-content.pinning {
      left: auto;
      right: 100%;
      top: 0;
    }

    .#{$prefix-cls}-composite-panel-tabs-content {
      border-right: none;
      border-left: 1px solid var(--dn-panel-border-color);
    }
    .#{$prefix-cls}-composite-panel-tabs {
      border-left: 1px solid var(--dn-panel-border-color);
    }
  }
}

.#{$prefix-cls}-workspace-panel {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  padding: 4px;
  overflow: hidden;
  box-sizing: border-box;
  background-color: var(--dn-workspace-panel-bg-color);
  position: relative;
  z-index: 1;

  &-item {
    position: relative;
  }

  button[disabled] {
    pointer-events: none !important;
  }
}

.#{$prefix-cls}-settings-panel {
  flex-grow: 0;
  flex-shrink: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  z-index: 2;
  width: 300px;
  background: var(--dn-composite-panel-tabs-content-bg-color);
  border-left: 1px solid var(--dn-panel-border-color);
  height: 100%;
  box-sizing: content-box;
  user-select: none;

  &.pinning {
    position: absolute;
    z-index: 10;
    top: 0;
    right: 0;
    box-shadow: 2px 5px 10px rgba(102, 102, 102, 0.52);
    border-left: 1px solid transparent;
  }

  &-header {
    padding: 14px 7px;
    color: var(--dn-composite-panel-tabs-header-color);
    line-height: 18px;
    font-size: 16px;
    border-bottom: 1px solid var(--dn-panel-border-color);
    display: flex;
    justify-content: space-between;

    &-actions {
      display: flex;
      align-items: center;

      & > * {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    &-pin {
      transition: all 0.15s ease-in-out;

      &:hover {
        transform: scale(1.1);
      }
    }

    &-title {
      font-size: 14px;
    }

    &-close {
      transition: all 0.15s ease-in-out;

      &:hover {
        transform: rotate(90deg);
      }
    }
  }

  &-body {
    flex-grow: 2;
    flex-shrink: 2;
    overflow: overlay;
    height: 100%;
  }

  &-opener {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    background: var(--dn-composite-panel-tabs-content-bg-color);
    border: 1px solid var(--dn-panel-border-color);
    color: var(--dn-composite-panel-tabs-color);
    box-shadow: 0 0 6px rgb(0 0 0 / 10%);
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    cursor: pointer;

    .dn-icon {
      transition: all 0.15s ease-in-out;
    }

    &:hover .dn-icon {
      transform: rotate(45deg);
    }
  }
}
