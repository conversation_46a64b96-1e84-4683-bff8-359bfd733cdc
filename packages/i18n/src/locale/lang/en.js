export default {
  // commonly used
  public_name: 'Name',
  public_index: 'Index',
  public_type: 'Type',
  public_title: 'Title',
  public_status: 'Status',
  public_description: 'Description',
  public_version: 'Version',
  public_operation: 'Operation',
  public_operation_abb: 'Op',
  public_operation_available: 'Available Operation',
  public_object: 'Object',
  public_create_time: 'Create Time',
  public_creator: 'Creator',
  public_update_time: 'Update Time',
  public_change_time: 'Modification Time',
  public_serial_number: '#',
  public_query: 'Query',
  public_aggregate: 'Aggregate',
  public_table: 'Table',
  public_table_name: 'Table Name',
  public_total: 'Total',
  public_database: 'Database',
  public_file: 'File',
  public_schema: 'Schema',
  public_basic_settings: 'Basic Settings',
  public_advanced_settings: 'Advanced Settings',
  public_ssl_settings: 'SSL Settings',
  public_ssh_settings: 'SSH Settings',
  public_recommend: 'Recommend',
  public_board_view: 'Board',
  public_list_view: 'List',
  public_all: 'All',
  public_no_permissions: 'No Permissions',
  public_yes: 'Yes',
  public_no: 'No',
  public_tags: 'Tags',
  public_phone: 'Phone Number',
  public_email: 'Email',
  public_view_details: 'View Details',
  public_warn: 'Warn',
  public_error: 'Error',
  // state
  public_status_ready: 'Valid',
  public_status_invalid: 'Invalid',
  public_status_testing: 'Testing',
  public_status_wait_run: 'Ready',
  public_status_edit: 'Editing',
  public_status_running: 'Running',
  public_status_starting: 'Starting',
  public_status_scheduling: 'Scheduling',
  public_status_stopping: 'Stopping',
  public_status_force_stopping: 'Force To Stop',
  public_status_waiting: 'Waiting',
  public_status_scheduled: 'Scheduled',
  public_status_complete: 'Completed',
  public_status_stop: 'Stopped',
  public_status_finished: 'Completed',
  public_status_error: 'Error',
  public_status_failed: 'Failed',
  public_status_schedule_failed: 'Scheduling Failed',
  public_status_renewing: 'Resetting',
  public_status_renew_failed: 'Reset Failed',
  public_status_renew_normal: 'Normal',
  public_status_published: 'Published',
  public_status_unpublished: 'Unpublished',
  public_status_to_be_generated: 'Generating',
  public_status_to_be_restart: 'Wait for restart',
  public_status_restarting: 'Restarting',
  public_status_deploying: 'Deploying',
  public_status_altering: 'Altering',
  public_status_creating: 'Creating',
  public_status_activated: 'Activated',
  public_status_init: 'Init',
  public_status_waiting_delete: 'Waiting Delete',
  public_status_deleting: 'Deleting',
  public_status_delete_failed: 'Delete Failed',
  public_status_deleted: 'Deleted',
  public_status_task_init: 'Initializing',
  public_status_do_snapshot: 'Full Syncing',
  public_status_snapshot_completed: 'Full Sync Completed',
  public_status_do_cdc: 'Incremental Syncing',
  public_status_ping_timeout: 'Ping Timeout',
  // button
  public_button_edit: 'Edit',
  public_button_confirm: 'OK',
  public_button_cancel: 'Cancel',
  public_button_back: 'Back',
  public_button_save: 'Save',
  public_button_check: 'View',
  public_button_delete: 'Delete',
  public_button_setting: 'Settings',
  public_button_reduction: 'Reduction',
  public_button_copy: 'Copy',
  public_button_details: 'Details',
  public_button_preview: 'Preview',
  public_button_reload: 'Reload',
  public_button_restart: 'Restart',
  public_button_start: 'Start',
  public_button_stop: 'Stop',
  public_button_force_stop: 'Force Stop',
  public_button_close: 'Close',
  public_button_reset: 'Reset',
  public_button_create: 'Create',
  public_button_quickly_create_task: 'Quickly Create Task',
  public_button_export: 'Export',
  public_button_download: 'Download',
  public_button_add: 'New',
  public_button_bulk_operation: 'Batch Operation',
  public_button_bulk_tag: 'Set Tag',
  public_button_revise: 'Change',
  public_button_refresh: 'Refresh',
  public_button_public: 'Publish',
  public_button_revoke: 'Undo',
  public_button_submit: 'Submit',
  public_button_upload: 'Upload',
  public_button_bind: 'Bind',
  public_button_unbind: 'Unbind',
  public_button_retry: 'Retry',
  public_button_expand: 'Expand',
  public_button_fold: 'Fold',
  public_button_inquire: 'Inquire',
  public_button_generate: 'Generate',
  public_button_sort: 'Sort',
  public_button_subscription: 'Subscribe',
  public_button_unsubscribe: 'Unsubscribe',
  public_button_renew: 'Renew',
  public_button_pay: 'Pay',
  public_button_renewal: 'Renewal',
  public_button_order: 'Order',
  public_button_next: 'Next',
  public_button_previous: 'Previous',
  public_button_search: 'Search',
  public_button_stop_mining: 'Stop Mining',
  public_button_stop_recover: 'Resume Mining',
  public_button_execute: 'Execute',
  public_button_obtain: 'Obtain',
  public_button_help: 'Help',
  public_button_understand: 'Finish',
  // information
  public_message_title_prompt: 'Prompt',
  public_message_delete_confirm: 'Delete',
  public_message_save_ok: 'Saved Successfully',
  public_message_save_fail: 'Save Failed',
  public_message_copy_success: 'Copy Successful',
  public_message_copy_fail: 'Copy Failed',
  public_message_copied: 'Copied',
  public_message_delete_ok: 'Delete Successful',
  public_message_delete_fail: 'Delete Failed',
  public_message_deleting: 'Deleting',
  public_message_operation_success: 'Operation Succeeded',
  public_message_operation_failed: 'Operation Failed',
  public_message_request_error: 'Failed to request data',
  public_message_loading: 'Loading',
  public_message_401: 'Login expired, Please login again',
  public_message_404: 'The requested resource does not exist',
  public_message_5xx: 'Server Exception',
  public_message_network_unconnected:
    'An abnormal network connection has been detected',
  public_message_network_connected:
    'The network has been successfully restored to its normal operating state',
  public_message_request_timeout: 'Request Timeout',
  public_message_send_success: 'Send Successfully',
  public_message_download_ok: 'Download Successfully',
  public_message_publish_successful: 'Publish Successful',
  // drop-down list
  public_select_placeholder: 'Please Select',
  public_select_option_all: 'Select All',
  public_select_option_default: 'Default',
  // Input box
  public_input_placeholder: 'Please Enter..',
  public_input_placeholder_name: 'Please enter a name',
  public_input_placeholder_search: 'Type here to search..',
  // form
  public_form_not_empty: 'Cannot be empty',
  // data
  public_data_see_more: 'See More',
  public_data_no_data: 'No Data',
  public_data_no_data1: 'No Data',
  public_data_no: 'None',
  public_data_no_find_result: 'No matching results found',
  public_data_default: 'Defaults',
  public_data_filter_condition: 'Filter Condition',
  public_data_type: 'Data Type',
  // time
  public_time_ms: 'Milliseconds',
  public_time_s: 'Seconds',
  public_time_m: 'Minute',
  public_time_h: 'Hour',
  public_time_d: 'Day',
  public_time_day: 'Day',
  public_time_month: 'Month',
  public_time_year: 'Year',
  public_time_input: 'Input',
  public_time_output: 'Output',
  public_time_five_min: 'The last five minutes',
  public_time_ten_min: 'The last ten minutes',
  public_time_thirty_min: 'The last thirty minutes',
  public_time_last_hour: 'The last hour',
  public_time_period: 'Period',
  public_time_every_day: 'Every day',
  public_time_Last_six_hours: 'The last 6 hours',
  public_time_last_day: 'Last 1 day',
  public_time_last_three_days: 'The last 3 days',
  public_time_custom_time: 'Custom Time',
  public_time_user_specified_time: 'User specified time',
  public_time_current: 'Current Time',
  // relation
  public_or: 'or',
  public_and: 'and',
  // unit
  public_unit_ge: '',
  // agent
  public_agent: 'Agent',
  public_agent_name: 'Agent Name',
  public_agent_button_create: 'Create Agent',
  public_agent_button_deploy: 'Deploy',
  public_agent_button_deploy_now: 'Deploy Now',
  public_agent_button_deploy_later: 'Deploy Later',
  public_agent_button_auto_upgrade: 'Auto Upgrade',
  public_agent_button_manual_upgrade: 'Manual Upgrade',
  public_agent_status_offline: 'Offline',
  public_agent_status_to_be_deployed: 'To be deployed',
  // connect
  public_connection: 'Connection',
  public_connection_name: 'Name',
  public_connection_type: 'Type',
  public_connection_type_source: 'Source',
  public_connection_type_target: 'Target',
  public_connection_type_source_and_target: 'Source&Target',
  public_connection_information: 'Address',
  public_connection_schema_status: 'Schema',
  public_connection_schema_status_tip:
    'The connection can only be created normally once the Schema has been loaded.',
  public_connection_table_structure_update_time: 'Schema Load Time',
  public_connection_button_create: 'Create Connection',
  public_connection_button_copy: 'Copy Connection',
  public_connection_button_test: 'Test',
  public_connection_button_connection_test: 'Connection Test',
  public_connection_button_load_schema: 'Load Schema',
  public_connection_form_database_name: 'Database Name',
  public_connection_form_database_type: 'Database Type',
  public_connection_form_database_address: 'Database Address',
  public_connection_form_host: 'Port',
  public_connection_form_account: 'Account',
  public_connection_form_password: 'Password',
  public_connection_form_schema: 'Schema',
  public_connection_form_other_connection_string:
    'Other connection string parameters',
  public_connection_form_time_zone_of_time_type: 'Time zone of time type',
  public_connection_form_link_plugin_source: 'Link Plugin Source',
  public_source_database: 'Source Database',
  public_target_database: 'Target Database',
  public_source_connection: 'Source Connection',
  public_target_connection: 'Target Connection',
  // Task
  public_task: 'Task',
  public_task_name: 'Task Name',
  public_task_type: 'Task Type',
  public_task_sync_type: 'Sync Type',
  public_task_type_migrate: 'Data Replication',
  public_task_type_sync: 'Data Transformation',
  public_task_type_heartbeat: 'Heartbeat Task',
  public_task_type_log_collector: 'Shared Mining',
  public_task_type_initial_sync: 'Full Sync',
  public_task_type_cdc: 'Incremental Sync',
  public_task_type_initial_sync_and_cdc: 'Full & Incremental Sync',
  public_task_status: 'Task Status',
  public_task_last_run_time: 'Last Run Time',
  public_task_cdc_time_point: 'Last Event Time',
  public_task_full_start_time: 'Full start time',
  public_task_full_completion_time: 'Full completion time',
  public_task_full_sync_progress: 'Full Progress',
  public_task_max_incremental_delay: 'Maximum replication delay',
  public_task_heartbeat_time: 'Task heartbeat time',
  public_task_mission_error: 'Task Error',
  public_task_reasons_for_error: 'Reasons for error',
  public_task_log: 'Task Log',
  public_task_create: 'Create Task',
  public_task_copy: 'Copy Task',
  public_task_import: 'Import Task',
  public_task_export: 'Export Task',
  public_task_error_schedule_limit:
    'Your currently online Agent has reached its maximum task limit. Please start the offline Agents first.\n',
  // node
  public_node_name: 'Node Name',
  public_node_type: 'Node Type',
  public_node_source: 'Source Node',
  public_node_processor: 'Processing Node',
  public_node_target: 'Target Node',
  // task event
  public_event_incremental_delay: 'Incremental Delay',
  public_event_total_input: 'Total Input',
  public_event_total_output: 'Total Output',
  public_event_insert: 'Insert',
  public_event_update: 'Update',
  public_event_ddl: 'DDL',
  public_event_cdc_placeholder: 'No incremental data',
  //milestone
  public_milestone_time_cdc_consuming: 'start time-consuming',
  public_milestone_time_consuming: 'time-consuming',
  public_milestone_time_table_structure:
    'A total of migration {val} table structure',
  public_milestone_time_scheduling: 'Task is scheduled to {val}',
  public_milestone_connection_succeeded: 'Connection succeeded',
  // 外存
  public_external_memory_name: 'Storage Name',
  public_external_memory_configuration: 'External storage configuration',
  public_external_memory_type: 'External memory type',
  public_external_memory_table: 'External storage table name',
  public_external_memory_connection: 'External memory connection',
  public_external_memory_info: 'External storage information',

  public_shared_mining: 'CDC Log Cache',
  public_external_storage: 'External Storage',

  public_page_title_advanced_features: 'Advanced',

  public_please_wait: 'Please wait a moment...',
  public_view_all: 'View All',

  public_today: 'Today',
  public_yesterday: 'Yesterday',
  public_this_hour: 'This Hour',
  public_this_week: 'This Week',
  public_this_month: 'This Month',
  public_this_year: 'This Year',
  public_last_week: 'Last Week',
  public_last_month: 'Last Month',
  public_last_hour: 'Last Hour',
  public_last_year: 'Last Year',
  public_date_past: 'Past',
  public_date_past_val: 'Previous',
  public_date_current: 'Current',
  public_date_current_prefix: 'Right now, this is ',
  public_date_relative: 'Relative Dates',
  public_date_specific: 'Specific Dates',
  public_unit_hour: 'hours',
  public_unit_day: 'days',
  public_unit_week: 'weeks',
  public_unit_month: 'months',
  public_unit_year: 'years',

  public_remark: 'Remark',
  public_task_alert: 'Task Alert',
  public_request: 'Request',
  public_request_headers: 'Request Headers',
  public_request_content: 'Request Content',
  public_response: 'Response',
  public_response_headers: 'Response Headers',
  public_response_content: 'Response Content',
  public_resend: 'Resend',

  public_unique_index: 'Unique Index',
  public_normal_index: 'Index',
  public_batch_publish: 'Batch Publish',
  public_database_time: 'Database Time',
  public_data_update_time: 'Last Updated',

  public_create_source_connection: 'Create Source Connection',
  public_create_target_connection: 'Create Target Connection',
  public_configuration_task: 'Configuration Task',
  public_need_help: 'Need Help?',
  public_test_and_continue: 'Test Connection and Continue',
  menu_tour_instance:
    'Here you can subscribe to the semi-managed engine deployed locally',
  menu_tour_instance_link: 'Learn More About Semi-Managed Engines',
  menu_tour_connection: 'Manage and Add Your Data Sources/Targets Here',
  menu_tour_create_task:
    'Click Here to Try Creating Advanced Replication and Synchronization Tasks',
  public_task_count: '{val} tasks',
  public_retrying: 'Retrying',
  public_next_retry_time: 'next retry scheduled for',
  public_data_capture: 'Data Capture',
  public_keywords: 'Keywords',
  public_data_capture_keywords_ph: 'Please enter keywords to capture data',
  public_view_docs: 'View Docs',
  public_file_name: 'File Name',
  public_file_size: 'File Size',
  public_log_download: 'Log Download',
  public_alert_401_tip:
    'Your session has expired or logged in on another device. Please log in again to continue.',
  public_alert_401: 'Session Expired Reminder',
  public_document: 'Document',
  public_array: 'Array',
  public_load_more: 'Load More',
  public_loading: 'Loading...',
  public_load_end: 'End',
  public_store_type: 'Store Type',
  public_from_db_type: 'From DB Type',
  public_sample_size: 'Sample Size',
  public_data_validation: 'Data Validation',
  public_validation_record: 'Validation Record',
  public_foreign_key_tip: 'Foreign key {name} to {val}',
  public_form_design: 'Form Design',
  public_code_edit: 'Code Edit',
  public_preview_form: 'Preview Form',
  public_template_example: 'Template Example',
  public_usd: 'USD',
  public_cny: 'CNY',
  public_hkd: 'HKD',
  public_selfHost: 'Semi-Managed',
  public_fullManagement: 'Fully-Managed',

  public_error_time: 'Error Time',
  public_error_log: 'Error Log',
  public_complete_time: 'Complete Time',
  public_cdc_time: 'CDC Time',
  public_stop_time: 'Stop Time',
  public_delay_time: 'Delay Time',
  public_current_cost_time: 'Current Cost Time',
  public_threshold: 'Threshold',
  public_start_import: 'Start Import',
  public_generate_recovery_sql: 'Generate Recovery SQL',
  public_start_generate_recovery_sql: 'Start Generate Recovery SQL',
  public_generate_recovery_sql_success: 'Generate Recovery SQL Success',
  public_download_recovery_sql: 'Download Recovery SQL',
}
