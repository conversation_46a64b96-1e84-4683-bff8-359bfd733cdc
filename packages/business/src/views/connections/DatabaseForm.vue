<script>
import { action } from '@formily/reactive'
import {
  clusterApi,
  connectionsApi,
  databaseTypesApi,
  externalStorageApi,
  proxyApi,
} from '@tap/api'

import { VIcon } from '@tap/component'
import resize from '@tap/component/src/directives/resize'
import { SchemaToForm } from '@tap/form'
import i18n from '@tap/i18n'
import { checkConnectionName, submitForm, uuid } from '@tap/shared'
import { cloneDeep, isEmpty } from 'lodash-es'

import { DatabaseIcon } from '../../components'
import ConnectorDoc from '../../components/ConnectorDoc'
import mixins from '../../components/create-connection/mixins'
import SceneDialog from '../../components/create-connection/SceneDialog.vue'
import { ConnectionDebug } from './ConnectionDebug'
import { JsDebug } from './JsDebug'
import Test from './Test'
import UsedTaskDialog from './UsedTaskDialog'
import { getConnectionIcon } from './util'

export default {
  name: 'DatabaseForm',
  name: 'DatabaseForm',
  components: {
    ConnectorDoc,
    DatabaseIcon,
    SceneDialog,
    Test,
    VIcon,
    SchemaToForm,
    ConnectionDebug,
    UsedTaskDialog,
    JsDebug,
  },
  directives: {
    resize,
  },
  mixins: [mixins],
  inject: ['checkAgent', 'buried', 'lockedFeature'],
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.pathUrl = from?.fullPath
    })
  },
  data() {
    const validateRename = (rule, value, callback) => {
      if (!this.renameData.rename || !this.renameData.rename.trim()) {
        callback(
          new Error(
            this.$t('public_connection_name') +
              this.$t('public_form_not_empty'),
          ),
        )
      } else if (!checkConnectionName(this.renameData.rename)) {
        callback(
          new Error(
            i18n.t(
              'packages_business_connections_databaseform_mingchengguizezhong',
            ),
          ),
        )
      } else {
        callback()
      }
    }
    return {
      isDaas: import.meta.env.VUE_APP_PLATFORM === 'DAAS',
      rules: [],
      id: '',
      commandCallbackFunctionId: '',
      visible: false,
      model: {
        config: null,
      },
      status: '',
      loadingFrom: true,
      dialogTestVisible: false,
      dialogDatabaseTypeVisible: false,
      dialogEditNameVisible: false,
      submitBtnLoading: false,
      editBtnLoading: false,
      renameData: {
        rename: '',
      },
      width: 440,
      height: 300,
      renameRules: {
        rename: [{ validator: validateRename, trigger: 'blur' }],
      },
      pdkOptions: {},
      schemaData: null,
      jsDebugSchemaData: null,
      jsDebugParamsMethod: null,
      jsDebugDataMethod: null,
      schemaScope: null,
      doc: '',
      pathUrl: '',
      showDebug: false,
      showJsDebug: false,
      heartbeatTaskId: '',
      connectionLogCollectorTaskDialog: false,
      // 当前连接是否有共享缓存任务使用
      connectionLogCollectorTaskData: {
        items: [],
        total: 0,
      },
      showAgentIpAlert: false,
      schemaFormInstance: null,
    }
  },
  computed: {
    hasBackListener() {
      return 'onBack' in this.$attrs
    },
    connectionId() {
      return this.model?.id || this.commandCallbackFunctionId
    },
    docUrl() {
      return `https://docs.tapdata.${
        !this.$store.getters.isDomesticStation || this.$i18n.locale === 'en'
          ? 'io'
          : 'net'
      }/prerequisites/allow-access-network`
    },
  },
  async created() {
    if (!this.isDaas) {
      const { items: agentData } = await this.$axios.get(
        `api/tcm/agent?filter=${encodeURIComponent(
          JSON.stringify({
            where: {
              agentType: 'Cloud',
              status: 'Running',
            },
          }),
        )}`,
      )

      if (agentData.length) {
        this.showAgentIpAlert = true
      }
    }
  },
  mounted() {
    this.schemaFormInstance = this.$refs.schemaToForm?.form // 获取表单的 form
    this.id = this.$route.params.id || ''
    const { fromPath } = this.$route.query
    if (fromPath) {
      this.pathUrl = fromPath
    }
    this.getPdkForm()
  },
  methods: {
    goBack() {
      const msg = this.$route.params.id
        ? i18n.t('packages_business_connections_databaseform_cicaozuohuidiu')
        : i18n.t('packages_business_connections_databaseform_cicaozuohuidiu')
      // let title = this.$route.params.id ? '是否放弃修改内容？' : '是否放弃创建该连接？'

      this.$confirm(msg, '', {
        confirmButtonText: this.$t('packages_business_connection_form_give_up'),
        cancelButtonText: this.$t('public_button_cancel'),
        type: 'warning',
        showClose: false,
      }).then((resFlag) => {
        if (!resFlag) {
          return
        }
        this.gotoBackPath()
      })
    },
    gotoBackPath() {
      if (this.pathUrl) {
        this.$router.push(
          this.pathUrl === '/'
            ? {
                name: 'connections',
              }
            : this.pathUrl,
        )
      } else {
        this.$router.back()
      }
    },
    submit() {
      this.buried('connectionSubmit')
      this.pdkFormModel = this.$refs.schemaToForm?.getForm?.()
      this.schemaFormInstance?.validate().then(
        () => {
          this.submitBtnLoading = true
          // 保存数据源
          const id = this.$route.params?.id
          const { pdkOptions } = this
          const formValues = this.$refs.schemaToForm?.getFormValues?.()
          const { __TAPDATA } = formValues
          formValues.__connectionType = __TAPDATA.connection_type
          delete formValues.__TAPDATA
          const params = Object.assign(
            {
              ...__TAPDATA,
              database_type: pdkOptions.type,
              pdkHash: pdkOptions.pdkHash,
            },
            {
              status: 'testing',
              schema: {},
              retry: 0,
              nextRetry: null,
              response_body: {},
              project: '',
              submit: true,
              pdkType: 'pdk',
            },
            {
              config: formValues,
            },
          )
          let promise = null
          if (id) {
            params.id = id
            promise = connectionsApi.updateById(id, params)
          } else {
            const { commandCallbackFunctionId } = this
            params.status = this.status ? this.status : 'testing' //默认值 0 代表没有点击过测试
            promise = connectionsApi.create(params, {
              id: commandCallbackFunctionId,
            })
          }
          promise
            .then(() => {
              this.buried('connectionSubmit', '', {
                result: true,
              })
              this.$message.success(this.$t('public_message_save_ok'))
              this.gotoBackPath()
            })
            .catch(() => {
              this.buried('connectionSubmit', '', {
                result: false,
              })
            })
            .finally(() => {
              this.submitBtnLoading = false
            })
        },
        () => {
          this.$el
            .querySelector('.formily-element-form-item-error')
            .scrollIntoView()
        },
      )
    },
    //开始测试
    async startTest() {
      this.buried('connectionTest')
      this.checkAgent(() => {
        this.schemaFormInstance.validate().then(
          () => {
            this.startTestPdk()
          },
          () => {
            this.$el
              .querySelector('.formily-element-plus-form-item-error')
              .scrollIntoView()
          },
        )
      }).catch(() => {
        this.buried('connectionTestAgentFail')
      })
    },
    startTestPdk() {
      const formValues = this.$refs.schemaToForm?.getFormValues?.()
      const { __TAPDATA } = formValues
      formValues.__connectionType = __TAPDATA.connection_type
      Object.assign(this.model, __TAPDATA)
      delete formValues.__TAPDATA
      this.model.config = formValues
      this.model.pdkType = 'pdk'
      this.model.pdkHash = this.$route.query?.pdkHash
      this.model.database_type = this.pdkOptions.pdkId
      this.dialogTestVisible = true
      if (this.$route.params.id) {
        //编辑需要特殊标识 updateSchema = false editTest = true
        this.$refs.test.start(false, true)
      } else {
        delete this.model.id
        this.$refs.test.start(false)
      }
    },
    returnTestData(data) {
      if (!data.status || data.status === null) return
      this.status = data.status
      this.buried('connectionTest', '', {
        result: data.status === 'ready',
      })
    },
    //取消
    handleCancelRename() {
      this.renameData.rename = this.model.name
      this.$refs.renameForm.clearValidate()
      this.dialogEditNameVisible = false
    },
    //保存名字
    submitEdit() {
      this.$refs.renameForm.validate((valid) => {
        if (valid) {
          this.editBtnLoading = true
          if (this.renameData.rename === '') {
            this.editBtnLoading = false
            this.renameData.rename = this.model.name
            this.$refs.renameForm.clearValidate()
            return
          }
          const params = {
            name: this.renameData.rename,
            id: this.model.id,
            submit: true,
          }
          connectionsApi
            .patchId(params)
            .then(() => {
              this.editBtnLoading = false
              this.model.name = this.renameData.rename
              const { name } = this.model
              this.schemaFormInstance.setValues({
                __TAPDATA: {
                  name,
                },
              })
              this.$refs.renameForm.clearValidate()
              this.$message.success(this.$t('public_message_save_ok'))
              this.dialogEditNameVisible = false
            })
            .catch(() => {
              this.$refs.renameForm.clearValidate()
              this.editBtnLoading = false
            })
        }
      })
    },
    handleDatabaseType(item) {
      this.dialogDatabaseTypeVisible = false
      const { pdkHash, pdkId } = item
      this.$router
        .push({
          name: 'connectionCreate',
          query: {
            pdkHash,
            pdkId,
          },
        })
        .then(() => {
          location.reload()
        })
    },
    async getPdkForm() {
      const pdkHash = this.$route.query?.pdkHash
      const data = await databaseTypesApi.pdkHash(pdkHash)
      const id = this.id || this.$route.params.id
      this.pdkOptions = data || {}

      if (
        this.pdkOptions.capabilities?.some(
          (t) => t.id === 'command_callback_function',
        )
      ) {
        this.commandCallbackFunctionId = await proxyApi.getId()
      }

      const { connectionType } = this.pdkOptions
      const typeEnum = ['source', 'target'].includes(connectionType)
        ? [
            {
              label: this.$t(`public_connection_type_${connectionType}`),
              value: connectionType,
              tip: this.$t(
                `packages_business_connection_form_${connectionType}_tip`,
              ),
            },
          ]
        : [
            {
              label: this.$t('public_connection_type_source_and_target'),
              value: 'source_and_target',
              tip: this.$t(
                'packages_business_connection_form_source_and_target_tip',
              ),
            },
            {
              label: this.$t('public_connection_type_source'),
              value: 'source',
              tip: this.$t('packages_business_connection_form_source_tip'),
            },
            {
              label: this.$t('public_connection_type_target'),
              value: 'target',
              tip: this.$t('packages_business_connection_form_target_tip'),
            },
          ]

      const endProperties = {}

      // 是否支持共享挖掘
      if (
        !this.lockedFeature.sharedMiningList &&
        this.pdkOptions.capabilities?.some(
          (t) => t.id === 'stream_read_function',
        )
      ) {
        Object.assign(endProperties, {
          shareCdcEnable: {
            type: 'boolean',
            default: false,
            title: this.$t('packages_business_connection_form_shared_mining'),
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              tooltip: this.$t(
                'packages_business_connection_form_shared_mining_tip',
              ),
            },
            'x-component': 'Switch',
            'x-component-props': {
              placeholder: this.$t(
                'packages_business_connection_form_shared_mining_tip',
              ),
            },
          },
          shareCDCExternalStorageId: {
            title: this.$t('packages_business_external_storage'), //外存配置
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              onChange: `{{ val => shareCDCExternalStorageIdOnChange(val, $form) }}`,
              disabled: `{{ getShareCDCExternalStorageIdDisabled() }}`,
            },
            'x-reactions': [
              {
                dependencies: ['__TAPDATA.shareCdcEnable'],
                fulfill: {
                  state: {
                    display: '{{$deps[0] ? "visible" : "hidden"}}',
                  },
                },
              },
              '{{useAsyncDataSourceByConfig({service: loadExternalStorage, withoutField: true}, $values.id ? $self.value : null)}}',
              {
                dependencies: ['__TAPDATA.shareCdcEnable'],
                fulfill: {
                  state: {
                    value: `{{ $deps[0] ? $self.value || $self.dataSource?.find(item => item.isDefault)?.value : '' }}`,
                  },
                },
              },
            ],
          },
          shareCDCExternalStorageIdTips: {
            type: 'void',
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              colon: false,
              className: 'mt-n6',
            },
            'x-component': 'Space',
            'x-reactions': [
              {
                fulfill: {
                  state: {
                    display: `{{ getShareCDCExternalStorageIdDisabled() ? "visible" : "hidden" }}`,
                  },
                },
              },
            ],
            properties: {
              tips: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'Text',
                'x-component-props': {
                  content: i18n.t(
                    'packages_business_connections_databaseform_dangqianlianjiede',
                  ),
                  class: 'color-danger',
                },
              },
              Link: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'Button',
                'x-component-props': {
                  type: 'text',
                  class: 'text-decoration-underline',
                  onClick: '{{handleLogCollectorTaskDialog}}',
                },
                'x-content': i18n.t(
                  'packages_business_connections_databaseform_chakanwajueren',
                ),
              },
            },
          },
        })
      }

      // 是否支持包含表
      if (
        this.pdkOptions.capabilities?.some(
          (t) => t.id === 'get_table_names_function',
        )
      ) {
        Object.assign(endProperties, {
          loadAllTables: {
            type: 'boolean',
            default: true,
            title: i18n.t(
              'packages_business_connections_databaseform_baohanbiao',
            ),
            'x-decorator': 'FormItem',
            'x-component': 'Radio.Group',
            enum: [
              {
                label: i18n.t('public_select_option_all'),
                value: true,
              },
              {
                label: i18n.t(
                  'packages_business_connections_databaseform_zidingyi',
                ),
                value: false,
              },
            ],
          },
          table_filter: {
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              placeholder: this.$t(
                'packages_business_connection_form_database_owner_tip',
              ),
            },
            'x-decorator-props': {
              colon: false,
            },
            'x-reactions': {
              dependencies: ['__TAPDATA.loadAllTables'],
              fulfill: {
                state: {
                  display: '{{$deps[0] ? "hidden" : "visible"}}',
                },
              },
            },
          },
          openTableExcludeFilter: {
            title: i18n.t(
              'packages_business_connections_databaseform_paichubiao',
            ),
            type: 'boolean',
            default: false,
            'x-decorator-props': {
              feedbackLayout: 'none',
            },
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
          },
          openTableExcludeFilterTips: {
            type: 'void',
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              colon: false,
            },
            'x-component': 'Text',
            'x-component-props': {
              icon: 'info',
              content: i18n.t(
                'packages_business_connections_databaseform_keyicongbaohan',
              ),
            },
          },
          tableExcludeFilter: {
            type: 'string',
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              placeholder: this.$t(
                'packages_business_connection_form_database_owner_tip',
              ),
            },
            'x-decorator-props': {
              colon: false,
              style: {
                'margin-top': '-22px',
              },
            },
            'x-reactions': {
              dependencies: ['__TAPDATA.openTableExcludeFilter'],
              fulfill: {
                state: {
                  display: '{{ $deps[0] ? "visible" : "hidden"}}',
                },
              },
            },
          },
        })
      }

      Object.assign(endProperties, {
        accessNodeType: {
          type: 'string',
          title: this.$t('packages_business_connection_form_access_node'),
          default: 'AUTOMATIC_PLATFORM_ALLOCATION',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            tooltip: this.$t(
              'packages_business_connection_form_access_node_tip',
            ),
          },
          'x-component': 'Select',
          enum: [
            {
              label: this.$t('packages_business_connection_form_automatic'),
              value: 'AUTOMATIC_PLATFORM_ALLOCATION',
            },
            {
              label: this.$t('packages_business_connection_form_manual'),
              value: 'MANUALLY_SPECIFIED_BY_THE_USER',
            },
          ],
          'x-reactions': [
            {
              dependencies: ['__TAPDATA.shareCdcEnable'],
              fulfill: {
                state: {
                  value: `{{!$isDaas && $deps[0] ? 'MANUALLY_SPECIFIED_BY_THE_USER' : $self.value}}`,
                  dataSource: `{{!$isDaas && $deps[0] ? [
                    { label: '${this.$t(
                      'packages_business_connection_form_automatic',
                    )}', value: 'AUTOMATIC_PLATFORM_ALLOCATION', disabled: true },
                    { label: '${this.$t(
                      'packages_business_connection_form_manual',
                    )}', value: 'MANUALLY_SPECIFIED_BY_THE_USER' }
                  ] : !$isDaas ? [
                    { label: '${this.$t(
                      'packages_business_connection_form_automatic',
                    )}', value: 'AUTOMATIC_PLATFORM_ALLOCATION' },
                    { label: '${this.$t(
                      'packages_business_connection_form_manual',
                    )}', value: 'MANUALLY_SPECIFIED_BY_THE_USER' }
                  ] : [
                    { label: '${this.$t(
                      'packages_business_connection_form_automatic',
                    )}', value: 'AUTOMATIC_PLATFORM_ALLOCATION' },
                    { label: '${this.$t(
                      'packages_business_connection_form_manual',
                    )}', value: 'MANUALLY_SPECIFIED_BY_THE_USER' },
                    {
                      label: '${this.$t('packages_business_connection_form_group')}',
                      value: 'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP'
                    }
                  ]}}`,
                },
              },
            },
            {
              target: '__TAPDATA.accessNodeProcessId',
              effects: ['onFieldInputValueChange'],
              fulfill: {
                state: {
                  value: '',
                  // value: `{{console.log("$target.dataSource", $target.dataSource), $target.value ? '' : $target.dataSource && $target.dataSource[0] ? $target.dataSource[0].value : ''}}`
                },
              },
            },
          ],
        },
        accessNodeOption: {
          type: 'string',
          'x-display': 'hidden',
          'x-reactions': [
            {
              dependencies: ['.accessNodeType'],
              fulfill: {
                state: {
                  visible:
                    "{{['MANUALLY_SPECIFIED_BY_THE_USER', 'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP'].includes($deps[0])}}",
                },
              },
            },
            '{{useAsyncDataSource(loadAccessNode, "dataSource", {value: $self.value})}}',
          ],
        },
        agentWrap: {
          type: 'void',
          'x-component': 'Space',
          'x-component-props': {
            class: 'w-100 align-items-start',
          },
          'x-reactions': {
            dependencies: ['.accessNodeType'],
            fulfill: {
              state: {
                visible:
                  "{{['MANUALLY_SPECIFIED_BY_THE_USER', 'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP'].includes($deps[0])}}",
              },
            },
          },
          properties: {
            accessNodeProcessId: {
              type: 'string',
              description: `{{$values.__TAPDATA.shareCdcEnable ? '${this.$t(
                'packages_business_agent_select_not_found_for_rocksdb',
              )}' : ''}}`,
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                colon: false,
                class: 'flex-1',
              },
              'x-component': 'Select',
              'x-component-props': {
                onChange: `{{ () => $self.setSelfErrors('') }}`,
              },
              'x-reactions': [
                // '{{useAsyncDataSource(loadAccessNode, "dataSource", {value: $self.value})}}',
                // 根据下拉数据判断是否存在已选的agent
                {
                  dependencies: [
                    '.accessNodeType',
                    '.accessNodeOption#dataSource',
                  ],
                  fulfill: {
                    state: {
                      title: `{{'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP' === $deps[0] ? '${i18n.t(
                        'packages_business_choose_agent_group',
                      )}': '${i18n.t('packages_business_choose_agent')}'}}`,
                    },
                    run: `
                console.log('$deps[1]', $deps)
                if (!$deps[1]) return
                $self.dataSource = $deps[1].filter(item => item.accessNodeType === $deps[0])
                if ($self.dataSource?.length) {
                // $self.dataSource = $deps[1].filter(item => item.accessNodeType === $deps[0])
                if ($self.value) {
                  const current = $self.dataSource.find(item => item.value === $self.value)
                  if (!current) {
                    $self.setSelfErrors('${this.$t('packages_business_agent_select_not_found')}')
                  }
                }
              }`,
                  },
                },
              ],
              // 校验下拉数据判断是否存在已选的agent
              'x-validator': `{{(value, rule, ctx)=> {
            if (!value) {
              let msg = '${this.$t('packages_business_agent_select_placeholder')}'
              const {shareCDCExternalStorageId} = $values.__TAPDATA
              if (shareCDCExternalStorageId) {
                const dataSource = $form.query('__TAPDATA.shareCDCExternalStorageId').get('dataSource')
                const type = dataSource.find(item => item.value === shareCDCExternalStorageId)?.type
                if (type === 'rocksdb') msg = '${this.$t('packages_business_agent_select_not_found_for_rocksdb')}'
              }
              return msg
            } else if (value && ctx.field.dataSource?.length) {
              const current = ctx.field.dataSource.find(item => item.value === value)
              if (!current) {
                $self.setSelfErrors('')
                return '${this.$t('packages_business_agent_select_not_found')}'
              }
            }
          }}}`,
            },
            priorityProcessId: {
              title: i18n.t('packages_business_priorityProcessId'),
              type: 'string',
              default: '',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                class: 'flex-1',
              },
              'x-component': 'Select',
              'x-reactions': {
                dependencies: [
                  '.accessNodeType',
                  '.accessNodeOption#dataSource',
                  '.accessNodeProcessId',
                ],
                fulfill: {
                  state: {
                    visible:
                      "{{'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP' === $deps[0]}}",
                  },
                  run: `
                    let children = []

                    if ($deps[1] && $deps[2]) {
                      children = $deps[1].find(item => item.accessNodeType === $deps[0] && item.value === $deps[2]).children || []
                    }

                    $self.dataSource = [
                      {
                        label:'${i18n.t('packages_business_connection_form_automatic')}',
                        value: ''
                      }
                    ].concat(children)

                    if ($self.value && !children.find(item => item.value === $self.value)) {
                      $self.value = ''
                    }
                  `,
                },
              },
            },
          },
        },

        schemaUpdateHour: {
          type: 'string',
          title: i18n.t(
            'packages_business_connections_databaseform_moxingjiazaipin',
          ),
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-decorator-props': {
            tooltip: i18n.t(
              'packages_business_connections_databaseform_shujuyuanzhongmo',
            ),
          },
          default: '02:00',
          enum: [
            {
              label: i18n.t(
                'packages_business_connections_databaseform_bujiazai',
              ),
              value: 'false',
            },
            '00:00',
            '01:00',
            '02:00',
            '03:00',
            '04:00',
            '05:00',
            '06:00',
            '07:00',
            '08:00',
            '09:00',
            '10:00',
            '11:00',
            '12:00',
            '13:00',
            '14:00',
            '15:00',
            '16:00',
            '17:00',
            '18:00',
            '19:00',
            '20:00',
            '21:00',
            '22:00',
            '23:00',
          ],
        },
        heartbeatObject: !this.pdkOptions.tags?.includes('NoHeartbeat')
          ? {
              type: 'void',
              'x-component': 'Space',
              title: i18n.t(
                'packages_business_connections_databaseform_kaiqixintiaobiao',
              ),
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                tooltip: i18n.t(
                  'packages_business_connections_databaseform_dakaixintiaobiao',
                ),
              },
              properties: {
                heartbeatEnable: {
                  type: 'boolean',
                  default: false,
                  'x-component': 'Switch',
                },
              },
              'x-reactions': {
                dependencies: ['__TAPDATA.connection_type'],
                fulfill: {
                  state: {
                    display:
                      '{{$deps[0] === "source_and_target" ? "visible":"hidden"}}',
                  },
                },
              },
            }
          : undefined,
      })

      if (this.isDaas) {
        endProperties.schemaUpdateHour.default = 'default'
        endProperties.schemaUpdateHour.enum.unshift({
          label: i18n.t('packages_business_connections_databaseform_system'),
          value: 'default',
        })
        endProperties.accessNodeType.enum.push({
          label: this.$t('packages_business_connection_form_group'),
          value: 'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP',
        })
      }

      const connectionProperties =
        data?.properties?.connection?.properties || {}
      const { OPTIONAL_FIELDS } = connectionProperties
      delete connectionProperties.OPTIONAL_FIELDS

      let reactions

      if (
        import.meta.env.VUE_APP_CONNECTOR_SCHEMA &&
        /^\s*[[{].*[\]}]\s*$/.test(import.meta.env.VUE_APP_CONNECTOR_SCHEMA)
      ) {
        reactions = JSON.parse(import.meta.env.VUE_APP_CONNECTOR_SCHEMA)
      } else if (import.meta.env.VUE_APP_HIDE_CONNECTOR_SCHEMA) {
        reactions = [
          {
            target: import.meta.env.VUE_APP_HIDE_CONNECTOR_SCHEMA,
            fulfill: {
              state: { display: 'hidden' },
            },
          },
        ]
      }

      if (!this.hasFeature('shareCdc')) {
        reactions ??= []
        reactions.push({
          target: '__TAPDATA.shareCdcEnable',
          fulfill: {
            state: { display: 'hidden' },
          },
        })
      }

      if (!this.hasFeature('oracleBridge')) {
        reactions ??= []
        reactions.push({
          target: 'logPluginName',
          when: '{{pdkId !== "postgres"}}',
          fulfill: { state: { display: 'hidden' } },
        })
      }

      const result = {
        type: 'object',
        'x-component-props': {
          width: 500,
        },
        properties: {
          START: {
            type: 'void',
            'x-index': 0,
            'x-reactions': reactions,
            properties: {
              __TAPDATA: {
                type: 'object',
                properties: {
                  name: {
                    type: 'string',
                    title: this.$t('public_connection_name'),
                    required: true,
                    'x-decorator': 'FormItem',
                    'x-component': 'Input',
                    'x-validator': {
                      pattern:
                        /^([\u4E00-\u9FA5A-Z])([\w\s\-.\u4E00-\u9FA5])*$/i,
                      message: i18n.t(
                        'packages_business_connections_databaseform_mingchengguizezhong',
                      ),
                    },
                  },
                  connection_type: {
                    type: 'string',
                    title: this.$t('public_connection_type'),
                    required: true,
                    default:
                      this.pdkOptions.connectionType || 'source_and_target',
                    enum: typeEnum,
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      feedbackLayout: 'none',
                    },
                    'x-component': 'Radio.Group',
                    'x-component-props': {
                      optionType: 'button',
                    },
                  },
                  connection_form_source_and_target_tip: {
                    type: 'void',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      colon: false,
                    },
                    'x-component': 'Text',
                    'x-component-props': {
                      icon: 'info',
                      content: this.$t(
                        'packages_business_connection_form_source_and_target_tip',
                      ),
                    },
                    'x-reactions': {
                      dependencies: ['__TAPDATA.connection_type'],
                      fulfill: {
                        schema: {
                          'x-decorator-props.style.display':
                            '{{$deps[0]==="source_and_target" ? null:"none"}}',
                        },
                      },
                    },
                  },
                  connection_form_source_tip: {
                    type: 'void',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      colon: false,
                    },
                    'x-component': 'Text',
                    'x-component-props': {
                      icon: 'info',
                      content: this.$t(
                        'packages_business_connection_form_source_tip',
                      ),
                    },
                    'x-reactions': {
                      dependencies: ['__TAPDATA.connection_type'],
                      fulfill: {
                        schema: {
                          'x-decorator-props.style.display':
                            '{{$deps[0]==="source" ? null:"none"}}',
                        },
                      },
                    },
                  },
                  connection_form_target_tip: {
                    type: 'void',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      colon: false,
                    },
                    'x-component': 'Text',
                    'x-component-props': {
                      icon: 'info',
                      content: this.$t(
                        'packages_business_connection_form_target_tip',
                      ),
                    },
                    'x-reactions': {
                      dependencies: ['__TAPDATA.connection_type'],
                      fulfill: {
                        schema: {
                          'x-decorator-props.style.display':
                            '{{$deps[0]==="target" ? null:"none"}}',
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          ...connectionProperties,
          END: {
            type: 'void',
            'x-index': 1000000,
            'x-component': 'FormCollapse',
            'x-component-props': {
              // class: 'border-bottom-0',
              activeKey: [],
            },
            properties: {
              advance: {
                type: 'void',
                'x-component': 'FormCollapse.Item',
                'x-component-props': {
                  title: i18n.t('public_advanced_settings'),
                },
                properties: {
                  OPTIONAL_FIELDS,
                  __TAPDATA: {
                    type: 'object',
                    // 'x-index': 1000000,
                    properties: endProperties,
                  },
                },
              },
              ssl: this.pdkOptions.tags?.includes('ssl')
                ? {
                    type: 'void',
                    'x-component': 'FormCollapse.Item',
                    'x-component-props': {
                      title: i18n.t('public_ssl_settings'),
                    },
                    properties: {
                      useSSL: {
                        // 使用 SSL
                        title: i18n.t('packages_business_use_ssl'),
                        type: 'boolean',
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          className: 'item-control-horizontal',
                          layout: 'horizontal',
                        },
                        'x-component': 'Switch',
                      },
                      sslCa: {
                        // CA 文件
                        title: i18n.t(
                          'packages_business_certificate_authority',
                        ),
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'TextFileReader',
                        'x-component-props': {
                          base64: true,
                        },
                        fileNameField: 'sslCAFile',
                      },
                      sslCert: {
                        // 客户端证书文件
                        title: i18n.t('packages_business_client_certificate'),
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'TextFileReader',
                        'x-component-props': {
                          base64: true,
                        },
                        fileNameField: 'sslCertFile',
                      },
                      sslKey: {
                        // 客户端密钥文件
                        title: i18n.t('packages_business_client_key'),
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'TextFileReader',
                        'x-component-props': {
                          base64: true,
                        },
                        fileNameField: 'sslKeyFile',
                      },
                      sslKeyPassword: {
                        // 客户端密钥密码
                        title: i18n.t('packages_business_client_key_password'),
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Password',
                      },
                    },
                  }
                : undefined,
              ssh: this.pdkOptions.tags?.includes('ssh')
                ? {
                    type: 'void',
                    'x-component': 'FormCollapse.Item',
                    'x-component-props': {
                      title: i18n.t('public_ssh_settings'),
                    },
                    properties: {
                      __TAPDATA: {
                        type: 'object',
                        properties: {
                          useSSH: {
                            // 使用 SSH 隧道
                            title: i18n.t('packages_business_use_ssh'),
                            type: 'boolean',
                            'x-decorator': 'FormItem',
                            'x-decorator-props': {
                              className: 'item-control-horizontal',
                              layout: 'horizontal',
                            },
                            'x-component': 'Switch',
                          },
                          sshHost: {
                            // 主机名
                            title: i18n.t('packages_business_ssh_host'),
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'Input',
                          },
                          sshPort: {
                            // 端口
                            title: i18n.t('packages_business_ssh_port'),
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'InputNumber',
                          },
                          sshUsername: {
                            // 用户名
                            title: i18n.t('packages_business_ssh_username'),
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'Input',
                          },
                          sshPassword: {
                            // 密码
                            title: i18n.t('packages_business_ssh_password'),
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'Password',
                          },
                        },
                      },
                    },
                  }
                : undefined,
            },
          },
        },
      }

      if (id) {
        await this.getPdkData(id)
        // 开启了共享挖掘
        const { shareCdcEnable, shareCDCExternalStorageId } = this.model
        if (shareCdcEnable && shareCDCExternalStorageId) {
          this.connectionLogCollectorTaskData =
            await connectionsApi.usingDigginTaskByConnectionId(id)
        }
        delete result.properties.START.properties.__TAPDATA.properties.name
      }

      this.setConnectionConfig()

      this.schemaScope = {
        $isDaas: this.isDaas,
        pdkId: this.pdkOptions.pdkId,
        isEdit: !!id,
        useAsyncDataSource: (
          service,
          fieldName = 'dataSource',
          ...serviceParams
        ) => {
          return (field) => {
            field.loading = true
            service({ field }, ...serviceParams).then(
              action.bound((data) => {
                if (fieldName === 'value') {
                  field.setValue(data)
                } else field[fieldName] = data
                field.loading = false
              }),
            )
          }
        },
        useAsyncDataSourceByConfig: (config, ...serviceParams) => {
          // withoutField: 不往service方法传field参数
          const {
            service,
            fieldName = 'dataSource',
            withoutField = false,
          } = config
          return (field) => {
            field.loading = true
            const fetch = withoutField
              ? service(...serviceParams)
              : service(field, ...serviceParams)
            fetch.then(
              action.bound((data) => {
                if (fieldName === 'value') {
                  field.setValue(data)
                } else field[fieldName] = data
                field.loading = false
              }),
            )
          }
        },
        loadAccessNode: async (fieldName, others = {}) => {
          const data = await clusterApi.findAccessNodeInfo()

          const mapNode = (item) => ({
            value: item.processId,
            label: `${item.agentName || item.hostName}（${
              item.status === 'running'
                ? i18n.t('public_status_running')
                : i18n.t('public_agent_status_offline')
            }）`,
            disabled: item.status !== 'running',
            accessNodeType: item.accessNodeType,
          })

          return (
            data
              ?.filter(
                (t) =>
                  t.status === 'running' ||
                  t.accessNodeType ===
                    'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP' ||
                  t.processId === others.value,
              )
              ?.map((item) => {
                if (
                  item.accessNodeType ===
                  'MANUALLY_SPECIFIED_BY_THE_USER_AGENT_GROUP'
                ) {
                  return {
                    value: item.processId,
                    label: `${item.accessNodeName}（${i18n.t('public_status_running')}：${
                      item.accessNodes?.filter((ii) => ii.status === 'running')
                        .length || 0
                    }）`,
                    accessNodeType: item.accessNodeType,
                    children: item.accessNodes?.map(mapNode) || [],
                  }
                }
                return mapNode(item)
              }) || []
          )
        },
        loadCommandList: async (filter, val) => {
          try {
            const { $values, command, where = {}, page, size } = filter
            const { pdkHash, id } = this.pdkOptions
            const { __TAPDATA, ...formValues } = $values
            const search = where.label?.like
            const getValues = Object.assign(
              {},
              this.model?.config || {},
              formValues,
            )
            let subscribeIds = []
            if (__TAPDATA.accessNodeProcessId) {
              subscribeIds = [`processId_${__TAPDATA.accessNodeProcessId}`]
            }
            const params = {
              pdkHash,
              connectionId: id || this.commandCallbackFunctionId,
              connectionConfig: isEmpty(formValues)
                ? this.model?.config || {}
                : getValues,
              subscribeIds,
              command,
              type: 'connection',
              action: search ? 'search' : 'list',
              argMap: {
                key: search,
                page,
                size: size || 1000,
              },
            }
            if (!params.pdkHash || !params.connectionId) {
              return { items: [], total: 0 }
            }
            const result = await proxyApi.command(params)
            if (!result.items) {
              return { items: [], total: 0 }
            }
            return result
          } catch (error) {
            console.log('catch', error) // eslint-disable-line
            return { items: [], total: 0 }
          }
        },
        getToken: async (field, params, $form) => {
          const filter = {
            subscribeId: `source#${this.model?.id || this.commandCallbackFunctionId}`,
            service: 'engine',
            expireSeconds: 100000000,
          }
          proxyApi.subscribe(filter).then((data) => {
            const isDaas = import.meta.env.VUE_APP_PLATFORM === 'DAAS'
            const p = location.origin + location.pathname
            let str = `${p}${isDaas ? '' : 'tm/'}api/proxy/callback/${data.token}`
            if (/^\/\w+/.test(data.token)) {
              str = `${p.replace(/\/$/, '')}${data.token}`
            }
            $form.setValuesIn(field.name, str)
          })
        },
        getCommandAndSetValue: async ($form, others = {}) => {
          const getState = $form.getState()
          const { pdkHash } = this.pdkOptions
          const { __TAPDATA, ...formValues } = getState?.values || {}
          const getValues = Object.assign(
            {},
            this.model?.config || {},
            formValues,
          )
          let subscribeIds = []
          if (__TAPDATA.accessNodeProcessId) {
            subscribeIds = [`processId_${__TAPDATA.accessNodeProcessId}`]
          }
          const params = {
            pdkHash,
            connectionId: this.model?.id || this.commandCallbackFunctionId,
            connectionConfig: isEmpty(formValues)
              ? this.model?.config || {}
              : getValues,
            ...others,
            subscribeIds,
            type: 'connection',
          }
          proxyApi.command(params).then((data) => {
            const setValue = data.setValue
            if (setValue) {
              for (const key in setValue) {
                $form.setValuesIn(key, setValue[key]?.data)
              }
            }
          })
        },
        async loadExternalStorage(id) {
          try {
            const filter = {
              where: {},
              limit: 1000,
              skip: 0,
            }
            if (id) {
              const ext = await externalStorageApi.get(id)
              filter.where.type = ext?.type
            }
            const { items = [] } = await externalStorageApi.list({
              filter: JSON.stringify(filter),
            })
            return items.map((item) => {
              return {
                type: item.type,
                label: item.name,
                value: item.id,
                isDefault: item.defaultStorage,
              }
            })
          } catch {
            return []
          }
        },
        goToAuthorized: async (params) => {
          // fromPath 记录进入编辑连接的来源路由，认证回来后设置返回的路由
          const routeQuery = { ...this.$route.query, fromPath: this.pathUrl }
          const routeParams = this.$route.params
          delete routeQuery.connectionConfig
          const routeUrl = this.$router.resolve({
            name: routeParams?.id ? 'connectionsEdit' : 'connectionCreate',
            query: routeQuery,
            params: routeParams,
          })

          const { __TAPDATA, ...__TAPDATA_CONFIG } =
            this.$refs.schemaToForm?.getFormValues?.() || {}
          params.oauthUrl = params?.oauthUrl.replaceAll(
            /@\{(\w+)\}@/g,
            function (val, sub) {
              return __TAPDATA_CONFIG[sub]
            },
          )
          const data = Object.assign({}, params, {
            url: location.origin + location.pathname + routeUrl.href,
            connectionConfig: {
              __TAPDATA,
              __TAPDATA_CONFIG,
            },
          })
          submitForm(params?.target, data)
        },
        shareCDCExternalStorageIdOnChange: (val, $form) => {
          $form.setFieldState(
            '__TAPDATA.shareCDCExternalStorageIdTips',
            (state) => {
              state.display =
                this.connectionLogCollectorTaskData.total &&
                val !== this.model.shareCDCExternalStorageId
                  ? 'visible'
                  : 'hidden'
            },
          )
        },
        getShareCDCExternalStorageIdDisabled: () => {
          return !!this.connectionLogCollectorTaskData.total
        },
        handleLogCollectorTaskDialog: async () => {
          this.connectionLogCollectorTaskDialog = true
        },
        handleJsDebug: (path = []) => {
          const properties = this.schemaData?.properties || {}
          const fieldObj = {}
          path.forEach((p) => {
            const { key, data } = this.getOptionByPath(properties, p)
            fieldObj[key] = data
          })
          this.jsDebugSchemaData = fieldObj
          this.showJsDebug = true
        },
        handleGetGenerateRefreshToken: ($index, $record, items, others) => {
          if (
            items
              .filter((t, i) => i !== $index)
              .some((t) => t.supplierKey === $record.supplierKey)
          ) {
            return this.$message.error(
              this.$t('packages_form_message_exists_name'),
            )
          }
          const params = Object.assign(
            {
              supplierKey: $record.supplierKey,
              randomId: $record.randomId,
              subscribeId: `source#${this.model?.id || this.commandCallbackFunctionId}`,
              service: 'engine',
            },
            others,
          )
          proxyApi.generateRefreshToken(params).then((data = {}) => {
            const isDaas = import.meta.env.VUE_APP_PLATFORM === 'DAAS'
            const p = location.origin + location.pathname
            let str = `${p}${isDaas ? '' : 'tm/'}${data.path}/${data.token}`
            if (/^\/\w+/.test(data.token)) {
              str = `${p.replace(/\/$/, '')}${data.token}`
            }
            $record.refreshURL = str
          })
        },
        getUid: () => {
          return uuid()
        },
        getHost: async () => {
          const data = await proxyApi.host()
          return data?.host
        },
      }
      this.schemaData = result
      this.loadingFrom = false
    },
    getConnectionIcon() {
      const { pdkHash } = this.$route.query || {}
      return getConnectionIcon(pdkHash)
    },

    getForm() {
      return this.schemaFormInstance
    },

    handleDebug() {
      this.showDebug = true
    },

    async setConnectionConfig() {
      const { connectionConfig, pdkHash } = this.$route.query || {}
      if (connectionConfig) {
        let subscribeIds = []
        const connectionConfigObj = JSON.parse(connectionConfig)
        const accessNodeProcessId =
          connectionConfigObj.__TAPDATA?.accessNodeProcessId
        if (accessNodeProcessId) {
          subscribeIds = [`processId_${accessNodeProcessId}`]
        }
        const params = {
          pdkHash,
          connectionConfig: connectionConfigObj,
          command: 'OAuth',
          type: 'connection',
          subscribeIds,
        }
        const res = await proxyApi.command(params)
        const {
          __TAPDATA,
          __TAPDATA_CONFIG = {},
          ...trace
        } = res || connectionConfigObj || {}
        Object.assign(
          this.model,
          __TAPDATA,
          {
            config: __TAPDATA_CONFIG,
          },
          trace,
        )
        this.schemaFormInstance.setValues({
          __TAPDATA,
          ...__TAPDATA_CONFIG,
          ...trace,
        })
      }
    },

    getOptionByPath(obj = {}, path) {
      const arr = path.split('.')
      const key = arr.shift()
      const data = obj[key] || {}
      if (arr.length) {
        return this.getOptionByPath(data.properties, arr.join('.'))
      }
      delete data.title
      return {
        key,
        data,
      }
    },
  },
}
</script>

<template>
  <div v-loading="loadingFrom" class="connection-from rounded-lg">
    <div class="connection-from-body gap-4">
      <main class="connection-from-main bg-white rounded-xl overflow-hidden">
        <div class="connection-from-title p-4">
          <div class="flex align-center gap-2">
            <slot name="title-prefix">
              <el-button
                text
                @click="
                  $router.push({
                    name: 'connectionsList',
                  })
                "
              >
                <template #icon>
                  <VIcon>left</VIcon>
                </template>
              </el-button>
            </slot>
            <span class="flex-1">{{
              $route.params.id
                ? $t('packages_business_connection_form_edit_connection')
                : $t('public_connection_button_create')
            }}</span>
            <div class="flex align-center overflow-hidden gap-2">
              <DatabaseIcon
                class="flex-shrink-0"
                :item="$route.query"
                :size="20"
              />
              <template v-if="!$route.params.id">
                <span
                  class="ml-auto font-color-light fw-normal fs-7 ellipsis"
                  >{{ pdkOptions.name }}</span
                >
                <el-button
                  v-if="!$route.params.id"
                  text
                  type="primary"
                  @click="dialogDatabaseTypeVisible = true"
                >
                  {{ $t('packages_business_connection_form_change') }}
                </el-button>
              </template>
              <template v-else>
                <span
                  class="ml-auto font-color-light fw-normal fs-7 ellipsis"
                  >{{ model.name }}</span
                >
                <el-button
                  text
                  type="primary"
                  @click="dialogEditNameVisible = true"
                >
                  {{ $t('packages_business_connection_form_rename') }}
                </el-button>
              </template>
            </div>
          </div>
        </div>

        <div class="form-wrap">
          <div class="form px-4">
            <div
              v-if="!isDaas && showAgentIpAlert"
              class="flex flex-column gap-2 mb-3 rounded-lg p-2 bg-color-primary-light-9"
            >
              <div class="flex align-items-start gap-1">
                <div class="p-1">
                  <VIcon class="color-primary" :size="22">info</VIcon>
                </div>
                <div class="lh-base p-1 fw-sub fs-7">
                  {{ $t('packages_business_agent_ip_tips_prefix') }}:
                </div>
              </div>

              <el-collapse
                value="1"
                class="rounded-lg overflow-hidden rounded-collapse"
              >
                <el-collapse-item title="TapData IP addresses" name="1">
                  <ul class="ml-6 font-color-dark">
                    <li>***********</li>
                    <li>*************</li>
                    <li>*************</li>
                  </ul>
                </el-collapse-item>
              </el-collapse>
            </div>

            <SchemaToForm
              ref="schemaToForm"
              class="pdk-schema-form"
              :schema="schemaData"
              :scope="schemaScope"
              layout="vertical"
              label-width="100%"
            />
            <span class="status">
              <span v-if="['invalid'].includes(status)" class="error">
                <VIcon>error</VIcon>
                <span>
                  {{ $t('public_status_invalid') }}
                </span>
              </span>
              <span v-if="['ready'].includes(status)" class="success">
                <el-icon><SuccessFilled /></el-icon>
                <span>
                  {{ $t('public_status_ready') }}
                </span>
              </span>
              <span v-if="['testing'].includes(status)" class="warning">
                <el-icon><el-icon-warning /></el-icon>
                <span>
                  {{ $t('public_status_testing') }}
                </span>
              </span>
            </span>
          </div>
        </div>
        <footer class="footer text-center border-top py-4">
          <el-button @click="goBack()">{{
            $t('public_button_back')
          }}</el-button>
          <el-button class="test" @click="startTest()">{{
            $t('public_connection_button_test')
          }}</el-button>
          <el-button
            v-if="['custom'].includes(pdkOptions.pdkId)"
            class="test"
            @click="handleDebug"
            >{{
              $t('packages_business_connections_databaseform_jiaobentiaoshi')
            }}
          </el-button>
          <el-button type="primary" :loading="submitBtnLoading" @click="submit">
            {{ $t('public_button_save') }}
          </el-button>
        </footer>
      </main>
      <div class="flex-1 overflow-x-hidden bg-white rounded-lg">
        <ConnectorDoc
          :pdk-hash="$route.query.pdkHash"
          :pdk-id="$route.query.pdkId"
        />
      </div>
    </div>
    <Test ref="test" :connection="model" @return-test-data="returnTestData" />
    <SceneDialog
      v-model:visible="dialogDatabaseTypeVisible"
      selector-type="source_and_target"
      @selected="handleDatabaseType"
    />
    <el-dialog
      v-model="dialogEditNameVisible"
      :title="$t('packages_business_connection_rename')"
      :close-on-click-modal="false"
      width="30%"
    >
      <el-form
        ref="renameForm"
        :model="renameData"
        :rules="renameRules"
        @submit.prevent
      >
        <el-form-item prop="rename">
          <el-input
            v-model="renameData.rename"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <span
          style="
            color: #ccc;
            margin-top: 5px;
            font-size: 12px;
            display: inline-block;
          "
          >{{
            $t('packages_business_connections_databaseform_zhongyingkaitouge')
          }}</span
        >
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelRename">{{
            $t('public_button_cancel')
          }}</el-button>
          <el-button
            type="primary"
            :loading="editBtnLoading"
            @click="submitEdit()"
            >{{ $t('public_button_confirm') }}</el-button
          >
        </span>
      </template>
    </el-dialog>
    <ConnectionDebug
      v-model:visible="showDebug"
      :schema="schemaData"
      :pdk-options="pdkOptions"
      :get-form="getForm"
    />
    <JsDebug
      v-model:visible="showJsDebug"
      :schema="jsDebugSchemaData"
      :pdk-options="pdkOptions"
      :get-form="getForm"
      :connection-id="connectionId"
    />
    <UsedTaskDialog
      v-model:value="connectionLogCollectorTaskDialog"
      :data="connectionLogCollectorTaskData"
    />
  </div>
</template>

<style lang="scss" scoped>
.connection-from {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  // background-color: #eff1f4;

  .alert-primary {
    background: #e8f3ff;
  }

  .connection-from-body {
    display: flex;
    flex: 1;
    //padding-left: 24px;
    //border-radius: 4px;
    overflow: hidden;
    //background-color: map.get($bgColor, white);
    .connection-from-main {
      display: flex;
      flex: 1;
      flex-direction: column;

      .connection-from-title {
        font-size: $fontSubtitle;
        font-weight: 500;
        color: map.get($fontColor, dark);
        line-height: 28px;
      }

      .connection-from-label {
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        .label:before {
          content: '*';
          color: #f56c6c;
          margin-right: 4px;
        }

        .label {
          width: 160px;
          font-size: $fontBaseTitle;
          color: map.get($fontColor, light);
          text-transform: capitalize;
        }

        .content-box {
          display: flex;
          max-width: 680px;
          line-height: 22px;
          font-size: $fontBaseTitle;
          font-weight: 400;
          color: map.get($fontColor, dark);
          align-items: center;
          white-space: nowrap;
          word-break: break-word;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .img-box {
          display: flex;
          width: 25px;
          height: 25px;
          justify-content: center;
          align-items: center;
          background: map.get($bgColor, white);
          border-radius: 3px;

          img {
            width: 100%;
          }
        }
      }

      .form-wrap {
        display: flex;
        flex: 1;
        overflow: hidden;

        .form {
          width: 100%;
          height: 100%;
          overflow-y: auto;
          //.scheme-to-form {
          //  width: 480px;
          //}
          .form-builder {
            width: 396px;

            :deep(.e-form-builder-item) {
              &.large-item {
                width: 610px;

                .el-form-item__content {
                  padding-right: 20px;
                }
              }

              &.small-item {
                width: 320px;
              }

              &.mongodb-item {
                width: 680px;
              }

              &.mongodb-tip-item .el-form-item__content {
                width: 680px;
              }

              .url-tip {
                font-size: 12px;
                color: map.get($fontColor, light);

                b {
                  font-size: 12px;
                  font-weight: 400;
                  color: map.get($fontColor, light);
                }
              }

              .fb-radio-group {
                .el-radio--mini.is-bordered {
                  padding-top: 0;
                }
              }

              .el-input .el-input__inner,
              .el-textarea__inner {
                background-color: rgba(239, 241, 244, 0.2);
              }

              .el-textarea__inner {
                min-height: 70px !important;
              }
            }

            :deep(.el-input-group__append button.el-button) {
              background-color: inherit;
              border-color: azure;
            }
          }
        }

        :deep(.formily-element-plus-form-item) {
          .el-input-number {
            width: 180px;
          }

          .el-input-number--small {
            width: 130px;
          }
        }
      }
    }

    .status {
      font-size: 12px;
      margin-top: 2px;

      .error {
        color: #f56c6c;
      }

      .success {
        color: #67c23a;
      }

      .warning {
        color: #e6a23c;
      }
    }
  }
}

.pdk-schema-form {
  :deep(.formily-element-plus-form-item-feedback-layout-loose) {
    margin-bottom: 20px;
  }

  :deep(.formily-element-plus-form-item-layout-vertical) {
    > .formily-element-plus-form-item-label {
      margin-bottom: 8px;

      .formily-element-plus-form-item-label-content {
        min-height: unset;
        height: unset;
      }

      .formily-element-plus-form-item-label-tooltip {
        margin-left: 4px;
        height: unset;
      }

      * {
        line-height: 22px;
      }
    }
  }

  :deep(.formily-element-plus-form-collapse) {
    &.border-bottom-0 {
      .el-collapse-item__header {
        border-bottom: none;
      }
    }

    .el-collapse-item__content {
      padding-bottom: 0;
    }
  }
}
.rounded-collapse {
  :deep(.el-collapse-item__header) {
    height: 38px;
    padding: 0 16px;
    gap: 8px;
    font-weight: 400;
    &.is-active {
      color: map.get($color, primary);
    }
  }
  :deep(.el-collapse-item__arrow) {
    order: -1;
    margin: 0;
  }
  :deep(.el-collapse-item__content) {
    padding: 0 16px 16px;
  }
}
</style>
