export default {
  packages_business_status_wait_start: '待启动',
  packages_business_status_starting: '启动中',
  packages_business_status_renewing: '重置中',
  packages_business_status_renew_failed: '重置失败',
  packages_business_milestone_list_status_waiting: '待执行',
  packages_business_milestone_list_status_running: '运行中',
  packages_business_milestone_list_status_paused: '已暂停',
  packages_business_milestone_list_status_progressing: '进行中',
  packages_business_milestone_list_status_cdc_progressing: '启动中...',
  packages_business_milestone_list_status_cdc_finish: '正在同步数据变更',
  packages_business_task_status_running: '已运行',
  packages_business_task_status_not_running: '未运行',
  packages_business_task_info_w: '周',
  packages_business_connection_form_data_source: '数据源',
  packages_business_connection_selector_desc1: '试用版暂不支持',
  packages_business_connection_selector_desc2: '更多数据源请使用正式版',
  packages_business_task_info_log_placeholder: '请输入日志内容',
  packages_business_task_info_no_more: '没有更多了',
  packages_business_customer_logs_to_solutions: '查看解决方案',
  packages_business_customer_logs_to_link: '查看数据库错误帮助页面',
  packages_business_customer_logs_no_more_data: '没有更多数据',
  packages_business_customer_logs_no_search_data: '无搜索结果',
  packages_business_customer_logs_copy_result: '已复制到剪贴板',
  packages_business_loading: '加载中',
  packages_business_schema_progress_status_error: '加载错误',
  packages_business_schema_progress_dialog_error_title: 'Schema加载出错',
  packages_business_schema_progress_load_time: '加载时间：{0}',
  packages_business_dataFlow_batchSortOperation: '设置标签',
  packages_business_dataFlow_dataLoading: '数据努力加载中...',
  packages_business_message_upload_success: '上传成功',
  packages_business_message_upload_fail: '上传失败',
  packages_business_message_upload_msg: '请上传需要导入的任务文件',
  packages_business_modules_dialog_import_title: '任务导入',
  packages_business_modules_dialog_condition: '条件',
  packages_business_modules_dialog_overwrite_data: '覆盖已有数据',
  packages_business_modules_dialog_skip_data: '跳过已有数据',
  packages_business_modules_dialog_group: '分组',
  packages_business_modules_dialog_file: '文件',
  packages_business_modules_dialog_upload_files: '上传文件',
  packages_business_connection_form_edit_connection: '编辑连接',
  packages_business_connection_form_data_source_type: '数据源类型',
  packages_business_connection_form_change: '更换',
  packages_business_connection_form_rename: '改名',
  packages_business_connection_form_database_owner_tip:
    '逗号分割的表达式列表，使用 * 代表任意长度任意字符',
  packages_business_connection_form_source_and_target_tip: `此数据连接在 ${
    import.meta.env.VUE_APP_PAGE_TITLE
  } 中能同时作为源和目标使用`,
  packages_business_connection_form_source_tip: `此数据连接在 ${import.meta.env.VUE_APP_PAGE_TITLE} 中只能作为源使用，不能作用为目标`,
  packages_business_connection_form_target_tip: `此数据连接在 ${import.meta.env.VUE_APP_PAGE_TITLE} 中只能作为目标使用，不能作用为源`,
  packages_business_connection_form_shared_mining: '使用共享挖掘',
  packages_business_connection_form_shared_mining_tip:
    '共享挖掘会挖掘增量日志，当有多个增量任务时不需要重复开启日志采集进程，能极大缓解源库资源的占用和浪费',
  packages_business_connection_form_access_node: 'Agent设置',
  packages_business_connection_form_automatic: '平台自动分配',
  packages_business_connection_form_manual: '用户手动指定',
  packages_business_connection_form_group: '按标签分配',
  packages_business_choose_agent: '选择Agent',
  packages_business_choose_agent_group: '选择Agent标签',
  packages_business_priorityProcessId: '标签内调度方式',
  packages_business_connection_form_access_node_tip:
    '自动情况下由平台分配节点进行连接访问，手动情况下由用户手动指定节点进行访问',
  packages_business_connection_form_give_up: '放弃',
  packages_business_share_form_setting_table_name: '存储MongoDB表名',
  packages_business_share_form_setting_log_time: '日志保存时长',
  packages_business_message_saveFail: '保存失败',
  packages_business_connection_rename: '改名',
  packages_business_dataForm_saveFail: '保存失败',
  packages_business_dataForm_error_connectionNameExist: '连接名称已存在',
  packages_business_connection_list_form_database_type: '数据库类型',
  packages_business_connection_list_name: '连接名',
  packages_business_connection_list_status: '状态',
  packages_business_connection_list_desc:
    '数据源包括数据库、结构化文件、应用程序RESTful API、自定义接口等类型，必须先创建数据源才能创建迁移或同步任务。更多配置说明，请点击',
  packages_business_connection_list_help_doc: '帮助文档',
  packages_business_connection_dataBaseStatus: '状态',
  packages_business_connection_deteleDatabaseMsg:
    '删除连接 xxx 后，此连接将无法恢复',
  packages_business_connection_checkMsg:
    '此数据源被传输任务或API所占用，无法删除',
  packages_business_connection_copyFailedMsg:
    '复制失败，原因：系统设置中 "连接设置 - 允许创建重复数据源" 被设置为 "false"',
  packages_business_text_open: '开启',
  packages_business_connection_form_oracle_redoLog_parser: '裸日志',
  packages_business_connection_preview_no_sure: '不确认',
  packages_business_connection_preview_master_partition: '仅写入master分区',
  packages_business_connection_preview_isr_partition: '写入所有ISR分区',
  packages_business_message_cancel: '取 消',
  packages_business_message_confirm: '确 定',
  packages_business_connection_reloadTittle: '重新加载 schema',
  packages_business_connection_reloadMsg:
    '如果此库的schema过多，可能耗时较长，确定要刷新数据源的schema ',
  packages_business_dataForm_primaryTest: '正在启动连接检测服务，请稍等...',
  packages_business_dataForm_testing: '正在测试中，请稍等...',
  packages_business_dataForm_test_testResultFail: '连接测试失败',
  packages_business_dataForm_test_testResultSuccess: '连接测试成功',
  packages_business_dataForm_test_success: '测试成功',
  packages_business_dataForm_test_fail: '测试失败',
  packages_business_dataForm_test_testing: '未测试',
  packages_business_dataForm_test_items: '测试事项',
  packages_business_dataForm_test_result: '状态',
  packages_business_dataForm_test_information: '说明',
  packages_business_dataForm_test_unTest: '待测试 ... ',
  packages_business_dataForm_test_error: '测试服务请求超时，请关闭重试',
  packages_business_dataForm_test_retryBtn: '重试',
  packages_business_dataForm_test_retryTest: '连接测试服务启动失败，请点击重试',
  packages_business_message_update_success: '修改成功',
  packages_business_task_preview_subtasks: '子任务',
  packages_business_task_monitor_sync_type: '同步类型',
  packages_business_task_monitor_run_connection: '连接',
  packages_business_task_monitor_history_run_record: '历史运行记录',
  packages_business_task_details_sub_task: '子任务',
  packages_business_dataFlow_importantReminder: '重要提醒',
  packages_business_dataFlow_modifyEditText: '编辑任务如果修改了',
  packages_business_dataFlow_nodeLayoutProcess: '节点排版流程',
  packages_business_dataFlow_nodeAttributes: '节点属性',
  packages_business_dataFlow_matchingRelationship: '匹配关系',
  packages_business_dataFlow_afterSubmission: '提交后必须',
  packages_business_dataFlow_runNomally: '才能正常运行',
  packages_business_dataFlow_editLayerTip:
    ' 否则可能导致异常错误，请问您要继续编辑吗?',
  packages_business_dataFlow_continueEditing: '继续编辑',
  packages_business_task_monitor_progress: '任务进度',
  packages_business_task_monitor_run_log: '运行日志',
  packages_business_task_monitor_mining_task: '挖掘任务',
  packages_business_dataFlow_inputOutput: '输入输出统计',
  packages_business_dataFlow_dataScreening: '事件统计',
  packages_business_dataFlow_throughputpop:
    '输入输出统计: 平均每秒源端数据采集的速度以及目标端写入的速度，数值越大越好',
  packages_business_task_monitor_full_completion_time: '预计全量完成还需时间',
  packages_business_task_monitor_total_insert: '总插入',
  packages_business_task_monitor_total_update: '总更新',
  packages_business_task_monitor_total_delete: '总删除',
  packages_business_task_info_start_time: '开始时间',
  packages_business_task_info_node: '节点',
  packages_business_task_info_frequency: '频率',
  packages_business_task_info_select_node: '请选择节点',
  packages_business_task_info_select_period: '请选择周期',
  packages_business_task_info_select_frequency: '请选择频率',
  packages_business_task_info_fifteen_min: '最近十五分钟',
  packages_business_task_info_five_seconds: '5秒',
  packages_business_task_info_one_min: '1分钟',
  packages_business_task_info_full_progress: '全量进度',
  packages_business_task_info_calculating: '计算中',
  packages_business_task_info_increment_time_point: '增量所处时间点',
  packages_business_migrate_no_progress_statistics_yet: '暂无进度统计信息',
  packages_business_migrate_no_latency_statistics_yet: '暂无延迟统计信息',
  packages_business_task_monitor_full_sync: '全量同步概览',
  packages_business_task_info_table_number: '计划全量同步数据量',
  packages_business_task_info_completed: '已完成全量同步数据量',
  packages_business_task_info_fully_completed: '全量已完成',
  packages_business_task_info_overView_status: '计算中',
  packages_business_button_clear: '清除',
  packages_business_button_rollback: '回溯',
  packages_business_task_monitor_status: '状态',
  packages_business_task_info_synced: '已同步',
  packages_business_task_info_task_init: '任务初始化',
  packages_business_task_info_task_structure: '结构迁移',
  packages_business_task_info_task_cdc: '增量同步',
  packages_business_task_info_srcName: '源数据源名称',
  packages_business_task_info_srcTableName: '源表名称',
  packages_business_task_info_tgtName: '目标数据源名称',
  packages_business_task_info_tgtTableName: '目标表名称',
  packages_business_task_info_cdc_delay: '延迟(ms)',
  packages_business_task_info_cdc_time: '增量所处时间点',
  packages_business_task_info_source_table: '源数据表',
  packages_business_task_info_source_database: '源数据库',
  packages_business_task_info_data_row: '数据量（行）',
  packages_business_task_info_target_table: '目标数据表',
  packages_business_task_info_amount_sync_data: '已完成同步数据量（行）',
  packages_business_task_info_schedule: '进度',
  packages_business_task_info_table_name: '表名称',
  packages_business_task_info_overView_error_msg: '计算出错',
  packages_business_share_task_table_name: '挖掘任务名称',
  packages_business_share_task_table_time: '挖掘所处时间',
  packages_business_share_task_table_status: '挖掘所处状态',
  packages_business_connection_list_schema_load_progress: 'Schema加载进度',
  packages_business_connection_list_test_failed: '测试连接失败',
  packages_business_task_info_connection_test: '测试',
  packages_business_task_start_task: '启动任务',
  packages_business_task_stop_task: '停止任务',
  packages_business_task_info_forced_stop_task: '强制停止任务',
  packages_business_task_info_running_time: '运行时间',
  packages_business_task_info_operator: '操作者',
  packages_business_task_info_operator_content: '操作内容',
  packages_business_task_info_data_screening: '事件统计',
  packages_business_task_info_input_output: '输入输出统计',
  packages_business_task_info_throughputpop:
    '输入输出统计: 平均每秒源端数据采集的速度以及目标端写入的速度，数值越大越好',
  packages_business_task_monitor_time: '时间',
  packages_business_task_monitor_mission_milestone: '任务里程碑',
  packages_business_task_monitor_no_milestone_data:
    '此任务尚未启动或已被重置，暂无运行里程碑数据',
  packages_business_task_info_milestone: '里程碑',
  packages_business_milestone_btn_check_error: '查看错误原因',
  packages_business_task_monitor_mining_task_name: '挖掘任务名称',
  packages_business_task_monitor_mining_task_point_time: '挖掘所处时间点',
  packages_business_task_monitor_mining_task_status: '挖掘状态',
  packages_business_button_bulk_import: '导入',
  packages_business_message_save_fail: '保存失败',
  packages_business_task_list_transform_running: '推演中',
  packages_business_task_list_transform_done: '推演完成',
  packages_business_task_list_transform_error: '推演失败',
  packages_business_task_list_edit: '编辑',
  packages_business_task_list_export: '导出',
  packages_business_task_list_sync_type: '同步类型',
  packages_business_task_list_status_all: '全部状态',
  packages_business_task_list_button_monitor: '监控',
  packages_business_task_preview_title: '数据库迁移详情',
  packages_business_task_preview_createUser: '创建人',
  packages_business_task_preview_sync_type: '任务同步',
  packages_business_task_preview_type: '任务同步',
  packages_business_task_preview_id: '任务ID',
  packages_business_task_preview_createAt: '创建时间',
  packages_business_task_preview_createTime: '创建时间',
  packages_business_task_preview_startTime: '启动时间',
  packages_business_task_preview_initStartTime: '全量开始时间',
  packages_business_task_preview_cdcStartTime: '增量开始时间',
  packages_business_task_preview_taskFinishTime: '任务完成时间',
  packages_business_task_preview_taskLastHour: '任务总时长',
  packages_business_task_preview_eventTime: '增量所处时间点',
  packages_business_task_preview_cdcDelayTime: '增量最大滞后时间',
  packages_business_task_preview_failCount: '失败总次数',
  packages_business_message_resetOk: '重置成功',
  packages_business_dataFlow_multiError_notFound: '此任务不存在',
  packages_business_dataFlow_multiError_statusError: '任务状态不允许这种操作',
  packages_business_dataFlow_multiError_otherError: '操作失败, 请重试',
  packages_business_dataFlow_batchRest: '批量重置',
  packages_business_dataFlow_batchDelete: '批量删除',
  packages_business_dataFlow_bulkExport: '批量导出',
  packages_business_dataFlow_bulkScheuled: '批量启动',
  packages_business_dataFlow_bulkStopping: '批量停止',
  packages_business_dataFlow_taskBulkOperation: '任务操作',
  packages_business_dataFlow_addTag: '添加标签',
  packages_business_dataVerify_dataVerify: '数据校验',
  packages_business_dataFlow_selectAll: '全选',
  packages_business_dataFlow_skipError_title: '跳过错误设置',
  packages_business_dataFlow_skipError_tip:
    '任务上次停止时发生了以下数据相关的错误，请确认这些错误已经被处理。如果希望跳过这些错误，请勾选相应的错误项并点击"跳过错误，启动任务" 。',
  packages_business_dataFlow_skipError_attention:
    '注意：若导致错误的数据未被处理，跳过错误可能导致这条数据被丢弃。',
  packages_business_dataFlow_skipError_startJob: '跳过错误，启动任务',
  packages_business_dataFlow_skipError_taskName: '任务名',
  packages_business_dataFlow_skipError_errorTotal: '共 XX 条,已选择',
  packages_business_dataFlow_skipError_strip: '条',
  packages_business_page_title_task_stat: '任务统计',
  packages_business_task_info_subtasks_name: '子任务名称',
  packages_business_task_info_subtasks_status: '状态',
  packages_business_dataFlow_view: '查看',
  packages_business_dataFlow_copy: '复制',
  packages_business_dataFlow_button_reset: '重置',
  packages_business_connection_type_source: '源头',
  packages_business_connection_type_target: '目标',
  packages_business_connection_type_source_and_target: '源头和目标',
  packages_business_task_preview_status_error: '错误',
  packages_business_task_preview_status_edit: '编辑中',
  packages_business_task_preview_status_wait_run: '启动中',
  packages_business_task_preview_status_complete: '已完成',
  packages_business_task_preview_status_running: '运行中',
  packages_business_task_preview_status_stop: '已停止',
  packages_business_task_preview_status_stopping: '停止中',
  packages_business_task_preview_status_preparing: '准备中',
  packages_business_task_preview_status_scheduling: '启动中',
  packages_business_task_preview_status_schedule_failed: '调度失败',
  packages_business_task_preview_status_ready: '待启动',
  packages_business_task_info_status_waiting: '待运行',
  packages_business_task_info_status_running: '同步中',
  packages_business_task_info_status_done: '已完成',
  packages_business_task_info_status_paused: '已暂停',
  packages_business_logs_detailed_sousuowushuju: '搜索无数据',
  packages_business_logs_index_xiangxi: '详细',
  packages_business_logs_index_putong: '普通',
  packages_business_shared_task_yijingzhiweie: '已经置为[error]',
  packages_business_shared_task_weishibiederen: '未识别的任务状态：{val1}',
  packages_business_connections_databaseform_cicaozuohuidiu:
    '此操作会丢失当前正在创建的连接',
  packages_business_connections_databaseform_mingchengguizezhong:
    '名称规则：中英开头，1～100个字符，可包含中英文、数字、中划线、下划线、空格',
  packages_business_connections_databaseform_zhongyingkaitouge:
    '中英开头，1～100个字符，可包含中英文、数字、中划线、下划线、空格',
  packages_business_connections_list_renwuzongshu: '任务总数: ',
  packages_business_connections_list_gailianjieyibei:
    '该连接已被 {val1} 个任务调用，请删除任务或修改配置后重试',
  packages_business_connections_preview_schem: 'schema加载完成',
  packages_business_etl_details_caozuoshibaiqing: '操作失败，请重试',
  packages_business_etl_details_shifouzhongzhigai: '是否重置该任务？',
  packages_business_etl_details_zhongzhirenwux:
    '重置任务 xxx 将清除任务同步进度，任务将重新执行',
  packages_business_etl_details_qiangzhitingzhiren:
    '强制停止任务 xxx 将立即中断数据传输强制任务快速停止，并重置该任务',
  packages_business_etl_details_shifouqiangzhiting: '是否强制停止该任务？',
  packages_business_etl_details_zantingrenwux:
    '暂停任务 xxx 后，任务中未完成全量同步的表再次启动时，会重新执行全量同步',
  packages_business_dataFlow_agent_force_stop_confirm_message:
    '强制停止任务 xxx ，由于Agent已离线，我们只会重置该任务的状态，但是无法停止此任务的运行，请您确保已在本地手动停止或删除了该Agent，或等待该Agent连接上后再停止该任务。',
  packages_business_etl_details_shifouzantinggai: '是否暂停该任务？',
  packages_business_etl_details_shanchurenwux:
    '删除任务 xxx 后，此任务将无法恢复',
  packages_business_etl_details_shifoushanchugai: '是否删除该任务？',
  packages_business_etl_details_renwuXxx:
    '任务XXX中含有聚合处理节点，任务停止后再次启动，任务会先进行重置，确定停止？',
  packages_business_etl_details_chushihualeixing:
    '初始化类型的任务暂停后如果再次启动，任务会从头开始同步，确定暂停?',
  packages_business_etl_details_miaoshuneirong: '描述内容',
  packages_business_statistics_index_tongburenwu: '同步任务',
  packages_business_statistics_index_qianyirenwu: '迁移任务',
  packages_business_statistics_schedule_cike: '此刻',
  packages_business_statistics_schedule_shujukushiqu: '数据库时区',
  packages_business_statistics_schedule_yonghuliulanqi: '用户浏览器时区',
  packages_business_statistics_schedule_shijian: '时间：',
  packages_business_statistics_schedule_leixing: '类型：',
  packages_business_dataFlow_delete_confirm_message:
    '删除任务 xxx 后，此任务将无法恢复',
  packages_business_dataFlow_bulk_delete_confirm_title: '是否批量删除任务？',
  packages_business_dataFlow_bulk_delete_confirm_message:
    '批量删除任务后，任务将无法恢复',
  packages_business_dataFlow_stop_confirm_title: '是否暂停该任务？',
  packages_business_dataFlow_stop_confirm_message:
    '暂停任务 xxx 后，任务中未完成全量同步的表再次启动时，会重新执行全量同步',
  packages_business_dataFlow_bulk_stop_confirm_title: '是否批量暂停任务？',
  packages_business_dataFlow_bulk_stop_confirm_message:
    '批量暂停任务后，任务中未完成全量同步的表再次启动时，会重新执行全量同步',
  packages_business_dataFlow_force_stop_confirm_title: '是否强制停止该任务？',
  packages_business_dataFlow_force_stop_confirm_message:
    '强制停止任务 xxx 将立即中断数据传输强制任务快速停止，并重置该任务',
  packages_business_dataFlow_bulk_force_stop_confirm_title:
    '是否批量强制停止任务？',
  packages_business_dataFlow_bulk_force_stop_confirm_message:
    '批量强制停止任务将立即中断数据传输强制任务快速停止，并重置该任务',
  packages_business_dataFlow_initialize_confirm_title: '是否重置该任务？',
  packages_business_dataFlow_initialize_confirm_message:
    '重置任务 xxx 将清除任务同步进度，任务将重新执行',
  packages_business_dataFlow_bulk_initialize_confirm_title:
    '是否批量重置任务？',
  packages_business_dataFlow_bulk_initialize_confirm_message:
    '批量重置任务将清除任务同步进度，任务将重新执行',
  packages_business_connections_databaseform_zidingyi: '自定义',
  packages_business_connections_databaseform_duixiangshouji: '对象收集',
  packages_business_verification_details_yichangshuju: '异常数据',
  packages_business_verification_details_mubiaobiaoming: '目标表名',
  packages_business_verification_details_yuanbiaoming: '源表名',
  packages_business_verification_details_gongxijiaoyanjie:
    '恭喜~校验结果源表与目标表内容完全一致，没有错误记录',
  packages_business_verification_details_mubiaobiaoziduan: '目标表字段：值',
  packages_business_verification_details_yuanbiaoziduanzhi: '源表字段：值',
  packages_business_verification_details_xianshiwanzhengzi: '显示完整字段',
  packages_business_verification_details_jinxianshichayi: '仅显示差异字段',
  packages_business_verification_details_yichangshujuhang: '异常数据（行）：',
  packages_business_verification_details_mubiaobiao: '目标表：',
  packages_business_verification_details_yuanbiao: '源表：',
  packages_business_verification_details_jiaoyanjieguo: '校验结果',
  packages_business_verification_details_jiaoyanzhong: '校验中',
  packages_business_verification_details_jiaoyan: '校验',
  packages_business_verification_details_qingshurubiaoming: '请输入表名…',
  packages_business_shared_const_gaojingzhong: '告警中',
  packages_business_shared_const_yihuifu: '已恢复',
  packages_business_components_alert_yiguanbi: '已关闭',
  packages_business_components_alert_huifu: '恢复',
  packages_business_shared_const_yiban: '一般',
  packages_business_shared_const_jinggao: '警告',
  packages_business_shared_const_yanzhong: '严重',
  packages_business_shared_const_jinji: '紧急',
  packages_business_external_storage: '外存配置',
  packages_business_relation_details_chakanrenwu: '查看任务',
  packages_business_relation_details_shiyonggaiguanlian:
    '使用该{val}的任务清单',
  packages_business_relation_list_jiaoyanrenwu: '校验任务',
  packages_business_relation_list_huancunrenwu: '缓存任务',
  packages_business_relation_list_qingshururenwu: '请输入任务名称',
  packages_business_relation_details_huancun: '缓存',
  packages_business_relation_details_wajue: '挖掘',
  packages_business_relation_details_renwu: '任务',
  packages_business_agent_select_placeholder: '请选择agent',
  packages_business_agent_select_not_found: '该agent已不存在，请选择其他agent',
  packages_business_agent_select_not_found_for_rocksdb:
    '选用RocksDB作为共享挖掘外存时，需要手动指定一个Agent',
  packages_business_components_connectiontypeselectorsort_wodeshujuyuan:
    '我的数据源',
  packages_business_components_connectiontypeselectorsort_jiaoyouTap: `交由 ${import.meta.env.VUE_APP_PAGE_TITLE} 进行全面的质量测试，以保证插件的稳定性和质量`,
  packages_business_components_connectiontypeselectorsort_zhuyizhelishi:
    '注意：这里是您自己上传的数据源插件，如果要用于生产任务，请在GitHub上提交源代码',
  packages_business_components_connectiontypeselectorsort_zhuyiBet: `注意：Beta 数据源尚未通过 ${import.meta.env.VUE_APP_PAGE_TITLE} 的认证测试流程，${import.meta.env.VUE_APP_PAGE_TITLE} 暂不保证这些数据源的稳定运行`,
  packages_business_components_connectiontypeselectorsort_shiyongbanzanbu:
    '敬请期待以下数据源开放',
  packages_business_components_connectiontypeselectorsort_betashu: 'Beta数据源',
  packages_business_components_connectiontypeselectorsort_renzhengshujuyuan:
    'GA数据源',
  packages_business_components_connectiontypeselectorsort_jijiangshangxian:
    'Alpha数据源',
  packages_business_connections_list_lianjiefenlei: '连接分类',
  packages_business_task_migratelist_renwufenlei: '任务分类',
  packages_business_task_list_renwubuzhichi: '任务不支持该操作',
  packages_business_connections_databaseform_keyicongbaohan:
    '可以从包含表规则匹配到的表中将指定的表排除',
  packages_business_connections_databaseform_paichubiao: '排除表',
  packages_business_connections_databaseform_baohanbiao: '包含表',
  packages_business_connections_list_wenjianleixingde:
    '文件类型的连接暂不支持加载Schema',
  // 数据校验
  packages_business_verification_task_name: '校验任务名',
  packages_business_verification_type: '校验类型',
  packages_business_verification_check_frequency: '校验频率',
  packages_business_verification_single_repeating_verify: '单次/重复校验',
  packages_business_verification_is_enabled: '是否启用',
  packages_business_verification_single: '单次校验',
  packages_business_verification_repeating: '重复校验',
  packages_business_verification_row_verify: '快速count校验',
  packages_business_verification_content_verify: '表全字段值校验',
  packages_business_verification_joint_verify: '关联字段值校验',
  packages_business_verification_hash_verify: '全表 hash 校验',
  packages_business_verification_job_enable: '已启用',
  packages_business_verification_job_disable: '已禁止',
  packages_business_verification_check_same: '校验一致',
  packages_business_verification_count_difference: 'Count不一致',
  packages_business_verification_content_difference: '内容差异',
  packages_business_verification_executeVerifyTip: '执行',
  packages_business_verification_addVerifyTip: '新建校验任务',
  packages_business_verification_historyTip: '历史',
  packages_business_verification_configurationTip: '配置',
  packages_business_verification_details_title: '任务校验详情',
  packages_business_verification_history_title: '任务校验历史',
  packages_business_verification_diff_history_title: '差异校验历史',
  packages_business_verification_diff_details_title: '差异校验详情',
  packages_business_verification_result_title: '校验结果',
  packages_business_verification_button_diff_verify: '差异校验',
  packages_business_verification_button_diff_verify_running: '校验中',
  packages_business_verification_button_diff_verify_tips:
    '对本次全量校验的差异数据结果进行再次校验，行数差异暂不支持差异校验',
  packages_business_verification_last_start_time: '最后校验时间',
  packages_business_verification_button_diff_task_history: '校验历史',
  packages_business_verification_message_old_data_not_support:
    '旧数据暂不支持二次校验',
  packages_business_verification_message_out_of_limit:
    '您的差异数据量已超出任务支持的最大错误数据保存条数，暂时无法进行二次校验',
  packages_business_verification_result_count_more: '目标count多: {0}',
  packages_business_verification_result_count_less: '目标count少: {0}',
  packages_business_verification_result_content_diff: '表数据差: {0}',
  packages_business_verification_result_count_inconsistent: '不一致',
  packages_business_verification_result_count_consistent: '一致',
  packages_business_verification_result_field_name: '字段名',
  packages_business_verification_result_source_info: '源信息',
  packages_business_verification_result_target_info: '目标信息',
  packages_business_verification_create_window_duration: '窗口时长',
  packages_business_verification_form_source_filter: '源表数据过滤',
  packages_business_verification_form_target_filter: '目标表数据过滤',
  packages_business_verification_checking: '校验中...',
  packages_business_verification_message_error_joint_table_not_set:
    '请添加校验条件',
  packages_business_verification_message_error_joint_table_target_or_source_not_set:
    '校验条件{val}中源表或目标表未选择',
  packages_business_verification_message_error_joint_table_target_or_source_filter_not_set:
    '校验条件{val}中源表或目标表数据过滤未选择',
  packages_business_verification_message_error_joint_table_field_not_match:
    '校验条件{val}中源表与目标表的索引字段个数不相等',
  packages_business_verification_message_error_script_no_enter:
    '开启高级校验后，JS校验逻辑不能为空',
  packages_business_verification_message_confirm_delete_script:
    '确定要删除自定义JS校验逻辑吗',
  packages_business_verification_message_confirm_back:
    '此操作会丢失当前正在创建（编辑）的校验任务',
  packages_business_verification_message_title_confirm_back:
    '是否放弃创建（编辑）校验任务？',
  packages_business_taskprogress_plan_sync_table_num: '计划同步表数量',
  packages_business_taskprogress_completed_sync_table_num: '已完成同步表数量',
  packages_business_taskprogress_plan_sync_data: '计划同步数据量（行）',
  packages_business_taskprogress_completed_sync_data: '已完成同步数据量（行）',
  packages_business_taskprogress_current_sync: '各库当前同步情况',
  packages_business_taskprogress_full_sync_progress: '全量同步进度',
  packages_business_verification_verifyDetail: '校验详情',
  packages_business_verification_sourceTable: '源表',
  packages_business_verification_targetTable: '目标表',
  packages_business_verification_sourceRows: '源表校验行数',
  packages_business_verification_rowConsistent: 'Count差',
  packages_business_verification_contConsistent: '表数据差',
  packages_business_verification_verifyHistory: '校验历史',
  packages_business_verification_tableDetail: '表明细',
  packages_business_verification_configuration: '查看配置',
  packages_business_verification_verifyName: '校验任务',
  packages_business_verification_sourceTotalRows: '本次校验行数',
  packages_business_verification_targetTotalRows: '目标行数',
  packages_business_verification_verifyStatus: '校验状态',
  packages_business_verification_verifystatus: '校验状态',
  packages_business_verification_verifyTime: '校验时间',
  packages_business_verification_completeTime: '完成时间',
  packages_business_verification_rowVerify: '快速count校验',
  packages_business_verification_contentVerify: '表全字段值校验',
  packages_business_verification_jointVerify: '关联字段值校验',
  packages_business_verification_singleVerify: '单次校验',
  packages_business_verification_repeatingVerify: '重复校验',
  packages_business_verification_inconsistent: '不一致',
  packages_business_verification_consistent: '一致',
  packages_business_verification_toBeVerified: '待校验',
  packages_business_verification_verifying: '校验中',
  packages_business_verification_verifyFinished: '校验完成',
  packages_business_verification_verifyJobExpired: '校验任务结束',
  packages_business_verification_executeVerifyInstantly: '执行校验',
  packages_business_verification_deleteVerifyJob: '删除',
  packages_business_verification_verifySetting: '校验设置',
  packages_business_verification_batchVerify: '批量校验',
  packages_business_verification_verifyType: '校验类型',
  packages_business_verification_verifytype: '校验类型',
  packages_business_verification_singleRepeatingVerify: '单次/重复校验',
  packages_business_verification_rowAndContConsistent: '行数和内容差异',
  packages_business_verification_sourceFieldName: '源表字段名',
  packages_business_verification_targetFieldName: '目标字段名',
  packages_business_verification_Value: '值',
  packages_business_verification_inconsistentType: '差异类型',
  packages_business_verification_chooseJob: '选择任务',
  packages_business_verification_frequency: '校验频次',
  packages_business_verification_startTime: '开始时间',
  packages_business_verification_LastTime: '结束时间',
  packages_business_verification_startAndStopTime: '起止时间',
  packages_business_verification_verifyInterval: '校验间隔',
  packages_business_verification_inconsistentCount: '错误数据保存条数',
  packages_business_verification_table: '待校验表',
  packages_business_verification_addTable: '添加表',
  packages_business_verification_automaticallyAdd: '自动添加',
  packages_business_verification_enable: '已启用',
  packages_business_verification_disable: '已禁止',
  packages_business_verification_isEnabled: '是否启用',
  packages_business_verification_newVerify: '新建校验',
  packages_business_verification_edit: '编辑校验',
  packages_business_verification_advanceVerify: '高级校验',
  packages_business_verification_JSVerifyLogic: 'JS校验逻辑',
  packages_business_verification_addJS: '添加逻辑',
  packages_business_verification_returnMsg: '返回的message',
  packages_business_verification_returnedData: '返回的data',
  packages_business_verification_sourceTableData: '源表数据',
  packages_business_verification_success:
    '校验结果显示源表与目标表内容完全一致。',
  packages_business_verification_clickVerified: '点下方按钮添加校验表',
  packages_business_verification_ChoosePKField: '请选索引或主键字段',
  packages_business_verification_indexField: '关联字段',
  packages_business_verification_BasicSettings: '基本设置',
  packages_business_verification_verifyCondition: '校验表配置',
  packages_business_verification_clear: '清空',
  packages_business_verification_fastCountTip:
    '快速count仅对源表和目标表的行数进行count校验，速度极快，但是不会展示差异的具体字段内容。',
  packages_business_verification_contentVerifyTip:
    '表全字段值校验会对源表和目标表的全部字段进行逐行校验，能查出所有字段的差异，但是速度慢。此操作同时会对源和目标库发起查询，可能会对数据库造成读取压力。',
  packages_business_verification_jointFieldTip:
    '关联字段值校验只对源表和目标表的关联字段的值进行比对校验，速度快于全表字段值校验模式。此操作同时会对源和目标库发起查询，可能会对数据库造成读取压力。',
  packages_business_verification_waiting: '待校验',
  packages_business_verification_scheduling: '校验启动中',
  packages_business_verification_error: '校验失败',
  packages_business_verification_done: '校验结束',
  packages_business_verification_running: '校验中',
  packages_business_verification_verifyProgress: '校验进度',
  packages_business_verification_tasksTime: '请选择起止时间',
  packages_business_verification_tasksDataFlow: '请选择任务',
  packages_business_verification_tasksJobName: '请输入校验任务名称',
  packages_business_verification_tasksVerifyCondition: '请添加校验条件',
  packages_business_verification_tasksVerifyInterval: '请输入校验间隔',
  packages_business_verification_lackSource: '校验条件中源表或目标表未选择',
  packages_business_verification_lackIndex:
    '校验条件{val}中源表或目标表的索引字段未选择',
  packages_business_verification_tasksAmount:
    '校验条件中源表与目标表的索引字段个数不相等',
  packages_business_verification_uniqueField: '唯一字段差异',
  packages_business_verification_otherField: '其他字段差异',
  packages_business_verification_back: '返回',
  packages_business_verification_startVerify: '正在执行校验',
  packages_business_verification_deleteMessage:
    '删除校验任务将无法恢复, 确定删除',
  packages_business_verification_checkStatusPre: '此任务处于 ',
  packages_business_verification_checkStatusSuffix: '状态，无法配置校验设置',
  packages_business_verification_backConfirmMessage:
    '此操作会丢失当前正在创建（编辑）的校验任务',
  packages_business_verification_backConfirmTitle:
    '是否放弃创建（编辑）校验任务？',
  packages_business_verification_history_source_total_rows: '源总行数',
  packages_business_verification_form_label_error_save_count: '错误保存条数',
  packages_business_verification_button_auto_add_table: '自动添加表',
  packages_business_components_conditionbox_suoxuanrenwuque:
    '所选任务缺少节点连线信息',
  packages_business_components_conditionbox_cunzaichulijiedian_wufazidong:
    '存在处理节点，无法自动添加表',
  packages_business_components_conditionbox_shifouqingkongsuo:
    '是否清空所有条件',
  packages_business_components_conditionbox_mubiaobiao: ' 目标表',
  packages_business_components_conditionbox_laiyuanbiao: '来源表',
  packages_business_components_conditionbox_daijiaoyanlianjie: '待校验连接',
  packages_business_components_conditionbox_jianyantiaojian: '校验表配置',
  packages_business_components_conditionbox_zhankaibianji: '展开编辑',
  packages_business_components_fieldbox_tianjiahang: '添加行',
  packages_business_components_fieldbox_ziduan: '字段',
  packages_business_components_fieldbox_qingshuruziduan: '请输入字段名',
  packages_business_components_fieldbox_quanziduan: '全字段',
  packages_business_components_fieldbox_daijiaoyanmoxing: '待校验模型',
  packages_business_verification_details_dongtaijiaoyan: '动态校验',
  packages_business_verification_details_zhankai: '展开',
  packages_business_verification_details_shouqi: '收起',
  packages_business_verification_form_diinde:
    '校验条件{val1}，待校验模型不能为空',
  packages_business_verification_form_zhaobudaojiedian:
    '找不到节点对应的表信息',
  packages_business_verification_form_qingshurukaishi: '请输入开始时间',
  packages_business_verification_form_jiaoyanjieshushi: '校验结束时间',
  packages_business_verification_form_jiaoyankaishishi: '校验开始时间',
  packages_business_verification_form_zhishuchulaiyuan:
    '只输出来源表不一致的数据',
  packages_business_verification_form_shuchusuoyoubu: '输出所有不一致的数据',
  packages_business_verification_form_jieguoshuchu: '结果输出',
  packages_business_verification_form_zhidingrenyibiao:
    '指定任意表进行数据校验',
  packages_business_verification_form_weitedingdeP: '按照任务进行数据校验',
  packages_business_verification_form_jiaoyanrenwumo: '校验任务模式',
  packages_business_task_status_agent_tooltip_time:
    '距上次状态上报时间已经{time}',
  packages_business_task_status_agent_tooltip_agent: '任务所在的引擎为',
  packages_business_task_status_retrying_tooltip:
    '当前任务正在重试中，重试开始时间：{val}',
  packages_business_select_placeholder: '请添加或选择',
  packages_business_verification_form_youjiantongzhi: '邮件通知',
  packages_business_verification_form_xitongtongzhi: '系统通知',
  packages_business_verification_form_jiaoyanjieguobu: '校验结果不一致告警',
  packages_business_verification_form_jianyanrenwuyun: '检验任务运行出错警告',
  packages_business_verification_form_jiaoyangaojing: '校验告警',
  packages_business_verification_form_zanbuzhichi_doris: '暂不支持Doris。',
  packages_business_verification_form_task_alarm: '校验任务告警',
  packages_business_verification_form_task_alarm_when_error:
    '当校验任务出错时进行告警',
  packages_business_verification_form_task_alarm_when_diff_result_over_count1:
    '当count校验结果的差异行数大于',
  packages_business_verification_form_task_alarm_when_diff_result_over_count2:
    '时进行告警',
  packages_business_verification_form_task_alarm_when_result_table_over_count1:
    '当值校验结果的表数据差大于',
  packages_business_task_status_error_tip:
    '任务删除成功，以下几个 PostgreSQL 连接的信息清除失败，需要您使用以下方式手动清除',
  packages_business_task_status_next_run_time: '下次运行时间：{val}',
  packages_business_relation_details_rizhiwajueshi: '日志挖掘时间',
  packages_business_relation_details_wajuemingcheng: '挖掘名称',
  packages_business_relation_details_wajuexinxi: '挖掘信息',
  packages_business_connections_databaseform_shujuyuanzhongmo:
    '当数据源中模型数量小于1万时，会按照每小时一次进行模型刷新；当数据源中模型数量大于1万时，会每天按照指定的时间进行模型刷新。',
  packages_business_connections_databaseform_moxingjiazaipin: '模型加载时间',
  packages_business_task_list_lianjieming: '连接名: ',
  packages_business_task_list_dierbushanchu: '// 第二步 删除 slot_name',
  packages_business_task_list_diyibuchaxun: '//第一步 查询 slot_name',
  //告警设置
  packages_business_notify_sms_notification: '短信通知',
  packages_business_notify_email_notification: '邮件通知',
  packages_business_notify_webchat_notification: '微信通知',
  packages_business_notify_alarm_title: 'Agent告警设置',
  packages_business_notify_system_notice: '系统通知',
  packages_business_setting_alarm_notification_notify_noticeInterval:
    '发送间隔',
  packages_business_setting_notification_alarm_notification_gaojingtongzhi:
    '告警通知',
  packages_business_setting_alarmnotification_gaojingzhibiao: '告警指标',
  packages_business_setting_alarmnotification_dangjiediandeping:
    '当节点的平均处理耗时超过阀值时',
  packages_business_setting_alarmnotification_dangshujuyuanjie:
    '当数据源节点的平均处理耗时超过阀值时',
  packages_business_setting_alarmnotification_dangshujuyuanxie:
    '当数据源协议连接耗时超过阀值时',
  packages_business_setting_alarmnotification_dangshujuyuanwang:
    '当数据源网络连接耗时',
  packages_business_setting_alarmnotification_dangshujuwufa:
    '当数据源无法连接网络时',
  packages_business_setting_alarmnotification_dangrenwudezeng:
    '当任务的增量延迟超过阀值时',
  packages_business_setting_alarmnotification_dangrenwutingzhi: '当任务停止时',
  packages_business_setting_alarmnotification_dangrenwuzengliang:
    '当任务增量开始时',
  packages_business_setting_alarmnotification_dangrenwuquanliang:
    '当任务全量完成时',
  packages_business_setting_alarmnotification_dangrenwujiaoyan:
    '当任务校验出错时',
  packages_business_setting_alarmnotification_dangrenwuyudao:
    '当任务遇到错误时',
  packages_business_setting_alarmnotification_dangrenwustop: 'Agent服务停止时',
  packages_business_setting_alarmnotification_dangrenwuuP: 'Agent服务启动时',
  packages_business_setting_alarmnotification_msshigaojing: 's 时告警',
  packages_business_setting_alarmnotification_lianxu: '连续',
  packages_business_setting_alarmnotification_cichugaojinggui:
    '此处告警规则设置为系统全局告警规则设置，任务运行监控页面的告警规则设置优先级高于系统全局设置',
  packages_business_setting_alarmnotification_renwumorengao:
    '任务默认告警规则设置',
  packages_business_setting_alarmnotification_morengaojinggui: '默认告警规则',
  packages_business_setting_alarmnotification_renwugaojingshe: '任务告警设置',
  packages_business_setting_alarmnotification_recipient_setting:
    '任务默认告警接收人设置',
  packages_business_setting_alarmnotification_recipient_desc:
    '此处设置为系统全局告警接收人设置，设置的所有的接收人都可以收到当前系统已开启的告警',
  packages_business_setting_alarmnotification_recipient_default:
    '默认告警接收人',
  packages_business_setting_alarmnotification_recipient: '告警接收人',
  packages_business_setting_alarmnotification_recipient_tip:
    '支持设置多个告警接收人邮箱，多个邮箱以逗号分隔',
  packages_business_setting_alarmnotification_channel: '告警通知渠道',
  packages_business_setting_alarmnotification_dangjiaoyanrenwucuowu:
    '当校验任务遇到错误时',
  packages_business_setting_alarmnotification_dangjiaoyanrenwushuliangcuowu:
    '当count校验结果的差异行数大于阈值时',
  packages_business_setting_alarmnotification_dangjiaoyanrenwuzhicuowu:
    '当值校验结果的表数据差大于阈值时',
  //消息通知
  packages_business_notify_user_all_notice: '全部通知',
  packages_business_notify_unread_notice: '未读消息',
  packages_business_notify_mask_read: '标记本页为已读',
  packages_business_notify_mask_read_all: '标记全部为已读',
  packages_business_notify_notice_type: '消息类型',
  packages_business_notify_notice_level: '消息级别',
  packages_business_notify_no_notice: '暂无通知',
  packages_business_notify_no_webchat_notification:
    '您的账户还没有进行微信绑定，如需通过微信接收通知信息，请先退出登录后通过微信扫码完成绑定',
  packages_business_connections_databaseform_bujiazai: '不加载',
  packages_business_connections_databaseform_system: '跟随系统设置',
  packages_business_custom_node_placeholder: '请输入节点名称搜索',
  packages_business_custom_node_edit_confirm:
    '检测到以下运行中的任务调用了该节点，如需配置生效请重新启动任务',
  packages_business_task_list_sqLyuju: 'SQL语句:',
  packages_business_relation_details_waicunxinxi: '使用的外存信息',
  packages_business_milestone_list_cuowuxinxi: '错误信息',
  packages_business_milestone_list_progr: '({val1}%,剩余{val2})',
  packages_business_milestone_list_chucuo: '出错',
  packages_business_milestone_list_shujuchuli: '数据处理',
  packages_business_milestone_list_mubiaoshujuxie: '目标数据写入',
  packages_business_milestone_list_chuangjianmubiaobiao: '创建目标表',
  packages_business_milestone_list_lianjiebingyanzheng: '连接并验证账号权限',
  packages_business_milestone_list_duquzengliangshu: '读取增量数据',
  packages_business_milestone_list_kaiqizengliang: '开启增量',
  packages_business_milestone_list_duququanliangshu: '读取全量数据',
  packages_business_milestone_list_finish: '{val1}/{val2} 已经完成，{val3} ...',
  packages_business_milestone_list_zhengtijindu: '整体进度',
  packages_business_milestone_list_jinhangzhongpr:
    '进行中，{val1}%已完成，预计剩余时间{val2}',
  packages_business_milestone_list_zengliangshujuqian: '增量数据迁移',
  packages_business_milestone_list_quanliangshujuqian: '全量数据复制',
  packages_business_milestone_list_biaojiegouqianyi: '表结构复制',
  packages_business_milestone_list_load_table_structure: '加载表结构',
  packages_business_milestone_list_shujujiedianchu: '连接数据源',
  packages_business_milestone_list_renwudiaodu: '任务调度',
  packages_business_milestone_list_haoshi: '耗时',
  packages_business_milestone_list_guanjianbuzhou: '关键步骤',
  packages_business_nodes_list_laiyuan: '来源',
  //表详情
  daas_data_discovery_previewdrawer_qingshurumingcheng: '请输入名称',
  daas_data_discovery_previewdrawer_jiedian: '节点',
  daas_data_discovery_previewdrawer_renwumiaoshu: '任务描述',
  daas_data_discovery_previewdrawer_yinqingmiaoshu: '引擎描述',
  daas_data_discovery_previewdrawer_yinqingmingcheng: '引擎名称',
  daas_data_discovery_previewdrawer_jiedianshu: '节点数',
  daas_data_discovery_previewdrawer_shuchucanshu: '输出参数',
  daas_data_discovery_previewdrawer_fuwumiaoshu: '服务描述',
  daas_data_discovery_previewdrawer_jiedianmiaoshu: '节点描述',
  daas_data_discovery_previewdrawer_shurujiedian: '输入节点',
  daas_data_discovery_previewdrawer_shuchujiedian: '输出节点',
  //数据发现-数据对象
  object_list_name: '对象名称',
  object_list_classification: '对象分类',
  object_list_type: '对象类型',
  object_list_source_type: '来源类型',
  object_list_source_information: '来源信息',
  datadiscovery_catalogue_ziyuanbangding: '资源绑定',
  datadiscovery_catalogue_lianjieduixiangming: '连接对象名',
  datadiscovery_catalogue_ziyuanleixing: '资源类型',
  datadiscovery_objectlist_duixiangminglaiyuan: '对象名称/数据源',
  datadiscovery_objectlist_laiyuanfenlei: '来源分类',
  datadiscovery_previewdrawer_shujuxiang: '数据项',
  datadiscovery_previewdrawer_yewumingcheng: '业务名称',
  datadiscovery_previewdrawer_lianjiemiaoshu: '连接描述',
  datadiscovery_previewdrawer_shujuliang: '数据量',
  datadiscovery_previewdrawer_biangengshijian: '变更时间',
  datadiscovery_previewdrawer_guanliyuan: '管理员',
  datadiscovery_previewdrawer_duixiangxiangqing: '对象详情',
  datadiscovery_previewdrawer_yewumiaoshu: '业务描述',
  datadiscovery_previewdrawer_yewuleixing: '业务类型',
  datadiscovery_previewdrawer_suoyin: '索引',
  datadiscovery_previewdrawer_waijian: '外键',
  datadiscovery_previewdrawer_zhujian: '主键',
  connection_list_name: '连接名',
  meta_table_default: '默认值',
  meta_table_not_null: '非空',
  page_title_overview: '概览',
  metadata_meta_type_table: '数据表',
  packages_business_create_connection_dialog_xuanzeshujuyuan: '选择数据源类型',
  packages_business_create_connection_dialog_neirongSho:
    '显示处于 ALPHA 状态的连接器',
  packages_business_create_connection_dialog_neirongSho2:
    '显示处于 BETA 状态的连接器',
  packages_business_create_connection_dialog_neirongCho:
    '从下面选择一个数据源连接器并配置连接和凭据。',
  // 共享挖掘
  packages_business_shared_cdc_placeholder_task_name: '请输入挖掘任务名搜索',
  packages_business_shared_cdc_placeholder_connection_name:
    '请输入连接名称搜索',
  packages_business_shared_cdc_name: '请输入挖掘名称',
  packages_business_shared_cdc_setting_select_mode: '存储模式',
  packages_business_shared_cdc_setting_select_mongodb_tip: '请输入mongodb连接',
  packages_business_shared_cdc_setting_select_table_tip: '请输入表名',
  packages_business_shared_cdc_setting_select_time_tip: '请选择日志保存时长',
  packages_business_shared_cdc_setting_message_edit_save:
    '保存成功，重启任务后生效',
  packages_business_shared_list_name: '挖掘名称',
  packages_business_shared_list_time_excavation: '挖掘所处时间点',
  packages_business_shared_list_setting: '挖掘设置',
  packages_business_shared_list_status: '状态',
  packages_business_shared_list_time: '挖掘延迟',
  packages_business_shared_list_edit_title: '挖掘编辑',
  packages_business_shared_list_edit_title_start_time: '挖掘开始时间',
  packages_business_shared_detail_title: '挖掘表详情',
  packages_business_shared_form_setting_table_name: '存储MongoDB表名',
  packages_business_shared_form_setting_log_time: '日志保存时长',
  packages_business_shared_form_edit_name: '挖掘名称',
  packages_business_shared_form_edit_title: '是否放弃编辑该挖掘任务',
  packages_business_shared_form_edit_text: '此操作不会保存已修改的内容',
  packages_business_shared_detail_mining_info: '挖掘信息',
  packages_business_shared_detail_name: '挖掘名称',
  packages_business_shared_detail_log_mining_time: '日志挖掘时间',
  packages_business_shared_detail_log_time: '日志保存时长',
  packages_business_shared_detail_call_task: '调用任务',
  packages_business_shared_detail_source_time: '源库时间点',
  packages_business_shared_detail_sycn_time_point: '同步时间点',
  packages_business_shared_detail_mining_status: '挖掘状态',
  packages_business_shared_detail_button_table_info: '表详情',
  packages_business_shared_detail_statistics_time: '统计时间',
  packages_business_shared_detail_incremental_time: '所处的时间点',
  packages_business_shared_mining_detail_wajuexiangqingx: '挖掘详情x轴：',
  packages_business_stop_confirm_message:
    '初始化类型的任务暂停后如果再次启动，任务会从头开始同步，确定暂停?',
  packages_business_important_reminder: '重要提醒',
  packages_business_tablename: '表名称',
  packages_business_shared_cdc_persistence_rocksdb_path:
    'RocksDB存储的本地路径',
  packages_business_shared_mining_table_jinriwajue: '今日挖掘',
  packages_business_shared_mining_table_leijiwajue: '累计挖掘',
  packages_business_shared_mining_table_zuixinrizhishi: '最新日志时间',
  packages_business_shared_mining_table_shoutiaorizhishi: '首条日志时间',
  packages_business_shared_mining_table_jiaruwajueshi: '加入挖掘时间',
  packages_business_shared_mining_table_biaoming: '表名',
  packages_business_shared_mining_table_wajuebiaoxinxi: '挖掘表信息',
  packages_business_relation_sharedlist_shiyongdewajue: '使用的挖掘表',
  packages_business_components_classificationtree_suoyoumulu: '所有目录',
  packages_business_milestone_list_zengliangshujuxie: '增量数据写入',
  packages_business_milestone_list_quanliangshujuxie: '全量数据写入',
  packages_business_milestone_list_jinruzengliangshu: '增量数据复制',
  packages_business_logs_nodelog_cuowuduizhan: '错误堆栈',
  packages_business_logs_nodelog_yuanyinfenxi: ' 原因分析',
  packages_business_logs_nodelog_xianshishijianchuo: '显示时间',
  packages_business_connections_databaseform_chakanxintiaoren: '查看心跳任务',
  packages_business_connections_databaseform_dakaixintiaobiao:
    '开启此功能后, 平台会在源库内新建一张表, 并以每秒 1 次的频率, 向源库的这张表内做更新, 借助这张表, 平台可以实现精准的数据延迟探测, 并可以有效监控任务健康状况, 此功能需要对源库具有写权限才会生效。',
  packages_business_connections_databaseform_kaiqixintiaobiao: '开启心跳表',
  packages_business_connections_databaseform_jiaobentiaoshi: '脚本调试',
  // api服务管理
  packages_business_data_server_drawer_qingshurucanshu: '请输入参数名称',
  packages_business_data_server_drawer_paixu: '排序',
  packages_business_data_server_drawer_meigefenyefan: '每个分页返回的记录数',
  packages_business_data_server_drawer_fenyebianhao: '分页编号',
  packages_business_data_server_drawer_zidingyichaxun: '自定义查询',
  packages_business_data_server_drawer_morenchaxun: '默认查询',
  packages_business_data_server_drawer_qingxuanzeduixiang: '请选择对象名称',
  packages_business_data_server_drawer_qingxuanzelianjie: '请选择连接类型',
  packages_business_data_server_drawer_qingshurufuwu: '请输入服务名称',
  packages_business_data_server_drawer_selectPermissions: '请选择权限范围',
  packages_business_data_server_drawer_shilidaima: '示例代码',
  packages_business_data_server_drawer_shilidaima2: '示例代码',
  packages_business_data_server_drawer_fanhuijieguo: '返回结果',
  packages_business_data_server_drawer_diaoyongfangshi: '调用方式',
  packages_business_data_server_drawer_fuwufangwen: '服务访问',
  packages_business_data_server_drawer_shuchujieguo: '输出结果',
  packages_business_data_server_drawer_pailietiaojian: '排列条件',
  packages_business_data_server_drawer_shaixuantiaojian: '筛选条件',
  packages_business_data_server_drawer_canshuzhi: '参数值',
  packages_business_data_server_drawer_canshumingcheng: '参数名称',
  packages_business_data_server_drawer_shurucanshu: '输入参数',
  packages_business_data_server_drawer_quanxianfanwei: '权限范围',
  packages_business_data_server_drawer_jiekouleixing: '接口类型',
  packages_business_data_server_drawer_fabujiedian: '发布节点',
  packages_business_data_server_drawer_caozuoleixing: '操作类型',
  packages_business_data_server_drawer_zanwumiaoshu: '暂无描述',
  packages_business_data_server_drawer_tiaoshi: '调试',
  packages_business_data_server_drawer_peizhi: '配置',
  packages_business_data_server_drawer_chuangjianfuwu: '创建服务',
  packages_business_data_server_drawer_fuwuxiangqing: '服务详情',
  packages_business_data_server_list_quedingchexiaogai: '确定撤销该服务？',
  packages_business_data_server_list_quedingfabugai: '确定发布该服务？',
  packages_business_data_server_list_querenshanchufu: '确认删除服务？',
  packages_business_data_server_list_huoqufuwuyu: '获取服务域名失败！',
  packages_business_data_server_list_fuwuzhuangtai: '服务状态',
  packages_business_data_server_list_guanlianduixiang: '关联对象',
  packages_business_data_server_list_fuwumingcheng: '服务名称',
  packages_business_data_server_drawer_geshicuowu: '格式错误',
  packages_business_data_server_drawer_validate:
    '只能包含中文、字母、数字、下划线和美元符号,并且数字不能开头',
  packages_business_data_server_drawer_aPI_path_Settings: '访问路径设置',
  packages_business_data_server_drawer_default_path: '默认访问路径',
  packages_business_data_server_drawer_custom_path: '自定义访问路径',
  packages_business_data_server_drawer_prefix: '前缀',
  packages_business_data_server_drawer_base_path: '基础路径',
  packages_business_data_server_drawer_path: '访问路径',
  packages_business_data_server_drawer_confirm_tip:
    '重新生成会导致原API访问路径发生改变，是否确认重新生成？',
  packages_business_connection_debug_input_arg: '模拟参数',
  packages_business_connection_debug_input_arg_error: '模拟参数格式错误',
  packages_business_more_than: '超过',
  packages_business_more_than_after: '秒，未返回结果时自动终止试运行',
  packages_business_connection_debug_form_error: '请检查表单必填项',
  packages_business_connection_debug_as: '作为',

  // LDP
  packages_business_data_console_sources: '源数据层',
  packages_business_data_console_fdm: '平台缓存层',
  packages_business_data_console_mdm: '平台加工层',
  packages_business_data_console_targets: '数据目标和服务层',
  packages_business_data_console_goto_ai_chat: 'AI 对话',
  packages_business_create_clone_task: '创建数据复制任务',
  packages_business_create_sync_task: '创建数据开发任务',
  packages_business_table_prefix: '表前缀',
  packages_business_last_data_change_time: '数据最后更新时间',
  packages_business_cdc_delay_time: '增量数据延迟',
  packages_business_rows: '行数',
  packages_business_columns: '列数',
  packages_business_storage_size: '存储大小',
  packages_business_columns_preview: '列预览',
  packages_business_sample_data: '样本数据',
  packages_business_table_preview_task: '以这个模型作为源/目标的任务',
  packages_business_table_preview_connection_task: '以该连接作为源/目标的任务',
  packages_business_table_count: '包含表数量',
  packages_business_overview: '概览',
  packages_business_tasks: '任务',
  packages_business_model_update_time: '模型更新时间',
  packages_business_save_and_more: '保存并添加更多',
  packages_business_table_status_error: '异常',
  packages_business_table_status_draft: '草稿',
  packages_business_table_status_normal: '正常',
  packages_business_data_console_target_no_task: '未对此目标配置任何任务',
  packages_business_data_console_target_no_api: '未对此应用配置任何API',
  packages_business_data_console_target_connection_desc: '将数据同步到 {val}',
  packages_business_view_more: '查看更多',
  packages_business_view_collapse: '收起',

  packages_business_shared_const_shier: '十二',
  packages_business_shared_const_shiyi: '十一',
  packages_business_shared_const_shi: '十',
  packages_business_shared_const_jiu: '九',
  packages_business_shared_const_ba: '八',
  packages_business_shared_const_qi: '七',
  packages_business_shared_const_liu: '六',
  packages_business_shared_const_wu: '五',
  packages_business_shared_const_si: '四',
  packages_business_shared_const_san: '三',
  packages_business_shared_const_er: '二',
  packages_business_shared_const_yi: '一',
  packages_business_shared_const_ling: '零',
  packages_business_shared_const_yiquxiao: '已取消',
  packages_business_shared_const_shixiao: '失效',
  packages_business_shared_const_tuikuanzhong: '退款中',
  packages_business_shared_const_tuikuanshibai: '退款失败',
  packages_business_shared_const_yituikuan: '已退款',
  packages_business_shared_const_zhifushibai: '支付失败',
  packages_business_payment_timeout: '支付超时',
  packages_business_shared_const_yizhifu: '已支付',
  packages_business_shared_const_weizhifu: '未支付',
  packages_business_shared_ws_client_webso: 'websocket 消息解析失败: ',
  packages_business_shared_ws_client_webso2: 'websocket 接收消息格式错误: ',
  packages_business_shared_ws_client_acces: 'access_token 过期',
  packages_business_shared_ws_client_webso3: 'websocket 已关闭',
  packages_business_shared_ws_client_webso4: 'websocket 断开连接',
  packages_business_shared_ws_client_webso5: 'websocket 已连接',
  packages_business_shared_ws_client_webso6: 'websocket 超过最大重连次数 ',
  packages_business_shared_ws_client_cizhonglian: '次重连',
  packages_business_shared_ws_client_webso7: 'websocket 尝试第',
  packages_business_shared_ws_client_webso8: 'websocket 连接失败，准备尝试重连',
  packages_business_switch_directory_view: '切换至目录视图',
  packages_business_switch_data_console_view: '切换至面板视图',
  packages_business_task_created_success: '任务创建成功，点击查看',
  packages_business_task_created_fail_no_primary_key:
    '任务已经创建，但由于您的表没有主键，需要进入任务编辑手动设置更新条件字段，点击查看任务',
  packages_business_fdm_create_task_dialog_desc_prefix: `${import.meta.env.VUE_APP_PAGE_TITLE} 将自动创建一个数据复制管道任务，将您选择的`,
  packages_business_fdm_create_task_dialog_desc_suffix:
    '的结构和数据自动复制到数据平台的 Cache 层并保持源库和Cache 层数据的准实时同步及自动校验。在大部分时候源库的结构改动(DDL)也会被复制到Cache 层。您可以在通过点击Cache 层里面的库名右侧的ICON来监控该管道任务的运行状态。您也可以选择现在修改在 Cache 层的物理表名前缀。',
  packages_business_mdm_create_task_dialog_desc_prefix:
    '这将在数据平台的 Curated 层创建一个加工模型。创建加工模型的常见场景有以下几种：',
  packages_business_fdm_create_task_dialog_desc_li1:
    '需要对 Cache 层的数据做一些转型，增强，加计算字段等处理',
  packages_business_fdm_create_task_dialog_desc_li2:
    '需要对数个 Cache 层的表的结构进行合并，构建一个宽表',
  packages_business_fdm_create_task_dialog_desc_li3:
    '需要对数个 Cache 层的表的数据进行合并，构建一个合并表',
  packages_business_mdm_create_task_dialog_desc_suffix:
    '注意: 你可以直接在Cache 层直接发布API或者做数据复制任务到目标端。如果是因为这两个原因，你无需创建加工层模型。',
  packages_business_mdm_create_task_dialog_desc_table_name:
    '请输入打算新构建在Curated 层里面的表名。如果该表名已经存在，默认将覆盖已有的数据',
  packages_business_save_and_run_now: '保存并运行',
  packages_business_save_only: '仅保存',
  packages_business_target_create_task_dialog_desc_prefix_clone: `${import.meta.env.VUE_APP_PAGE_TITLE} 将创建一个数据复制任务，将`,
  packages_business_target_create_task_dialog_desc_prefix_sync: `${import.meta.env.VUE_APP_PAGE_TITLE} 将创建一个数据开发任务，将`,
  packages_business_target_create_task_dialog_desc_to: '同步到',
  packages_business_target_create_task_dialog_desc_suffix:
    '请点击下面的按钮继续,您也可以更改任务名称。',
  packages_business_fdm_empty_text:
    '请将<strong>源数据层</strong>中的表拖拽至此<br/>即可开始复制数据',
  packages_business_mdm_empty_text:
    '请将<strong>源数据层/平台缓存层</strong>中的表拖拽至此<br/>即可开始同步数据',
  packages_business_catalog_delete_confirm_message:
    '此操作仅会将该分类及其子分类删除，如需删除分类下的物理表,请您自行操作。',
  packages_business_mdm_table_duplication_confirm:
    '目标表已经存在，请确定是否继续？',
  packages_business_data_console_mode: '请选择产品能力模式',
  packages_business_data_console_mode_integration: '作为数据集成平台使用',
  packages_business_data_console_mode_integration_tooltip_1:
    '支持异构数据与结构自动实时同步',
  packages_business_data_console_mode_integration_tooltip_2:
    '支持数据的实时加工与转换',
  packages_business_data_console_mode_integration_tooltip_3:
    '100+连接器, 包括数据库,消息队列,文件,API 等',
  packages_business_data_console_mode_service_tooltip_1:
    '支持数据集成模式全部能力',
  packages_business_data_console_mode_service_tooltip_2:
    '支持平台缓存并标准化企业关键数据模型',
  packages_business_data_console_mode_service_tooltip_3:
    '支持多种下游服务集成与发布管理',
  packages_business_data_console_mode_service: '作为数据服务平台使用',
  packages_business_data_console_fdm_mdm_storage: '数据层存储',
  packages_business_data_console_fdm_storage: '平台缓存层存储',
  packages_business_data_console_mdm_storage: '平台加工层存储',
  packages_business_data_console_fdm_mdm_storage_tooltip:
    '请指定用于额外数据层存储的数据库连接',
  packages_business_mongodb_atlas_cluster: 'MongoDB Atlas 集群',
  packages_business_mongodb_self_hosted_cluster: '自托管 MongoDB 集群',
  packages_business_mongodb_full_management_cluster: '全托管 MongoDB 集群',
  packages_business_data_console_setting_saved_tooltip:
    '存储中心保存后, 暂不支持修改, 请谨慎设置',
  // 共享缓存
  packages_business_shared_cache_create: '创建缓存',
  packages_business_shared_cache_edit: '编辑缓存',
  packages_business_shared_cache_placeholder_task_name: '请输入缓存任务名搜索',
  packages_business_shared_cache_placeholder_connection_name:
    '请输入连接名称搜索',
  packages_business_shared_cache_button_create: '新建缓存',
  packages_business_shared_cache_name: '缓存名称',
  packages_business_shared_cache_status: '缓存状态',
  packages_business_shared_cache_time: '缓存时间',
  packages_business_shared_cache_keys: '缓存键',
  packages_business_shared_cache_fields: '缓存字段',
  packages_business_shared_cache_code: '应用代码',
  packages_business_shared_cache_placeholder_name: '请输入缓存名称',
  packages_business_shared_cache_placeholder_connection: '请选择连接',
  packages_business_shared_cache_placeholder_table: '请选择表',
  packages_business_shared_cache_placeholder_keys: '请选择缓存键',
  packages_business_shared_cache_placeholder_fields: '请选择缓存字段',
  packages_business_shared_cache_messge_no_table: '找不对所选表模型',
  packages_business_shared_cache_max_memory: '缓存最大内存',
  packages_business_shared_cache_placeholder_max_memory: '请输入缓存最大内存',
  packages_business_shared_cache_placeholder_external_storage: '请选择外存配置',
  packages_business_shared_cache_keys_tooltip:
    '以该字段作为主键识别数据进行缓存',
  packages_business_shared_cache_fields_tooltip: '需要进行缓存的常用字段',
  packages_business_shared_cache_max_memory_tooltip:
    '系统会保存的最大内存量，超过则按调用时间，将最不常用的数据删掉',
  packages_business_shared_cache_code_tooltip:
    '可在JS节点中输入这段代码使用该缓存',
  packages_business_shared_cache_column_connection: '所属连接',
  packages_business_shared_cache_column_table: '所属表',
  packages_business_shared_cache_cache_key_message: '所选缓存键无索引',
  packages_business_shared_cache_cache_key_auto_create: '自动创建索引',
  packages_business_shared_cache_cache_key_auto_create_tip:
    '开启该能力后，会自动在源表为缓存键创建索引，可能会对源库造成影响，请谨慎开启',
  packages_business_relation_list_gongxianghuancun: '共享缓存',
  packages_business_application_delete_shanchuyingyong: '删除应用',
  packages_business_application_delete_ninzhengzaishanchu:
    '您正在删除应用<span class="fw-bolder font-color-dark">{val1}</span>，是否确认删除',
  packages_business_application_delete_ninzhengzaishanchu2:
    '您正在删除应用<span class="fw-bolder font-color-dark">{val1}</span>，该应用下的API将移动到',
  packages_business_application_delete_yingyongmiaoshubu: '应用描述不能为空',
  packages_business_application_delete_yingyongmingchengbu: '应用名称不能为空',
  packages_business_application_delete_shifouquerenshan: '是否确认删除',
  packages_business_application_editor_yingyongmiaoshu: '应用描述',
  packages_business_application_list_qingshuruyingyong: '请输入应用名称',
  packages_business_application_list_yifabuAp: '已发布API数量',
  packages_business_application_list_zongApIshu: '总API数量',
  packages_business_application_list_yingyongmingcheng: '应用名称',
  packages_business_application_list_chuangjianyingyong: '创建应用',
  packages_business_data_server_drawer_qingxuanzesuoshu: '请选择所属应用',
  packages_business_data_server_drawer_suoshuyingyong: '所属应用',
  packages_business_create_connection_scenedialog_gongzuoliu: '工作流',
  packages_business_create_connection_scenedialog_duiliegongshu: '队列供数',
  packages_business_create_connection_scenedialog_guochantidai: '国产替代',
  packages_business_create_connection_scenedialog_shujukutongbu: '数据库同步',
  packages_business_create_connection_scenedialog_chaxunjiasu: '查询加速',
  packages_business_create_connection_scenedialog_rushucang: '入数仓',
  packages_business_create_connection_scenedialog_tuijianchangjing: '推荐场景',
  packages_business_create_connection_scenedialog_qingxuanzeninde:
    '请选择您的使用场景',
  packages_business_create_connection_serveform_fenleimingcheng: '分类名称',
  packages_business_components_tableview_yizhegemoxing:
    '以这个模型源/目标的任务',
  packages_business_components_tableview_xinzenglebiaoqian: '新增了标签603',
  packages_business_components_tableview_zengliangshujuyan: '增量数据延迟：',
  packages_business_components_tableview_shujuzuihougeng: '数据最后更新时间：',
  packages_business_swimlane_fdm_biaobianji: '表编辑',
  packages_business_swimlane_tablepreview_zuihoufangwenshi: '最后访问时间',
  packages_business_swimlane_tablepreview_apIchuanshu: 'API传输总量',
  packages_business_swimlane_tablepreview_apIfangwen: 'API访问行数',
  packages_business_swimlane_tablepreview_fangwencishu: '访问次数',
  packages_business_swimlane_tablepreview_apifuwu: 'api服务名称',
  packages_business_swimlane_target_yejibao: '业绩宝',
  packages_business_task_list_meiyoufaxiannin:
    '没有发现您最近有任务报错, 如果有其他问题, 欢迎咨询我们的人工客服',
  packages_business_api_application_list_xitongmorenchuang:
    '系统默认创建的应用，不可编辑和删除',
  packages_business_create_connection_title_select_type: '请选择数据源类型',
  // 外存管理
  packages_business_external_storage_list_querenshanchuwai: '确认删除外存？',
  packages_business_external_storage_list_qingshurucunchu: '请输入存储路径',
  packages_business_external_storage_list_qingshuruwaicun: '请输入外存名称',
  packages_business_external_storage_list_qingshuruwaicun2: '请输入外存表名称',
  packages_business_external_storage_list_sheweimoren: '设为默认',
  packages_business_external_storage_list_cunchulujing: '存储路径',
  packages_business_external_storage_list_chuangjianwaicun: '创建外存',
  packages_business_external_storage_list_bianjiwaicun: '编辑外存',
  packages_business_external_storage_list_tishi:
    '该外存已被 {val1} 个任务调用，请删除或者修改配置后重试',
  // API
  packages_business_api_publish: 'API 发布',
  packages_business_api_application: 'API 应用',
  packages_business_api_application_md: `## API 应用
- 您可以轻松地创建新的应用，将 API 分类到不同的应用中，实现API的差异化管理，从而提高业务安全性和效率
- 您可以将数据库表拖放到应用上，快速地发布 API
      `,
  packages_business_qingshurucanshu: '请输入参数名称',
  packages_business_paixu: '排序',
  packages_business_meigefenyefan: '每个分页返回的记录数',
  packages_business_fenyebianhao: '分页编号',
  packages_business_zidingyichaxun: '自定义查询',
  packages_business_morenchaxun: '默认查询',
  packages_business_qingxuanzeduixiang: '请选择对象名称',
  packages_business_qingxuanzelianjie: '请选择连接类型',
  packages_business_qingshurufuwu: '请输入服务名称',
  packages_business_selectPermissions: '请选择权限范围',
  packages_business_shilidaima: '示例代码',
  packages_business_shilidaima2: '示例代码',
  packages_business_fanhuijieguo: '返回结果',
  packages_business_diaoyongfangshi: '调用方式',
  packages_business_fuwufangwen: '服务访问',
  packages_business_shuchujieguo: '输出结果',
  packages_business_pailietiaojian: '排列条件',
  packages_business_shaixuantiaojian: '筛选条件',
  packages_business_canshuzhi: '参数值',
  packages_business_canshumingcheng: '参数名称',
  packages_business_shurucanshu: '输入参数',
  packages_business_quanxianfanwei: '权限范围',
  packages_business_jiekouleixing: '接口类型',
  packages_business_fabujiedian: '发布节点',
  packages_business_caozuoleixing: '操作类型',
  packages_business_zanwumiaoshu: '暂无描述',
  packages_business_tiaoshi: '调试',
  packages_business_peizhi: '配置',
  packages_business_chuangjianfuwu: '创建服务',
  packages_business_fuwuxiangqing: '服务详情',
  packages_business_geshicuowu: '格式错误',
  packages_business_validate:
    '只能包含中文、字母、数字、下划线和美元符号,并且数字不能开头',
  packages_business_aPI_path_Settings: '访问路径设置',
  packages_business_default_path: '默认访问路径',
  packages_business_custom_path: '自定义访问路径',
  packages_business_prefix: '前缀',
  packages_business_base_path: '基础路径',
  packages_business_path: '访问路径',
  packages_business_confirm_tip:
    '重新生成会导致原API访问路径发生改变，是否确认重新生成？',
  packages_business_create_connection_scenedialog_table:
    'Tablestore 是一种高可靠性、高性能、灵活性和可扩展性的分布式NoSQL数据存储服务，适用于实时数据查询和分析等应用场景。',
  packages_business_create_connection_scenedialog_selec:
    'SelectDB Cloud 是一种基于Apache Doris内核的全托管实时数据仓库服务，具有高可靠性、高性能、易用性和低成本等优点，适用于处理海量数据的查询和分析需求。',
  packages_business_create_connection_scenedialog_redis:
    'Redis 是一种高性能内存数据库，支持多种数据结构和持久化方式，具有可扩展性和可靠性，适用于缓存、会话管理、排行榜、消息队列等应用场景。',
  packages_business_create_connection_scenedialog_mongo:
    'MongoDB 是一种非关系型数据库，具有灵活性、高性能、易用性和可扩展性，适用于需要处理大量非结构化数据和需要快速查询和可扩展性的应用场景。',
  packages_business_create_connection_scenedialog_bigQu:
    'BigQuery 是Google Cloud提供的托管式数据仓库，以高速、可扩展和安全著称，可以处理PB级数据，与多个工具集成，适用于各种数据分析和挖掘场景。',
  packages_business_create_connection_mysql_desc:
    'MySQL 适用于中小规模网站和应用程序，轻量级数据库管理系统，支持数据存储、查询和简单分析，广泛用于Web开发和轻负载应用。',
  packages_business_create_connection_oracle_desc:
    'Oracle 适用于企业级数据库解决方案，支持大规模数据处理、高性能事务处理和复杂查询，广泛应用于企业的核心业务系统和数据管理。',
  packages_business_create_connection_sqlserver_desc:
    'SQL Server 主要用于管理和处理大规模数据库，适用于企业级应用程序和网站，支持数据存储、查询、分析和报告等。',
  packages_business_create_connection_postgresql_desc:
    'PostgreSQL 适用于高度稳定的数据存储和复杂查询，广泛应用于Web应用程序、地理信息系统、数据分析和企业级应用。',
  packages_business_create_connection_clickhouse_desc:
    'ClickHouse 适用于快速查询和分析大规模数据，特别擅长处理实时分析、日志分析、数据仓库和时间序列数据。',
  packages_business_create_connection_elasticsearch_desc:
    'Elasticsearch 适用于全文搜索、日志分析、实时数据分析和大规模数据索引，广泛应用于搜索引擎、监控和企业级应用。',
  packages_business_create_connection_dummy_desc:
    'Dummy 通常用于表示虚拟或占位符实体，无实际数据。在软件开发和测试中，Dummy对象用于填充空缺或模拟占位行为。',
  packages_business_create_connection_kafka_desc:
    'Kafka 适用于高吞吐量的实时数据流处理，用于日志收集、数据传输、消息发布/订阅和流式处理应用，特别擅长大规模数据流处理。',
  packages_business_create_connection_doris_desc:
    'Doris 适用于实时数据分析和报表，支持高并发查询和复杂分析，广泛应用于数据仓库、BI报表和数据可视化。',
  packages_business_create_connection_mongodbatlas_desc:
    'MongoDB Atlas是全托管的MongoDB数据库服务，它通过自动化数据库管理，简化了部署、扩展和监控过程，使开发者可以更专注于应用程序开发。Atlas支持弹性扩展、全球部署和安全性功能，适用于各种规模和类型的应用。',
  packages_business_swimlane_tablepreview_chuangjianrenwu: '创建任务',
  packages_business_as_source: '作为源头',
  packages_business_as_target: '作为目标',
  packages_business_connections_databaseform_dangqianlianjiezheng:
    '当前连接正在使用原外存，切换会导致数据丢失，请谨慎操作。',
  packages_business_swimlane_target_shouye: '首页',
  packages_business_connections_databaseform_chakanwajueren: '查看挖掘任务',
  packages_business_connections_databaseform_dangqianlianjiede:
    '当前连接的挖掘任务正在使用该外存，暂不允许修改，如需修改请先重置或删除对应挖掘任务。',
  packages_business_shared_mining_table_yitingzhiwajue: '已停止挖掘',
  packages_business_shared_mining_table_zhengzaiwajue: '正在挖掘',
  packages_business_shared_mining_table_ninyaotingzhiwa:
    '您要停止挖掘的表正在被以下任务使用，停止挖掘后将会影响以下任务的正常同步，请确认是否要继续停止。',
  packages_business_shared_mining_table_tingzhiwajueti: '停止挖掘提醒',
  packages_business_shared_mining_table_yihebingdelian: '已合并的连接',
  packages_business_shared_mining_table_shengyuyigelian:
    '挖掘任务中至少要有一张表在挖掘，不能全部停止。',
  packages_business_logs_nodelog_yijianfuzhi: '一键复制',
  packages_business_connections_jsdebug_shiyongHtt:
    '使用HttpReceiver最新接收到的数据用于调试',
  packages_business_connections_jsdebug_huoqutiaoshishu: '获取调试数据',
  packages_business_shared_mining_list_shanchurenwus:
    '删除任务<span class="color-primary">{val1}</span>后，此任务将无法恢复',
  packages_business_shared_mining_list_gaiwajuerenwu:
    '该挖掘任务已被 {val} 个任务调用，请删除任务后重试',
  packages_business_shared_cache_list_qingxianxiugaiwai:
    '外存不存在，请先修改外存配置后，再启动。',
  packages_business_components_conditionbox_shifouquerenqing:
    '是否确认清除索引字段为空的校验条件？',
  packages_business_components_conditionbox_suoyinziduanwei: '索引字段为空',
  packages_business_components_conditionbox_yijianqingchusuo:
    '一键清除索引字段为空的条件',
  packages_business_external_storage_list_yanzhengfuwuduan: '验证服务端证书',
  packages_business_external_storage_list_siyaomima: '私钥密码',
  packages_business_external_storage_list_kehuduansiyao: '客户端私钥',
  packages_business_external_storage_list_zhengshubanfaji: '证书颁发机构',
  packages_business_external_storage_list_shiyongTls: '使用 TLS/SSL 连接',
  page_title_verification_create: '新建校验',
  page_title_task_edit: '编辑任务',
  page_title_task_details: '任务详情',
  page_title_verification_history: '校验历史',
  page_title_data_difference_details: '差异详情',
  page_title_data_verification_result: '校验结果',
  page_title_diff_verification_history: '差异校验历史',
  page_title_diff_verification_details: '差异校验详情',
  packages_business_connections_list_dangqianlianjiex:
    '当前连接 xxx 正在作为FDM和MDM的存储使用，删除会导致已有存储数据丢失，是否确认要继续删除。',
  packages_business_connections_list_zhengzaizuoweiF:
    '正在作为FDM和MDM的存储使用，修改会导致已有存储数据丢失，是否确认要继续修改',
  packages_business_connections_list_dangqianlianjie: '当前连接',
  packages_business_components_conditionbox_chakanzidingyi: '查看自定义字段',
  packages_business_components_fielddialog_ziduanbuyunxu: '字段不允许为空',
  packages_business_components_fielddialog_zidingyiziduan: '自定义字段',
  packages_business_verification_list_biaobufenziduan: '表部分字段校验',
  packages_business_components_conditionbox_laiyuanbiaoshuju: '来源表数据过滤',
  packages_business_components_conditionbox_mubiaobiaoshuju: '目标表数据过滤',
  packages_business_components_conditionbox_enableCustomCommand_tip:
    '需要保证查询条件有索引，如果没索引会产生全表扫描导致数据库压力变大',
  packages_business_data_server_list_apIwendang: 'API文档导出',
  packages_business_verification_form_gaojipeizhi: '高级配置',
  packages_business_verification_form_validate_table_is_empty:
    '源表和目标表不能为空，请修改校验表配置',
  packages_business_verification_form_validate_table_is_empty1:
    '因为找不到源表或目标表，以下来源连接将会自动跳过校验：',
  packages_business_verification_form_condition_is_empty:
    '关联校验条件不能为空，请修改校验表配置',
  packages_business_verification_form_index_field_is_empty:
    '因为找不到索引字段，以下来源表将会自动跳过校验：',
  packages_business_verification_form_index_field_count_is_not_equal:
    '因为源表与目标表的索引字段个数不相等，以下来源表将会自动跳过校验：',
  packages_business_verification_list_renyibiaoshuju: '任意表数据校验',
  packages_business_verification_list_renwuyizhixing: '任务一致性校验',
  packages_business_permissionse_settings_create_quanxianshezhi: '权限设置',
  packages_business_permissionse_settings_create_shezhiquanxian: '设置权限',
  packages_business_permissionse_settings_create_xuanzeshouquanjiao:
    '选择授权角色',
  packages_business_permissionse_settings_create_wufaduiyixiashujujinxingshouquan:
    '无法对以下数据进行授权，将跳过保存',
  packages_business_connections_permissionsdialog_tianjiashouquan: '添加授权',
  packages_business_connections_permissionsdialog_gongnengquanxian: '功能权限',
  packages_business_connections_permissionsdialog_shouquanjuese: '授权角色',
  packages_business_connections_permissionsdialog_lianjiequanxianshe:
    '连接权限设置',
  packages_business_connections_preview_quanxianguanli: '权限管理',
  packages_business_connections_preview_shujulianjiequan: '数据连接权限',
  packages_business_notice_list_gonggaobiaoti: '公告标题',
  packages_business_connections_list_wuquanxiandecao: '无权限的操作已被隐藏',
  packages_business_components_upgradecharges_dingyuexinyinqing: '订阅新引擎',
  packages_business_components_upgradecharges_shengjiguige: '升级规格',
  packages_business_components_upgradecharges_dingyuefangshi: '订阅方式',
  packages_business_components_upgradecharges_keyongrenwushu: '剩余可用任务数',
  packages_business_components_upgradecharges_dangqianguige: '当前规格',
  packages_business_components_upgradecharges_dingyueshengji: '订阅升级',
  packages_business_create_connection_sceneform_lianjieceshiwu:
    '连接测试无效，请检查您的连接配置',
  packages_business_create_connection_sceneform_qingxianjinxinglian:
    '请先进行连接测试',
  packages_business_logs_nodelog_qingshengjidingyue:
    '请升级订阅以获取更多任务数量，点击弹窗显示升级引导',
  packages_business_logs_nodelog_yinqingkeyibei:
    '引擎可以被调用的任务超过了限制数，',
  packages_business_task_list_nindekeyunxing:
    '您的可运行任务数已达上限，请订阅升级规格，以便您运行更多的任务！',
  packages_business_setting_alarmsetting_qubangding: '去绑定',
  packages_business_setting_alarmsetting_jiancedaoninhai:
    '检测到您还未绑定邮箱，无法开启邮件通知。',
  packages_business_verification_form_zhengzaijiyuren:
    '正在基于任务生成校验条件',
  packages_business_agent_ip_tips_prefix:
    '请在防火墙中允许这些 TapData IP 访问数据库端口，并确保权限设置正确',
  packages_business_agent_ip_tips_suffix: '点击查看全托管Agent的IP地址信息',
  packages_business_demo_database_desc:
    'Demo 数据源，可快速创建数据源信息，无需准备数据库信息即可体验。',
  packages_business_use_ssl: '使用 SSL',
  packages_business_certificate_authority: 'CA 文件',
  packages_business_client_certificate: '客户端证书文件',
  packages_business_client_key: '客户端密钥文件',
  packages_business_client_key_password: '客户端密钥密码',
  packages_business_use_ssh: '使用 SSH 隧道',
  packages_business_ssh_host: '主机名',
  packages_business_ssh_port: '端口',
  packages_business_ssh_username: '用户名',
  packages_business_ssh_password: '密码',
  packages_business_connections_test_xiazaijindu: '下载进度',
  packages_business_connections_test_xiazaishibai: '下载失败',
  packages_business_relmig_import: 'MongoDB Relmig 导入',
  packages_business_relmig_import_desc: `这个功能旨在无缝导入 MongoDB 关系迁移器导出的 relmig 项目文件到 ${import.meta.env.VUE_APP_PAGE_TITLE}。在 relmig 文件被导入后， ${import.meta.env.VUE_APP_PAGE_TITLE} 将自动创建一个任务来执行源数据库的实时数据同步，并将其转换为 MongoDB 数据库中的 JSON 数据格式。`,
  packages_business_relmig_upload: '上传 relmig 文件',
  packages_business__relmig_import_connection_tip:
    '如果您还没有创建，请点击这里',
  packages_business__relmig_import_source_connection_placeholder:
    '请选择包含您在 relmig 项目中使用的源表的源连接',
  packages_business__relmig_import_target_connection_placeholder:
    '请选择您希望数据同步到的目标连接',
  packages_business_task_tag_placeholder:
    '为这个任务分配一个标签，以便您能够轻松找到它',
  packages_business_paid_connector: '付费数据源',
  packages_business_more_free_connector: '更多免费数据源',
  packages_business_request_connector_title: '试用 Alpha/Beta 数据源',
  packages_business_request_connector_pending: '审批中',
  packages_business_request_connector_pending_desc: '您已提交申请，请等待审批',
  packages_business_request_connector_alert:
    '👋 欢迎试用 {qcType} 版本的 {type} 数据源，填写表单后即可开始试用。\n💁 为了获得最佳体验，请提供准确的联系方式。我们将主动联系您，提供支持和帮助。',
  packages_business_request_connector_use_plan: '您计划使用此数据源的场景',
  packages_business_request_connector_use_plan_placeholder:
    '请填写您的使用场景',
  packages_business_request_connector_use_time: '预计使用时间',
  packages_business_request_connector_use_time_option1: '5天',
  packages_business_request_connector_use_time_option2: '半年',
  packages_business_request_connector_use_time_option3: '1年',
  packages_business_request_connector_success:
    '我们收到了您的请求，很快就会有人与您联系。',
  packages_business_view_more_apis: '查看更多API',
  packages_business_verification_hashTip: '暂不支持异构数据库',
  packages_business_heterogeneous_database: '异构数据库',
  packages_business_selected_rows: '已选 {val} 行',
  packages_business_download_analysis_report: '分析报告',
  packages_business_download_analysis_report_title: '任务分析报告生成中...',
  packages_business_download_analysis_report_desc:
    '报告生成大约需要 60s, 在下载后, 请发送给支持团队进行分析',
  packages_business_exporting_task: '正在导出任务',
  packages_business_exporting_run_history: '正在导出任务运行历史记录',
  packages_business_exporting_task_log: '正在导出任务日志',
  packages_business_exporting_metrics: '正在导出监控指标',
  packages_business_gen_engine_cpu_chart: '正在生成引擎 CPU 分析图',
  packages_business_gen_tm_cpu_chart: '正在生成管理端 CPU 分析图',
  packages_business_gen_engine_mem_chart: '正在生成引擎 内存分配 分析图',
  packages_business_gen_tm_mem_chart: '正在生成管理端 内存分配 分析图',
  packages_business_exporting_engine_thread: '正在导出引擎线程数据',
  packages_business_exporting_tm_thread: '正在导出管理端线程数据',
  packages_business_downloading_file: '正在下载文件',
  packages_business_long_wait: '请稍候',
  packages_business_correction: '修复',
  packages_business_data_correction: '一键修复',
  packages_business_confirmExecuteDataRepair: '确认执行数据修复吗？',
  packages_business_checkTaskInfo: '校验任务信息',
  packages_business_taskName: '任务名称',
  packages_business_taskStatus: '任务状态',
  packages_business_taskIncrementDelay: '任务增量延迟',
  packages_business_checkDetails: '校验详情',
  packages_business_diffThreshold: '差异阈值',
  packages_business_diffTotal: '差异总数',
  packages_business_diffExceededAlert:
    '差异总数已超过阈值，超过的部分将不予修复',
  packages_business_correctionDetails: '修复详情',
  packages_business_correctionDataVolume: '修复数据行数',
  packages_business_correctionTableCount: '修复表数量',
  packages_business_correctionTaskStarted: '修复任务已开始',
  packages_business_sourceOnly: '目标少数据',
  packages_business_targetOnly: '目标多数据',
  packages_business_no_data_correction: '没有可修复的数据',
  packages_business_recovering: '修复中',
  packages_business_business_information: '业务信息',
  packages_business_publish_api: '发布API',
  packages_business_field_description: '字段描述',
  packages_business_shared_cache_enforceShareCdc:
    '当共享挖掘不可用(缓存启动时)',
  packages_business_shared_cache_enforceShareCdc_true: '缓存直接报错停止',
  packages_business_not_support_validation: '{connection} 不支持{method}',
  packages_business_download_details: '下载详情',
  packages_business_solution: '解决方案',
  packages_business_error_details: '错误详情',
  packages_business_instance_info: '连接唯一标识',
  packages_business_warning_details: '警告详情',
  packages_business_custom_collate: '自定义排序',
  packages_business_please_select_field: '请选择字段',
  packages_business_please_input_charset: '请输入字符集',
  packages_business_auto_fill_join_fields: '智能填充关联条件',
  packages_business_auto_fill_join_tooltip_title:
    '开启后，系统将按以下优先级自动填充关联条件：',
  packages_business_auto_fill_join_tooltip_primary: '1. 优先使用主键字段',
  packages_business_auto_fill_join_tooltip_notnull:
    '2. 如无主键，则使用非空字段',
  packages_business_auto_fill_join_tooltip_all:
    '3. 如无非空字段，则使用全部字段',
  packages_business_nulls_first: 'NULL优先排序',
  packages_business_nulls_first_tip:
    '关联字段存在NULL值时，数据库默认将NULL排在最后，可能导致校验失败。开启此选项将NULL值排在前面，但可能无法使用数据库索引，增加数据库负载。',
  packages_business_ignoreTimePrecision: '忽略时间精度',
  packages_business_ignoreTimePrecision_tip: `<p>开启后，当源表与目标表时间精度不一致时，系统会统一为较低精度后再比对。</p>
<p>可选择：</p>
<ul class="pl-4">
  <li class="list-disc">四舍五入（如：1267 微秒 → 127 毫秒）</li>
  <li class="list-disc">截断（如：1267 微秒 → 126 毫秒）</li>
</ul>
<p>适用于高精度同步至低精度字段的场景。</p>
<p>若精度相同但存储精度不同（如 Sybase 为约 3.33ms），系统会忽略超出部分。</p>`,
  packages_business_checkTableThreadNum: '校验线程数量',
  packages_business_checkTableThreadNum_tip:
    '校验线程数量，在资源充足的情况下可进行调整，默认线程数为 10',
  packages_business_verification_empty_chooseJob: '暂无校验表配置，请选择任务',
  packages_business_verification_empty_add_table: '暂无校验表配置，请添加表',
  packages_business_verification_empty_auto_add_table:
    '暂无校验表配置，请自动添加表',
  packages_business_custom_mail_template: '自定义邮件模板',
  packages_business_alarm_type: '告警类型',
  packages_business_mail_title: '邮件主题',
  packages_business_mail_content: '邮件正文',
  packages_business_available_variables: '可用变量',
  packages_business_click_variable_name_insert_template:
    '点击变量名称插入到模板中',
  packages_business_ignoreTimePrecision_round: '四舍五入',
  packages_business_ignoreTimePrecision_truncate: '截断',
  packages_business_validation_task_type: '校验任务类型',
  packages_business_select_task_to_be_verified: '选择被校验的任务',
  packages_business_drag_file_here: '拖拽 .gz 文件至此或 <em>选择文件上传</em>',
  packages_business_request_speed_limit: '每秒请求限制',
  packages_business_request_speed_limit_tag: '每秒请求 ≤ {val}',
  packages_business_request_speed_limit_tip: '默认是 0（表示不限制）',
}
