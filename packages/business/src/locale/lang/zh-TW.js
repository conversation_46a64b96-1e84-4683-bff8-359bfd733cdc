export default {
  packages_business_status_wait_start: '待啟動',
  packages_business_status_starting: '啟動中',
  packages_business_status_renewing: '重置中',
  packages_business_status_renew_failed: '重置失敗',
  packages_business_milestone_list_status_waiting: '待執行',
  packages_business_milestone_list_status_running: '運行中',
  packages_business_milestone_list_status_paused: '已暫停',
  packages_business_milestone_list_status_progressing: '進行中',
  packages_business_milestone_list_status_cdc_progressing: '啟動中...',
  packages_business_milestone_list_status_cdc_finish: '增量數據同步中',
  packages_business_task_status_running: '已運行',
  packages_business_task_status_not_running: '未運行',
  packages_business_task_info_w: '周',
  packages_business_connection_form_data_source: '數據源',
  packages_business_connection_selector_desc1: '試用版暫不支持',
  packages_business_connection_selector_desc2: '更多數據源請使用正式版',
  packages_business_task_info_log_placeholder: '請輸入日誌內容',
  packages_business_task_info_no_more: '沒有更多了',
  packages_business_customer_logs_to_solutions: '查看解決方案',
  packages_business_customer_logs_to_link: '查看數據庫錯誤幫助頁面',
  packages_business_customer_logs_no_more_data: '沒有更多數據',
  packages_business_customer_logs_no_search_data: '無搜索結果',
  packages_business_customer_logs_copy_result: '已復製到剪貼板',
  packages_business_loading: '加載中',
  packages_business_schema_progress_status_error: '加載錯誤',
  packages_business_schema_progress_dialog_error_title: 'Schema加載出錯',
  packages_business_schema_progress_load_time: '加載時間：{0}',
  packages_business_dataFlow_batchSortOperation: '批量分類操作',
  packages_business_dataFlow_dataLoading: '數據努力加載中...',
  packages_business_message_upload_success: '上傳成功',
  packages_business_message_upload_fail: '上傳失敗',
  packages_business_message_upload_msg: '請上傳需要導入的任務檔案',
  packages_business_modules_dialog_import_title: '任務導入',
  packages_business_modules_dialog_condition: '條件',
  packages_business_modules_dialog_overwrite_data: '覆蓋已有數據',
  packages_business_modules_dialog_skip_data: '跳過已有數據',
  packages_business_modules_dialog_group: '分組',
  packages_business_modules_dialog_file: '文件',
  packages_business_modules_dialog_upload_files: '上傳文件',
  packages_business_connection_form_edit_connection: '編輯連接',
  packages_business_connection_form_data_source_type: '數據源類型',
  packages_business_connection_form_change: '更換',
  packages_business_connection_form_rename: '改名',
  packages_business_connection_form_database_owner_tip:
    '逗號分割的表達式列表，使用 * 代表任意長度任意字符',
  packages_business_connection_form_source_and_target_tip: `此數據連接在 ${
    import.meta.env.VUE_APP_PAGE_TITLE
  } 中能同時作為源和目標使用`,
  packages_business_connection_form_source_tip: `此數據連接在 ${import.meta.env.VUE_APP_PAGE_TITLE} 中只能作為源使用，不能作用為目標`,
  packages_business_connection_form_target_tip: `此數據連接在 ${import.meta.env.VUE_APP_PAGE_TITLE} 中只能作為目標使用，不能作用為源`,
  packages_business_connection_form_shared_mining: '使用共享挖掘',
  packages_business_connection_form_shared_mining_tip:
    '共享挖掘会挖掘增量日志，当有多个增量任务时不需要重复开启日志采集进程，能极大缓解源库资源的占用和浪费',
  packages_business_connection_form_access_node: 'Agent設置',
  packages_business_connection_form_automatic: '平台自動分配',
  packages_business_connection_form_manual: '用戶手動指定',
  packages_business_connection_form_group: '按標籤分配',
  packages_business_choose_agent: '選擇Agent',
  packages_business_choose_agent_group: '選擇Agent標籤',
  packages_business_priorityProcessId: '標籤內調度方式',
  packages_business_connection_form_access_node_tip:
    '自動情況下由平台分配節點進行連接訪問，手動情況下由用戶手動指定節點進行訪問',
  packages_business_connection_form_give_up: '放棄',
  packages_business_share_form_setting_table_name: '存儲MongoDB表名',
  packages_business_share_form_setting_log_time: '日誌保存時長',
  packages_business_message_saveFail: '保存失敗',
  packages_business_connection_rename: '改名',
  packages_business_dataForm_saveFail: '保存失敗',
  packages_business_dataForm_error_connectionNameExist: '連接名稱已存在',
  packages_business_connection_list_form_database_type: '數據庫類型',
  packages_business_connection_list_name: '連接名',
  packages_business_connection_list_status: '狀態',
  packages_business_connection_list_desc:
    '數據源包括數據庫、結構化文件、應用程序RESTful API、自定義接口等類型，必須先創建數據源才能創建遷移或同步任務。更多配置說明，請點擊',
  packages_business_connection_list_help_doc: '幫助文檔',
  packages_business_connection_dataBaseStatus: '狀態',
  packages_business_connection_deteleDatabaseMsg:
    '刪除連接 xxx 後，此連接將無法恢復',
  packages_business_connection_checkMsg:
    '此數據源被傳輸任務或API所佔用，無法刪除',
  packages_business_connection_copyFailedMsg:
    '複製失敗，原因：系統設置中 "連接設置 - 允許創建重複數據源" 被設置為 "false"',
  packages_business_text_open: '開啟',
  packages_business_connection_form_oracle_redoLog_parser: '裸日誌',
  packages_business_connection_preview_no_sure: '不確認',
  packages_business_connection_preview_master_partition: '僅寫入master分區',
  packages_business_connection_preview_isr_partition: '寫入所有ISR分區',
  packages_business_message_cancel: '取 消',
  packages_business_message_confirm: '確定',
  packages_business_connection_reloadTittle: '重新加載 schema',
  packages_business_connection_reloadMsg:
    '如果此庫的schema過多，可能耗時較長，確定要刷新數據源的schema ',
  packages_business_dataForm_primaryTest: '正在啟動連接檢測服務，請稍等 ',
  packages_business_dataForm_testing: '正在測試中，請稍等...',
  packages_business_dataForm_test_testResultFail: '連接測試失敗',
  packages_business_dataForm_test_testResultSuccess: '連接測試成功',
  packages_business_dataForm_test_success: '測試通過',
  packages_business_dataForm_test_fail: '測試未通過',
  packages_business_dataForm_test_testing: '未測試',
  packages_business_dataForm_test_items: '測試事項',
  packages_business_dataForm_test_result: '結果',
  packages_business_dataForm_test_information: '說明',
  packages_business_dataForm_test_unTest: '待測試... ',
  packages_business_dataForm_test_error: '測試服務請求超時，請關閉重試',
  packages_business_dataForm_test_retryBtn: '重試',
  packages_business_dataForm_test_retryTest: '連接測試服務啟動失敗，請點擊重試',
  packages_business_message_update_success: '修改成功',
  packages_business_task_preview_subtasks: '子任務',
  packages_business_task_monitor_sync_type: '同步類型',
  packages_business_task_monitor_run_connection: '連接',
  packages_business_task_monitor_history_run_record: '歷史運行記錄',
  packages_business_task_details_sub_task: '子任務',
  packages_business_dataFlow_importantReminder: '重要提醒',
  packages_business_dataFlow_modifyEditText: '編輯任務如果修改了',
  packages_business_dataFlow_nodeLayoutProcess: '節點排版流程',
  packages_business_dataFlow_nodeAttributes: '節點屬性',
  packages_business_dataFlow_matchingRelationship: '匹配關係',
  packages_business_dataFlow_afterSubmission: '提交後必須',
  packages_business_dataFlow_runNomally: '才能正常運行',
  packages_business_dataFlow_editLayerTip:
    ' 否則可能導致異常錯誤，請問您要繼續編輯嗎?',
  packages_business_dataFlow_continueEditing: '繼續編輯',
  packages_business_task_monitor_progress: '任務進度',
  packages_business_task_monitor_run_log: '運行日誌',
  packages_business_task_monitor_mining_task: '挖掘任務',
  packages_business_dataFlow_inputOutput: '輸入輸出統計',
  packages_business_dataFlow_dataScreening: '事件統計',
  packages_business_dataFlow_throughputpop:
    '輸入輸出統計: 平均每秒源端數據採集的速度以及目標端寫入的速度，數值越大越好',
  packages_business_task_monitor_full_completion_time: '預計全量完成還需時間',
  packages_business_task_monitor_total_insert: '總插入',
  packages_business_task_monitor_total_update: '總更新',
  packages_business_task_monitor_total_delete: '總刪除',
  packages_business_task_info_start_time: '開始時間',
  packages_business_task_info_node: '節點',
  packages_business_task_info_frequency: '頻率',
  packages_business_task_info_select_node: '請選擇節點',
  packages_business_task_info_select_period: '請選擇週期',
  packages_business_task_info_select_frequency: '請選擇頻率',
  packages_business_task_info_fifteen_min: '最近十五分鐘',
  packages_business_task_info_five_seconds: '5秒',
  packages_business_task_info_one_min: '1分鐘',
  packages_business_task_info_full_progress: '全量進度',
  packages_business_task_info_calculating: '計算中',
  packages_business_task_info_increment_time_point: '增量所處時間點',
  packages_business_migrate_no_progress_statistics_yet: '暫無進度統計信息',
  packages_business_migrate_no_latency_statistics_yet: '暫無延遲統計信息',
  packages_business_task_monitor_full_sync: '全量同步概覽',
  packages_business_task_info_table_number: '計劃全量同步數據量',
  packages_business_task_info_completed: '已完成全量同步數據量',
  packages_business_task_info_fully_completed: '全量已完成',
  packages_business_task_info_overView_status: '計算中',
  packages_business_button_clear: '清除',
  packages_business_button_rollback: '回溯',
  packages_business_task_monitor_status: '狀態',
  packages_business_task_info_synced: '已同步',
  packages_business_task_info_task_init: '任務初始化',
  packages_business_task_info_task_structure: '結構遷移',
  packages_business_task_info_task_cdc: '增量同步',
  packages_business_task_info_srcName: '源數據源名稱',
  packages_business_task_info_srcTableName: '源表名稱',
  packages_business_task_info_tgtName: '目標數據源名稱',
  packages_business_task_info_tgtTableName: '目標表名稱',
  packages_business_task_info_cdc_delay: '延遲(ms)',
  packages_business_task_info_cdc_time: '增量所處時間點',
  packages_business_task_info_source_table: '源數據表',
  packages_business_task_info_source_database: '源數據庫',
  packages_business_task_info_data_row: '數據量（行）',
  packages_business_task_info_target_table: '目標數據表',
  packages_business_task_info_amount_sync_data: '已完成同步數據量（行）',
  packages_business_task_info_schedule: '進度',
  packages_business_task_info_table_name: '表名稱',
  packages_business_task_info_overView_error_msg: '計算出錯',
  packages_business_share_task_table_name: '挖掘任務名稱',
  packages_business_share_task_table_time: '挖掘所處時間',
  packages_business_share_task_table_status: '挖掘所處狀態',
  packages_business_connection_list_schema_load_progress: 'Schema加載進度',
  packages_business_connection_list_test_failed: '測試連接失敗',
  packages_business_task_info_connection_test: '測試',
  packages_business_task_start_task: '啟動任務',
  packages_business_task_stop_task: '停止任務',
  packages_business_task_info_forced_stop_task: '強制停止任務',
  packages_business_task_info_running_time: '運行時間',
  packages_business_task_info_operator: '操作者',
  packages_business_task_info_operator_content: '操作內容',
  packages_business_task_info_data_screening: '事件統計',
  packages_business_task_info_input_output: '輸入輸出統計',
  packages_business_task_info_throughputpop:
    '輸入輸出統計: 平均每秒源端數據採集的速度以及目標端寫入的速度，數值越大越好',
  packages_business_task_monitor_time: '時間',
  packages_business_task_monitor_mission_milestone: '任務里程碑',
  packages_business_task_monitor_no_milestone_data:
    '此任務尚未啟動或已被重置，暫無運行里程碑數據',
  packages_business_task_info_milestone: '里程碑',
  packages_business_milestone_btn_check_error: '查看錯誤原因',
  packages_business_task_monitor_mining_task_name: '挖掘任務名稱',
  packages_business_task_monitor_mining_task_point_time: '挖掘所處時間點',
  packages_business_task_monitor_mining_task_status: '挖掘狀態',
  packages_business_button_bulk_import: '導入',
  packages_business_message_save_fail: '保存失敗',
  packages_business_task_list_transform_done: '推演完成',
  packages_business_task_list_transform_error: '推演失敗',
  packages_business_task_list_edit: '編輯',
  packages_business_task_list_export: '導出',
  packages_business_task_list_sync_type: '同步類型',
  packages_business_task_list_status_all: '全部狀態',
  packages_business_task_list_button_monitor: '監控',
  packages_business_task_preview_title: '數據庫遷移詳情',
  packages_business_task_preview_createUser: '創建人',
  packages_business_task_preview_sync_type: '任務同步',
  packages_business_task_preview_type: '任務同步',
  packages_business_task_preview_id: '任務ID',
  packages_business_task_preview_createAt: '創建時間',
  packages_business_task_preview_createTime: '創建時間',
  packages_business_task_preview_startTime: '啟動時間',
  packages_business_task_preview_initStartTime: '全量開始時間',
  packages_business_task_preview_cdcStartTime: '增量開始時間',
  packages_business_task_preview_taskFinishTime: '任務完成時間',
  packages_business_task_preview_taskLastHour: '任務總時長',
  packages_business_task_preview_eventTime: '增量所處時間點',
  packages_business_task_preview_cdcDelayTime: '增量最大滯後時間',
  packages_business_task_preview_failCount: '失敗總次數',
  packages_business_message_resetOk: '重置成功',
  packages_business_dataFlow_multiError_notFound: '此任務不存在',
  packages_business_dataFlow_multiError_statusError: '任務狀態不允許這種操作',
  packages_business_dataFlow_multiError_otherError: '操作失敗，請重試',
  packages_business_dataFlow_batchDelete: '批量刪除',
  packages_business_dataFlow_batchRest: '批量重置',
  packages_business_dataFlow_bulkExport: '批量導出',
  packages_business_dataFlow_bulkScheuled: '批量啟動',
  packages_business_dataFlow_bulkStopping: '批量停止',
  packages_business_dataFlow_taskBulkOperation: '任務操作',
  packages_business_dataFlow_addTag: '添加標籤',
  packages_business_dataVerify_dataVerify: '數據校驗',
  packages_business_dataFlow_selectAll: '全選',
  packages_business_dataFlow_skipError_title: '跳過錯誤設置',
  packages_business_dataFlow_skipError_tip:
    '任務上次停止時發生了以下數據相關的錯誤，請確認這些錯誤已經被處理。如果希望跳過這些錯誤，請勾選相應的錯誤項並點擊“跳過錯誤，啟動任務” 。 ',
  packages_business_dataFlow_skipError_attention:
    '注意：若導致錯誤的數​​據未被處理，跳過錯誤可能導致這條數據被丟棄。 ',
  packages_business_dataFlow_skipError_startJob: '跳過錯誤，啟動任務',
  packages_business_dataFlow_skipError_taskName: '任務名',
  packages_business_dataFlow_skipError_errorTotal: '共XX條，已選擇',
  packages_business_dataFlow_skipError_strip: '條',
  packages_business_page_title_task_stat: '任務統計',
  packages_business_task_info_subtasks_name: '子任務名稱',
  packages_business_task_info_subtasks_status: '狀態',
  packages_business_dataFlow_view: '查看',
  packages_business_dataFlow_copy: '複製',
  packages_business_dataFlow_button_reset: '重置',
  packages_business_connection_type_source: '源頭',
  packages_business_connection_type_target: '目標',
  packages_business_connection_type_source_and_target: '源頭和目標',
  packages_business_task_preview_status_error: '錯誤',
  packages_business_task_preview_status_edit: '編輯中',
  packages_business_task_preview_status_wait_run: '啟動中',
  packages_business_task_preview_status_complete: '已完成',
  packages_business_task_preview_status_running: '運行中',
  packages_business_task_preview_status_stop: '已停止',
  packages_business_task_preview_status_stopping: '停止中',
  packages_business_task_preview_status_preparing: '准备中',
  packages_business_task_preview_status_scheduling: '啟動中',
  packages_business_task_preview_status_schedule_failed: '調度失敗',
  packages_business_task_preview_status_ready: '待啟動',
  packages_business_task_info_status_waiting: '待運行',
  packages_business_task_info_status_running: '同步中',
  packages_business_task_info_status_done: '已完成',
  packages_business_task_info_status_paused: '已暫停',
  packages_business_logs_detailed_sousuowushuju: '搜索無數據',
  packages_business_logs_index_xiangxi: '詳細',
  packages_business_logs_index_putong: '普通',
  packages_business_shared_task_yijingzhiweie: '已經置為[error]',
  packages_business_shared_task_weishibiederen: '未識別的任務狀態：{val1}',
  packages_business_connections_databaseform_cicaozuohuidiu:
    '此操作會丟失當前正在創建的連接',
  packages_business_connections_databaseform_mingchengguizezhong:
    '名稱規則：中英開頭，1～100個字符，可包含中英文、數字、中劃線、下劃線、空格',
  packages_business_connections_databaseform_zhongyingkaitouge:
    '中英開頭，1～100個字符，可包含中英文、數字、中劃線、下劃線、空格',
  packages_business_connections_list_renwuzongshu: '任務總數: ',
  packages_business_connections_list_gailianjieyibei:
    '該連接已被以下任務調用，請刪除任務或修改配置後重試',
  packages_business_connections_preview_schem: 'schema加載完成',
  packages_business_etl_details_caozuoshibaiqing: '操作失敗，請重試',
  packages_business_etl_details_shifouzhongzhigai: '是否重置該任務？ ',
  packages_business_etl_details_zhongzhirenwux:
    '重置任務 xxx 將清除任務同步進度，任務將重新執行',
  packages_business_etl_details_qiangzhitingzhiren:
    '強制停止任務 xxx 將立即中斷數據傳輸強制任務快速停止，並重置該任務',
  packages_business_etl_details_shifouqiangzhiting: '是否強制停止該任務？ ',
  packages_business_etl_details_zantingrenwux:
    '暫停任務 xxx 後，任務中未完成全量同步的表再次啟動時，會重新執行全量同步',
  packages_business_etl_details_shifouzantinggai: '是否暫停該任務？ ',
  packages_business_etl_details_shanchurenwux:
    '刪除任務 xxx 後，此任務將無法恢復',
  packages_business_etl_details_shifoushanchugai: '是否刪除該任務？ ',
  packages_business_etl_details_renwuXxx:
    '任務XXX中含有聚合處理節點，任務停止後再次啟動，任務會先進行重置，確定停止？ ',
  packages_business_etl_details_chushihualeixing:
    '初始化類型的任務暫停後如果再次啟動，任務會從頭開始同步，確定暫停?',
  packages_business_etl_details_miaoshuneirong: '描述內容',
  packages_business_statistics_index_tongburenwu: '同步任務',
  packages_business_statistics_index_qianyirenwu: '遷移任務',
  packages_business_statistics_schedule_cike: '此刻',
  packages_business_statistics_schedule_shujukushiqu: '數據庫時區',
  packages_business_statistics_schedule_yonghuliulanqi: '用戶瀏覽器時區',
  packages_business_statistics_schedule_shijian: '時間：',
  packages_business_statistics_schedule_leixing: '類型：',
  packages_business_dataFlow_delete_confirm_title: '是否刪除該任務？',
  packages_business_dataFlow_delete_confirm_message:
    '刪除任務 xxx 後，此任務將無法恢復',
  packages_business_dataFlow_bulk_delete_confirm_title: '是否批量刪除任務？',
  packages_business_dataFlow_bulk_delete_confirm_message:
    '批量刪除任務後，任務將無法恢復',
  packages_business_dataFlow_stop_confirm_title: '是否暫停該任務？',
  packages_business_dataFlow_stop_confirm_message:
    '暫停任務 xxx 後，任務中未完成全量同步的表再次啟動時，會重新執行全量同步',
  packages_business_dataFlow_bulk_stop_confirm_title: '是否批量暫停任務？',
  packages_business_dataFlow_bulk_stop_confirm_message:
    '批量暫停任務後，任務中未完成全量同步的表再次啟動時，會重新執行全量同步',
  packages_business_dataFlow_force_stop_confirm_title: '是否強制停止該任務？',
  packages_business_dataFlow_agent_force_stop_confirm_message:
    '強制停止任務xxx，由於Agent已離線，我們只會重置該任務的狀態，但是無法停止此任務的運行，請您確保已在本地手動停止或删除了該Agent，或等待該Agent連接上後再停止該任務。',
  packages_business_dataFlow_force_stop_confirm_message:
    '強制停止任務 xxx 將立即中斷數據傳輸強制任務快速停止，並重置該任務',
  packages_business_dataFlow_bulk_force_stop_confirm_title:
    '是否批量強制停止任務？',
  packages_business_dataFlow_bulk_force_stop_confirm_message:
    '批量強制停止任務將立即中斷數據傳輸強制任務快速停止，並重置該任務',
  packages_business_dataFlow_initialize_confirm_title: '是否重置該任務？',
  packages_business_dataFlow_initialize_confirm_message:
    '重置任務 xxx 將清除任務同步進度，任務將重新執行',
  packages_business_dataFlow_bulk_initialize_confirm_title:
    '是否批量重置任務？',
  packages_business_dataFlow_bulk_initialize_confirm_message:
    '批量重置任務將清除任務同步進度，任務將重新執行',
  packages_business_connections_databaseform_zidingyi: '自定義',
  packages_business_connections_databaseform_duixiangshouji: '對象收集',
  packages_business_verification_details_yichangshuju: '異常數據',
  packages_business_verification_details_mubiaobiaoming: '目標表名',
  packages_business_verification_details_yuanbiaoming: '源表名',
  packages_business_verification_details_gongxijiaoyanjie:
    '恭喜~校驗結果源表與目標表內容完全一致，沒有錯誤記錄',
  packages_business_verification_details_mubiaobiaoziduan: '目標表字段：值',
  packages_business_verification_details_yuanbiaoziduanzhi: '源表字段：值',
  packages_business_verification_details_xianshiwanzhengzi: '顯示完整字段',
  packages_business_verification_details_jinxianshichayi: '僅顯示差異字段',
  packages_business_verification_details_yichangshujuhang: '異常數據（行）：',
  packages_business_verification_details_mubiaobiao: '目標表：',
  packages_business_verification_details_yuanbiao: '源表：',
  packages_business_verification_details_jiaoyanjieguo: '校驗結果',
  packages_business_verification_details_jiaoyanzhong: '校驗中',
  packages_business_verification_details_jiaoyan: '校驗',
  packages_business_verification_details_qingshurubiaoming: '請輸入表名…',
  packages_business_shared_const_gaojingzhong: '告警中',
  packages_business_shared_const_yihuifu: '已恢復',
  packages_business_components_alert_yiguanbi: '已關閉',
  packages_business_components_alert_huifu: '恢復',
  packages_business_shared_const_yiban: '一般',
  packages_business_shared_const_jinggao: '警告',
  packages_business_shared_const_yanzhong: '嚴重',
  packages_business_shared_const_jinji: '緊急',
  packages_business_external_storage: '外存配置',
  packages_business_relation_details_chakanrenwu: '查看任務',
  packages_business_relation_details_shiyonggaiguanlian:
    '使用該{val}的任務清單',
  packages_business_relation_list_jiaoyanrenwu: '校驗任務',
  packages_business_relation_list_huancunrenwu: '緩存任務',
  packages_business_relation_list_qingshururenwu: '請輸入任務名稱...',
  packages_business_relation_details_huancun: '緩存',
  packages_business_relation_details_wajue: '挖掘',
  packages_business_relation_details_renwu: '任務',
  packages_business_agent_select_placeholder: '請選擇agent',
  packages_business_agent_select_not_found: '該agent已不存在，請選擇其他agent',
  packages_business_agent_select_not_found_for_rocksdb:
    '選用RocksDB作為共享挖掘外存時，需要手動指定一個Agent',
  packages_business_components_connectiontypeselectorsort_wodeshujuyuan:
    '我的數據源',
  packages_business_components_connectiontypeselectorsort_jiaoyouTap: `交由 ${import.meta.env.VUE_APP_PAGE_TITLE} 進行全面的質量測試，以保證插件的穩定性和質量`,
  packages_business_components_connectiontypeselectorsort_zhuyizhelishi:
    '注意：這裡是您自己上傳的數據源插件，如果要用於生產任務，請在GitHub上提交源代碼',
  packages_business_components_connectiontypeselectorsort_zhuyiBet: `注意：Beta 數據源尚未通過 ${import.meta.env.VUE_APP_PAGE_TITLE} 的認證測試流程，${import.meta.env.VUE_APP_PAGE_TITLE}暫不保證這些數據源的穩定運行`,
  packages_business_components_connectiontypeselectorsort_shiyongbanzanbu:
    '敬請期待以下數據源開放',
  packages_business_components_connectiontypeselectorsort_betashu: 'Beta数据源',
  packages_business_components_connectiontypeselectorsort_renzhengshujuyuan:
    'GA数据源',
  packages_business_connections_list_lianjiefenlei: '連接分類',
  packages_business_task_migratelist_renwufenlei: '任務分類',
  packages_business_components_connectiontypeselectorsort_jijiangshangxian:
    'Alpha数据源',
  packages_business_task_list_renwubuzhichi: '任務不支持該操作',
  packages_business_connections_databaseform_keyicongbaohan:
    '可以從包含表規則匹配到的表中將指定的表排除',
  packages_business_connections_databaseform_paichubiao: '排除表',
  packages_business_connections_databaseform_baohanbiao: '包含表',
  packages_business_connections_list_wenjianleixingde:
    '文件類型的連接暫不支持加載Schema',
  // 数据校验
  packages_business_verification_task_name: '校驗任務名',
  packages_business_verification_type: '校驗類型',
  packages_business_verification_check_frequency: '校驗頻率',
  packages_business_verification_single_repeating_verify: '單次/重複校驗',
  packages_business_verification_is_enabled: '是否啟用',
  packages_business_verification_single: '單次校驗',
  packages_business_verification_repeating: '重複校驗',
  packages_business_verification_row_verify: '快速count校驗',
  packages_business_verification_content_verify: '表全字段值校驗',
  packages_business_verification_joint_verify: '關聯字段值校驗',
  packages_business_verification_hash_verify: '全表 hash 校驗',
  packages_business_verification_job_enable: '已啟用',
  packages_business_verification_job_disable: '已禁止',
  packages_business_verification_check_same: '校驗一致',
  packages_business_verification_count_difference: 'Count不一致',
  packages_business_verification_content_difference: '內容差異',
  packages_business_verification_executeVerifyTip: '执行',
  packages_business_verification_addVerifyTip: '新建校驗任務',
  packages_business_verification_historyTip: '历史',
  packages_business_verification_configurationTip: '配置',
  packages_business_verification_details_title: '任務校驗詳情',
  packages_business_verification_history_title: '任務校驗歷史',
  packages_business_verification_diff_history_title: '差異校驗歷史',
  packages_business_verification_diff_details_title: '差異校驗詳情',
  packages_business_verification_result_title: '校驗結果',
  packages_business_verification_button_diff_verify: '差異校驗',
  packages_business_verification_button_diff_verify_running: '校驗中',
  packages_business_verification_button_diff_verify_tips:
    '對本次全量校驗的差異數據結果進行再次校驗，行數差異暫不支持差異校驗',
  packages_business_verification_last_start_time: '最後校驗時間',
  packages_business_verification_button_diff_task_history: '校驗歷史',
  packages_business_verification_message_old_data_not_support:
    '舊數據暫不支持二次校驗',
  packages_business_verification_message_out_of_limit:
    '您的差異數據量已超出任務支持的最大錯誤數據保存條數，暫時無法進行二次校驗',
  packages_business_verification_result_count_more: '目標count多: {0}',
  packages_business_verification_result_count_less: '目標count少: {0}',
  packages_business_verification_result_content_diff: '表數據差: {0}',
  packages_business_verification_result_count_inconsistent: '不一致',
  packages_business_verification_result_count_consistent: '一致',
  packages_business_verification_result_field_name: '字段名',
  packages_business_verification_result_source_info: '源信息',
  packages_business_verification_result_target_info: '目標信息',
  packages_business_verification_create_window_duration: '窗口時長',
  packages_business_verification_form_source_filter: '源表數據過濾',
  packages_business_verification_form_target_filter: '目標表數據過濾',
  packages_business_verification_checking: '校驗中...',
  packages_business_verification_message_error_joint_table_not_set:
    '請添加校驗條件',
  packages_business_verification_message_error_joint_table_target_or_source_not_set:
    '校驗條件{val}中源表或目標表未選擇',
  packages_business_verification_message_error_joint_table_target_or_source_filter_not_set:
    '校驗條件{val}中源表或目標表数据过滤未選擇',
  packages_business_verification_message_error_joint_table_field_not_match:
    '校驗條件{val}中源表與目標表的索引欄位個數不相等',
  packages_business_verification_message_error_script_no_enter:
    '開啟高級校驗後，JS校驗邏輯不能為空',
  packages_business_verification_message_confirm_delete_script:
    '確定要刪除自定義JS校驗邏輯嗎',
  packages_business_verification_message_confirm_back:
    '此操作會丟失當前正在創建（編輯）的校驗任務',
  packages_business_verification_message_title_confirm_back:
    '是否放棄創建（編輯）校驗任務？',
  packages_business_taskprogress_plan_sync_table_num: '計劃同步表數量',
  packages_business_taskprogress_completed_sync_table_num: '已完成同步表數量',
  packages_business_taskprogress_plan_sync_data: '計劃同步數據量（行）',
  packages_business_taskprogress_completed_sync_data: '已完成同步數據量（行）',
  packages_business_taskprogress_current_sync: '各庫當前同步情況',
  packages_business_taskprogress_full_sync_progress: '全量同步進度',
  packages_business_verification_verifyDetail: '校驗詳情',
  packages_business_verification_sourceTable: '源表',
  packages_business_verification_targetTable: '目標表',
  packages_business_verification_sourceRows: '源表校驗行數',
  packages_business_verification_rowConsistent: '行數差異',
  packages_business_verification_contConsistent: '內容差異',
  packages_business_verification_verifyHistory: '校驗歷史',
  packages_business_verification_tableDetail: '表明細',
  packages_business_verification_configuration: '查看配置',
  packages_business_verification_verifyName: '校驗任務',
  packages_business_verification_sourceTotalRows: '本次校驗行數',
  packages_business_verification_targetTotalRows: '目標行數',
  packages_business_verification_verifyStatus: '校驗狀態',
  packages_business_verification_verifystatus: '校驗狀態',
  packages_business_verification_completeTime: '完成時間',
  packages_business_verification_verifyTime: '校驗時間',
  packages_business_verification_rowVerify: '快速count校驗',
  packages_business_verification_contentVerify: '表全字段值校驗',
  packages_business_verification_jointVerify: '關聯字段值校驗',
  packages_business_verification_singleVerify: '單次校驗',
  packages_business_verification_repeatingVerify: '重複校驗',
  packages_business_verification_inconsistent: '不一致',
  packages_business_verification_consistent: '一致',
  packages_business_verification_toBeVerified: '待校驗',
  packages_business_verification_verifying: '校驗中',
  packages_business_verification_verifyFinished: '校驗完成',
  packages_business_verification_verifyJobExpired: '校驗任務結束',
  packages_business_verification_executeVerifyInstantly: '執行校驗',
  packages_business_verification_deleteVerifyJob: '刪除',
  packages_business_verification_verifySetting: '校驗設置',
  packages_business_verification_batchVerify: '批量校驗',
  packages_business_verification_verifyType: '校驗類型',
  packages_business_verification_verifytype: '校驗類型',
  packages_business_verification_singleRepeatingVerify: '單次/重複校驗',
  packages_business_verification_rowAndContConsistent: '行數和內容差異',
  packages_business_verification_sourceFieldName: '源表字段名',
  packages_business_verification_targetFieldName: '目標字段名',
  packages_business_verification_Value: '值',
  packages_business_verification_inconsistentType: '差異類型',
  packages_business_verification_chooseJob: '選擇任務',
  packages_business_verification_frequency: '校驗頻次',
  packages_business_verification_startTime: '開始時間',
  packages_business_verification_LastTime: '結束時間',
  packages_business_verification_verifyInterval: '校驗間隔',
  packages_business_verification_inconsistentCount: '錯誤數據保存條數',
  packages_business_verification_table: '待校驗表',
  packages_business_verification_addTable: '添加表',
  packages_business_verification_automaticallyAdd: '自動添加',
  packages_business_verification_enable: '已啟用',
  packages_business_verification_disable: '已禁止',
  packages_business_verification_isEnabled: '是否啟用',
  packages_business_verification_newVerify: '新建校驗',
  packages_business_verification_edit: '編輯校驗',
  packages_business_verification_clickVerified: '點下方按鈕添加校驗表',
  packages_business_verification_ChoosePKField: '請選索引或主鍵字段',
  packages_business_verification_indexField: '關聯字段',
  packages_business_verification_BasicSettings: '基本設置',
  packages_business_verification_verifyCondition: '校驗條件',
  packages_business_verification_advanceVerify: '高级校验',
  packages_business_verification_JSVerifyLogic: 'JS校验逻辑',
  packages_business_verification_addJS: '添加逻辑',
  packages_business_verification_clear: '清空',
  packages_business_verification_returnMsg: '返回的message',
  packages_business_verification_returnedData: '返回的data',
  packages_business_verification_sourceTableData: '源表數據',
  packages_business_verification_fastCountTip:
    '快速count僅對源表和目標表的行數進行count校驗，速度極快，但是不會展示差異的具體字段內容。',
  packages_business_verification_contentVerifyTip:
    '表全字段值校驗會對源表和目標表的全部字段進行逐行校驗，能查出所有字段的差異，但是速度慢。此操作同時會對源和目標庫發起查詢，可能會對數據庫造成讀取壓力。',
  packages_business_verification_jointFieldTip:
    '關聯字段值校驗只對源表和目標表的關聯字段的值進行比對校驗，速度快於全表字段值校驗模式。此操作同時會對源和目標庫發起查詢，可能會對數據庫造成讀取壓力。',
  packages_business_verification_waiting: '待校驗',
  packages_business_verification_scheduling: '校驗啟動中',
  packages_business_verification_error: '校驗失敗',
  packages_business_verification_done: '校驗結束',
  packages_business_verification_running: '校驗中',
  packages_business_verification_success:
    '校驗結果顯示源表與目標表內容完全一致。',
  packages_business_verification_verifyProgress: '校验进度',
  packages_business_verification_tasksTime: '請選擇起止時間',
  packages_business_verification_tasksDataFlow: '請選擇任務',
  packages_business_verification_tasksJobName: '請輸入校驗任務名稱',
  packages_business_verification_tasksVerifyCondition: '請添加校驗條件',
  packages_business_verification_tasksVerifyInterval: '請輸入校驗間隔',
  packages_business_verification_lackSource: '校驗條件中源表或目標表未選擇',
  packages_business_verification_lackIndex:
    '校驗條件{val}中源表或目標表的索引字段未選擇',
  packages_business_verification_tasksAmount:
    '校驗條件中源表與目標表的索引字段個數不相等',
  packages_business_verification_uniqueField: '唯一字段差異',
  packages_business_verification_otherField: '其他字段差異',
  packages_business_verification_back: '返回',
  packages_business_verification_startVerify: '正在執行校驗',
  packages_business_verification_deleteMessage:
    '刪除校驗任務將無法恢復, 確定刪除',
  packages_business_verification_checkStatusPre: '此任務處於 ',
  packages_business_verification_checkStatusSuffix: '狀態，無法配置校驗設置',
  packages_business_verification_backConfirmMessage:
    '此操作會丟失當前正在創建（編輯）的校驗任務',
  packages_business_verification_backConfirmTitle:
    '是否放棄創建（編輯）校驗任務？',
  packages_business_verification_history_source_total_rows: '源總行數',
  packages_business_verification_source_total_rows: '源總行數',
  packages_business_verification_form_label_error_save_count: '錯誤保存條數',
  packages_business_verification_button_auto_add_table: '自動添加表',
  packages_business_components_conditionbox_suoxuanrenwuque:
    '所選任務缺少節點連線信息',
  packages_business_components_conditionbox_cunzaichulijiedian_wufazidong:
    '存在處理節點，無法自動添加表',
  packages_business_components_conditionbox_shifouqingkongsuo:
    '是否清空所有條件',
  packages_business_components_conditionbox_mubiaobiao: ' 目標表',
  packages_business_components_conditionbox_laiyuanbiao: '來源表',
  packages_business_components_conditionbox_daijiaoyanlianjie: '待校驗連接',
  packages_business_components_conditionbox_jianyantiaojian: '檢驗條件',
  packages_business_components_conditionbox_zhankaibianji: '展開編輯',
  packages_business_components_fieldbox_tianjiahang: '添加行',
  packages_business_components_fieldbox_ziduan: '字段',
  packages_business_components_fieldbox_qingshuruziduan: '請輸入字段名',
  packages_business_components_fieldbox_quanziduan: '全字段',
  packages_business_components_fieldbox_daijiaoyanmoxing: '待校驗模型',
  packages_business_verification_details_dongtaijiaoyan: '動態校驗',
  packages_business_verification_details_zhankai: '展開',
  packages_business_verification_details_shouqi: '收起',
  packages_business_verification_form_diinde:
    '校驗條件{val1}，待校驗模型不能為空',
  packages_business_verification_form_zhaobudaojiedian:
    '找不到節點對應的表信息',
  packages_business_verification_form_qingshurukaishi: '請輸入開始時間',
  packages_business_verification_form_jiaoyanjieshushi: '校驗結束時間',
  packages_business_verification_form_jiaoyankaishishi: '校驗開始時間',
  packages_business_verification_form_zhishuchulaiyuan:
    '只輸出來源表不一致的數據',
  packages_business_verification_form_shuchusuoyoubu: '輸出所有不一致的數據',
  packages_business_verification_form_jieguoshuchu: '結果輸出',
  packages_business_verification_form_zhidingrenyibiao: '指定任意表的校驗任務',
  packages_business_verification_form_weitedingdeP:
    '為特定的PIPELINE創建的校驗任務',
  packages_business_verification_form_jiaoyanrenwumo: '校驗任務模式',
  packages_business_task_status_agent_tooltip_time:
    '距上次狀態上報時間已經{time}',
  packages_business_task_status_agent_tooltip_agent: '任務所在的引擎為',
  packages_business_task_status_retrying_tooltip:
    '當前任務正在重試中，重試開始時間：{val}',
  packages_business_select_placeholder: '請添加或選擇',
  packages_business_verification_form_youjiantongzhi: '郵件通知',
  packages_business_verification_form_xitongtongzhi: '系統通知',
  packages_business_verification_form_jiaoyanjieguobu: '校驗結果不一致告警',
  packages_business_verification_form_jianyanrenwuyun: '檢驗任務運行出錯警告',
  packages_business_verification_form_jiaoyangaojing: '校驗告警',
  packages_business_verification_form_zanbuzhichi_doris: '暫不支持Doris。 ',
  packages_business_verification_form_task_alarm: '校驗任務告警',
  packages_business_verification_form_task_alarm_when_error:
    '當校驗任務出錯時進行告警',
  packages_business_verification_form_task_alarm_when_diff_result_over_count1:
    '當count校驗結果的差異行數大於',
  packages_business_verification_form_task_alarm_when_diff_result_over_count2:
    '時進行告警',
  packages_business_verification_form_task_alarm_when_result_table_over_count1:
    '當值校驗結果的表數據差大於',
  packages_business_task_status_error_tip:
    '任務刪除成功，以下幾個 PostgreSQL 連接的信息清除失敗，需要您使用以下方式手動清除',
  packages_business_task_status_next_run_time: '下次運行時間：{val}',
  packages_business_relation_details_rizhiwajueshi: '日誌挖掘時間',
  packages_business_relation_details_wajuemingcheng: '挖掘名稱',
  packages_business_relation_details_wajuexinxi: '挖掘信息',
  packages_business_connections_databaseform_shujuyuanzhongmo:
    '當數據源中模型數量小於1萬時，會按照每小時一次進行模型刷新；當數據源中模型數量大於1萬時，會每天按照指定的時間進行模型刷新。',
  packages_business_connections_databaseform_moxingjiazaipin: '模型加載時間',
  packages_business_task_list_lianjieming: '連接名: ',
  packages_business_task_list_dierbushanchu: '// 第二步 刪除 slot_name',
  packages_business_task_list_diyibuchaxun: '//第一步 查詢 slot_name',
  packages_business_notify_webchat_notification: '微信通知',
  packages_business_notify_sms_notification: '簡訊通知',
  packages_business_notify_email_notification: '郵件通知',
  packages_business_notify_alarm_title: 'Agent告警设置',
  packages_business_notify_system_notice: '系统通知',
  packages_business_setting_alarm_notification_notify_noticeInterval:
    '发送间隔',
  packages_business_setting_notification_alarm_notification_gaojingtongzhi:
    '告警通知',
  packages_business_setting_alarmnotification_gaojingzhibiao: '告警指標',
  packages_business_setting_alarmnotification_dangjiediandeping:
    '當節點的平均處理耗時超過閥值時',
  packages_business_setting_alarmnotification_dangshujuyuanjie:
    '當數據源節點的平均處理耗時超過閥值時',
  packages_business_setting_alarmnotification_dangshujuyuanxie:
    '當數據源協議連接耗時超過閥值時',
  packages_business_setting_alarmnotification_dangshujuyuanwang:
    '當數據源網络連接耗時',
  packages_business_setting_alarmnotification_dangshujuwufa:
    '當數據無法網络連接耗時',
  packages_business_setting_alarmnotification_dangrenwudezeng:
    '當任務的增量延遲超過閥值時',
  packages_business_setting_alarmnotification_dangrenwutingzhi: '當任務停止時',
  packages_business_setting_alarmnotification_dangrenwuzengliang:
    '當任務增量开始時',
  packages_business_setting_alarmnotification_dangrenwuquanliang:
    '當任務全量完成時',
  packages_business_setting_alarmnotification_dangrenwujiaoyan:
    '當任務校驗出錯時',
  packages_business_setting_alarmnotification_dangrenwuyudao:
    '當任務遇到錯誤時',
  packages_business_setting_alarmnotification_dangrenwustop: 'Agent服務停止時',
  packages_business_setting_alarmnotification_dangrenwuuP: 'Agent服务啓動時',
  packages_business_setting_alarmnotification_msshigaojing: 's 時告警',
  packages_business_setting_alarmnotification_lianxu: '連續',
  packages_business_setting_alarmnotification_cichugaojinggui:
    '此處告警規則設置為系統全局告警規則設置，任務運行監控頁面的告警規則設置優先級高於系統全局設置',
  packages_business_setting_alarmnotification_renwumorengao:
    '任務默認告警規則設置',
  packages_business_setting_alarmnotification_morengaojinggui: '默認告警規則',
  packages_business_setting_alarmnotification_renwugaojingshe: '任務告警設置',
  packages_business_setting_alarmnotification_recipient_setting:
    '任務默認告警接收人設置',
  packages_business_setting_alarmnotification_recipient_desc:
    '此處設置為系統全局告警接收人設置，設置的所有的接收人都可以收到當前系統已開啓的告警',
  packages_business_setting_alarmnotification_recipient_default:
    '默認告警接收人',
  packages_business_setting_alarmnotification_recipient: '告警接收人',
  packages_business_setting_alarmnotification_recipient_tip:
    '支持設置多個告警接收人郵箱，多個郵箱以逗號分隔',
  packages_business_setting_alarmnotification_channel: '告警通知渠道',
  packages_business_setting_alarmnotification_dangjiaoyanrenwucuowu:
    '當校驗任務遇到錯誤時',
  packages_business_setting_alarmnotification_dangjiaoyanrenwushuliangcuowu:
    '當count校驗結果的差異行數大於閾值時',
  packages_business_setting_alarmnotification_dangjiaoyanrenwuzhicuowu:
    '當值校驗結果的表數據差大於閾值時',
  //消息通知
  packages_business_notify_user_all_notice: '全部通知',
  packages_business_notify_unread_notice: '未讀消息',
  packages_business_notify_mask_read: '標記本頁為已讀',
  packages_business_notify_mask_read_all: '標記全部為已讀',
  packages_business_notify_notice_type: '消息類型',
  packages_business_notify_notice_level: '消息級別',
  packages_business_notify_no_notice: '暫無通知',
  packages_business_notify_no_webchat_notification:
    '您的賬戶還沒有進行微信綁定，如需通過微信接收通知信息，請先退出登錄後通過微信掃碼完成綁定',
  packages_business_connections_databaseform_bujiazai: '不加載',
  packages_business_connections_databaseform_system: '跟隨系統設置',
  packages_business_custom_node_placeholder: '請輸入節點名稱搜索',
  packages_business_custom_node_edit_confirm:
    '檢測到以下運行中的任務調用了該節點，如需配置生效請重新啓動任務',
  packages_business_task_list_sqLyuju: 'SQL語句:',
  packages_business_relation_details_waicunxinxi: '使用的外存信息',
  packages_business_milestone_list_cuowuxinxi: '錯誤信息',
  packages_business_milestone_list_progr: '({val1}%,剩餘{val2})',
  packages_business_milestone_list_chucuo: '出錯',
  packages_business_milestone_list_shujuchuli: '數據處理',
  packages_business_milestone_list_mubiaoshujuxie: '目標數據寫入',
  packages_business_milestone_list_chuangjianmubiaobiao: '創建目標表',
  packages_business_milestone_list_lianjiebingyanzheng: '連接並驗證賬號權限',
  packages_business_milestone_list_duquzengliangshu: '讀取增量數據',
  packages_business_milestone_list_kaiqizengliang: '開啟增量',
  packages_business_milestone_list_duququanliangshu: '讀取全量數據',
  packages_business_milestone_list_finish: '{val1}/{val2} 已完成，{val3} ...',
  packages_business_milestone_list_zhengtijindu: '整體進度',
  packages_business_milestone_list_jinhangzhongpr:
    '進行中，{val1}%已完成，預計剩餘時間{val2}',
  packages_business_milestone_list_zengliangshujuqian: '增量數據遷移',
  packages_business_milestone_list_quanliangshujuqian: '全量數據複製',
  packages_business_milestone_list_biaojiegouqianyi: '表結構複製',
  packages_business_milestone_list_load_table_structure: '加載表結構',
  packages_business_milestone_list_shujujiedianchu: '連接數據源',
  packages_business_milestone_list_renwudiaodu: '任務調度',
  packages_business_milestone_list_haoshi: '耗時',
  packages_business_milestone_list_guanjianbuzhou: '關鍵步驟',
  packages_business_nodes_list_laiyuan: '來源',
  daas_data_discovery_previewdrawer_jiedian: '節點',
  daas_data_discovery_previewdrawer_renwumiaoshu: '任務描述',
  daas_data_discovery_previewdrawer_yinqingmiaoshu: '引擎描述',
  daas_data_discovery_previewdrawer_yinqingmingcheng: '引擎名稱',
  daas_data_discovery_previewdrawer_jiedianshu: '節點數',
  daas_data_discovery_previewdrawer_shuchucanshu: '輸出參數',
  daas_data_discovery_previewdrawer_fuwumiaoshu: '服務描述',
  daas_data_discovery_previewdrawer_jiedianmiaoshu: '節點描述',
  daas_data_discovery_previewdrawer_shurujiedian: '输入节点',
  daas_data_discovery_previewdrawer_shuchujiedian: '输出节点',
  daas_router_routes_guanlianrenwuxiang: '關聯任務詳情',
  object_list_name: '對象名稱',
  object_list_classification: '對象分類',
  object_list_type: '對像類型',
  object_list_source_type: '來源類型',
  object_list_source_information: '來源信息',
  datadiscovery_catalogue_ziyuanbangding: '資源綁定',
  datadiscovery_catalogue_lianjieduixiangming: '連接對象名',
  datadiscovery_catalogue_ziyuanleixing: '資源類型',
  datadiscovery_objectlist_duixiangminglaiyuan: '對象名稱/數據源',
  datadiscovery_objectlist_laiyuanfenlei: '來源分類',
  datadiscovery_previewdrawer_shujuxiang: '數據項',
  datadiscovery_previewdrawer_yewumingcheng: '業務名稱',
  datadiscovery_previewdrawer_lianjiemiaoshu: '連接描述',
  datadiscovery_previewdrawer_shujuliang: '數據量',
  datadiscovery_previewdrawer_biangengshijian: '變更時間',
  datadiscovery_previewdrawer_guanliyuan: '管理員',
  datadiscovery_previewdrawer_duixiangxiangqing: '對象詳情',
  datadiscovery_previewdrawer_yewumiaoshu: '業務描述',
  datadiscovery_previewdrawer_yewuleixing: '業務類型',
  datadiscovery_previewdrawer_suoyin: '索引',
  datadiscovery_previewdrawer_waijian: '外鍵',
  datadiscovery_previewdrawer_zhujian: '主鍵',
  connection_list_name: '连接名',
  meta_table_default: '默认值',
  meta_table_not_null: '非空',
  page_title_overview: '概览',
  metadata_meta_type_table: '数据表',
  packages_business_create_connection_dialog_xuanzeshujuyuan: '選擇數據源類型',
  packages_business_create_connection_dialog_neirongSho:
    '顯示處於 ALPHA 狀態的連接器',
  packages_business_create_connection_dialog_neirongSho2:
    '顯示處於 BETA 狀態的連接器',
  packages_business_create_connection_dialog_neirongCho:
    '從下面選擇一個數據源連接器並配置連接和憑據。',
  // 共享挖掘
  packages_business_shared_cdc_placeholder_task_name: '請輸入挖掘任務名搜索',
  packages_business_shared_cdc_placeholder_connection_name:
    '請輸入連接名稱搜索',
  packages_business_shared_cdc_name: '請輸入挖掘名稱',
  packages_business_shared_cdc_setting_select_mode: '存储模式',
  packages_business_shared_cdc_setting_select_mongodb_tip: '請輸入mongodb連接',
  packages_business_shared_cdc_setting_select_table_tip: '請輸入表名',
  packages_business_shared_cdc_setting_select_time_tip: '請選擇日誌保存時長',
  packages_business_shared_cdc_setting_message_edit_save:
    '保存成功，重啟任務後生效',
  packages_business_shared_list_name: '挖掘名稱',
  packages_business_shared_list_time_excavation: '挖掘所處時間點',
  packages_business_shared_list_time: '挖掘延遲',
  packages_business_shared_list_setting: '挖掘設置',
  packages_business_shared_list_status: '狀態',
  packages_business_shared_list_edit_title: '挖掘编辑',
  packages_business_shared_list_edit_title_start_time: '挖掘開始時間',
  packages_business_shared_form_setting_table_name: '存儲MongoDB表名',
  packages_business_shared_form_setting_log_time: '日誌保存時長',
  packages_business_shared_form_edit_name: '挖掘名稱',
  packages_business_shared_form_edit_title: '是否放棄編輯該挖掘任務',
  packages_business_shared_form_edit_text: '此操作不會保存已修改的內容',
  packages_business_shared_detail_mining_info: '挖掘信息',
  packages_business_shared_detail_name: '挖掘名稱',
  packages_business_shared_detail_log_mining_time: '日誌挖掘時間',
  packages_business_shared_detail_log_time: '日誌保存時長',
  packages_business_shared_detail_call_task: '調用任務',
  packages_business_shared_detail_source_time: '源庫時間點',
  packages_business_shared_detail_sycn_time_point: '同步時間點',
  packages_business_shared_detail_mining_status: '挖掘狀態',
  packages_business_shared_detail_button_table_info: '表詳情',
  packages_business_shared_detail_statistics_time: '統計時間',
  packages_business_shared_detail_incremental_time: '所處的時間點',
  packages_business_shared_mining_detail_wajuexiangqingx: '挖掘詳情x軸：',
  packages_business_stop_confirm_message:
    '初始化類型的任務暫停後如果再次啟動，任務會從頭開始同步，確定暫停？',
  packages_business_important_reminder: '重要提醒',
  packages_business_tablename: '表名稱',
  packages_business_shared_cdc_persistence_rocksdb_path:
    'RocksDB存儲的本地路徑',
  packages_business_shared_mining_table_jinriwajue: '今日挖掘',
  packages_business_shared_mining_table_leijiwajue: '累計挖掘',
  packages_business_shared_mining_table_zuixinrizhishi: '最新日誌時間',
  packages_business_shared_mining_table_shoutiaorizhishi: '首條日誌時間',
  packages_business_shared_mining_table_jiaruwajueshi: '加入挖掘時間',
  packages_business_shared_mining_table_biaoming: '表名',
  packages_business_shared_mining_table_wajuebiaoxinxi: '挖掘表信息',
  packages_business_relation_sharedlist_shiyongdewajue: '使用的挖掘表',
  packages_business_milestone_list_zengliangshujuxie: '增量數據寫入',
  packages_business_milestone_list_quanliangshujuxie: '全量數據寫入',
  packages_business_milestone_list_jinruzengliangshu: '增量數據複製',
  packages_business_logs_nodelog_cuowuduizhan: '錯誤堆棧',
  packages_business_logs_nodelog_yuanyinfenxi: ' 原因分析',
  packages_business_logs_nodelog_xianshishijianchuo: '顯示時間',
  packages_business_connections_databaseform_chakanxintiaoren: '查看心跳任務',
  packages_business_connections_databaseform_dakaixintiaobiao:
    '开启此功能后, 平台会在源库内新建一张表, 并以每秒 1 次的频率, 向源库的这张表内做更新, 借助这张表, 平台可以实现精准的数据延迟探测, 并可以有效监控任务健康状况, 此功能需要对源库具有写权限才会生效。',
  packages_business_connections_databaseform_kaiqixintiaobiao: '開啟心跳表',
  packages_business_connections_databaseform_jiaobentiaoshi: '腳本調試',
  // api服务管理
  packages_business_data_server_drawer_qingshurucanshu: '請輸入參數名稱',
  packages_business_data_server_drawer_paixu: '排序',
  packages_business_data_server_drawer_meigefenyefan: '每個分頁返回的記錄數',
  packages_business_data_server_drawer_fenyebianhao: '分頁編號',
  packages_business_data_server_drawer_zidingyichaxun: '自定義查詢',
  packages_business_data_server_drawer_morenchaxun: '默認查詢',
  packages_business_data_server_drawer_qingxuanzeduixiang: '請選擇對象名稱',
  packages_business_data_server_drawer_qingxuanzelianjie: '請選擇連接類型',
  packages_business_data_server_drawer_qingshurufuwu: '請輸入服務名稱',
  packages_business_data_server_drawer_quanxianfanwei: '權限範圍',
  packages_business_data_server_drawer_selectPermissions: '請選擇權限範圍',
  packages_business_data_server_drawer_shilidaima: '示例代碼',
  packages_business_data_server_drawer_shilidaima2: '示例代碼',
  packages_business_data_server_drawer_fanhuijieguo: '返回結果',
  packages_business_data_server_drawer_diaoyongfangshi: '調用方式',
  packages_business_data_server_drawer_fuwufangwen: '服務訪問',
  packages_business_data_server_drawer_shuchujieguo: '輸出結果',
  packages_business_data_server_drawer_pailietiaojian: '排列條件',
  packages_business_data_server_drawer_shaixuantiaojian: '篩選條件',
  packages_business_data_server_drawer_canshuzhi: '參數值',
  packages_business_data_server_drawer_canshumingcheng: '參數名稱',
  packages_business_data_server_drawer_shurucanshu: '輸入參數',
  packages_business_data_server_drawer_jiekouleixing: '接口類型',
  packages_business_data_server_drawer_fabujiedian: '發布節點',
  packages_business_data_server_drawer_caozuoleixing: '操作類型',
  packages_business_data_server_drawer_zanwumiaoshu: '暫無描述',
  packages_business_data_server_drawer_tiaoshi: '調試',
  packages_business_data_server_drawer_peizhi: '配置',
  packages_business_data_server_drawer_chuangjianfuwu: '創建服務',
  packages_business_data_server_drawer_fuwuxiangqing: '服務詳情',
  packages_business_data_server_list_quedingchexiaogai: '確定撤銷該服務？',
  packages_business_data_server_list_quedingfabugai: '確定發布該服務？',
  packages_business_data_server_list_querenshanchufu: '確認刪除服務？',
  packages_business_data_server_list_huoqufuwuyu: '獲取服務域名失敗！',
  packages_business_data_server_list_fuwuzhuangtai: '服務狀態',
  packages_business_data_server_list_guanlianduixiang: '關聯對象',
  packages_business_data_server_list_fuwumingcheng: '服務名稱',
  packages_business_data_server_drawer_geshicuowu: '格式錯誤',
  packages_business_data_server_drawer_validate:
    '只能包含中文、字母、數字、下劃線和美元符號,並且數字不能開頭',
  packages_business_data_server_drawer_aPI_path_Settings: '訪問路徑設置',
  packages_business_data_server_drawer_default_path: '默認訪問路徑',
  packages_business_data_server_drawer_custom_path: '自定義訪問路徑',
  packages_business_data_server_drawer_prefix: '前綴',
  packages_business_data_server_drawer_base_path: '基礎路徑',
  packages_business_data_server_drawer_path: '訪問路徑',
  packages_business_data_server_drawer_confirm_tip:
    '重新生成會導致原API訪問路徑發生改變，是否確認重新生成？ ',
  packages_business_connection_debug_input_arg: '模拟參數',
  packages_business_connection_debug_input_arg_error: '模拟參數格式錯誤',
  packages_business_more_than: '超過',
  packages_business_more_than_after: '秒，未返回結果時自動終止試運行',
  packages_business_connection_debug_form_error: '請檢查表單必填項',
  packages_business_connection_debug_as: '作為',

  // LDP
  packages_business_data_console_sources: '源數據層',
  packages_business_data_console_fdm: '平台緩存層',
  packages_business_data_console_mdm: '平台加工層',
  packages_business_data_console_targets: '數據目標和服務層',
  packages_business_data_console_goto_ai_chat: 'AI 对话',
  packages_business_create_clone_task: '創建數據複製任務',
  packages_business_create_sync_task: '創建數據開發任務',
  packages_business_table_prefix: '表前綴',
  packages_business_last_data_change_time: '數據最後更新時間',
  packages_business_cdc_delay_time: '增量數據延遲',
  packages_business_rows: '行數',
  packages_business_columns: '列數',
  packages_business_storage_size: '存儲大小',
  packages_business_columns_preview: '列預覽',
  packages_business_sample_data: '樣本數據',
  packages_business_table_preview_task: '以這個模型作為源/目標的任務',
  packages_business_table_preview_connection_task: '以該連接作為源/目標的任務',
  packages_business_table_count: '包含表數量',
  packages_business_overview: '概覽',
  packages_business_tasks: '任務',
  packages_business_model_update_time: '模型更新時間',
  packages_business_save_and_more: '保存並添加更多',
  packages_business_table_status_error: '異常',
  packages_business_table_status_draft: '草稿',
  packages_business_table_status_normal: '正常',
  packages_business_data_console_target_no_task: '未對此目標配置任何任務',
  packages_business_data_console_target_no_api: '未對此應用配置任何API',
  packages_business_data_console_target_connection_desc: '將數據同步到 {val}',
  packages_business_view_more: '查看更多',
  packages_business_view_collapse: '收起',

  packages_business_shared_const_shier: '十二',
  packages_business_shared_const_shiyi: '十一',
  packages_business_shared_const_shi: '十',
  packages_business_shared_const_jiu: '九',
  packages_business_shared_const_ba: '八',
  packages_business_shared_const_qi: '七',
  packages_business_shared_const_liu: '六',
  packages_business_shared_const_wu: '五',
  packages_business_shared_const_si: '四',
  packages_business_shared_const_san: '三',
  packages_business_shared_const_er: '二',
  packages_business_shared_const_yi: '一',
  packages_business_shared_const_ling: '零',
  packages_business_shared_const_yiquxiao: '已取消',
  packages_business_shared_const_shixiao: '失效',
  packages_business_shared_const_tuikuanzhong: '退款中',
  packages_business_shared_const_tuikuanshibai: '退款失败',
  packages_business_shared_const_yituikuan: '已退款',
  packages_business_shared_const_zhifushibai: '支付失败',
  packages_business_payment_timeout: '支付超時',
  packages_business_shared_const_yizhifu: '已支付',
  packages_business_shared_const_weizhifu: '未支付',
  packages_business_shared_ws_client_webso: 'websocket 消息解析失败: ',
  packages_business_shared_ws_client_webso2: 'websocket 接收消息格式错误: ',
  packages_business_shared_ws_client_acces: 'access_token 过期',
  packages_business_shared_ws_client_webso3: 'websocket 已关闭',
  packages_business_shared_ws_client_webso4: 'websocket 断开连接',
  packages_business_shared_ws_client_webso5: 'websocket 已连接',
  packages_business_shared_ws_client_webso6: 'websocket 超过最大重连次数 ',
  packages_business_shared_ws_client_cizhonglian: '次重连',
  packages_business_shared_ws_client_webso7: 'websocket 尝试第',
  packages_business_shared_ws_client_webso8: 'websocket 连接失败，准备尝试重连',
  packages_business_switch_directory_view: '切換至目錄視圖',
  packages_business_switch_data_console_view: '切換至面板視圖',
  packages_business_task_created_success: '任務創建成功，點擊查看',
  packages_business_task_created_fail_no_primary_key:
    '任務已經創建，但由於您的表沒有主鍵，需要進入任務編輯手動設置更新條件字段，點擊查看任務',
  packages_business_fdm_create_task_dialog_desc_prefix: `${import.meta.env.VUE_APP_PAGE_TITLE}  將自動創建一個數據複製管道任務，將您選擇的`,
  packages_business_fdm_create_task_dialog_desc_suffix:
    '的結構和數據自動複製到數據平台的 Cache 層並保持源庫和Cache 層數據的准實時同步及自動校驗。在大部分時候源庫的結構改動(DDL)也會被複製到Cache 層。您可以在通過點擊Cache 層裡面的庫名右側的ICON來監控該管道任務的運行狀態。您也可以選擇現在修改在Cache 層的物理表名前綴。',
  packages_business_mdm_create_task_dialog_desc_prefix:
    '這將在數據平台的 Curated 層創建一個加工模型。創建加工模型的常見場景有以下幾種：',
  packages_business_fdm_create_task_dialog_desc_li1:
    '需要對 Cache 層的數據做一些轉型，增強，加計算字段等處理',
  packages_business_fdm_create_task_dialog_desc_li2:
    '需要對數個 Cache 層的表的結構進行合併，構建一個寬表',
  packages_business_fdm_create_task_dialog_desc_li3:
    '需要對數個 Cache 層的表的數據進行合併，構建一個合併表',
  packages_business_mdm_create_task_dialog_desc_suffix:
    '注意: 你可以直接在Cache 層直接發佈API或者做數據複製任務到目標端。如果是因為這兩個原因，你無需創建加工層模型。',
  packages_business_mdm_create_task_dialog_desc_table_name:
    '請輸入打算新構建在Curated 層裡面的表名。如果該表名已經存在，默認將覆蓋已有的數據',
  packages_business_save_and_run_now: '保存並運行',
  packages_business_save_only: '僅保存',
  packages_business_target_create_task_dialog_desc_prefix_clone: `${import.meta.env.VUE_APP_PAGE_TITLE} 將創建一個數據複製任務，將`,
  packages_business_target_create_task_dialog_desc_prefix_sync: `${import.meta.env.VUE_APP_PAGE_TITLE} 將創建一個數據開發任務，將`,
  packages_business_target_create_task_dialog_desc_to: '同步到',
  packages_business_target_create_task_dialog_desc_suffix:
    '請點擊下面的按鈕繼續,您也可以更改任務名稱。',
  packages_business_fdm_empty_text:
    '請將<strong>源數據層</strong>中的表拖拽至此<br/>即可開始複製數據',
  packages_business_mdm_empty_text:
    '請將<strong>源數據層/平台緩存層</strong>中的表拖拽至此<br/>即可開始同步數據',
  packages_business_catalog_delete_confirm_message:
    '此操作僅會將該分類及其子分類刪除，如需刪除分類下的物理表,請您自行操作。',
  packages_business_mdm_table_duplication_confirm:
    '目標表已經存在，請確定是否繼續？',
  packages_business_data_console_mode: '請揀選產品能力模式',
  packages_business_data_console_mode_integration: '數據集成',
  packages_business_data_console_mode_service: '數據服務平台',
  packages_business_data_console_mode_integration_tooltip_1:
    '支援異構數據同結構自動實時同步',
  packages_business_data_console_mode_integration_tooltip_2:
    '支援數據的實時加工與轉換',
  packages_business_data_console_mode_integration_tooltip_3:
    '100+連接器，包括資料庫、消息隊列、檔案、API 等',
  packages_business_data_console_mode_service_tooltip_1:
    '支援數據集成模式全部能力',
  packages_business_data_console_mode_service_tooltip_2: '支援平台快取企業數據',
  packages_business_data_console_mode_service_tooltip_3:
    '支援多種下游服務集成與發布管理',
  packages_business_data_console_fdm_mdm_storage: '数据层存储',
  packages_business_data_console_fdm_storage: '平台緩存層存儲',
  packages_business_data_console_mdm_storage: '平台加工層存儲',
  packages_business_data_console_fdm_mdm_storage_tooltip:
    '請指定用於額外數據層存儲的數據庫連接',
  packages_business_mongodb_atlas_cluster: 'MongoDB Atlas 集群',
  packages_business_mongodb_self_hosted_cluster: '自托管 MongoDB 集群',
  packages_business_mongodb_full_management_cluster: '全托管 MongoDB 集群',
  packages_business_data_console_setting_saved_tooltip: '保存後暫不支持修改',
  // 共享緩存
  packages_business_shared_cache_create: '創建緩存',
  packages_business_shared_cache_edit: '編輯緩存',
  packages_business_shared_cache_placeholder_task_name: '請輸入緩存任務名搜索',
  packages_business_shared_cache_placeholder_connection_name:
    '請輸入連接名稱搜索',
  packages_business_shared_cache_button_create: '新建緩存',
  packages_business_shared_cache_name: '緩存名稱',
  packages_business_shared_cache_status: '緩存狀態',
  packages_business_shared_cache_time: '緩存時間',
  packages_business_shared_cache_keys: '緩存鍵',
  packages_business_shared_cache_fields: '緩存欄位',
  packages_business_shared_cache_code: '應用代碼',
  packages_business_shared_cache_placeholder_name: '請輸入緩存名稱',
  packages_business_shared_cache_placeholder_connection: '請選擇連接',
  packages_business_shared_cache_placeholder_table: '請選擇表',
  packages_business_shared_cache_placeholder_keys: '請選擇緩存鍵',
  packages_business_shared_cache_placeholder_fields: '請選擇緩存欄位',
  packages_business_shared_cache_max_memory: '緩存最大內存',
  packages_business_shared_cache_keys_tooltip:
    '以該字段作為主鍵識別數據進行緩存',
  packages_business_shared_cache_fields_tooltip: '需要進行緩存的常用字段',
  packages_business_shared_cache_max_memory_tooltip:
    '系統會保存的最大內存量，超過則按調用時間，將最不常用的數據刪掉',
  packages_business_shared_cache_code_tooltip:
    '可在JS節點中輸入這段代碼使用該緩存',
  packages_business_shared_cache_column_connection: '所屬連接',
  packages_business_shared_cache_column_table: '所屬表',
  packages_business_shared_cache_cache_key_message: '所選緩存鍵無索引',
  packages_business_shared_cache_cache_key_auto_create: '自動建立索引',
  packages_business_shared_cache_cache_key_auto_create_tip:
    '開啓該能力後，會自動在源表為緩存鍵創建索引，可能會對源庫造成影響，請謹慎開啓',
  packages_business_relation_list_gongxianghuancun: '共享緩存',
  packages_business_application_delete_shanchuyingyong: '删除应用',
  packages_business_application_delete_ninzhengzaishanchu:
    '您正在删除应用<span class="fw-bolder font-color-dark">{val1}</span>，是否确认删除',
  packages_business_application_delete_ninzhengzaishanchu2:
    '您正在删除应用<span class="fw-bolder font-color-dark">{val1}</span>，该应用下的API将移动到',
  packages_business_application_delete_yingyongmiaoshubu: '应用描述不能为空',
  packages_business_application_delete_yingyongmingchengbu: '应用名称不能为空',
  packages_business_application_delete_shifouquerenshan: '是否确认删除',
  packages_business_application_editor_yingyongmiaoshu: '应用描述',
  packages_business_application_list_qingshuruyingyong: '请输入应用名称',
  packages_business_application_list_yifabuAp: '已发布API数量',
  packages_business_application_list_zongApIshu: '总API数量',
  packages_business_application_list_yingyongmingcheng: '应用名称',
  packages_business_application_list_chuangjianyingyong: '创建应用',
  packages_business_data_server_drawer_qingxuanzesuoshu: '请选择所属应用',
  packages_business_data_server_drawer_suoshuyingyong: '所属应用',
  packages_business_create_connection_scenedialog_gongzuoliu: '工作流',
  packages_business_create_connection_scenedialog_duiliegongshu: '隊列供數',
  packages_business_create_connection_scenedialog_guochantidai: '國產替代',
  packages_business_create_connection_scenedialog_shujukutongbu: '數據庫同步',
  packages_business_create_connection_scenedialog_chaxunjiasu: '查詢加速',
  packages_business_create_connection_scenedialog_rushucang: '入數倉',
  packages_business_create_connection_scenedialog_tuijianchangjing: '推薦場景',
  packages_business_create_connection_scenedialog_qingxuanzeninde:
    '請選擇您的使用場景',
  packages_business_create_connection_serveform_fenleimingcheng: '分類名稱',
  packages_business_components_tableview_yizhegemoxing:
    '以這個模型源/目標的任務',
  packages_business_components_tableview_xinzenglebiaoqian: '新增了標籤603',
  packages_business_components_tableview_zengliangshujuyan: '增量數據延遲：',
  packages_business_components_tableview_shujuzuihougeng: '數據最後更新時間：',
  packages_business_swimlane_fdm_biaobianji: '表編輯',
  packages_business_swimlane_tablepreview_zuihoufangwenshi: '最後訪問時間',
  packages_business_swimlane_tablepreview_apIchuanshu: 'API傳輸總量',
  packages_business_swimlane_tablepreview_apIfangwen: 'API訪問行數',
  packages_business_swimlane_tablepreview_fangwencishu: '訪問次數',
  packages_business_swimlane_tablepreview_apifuwu: 'api服務名稱',
  packages_business_swimlane_target_yejibao: '業績寶',
  packages_business_task_list_meiyoufaxiannin:
    '沒有發現您最近有任務報錯, 如果有其他問題, 歡迎諮詢我們的人工客服',
  packages_business_api_application_list_xitongmorenchuang:
    '系統默認創建的應用，不可編輯和刪除',
  packages_business_create_connection_title_select_type: '请选择数据源类型',
  // 外存管理
  packages_business_external_storage_list_querenshanchuwai: '確認刪除外存？',
  packages_business_external_storage_list_qingshurucunchu: '請輸入存儲路徑',
  packages_business_external_storage_list_qingshuruwaicun: '請輸入外存名稱',
  packages_business_external_storage_list_qingshuruwaicun2: '請輸入外存表名稱',
  packages_business_external_storage_list_sheweimoren: '設為默認',
  packages_business_external_storage_list_cunchulujing: '存儲路徑',
  packages_business_external_storage_list_chuangjianwaicun: '創建外存',
  packages_business_external_storage_list_bianjiwaicun: '編輯外存',
  packages_business_external_storage_list_tishi:
    '該外存已被 {val1} 调用，請刪除或修改配置後重試。',
  // API
  packages_business_api_publish: 'API 發佈',
  packages_business_api_application: 'API 應用',
  packages_business_api_application_md: `## API 應用
  - 您可以輕鬆地創建新的應用，將 API 分類到不同的應用中，實現API的差異化管理，從而提高業務安全性和效率
  - 您可以將數據庫表拖放到應用上，快速地發佈 API `,
  packages_business_qingshurucanshu: '請輸入參數名稱',
  packages_business_paixu: '排序',
  packages_business_meigefenyefan: '每個分頁返回的記錄數',
  packages_business_fenyebianhao: '分頁編號',
  packages_business_zidingyichaxun: '自定義查詢',
  packages_business_morenchaxun: '默認查詢',
  packages_business_qingxuanzeduixiang: '請選擇對象名稱',
  packages_business_qingxuanzelianjie: '請選擇連接類型',
  packages_business_qingshurufuwu: '請輸入服務名稱',
  packages_business_quanxianfanwei: '權限範圍',
  packages_business_selectPermissions: '請選擇權限範圍',
  packages_business_shilidaima: '示例代碼',
  packages_business_shilidaima2: '示例代碼',
  packages_business_fanhuijieguo: '返回結果',
  packages_business_diaoyongfangshi: '調用方式',
  packages_business_fuwufangwen: '服務訪問',
  packages_business_shuchujieguo: '輸出結果',
  packages_business_pailietiaojian: '排列條件',
  packages_business_shaixuantiaojian: '篩選條件',
  packages_business_canshuzhi: '參數值',
  packages_business_canshumingcheng: '參數名稱',
  packages_business_shurucanshu: '輸入參數',
  packages_business_jiekouleixing: '接口類型',
  packages_business_fabujiedian: '發布節點',
  packages_business_caozuoleixing: '操作類型',
  packages_business_zanwumiaoshu: '暫無描述',
  packages_business_tiaoshi: '調試',
  packages_business_peizhi: '配置',
  packages_business_chuangjianfuwu: '創建服務',
  packages_business_fuwuxiangqing: '服務詳情',
  packages_business_geshicuowu: '格式錯誤',
  packages_business_validate:
    '只能包含中文、字母、數字、下劃線和美元符號,並且數字不能開頭',
  packages_business_aPI_path_Settings: '訪問路徑設置',
  packages_business_default_path: '默認訪問路徑',
  packages_business_custom_path: '自定義訪問路徑',
  packages_business_prefix: '前綴',
  packages_business_base_path: '基礎路徑',
  packages_business_path: '訪問路徑',
  packages_business_confirm_tip:
    '重新生成會導致原API訪問路徑發生改變，是否確認重新生成？ ',
  packages_business_create_connection_scenedialog_table:
    'Tablestore是一種高可靠性、高性能、靈活性和可擴展性的分佈式NoSQL數據存儲服務，適用於實時數據查詢和分析等應用場景。 ',
  packages_business_create_connection_scenedialog_selec:
    'SelectDB Cloud是一種基於Apache Doris內核的全託管實時數據倉庫服務，具有高可靠性、高性能、易用性和低成本等優點，適用於處理海量數據的查詢和分析需求。 ',
  packages_business_create_connection_scenedialog_redis:
    'Redis是一種高性能內存數據庫，支持多種數據結構和持久化方式，具有可擴展性和可靠性，適用於緩存、會話管理、排行榜、消息隊列等應用場景。 ',
  packages_business_create_connection_scenedialog_mongo:
    'MongoDB是一種非關係型數據庫，具有靈活性、高性能、易用性和可擴展性，適用於需要處理大量非結構化數據和需要快速查詢和可擴展性的應用場景。 ',
  packages_business_create_connection_scenedialog_bigQu:
    'BigQuery是Google Cloud提供的託管式數據倉庫，以高速、可擴展和安全著稱，可以處理PB級數據，與多個工具集成，適用於各種數據分析和挖掘場景。 ',
  packages_business_create_connection_mysql_desc:
    'MySQL 適用於中小規模網站和應用程式，輕量級資料庫管理系統，支援資料儲存、查詢和簡單分析，廣泛用於 Web 開發和輕負載應用。',
  packages_business_create_connection_oracle_desc:
    'Oracle 適用於企業級資料庫解決方案，支援大規模資料處理、高效能交易處理和複雜查詢，在企業的核心業務系統和資料管理上廣泛應用。',
  packages_business_create_connection_sqlserver_desc:
    'SQL Server 主要用於管理和處理大規模資料庫，適用於企業級應用程式和網站，支援資料儲存、查詢、分析和報告等功能。',
  packages_business_create_connection_postgresql_desc:
    'PostgreSQL 適用於高度穩定的資料儲存和複雜查詢，廣泛應用於 Web 應用程式、地理資訊系統、資料分析和企業級應用。',
  packages_business_create_connection_clickhouse_desc:
    'ClickHouse 適用於快速查詢和分析大規模資料，特別擅長處理實時分析、日誌分析、資料倉儲和時間序列資料。',
  packages_business_create_connection_elasticsearch_desc:
    'Elasticsearch 適用於全文搜尋、日誌分析、實時資料分析和大規模資料索引，廣泛應用於搜尋引擎、監控和企業級應用。',
  packages_business_create_connection_dummy_desc:
    'Dummy 通常用於表示虛擬或佔位符實體，無實際資料。在軟體開發和測試中，Dummy 物件用於填充空缺或模擬佔位行為。',
  packages_business_create_connection_kafka_desc:
    'Kafka 適用於高吞吐量的實時資料流處理，用於日誌收集、資料傳輸、消息發布/訂閱和流式處理應用，特別擅長大規模資料流處理。',
  packages_business_create_connection_doris_desc:
    'Doris 適用於實時資料分析和報表，支援高並發查詢和複雜分析，廣泛應用於資料倉儲、BI 報表和資料視覺化。',
  packages_business_create_connection_mongodbatlas_desc:
    'MongoDB Atlas是全托管的MongoDB數據庫服務，它通過自動化數據庫管理，簡化了部署、擴展和監控過程，使開發者可以更專注於應用程序開發。Atlas支持彈性擴展、全球部署和安全性功能，適用於各種規模和類型的應用。',
  packages_business_swimlane_tablepreview_chuangjianrenwu: '創建任務',
  packages_business_as_source: '作為源頭',
  packages_business_as_target: '作為目標',
  packages_business_connections_databaseform_dangqianlianjiezheng:
    '當前連接正在使用原外存，切換會導致數據丟失，請謹慎操作。 ',
  packages_business_swimlane_target_shouye: '首頁',
  packages_business_connections_databaseform_chakanwajueren: '查看挖掘任務',
  packages_business_connections_databaseform_dangqianlianjiede:
    '當前連接的挖掘任務正在使用該外存，暫不允許修改，如需修改請先重置或刪除對應挖掘任務。 ',
  packages_business_shared_mining_table_yitingzhiwajue: '已停止挖掘',
  packages_business_shared_mining_table_zhengzaiwajue: '正在挖掘',
  packages_business_shared_mining_table_ninyaotingzhiwa:
    '您要停止挖掘的表正在被以下任務使用，停止挖掘後將會影響以下任務的正常同步，請確認是否要繼續停止。 ',
  packages_business_shared_mining_table_tingzhiwajueti: '停止挖掘提醒',
  packages_business_shared_mining_table_yihebingdelian: '已合并的連接',
  packages_business_shared_mining_table_shengyuyigelian:
    '挖掘任務中至少要有一張表在挖掘，不能全部停止。',
  packages_business_logs_nodelog_yijianfuzhi: '一鍵複製',
  packages_business_connections_jsdebug_shiyongHtt:
    '使用HttpReceiver最新接收到的數據用於調試',
  packages_business_connections_jsdebug_huoqutiaoshishu: '獲取調試數據',
  packages_business_shared_mining_list_shanchurenwus:
    '刪除任務<span class="color-primary">{val1}</span>後，此任務將無法恢復',
  packages_business_shared_mining_list_gaiwajuerenwu:
    '該挖掘任務已被 {val} 個任務調用，請刪除任務後重試',
  packages_business_shared_cache_list_qingxianxiugaiwai:
    '外存不存在，請先修改外存配置後，再啟動。',
  packages_business_components_conditionbox_shifouquerenqing:
    '是否確認清除索引字段為空的校驗條件？ ',
  packages_business_components_conditionbox_suoyinziduanwei: '索引字段為空',
  packages_business_components_conditionbox_yijianqingchusuo:
    '一鍵清除索引字段為空的條件',
  packages_business_external_storage_list_yanzhengfuwuduan: '驗證服務端證書',
  packages_business_external_storage_list_siyaomima: '私鑰密碼',
  packages_business_external_storage_list_kehuduansiyao: '客戶端私鑰',
  packages_business_external_storage_list_zhengshubanfaji: '證書頒發機構',
  packages_business_external_storage_list_shiyongTls: '使用 TLS/SSL 連接',
  page_title_verification_create: '新建校驗',
  page_title_task_edit: '編輯任務',
  page_title_task_details: '任務詳情',
  page_title_verification_history: '校驗歷史',
  page_title_data_difference_details: '差異詳情',
  page_title_data_verification_result: '校驗結果',
  page_title_diff_verification_history: '差異校驗歷史',
  page_title_diff_verification_details: '差異校驗詳情',
  packages_business_connections_list_dangqianlianjiex:
    '當前連接 xxx 正在作為FDM和MDM的存儲使用，刪除會導致已有存儲數據丟失，是否確認要繼續刪除。 ',
  packages_business_connections_list_zhengzaizuoweiF:
    '正在作為FDM和MDM的存儲使用，修改會導致已有存儲數據丟失，是否確認要繼續修改',
  packages_business_connections_list_dangqianlianjie: '當前連接',
  packages_business_components_conditionbox_chakanzidingyi: '查看自定義字段',
  packages_business_components_fielddialog_ziduanbuyunxu: '字段不允許為空',
  packages_business_components_fielddialog_zidingyiziduan: '自定義字段',
  packages_business_verification_list_biaobufenziduan: '表部分字段校驗',
  packages_business_components_conditionbox_laiyuanbiaoshuju: '來源表數據過濾',
  packages_business_components_conditionbox_mubiaobiaoshuju: '目標表數據過濾',
  packages_business_components_conditionbox_enableCustomCommand_tip:
    '需要保證查詢條件有索引，如果沒索引會產生全表掃描導致數據庫壓力變大',
  packages_business_data_server_list_apIwendang: 'API文檔導出',
  packages_business_verification_form_gaojipeizhi: '高級配置',
  packages_business_verification_form_validate_table_is_empty:
    '源表和目標表不能為空，請修改校驗表配置',
  packages_business_verification_form_validate_table_is_empty1:
    '因為找不到源表或目標表，以下來源連接將會自動跳過校驗：',
  packages_business_verification_form_condition_is_empty:
    '關聯校驗條件不能為空，請修改校驗表配置',
  packages_business_verification_form_index_field_is_empty:
    '因為找不到索引字段，以下來源表將會自動跳過校驗：',
  packages_business_verification_form_index_field_count_is_not_equal:
    '因為源表與目標表的索引字段個數不相等，以下來源表將會自動跳過校驗：',
  packages_business_verification_list_renyibiaoshuju: '任意表數據校驗',
  packages_business_verification_list_renwuyizhixing: '任務一致性校驗',
  packages_business_permissionse_settings_create_quanxianshezhi: '權限設置',
  packages_business_permissionse_settings_create_shezhiquanxian: '設置權限',
  packages_business_permissionse_settings_create_xuanzeshouquanjiao:
    '選擇授權角色',
  packages_business_permissionse_settings_create_wufaduiyixiashujujinxingshouquan:
    '無法對以下數據進行授權，將跳過保存',
  packages_business_connections_permissionsdialog_tianjiashouquan: '添加授權',
  packages_business_connections_permissionsdialog_gongnengquanxian: '功能權限',
  packages_business_connections_permissionsdialog_shouquanjuese: '授權角色',
  packages_business_connections_permissionsdialog_lianjiequanxianshe:
    '連接權限設置',
  packages_business_connections_preview_quanxianguanli: '權限管理',
  packages_business_connections_preview_shujulianjiequan: '數據連接權限',
  packages_business_notice_list_gonggaobiaoti: '公告標題',
  packages_business_connections_list_wuquanxiandecao: '無權限的操作已被隱藏',
  packages_business_components_upgradecharges_dingyuexinyinqing: '訂閱新引擎',
  packages_business_components_upgradecharges_shengjiguige: '升級規格',
  packages_business_components_upgradecharges_dingyuefangshi: '訂閱方式',
  packages_business_components_upgradecharges_keyongrenwushu: '剩餘可用任務數',
  packages_business_components_upgradecharges_dangqianguige: '當前規格',
  packages_business_components_upgradecharges_dingyueshengji: '訂閱升級',
  packages_business_create_connection_sceneform_lianjieceshiwu:
    '連接測試無效，請檢查您的連接配置',
  packages_business_create_connection_sceneform_qingxianjinxinglian:
    '請先進行連接測試',
  packages_business_logs_nodelog_qingshengjidingyue:
    '請升級訂閱以獲取更多任務數量，點擊彈窗顯示升級引導',
  packages_business_logs_nodelog_yinqingkeyibei:
    '引擎可以被調用的任務超過了限制數，',
  packages_business_task_list_nindekeyunxing:
    '您的可運行任務數已達上限，請訂閱升級規格，以便您運行更多的任務！',
  packages_business_setting_alarmsetting_qubangding: '去綁定',
  packages_business_setting_alarmsetting_jiancedaoninhai:
    '檢測到您還未綁定郵箱，無法開啟郵件通知。',
  packages_business_verification_form_zhengzaijiyuren:
    '正在基於任務產生校驗條件',
  packages_business_agent_ip_tips_prefix:
    '請在防火牆中允許這些 TapData IP 訪問數據庫端口，並確保權限設置正確',
  packages_business_agent_ip_tips_suffix: '點擊查看全托管Agent的IP地址信息',
  packages_business_demo_database_desc:
    'Demo 數據源，可快速創建數據源信息，無需準備數據庫信息即可體驗。',
  packages_business_use_ssl: '使用 SSL',
  packages_business_certificate_authority: 'CA 文件',
  packages_business_client_certificate: '客戶端證書文件',
  packages_business_client_key: '客戶端密鑰文件',
  packages_business_client_key_password: '客戶端密鑰密碼',
  packages_business_use_ssh: '使用 SSH 隧道',
  packages_business_ssh_host: '主機名',
  packages_business_ssh_port: '端口',
  packages_business_ssh_username: '用戶名',
  packages_business_ssh_password: '密碼',
  packages_business_connections_test_xiazaijindu: '下載進度',
  packages_business_connections_test_xiazaishibai: '下載失敗',
  packages_business_relmig_import: 'MongoDB Relmig 導入',
  packages_business_relmig_import_desc: `這個功能旨在無縫導入 MongoDB 關係遷移器導出的 relmig 項目文件到 ${import.meta.env.VUE_APP_PAGE_TITLE} 。在 relmig 文件被導入後，${import.meta.env.VUE_APP_PAGE_TITLE} 將自動創建一個任務來執行源數據庫的實時數據同步，並將其轉換為 MongoDB 數據庫中的 JSON 數據格式。`,
  packages_business_relmig_upload: '上傳 relmig 文件',
  packages_business__relmig_import_connection_tip:
    '如果您還沒有創建，請點擊這裡',
  packages_business__relmig_import_source_connection_placeholder:
    '請選擇包含您在 relmig 項目中使用的源表的源連接',
  packages_business__relmig_import_target_connection_placeholder:
    '請選擇您希望數據同步到的目標連接',
  packages_business_task_tag_placeholder:
    '為這個任務分配一個標籤，以便您能夠輕鬆找到它',
  packages_business_paid_connector: '付費數據源',
  packages_business_more_free_connector: '更多免費數據源',
  packages_business_request_connector_title: '試用 Alpha/Beta 數據源',
  packages_business_request_connector_pending: '審批中',
  packages_business_request_connector_pending_desc: '您已提交申請，請等待審批',
  packages_business_request_connector_alert:
    '👋 歡迎試用 {qcType} 版本的 {type} 數據源，填寫表單後即可開始試用。\n💁 為了獲得最佳體驗，請提供準確的聯繫方式。我們將主動聯繫您，提供支援和幫助。',
  packages_business_request_connector_use_plan: '您計劃使用此數據源的場景',
  packages_business_request_connector_use_plan_placeholder:
    '請填寫您的使用場景',
  packages_business_request_connector_use_time: '預計使用時間',
  packages_business_request_connector_use_time_option1: '5天',
  packages_business_request_connector_use_time_option2: '半年',
  packages_business_request_connector_use_time_option3: '1年',
  packages_business_request_connector_success:
    '我們收到了您的請求，很快就會有人與您聯繫。',
  packages_business_view_more_apis: '查看更多API',
  packages_business_verification_hashTip: '暫不支持異構數據庫',
  packages_business_heterogeneous_database: '異構數據庫',
  packages_business_selected_rows: '已選 {val} 行',
  packages_business_download_analysis_report: '分析報告',
  packages_business_download_analysis_report_title: '任務分析報告生成中...',
  packages_business_download_analysis_report_desc:
    '報告產生大約需要 60s, 在下載後, 請發送給支援團隊進行分析',
  packages_business_exporting_task: '正在導出任務',
  packages_business_exporting_run_history: '正在導出任務運行歷史記錄',
  packages_business_exporting_task_log: '正在導出任務日誌',
  packages_business_exporting_metrics: '正在導出監控指標',
  packages_business_gen_engine_cpu_chart: '正在生成引擎 CPU 分析圖',
  packages_business_gen_tm_cpu_chart: '正在生成管理端 CPU 分析圖',
  packages_business_gen_engine_mem_chart: '正在生成引擎 內存分配 分析圖',
  packages_business_gen_tm_mem_chart: '正在生成管理端 內存分配 分析圖',
  packages_business_exporting_engine_thread: '正在導出引擎線程數據',
  packages_business_exporting_tm_thread: '正在導出管理端線程數據',
  packages_business_downloading_file: '正在下載文件',
  packages_business_long_wait: '請稍候',
  packages_business_correction: '修復',
  packages_business_data_correction: '一鍵修復',
  packages_business_confirmExecuteDataRepair: '確認執行數據修復嗎？',
  packages_business_checkTaskInfo: '校驗任務信息',
  packages_business_taskName: '任務名稱',
  packages_business_taskStatus: '任務狀態',
  packages_business_taskIncrementDelay: '任務增量延遲',
  packages_business_checkDetails: '校驗詳情',
  packages_business_diffThreshold: '差異閾值',
  packages_business_diffTotal: '差異總數',
  packages_business_diffExceededAlert:
    '差異總數已超過閾值，超過的部分將不予修復',
  packages_business_correctionDetails: '修復詳情',
  packages_business_correctionDataVolume: '修複數據行數',
  packages_business_correctionTableCount: '修復表數量',
  packages_business_correctionTaskStarted: '修復任務已開始',
  packages_business_sourceOnly: '目標少數據',
  packages_business_targetOnly: '目標多數據',
  packages_business_no_data_correction: '沒有可修復的數據',
  packages_business_recovering: '修復中',
  packages_business_business_information: '業務信息',
  packages_business_publish_api: '發佈API',
  packages_business_field_description: '字段描述',
  packages_business_shared_cache_enforceShareCdc:
    '當共享挖掘不可用(緩存啓動時)',
  packages_business_shared_cache_enforceShareCdc_true: '緩存直接報錯停止',
  packages_business_not_support_validation: '{connection} 不支持{method}',
  packages_business_download_details: '下載詳情',
  packages_business_solution: '解決方案',
  packages_business_error_details: '錯誤詳情',
  packages_business_instance_info: '連接唯一標識',
  packages_business_warning_details: '警告詳情',
  packages_business_custom_collate: '自定義排序',
  packages_business_please_select_field: '請選擇字段',
  packages_business_please_input_charset: '請輸入字符集',
  packages_business_auto_fill_join_fields: '智能填充关联条件',
  packages_business_auto_fill_join_tooltip_title:
    '開啓後，系統將按以下優先級自動填充關聯條件：',
  packages_business_auto_fill_join_tooltip_primary: '1. 優先使用主鍵字段',
  packages_business_auto_fill_join_tooltip_notnull:
    '2. 如無主鍵，則使用非空字段',
  packages_business_auto_fill_join_tooltip_all:
    '3. 如無非空字段，則使用全部字段',
  packages_business_nulls_first: 'NULL優先排序',
  packages_business_nulls_first_tip:
    '關聯字段存在NULL值時，數據庫默認將NULL排在最後，可能導致校驗失敗。開啓此選項將NULL值排在前面，但可能無法使用數據庫索引，增加數據庫負載。',
  packages_business_ignoreTimePrecision: '忽略時間精度',
  packages_business_ignoreTimePrecision_tip: `<p>開啓後，當源表與目標表時間精度不一致時，系統會統一為較低精度後再比對。</p>
<p>可選擇：</p>
<ul class="pl-4">
  <li class="list-disc">四捨五入（如：1267 微秒 → 127 毫秒）</li>
  <li class="list-disc">截斷（如：1267 微秒 → 126 毫秒）</li>
</ul>
<p>適用於高精度同步至低精度字段的場景。</p>
<p>若精度相同但存儲精度不同（如 Sybase 為約 3.33ms），系統會忽略超出部分。</p>`,
  packages_business_checkTableThreadNum: '校驗線程數量',
  packages_business_checkTableThreadNum_tip:
    '校驗線程數量，在資源充足的情況下可進行調整，默認線程數為 10',
  packages_business_verification_empty_add_table: '暫無校驗表配置，請添加表',
  packages_business_verification_empty_auto_add_table:
    '暫無校驗表配置，請自動添加表',
  packages_business_verification_empty_chooseJob: '暫無校驗表配置，請選擇任務',
  packages_business_custom_mail_template: '自定義郵件模板',
  packages_business_alarm_type: '告警類型',
  packages_business_mail_title: '郵件主題',
  packages_business_mail_content: '郵件正文',
  packages_business_available_variables: '可用變量',
  packages_business_click_variable_name_insert_template:
    '點擊變量名稱插入到模板中',
  packages_business_ignoreTimePrecision_round: '四捨五入',
  packages_business_ignoreTimePrecision_truncate: '截斷',
  packages_business_validation_task_type: '校驗任務類型',
  packages_business_select_task_to_be_verified: '選擇被校驗的任務',
  packages_business_drag_file_here: '拖拽 .gz 文件至此或 <em>選擇文件上傳</em>',
  packages_business_request_speed_limit: '每秒請求限制',
  packages_business_request_speed_limit_tag: '每秒請求 ≤ {val}',
  packages_business_request_speed_limit_tip: '默認是 0（表示不限制）',
}
