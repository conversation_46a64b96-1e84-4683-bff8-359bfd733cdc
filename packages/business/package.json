{"name": "@tap/business", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "src/index.js", "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@formily/reactive": "^2.3.0", "@tap/api": "workspace:^1.0.0", "@tap/assets": "workspace:^1.0.0", "@tap/component": "workspace:^1.0.0", "@tap/form": "workspace:^1.0.0", "@tap/i18n": "workspace:^1.0.0", "@tap/shared": "workspace:^1.0.0", "axios": "^0.21.1", "cron-parser": "^4.6.0", "dayjs": "^1.11.2", "juice": "^11.0.1", "lodash": "^4.17.15", "qs": "^6.11.0", "tiny-emitter": "^2.1.0", "vue": "^3.0.0", "vue-json-viewer": "^2.2.15", "vue-virtual-scroller": "2.0.0-beta.8", "vuex": "^4.0.2"}}