export default {
  packages_component_transfer_titles_0: 'List 1',
  packages_component_transfer_titles_1: 'List 2',
  packages_component_filter_placeholder: 'Kindly input the searching words..',
  packages_component_loading: 'Loading',
  packages_component_no_match: 'No matching data found',
  packages_component_function_type_option_custom: 'Custom Function',
  packages_component_function_type_option_jar: 'Third-party jar package',
  packages_component_function_type_option_system: 'System Function',
  packages_component_shared_cache: 'Pipeline Data Cache',
  packages_component_dataFlow_selectAll: 'Select All',
  packages_component_button_button: 'Add',
  packages_component_message_cancel: 'Cancel',
  packages_component_classification_title: 'Data Category',
  packages_component_classification_userTitle: 'User Group',
  packages_component_classification_creatUserGroup: 'Create user group',
  packages_component_classification_creatDataClassification: 'Create data classification',
  packages_component_classification_nameExist: 'Category name already existed.',
  packages_component_classification_addNode: 'Add tag at the same level',
  packages_component_classification_addChildernNode: 'Add Child Tag',
  packages_component_classification_nodeName: 'Please enter tag name',
  packages_component_classification_deteleMessage:
    'This operation will remove all sub-categories currently present in this category. Are you sure you want to proceed with the deletion?',
  packages_component_formBuilder_file_placeholder: 'Please select a file',
  packages_component_formBuilder_file_button: 'Select',
  packages_component_src_discoveryclassification_qingshurumulu: 'Please enter a directory description',
  packages_component_src_discoveryclassification_mulumiaoshu: 'Catalog Description',
  packages_component_src_discoveryclassification_ziyuanmulu: 'Resource Directory',
  packages_component_src_discoveryclassification_mulufenlei: 'Catalog Classification',
  packages_component_src_discoveryclassification_mulumingcheng: 'Directory Name',
  packages_component_src_discoveryclassification_suoyoumulu: 'All directories',
  packages_component_filter_bar_datetimerange_jieshushijian: 'End Time',
  packages_component_filter_bar_datetimerange_kaishishijian: 'Start Time',
  packages_component_filter_bar_datetimerange_zhi: 'To',
  packages_component_form_builder_file_shangchuanwenjianda: 'The upload file size cannot exceed {val1}KB',
  packages_component_src_inlineinput_zifuchangduxian: 'Character length limit {val1}-{val2} characters',
  packages_component_src_selectlist_meiyougengduoshu: 'No more data',
  packages_component_src_classification_chuangjianfenlei: 'New Tag',
  packages_component_src_discoveryclassification_morenmuluji: 'Default Directory (Technology)',
  packages_component_src_selectlist_qingchuyixuan: 'Clear Selected',
  packages_component_src_selectlist_xiang: 'Item',
  packages_component_src_selectlist_yixuanze: 'Selected',

  packages_component_src_upgradefee_dingyuezhuanyeban: 'Professional Plan',
  packages_component_src_upgradefee_gaojishouhouzhi: 'Technical Support:  Premium With SLA',
  packages_component_src_upgradefee_shujuchulixing: 'Throughput: Higher',
  packages_component_src_upgradefee_desc_1: 'Everything included in Free Plan, Plus...',
  packages_component_src_upgradefee_renwushukegen: 'Max Concurrently Tasks: Unlimited',
  packages_component_src_upgradefee_taocanfufei: 'Pay-As-You-Go',
  packages_component_src_upgradefee_tigongzhuanyehua: 'For business and production use',
  packages_component_src_upgradefee_xianshiyouhui: 'Limited-time Discount',
  packages_component_src_upgradefee_zhuanyeban: 'Professional Plan',
  packages_component_src_upgradefee_dangqianbanben: 'Current Plan',
  packages_component_src_upgradefee_biaozhunshouhouzhi: 'Technical Support: Standard',
  packages_component_src_upgradefee_ge: '2',
  packages_component_src_upgradefee_zuidarenwushu: 'Max Concurrently Tasks: ',
  packages_component_src_basic_component: 'Basic Data Replica and Development Features',
  packages_component_src_upgradefee_mianfei: 'Free',
  packages_component_src_upgradefee_tigongmianfeishi: 'For personal use or testing purposes',
  packages_component_src_upgradefee_jichuban: 'Basic Plan',
  packages_component_src_upgradefee_dingyueshengji: 'Professional Plan',
  packages_component_data_already_exists: 'Data already exists'
}
