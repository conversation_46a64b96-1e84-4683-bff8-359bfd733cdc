export default {
  packages_component_transfer_titles_0: '列表 1',
  packages_component_transfer_titles_1: '列表 2',
  packages_component_filter_placeholder: '请输入搜索内容',
  packages_component_loading: '加载中',
  packages_component_no_match: '无匹配数据',
  packages_component_function_type_option_custom: '自定义函数',
  packages_component_function_type_option_jar: '第三方jar包',
  packages_component_function_type_option_system: '系统函数',
  packages_component_shared_cache: '共享缓存',
  packages_component_dataFlow_selectAll: '全选',
  packages_component_button_button: '新增',
  packages_component_message_cancel: '取 消',
  packages_component_classification_title: '数据分类',
  packages_component_classification_userTitle: '用户组',
  packages_component_classification_creatUserGroup: '创建用户组',
  packages_component_classification_creatDataClassification: '创建数据分类',
  packages_component_classification_nameExist: '分类名称已存在',
  packages_component_classification_addNode: '新增同级标签',
  packages_component_classification_addChildernNode: '新增子标签',
  packages_component_classification_nodeName: '请输入标签名称',
  packages_component_classification_deteleMessage: '此操作会将该标签下存在的子标签都删除，是否删除',
  packages_component_formBuilder_file_placeholder: '请选择文件',
  packages_component_formBuilder_file_button: '选择文件',
  packages_component_src_discoveryclassification_qingshurumulu: '请输入目录描述',
  packages_component_src_discoveryclassification_mulumiaoshu: '目录描述',
  packages_component_src_discoveryclassification_ziyuanmulu: '资源目录',
  packages_component_src_discoveryclassification_mulufenlei: '目录分类',
  packages_component_src_discoveryclassification_mulumingcheng: '目录名称',
  packages_component_src_discoveryclassification_suoyoumulu: '所有目录',
  packages_component_filter_bar_datetimerange_jieshushijian: '结束时间',
  packages_component_filter_bar_datetimerange_kaishishijian: '开始时间',
  packages_component_filter_bar_datetimerange_zhi: '至',
  packages_component_form_builder_file_shangchuanwenjianda: '上传文件大小不能超过 {val1}KB',
  packages_component_src_inlineinput_zifuchangduxian: '字符长度限制{val1}-{val2}个字符',
  packages_component_src_selectlist_meiyougengduoshu: '没有更多数据',
  packages_component_src_classification_chuangjianfenlei: '新建标签',

  packages_component_src_upgradefee_dingyuezhuanyeban: '专业版',
  packages_component_src_upgradefee_gaojishouhouzhi: ' 高级售后支持，提供服务SLA',
  packages_component_src_upgradefee_shujuchulixing: ' 处理性能：高',
  packages_component_src_upgradefee_desc_1: '包含基础版的全部能力, 加上...',
  packages_component_src_upgradefee_renwushukegen: '任务数量: 无限制',
  packages_component_src_upgradefee_taocanfufei: '按规格付费',
  packages_component_src_upgradefee_tigongzhuanyehua: '适合企业使用, 或者生产场景使用',
  packages_component_src_upgradefee_xianshiyouhui: '限时优惠',
  packages_component_src_upgradefee_zhuanyeban: '专业版 ',
  packages_component_src_upgradefee_dangqianbanben: '当前版本',
  packages_component_src_upgradefee_biaozhunshouhouzhi: '标准售后支持',
  packages_component_src_upgradefee_ge: '2个',
  packages_component_src_upgradefee_zuidarenwushu: '最大并发任务数: ',
  packages_component_src_basic_component: '支持基本的数据复制与开发特性',
  packages_component_src_upgradefee_mianfei: '免费',
  packages_component_src_upgradefee_tigongmianfeishi: '适合个人使用, 或者测试场景使用',
  packages_component_src_upgradefee_jichuban: '基础版',
  packages_component_src_upgradefee_dingyueshengji: '专业版',
  packages_component_src_selectlist_qingchuyixuan: '清除已选',
  packages_component_src_selectlist_xiang: '项',
  packages_component_src_selectlist_yixuanze: '已选择',
  packages_component_data_already_exists: '数据已存在'
}
