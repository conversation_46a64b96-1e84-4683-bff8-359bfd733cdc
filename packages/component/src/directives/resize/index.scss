$borderColor: #d3d3d3;
$selectedColor: #d3d3d3;
$primary: map.get($color, primary);

.resize-trigger {
  background-color: transparent;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.3);
  z-index: 1990;

  &:before {
    content: '';
    position: absolute;
    background-color: transparent;
  }

  &.--left,
  &.--right {
    top: 0;
    width: 6px;
    height: 100%;
    cursor: col-resize;
    transform: translateX(-50%);
    &:before {
      top: 0;
      left: 2px;
      width: 2px;
      height: 100%;
    }
  }

  &.--left {
    left: 0;
  }

  &.--right {
    left: 100%;
  }

  &.--top,
  &.--bottom {
    left: 0;
    height: 6px;
    width: 100%;
    cursor: row-resize;
    transform: translateY(-50%);
    &:before {
      left: 0;
      top: 2px;
      width: 100%;
      height: 2px;
    }
  }

  &.--top {
    top: 0;
  }

  &.--bottom {
    top: 100%;
  }

  &.active,
  &:hover {
    &:before {
      background-color: $primary;
    }
  }
}
