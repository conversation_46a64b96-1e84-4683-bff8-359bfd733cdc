import { defineComponent } from 'vue'
import VIcon from '../base/VIcon.vue'
import './style.scss'

export const IconButton = defineComponent({
  name: 'IconButton',
  props: {
    iconSize: [Number, String],
    xs: <PERSON><PERSON><PERSON>,
    sm: <PERSON><PERSON><PERSON>,
    md: <PERSON><PERSON><PERSON>,
    lg: <PERSON><PERSON><PERSON>,
    xl: <PERSON><PERSON><PERSON>,
    clickAndRotate: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON><PERSON>,
    loading: <PERSON><PERSON><PERSON>,
  },
  setup(props, { attrs, slots }) {
    return () => {
      return (
        <button
          disabled={props.disabled || props.loading}
          class={[
            't-button t-button--icon',
            {
              't-button--icon-xs': props.xs,
              't-button--icon-sm': props.sm,
              't-button--icon-md':
                props.md || (!props.xs && !props.sm && !props.lg && !props.xl),
              't-button--icon-lg': props.lg,
              't-button--icon-xl': props.xl,
              't-button__rotating': props.clickAndRotate,
            },
          ]}
          type="button"
          {...attrs}
        >
          <VIcon class={{ 'animation-rotate-fast': props.loading }}>
            {slots.default()}
          </VIcon>
        </button>
      )
    }
  },
})
