.t-button {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: #646a73;
  flex-shrink: 0;
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
  user-select: none;
  outline: none;
  border: 1px solid #fff;
  background: none;
  transition: color 0.1s ease-in, background-color 0.1s ease-in, border-color 0.1s ease-in, width 0.2s ease-in;
  touch-action: manipulation;
  text-decoration: none;
  &--icon {
    padding: 4px;
    min-width: 0;
    height: auto;
    border: none;
    line-height: 0;
    border-radius: 6px;

    &:hover,
    &.active {
      background: rgba(31, 35, 41, 0.1);
      border-color: rgba(0, 0, 0, 0);
    }

    &:disabled,
    &:disabled:active,
    &:disabled:hover,
    &:disabled[aria-expanded],
    &[disabled],
    &[disabled]:active,
    &[disabled]:hover,
    &[disabled][aria-expanded] {
      color: rgb(187, 191, 196) !important;
      background: rgba(0, 0, 0, 0) !important;
      border-color: rgba(0, 0, 0, 0) !important;
      cursor: not-allowed !important;
    }

    &-xs {
      font-size: 12px;
    }

    &-sm {
      font-size: 16px;
    }

    &-md {
      font-size: 20px;
    }

    &-lg {
      font-size: 24px;
    }

    &-xl {
      font-size: 28px;
    }
  }

  &__primary {
    color: map.get($color, primary);
    &:hover {
      background: rgb(59 71 229 / 10%);
    }
  }

  &__rotating {
    .v-icon {
      transform: rotate(360deg);
      transition: 1s ease-in-out;
    }
    &:active .v-icon {
      transform: rotate(0);
      transition: 0s;
    }
  }
}
