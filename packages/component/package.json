{"name": "@tap/component", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@tap/api": "workspace:^1.0.0", "@tap/assets": "workspace:^", "@tap/i18n": "workspace:^1.0.0", "@tap/shared": "workspace:^", "@tiptap/core": "^2.12.0", "@tiptap/extension-bold": "^2.12.0", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-code-block": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-italic": "^2.12.0", "@tiptap/extension-list-item": "^2.12.0", "@tiptap/extension-paragraph": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/vue-3": "^2.12.0", "ace-builds": "^1.4.13", "core-js": "^3.8.3", "dayjs": "^1.11.2", "echarts": "^5.0.2", "highlight.js": "^11.0.1", "lodash": "^4.17.15", "markdown-it": "^13.0.1", "tiny-emitter": "^2.1.0", "vue": "^3.0.0", "vue-echarts": "^6.0.0", "vue-virtual-scroll-list": "^2.3.4", "vue-virtual-scroller": "2.0.0-beta.8", "vuex": "^4.0.2"}}