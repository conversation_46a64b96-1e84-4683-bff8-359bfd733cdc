{"name": "@tap/ldp", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@tap/api": "workspace:^1.0.0", "@tap/assets": "workspace:^1.0.0", "@tap/business": "workspace:^1.0.0", "@tap/component": "workspace:^1.0.0", "@tap/dag": "workspace:^1.0.0", "@tap/form": "workspace:^1.0.0", "@tap/i18n": "workspace:^1.0.0", "@tap/shared": "workspace:^1.0.0", "@vueuse/core": "^10.5.0", "axios": "^0.21.1", "core-js": "^3.8.3", "dagre": "^0.8.5", "dayjs": "^1.11.2", "tiny-emitter": "^2.1.0", "vue-virtual-scroller": "2.0.0-beta.8", "vuex": "^4.0.2"}}