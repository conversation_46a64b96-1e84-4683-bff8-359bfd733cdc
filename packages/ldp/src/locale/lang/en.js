export default {
  packages_ldp_lineage: 'Lineage',
  packages_ldp_order_fully_managed_storage: 'Buy Now',
  packages_ldp_connection_expired: 'Storage has expired, please reset.',
  packages_ldp_view_lineage: 'View Lineage',
  packages_ldp_lineage_loading_tips: 'Double-click the node to drill down.',
  packages_ldp_table_comment: 'Table Comment',
  packages_ldp_src_dashboard_anEsctui: 'Press Esc to exit the traceability scene',
  packages_ldp_src_tablepreview_querenshanchu: 'Confirm to delete? ',
  packages_ldp_src_tablepreview_gaibiaojianghuicong:
    'The table will be deleted from the database and cannot be recovered after the operation',
  packages_ldp_src_tablepreview_jiancedaoyouren:
    'It is detected that a task is using {val1}, please delete all related tasks and try again',
  packages_ldp_src_target_muqianzhichide: 'Currently supported types',
  packages_ldp_upgrade_storage: 'Upgrade Storage',
  packages_ldp_data_hub_intro_title: 'What is Real Time Data Hub?',
  packages_ldp_data_hub_intro_desc1:
    'A data hub allows you to consolidate mission critical data from siloed sources into centralized storage, then provide fresh data to downstream applications or dashboards from a single location.',
  packages_ldp_data_hub_intro_desc2:
    'Tapdata uses CDC technology to sync the data from source and uses MongoDB / MongoDB Atlas as hub storage, to achieve the near real time data latency experiecne.',
  packages_ldp_data_hub_intro_scene_title: 'Which Use Cases Can I Use Real Time Data Hub?',
  packages_ldp_data_hub_intro_scene_single_view: 'Single View',
  packages_ldp_data_hub_intro_scene_single_view_sub: 'Products ｜ Customers ｜ Orders',
  packages_ldp_data_hub_intro_scene_realtime: 'Real Time',
  packages_ldp_data_hub_intro_scene_realtime_sub: 'Dashboards  ｜  Reports',
  packages_ldp_data_hub_intro_scene_api: 'Enterprise API Service',
  packages_ldp_data_hub_intro_scene_api_sub: 'Database to API',
  packages_ldp_data_hub_intro_how_do: 'How Does It Work?',
  packages_ldp_data_hub_intro_how_do_step1: 'Configure Data Hub Storage',
  packages_ldp_data_hub_intro_how_do_step1_sub:
    'First configure a data storage in the cloud, Tapdata uses MongoDB Atlas.',
  packages_ldp_data_hub_intro_how_do_step2: 'Sync Data & Consolidate',
  packages_ldp_data_hub_intro_how_do_step2_sub:
    'Use Tapdata Replication & Transformation, sync data from your data sources into Data Hub.',
  packages_ldp_data_hub_intro_how_do_step3: 'Publish API or Send to Dashboards',
  packages_ldp_data_hub_intro_how_do_step3_sub:
    'from data hub, or connect to the Bl product of your choice You may then publish API.',
  packages_ldp_data_hub_subscribe: 'Configure Storage',
  page_title_data_hub: 'Real Time Data Hub',
  packages_ldp_source_empty_text: '1. Create your own data source first',
  packages_ldp_target_empty_text: '2. Next, create your target database.',
  packages_ldp_not_support_increments: 'Current source data does not support increments.',
  packages_ldp_drag_source_table_to_start: 'Drag the source table here to start copying.',
  packages_ldp_run_only_once: 'Run only once',
  packages_ldp_run_every_10_minutes: 'Run every 10 minutes',
  packages_ldp_run_every_hour: 'Run every 1 hour',
  packages_ldp_run_every_day: 'Run every day',
  packages_ldp_custom_cron_expression: 'Custom cron expression',
  packages_ldp_view_task_monitor: 'View Task Monitor',
  packages_ldp_book_demo: 'Feel free to Book a Demo with us',
  packages_ldp_mdm_create_method: 'Create Method',
  packages_ldp_mdm_create_method_transformation: 'Use Data Transformation Task',
  packages_ldp_mdm_create_method_materialized: 'Use Materialized View'
}
