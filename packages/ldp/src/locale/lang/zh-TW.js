export default {
  packages_ldp_lineage: '血緣',
  packages_ldp_lineage_loading_tips: '雙擊節點可以下鑽',
  packages_ldp_order_fully_managed_storage: '订购全托管存储',
  packages_ldp_connection_expired: '存儲已失效，請重新設置',
  packages_ldp_view_lineage: '查看血緣',
  packages_ldp_table_comment: '表註釋',
  packages_ldp_src_dashboard_anEsctui: '按Esc退出溯源場景',
  packages_ldp_src_tablepreview_querenshanchu: '確認刪除？ ',
  packages_ldp_src_tablepreview_gaibiaojianghuicong: '該表將會從數據庫裡刪除，操作後不可恢復',
  packages_ldp_src_tablepreview_jiancedaoyouren: '檢測到有任務正在使用 {val1}，請刪除所有相關任務後重試',
  packages_ldp_src_target_muqianzhichide: '目前支持的類型',
  packages_ldp_upgrade_storage: '升級存儲',
  packages_ldp_data_hub_intro_title: '甚麼是實時數據平台',
  packages_ldp_data_hub_intro_desc1:
    '數據中心允許組織連接不同的數據源，並將這些整合的數據從一個統一的、集中的位置提供給多個應用程序或用戶。',
  packages_ldp_data_hub_intro_desc2:
    'Tapdata采用CDC技術從數據源同步數據，使用MongoDB / MongoDB Atlas作為中心存儲，實現近乎實時的數據延遲體驗。',
  packages_ldp_data_hub_intro_scene_title: '哪些應用場景可以使用實時數據平台?',
  packages_ldp_data_hub_intro_scene_single_view: '單一視圖',
  packages_ldp_data_hub_intro_scene_single_view_sub: ' 產品 ｜ 客戶 ｜ 訂單',
  packages_ldp_data_hub_intro_scene_realtime: '實時',
  packages_ldp_data_hub_intro_scene_realtime_sub: '數據儀表板 ｜ 報表',
  packages_ldp_data_hub_intro_scene_api: '企業API服務',
  packages_ldp_data_hub_intro_scene_api_sub: '數據庫到API',
  packages_ldp_data_hub_intro_how_do: '實時數據平台是如何工作的?',
  packages_ldp_data_hub_intro_how_do_step1: '配置數據中心存儲',
  packages_ldp_data_hub_intro_how_do_step1_sub: '首先，在雲端配置一個數據存儲，Tapdata 使用 MongoDB Atlas。',
  packages_ldp_data_hub_intro_how_do_step2: '同步數據並合併',
  packages_ldp_data_hub_intro_how_do_step2_sub: '使用 Tapdata 的複製和轉換功能，將數據從您的數據源同步到數據中心。',
  packages_ldp_data_hub_intro_how_do_step3: '發布API或發送到儀表板',
  packages_ldp_data_hub_intro_how_do_step3_sub: '發布API或發送到儀表板',
  packages_ldp_data_hub_subscribe: '訂閱存儲',
  page_title_data_hub: '實時數據平台',
  packages_ldp_source_empty_text: '1. 建立來源資料庫',
  packages_ldp_target_empty_text: '2. 建立目標資料庫',
  packages_ldp_not_support_increments: '當前源數據不支持增量',
  packages_ldp_drag_source_table_to_start: '將源表拖入此處開始複製',
  packages_ldp_run_only_once: '僅運行一次',
  packages_ldp_run_every_10_minutes: '每10分鐘運行一次',
  packages_ldp_run_every_hour: '每1小時運行一次',
  packages_ldp_run_every_day: '每天運行一次',
  packages_ldp_custom_cron_expression: '自定義cron表達式',
  packages_ldp_view_task_monitor: '查看任務監控',
  packages_ldp_book_demo: '預約演示',
  packages_ldp_mdm_create_method: '創建方式',
  packages_ldp_mdm_create_method_transformation: '使用數據轉換任務',
  packages_ldp_mdm_create_method_materialized: '使用建模向導'
}
