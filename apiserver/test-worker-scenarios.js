/**
 * 测试不同 Worker 数量场景下的限速效果
 * 模拟不同的 API_WORKER_COUNT 环境变量值
 */

const { RateLimiter } = require('./dist/src/rate-limiter');

console.log('🚀 不同 Worker 数量场景测试\n');

// 测试场景配置
const scenarios = [
    { workers: 1, description: "单 Worker 场景" },
    { workers: 2, description: "双 Worker 场景" },
    { workers: 4, description: "四 Worker 场景" },
    { workers: 8, description: "八 Worker 场景" },
    { workers: 16, description: "十六 Worker 场景" }
];

const testConfigs = [
    { limit: 10, description: "中等限制 (10 QPS)" },
    { limit: 20, description: "较高限制 (20 QPS)" },
    { limit: 5, description: "较低限制 (5 QPS)" },
    { limit: 1, description: "最低限制 (1 QPS)" }
];

console.log('📊 Worker 限制分配测试');
console.log('=' .repeat(80));
console.log('| Workers | Config Limit | Per Worker | Total Effective | Description');
console.log('|---------|--------------|------------|-----------------|-------------');

scenarios.forEach(scenario => {
    testConfigs.forEach(config => {
        // 模拟不同的 worker 数量
        const originalEnv = process.env.API_WORKER_COUNT;
        process.env.API_WORKER_COUNT = scenario.workers.toString();
        
        // 创建新的限速器实例来测试
        const testRateLimiter = new (require('./dist/src/rate-limiter').RateLimiter)();
        
        // 计算每个 worker 的限制
        const perWorkerLimit = Math.max(1, Math.floor(config.limit / scenario.workers));
        const totalEffective = perWorkerLimit * scenario.workers;
        
        console.log(`| ${scenario.workers.toString().padEnd(7)} | ${config.limit.toString().padEnd(12)} | ${perWorkerLimit.toString().padEnd(10)} | ${totalEffective.toString().padEnd(15)} | ${config.description}`);
        
        // 恢复环境变量
        if (originalEnv) {
            process.env.API_WORKER_COUNT = originalEnv;
        } else {
            delete process.env.API_WORKER_COUNT;
        }
    });
});

console.log('\n');

// 实际限速测试
console.log('🧪 实际限速效果测试');
console.log('=' .repeat(50));

async function testWorkerScenario(workerCount, configLimit) {
    console.log(`\n📋 测试场景: ${workerCount} Worker(s), 配置限制 ${configLimit} QPS`);
    
    // 设置环境变量
    const originalEnv = process.env.API_WORKER_COUNT;
    process.env.API_WORKER_COUNT = workerCount.toString();
    
    // 创建新的限速器实例
    const testRateLimiter = new (require('./dist/src/rate-limiter').RateLimiter)();
    
    const apiId = `test-api-${workerCount}-${configLimit}`;
    const expectedPerWorker = Math.max(1, Math.floor(configLimit / workerCount));
    const expectedTotal = expectedPerWorker * workerCount;
    
    console.log(`   期望每个 Worker: ${expectedPerWorker} QPS`);
    console.log(`   期望总限制: ${expectedTotal} QPS`);
    
    // 测试请求
    let allowedCount = 0;
    let blockedCount = 0;
    
    console.log('   测试请求:');
    for (let i = 1; i <= configLimit + 5; i++) {
        const isAllowed = testRateLimiter.checkRateLimit(apiId, { limit: configLimit });
        const status = isAllowed ? '✅' : '❌';
        
        if (isAllowed) {
            allowedCount++;
        } else {
            blockedCount++;
        }
        
        if (i <= 10) { // 只显示前10个请求的详细结果
            console.log(`     请求 ${i}: ${status}`);
        }
        
        // 如果连续被限制，提前结束
        if (blockedCount >= 3 && allowedCount > 0) {
            break;
        }
    }
    
    console.log(`   结果: 允许 ${allowedCount}, 被限制 ${blockedCount}`);
    
    // 验证结果
    if (allowedCount === expectedPerWorker) {
        console.log('   ✅ 测试通过: 限制符合预期');
    } else {
        console.log(`   ❌ 测试异常: 期望 ${expectedPerWorker}, 实际 ${allowedCount}`);
    }
    
    // 恢复环境变量
    if (originalEnv) {
        process.env.API_WORKER_COUNT = originalEnv;
    } else {
        delete process.env.API_WORKER_COUNT;
    }
}

// 运行测试场景
async function runTests() {
    // 测试几个关键场景
    await testWorkerScenario(1, 10);   // 单 worker, 10 QPS
    await testWorkerScenario(4, 10);   // 4 workers, 10 QPS -> 每个 2 QPS
    await testWorkerScenario(8, 10);   // 8 workers, 10 QPS -> 每个 1 QPS
    await testWorkerScenario(4, 3);    // 4 workers, 3 QPS -> 每个 1 QPS (最小值)
    
    console.log('\n🎯 测试总结:');
    console.log('1. Worker 数量可以通过 API_WORKER_COUNT 环境变量控制');
    console.log('2. 限制会自动按 worker 数量分配，确保总限制不超过配置值');
    console.log('3. 每个 worker 至少保证 1 QPS');
    console.log('4. 使用 start.sh -p <数量> 可以方便地设置 worker 数量');
    
    console.log('\n📝 使用示例:');
    console.log('  ./start.sh -p 1    # 启动 1 个 worker');
    console.log('  ./start.sh -p 4    # 启动 4 个 worker');
    console.log('  ./start.sh         # 使用默认数量 (CPU 核心数)');
    
    console.log('\n🎉 测试完成!');
}

runTests().catch(console.error);
