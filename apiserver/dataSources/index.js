'use strict';

const DataSource = require('./dataSource');

class DataSourceFactory {

  createDataSource (connection) {
    // enum[mongodb, mysql, postgresql, redis, oracle, db2, mssql]
    const type = this._getConnectionType(connection.database_type);
    switch (type) {
      case 'db2':
      case 'mongodb':
      case 'mssql':
      case 'mysql':
      case 'oracle':
      case 'postgresql':
        return new DataSource(connection, type);
      default:
        throw new Error(`unsupported data source type ${type}`);
    }
  }

  _getConnectionType (type) {
    if (!type) return 'mongodb';
    const lower = type.toLowerCase();
    switch (lower) {
      case 'postgres':
        return 'postgresql';
      case 'sqlserver':
        return 'mssql';
      default:
        return lower;
    }
  }
}

module.exports = new DataSourceFactory();
