Contributing
============

The main development is on GitHub at http://github.com/mcollina/msgpack5.
In order to contribute, fork the repo on github and send a pull requests with topic branches.
Do not forget to provide tests for your contribution.

Contact the lead dev
--------------------

You can reach [@matteocollina](http://twitter.com/matteocollina) on
twitter of via <NAME_EMAIL>.

Running the tests
-------------

* Fork and clone the repository
* Run `npm install`
* Run `npm test`


Coding guidelines
----------------

Adopt the prevailing code style in the repository.
This project use [JSHint](http://www.jshint.com/) to validate the
source code formatting with a pre commit hook: please respect that.


Contribution License Agreement
----------------

Project license: MIT

* You will only Submit Contributions where You have authored 100% of
  the content.
* You will only Submit Contributions to which You have the necessary
  rights. This means that if You are employed You have received the
  necessary permissions from Your employer to make the Contributions.
* Whatever content You Contribute will be provided under the Project
  License.
