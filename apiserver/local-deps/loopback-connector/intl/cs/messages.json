{"063f254115448e65526f70948c52587a": "Parametr {{getColumnsToAdd()}} mus<PERSON> být implementován konektorem", "06c539e08213828e62e7f5339fb255ec": "Parametr {{getPlaceholderForValue()}} mus<PERSON> být implementován konektorem", "17ca61bd6b497b0292b64f3689003583": "Parametr {{setNullableProperty}} mus<PERSON> b<PERSON>t implementován konektorem", "18b43e87956af1710152edafd66cee94": "Parametr {{buildQueryForeignKeys}} mus<PERSON> být implementován konektorem", "23e31dd386179ad5c3fda1d1563c55dc": "Parametr {{buildColumnType()}} mus<PERSON> být implementován konektorem", "2b051e39b1efd1f2106b155495087b0f": "Hodnota ID je povinná.", "2c3c1f5278d78000d113d6a7c18a7880": "Parametr {{alterTable()}} mus<PERSON> b<PERSON>t implementován konektorem", "2e0742dd07c3d744ec412a821c74ac30": "Parametr {{buildQueryViews}} mus<PERSON> být implementován konektorem", "30a4fa7fc115109b8cd096d895e9869e": "Parametr {{getPlaceholderForIdentifier()}} mus<PERSON> být implementován konektorem", "414e87ad36e87ec903abfadcf464e82d": "Parametr {{getDefaultSchema}} mus<PERSON> být implementován konektorem", "4cbf9bb7e7347b86c1dba4e54163c4ea": "Parametr {{getCountForAffectedRows()}} mus<PERSON> být implementován konektorem", "579886cc3b92b69f084b961f21adc003": "Parametr {{getArgs}} mus<PERSON> b<PERSON>t <PERSON>n konektorem", "6389c414179020263769484a2c11e8bd": "Parametr {{buildPropertyType}} mus<PERSON> být implementován konektorem", "64e3028e6e54d2328eb3f2321a5bcce4": "Parametr {{paginateSQL}} mus<PERSON> být implementován konektorem", "71a6ec1e7d216074ac90cc2bf801f969": "Parametr {{buildQueryTables}} mus<PERSON> být implementován konektorem", "760e951d2f37e40e0d4936efd97d56ee": "Parametr {{buildQueryPrimaryKeys}} mus<PERSON> být implementován konektorem", "7fd6d4275df0622d69e3d05dd22a8d2f": "Parametr {{buildQueryExportedForeignKeys}} musí být implementován konektorem", "80a32e80cbed65eba2103201a7c94710": "Model nebyl nalezen: {0}", "871b10e82a2dc1d0659571c5dee8fa95": "Parametr {{executeSQL()}} mus<PERSON> být implementován konektorem", "87431e613cf77076158f52a456cb0777": "Neplatný k<PERSON> {0} - chybí předpona názvu modelu", "8dc8fd7cc38a39896c03663cca38aad5": "Parametr execute() musí být implementován konektorem", "956ede286087599213bc112b5a76408b": "Parametr {{applyPagination()}} mus<PERSON> být implementován konektorem", "a0cf0e09c26df14283223e84e6a10f00": "Nelze aktualizovat atributy. {{Object}} s {{id}} {0} neexistuje!", "aa7504db641c08e5f5c1525751aef044": "Parametr {{setDefaultOptions}} mus<PERSON> být implementován konektorem", "b71717c454bc1db796a4c5363d9fd078": "Nelze provést nahrazení. Objekt s ID {0} neexistuje!", "bca74e4fbb2859c22381fb531a0f896d": "Parametr {{getInsertedId()}} mus<PERSON> být implementován konektorem", "c491cd5577a54bf265d407ddaa20ffcb": "Parametr {{escapeValue()}} mus<PERSON> b<PERSON>t <PERSON>n konektorem", "c575904db17542907e7150d2c8ad6f36": "Parametr {{showFields()}} mus<PERSON> b<PERSON>t <PERSON>n konektorem", "c635b8365e02134d16c2309e66a5773a": "Parametr {{getColumnsToDrop()}} mus<PERSON> být implementován konektorem", "d480d9fd521ea577183bb3463f76a608": "Parametr {{showIndexes()}} mus<PERSON> být implementován konektorem", "e54d944c2a2c85a23caa86027ae307cf": "<PERSON><PERSON><PERSON>grovat modely, k<PERSON><PERSON> nej<PERSON> připoje<PERSON> k tomuto zdroji dat: {0}", "f16cd32194dd5bd785d014a8ba1b91aa": "Parametr {{buildQueryColumns}} mus<PERSON> být implementován konektorem", "f3e021460b7a6f5991fca890b786a11b": "Parametr {{escapeName()}} mus<PERSON> b<PERSON>t implementován konektorem", "fe1c27354af9f4c88bf9c1d246e41230": "Parametr {{toColumnValue()}} mus<PERSON> být <PERSON>n konektorem", "fe914555563f5f566adfc0c5db8445db": "Parametr {{fromColumnValue()}} mus<PERSON> být <PERSON>n konektorem"}