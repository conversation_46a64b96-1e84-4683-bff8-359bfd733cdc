{"063f254115448e65526f70948c52587a": "{{getColumnsToAdd()}} deve ser implementado pelo conector", "06c539e08213828e62e7f5339fb255ec": "{{getPlaceholderForValue()}} deve ser implementado pelo conector", "17ca61bd6b497b0292b64f3689003583": "{{setNullableProperty}} deve ser implementado pelo conector", "18b43e87956af1710152edafd66cee94": "{{buildQueryForeignKeys}} deve ser implementado pelo conector", "23e31dd386179ad5c3fda1d1563c55dc": "{{buildColumnType()}} deve ser implementado pelo conector", "2b051e39b1efd1f2106b155495087b0f": "Valor de ID é obrigatório", "2c3c1f5278d78000d113d6a7c18a7880": "{{alterTable()}} deve ser implementado pelo conector", "2e0742dd07c3d744ec412a821c74ac30": "{{buildQueryViews}} deve ser implementado pelo conector", "30a4fa7fc115109b8cd096d895e9869e": "{{getPlaceholderForIdentifier()}} deve ser implementado pelo conector", "414e87ad36e87ec903abfadcf464e82d": "{{getDefaultSchema}} deve ser implementado pelo conector", "4cbf9bb7e7347b86c1dba4e54163c4ea": "{{getCountForAffectedRows()}} deve ser implementado pelo conector", "579886cc3b92b69f084b961f21adc003": "{{getArgs}} deve ser implementado pelo conector", "6389c414179020263769484a2c11e8bd": "{{buildPropertyType}} deve ser implementado pelo conector", "64e3028e6e54d2328eb3f2321a5bcce4": "{{paginateSQL}} deve ser implementado pelo conector", "71a6ec1e7d216074ac90cc2bf801f969": "{{buildQueryTables}} deve ser implementado pelo conector", "760e951d2f37e40e0d4936efd97d56ee": "{{buildQueryPrimaryKeys}} deve ser implementado pelo conector", "7fd6d4275df0622d69e3d05dd22a8d2f": "{{buildQueryExportedForeignKeys}} deve ser implementado pelo conector", "80a32e80cbed65eba2103201a7c94710": "Modelo não localizado: {0}", "871b10e82a2dc1d0659571c5dee8fa95": "{{executeSQL()}} deve ser implementado pelo conector", "87431e613cf77076158f52a456cb0777": "Chave {0} inválida - prefixo do nome do modelo ausente", "8dc8fd7cc38a39896c03663cca38aad5": "execute() deve ser implementado pelo conector", "956ede286087599213bc112b5a76408b": "{{applyPagination()}} deve ser implementado pelo conector", "a0cf0e09c26df14283223e84e6a10f00": "Não foi possível atualizar atributos. {{Object}} com {{id}} {0} não existe!", "aa7504db641c08e5f5c1525751aef044": "{{setDefaultOptions}} deve ser implementado pelo conector", "b71717c454bc1db796a4c5363d9fd078": "Não foi possível substituir. O objeto com o ID {0} não existe!", "bca74e4fbb2859c22381fb531a0f896d": "{{getInsertedId()}} deve ser implementado pelo conector", "c491cd5577a54bf265d407ddaa20ffcb": "{{escapeValue()}} deve ser implementado pelo conector", "c575904db17542907e7150d2c8ad6f36": "{{showFields()}} deve ser implementado pelo conector", "c635b8365e02134d16c2309e66a5773a": "{{getColumnsToDrop()}} deve ser implementado pelo conector", "d480d9fd521ea577183bb3463f76a608": "{{showIndexes()}} deve ser implementado pelo conector", "e54d944c2a2c85a23caa86027ae307cf": "Não é possível migrar modelos não conectados a esta origem de dados: {0}", "f16cd32194dd5bd785d014a8ba1b91aa": "{{buildQueryColumns}} deve ser implementado pelo conector", "f3e021460b7a6f5991fca890b786a11b": "{{escapeName()}} deve ser implementado pelo conector", "fe1c27354af9f4c88bf9c1d246e41230": "{{toColumnValue()}} deve ser implementado pelo conector", "fe914555563f5f566adfc0c5db8445db": "{{fromColumnValue()}} deve ser implementado pelo conector"}