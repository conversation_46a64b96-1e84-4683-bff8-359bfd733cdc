<!DOCTYPE html>
<html lang="">
  <head>
	  <meta charset="utf-8" />
	  <meta name="keywords" content="Tapdata,SaaS,data integration,data replication,data governance,data publish" />
	  <meta
		  name="description"
		  content="tapdata cloud, 以可视化的形式完成数据的实时迁移, 同步与计算过程, 帮助用户构建自己的数据中台"
	  />
	  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
	  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
	  <meta http-equiv="Pragma" content="no-cache" />
	  <meta http-equiv="Expires" content="0" />
	  <meta name="version" content="1.0.0-A-134-gc9a78b5" />
	  <title>loading...</title>
	  <link rel="shortcut icon" href="/static/favicon/%VUE_APP_FAVICON%" />
	  <style>
		.el-loading-mask {
			position: absolute;
			z-index: 2000;
			background-color: rgba(255, 255, 255, 0.9);
			margin: 0;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			transition: opacity 0.3s;
		}
		.el-loading-mask.is-fullscreen {
			position: fixed;
		}
		.el-loading-spinner {
			top: 50%;
			margin-top: -21px;
			width: 100%;
			text-align: center;
			position: absolute;
		}
		.el-loading-spinner .circular {
			height: 50px;
			width: 50px;
			animation: loading-rotate 2s linear infinite;
		}
		.el-loading-spinner .path {
			animation: loading-dash 1.5s ease-in-out infinite;
			stroke-dasharray: 90, 150;
			stroke-dashoffset: 0;
			stroke-width: 2;
			stroke: #3b47e5;
			stroke-linecap: round;
		}
		@keyframes loading-rotate {
			100% {
				transform: rotate(360deg);
			}
		}
		@keyframes loading-dash {
			0% {
				stroke-dasharray: 1, 200;
				stroke-dashoffset: 0;
			}
			50% {
				stroke-dasharray: 90, 150;
				stroke-dashoffset: -40px;
			}
			100% {
				stroke-dasharray: 90, 150;
				stroke-dashoffset: -120px;
			}
		}
	  </style>
  </head>
  <body>
    <noscript>
	    <strong>We're sorry but this app doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
	    <div class="el-loading-mask is-fullscreen" style="z-index: 3000">
			<div class="el-loading-spinner">
				<svg viewBox="25 25 50 50" class="circular">
				<circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
				</svg>
			</div>
		</div>
    </div>
    <!-- built files will be auto injected -->
    <script type="module" src="/src/main.js"></script>
  </body>
</html>