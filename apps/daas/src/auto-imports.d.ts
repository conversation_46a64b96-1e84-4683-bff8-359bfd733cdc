/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const ElIcon: typeof import('element-plus/es')['ElIcon']
  const ElIconCopyDocument: typeof import('@element-plus/icons-vue')['CopyDocument']
  const ElIconRefresh: typeof import('@element-plus/icons-vue')['Refresh']
  const ElIconSearch: typeof import('@element-plus/icons-vue')['Search']
  const ElInput: typeof import('element-plus/es')['ElInput']
  const ElLoading: typeof import('element-plus/es')['ElLoading']
  const ElMessage: typeof import('element-plus/es')['ElMessage']
  const ElMessageBox: typeof import('element-plus/es')['ElMessageBox']
  const ElSwitch: typeof import('element-plus/es')['ElSwitch']
  const IconLucideClock: typeof import('~icons/lucide/clock')['default']
  const IconLucideFileText: typeof import('~icons/lucide/file-text')['default']
  const IconLucideHash: typeof import('~icons/lucide/hash')['default']
  const IconLucideTriangleAlert: typeof import('~icons/lucide/triangle-alert')['default']
  const IconMingcuteCheckCircleFill: typeof import('~icons/mingcute/check-circle-fill')['default']
  const IconMingcuteCloseCircleFill: typeof import('~icons/mingcute/close-circle-fill')['default']
  const IconMingcuteInformationFill: typeof import('~icons/mingcute/information-fill')['default']
  const IconMingcuteWarningFill: typeof import('~icons/mingcute/warning-fill')['default']
}
