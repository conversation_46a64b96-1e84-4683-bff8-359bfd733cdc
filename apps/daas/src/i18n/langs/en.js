export default {
  // 通用按钮
  button_reload: 'Reload',
  button_button: 'Add',
  // 通用消息
  message_loading: 'Loading',
  message_network_connected: 'Network is restored',
  //页面标题
  page_title_overview: 'Overview',
  page_title_dashboard: 'Dashboard',
  page_title_connections: 'Connections',
  page_title_connections_create: 'Create a connection',
  page_title_connections_edit: 'Edit connection',
  page_title_data_pipeline: 'Data Pipelines',
  page_title_advanced_features: 'Advanced',
  page_title_data_copy: 'Data Replication',
  page_title_task_edit: 'Edit task',
  page_title_task_details: 'Task details',
  page_title_task_stat: 'Task statistics',
  page_title_run_monitor: 'Run Monitoring',
  page_title_data_develop: 'Data Transformation',
  page_title_data_verify: 'Data Validation',
  page_title_data_difference_details: 'Difference Details',
  page_title_data_verification_result: 'Verification Result',
  page_title_diff_verification_history: 'Diff verification history',
  page_title_diff_verification_details: 'Diff verification details',
  page_title_shared_mining: 'CDC Log Cache',
  page_title_heartbeat_table: 'Heartbeat Task',
  page_title_shared_mining_details: 'Mining details',
  page_title_function: 'Function List',
  page_title_function_import: 'Import function',
  page_title_function_create: 'Create function',
  page_title_function_edit: 'Edit function',
  page_title_function_details: 'Function details',
  page_title_shared_cache: 'Live Cache',
  page_title_shared_cache_create: 'Create cache',
  page_title_shared_cache_edit: 'Edit cache',
  page_title_data_discovery: 'Data Discovery',
  page_title_data_object: 'Data Object',
  page_title_data_catalogue: 'Data Catalog',
  page_title_data_service: 'Data Services',
  page_title_data_server_list: 'API List',
  page_title_api_application: 'Application List',
  page_title_api_client: 'API Clients',
  page_title_api_servers: 'API Servers',
  page_title_api_audit: 'API Audit',
  page_title_api_audit_details: 'Details',
  page_title_api_monitor: 'API Status',
  page_title_system: 'System',
  page_title_data_metadata: 'Metadata',
  page_title_cluster: 'Cluster',
  page_title_user: 'Users',
  page_title_role: 'Roles',
  page_title_setting: 'System settings',
  page_title_webhook_alerts: 'Webhook Alerts',
  page_title_license: 'License',
  page_title_back_menu: 'Back',
  page_title_custom_node: 'Custom Processors',
  page_title_account: 'Personal settings',
  page_title_external_storage: 'External Storage',
  page_title_verification_create: 'New Validation',
  page_title_verification_edit: 'Edit Verification',
  page_title_verification_history: 'Verification History',
  page_title_data_console: 'Data Console',
  // -- 多表选择器
  component_table_selector_candidate_label: 'To be selected',
  component_table_selector_checked_label: 'Selected ',
  component_table_selector_error_not_exit: 'Table does not exist',
  component_table_selector_error: 'Selected tables has exceptions',
  component_table_selector_autofix: 'Clear exception tables',
  component_table_selector_bulk_pick: 'Bulk pick',
  component_table_selector_not_checked: 'You have not selected a table',
  component_table_selector_tables_empty:
    'You do not have a table at the moment, please click on the upper right corner to reload the table',
  component_table_selector_clipboard_placeholder:
    'Please enter table names separated by commas, for example: table_a, table_b',
  // app
  app_license_expire_warning: 'License expires in {0} days remaining',
  // Agent
  agent_check_error:
    "Agent's current state is abnormal and cannot create a connection, please check",
  // console
  dashboard_status_paused: 'Paused',
  dashboard_status_wait_run: 'Scheduled',
  dashboard_all_total: 'All tasks',
  dashboard_copy_total: 'Replication Task',
  dashboard_sync_total: 'Transformation Task',
  dashboard_valid_total: 'Validation Task',
  dashboard_current_all_total: 'Grand total of all tasks',
  dashboard_current_copy_total: 'Total number of replication tasks',
  dashboard_current_sync_total: 'Total number of transformation tasks',
  dashboard_current_valid_total: 'Total number of validation tasks',
  dashboard_copy_overview_title: 'Replication task overview',
  dashboard_copy_status_title: 'Replication task status',
  dashboard_sync_overview_title: 'Transformation task overview',
  dashboard_sync_status_title: 'Transformation task status',
  dashboard_valid_title: 'Data validation overview',
  dashboard_transfer_overview: 'Transfer overview',
  dashboard_server_title: 'Cluster overview',
  dashboard_total_valid: 'All verification tasks',
  dashboard_passed: 'Consistent verification',
  dashboard_countDiff: 'Count inconsistent',
  dashboard_valueDiff: 'Content Difference',
  dashboard_initializing: 'Initializing',
  dashboard_initialized: 'Initialization complete',
  dashboard_cdc: 'Increasing',
  dashboard_Lag: 'Incremental lag',
  dashboard_server: 'Server',
  dashboard_management: 'Management side',
  dashboard_task_transfer: 'Task transfer',
  dashboard_api_service: 'API service',
  dashboard_starting: 'Starting',
  dashboard_running: 'Running',
  dashboard_stopping: 'Closing',
  dashboard_stopped: 'Closed',
  dashboard_restarting: 'Restarting',
  dashboard_total_insert: 'Total insert',
  dashboard_total_update: 'Total update',
  dashboard_total_delete: 'Total delete',
  dashboard_public_error: 'Error',
  dashboard_public_total: 'Total',
  dashboard_total: 'Number of verification tasks enabled',
  dashboard_diff: 'Check the number of tasks with differences',
  dashboard_can: 'Number of verification tasks supported',
  dashboard_error: 'Number of tasks with errors in verification',
  dashboard_no_data_here: 'There is no data here~',
  dashboard_no_statistics: 'No {0} statistics yet',
  // 元数据管理
  metadata_db: 'Owned library',
  metadata_change_name: 'Rename',
  metadata_name_placeholder: 'Please enter the table name/database name',
  metadata_meta_type_directory: 'Directory',
  metadata_meta_type_table: 'Data table',
  metadata_form_create: 'Create model',
  metadata_form_database: 'Database',
  metadata_form_collection: 'Dataset',
  metadata_form_mongo_view: 'Mongodb view',
  metadata_detail_original_table_name: 'Original table name',
  metadata_detail_original_database_name: 'Original database name',
  // api release api发布
  modules_active: 'Released',
  modules_pending: 'Unpublished',
  modules_create: 'Create API',
  modules_import: 'import',
  modules_api_test: 'API test',
  modules_publish_api: 'Publish API',
  modules_unpublish_api: 'Unpublish',
  modules_dialog_import_title: 'Task import',
  modules_dialog_condition: 'Condition',
  modules_dialog_overwrite_data: 'Overwrite existing data',
  modules_dialog_skip_data: 'Skip existing data',
  modules_dialog_group: 'Group',
  modules_dialog_file: 'File',
  modules_dialog_upload_files: 'Upload files',
  modules_header_api_name: 'API name',
  modules_header_dataSource: 'Data Source',
  modules_header_tablename: 'table name',
  modules_header_status: 'Status',
  modules_header_basePath: 'Base Path',
  modules_header_classifications: 'Classification',
  modules_header_username: 'creator',
  modules_status_deploying: 'Deploying',
  modules_status_starting: 'Starting',
  modules_status_running: 'Running',
  modules_status_restart: 'Update in progress',
  modules_status_deploy_fail: 'Publishing API failed',
  modules_status_exit: 'Exited',
  modules_status_stop: 'Stopped',
  modules_status_ready: 'valid',
  modules_status_invalid: 'invalid',
  modules_allacancel: 'Batch cancel',
  modules_allarelease: 'Batch release',
  modules_releasefb:
    'Are you sure you want to release the following APIs in batches?',
  modules_releasecancel:
    'Are you sure you want to cancel the following APIs in batches?',
  modules_api_server_status: 'API Service Status',
  modules_sure: 'Are you sure you want',
  modules_cancel_failed: 'Unpublished API failed',
  modules_name_placeholder: 'Please enter the table name/database name',
  module_form_connection: 'Database',
  module_form_tablename: 'table name',
  module_form_default_Api: 'Default CURD API',
  module_form_customer_Api: 'Custom API',
  module_form_noPath: 'Please add a path',
  module_form_prefix: 'Prefix',
  module_form_basePath: 'Base Path',
  module_form_path: 'Path',
  module_form_security: 'Permission Settings',
  module_form_permission: 'Permission',
  module_form_choose: 'Choose directory',
  module_form_document: 'API document',
  module_form_tags: 'Data Directory',
  module_form_preview: 'Data Preview',
  module_form_public_api: 'Public',
  module_form_available_query_field: 'Available query field',
  module_form_required_query_field: 'Required query conditions',
  module_form_validator_name:
    'Can only contain letters, numbers, underscores and dollar signs, and numbers cannot start with',
  module_form_create_a_new_record: 'Create a new record',
  module_form_get_record_by_id: 'Get records based on id',
  module_form_update_record_by_id: 'Update record according to id',
  module_form_delete_record_by_id: 'Delete records based on id',
  module_form_get_record_list_by_page_and_limit: 'Get records by page',
  module_form_method: 'Method',
  module_form_fields: 'Fields',
  module_form_datatype: 'Data Type',
  module_form_condition: 'Filter condition',
  module_form_get_api_uri_fail: 'Failed to get API Server Uri',
  module_form_name_null: 'The name cannot be empty',
  query_build_match_condition: 'Match condition',
  query_build_any: 'any',
  query_build_addGroup: 'Add group',
  query_build_removeGroup: 'Remove group',
  query_build_addCondition: 'Add Condition',
  query_build_removeCondition: 'Remove Condition',
  query_build_show_filter: 'Show filter conditions',
  query_build_queryValue: 'Field value',
  query_build_add: 'Add',
  // client 客户端
  application_header_id: 'Client ID',
  application_header_client_name: 'Client name',
  application_header_grant_type: 'Grant Type',
  application_header_client_secret: 'Client Secret',
  application_header_redirect_uri: 'Redirect URI',
  application_header_scopes: 'Permission scope',
  application_generator: 'Generate',
  application_show_menu: 'Show to the menu',
  application_true: 'Yes',
  application_false: 'No',
  application_create: 'Create a client',
  //api 监控
  api_monitor_total_totalCount: 'Total number of APIs',
  api_monitor_total_warningApiCount: 'Total API Access',
  api_monitor_total_warningVisitCount: 'Total API Access Warning',
  api_monitor_total_visitTotalLine: 'The total number of API access lines',
  api_monitor_total_transmitTotal: 'API transmission total',
  api_monitor_total_warningCount: 'API warning count',
  api_monitor_total_successCount: 'API Successful count',
  api_monitor_total_columns_failed: 'Failure rate (%)',
  api_monitor_total_FailRate: 'API failure rate TOP sort',
  api_monitor_total_consumingTime: 'API response time TOP sort',
  api_monitor_total_rTime: 'Maximum Response time',
  api_monitor_total_clientName: 'client',
  api_monitor_total_api_list: 'API list',
  api_monitor_total_api_list_name: 'API name',
  api_monitor_total_api_list_status: 'API status',
  api_monitor_total_api_list_visitLine: 'Number of API access lines',
  api_monitor_total_api_list_visitCount: 'Number of API visits',
  api_monitor_total_api_list_transitQuantity: 'API access transfer volume',
  api_monitor_total_api_list_status_active: 'Active',
  api_monitor_total_api_list_status_pending: 'Pending',
  api_monitor_total_api_list_status_generating: 'Generating',
  api_monitor_detail_visitTotalCount: 'Number of Successful API visits',
  api_monitor_detail_visitQuantity: 'API transfer amount',
  api_monitor_detail_timeConsuming: 'API access time',
  api_monitor_detail_visitTotalLine: 'Number of API access lines',
  api_monitor_detail_speed: 'API transfer rate',
  api_monitor_detail_responseTime: 'API response time',
  api_monitor_detail_monitoring_period: 'Monitoring period',
  api_monitor_detail_Monitoring_conditions: 'Monitoring conditions',
  // api server api浏览
  api_server_user: 'User',
  api_server_create: 'New server',
  api_server_create_server: 'Create API',
  api_server_process_id: 'API server unique ID',
  api_server_client_name: 'API server name',
  api_server_client_uri: 'API server access address',
  api_server_download_API_Server_config: 'Download API configuration file',
  api_server_no_available: 'No API server available',
  // api browse api浏览
  dataExplorer_query: 'Query',
  dataExplorer_document: 'API Document',
  dataExplorer_query_time: 'Query usage',
  dataExplorer_render_time: 'Rendering use',
  dataExplorer_tag_title: 'Set tag',
  dataExplorer_show_column: 'Show column',
  dataExplorer_new_document: 'New record',
  dataExplorer_timeout: 'Timeout time',
  dataExplorer_unauthenticated:
    'You do not have permission to access the API. ',
  dataExplorer_message_timeout:
    'The connection to the API server has timed out, please check whether the API server has been started. ',
  dataExplorer_publish:
    'The API of {id} was not found on the API server. Please check if it has been published. ',
  dataExplorer_no_permissions:
    'Your token has expired, please refresh the page and try again. ',
  dataExplorer_datetype_without_timezone: 'Time zone of time type (optional)',
  dataExplorer_mysql_datetype_without_timezone: 'Type of impact: DATETIME',
  dataExplorer_export: 'Export file',
  dataExplorer_add_favorite: 'Favorites',
  dataExplorer_add_favorite_name: 'Favorite name',
  dataExplorer_format: 'Format code',
  dataExplorer_apiservr: 'Api server',
  dataExplorer_base_path: 'Base path',
  // server audit 服务审计
  apiaudit_name: 'API name',
  apiaudit_access_type: 'Access Type',
  apiaudit_visitor: 'Visitor',
  apiaudit_ip: 'Visitor IP',
  apiaudit_interview_time: 'Access Time',
  apiaudit_visit_result: 'Result',
  apiaudit_reason_fail: 'Failure reason',
  apiaudit_log_info: 'Log details',
  apiaudit_parameter: 'parameter',
  apiaudit_link: 'Link',
  apiaudit_access_records: 'Number of access records',
  apiaudit_average_access_rate: 'API average access rate',
  apiaudit_access_time: 'Access time',
  apiaudit_average_response_time: 'Average response time',
  apiaudit_success: 'success',
  apiaudit_placeholder: 'Please enter name/ID',
  // 连接
  connection_list_form_database_type: 'Database type',
  connection_list_name: 'Connection name',
  connection_form_agent_msg:
    "Agent's current state is abnormal and cannot create a connection, please check",
  connection_reload_schema_confirm_title: 'Reload schema',
  connection_reload_schema_confirm_msg:
    'If there are too many schemas in this library, it may take a long time. Make sure to refresh the schema of the data source',
  connection_reload_schema_fail: 'Schema load failed',
  //Dag
  //Task edit缓存节点提示
  task_list_status_all: 'All status',
  task_list_important_reminder: 'Important reminder',
  task_list_stop_confirm_message:
    'Pausing job while it is in the full sync stage may cause it to run from the beginning, are you sure you want to pause?',
  task_status_running: 'Running',
  task_status_not_running: 'Not running',
  task_info_progress: 'Running',
  //字段映射
  migrate_select_connection_tip:
    "If you haven't added a data source, please click the Add Data Source button to add it. In order to facilitate your testing, we recommend that the number of data sources should be at least 2",
  // Function management
  function_button_import_jar: 'Import',
  function_details: 'Function details',
  function_tips_name_repeat: 'Method name repeat',
  function_type_label: 'Function type',
  function_type_option_custom: 'Custom function',
  function_type_option_jar: 'Third-party jar package',
  function_type_option_system: 'System function',
  function_name_label: 'Function name',
  function_name_placeholder: 'Please enter the function name',
  function_name_repeat: 'Function name repeat',
  function_class_label: 'Class name',
  function_file_label: 'jar file',
  function_button_file_upload: 'Click to upload',
  function_file_upload_tips: 'Please upload the jar package file',
  function_file_upload_success: 'Upload successful',
  function_file_upload_fail: 'Upload failed',
  function_parameters_describe_label: 'Parameter description',
  function_parameters_describe_placeholder:
    'Support input parameter types and specific description of return parameter types',
  function_return_value_label: 'Return value',
  function_return_value_placeholder: 'Please enter the return value',
  function_describe_placeholder: 'Please enter a description',
  function_format: 'Format',
  function_format_placeholder: 'Please enter a format',
  function_jar_file_label: 'Jar file name',
  function_package_name_label: 'Package name',
  function_package_name_placeholder: 'Please enter a package name',
  function_class_name_label: 'Class name',
  function_method_name_label: 'Method name',
  function_script_label: 'Code details',
  function_script_empty: 'Please enter the function code',
  function_script_missing_function_name: 'Missing function name',
  function_script_missing_function_body: 'Missing function body',
  function_script_format_error: 'The function format is incorrect',
  function_script_only_one: 'Only one function is allowed to be created',
  function_import_list_title: 'Function list',
  function_button_load_function: 'Load function',
  function_message_load_function_fail: 'Load function fail',
  function_dialog_setting_title: 'Function setting',
  function_message_function_empty:
    'Please upload the jar file and load the function',
  function_message_delete_title: 'Delete function',
  function_message_delete_content:
    'Deletion may cause the task that has called this function to report an error. Are you sure to delete this function? ',
  function_tips_max_size: 'Max size ',
  // solution解决方案
  solution_customer_job_logs: 'Customer job logs',
  solution_error_code: 'Error code',
  solution_select_placeholder_type: 'Please select the type',
  solution_search_result: 'Result',
  solution_search_solutions: 'Solution',
  // 共享挖掘
  shared_cdc_placeholder_task_name:
    'Please enter the mining task name to search',
  shared_cdc_placeholder_connection_name:
    'Please enter the connection name to search',
  shared_cdc_name: 'Please enter the task name ',
  shared_cdc_setting_select_mode: 'Storage mode',
  shared_cdc_setting_select_mongodb_tip: 'Please enter mongodb connection',
  shared_cdc_setting_select_table_tip: 'Please enter the table name',
  shared_cdc_setting_select_time_tip: 'Please select the log saving time',
  shared_cdc_setting_message_edit_save:
    'Save successfully, it will take effect after restarting the task',
  share_list_name: 'Mining name',
  share_list_time_excavation: 'Excavation time point',
  share_list_setting: 'Mining settings',
  share_list_status: 'Status',
  share_list_time: 'Mining Delay',
  share_list_edit_title: 'Mining Edit',
  share_list_edit_title_start_time: 'Mining Start Time',
  share_form_setting_table_name: 'Store MongoDB table name',
  share_form_setting_log_time: 'Log save time',
  share_form_edit_name: 'Mining name',
  share_form_edit_title: 'Whether to give up editing this mining task',
  share_form_edit_text: 'This operation will not save the modified content',
  share_detail_mining_info: 'Mining information',
  share_detail_name: 'Mining name',
  share_detail_log_mining_time: 'Log mining time',
  share_detail_log_time: 'log storage time',
  share_detail_call_task: 'Call task',
  share_detail_source_time: 'Source library time point',
  share_detail_sycn_time_point: 'Sync time point',
  share_detail_mining_status: 'Mining status',
  share_detail_button_table_info: 'Table details',
  share_detail_statistics_time: 'Statistics time',
  share_detail_incremental_time: 'The time point',
  // 设置
  setting_email_template: 'Email Template',
  setting_saveSuccess: 'Save successfully, take effect in one minute',
  setting_nameserver: 'Server Name',
  setting_Log: 'Log Management',
  setting_SMTP: 'SMTP Settings',
  setting_Job: 'Task Management',
  setting_License: 'License Management',
  setting_expiredate: 'Expiration Date',
  setting_import: 'Import',
  setting_apply: 'Apply for license',
  setting_license_expire_date: 'License expiration time',
  setting_Worker: 'Process',
  setting_Download: 'Download',
  setting_Log_level: 'Log level',
  setting_maxCpuUsage: 'Maximum CPU usage (value range 0.1 ~ 1)',
  setting_maxHeapMemoryUsage: 'Maximum heap memory usage (value range 0.1 ~ 1)',
  setting_switch_insert_mode_interval:
    'Interval time for switching to batch insert mode in incremental mode (unit: second)',
  setting_Email_Communication_Protocol: 'Encryption Method',
  setting_SMTP_Server_Port: 'SMTP service port',
  setting_SMTP_Server_User: 'SMTP service account',
  setting_SMTP_Server_password: 'SMTP service password',
  setting_Email_Receivers: 'Email receiving email address',
  setting_Email_Send_Address: 'Email sending email address',
  setting_SMTP_Server_Host: 'SMTP Service Host',
  setting_Send_Email_Title_Prefix: 'Send Email title prefix (optional)',
  setting_SMTP_Proxy_Host: 'SMTP proxy service host (optional）',
  setting_SMTP_Proxy_Port: 'SMTP proxy service port (optional)',
  setting_Email_Template_Running: 'Task start notification',
  setting_Email_Template_Paused: 'Task Paused Notification',
  setting_Email_Template_Error: 'Task error notification',
  setting_Email_Template_Draft: 'Notification of task being edited',
  setting_Email_Template_CDC: 'Task Replication Delay Notification',
  setting_Email_Template_DDL: 'DDL error notification',
  setting_Clean_Message_Time: 'Clear message time',
  setting_Keep_Alive_Message: 'Keep online message',
  setting_Sample_Rate: 'Sampling rate',
  setting_task_load_threshold: 'Task load threshold (percentage)',
  setting_task_load_statistics_time: 'Task load statistics time (minute)',
  setting_ApiServer: 'API Distribution Settings',
  setting_Default_Limit: 'The number of rows returned by the default query',
  setting_Max_Limit: 'Maximum number of rows returned by the query',
  setting_Send_batch_size: 'Number of packed data',
  setting_hint_Send_batch_size: 'Number of packed data',
  setting_Mongodb_target_create_date:
    'Whether to add the creation time to the target data set',
  setting_Mongodb_target_create_date_docs:
    'Whether to add the creation time to the target data set',
  setting_System: 'System Resource Monitoring',
  setting_Collect_system_info_interval:
    'System resource monitoring collection frequency (seconds)',
  setting_Interval_to_collect_system_info:
    'System resource information (CPU, memory, hard disk usage) monitoring collection frequency',
  setting_Job_Sync_Mode: 'Task synchronization mode',
  setting_Worker_Threshold: 'Process Threshold',
  setting_Worker_Heartbeat_Expire: 'Process heartbeat period time (seconds)',
  setting_License_Key: 'Certificate Key',
  setting_Enter_jobs_log_level__error_warn_info_debug_trace:
    'Enter task log level: error/warn/info/debug/trace',
  setting_Email_Receivers_Multiple_separated_by_semicolons:
    'Mail recipients, you can enter multiple, separated by commas',
  setting_Keep_recent_n_hours_message_before_the_last_processed_message_s_time_:
    'Keep the last n hours news',
  setting_Store_full_record_as_embedded_document_in_target_collection_for_update_operations:
    'Cache a copy of the current overall data and merge it into the target data set',
  setting_Store_before_field_as_embedded_document_in_target_collection_before_update_operation:
    'Cache a copy of the overall data before modification and merge it into the target data set',
  setting_Store_job_script_processor_log_to_cloud:
    'Whether to transfer task logs to the cloud',
  setting_Validator_to_validate_data__s_sample_rate:
    'Validation data sampling rate',
  setting_retry_interval_second: 'Retry Interval(Second)',
  setting_max_retry_time_minute: 'Maximum Retry Time(Minute)',
  setting_Process_message_mode__consistency_fast:
    'Message processing mode consistency/fast',
  setting_Worker_can_execute_the_nums_of_Jobs:
    'The process can perform multiple tasks',
  setting_Worker_heartbeat_expire_time: 'Process heartbeat period time',
  setting_Users: 'User',
  setting_Show_Page: 'Show download page',
  setting_User_Registery: 'User registration management',
  setting_hint_Show_Page: 'Show download page',
  setting_hint_User_Registery:
    'User registration type settings. The value is set to "disabled": registration is prohibited; the value is set to "self-signup" to enable user self-registration; the value is set to "manual-approval" to allow user registration, but requires administrator approval. ',
  setting_DR_Rehearsal: 'Disaster Recovery Drill',
  setting_Mongod_path: 'Mongod path',
  setting_SSH_User: 'SSH username',
  setting_SSH_Port: 'SSH Port',
  setting_hint_Mongod_path: 'Mongod path',
  setting_hint_SSH_User: 'SSH username, used to connect to the host of Mongod',
  setting_hint_SSH_Port: 'SSH port, used to connect to the host of Mongod',
  setting_Enable_DR_Rehearsal: 'Allow disaster recovery exercises',
  setting_hint_Enable_DR_Rehearsal:
    'Disaster recovery rehearsal switch, true means on, false means off',
  setting_Download_Agent_Page: 'Agent Download Page',
  setting_Background_Analytics: 'Background Analysis',
  setting_Data_quality_analysis_frequency:
    'Data quality analysis interval (seconds)',
  setting_Dashboard_data_analysis_frequency:
    'Panel data analysis interval (seconds)',
  setting_dashboard_Analysis_Interval: 'Panel data analysis interval (seconds)',
  setting_quality_Analysis_Interval: 'Data quality analysis interval (seconds)',
  setting_Log_filter_interval: 'Log filtering interval (seconds)',
  setting_Filter_the_interval_between_duplicate_logs__seconds__:
    'The same log appears only once within a specified time (valid after 1 minute)',
  setting__DK36: 'File download',
  setting_File_Down_Base_Url: 'Address',
  setting_Set_the_average_number_of_events_per_second_to_allow:
    'Log settings allow the average number of events per second',
  setting_Log_Filter_Rate: 'Log output frequency (line/sec)',
  setting_Connections: 'Connection Settings',
  setting_Mongodb_Load_Schema_Sample_Size:
    'Mongodb load model sample records (rows)',
  setting_hint_Mongodb_Load_Schema_Sample_Size:
    'When MongoDB connects to load the model, this configuration will be used for sampling and loading',
  setting_Enable_API_Stats_Batch_Report: 'Enable API Statistics',
  setting_Header: 'UDP header information',
  setting_hint_Header: 'UDP header information',
  setting_Size_Of_Trigger_API_Stats_Report:
    'Maximum number of API request cache',
  setting_hint_Size_Of_Trigger_API_Stats_Report:
    'When the number of API request records reaches the specified number, batches are sent to the management end',
  setting_Time_Span_Of_Trigger_API_Stats_Report:
    'API request report frequency (seconds)',
  setting_hint_Time_Span_Of_Trigger_API_Stats_Report:
    'The API request is cached and sent to the management end at the specified time',
  setting_save: 'Save successfully, take effect in one minute',
  setting_Logout_forward_to_this_url: 'Logout forwarding address',
  setting_Check_devices: 'Important device detection',
  setting_ops: 'Operations & Maintenance',
  setting_server_oversee_url: 'O&M Control URL',
  setting_system: 'System Global Settings',
  setting_licenseNoticeDays: 'license expiration reminder',
  setting_license_alarm: 'License expiry advance reminder (days)',
  setting_License_expiry_email_reminder_:
    'License expiry advance reminder settings (days)',
  setting_flow_engine_version: 'Flow engine version',
  setting_tapdata_agent_version: `${import.meta.env.VUE_APP_PAGE_TITLE}  agent version`,
  setting_doc_base_url: 'Help document URL',
  setting_help: 'Help document',
  setting_Ip_addresses: 'Ipv4 addresses (separated by multiple commas)',
  setting_hint_Ip_addresses:
    'The ipv4 address of the device to be detected, for example: 127.0.0.1, ***********',
  setting_PingTimeout: 'Detection timeout (milliseconds)',
  setting_hint_PingTimeout:
    'When this setting is exceeded, it is considered that the device cannot be connected',
  setting_Job_field_replacement: 'Illegal characters replaced with',
  setting_A_replacement_for_the_invalid_field_name:
    'Some databases have special requirements for field names, System will automatically replace illegal characters during synchronization. MongoDB[Contains ".", "$" as the beginning]',
  setting_true__store_log_to_cloud__false__only_store_to_local_log_file_:
    'true: store log to cloud, false: only store to local log file.',
  setting_When_one_document_may_be_updated_frequently_within_very_short_period_a_few_updates_within_one_second__for_instance___the_change_stream_event_received_by_downstream_processor_may_return_the__fullDocument__that_is_inconsistent_with_the_actual_version_when_the_update_was_applied_to_that_document__To_avoid_this_inconsistency__enable_this_option_to_store_the_full_document_along_with_the_update_operation__This_will_at_the_expense_of_additional_storage_and_degraded_performance_:
    'When a document may be frequently updated in a very short time (for example, several updates within a second), the change stream event received by the downstream processor may return "fullDocument" that is inconsistent with the actual version ( Inconsistent with the actual version) the file. To avoid this inconsistency, please enable this option to store the complete document with the update operation. This will be at the expense of increased storage space and reduced performance. ',
  setting_the_before_field_contains_a_field_for_each_table_column_and_the_value_that_was_in_that_column_before_the_update_operation_:
    'the before field contains a field for each table column and the value that was in that column before the update operation.',
  setting_Job_heart_timeout:
    'Synchronization task heartbeat timeout (milliseconds)',
  setting_job_cdc_share_mode: 'Incremental synchronization task sharing mode',
  setting_job_cdc_share_mode_doc:
    'In the incremental synchronization phase, the sharing mode will be automatically adopted according to whether the log collection task is available. Affected database: Oracle',
  setting_job_cdc_share_only: 'Incremental tasks are forced to use shared mode',
  setting_job_cdc_share_only_doc:
    'When the incremental synchronization task sharing mode is turned on and a sharable log cannot be found, the task will be stopped',
  setting_test_email_success:
    'The test email has been sent, please log in to the receiving mailbox to check it',
  setting_test_ldap_success: 'Connected to Ldap service successfully',
  setting_test_email_countdown:
    'The operation is too frequent, please try again later',
  setting_email_template_from: 'From',
  setting_email_template_to: 'Recipient',
  setting_email_template_subject: 'Subject',
  setting_job_cdc_record: 'Automatically save incremental events',
  setting_job_cdc_record_doc: 'Automatically save incremental events',
  setting_job_cdc_record_ttl: 'Incremental event save time (days)',
  setting_job_cdc_record_ttl_doc: 'Incremental event save time (days)',
  setting_lagTime: 'incremental lag decision time (seconds)',
  setting_connection_schema_update_hour: 'Data source schema update time',
  setting_connection_schema_update_interval:
    'Data source schema update interval (days)',
  setting_creatDuplicateSource: 'Allow the creation of duplicate data sources',
  setting_requestFailed: 'Request processing failed',
  setting_Mongodb_will_use_this_sample_size_when_load_schema:
    'Mongodb will use this sample size when load schema When MongoDB connects to load the model, this configuration will be used for sample loading',
  setting_Switch_to_batch_insert_mode_interval__s__in_cdc_:
    'Switch to batch insert mode interval in cdc. ',
  setting_share_cdc: 'Share cdc',
  setting_share_cdc_persistence_mode: 'Share cdc persistence mode',
  setting_share_cdc_persistence_memory_size:
    'Shared incremental memory cache line count',
  setting_share_cdc_persistence_memory_size_doc:
    'This configuration controls the number of lines in the memory cache for shared incremental events',
  setting_share_cdc_persistence_mode_doc:
    'Share cdc persistence mode.Options: InMemory, MongoDB, RocksDB',
  setting_share_cdc_persistence_mongodb_uri_db:
    'Stores the connection name of the MongoDB',
  setting_share_cdc_persistence_mongodb_uri_db_doc:
    'This configuration takes effect only when MongoDB is selected as the mode. Enter the created MongoDB connection name.',
  setting_share_cdc_persistence_mongodb_collection:
    'The name of the collection where MongoDB is stored',
  setting_share_cdc_persistence_mongodb_collection_doc:
    'This configuration takes effect only when MongoDB is selected as the mode, enter the stored collection name',
  setting_share_cdc_persistence_rocksdb_path:
    'The local path to the RocksDB store',
  setting_share_cdc_persistence_rocksdb_path_doc:
    'This configuration takes effect only when RocksDB is selected as the mode, and the local path stored by RocksDB',
  setting_task_log_file_save_time: 'Task log retention time (days)',
  setting_task_log_file_save_size: 'Task log retention size (MB)',
  setting_task_log_file_save_count: 'Task log retention total count',
  setting_agent_log_file_save_time: 'Agent log retention time (days)',
  setting_agent_log_file_save_size: 'Agent log retention size (MB)',
  setting_agent_log_file_save_count: 'Agent log retention total count',
  setting_INCREMENTAL_DELAY_LINE_DATA_COEFFICIENT:
    'Incremental delay line data coefficient',
  setting_Login: 'Login Settings',
  setting_Login_Single_Session: 'Single session login',
  setting_Login_Single_Session_doc:
    'Once enabled, only a single session of login is allowed for the same account',
  setting_Login_Brief_Tips: 'Login brief tips',
  setting_Login_Brief_Tips_doc:
    'Once enabled, the login prompt will be simplified',
  setting_LDAP: 'LDAP Authentication',
  setting_Ldap_Login_Enable: 'Use LDAP Login',
  setting_Ldap_Server_Host: 'LDAP Server Address',
  setting_Ldap_Server_Port: 'LDAP Server Port',
  setting_Ldap_Base_DN: 'LDAP Base DN',
  setting_Ldap_Bind_DN: 'LDAP Account',
  setting_Ldap_Bind_Password: 'LDAP Password',
  setting_Ldap_SSL_Enable: 'Enable SSL',
  setting_Ldap_Server_Host_doc:
    'The domain controller address of AD, e.g., ldap://ad.example.com or ldaps://ad.example.com',
  setting_Ldap_Server_Port_doc:
    'LDAP typically uses port 389, while LDAPS (secure connection) uses port 636',
  setting_Ldap_Base_DN_doc:
    'The starting point of an LDAP query, used to define the search scope in AD, with multiple groups separated by semicolons. Example: cn=Users, dc=example,dc=com;cn=Test,dc=example,dc=com',
  setting_Ldap_Bind_DN_doc:
    'The full Distinguished Name (DN) of the user for authentication, i.e., the identity used to log in to the AD server, e.g., <EMAIL>',
  setting_Ldap_Bind_Password_doc:
    'The password corresponding to the Bind DN, used for authentication',
  user_list_user_name_email: 'Please enter username/email',
  user_list_change_time: 'Modification time',
  user_list_creat_user: 'Create user',
  user_list_edit_user: 'Edit user',
  user_list_user_name: 'username',
  user_list_role: 'Associated role',
  user_list_source: 'source',
  user_list_status: 'Status',
  user_list_activation: 'Activation',
  user_list_freeze: 'Freeze',
  user_list_check: 'check',
  user_list_bulk_activation: 'Bulk activation',
  user_list_bulk_freeze: 'Bulk freeze',
  user_list_bulk_check: 'Bulk check',
  user_list_del_user: 'After deleting user {0}, this user cannot be recovered',
  user_list_activetion_user: `After activating user {0}, this user will be able to use the ${import.meta.env.VUE_APP_PAGE_TITLE} system`,
  user_list_freeze_user: `After freezing user {0}, this user will not be able to use the ${import.meta.env.VUE_APP_PAGE_TITLE} system`,
  user_list_check_user:
    'After checking the mailbox of user {0}, this user can be activated',
  user_list_activetion_success: 'Activation successful',
  user_list_freeze_success: 'Freeze successful',
  user_list_freeze_error: 'Freeze failed',
  user_list_check_success: 'Pass the check',
  user_list_check_error: 'Check failed',
  user_status_notVerified: 'Not Verified',
  user_status_notActivated: 'Not activated',
  user_status_activated: 'activated',
  user_status_rejected: 'rejected',
  user_form_role: 'Associated role',
  user_form_email: 'email',
  user_form_email_must_valid: 'Please enter a valid email address',
  user_form_password_null:
    'Please enter a password, the length is 5 ~ 32 characters',
  user_form_pass_hint:
    'The length of the password cannot be less than 5 and greater than 32',
  user_form_password_not_cn:
    'Password only allows English, numbers and English punctuation',
  user_form_activation_code: 'Access code',
  user_form_status: 'Status',
  cluster_name: 'Monitoring name',
  cluster_status: 'Status',
  cluster_service_status: 'Service',
  cluster_cpu_usage: 'CPU usage',
  cluster_heap_memory_usage: 'Heap memory usage',
  cluster_update: 'Update',
  cluster_running: 'running',
  cluster_stopped: 'stopped',
  cluster_sync_gover: 'Flow Engine',
  cluster_manage_sys: 'Backend Admin',
  cluster_add_server_mon: 'Add service monitoring',
  cluster_agentSetting: 'Agent server settings',
  cluster_server_name: 'server name',
  cluster_placeholder_mon_server: 'Please enter the monitored service name',
  cluster_placeholder_command: 'Please enter the command',
  cluster_ip_display: 'Network card IP display',
  cluster_ip_tip:
    'Switching the network card only changes the display of IP under the server on the cluster management page, and does not affect any functions',
  cluster_confirm_text: 'Confirm',
  cluster_restart_server: 'Restart Service',
  cluster_unbind_server: 'Unbind Server',
  cluster_start_server: 'Start Service',
  cluster_startup_after_add: 'Please add after startup',
  cluster_startup_after_delete: 'Please delete after startup',
  cluster_del_message: 'OK to delete the server',
  cluster_server_nickname: 'server name',
  cluster_command: 'command',
  instance_details_shujuyuanziyuan: 'Data Source Resource Download',
  instance_details_xianchengziyuanxia: 'Flow Engine Thread Resource Download',
  license_node_name: 'Node name',
  license_node_sid: 'node sid',
  license_status: 'License status',
  license_expire_date: 'License expiration time',
  license_update_time: 'License update time',
  license_renew_dialog: 'Renew License',
  license_normal: 'normal',
  license_expiring: 'Expiring soon',
  license_expired: 'Expired',
  license_try_out: 'trial',
  license_copied_clipboard: 'Copied to clipboard',
  license_select_node: 'Please select the node first',
  license_renew_success: 'Update succeeded, page will refresh',
  // 自定义节点
  custom_node_name: 'Node Name',
  custom_node_name_placeholder: 'Please enter the node name to search',
  notify_setting: 'Notification Settings',
  notify_system_notice: 'System',
  notify_user_notice: 'User',
  notify_view_more: 'View all',
  notify_no_notice: 'No notification',
  notify_view_all_notify: 'View all',
  notify_user_all_notice: 'All',
  notify_unread_notice: 'Unread message',
  notify_mask_read: 'Mark this page as read',
  notify_mask_read_all: 'Mark all read',
  notify_data_flow: 'task',
  notify_sync: 'Data development',
  notify_migration: 'Data copy',
  notify_notice_type: 'Message Type',
  notify_notice_level: 'Message level',
  notify_manage_sever: 'Management side',
  notify_inspect: 'Verify Task',
  notify_ddl_deal: 'DDL processing',
  notify_source_name: 'source connection',
  notify_database_name: 'Database name',
  notify_system: 'license expiration time',
  notify_started: 'Started',
  notify_paused: 'Paused',
  notify_edited: 'edited',
  notify_deleted: 'Deleted',
  notify_abnormally_stopped: 'Stopped Unexpectedly',
  notify_stopped_by_error: 'Error stop',
  notify_startup_failed: 'Startup failed',
  notify_stop_failed: 'Stop failed',
  notify_encounter_error_skipped: 'An ERROR was skipped during operation',
  notify_cdc_lag: 'CDC lag timeout',
  notify_manage_sever_restart_failed: 'Management server restart failed',
  notify_api_sever_restart_failed: 'API service restart failed',
  notify_sync_sever_restart_failed:
    'The synchronization management service failed to restart',
  notify_connection_interrupted: 'Disconnected',
  notify_manage_sever_start_failed:
    'The management server service failed to start',
  notify_api_sever_start_failed: 'The API service failed to start',
  notify_sync_sever_start_failed:
    'The synchronization management service failed to start',
  notify_manage_sever_stop_failed: 'The management server failed to stop',
  notify_api_sever_stop_failed: 'The API service failed to stop',
  notify_sync_sever_stop_failed:
    'The synchronization management service failed to stop',
  notify_api_sever_abnormally_stopped: 'The API service stopped unexpectedly',
  notify_sync_sever_abnormally_stopped:
    'Flow Engine service unexpectedly stopped',
  notify_manage_sever_abnormally_Stopped:
    'The service on the management side stopped unexpectedly',
  notify_manage_sever_started_successfully:
    'The management service has been started',
  notify_api_sever_started_successfully: 'API service has been started',
  notify_sync_sever_started_successfully:
    'The synchronization management service has been started',
  notify_manage_sever_Stopped_successfully:
    'The management service has been stopped',
  notify_api_sever_stopped_successfully: 'API service has been stopped',
  notify_sync_sever_stopped_successfully:
    'The synchronization management service has been stopped',
  notify_manage_sever_restarted_successfully:
    'The management service has been restarted',
  notify_api_sever_restarted_successfully: 'The API service has been restarted',
  notify_sync_sever_restarted_successfully:
    'The synchronization management service has been restarted',
  notify_new_sever_created_successfully: 'New service monitoring was created',
  notify_new_sever_deleted_Successfully: 'New service monitoring was deleted',
  notify_database_ddl_changed: 'Monitored database DDL changes',
  notify_inspect_verify_job_count: 'Count is different',
  notify_inspect_verify_job_value: 'Content is different',
  notify_inspect_verify_job_delete: 'Deleted',
  notify_inspect_verify_job_error: 'Running error',
  notify_approaching: 'Remaining',
  notify_system_setting: 'System settings',
  notify_tip:
    'This setting configures system-wide notifications. Notification settings at the task level take precedence over this global notification setting.',
  notify_job_operation_notice: 'Task operation notification',
  notify_email_notice: 'Email notification',
  notify_job_started: 'The job was started',
  notify_noticeInterval: 'Send interval',
  notify_operator: 'Operator',
  role_list_select_role_name: 'Please enter the role name',
  role_list_role_name: 'role name',
  role_list_description: 'role description',
  role_list_associat_users: 'associat users',
  role_list_create: 'Create roles',
  role_list_edit: 'Edit roles',
  role_list_default_role: 'Default role',
  role_list_setting_permissions: 'Set Permissions',
  role_list_delete_remind: 'Confirm to delete role {0}',
  role_list_delete_success: 'Delete the role successfully',
  role_form_yes: 'Yes',
  role_form_no: 'No',
  role_form_selectUser: 'Please select a user name',
  role_form_connected: 'connected',
  role_form_already_exists: 'The role name is repeated',
  role_null: 'The role name cannot be empty',
  role_form_description: 'The role description cannot be empty',
  role_page_Dashboard_menu: 'Console',
  role_page_datasource_menu: 'Connection management',
  role_page_Data_SYNC_menu: 'Data Replication & Data Development',
  role_page_Data_verify_menu: 'Data verification',
  role_page_log_collector_menu: 'Shared mining',
  role_page_SYNC_Function_management_menu: 'Function management',
  role_page_custom_node_menu: 'Custom Node',
  role_page_shared_cache_menu: 'Shared cache',
  role_page_data_search_menu: 'Data search',
  role_page_data_catalog_menu: 'Data catalog',
  role_page_data_quality_menu: 'Data Quality',
  role_page_data_rules_menu: 'Data rules',
  role_page_time_to_live_menu: 'Data life cycle',
  role_page_data_lineage_menu: 'Data Map',
  role_page_API_management_menu: 'API release',
  role_page_API_data_explorer_menu: 'API browse',
  role_page_API_doc_test_menu: 'API test',
  role_page_API_stats_menu: 'API statistics',
  role_page_API_clients_menu: 'API clients',
  role_page_API_server_menu: 'API server',
  role_page_data_collect_menu: 'Data collection (old version)',
  role_page_schedule_jobs_menu: 'Scheduled tasks',
  role_page_Cluster_management_menu: 'Cluster management',
  role_page_agents_menu: 'Process management',
  role_page_user_management_menu: 'User management',
  role_page_role_management_menu: 'Role management',
  role_page_system_settings_menu: 'System settings',
  role_page_dictionary_menu: 'Dictionary template management',
  role_page_Topology_menu: 'Network topology',
  role_page_servers_oversee_menu: 'Operation and maintenance operation control',
  role_all_check: 'Check all',
  role_module_meun_Dashboard: 'Browse console',
  role_module_meun_datasource: 'Connection management',
  role_module_meun_Data_SYNC: 'Data Replication & Data Development',
  role_module_meun_SYNC_Function_management: 'Function management',
  role_module_meun_Data_verify: 'Data verification',
  role_module_meun_log_collector: 'Shared mining',
  role_module_meun_custom_node: 'custom node',
  role_module_meun_shared_cache: 'Shared cache',
  role_module_meun_data_search: 'Data search',
  role_module_meun_data_government: 'Data governance classification',
  role_module_meun_data_catalog: 'Data catalog',
  role_module_meun_data_quality: 'Data quality',
  role_module_meun_data_rules: 'Data rules',
  role_module_meun_time_to_live: 'Data life cycle',
  role_module_meun_data_lineage: 'Data Map',
  role_module_meun_API_management: 'API release',
  role_module_meun_API_data_explorer: 'API browse',
  role_module_meun_API_doc_test: 'API test',
  role_module_meun_API_stats: 'API statistics',
  role_module_meun_API_clients: 'API client',
  role_module_meun_API_server: 'API server',
  role_module_meun_data_collect: 'Data collection (old version)',
  role_module_meun_schedule_jobs: 'Scheduled tasks',
  role_module_meun_Cluster_management: 'Cluster management',
  role_module_meun_agents: 'Process management',
  role_module_meun_dictionary: 'dictionary template',
  role_module_meun_user_management: 'User management',
  role_module_meun_role_management: 'role management',
  role_module_meun_system_settings: 'System settings',
  role_name_Dashboard: 'Browse console',
  role_name_system_notice: 'Message notification',
  role_name_notice_settings: 'Message notification settings',
  role_name_account_operation_history: 'operation history',
  role_name_datasource: 'Browse connection management',
  role_name_datasource_category_management:
    'Connection management category management',
  role_name_datasource_category_application:
    'Connection management category application',
  role_name_datasource_creation: 'Connection management creation',
  role_name_datasource_delete: 'Connection management delete',
  role_name_datasource_edition: 'Connection management editor',
  role_name_data_transmission: 'Data pipeline',
  role_name_Data_SYNC: 'Browse replication and transformation tasks',
  role_name_SYNC_category_management: 'Task classification management',
  role_name_SYNC_category_application: 'Task classification application',
  role_name_SYNC_job_delete: 'Delete job',
  role_name_SYNC_job_edition: 'Edit job',
  role_name_SYNC_job_operation: 'Task operation',
  role_name_SYNC_job_import: 'Task import',
  role_name_SYNC_job_export: 'Task export',
  role_name_SYNC_Function_management: 'Browse letter management',
  role_name_Data_verify: 'Browse data verification',
  role_name_verify_job_creation: 'Create verification task',
  role_name_verify_job_edition: 'Edit and execute verification tasks',
  role_name_verify_job_delete: 'Delete verification job',
  role_name_verify_job_execution: 'Verify job execution',
  role_name_log_collector: 'Browse shared mining',
  role_name_custom_node: 'Browse custom node',
  role_name_shared_cache: 'Browse shared cache',
  role_name_data_search: 'Browse data search',
  role_name_data_government: 'Data governance',
  role_name_data_catalog: 'Browse data catalog',
  role_name_data_catalog_category_management:
    'Data catalog classification management',
  role_name_data_catalog_category_application:
    'Data catalog classification application',
  role_name_data_catalog_edition: 'Edit metadata',
  role_name_new_model_creation: 'Create model',
  role_name_meta_data_deleting: 'Metadata deletion',
  role_name_data_quality: 'Browse data quality',
  role_name_data_quality_edition: 'Edit data quality',
  role_name_data_rules: 'Browse data rules',
  role_name_data_rule_management: 'Data rule management',
  role_name_time_to_live: 'Browse data life cycle',
  role_name_time_to_live_management: 'Data Lifecycle Management',
  role_name_data_lineage: 'Browse data map',
  role_name_data_publish: 'Browse data publishing',
  role_name_API_management: 'Browse API release',
  role_name_API_category_application: 'API category application',
  role_name_API_category_management: 'API category management',
  role_name_API_creation: 'API creation',
  role_name_API_delete: 'API delete',
  role_name_API_edition: 'API editor',
  role_name_API_publish: 'Publish API',
  role_name_API_import: 'API import',
  role_name_API_export: 'API export',
  role_name_API_data_explorer: 'Browse API data browser',
  role_name_API_data_explorer_export: 'Export API data',
  role_name_API_data_explorer_deleting: 'Deleting API data',
  role_name_API_data_explorer_tagging: 'API data tagging',
  role_name_API_data_time_zone_editing: 'Edit time zone',
  role_name_API_data_creation: 'Add API data',
  role_name_API_data_download: 'Download API data',
  role_name_API_doc_test: 'Browse API documentation test',
  role_name_API_stats: 'Browse API statistical analysis',
  role_name_API_clients: 'Browse API Clients',
  role_name_API_clients_amanement: 'API client management',
  role_name_API_server: 'Browse API server',
  role_name_API_server_management: 'API server management',
  role_name_data_collect: 'Browse the old version of data collection',
  role_name_data_collect_all_data: 'Data collection old version',
  role_name_system_management: 'Browse system management',
  role_name_schedule_jobs: 'Browse Scheduled Jobs',
  role_name_schedule_jobs_management: 'Scheduled task management',
  role_name_Cluster_management: 'Browse cluster management',
  role_name_Cluster_operation: 'Operation Agent service',
  role_name_status_log: 'Status log',
  role_name_agents: 'Browse process management',
  role_name_user_management: 'Browse user management',
  role_name_user_creation: 'Create user',
  role_name_user_edition: 'Edit user',
  role_name_user_delete: 'Delete user',
  role_name_user_category_management: 'User category management',
  role_name_user_category_application: 'User classification application',
  role_name_role_management: 'Browse role management',
  role_name_role_creation: 'Create role',
  role_name_role_edition: 'Edit role',
  role_name_role_delete: 'Delete role',
  role_name_system_settings: 'Browse system settings',
  role_name_system_settings_modification: 'Modify settings',
  role_name_create_new_table_in_SYNC: 'Create table in task',
  role_name_servers_oversee: 'Browse O&M',
  role_name_dictionary: 'Browse dictionary template management',
  role_name_Topology: 'Browse network topology',
  milestone_list_status_waiting: 'Waiting',
  signin_code: 'Send',
  signin_verify_code: 'Please enter the verification code',
  signin_verify_code_success: 'Verification code sent successfully',
  signin_email_require: 'Email address is required',
  signin_verify_code_not_empty: 'Verification code must be filled',
  signin_verify_code_not_incorrect: 'Verification code error',
  signin_verify_password_invalid: 'Verification code at least 5 characters',
  signin_verify_password_notCN:
    'Password only allows English, numbers and English punctuation',
  signin_not_mailbox: 'oops~This mailbox has not been registered yet',
  meta_table_default: 'Default',
  meta_table_not_null: 'Not null',
  new_advanced_mode: 'Standard Mode',
  new_more_features: 'More features',
  new_data_copy: 'Data copy',
  new_data_development: 'Data Development',
  new_data_copy_desc:
    'Copy the database across databases, suitable for data migration, disaster recovery backup, system multi-activity and other scenarios',
  new_data_development_desc:
    'Extract source data and process calculation transformation, such as row filtering, field processing, multi-table merging, etc.',
  new_create_connection: 'Create data source',
  new_create_api: 'Create API',
  new_create_connection_desc:
    'The data source is the premise of creating a transfer task, and all the database and table data nodes in the task come from the data source',
  new_create_api_desc:
    'API is data publishing, you can create a new collection API based on the existing collection or through synchronization tasks',
  //数据发现-数据对象
  object_list_name: 'Object name',
  object_list_classification: 'Object classification',
  object_list_type: 'Object type',
  object_list_source_type: 'Source type',
  object_list_source_information: 'Source information',
  datadiscovery_catalogue_ziyuanbangding: 'Resource binding',
  datadiscovery_catalogue_lianjieduixiangming: 'Connection object name',
  datadiscovery_catalogue_ziyuanleixing: 'Resource Type',
  datadiscovery_objectlist_duixiangminglaiyuan: 'Object name/Data source',
  datadiscovery_objectlist_laiyuanfenlei: 'Source Classification',
  datadiscovery_previewdrawer_shujuxiang: 'Data Item',
  datadiscovery_previewdrawer_yewumingcheng: 'Business Name',
  datadiscovery_previewdrawer_lianjiemiaoshu: 'Connection Description',
  datadiscovery_previewdrawer_shujuliang: 'Data Volume',
  datadiscovery_previewdrawer_biangengshijian: 'Change Time',
  datadiscovery_previewdrawer_guanliyuan: 'Administrator',
  datadiscovery_previewdrawer_duixiangxiangqing: 'Object Details',
  datadiscovery_previewdrawer_yewumiaoshu: 'Business Description',
  datadiscovery_previewdrawer_yewuleixing: 'Business Type',
  datadiscovery_previewdrawer_suoyin: 'Index',
  datadiscovery_previewdrawer_waijian: 'Foreign Key',
  datadiscovery_previewdrawer_zhujian: 'Primary key',
  // web-core
  app_document: 'Documentation',
  app_qa: 'Customer Service',
  app_account: 'Account',
  app_version: 'Version',
  app_home: 'Home',
  app_signOut: 'Sign out',
  app_signOutMsg: 'Are you sure to sign out?',
  app_customerService_technicalSupport: 'User Support',
  app_customerService_technicalSupportText: 'Any question, please submit to',
  app_customerService_technicalSupportText1:
    'The account and password of Support Forum is same as cloud.tapdata.net.We will response you as soon as possible.',
  app_customerService_userSupport: 'Support Forum',
  app_customerService_otherDmands: 'Other Requirements',
  app_customerService_otherDmandsText:
    'Any requirement, please contact us by scanning WeChat QR below.',
  app_signIn_slogan: 'Use your data, as easy as water from tap',
  app_signIn_signIn: 'Sign in',
  app_signIn_keepSignIn: 'Keep signed in',
  app_signIn_email_placeholder: 'Enter your email',
  login_email_and_ad_placeholder: 'Enter your email/LDAP username',
  app_signIn_inviteCode_placeholder: 'invite code',
  app_signIn_password_placeholder: 'Enter your password',
  app_signIn_email_require: 'E-mail is required.',
  app_signIn_inviteCode_require: 'invite code is required.',
  app_signIn_inviteCode_invalid: 'invite code must be valid.',
  app_signIn_email_invalid:
    'Email or password is incorrect, please check and re-enter.',
  app_signIn_password_invalid: 'Password at least 5 characters.',
  app_signIn_permission_denied: 'Permission denied.',
  app_signIn_signInFail: "The email and password didn't work.",
  app_signIn_registry: 'Registration',
  app_signIn_registry_tip: 'I agree with',
  app_signIn_userPplicy: ' user policy',
  app_signIn_nextStep: 'Next',
  app_signIn_haveAccpunt: 'Remember account?',
  app_signIn_backLogin: 'Back to login',
  app_signIn_email_existed: 'Email address has been registered',
  app_signIn_userPplicy_message: 'Please tick "agree with user policy"',
  app_signIn_modifyPassword: 'Reset password',
  app_signIn_newPasswordTip:
    'Enter your registered mailbox and new password, and we will send you confirmation email and click link to reset password',
  app_signIn_newpassword_placeholder: 'Please set a new password',
  app_signIn_rememberPasswords: 'Remember passwords?',
  app_signIn_Registration: 'Register an account',
  app_signIn_forgetPassword: 'Forget password?',
  app_signIn_confirmationEmail:
    'Registration confirmation email has been sent to',
  app_signIn_mailbox:
    'Please log in to the mailbox and click the link to confirm~',
  app_signIn_receiveEmail: "Didn't receive email? Click",
  app_signIn_resend: 'Resend',
  app_signIn_orClick: 'or click',
  app_signIn_accountSuccess: 'has been successfully registered~',
  app_signIn_clickBtn:
    'Click the button below to enjoy the journey of data transmission',
  app_signIn_resetClickBtn: 'Click the button below to log in',
  app_signIn_goLogin: 'Go login',
  app_signIn_connectionFailed: 'registration confirmation link is invalid',
  app_signIn_resetConnectionFailed:
    'reset password confirmation link is invalid',
  app_signIn_confirmEmail: 'Please re',
  app_signIn_registered: ' Registered',
  app_signIn_resetAccountSuccess: 'The password has been reset successfully~',
  app_signIn_passwordResetText: 'Reset password email has been sent to',
  app_signIn_hasMailbox: 'oops~This mailbox has already been registered',
  app_signIn_disableSignup: 'oops~Disable Signup',
  app_signIn_getCode: 'Get InviteCode',
  app_signIn_qrCodeText: 'Scan wechat QR Code to get invite code',
  app_Home_initialization: 'Initializing',
  app_Home_loadingFinished: 'Initialization completed',
  app_Home_incremental: 'CDC',
  app_Home_incrementalLag: 'CDC Lag',
  message_cancel: 'Cancel',
  message_confirm: 'Confirm',
  message_save: 'Save',
  message_clickRelatedTasks: 'Click to view related tasks',
  message_noRelatedTask: 'No related tasks',
  cluster_cancel: 'Cancel',
  cluster_confirm: 'Confirm',
  cluster_confirmText: 'Confirm ',
  cluster_closeSever: ' Close service ',
  cluster_restartServer: ' Restart service ',
  cluster_startServer: ' Start service ',
  cluster_deleteOrNot: 'Delete or not',
  cluster_serviceCluMange: 'Service cluster management',
  cluster_start: 'Start up',
  cluster_close: 'Close',
  cluster_restart: 'Restart',
  cluster_syncGover: 'Syn gover',
  cluster_delete: 'Delete',
  cluster_edit: 'Edit',
  cluster_reduction: 'Restore',
  cluster_time: 'Test',
  cluster_saveOK: 'Saved successfully',
  cluster_saveFail: 'Save failed',
  cluster_deleteOK: 'Successfully deleted',
  cluster_deleteFail: 'Failed to delete',
  cluster_selectTime: 'Select time',
  cluster_selectDate: 'Select date',
  cluster_statusLog: 'Status log',
  cluster_placeholderServer: 'Please enter a server name',
  cluster_manageSys: 'Manage system',
  cluster_addServerMon: 'Add server monitoring',
  cluster_serverName: 'Server name',
  cluster_placeholderMonServer: 'Please enter the monitored service name',
  cluster_iPDisplay: 'IP display',
  cluster_ipTip:
    'Switching the network card only changes the display of IP under the server of cluster management page, does not affect any function.',
  cluster_delTittle: 'Delete Agent server',
  cluster_delMessage: 're you sure to delete the server ',
  cluster_startupAfter_delete: 'Please delete after startup',
  cluster_startupAfter_add: 'Please add after startup',
  cluster_noneText: ' is required.',
  cluster_hostName: 'Host name',
  cluster_ipAddress: 'IP address',
  cluster_uniqueEncode: 'Unique encoding',
  cluster_logs: 'Log',
  cluster_serviceType: 'Service type',
  cluster_level: 'Level',
  cluster_cpuUsage: 'CPU Usage',
  cluster_heapMemoryUsage: 'Heap memory usage',
  button_rename: 'Rename',
  button_all: 'All',
  dataFlow_leave: 'Leave',
  dataFlow_backlistText: 'Back to sync job list page',
  dataFlow_saveReminder:
    'This jobhas not been saved yet, If you leave this page, the job configuration will be lost. Are you sure to leave ?',
  dataFlow_saveFail:
    'Failed to save the task, please check the configuration and ensure that the data source status is valid.',
  dataFlow_aggregateNotDataNode:
    'The first target data node of aggregation node can only be COLLECTION',
  dataFlow_batchSortOperation: 'Batch sort operation',
  dataFlow_selectRowdata: 'Please select row data',
  dataFlow_clusterClone: 'Database Migration',
  dataFlow_custom: 'Data Sync',
  dataFlow_searchNode: 'Search Node',
  dataFlow_updateModel: 'Reload Model',
  dataFlow_loadingText: 'Loading...',
  dataFlow_databseProcessingHead: 'Data Processing & Sync',
  dataFlow_databseMigrationHead: 'Database Migration',
  dataFlow_dataMigrationHead: 'Data Sync',
  dataFlow_databseFreedomHead: 'Custom Data Sync',
  dataFlow_createNew: 'Create New',
  dataFlow_DissedNoAction:
    'oops~ The banned node/Connecting line can not be deleted and connected',
  dataFlow_notCopy: 'The banned node cannot be copied ',
  dataFlow_guidingMode: 'Guiding mode',
  dataFlow_advancedMode: 'Standard mode',
  dataFlow_freedomMode: 'Standard mode',
  dataFlow_advanceSetting: 'More advanced setting',
  dataFlow_closeSetting: 'Fold up',
  dataFlow_openPanel: 'Open',
  dataFlow_execution: 'Execution',
  dataFlow_previous: 'Previous',
  dataFlow_next: 'Next',
  dataFlow_sourceSetting: 'Source setting',
  dataFlow_targetSetting: 'Target setting',
  dataFlow_advancedetting: 'Advanced settings',
  dataFlow_simpleSceneTitle: 'Create a database replication task',
  dataFlow_sourceLibrarySetting: 'Source library structure and object settings',
  dataFlow_databseMigration:
    'With the guided mode to help understanding the operation method of databases migration which can quickly realize structure , inital, and CDC migration between databases.',
  dataFlow_databseProcessing:
    'With the  guided mode to help novice users to quickly understand the table level data processing and SYNC. This function can not only realize table level INITAL and CDC transmission, but also focus on various processors (JS processing, field filtering, aggregation processing, row level filtering, etc.) for complex logical processing demands.',
  dataFlow_databseFreedom:
    'Database migration can help users  to achieve structure,  inital, and CDC migration of multiple homogeneous or heterogeneous databases in one job. ',
  dataFlow_dataFreedom:
    'Data sync focuses on data processing (such as table merging, data splitting, joint mapping, field processing, content filtering, aggregation processing, JS processing ,etc )and sync of table-level real-time data sync.',
  dataFlow_moreFeatures: 'More Features',
  dataFlow_creatSource: 'Create data source',
  dataFlow_creatApi: 'Create API',
  dataFlow_dataValidation: 'Data Verification',
  dataFlow_sourceDescription:
    'The data source is the premise of creating the transmission job, the data source includes Database, File, GridFS, Rest API, View, Custom connection, etc.',
  dataFlow_apiDescription:
    'API, aka data publication API, you can create a new API which includes the paths of Post, Get, Patch, Delete.',
  dataFlow_datavaliDescription:
    'Data verification has the function of count verify, content verify, and joint field value verify which can verify the consistency of source and target.',
  dataFlow_multiError_allSelectionError:
    'The status of selected job does not allow this operation.',
  dataFlow_multiError_notFound: 'This job does not existed.',
  dataFlow_multiError_statusError:
    'Job status does not allow to do this operation.',
  dataFlow_multiError_otherError: 'Operation failed, please try it again.',
  dataFlow_changeName: 'Rename',
  dataFlow_Enable: 'Enable',
  dataFlow_Disable: 'Disable',
  dataFlow_draftNotStart: 'Configuration is not complete,  cannot be started',
  dataFlow_systemHint: 'System prompt',
  dataFlow_systemText:
    'The system detected that the following tasks were not saved， keep editing?',
  dataFlow_stystemOpen: 'Open',
  dataFlow_stystemOpenAll: 'Open all',
  dataFlow_stystemDeleteAll: 'Delete all',
  dataFlow_stystemLgnoreAll: 'Ignore all',
  dataFlow_newTaksName: 'The_new_task',
  dataFlow_selectNode: 'Please select a node',
  dataFlow_submitExecute: 'Submit and execute',
  dataFlow_submitOnly: 'Submit only',
  dataFlow_implementationModalities: 'Execution method',
  dataFlow_submitConfirmation: 'Submit Confirmation',
  dataFlow_SyncPoint: 'CDC start timepoint',
  dataFlow_cdcLabel: 'Data source:',
  dataFlow_syncType: ' Type',
  dataFlow_belongAgent: 'Agent',
  dataFlow_SyncInfo_localTZ:
    'Local Timezone CDC Time: custom a point of  CDC time，in local time zone',
  dataFlow_SyncInfo_current: 'Current Time：Current DB Time',
  dataFlow_SyncInfo_connTZ:
    'DB Timezone CDC Time: custom a point of  CDC time，in the time zone of a specific server',
  dataFlow_SyncInfo_localTZType: 'Local Timezone CDC Time',
  dataFlow_SyncInfo_currentType: 'Current Time',
  dataFlow_SyncInfo_connTZType: 'DB Timezone CDC Time',
  dataFlow_Current: 'Current',
  dataFlow_SyncTime: 'Sync Time',
  dataFlow_batchDelete: 'Batch Delete',
  dataFlow_batchRest: 'Batch reset',
  dataFlow_bulkExport: 'Bulk Export',
  dataFlow_bulkImport: 'Bulk Import',
  dataFlow_bulkScheuled: 'Batch Start',
  dataFlow_bulkStopping: 'Bulk Stop',
  dataFlow_taskBulkFx: 'Function',
  dataFlow_taskBulkOperation: 'Bulk Operation',
  dataFlow_upload: 'Click to upload',
  dataFlow_chooseFile: 'Select a document',
  dataFlow_import: 'Task Import',
  dataFlow_uploadOK: 'Upload successful',
  dataFlow_uploadError: 'Upload failed',
  dataFlow_uploadInfo: 'Click to view details',
  dataFlow_view: 'View',
  dataFlow_dataFlowExport: 'Export',
  dataFlow_addTag: 'Add Tag',
  dataFlow_editTag: 'Edit Tag',
  dataFlow_overWrite: 'Overwrite existing data',
  dataFlow_skipData: 'Skip existing data',
  dataFlow_loadingError: 'Loading failed, please',
  dataFlow_dataLoading: 'Data Loding ...',
  dataFlow_loadLogTip:
    'Run log is trying to load, it may take 5 ~ 10 seconds, please wait ...',
  dataFlow_noLogTip: 'No data',
  dataFlow_clickLoadTxt: 'Click to load',
  dataFlow_average: 'Average',
  dataFlow_current: 'Current',
  dataFlow_allNode: 'All Nodes',
  dataFlow_taskName: 'Flow Name',
  dataFlow_creatdor: 'Creator',
  dataFlow_ownedUser: 'Owned User',
  dataFlow_ownedLibrary: 'Owned Library',
  dataFlow_creationTime: 'Start Time',
  dataFlow_state: 'State',
  dataFlow_executionTime: 'Lapsed Time',
  dataFlow_finishTime: 'finish Time',
  dataFlow_sourceLibrary: 'Source',
  dataFlow_targetLibrary: 'Target',
  dataFlow_inputNumber: 'Input Total',
  dataFlow_outputNumber: 'Output Total',
  dataFlow_rowCount: 'Row',
  dataFlow_inputOutput: 'Input / Output statistics',
  dataFlow_transf: 'Transmission Time',
  dataFlow_timePoint: 'CDC timepoint',
  dataFlow_dataScreening: 'Event Statistics',
  dataFlow_taskDetail: 'Task Details',
  dataFlow_nodeDetail: 'Node Information',
  dataFlow_unit: 'Unit',
  dataFlow_article: 'pcs',
  dataFlow_secondUnit: 'second',
  dataFlow_second: 'Second',
  dataFlow_min: 'Branch',
  dataFlow_hour: 'HOUR',
  dataFlow_day: 'Day',
  dataFlow_input: 'Input',
  dataFlow_output: 'OutPut',
  dataFlow_totalInput: 'Total Input',
  dataFlow_totalOutput: 'Total Output',
  dataFlow_totalInsert: 'Total Insertion',
  dataFlow_totalUpdate: 'Total Update',
  dataFlow_totalDelete: 'Total Deletion',
  dataFlow_category: 'Category',
  dataFlow_replicate: 'Replicate Lag',
  dataFlow_throughputpop:
    'Throughput:The read speed from source node and the write speed to the target node, larger number is better',
  dataFlow_transtime_pop:
    'Transmission Time:The time lapsed from the data record is read from the source node until the data is written into target node',
  dataFlow_replicate_pop:
    'Replicate Lag:The time gap between source node last update time and target node last update time',
  dataFlow_status_paused: 'Paused',
  dataFlow_status_prepare: 'Prepare',
  dataFlow_status_cdc: 'CDC',
  dataFlow_status_initializing: 'Initializing',
  dataFlow_status_initialized: 'Initialized',
  dataFlow_status_Lag: 'Lag',
  dataFlow_status_all: 'All',
  dataFlow_lag: 'lag',
  dataFlow_executionStatus: 'Execution status',
  dataFlow_searchPlaceholder: 'Task name / Node name / DB name',
  dataFlow_searchAgent: 'Agent name',
  dataFlow_dataRange: 'Date range',
  dataFlow_startTime: 'Start time',
  dataFlow_endTime: 'End time',
  dataFlow_separator: 'to',
  dataFlow_dataPlaceholder: 'Select time range',
  dataFlow_taskStatus: 'Status',
  dataFlow_maxLagTime: 'Max lag time',
  dataFlow_taskStatusPlaceholder: 'Select task status',
  dataFlow_taskSettingPlaceholder: 'Select Sync type',
  dataFlow_updateTime: 'Update time',
  dataFlow_runningSpeed: 'Running speed',
  dataFlow_taskSwitch: 'Switch',
  dataFlow_operate: 'Operation',
  dataFlow_dataMap: 'Data Map',
  dataFlow_edit: 'Edit',
  dataFlow_copy: 'Copy',
  dataFlow_schedule: 'Schedule',
  dataFlow_run: 'Run',
  dataFlow_stop: 'Stop',
  dataFlow_cut: 'Cut',
  dataFlow_paste: 'Paste',
  dataFlow_undo: 'Undo',
  dataFlow_redo: 'Redo',
  dataFlow_selectAll: 'Select all',
  dataFlow_amplification: 'Zoom in',
  dataFlow_zoomOut: 'Zoom out',
  dataFlow_down: 'Down',
  dataFlow_up: 'Up',
  dataFlow_selectMultipleNode: 'Multiple selection',
  dataFlow_mouseDrag: 'Drag',
  dataFlow_runningMonitor: 'Monitor',
  dataFlow_select_source_connection: 'Source-side connection',
  dataFlow_select_sync_mode: 'Sync Mode',
  dataFlow_mapping: 'Association',
  dataFlow_select_target_connection: 'Target connection',
  dataFlow_sync_mode: 'Sync Mode',
  dataFlow_sync_type: 'Sync type',
  dataFlow_send_email: 'Send Email',
  dataFlow_stopped: 'task stopped',
  dataFlow_error: 'task error',
  dataFlow_edited: 'task edited',
  dataFlow_started: 'task started',
  dataFlow_sharecdcmode: 'shared incremental read mode',
  dataFlow_streaming: 'streaming read',
  dataFlow_polling: 'polling read',
  dataFlow_drop_target_before_start:
    'Whether the target table is deleted before starting the task',
  dataFlow_run_custom_sql: 'Repeat custom SQL',
  dataFlow_stop_on_error: 'Stop when error',
  dataFlow_need_to_create_Index: 'Auto-create index',
  dataFlow_transformModelVersion: 'Transform model version',
  dataFlow_noPrimaryKey: 'Supported no primary key',
  dataFlow_is_schedule: 'Regular job schedul',
  dataFlow_cron_expression: 'Scheduling cron expression',
  dataFlow_data_quality_tag: 'Add data quality tag',
  dataFlow_notification_lag: 'Notification',
  dataFlow_isOpenAutoDDL: 'Auto-DDL operation',
  dataFlow_ddlTip:
    'Warn: Automatic DDL does not support JS processor and field processor',
  dataFlow_transformerConcurrency: 'Transformer Concurrency',
  dataFlow_processorConcurrency: 'Processor Concurrency',
  dataFlow_cdcEngineFilter: 'Enable Engine Filtering',
  dataFlow_cdcFetchSize: 'Number CDC batch reads',
  dataFlow_cdcFetchSizeTip: 'Number of data read by system each time.',
  dataFlow_cdcFetchSizeTip1:
    'The smaller the number entered means the higher the CDC real-time performance, but the processing speed is relatively slow.',
  dataFlow_cdcFetchSizeTip2:
    'The more the number entered means the lower the real-time performance, and the overall processing speed will be faster. ',
  dataFlow_send_email_when_replication: 'Resend in a few seconds',
  dataFlow_send_email_at_most_one_replication:
    'Cancel sending in more than seconds',
  dataFlow_read_cdc_interval: ' CDC interval',
  dataFlow_cdc_concurrency: ' CDC concurrency',
  dataFlow_cdcShareFilterOnServer: 'Filter CDC shared log',
  dataFlow_read_batch_size: 'Read-amount/time',
  dataFlow_cdcDataProcess: 'CDC data process',
  dataFlow_batch: 'Batch process',
  dataFlow_onebyone: 'Row by row process',
  dataFlow_mission: 'Description',
  dataFlow_yes: 'yes',
  dataFlow_no: 'no',
  dataFlow_cronExpression: 'Please enter cron expression',
  dataFlow_selectGrpupFiled: 'Please select a grouping field',
  dataFlow_selectTargetField: 'Please select the target field',
  dataFlow_aggName: 'Sub-process Name',
  dataFlow_nodeName: 'Node Name',
  dataFlow_nodeType: 'Node Type',
  dataFlow_aggFunction: 'Polymerization',
  dataFlow_aggExpression: 'Target',
  dataFlow_filterPredicate: 'Filter Predicate',
  dataFlow_groupByExpression: 'Group Field',
  dataFlow_keepAggreHistoryData: 'Keep aggregation historical data',
  dataFlow_aggregation: 'Aggregation',
  dataFlow_aggrCleanSecond: 'Time to clean up old version data',
  dataFlow_aggrFullSyncSecond: 'Full synchronization time',
  dataFlow_enterFilterTable: 'Please enter the filter table content',
  dataFlow_lagTime: 'incremental lag time setting',
  dataFlow_lagTimeTip:
    'when the incremental task delay is greater than this value, the incremental task delay is considered, and the default value is 0',
  dataFlow_aggregatePrompt:
    'Warn：Using the aggregation processor node, the job will be reset when excutes restart',
  dataFlow_nameTip:
    'Script editing of subsequent nodes needs to refer to the name of this sub-process for the specified data processing, so different sub-process names cannot be repeated. ',
  dataFlow_button_submit: 'Submit',
  dataFlow_button_viewConfig: 'Node Config',
  dataFlow_button_viewMonitoring: 'Data Monitoring',
  dataFlow_button_setting: 'Setting',
  dataFlow_button_logs: 'Logs',
  dataFlow_button_preview: 'Preview',
  dataFlow_button_capture: 'Data Trace',
  dataFlow_button_stop_capture: 'Stop Trace',
  dataFlow_button_start: 'Start',
  dataFlow_button_stop: 'Stop',
  dataFlow_button_force_stop: 'Force Stop',
  dataFlow_button_reset: 'Reset',
  dataFlow_button_save: 'Save',
  dataFlow_button_saveDraft: 'Save Draft',
  dataFlow_button_saveing: 'Saving',
  dataFlow_button_reloadSchema: 'Reload Schema',
  dataFlow_button_debug: 'debug test',
  dataFlow_button_quantitative: 'Quantitative',
  dataFlow_button_increment: 'Increment',
  dataFlow_save_before_running: 'Please save the task before running',
  dataFlow_reset_job_msg: 'Reset Job?',
  dataFlow_reset_job_tip: 'Tip',
  dataFlow_stop_job_msg: 'Stop jobs?',
  dataFlow_stop_job_force_stop_msg: 'Force Stop jobs?',
  dataFlow_stop_job_tip: 'Tip',
  dataFlow_file_preview_fields_file_name: 'File Name',
  dataFlow_file_preview_fields_file_size_ondisk: 'File Size(Byte)',
  dataFlow_file_preview_fields_file_modify_time_ondisk: 'File Modify Time',
  dataFlow_file_preview_fields_file_create_time_ondisk: 'File Create Time',
  dataFlow_file_preview_fields_file_path: 'File Path',
  dataFlow_delete_confirm_Title: 'Delete the task? ',
  dataFlow_delete_confirm_Message:
    'After deleting task XXX, this task cannot be restored',
  dataFlow_bulk_delete_confirm_Title: 'Delete tasks in batch? ',
  dataFlow_bulk_delete_confirm_Message:
    'After deleting tasks in batch, tasks cannot be restored',
  dataFlow_stop_confirm_title: 'Do you want to suspend this task? ',
  dataFlow_stop_confirm_message:
    'After the task xxx is suspended, when the table in the task that has not been fully synchronized is started again, the full synchronization will be performed again',
  dataFlow_bulk_stop_confirm_title: 'Do you want to pause tasks in bulk? ',
  dataFlow_bulk_stop_confirm_message:
    'After the task is paused in batch, when the table in the task that has not been fully synchronized is started again, the full synchronization will be performed again',
  dataFlow_force_stop_confirm_title: 'Do you want to force stop this task? ',
  dataFlow_force_stop_confirm_message:
    'Forcibly stop the task xxx will immediately interrupt the data transmission, force the task to stop quickly, and reset the task',
  dataFlow_bulk_force_stop_confirm_title:
    'Do you want to force stop tasks in batches? ',
  dataFlow_bulk_force_stop_confirm_message:
    'The batch forced stop task will immediately interrupt the data transmission to force the task to stop quickly and reset the task',
  dataFlow_initialize_confirm_title: 'Do you want to reset this task? ',
  dataFlow_initialize_confirm_message:
    'Resetting task xxx will clear the task synchronization progress and the task will be executed again',
  dataFlow_bulk_initialize_confirm_title:
    'Do you want to reset tasks in bulk? ',
  dataFlow_bulk_initialize_confirm_message:
    'Resetting the task in batches will clear the task synchronization progress, and the task will be executed again',
  dataFlow_importantReminder: 'Important notice',
  dataFlow_modifyEditText: ' If you edited the',
  dataFlow_nodeLayoutProcess: ' node arrangement',
  dataFlow_nodeAttributes: 'node attribute',
  dataFlow_matchingRelationship: 'or matching attribute',
  dataFlow_afterSubmission: 'the job should be',
  dataFlow_runNomally: 'to make sure the job running correctly;',
  dataFlow_editLayerTip: 'otherwise the job will be abnormal, continue？',
  dataFlow_continueEditing: 'Still Edit',
  dataFlow_numberType: 'must be a number and cannot be less than 0',
  dataFlow_setting_distinctWriteType: 'De-rewrite mode',
  dataFlow_setting_intellect: 'Intelligent de-rewrite',
  dataFlow_setting_compel: 'Force de-rewrite',
  dataFlow_setting_intellectTip:
    "Intelligent deduplication: intelligent detection of the target's existing data, deduplication can greatly improve transmission performance",
  dataFlow_setting_compelTip:
    "Forced deduplication: Perform mandatory deduplication detection on the target's existing data, strictly guarantee accuracy but low transmission performance",
  dataFlow_setting_batchTip:
    'Batch:  Batch processing and transmission of CDC data with high performance.',
  dataFlow_setting_onebyoneTip:
    'Row by row: Processing and transmission of CDC data row by row',
  dataFlow_setting_sync_type_tip:
    'Transmission type can be changed after disable aggregation settings of collection node: ',
  dataFlow_skipError_title: 'Skip Error Settings',
  dataFlow_skipError_skipErrorSettings: 'Data Processing Error Handling',
  dataFlow_skipError_tip:
    'There were data processing errors detected in the job, please make sure these errors have been addressed. If you would like to skip these errors, please check them and click the "Skip errors, continue to start" button.  ',
  dataFlow_skipError_attention:
    'WARNING: If you chose to skip the errors, the relevant data may be discarded. ',
  dataFlow_skipError_startJob: 'Skip errors, continue to start',
  dataFlow_skipError_cancel: 'Cancel',
  dataFlow_skipError_taskName: 'Task name',
  dataFlow_skipError_errorTotal: 'Total XX, selected',
  dataFlow_skipError_strip: 'row',
  dataFlow_flowEngineVersion: 'Flow Engine Version',
  dataFlow_flowEngineV1: 'Flow Engine V1',
  dataFlow_jetFlowEngineV2: 'Jet Flow Engine V2',
  editor_cell_data_node_collection_form_collection_placeholder:
    'Please select collection',
  editor_cell_data_node_collection_form_fieldFilterType_retainedField:
    'Retained field',
  editor_cell_data_node_collection_form_fieldFilterType_deleteField:
    'Delete field',
  editor_cell_data_node_collection_form_fieldFilter_placeholderKeep:
    ' Select the fields to keep',
  editor_cell_data_node_collection_form_fieldFilter_placeholderDelete:
    ' Select the fields to delete',
  editor_cell_data_node_collection_form_filter_fieldFilter: 'Visual Mode',
  editor_cell_data_node_collection_form_filter_sqlFilter: 'SQL Filter',
  editor_cell_data_node_collection_form_filter_mqlFilter: 'MQL Filter',
  editor_cell_data_node_collection_form_filter_allField: 'All fields',
  editor_cell_data_node_collection_form_filter_rowLimit: 'Row limit',
  editor_cell_data_node_collection_form_filter_allRows: 'All rows',
  editor_cell_data_node_collection_form_filter_oneThousandRows: '1000 rows',
  editor_cell_data_node_collection_form_filter_tenThousandRows: '10000 rows',
  editor_cell_data_node_table_form_custom_sql_placeholder:
    'Please input you custom sql',
  editor_cell_data_node_table_form_custom_sql_mplaceholder:
    'Please input you custom mql',
  editor_cell_data_node_table_form_initial_offset_label: 'Custom SQL Offset',
  editor_cell_data_node_table_form_initial_offset_placeholder:
    'Please input you custom sql offset',
  editor_ui_sidebar_setting: 'Data Flow Settings',
  metadata_createModel: 'Create a model',
  metadata_header_name: 'Table name/owned database',
  metadata_header_last_user_name: 'Last update user',
  metadata_metaType_database: 'Database',
  metadata_metaType_api: 'API',
  metadata_metaType_job: 'Job',
  metadata_metaType_table: 'Table',
  metadata_metaType_collection: 'Collection',
  metadata_metaType_view: 'View',
  metadata_metaType_directory: 'Directory',
  metadata_metaType_dataflow: 'Data Flow',
  metadata_metaType_mongo_view: 'Mongodb View',
  metadata_metaType_ftp: 'FTP',
  metadata_metaType_apiendpoint: 'API End Point',
  metadata_details_model: 'Model',
  metadata_details_collection: 'Collection',
  metadata_details_collectionName: 'Collection name',
  metadata_details_createCollection: 'Create collection',
  metadata_details_dataDirectory: 'Data Directory',
  metadata_details_dataDetails: 'Data Details',
  metadata_details_basicAttributes: 'Basic Properties',
  metadata_details_businessAttributes: 'Custom Properties',
  metadata_details_clickAddDes: 'Click to add a description',
  metadata_details_propertyDetails: 'Property Details',
  metadata_details_comment: 'Description',
  metadata_details_data_type: 'Field Type',
  metadata_details_precision: 'Precision',
  metadata_details_columnSize: 'Column size',
  metadata_details_scale: 'Scale',
  metadata_details_autoincrement: 'Autoincrement',
  metadata_details_primary_key_position: 'Primary key',
  metadata_details_foreign_key_position: 'Foreign key',
  metadata_details_is_nullable: 'Not Null',
  metadata_details_unique: 'Unique',
  metadata_details_originalTableName: 'Original name',
  metadata_details_owningConnection: 'Connection',
  metadata_details_primaryKey: 'PK',
  metadata_details_source: 'Source',
  metadata_details_founder: 'Creater',
  metadata_details_Modifier: 'Editor',
  metadata_details_renamed: 'Renamed',
  metadata_details_searchPlaceholder: 'Field name/alias/description',
  metadata_details_selsectSource: 'Select source',
  metadata_details_createFiled: 'Create field',
  metadata_details_editFild: 'Edit field',
  metadata_details_prohibitOverwriting: 'Batch overwriting is prohibited',
  metadata_details_batchCoverage: 'Batch coverage',
  metadata_details_refreshModel: 'Refresh model',
  metadata_details_filedName: 'Field name',
  metadata_details_alias: 'Alias',
  metadata_details_fieldType: 'Field Type',
  metadata_details_allowOverwrite: 'Sync by DB',
  metadata_details_selfIncreasing: 'Autoincrement',
  metadata_details_fieldLength: 'Column size',
  metadata_details_accuracy: 'Precision',
  metadata_details_numberLength: 'Scale',
  metadata_details_dictionarySettings: 'Dictionary Settings',
  metadata_details_initialValue: 'Initial value',
  metadata_details_mappedValue: 'Mapping value',
  metadata_details_enterInitialValue: 'Enter initial value',
  metadata_details_enterMappedValue: 'Enter the mapped value',
  metadata_details_newMapping: 'Create new',
  metadata_details_chooseTemplate: 'Choose template',
  metadata_details_foreignKeySetting: 'Foreign Key Set',
  metadata_details_associationTable: 'Associated table',
  metadata_details_associationField: 'Associated field',
  metadata_details_connectionRelation: 'Relation type',
  metadata_details_oneone: 'one to one',
  metadata_details_onemany: 'one to many',
  metadata_details_manyone: 'many to one',
  metadata_details_addRelatedTable: 'Create new',
  metadata_details_filedAliasName: 'Field name/Alias',
  metadata_details_Float: 'Floating point number',
  metadata_details_String: 'String',
  metadata_details_baseObject: 'Object',
  metadata_details_Array: 'Array',
  metadata_details_Map: 'Dictionary Object',
  metadata_details_Short: 'Short integer',
  metadata_details_Long: 'Long integer',
  metadata_details_Double: 'Double Precision',
  metadata_details_Byte: 'Byte',
  metadata_details_Bytes: 'Number of bytes',
  metadata_details_BigDecimal: 'Decimal',
  metadata_details_Boolean: 'Boolean value',
  metadata_details_Date: 'Date',
  metadata_details_Integer: 'Integer',
  metadata_details_dictionary_typeNo:
    'This field type cannot add a dictionary template',
  metadata_details_fieldNameNo: 'The field name is empty',
  metadata_details_moreAttributes: 'More Properties',
  metadata_details_msgFiledName: 'Please enter the field name',
  metadata_details_success_Release:
    'Save successfully, please republish manually',
  metadata_details_filedName_repeat: 'Field name cannot be the same name',
  metadata_details_filedDictionary: 'Field Dictionary',
  metadata_details_foreignKeyAssociation: 'Foreign key',
  metadata_details_tableLayering: 'Category',
  metadata_details_theme: 'Theme',
  metadata_details_taskReference: 'Task Reference',
  metadata_details_APIReference: 'API Reference',
  metadata_details_creat: 'New',
  metadata_details_businessAttrTitle: 'Business attribute',
  metadata_details_attrName: 'Property name',
  metadata_details_attrKey: 'Property value',
  metadata_details_editAliasNameTitle: 'Edit alias',
  metadata_details_editCommentTitle: 'Edit description',
  metadata_details_uniquelyIdentifies: 'Qualified Name',
  metadata_details_query: 'Query',
  metadata_details_version_version_control: 'Version Control Mode',
  metadata_details_version_version_control_required:
    'Version Control Mode is required',
  metadata_details_version_lastVersion:
    'This metadata is already the latest version, the historical version records saved in the past will be saved in the list below',
  metadata_details_version_versionNum: 'Version number',
  metadata_details_version_versionComparison: 'Version comparison',
  metadata_details_version_compared: 'Compared',
  metadata_details_version_currentVersion: 'Current Plan',
  metadata_details_version_operator: 'Operator',
  metadata_details_version_modifyDescription: 'Modify Description',
  metadata_details_Modify_property: 'Modify property',
  metadata_details_Modify_field: 'Modify field property',
  metadata_details_Add_property: 'Add property',
  metadata_details_Add_new_field: 'Add new field in schema',
  metadata_details_Remove_property: 'Remove property',
  metadata_details_Remove_field: 'Remove field from schema',
  metadata_details_index_title: 'Index',
  metadata_details_index_name: 'Index name',
  metadata_details_index_create: 'Create index',
  metadata_details_index_fields: 'Time fields',
  metadata_details_index_unique: 'Unique constraint',
  metadata_details_index_status: 'Status',
  metadata_details_index_create_by: 'Create user',
  metadata_details_index_background: 'Background',
  metadata_details_index_properties: 'Properties',
  metadata_details_index_definition: 'Field name',
  metadata_details_index_options: 'Options',
  metadata_details_index_build_in_background: 'Build index in the background',
  metadata_details_index_create_unique: 'Create unique index',
  metadata_details_index_create_ttl: 'Create TTL',
  metadata_details_index_name_exists: 'Index name must be unique',
  metadata_details_index_index_exists: 'Index already exists',
  metadata_details_index_create_by_user: 'Platform user',
  metadata_details_index_create_by_dba: 'Database Administrator',
  metadata_details_index_status_creating: 'Creating',
  metadata_details_index_status_created: 'Create complete',
  metadata_details_index_status_creation_failed: 'Creation failed',
  metadata_details_index_status_deleted: 'Deleted',
  metadata_details_index_drop_index: 'Deleting index',
  metadata_details_index_unique_true: 'Unique',
  metadata_details_index_unique_false: 'not unique',
  metadata_details_validation_title: 'Data verification',
  metadata_details_validation_field_name: 'Field name',
  metadata_details_validation_rule: 'Rule',
  metadata_details_validation_ruleTem: 'Rule Template',
  metadata_details_validation_select_rule: 'Selection rules',
  metadata_details_validation_ungrouped: 'Ungrouped',
  metadata_details_validation_create: 'Create data verification',
  metadata_details_preview_title: 'Data Preview',
  metadata_details_pipeline_title: 'Pipeline',
  metadata_details_pipeline_collection: 'Data table',
  metadata_details_pipeline_pipeline: 'MongoDB Pipeline',
  metadata_details_pipeline_viewStatus: 'View Status',
  metadata_details_pipeline_FailedMessage: 'Failed details',
  metadata_details_pipeline_penpinSave:
    'Click the save button below to only save to the system, click the update button to apply to the database where this data is located',
  metadata_details_pipeline_apply: 'Apply',
  metadata_details_pipeline_view_tip:
    'The operation will overwrite the view with the same name, whether to create a view',
  metadata_details_pipeline_success: 'Apply successful',
  metadata_details_pipeline_failed: 'Application failed',
  metadata_metadataSearch_title: 'Metadata retrieval',
  metadata_metadataSearch_desc:
    'Metadata retrieval provides search functions for the names, aliases, descriptions of tables and fields, please select the search table/field first, then enter the content, and click the search button to search',
  metadata_metadataSearch_table: 'Search table',
  metadata_metadataSearch_column: 'Search field',
  metadata_metadataSearch_search: 'Search',
  metadata_metadataSearch_noSearch: 'Please press "Enter" to initiate a search',
  metadata_metadataSearch_noResult:
    'No search results, please confirm the search keywords',
  metadata_metadataSearch_noMore: 'No more search results',
  metadata_metadataSearch_more: 'Click to load more',
  metadata_metadataSearch_placeholder: 'please enter keyword to search',
  notification_stoppedByError: 'stopped by error',
  notification_CDCLag: 'CDC lag',
  notification_jobPaused: 'Job paused',
  notification_jobDeleted: 'Job deleted',
  notification_jobStateError: 'Job state error',
  notification_jobEncounterError: 'Job encounter error',
  notification_noticeInterval: 'Notice interval',
  notification_CDCLagTime: 'CDC lag time',
  notification_lagTime: 'Lag-time',
  notification_DDL: 'Database DDL changes',
  notification_agentNotice: 'Agent notice',
  notification_serverDisconnected: 'Server disconnected',
  notification_agentStarted: 'Agent started',
  notification_agentStopped: 'Agent stopped',
  notification_agentCreated: 'Agent created',
  notification_agentDeleted: 'Agent deleted',
  notification_inspectCount: 'Verify job count difference',
  notification_inspectValue: ' Verify job field value difference',
  notification_inspectDelete: ' Verify job was deleted',
  notification_inspectError: 'Verify job error',
  notification_placeholder_user: 'Choose operator',
  notification_placeholder_keyword: 'search by datasource/job name',
  notification_account: 'Account ',
  notification_operation_create: ' created ',
  notification_operation_update: ' updated ',
  notification_operation_delete: ' deleted ',
  notification_operation_start: ' started ',
  notification_operation_stop: ' stopped ',
  notification_operation_forceStop: ' force stopped ',
  notification_operation_reset: ' reset ',
  notification_operation_copy: ' copied ',
  notification_operation_upload: 'upload ',
  notification_operation_download: ' downloaded ',
  notification_operation_login: ' login ',
  notification_operation_logout: ' logout ',
  notification_modular_sync: 'Sync job',
  notification_modular_migration: 'Migration job',
  notification_modular_connection: ' connection ',
  notification_modular_dataflow: ' data flow job ',
  notification_modular_inspect: ' verity job ',
  notification_modular_ddlDeal: ' DDL',
  notification_modular_system: ' System ',
  notification_modular_user: 'user',
  notification_modular_role: 'role',
  notification_modular_accessCode: 'access code',
  notification_operation_readnotification_modular_message: '',
  dialog_placeholderTable:
    'Only supports English, numbers, underscores, minus signs, dots, and starts with English letter',
  dialog_downAgent_ok: 'OK',
  dialog_jobSchedule_runDay: "Run at 2 o'clock every day",
  queryBuilder_addCond: 'field Cond',
  account_accountSettings: 'Account settings',
  account_systemSetting: 'System Settings',
  account_email: 'Email',
  account_userName: 'User name',
  account_accessCode: 'Access Code',
  account_changePassword: ' Change Password',
  account_currentPassword: 'Please enter the current password',
  account_newPassword: 'Please enter the new password',
  account_confirmPassword: 'Confirm password again',
  account_changeEmail: 'Change Email',
  account_enterMailbox: 'Please enter mailbox',
  account_enterNewMailbox: 'Please enter the new mailbox',
  account_changeUsername: 'Change User name',
  account_newUsername: 'Please enter a new username',
  account_sendEmail: 'Sent verify email',
  account_samePawTip:
    'The new password cannot be the same as the original password.',
  account_newPawInconsistent: 'Inconsistent with the new password.',
  account_pawSaveSuccess: 'Password saved successfully',
  account_currerPawErrorTip:
    'The current password is incorrect, please enter the correct password',
  account_nameModifySuccess: 'Name modified successfully',
  account_passwordNotCN:
    'Only alphanumeric characters and hyphens are allowed in password',
  account_user_null: "That username's been taken",
  account_has_username: 'Username already exists',
  account_editFail: 'User name modification failed',
  role_allData: 'All role data',
  role_functionDataPermission: 'Function and data permissions',
  role_module: 'Module',
  role_choosePermissionTip:
    'Please select the functions and data permissions available for this role (checking all role data means you can browse or operate the data of all roles, unchecking means you can only browse or operate your own data )',
  role_funcPermission: 'Function permission settings',
  role_currentRole: 'Current role',
  role_pageVisible: 'Page permission settings',
  role_pageShowTip:
    'Checked means navigation and page are visible to the current character, unchecked will not display',
  role_choosePage: 'Select Page permission',
  role_bulkOperate: 'Select all',
  role_chooseAllFunction: 'Select all functions',
  role_chooseAllRole: 'All role data',
  role_settingTitle: 'Set Up Permission',
  guide_task_type_custom_tips:
    "Data synchronization focuses on table-level data processing and transmission, to meet the needs of users to achieve multi-table (data set), multi-table integration between multi-level data, data splitting, association mapping, field increase and decrease merge, content filtering, Real-time data synchronization is realized at the same time in the case of aggregate processing JS processing and other functions. Without affecting the user's business, it meets the user's needs for various business scenarios such as remote or local data disaster recovery, cross-instance data synchronization, query and report distribution, and real-time data warehouse management. ",
  guide_btn_to_dashboard: "Don't edit the task for now, go shopping first",
  user_creatUser: 'Create user',
  timeToLive_w: 'week',
  timeToLive_mo: 'month',
  timeToLive_y: 'year',
  dictionary_isMappedvalue: 'The mapped value cannot be empty',
  dataRule_classification: 'Classification',
  dataRule_rule: 'Rule',
  dataRule_data_type: 'Field Type',
  dataRule_data_Nullable: 'Nullable',
  dataRule_data_Range: 'Range',
  dataRule_data_Enum: 'Enumeration',
  dataRule_data_Regex: 'Regex',
  dataRule_greater_that: 'greater than',
  dataRule_less_that: 'less than',
  dataRule_enum_required: 'Enum value is required',
  dataRule_gt_lt_none: 'The range boundary cannot be none at the same time',
  dataRule_data_type_required: 'Data type is required',
  dataRule_data_regex_required: 'Data regex is required',
  dataRule_correct_rules: 'Please enter the correct rules',
  dataRule_pleaseNum: 'Please enter a number',
  dataRule_dataType_baseFloating: 'Float',
  dataRule_dataType_baseObject: 'Object',
  dataRule_dataType_baseBinarydata: 'Binarydata',
  dataRule_dataType_baseString: 'String',
  dataRule_dataType_baseArray: 'Array',
  dataRule_dataType_baseUndefined: 'Undefined',
  dataRule_dataType_baseBoolean: 'Booleanvalue',
  dataRule_dataType_basedate: 'Date',
  dataRule_dataType_baseNull: 'Null',
  dataRule_dataType_baseRegularexpression: 'Regularexpression',
  dataRule_dataType_baseShorttype: 'Shorttype',
  dataRule_dataType_baseTimestamp: 'Timestamp',
  dataRule_dataType_baseLonginteger: 'Longinteger',
  daas_notification_alarmnotification_gaojingtongzhi: 'Alert notification',
  daas_notification_center_xitonggaojing: 'System alert',
  daas_notification_systemalarm_guanbichenggong: 'Close successfully',
  daas_notification_systemalarm_gaojingshijian: 'Alert time',
  daas_notification_systemalarm_gaojingzhuangtai: 'Alert status',
  daas_notification_systemalarm_gaojingduixiang: 'Alert object',
  daas_notification_systemalarm_quanbugaojing: 'All alerts',
  daas_setting_alarmnotification_gaojingzhibiao: 'Alert indicator',
  daas_setting_alarmnotification_dangjiediandeping:
    'When the average processing time of the node',
  daas_setting_alarmnotification_dangshujuyuanjie:
    'When the average processing time of the data source node',
  daas_setting_alarmnotification_dangshujuyuanxie:
    'When the data source protocol connection takes time',
  daas_setting_alarmnotification_dangshujuyuanwang:
    'When the data source network connection takes time',
  daas_setting_alarmnotification_dangshujuwufa:
    'It takes time when the data cannot be connected to the network',
  daas_setting_alarmnotification_dangrenwudezeng:
    'When the replication delay of the task',
  daas_setting_alarmnotification_dangrenwutingzhi: 'When the task stops',
  daas_setting_alarmnotification_dangrenwuzengliang:
    'When the task increment start',
  daas_setting_alarmnotification_dangrenwuquanliang:
    'When the task is fully completed',
  daas_setting_alarmnotification_dangrenwujiaoyan:
    'When the task verification error occurs',
  daas_setting_alarmnotification_dangrenwuyudao:
    'When the task encounters an error',
  daas_setting_alarmnotification_dangrenwustop: 'When the Agent service stops',
  daas_setting_alarmnotification_msshigaojing: 'Alert when ms',
  daas_setting_alarmnotification_gedian: 'points',
  daas_setting_alarmnotification_lianxu: 'Continuous',
  daas_setting_alarmnotification_cichugaojinggui:
    'The alert rule setting here is the system global alert rule setting, and the priority of the alert rule setting on the task running monitoring page is higher than the system global setting',
  daas_setting_alarmnotification_renwumorengao:
    'Task default alert rule setting',
  daas_setting_alarmnotification_morengaojinggui: 'Default alert rule',
  daas_setting_alarmnotification_renwugaojingshe: 'Task alert setting',
  daas_setting_setting_chulijiediande:
    'The average processing time of processing nodes',
  daas_setting_setting_shujuyuanjiedian:
    'The average processing time of data source nodes',
  daas_setting_setting_shujuyuanxieyi:
    'Data source protocol connection takes time',
  daas_setting_setting_shujuyuanwanglu: 'Data source network connection time',
  daas_setting_setting_renwudezengliang: 'Replication delay for tasks',
  daas_setting_settingcenter_gaojingshezhi: 'Alert settings',
  packages_nodeDesign_custom_node_name_required: 'Please enter the node name',
  packages_nodeDesign_message_save_ok: 'Save Successfully',
  share_detail_title: 'Mining table details',
  shared_cache_messge_no_table: 'The selected table model is not found',
  shared_cache_placeholder_max_memory: 'Please enter the maximum cache memory',
  shared_cache_placeholder_external_storage:
    'Please select the external memory configuration',
  setting_newMongodbChangeStream: '111111',
  user_list_activetion_error: 'Activation failed',
  notify_schema_name: 'Model name',
  role_name_API_clients_amangement: 'API client management',
  signin_verify_email_invalid: 'Please enter a valid email address',
  dataFlow_atabseProcessingHead: 'Data processing synchronization',
  dataFlow_shareCdcMode: 'Shared incremental read mode',
  dataFlow_delete_confirm_title: 'Do you want to delete this task?',
  dataFlow_delete_confirm_message:
    'After deleting task xxx, this task will not be able to Restore',
  dataFlow_bulk_delete_confirm_title: 'Do you want to delete tasks in batches?',
  dataFlow_bulk_delete_confirm_message:
    'After deleting tasks in batches, the tasks cannot be restored',
  modules_apiServerStatus: 'API service status',
  modules_describtion: 'Description',
  modules_set_mode: 'Setting method',
  app_signIn_registry_sucess_wait_approval:
    "Wait for the administrator's approval before login, and you will be redirected to the login page in 5 seconds ",
  dataFlow_databsenProcessing:
    'In a guided mode, it helps novice users to quickly understand table-level data processing and synchronization. In addition to realizing table-level full or incremental transfer functions, this function pays more attention to the use of various processors (JS processing, field filtering, etc.) , aggregation processing, row-level filtering, etc.) to perform complex logical processing to meet the higher data processing needs of users',
  dataFlow_button_milestone: 'task milestones',
  daas_deletefile_emptyitem_zanwushuju: 'No data',
  daas_components_querycond_xuanzeriqishi: 'Select date and time',
  daas_src_main_qingqiuquanjupei:
    'Request global configuration (settings) failed: ',
  daas_src_main_baocuntok: 'Save token to cookie:',
  daas_api_page_apidocandtest_shouquanjiekou: 'Authorization interface',
  daas_api_page_apidocandtest_daochudaopo: 'Export to postman',
  daas_data_discovery_previewdrawer_qingshurumingcheng: 'Please Enter name',
  daas_data_server_drawer_qingshurucanshu: 'Please enter parameter name',
  daas_data_server_drawer_paixu: 'Sort',
  daas_data_server_drawer_meigefenyefan: 'Number of records returned per page',
  daas_data_server_drawer_fenyebianhao: 'Pagination number',
  daas_data_server_drawer_zidingyichaxun: 'Custom query',
  daas_data_server_drawer_morenchaxun: 'Default query',
  daas_data_server_drawer_qingxuanzeduixiang: 'Please select the object name',
  daas_data_server_drawer_qingxuanzelianjie: 'Please Select connection type',
  daas_data_server_drawer_qingshurufuwu: 'Please enter service name',
  daas_data_server_drawer_quanxianfanwei: 'Authority Range',
  daas_data_server_drawer_selectPermissions:
    'Please select the scope of authority',
  daas_data_server_drawer_shilidaima: 'Sample Code',
  daas_data_server_drawer_shilidaima2: 'Sample Code',
  daas_data_server_drawer_fanhuijieguo: 'Return Result',
  daas_data_server_drawer_diaoyongfangshi: 'Call method',
  daas_data_server_drawer_fuwufangwen: 'Service Access',
  daas_data_server_drawer_shuchujieguo: 'Output Result',
  daas_data_server_drawer_pailietiaojian: 'Order condition',
  daas_data_server_drawer_shaixuantiaojian: 'Filter condition ',
  daas_data_server_drawer_canshuzhi: 'parameter value',
  daas_data_server_drawer_canshumingcheng: 'Parameter Name',
  daas_data_server_drawer_shurucanshu: 'Input Parameter',
  daas_data_server_drawer_jiekouleixing: 'Interface Type',
  daas_data_server_drawer_fabujiedian: 'Publishing Node',
  daas_data_server_drawer_caozuoleixing: 'Operation Type',
  daas_data_server_drawer_zanwumiaoshu: 'no description yet',
  daas_data_server_drawer_tiaoshi: 'Debug',
  daas_data_server_drawer_peizhi: 'Configuration',
  daas_data_server_drawer_chuangjianfuwu: 'Create API',
  daas_data_server_drawer_fuwuxiangqing: 'Service Details',
  daas_data_server_list_quedingchexiaogai:
    'Are you sure you want to revoke this service?',
  daas_data_server_list_quedingfabugai:
    'Are you sure you want to publish the service?',
  daas_data_server_list_querenshanchufu:
    'Are you sure you want to delete the service?',
  daas_data_server_list_huoqufuwuyu: 'Get the service domain name Failed.',
  daas_data_server_list_fuwuzhuangtai: 'Service Status',
  daas_data_server_list_guanlianduixiang: 'Associated Object',
  daas_data_server_list_fuwumingcheng: 'Service Name',
  daas_function_importform_shangchuanwenjianda:
    'Upload file size cannot exceed {val1}M',
  daas_login_login_dengluchenggong: 'Login successful:',
  daas_login_passwordreset_shangweiyanzhengdian: 'Email not verified',
  daas_login_passwordreset_zhaobudaodianzi: 'Email not found',
  daas_metadata_search_yuanbiaoming: ' (Original table name:',
  daas_shared_mining_detail_wajuexiangqingx: 'Mining details x-axis:',
  daas_data_discovery_previewdrawer_jiedian: 'Node',
  daas_data_discovery_previewdrawer_renwumiaoshu: 'Task Description',
  daas_data_discovery_previewdrawer_yinqingmiaoshu: 'Engine Description',
  daas_data_discovery_previewdrawer_yinqingmingcheng: 'Engine Name',
  daas_data_discovery_previewdrawer_jiedianshu: 'Number of nodes',
  daas_data_discovery_previewdrawer_shuchucanshu: 'Output Parameters',
  daas_data_discovery_previewdrawer_fuwumiaoshu: 'Service Description',
  daas_data_discovery_previewdrawer_jiedianmiaoshu: 'Node Description',
  daas_data_discovery_previewdrawer_shurujiedian: 'Input Node',
  daas_data_discovery_previewdrawer_shuchujiedian: 'Output Node',
  daas_router_routes_guanlianrenwuxiang: 'Associated task details',
  daas_data_server_drawer_geshicuowu: 'Format error',
  daas_data_server_drawer_validate:
    'Only alphanumeric letters and underscores are allowed and must start with a letter.',
  daas_data_server_drawer_aPI_path_Settings: 'API Path Settings',
  daas_data_server_drawer_default_path: 'Default Path',
  daas_data_server_drawer_custom_path: 'Custom Path',
  daas_data_server_drawer_prefix: 'Prefix',
  daas_data_server_drawer_base_path: 'Base Path',
  daas_data_server_drawer_path: 'Path',
  daas_data_server_drawer_confirm_tip:
    'This will re-generate the API path, do you wish to continue?',
  daas_notification_center_yonghucaozuo: 'User Action',
  daas_cluster_cluster_lianjieshuliang: 'Number of connections',
  daas_cluster_cluster_mubiaoIPhe: 'Target IP and port',
  daas_cluster_cluster_lianjiezongshu: 'Total number of connections',
  daas_cluster_cluster_yinqingduiwaijian:
    'The number of external connections established by the engine',
  daas_role_role_ninhaiweibaocun:
    'You have not saved the permission settings, do you want to save the permission settings? ',
  daas_role_role_quanbugongneng: 'All Functions',
  daas_role_role_chakanquanbushu: 'View all data',
  daas_role_role_gongnengquanxian: 'Function Permission',
  daas_role_role_yemianquanxian: 'Page Permission',
  daas_role_role_gongnengmokuai: 'Function Module',
  daas_role_role_gouxuanxiangyingmo:
    'Check the corresponding module to indicate that the navigation is visible to users under the current role, and enable [View All Data] to indicate that the role can view and operate all the data under this module, and if it is not checked, it can only view and operate the data created and authorized by itself data. ',
  daas_feature_unavailable: 'Restricted Feature',
  daas_feature_unavailable_subtitle:
    'This feature is only available in Enterprise and/or Cloud versions. Please sign up for our cloud version or contact us for Enterprise version.',
  daas_feature_unavailable_upgrade_dec: "By upgrading , you'll get:",
  daas_feature_unavailable_upgrade_dec_li1:
    'Data validation (Enterprise edition only)',
  daas_feature_unavailable_upgrade_dec_li2: 'CDC Log Cache',
  daas_feature_unavailable_upgrade_dec_li3: 'Alarm settings',
  daas_feature_unavailable_upgrade_dec_li4:
    'Permission management (Enterprise edition only)',
  daas_feature_unavailable_upgrade_dec_li5: 'More data sources',
  daas_feature_unavailable_upgrade_dec_li1_desc: `${import.meta.env.VUE_APP_PAGE_TITLE} ensures data consistency with proprietary technology and supports data table validation to meet production requirements.`,
  daas_feature_unavailable_upgrade_dec_li2_desc: `${import.meta.env.VUE_APP_PAGE_TITLE} reduces source database load by supporting shared incremental log cache, fetching events directly from the cache without repeated reads.`,
  daas_feature_unavailable_upgrade_dec_li3_desc: `${import.meta.env.VUE_APP_PAGE_TITLE} sends alert emails via SMTP, allowing users to promptly receive anomaly notifications and ensure task stability.`,
  daas_feature_unavailable_upgrade_dec_li4_desc:
    'Roles are collections of permissions assigned to users. This simplifies management and enhances security by allowing role creation before user assignment.',
  daas_feature_unavailable_upgrade_dec_li5_desc: '',
  daas_feature_unavailable_get_enterprise: 'Get TapData Enterprise',
  daas_feature_unavailable_get_cloud: 'Try TapData Cloud',
  daas_feature_unavailable_go_to_compare: 'Compare Versions',
  daas_unbind_license: 'Unbind License',
  daas_cluster_cluster_view: 'Cluster View',
  daas_cluster_component_view: 'Component View',
  daas_cluster_engine_hostname: 'Hostname/IP',
  daas_cluster_connection_count: 'Connection Count',
  daas_cluser_keyword_placeholder: 'Search for hostnames',

  webhook_alerts: 'Webhook Alerts',
  webhook_alerts_detail: 'Webhook Alert Details',
  webhook_alerts_add: 'Create New Webhook',
  webhook_address: 'Webhook Address',
  webhook_params: 'Parameters',
  webhook_switch: 'Enabled',
  webhook_send_log: 'Send Records',
  webhook_send_log_desc: '(Only the last 200 records are retained)',
  webhook_send_address: 'Send Address',
  webhook_server_url: 'Server URL',
  webhook_server_url_empty: 'Please enter the server URL',
  webhook_server_url_error: 'Please enter a valid server URL',
  webhook_custom_template: 'Custom Template',
  webhook_custom_template_tip: `{'{'} 
    "action": "TaskAlter",
    "hookId": "\${'{'}hookId{'}'}",
    "actionTime": "\${'{'}actionTime{'}'}",
    "title": "\${'{'}title{'}'}",
    "content": "\${'{'}content{'}'}",
    "actionData": {'{'} 
        "status": "\${'{'}actionData.status{'}'}", // Indicates the current alarm status: ING (ongoing), RECOVER (recovered), CLOSE (closed)
        "statusTxt": "\${'{'}actionData.statusTxt{'}'}", // Indicates the current alarm status text: ongoing, recovered, closed
        "level": "\${'{'}actionData.level{'}'}", // Alarm level: RECOVERY, NORMAL, WARNING, CRITICAL, EMERGENCY, ERROR, WARN, INFO
        "component": "\${'{'}actionData.component{'}'}", // Engine alarm component, fixed as: FE
        "componentTxt": "\${'{'}actionData.componentTxt{'}'}", // Engine alarm component text value: Engine
        "type": "\${'{'}actionData.type{'}'}", // Alarm type: SYNCHRONIZATIONTASK_ALARM, SHARED_CACHE_ALARM, SHARED_MINING_ALARM, DATA_VERIFICATION_ALARM, ACCURATE_DELAY_ALARM, INSPECT_ALARM
        "typeTxt": "\${'{'}actionData.typeTxt{'}'}", // Alarm type text value: synchronization task alarm, shared cache alarm, shared mining alarm, data verification alarm, accurate delay alarm
        "metric": "\${'{'}actionData.metric{'}'}", // Event type: TASK_STATUS_STOP, TASK_STATUS_ERROR, TASK_FULL_COMPLETE, TASK_INCREMENT_START, TASK_INSPECT_ERROR, INSPECT_TASK_ERROR, DATANODE_CANNOT_CONNECT, DATANODE_TCP_CONNECT_CONSUME, DATANODE_HTTP_CONNECT_CONSUME, SYSTEM_FLOW_ENGINE_UP, SYSTEM_FLOW_ENGINE_DOWN, DATANODE_AVERAGE_HANDLE_CONSUME, TASK_INCREMENT_DELAY, PROCESSNODE_AVERAGE_HANDLE_CONSUME, INSPECT_COUNT_ERROR, INSPECT_VALUE_ERROR
        "metricTxt": "\${'{'}actionData.metricTxt{'}'}", // Event type text value: task stopped, task error, task fully completed, task increment started, task inspection error, inspection task error, data node cannot connect, data node TCP connection completed, data node HTTP connection completed, engine online, engine offline, data node average handling time exceeded threshold, task increment delay exceeded threshold, node average handling time exceeded threshold, count inspection result row difference exceeded threshold, value inspection result data difference exceeded threshold
        "name": "\${'{'}actionData.name{'}'}", // Specific task name
        "node": "\${'{'}actionData.node{'}'}", // Node name that generated the alarm, empty if no node; for task alarms, the node is the task name
        "currentValue": "\${'{'}actionData.currentValue{'}'}", // Value of the metric that triggered the alarm
        "threshold": "\${'{'}actionData.threshold{'}'}", // Threshold of the metric that triggered the alarm
        "lastOccurrenceTime": "\${'{'}actionData.lastOccurrenceTime{'}'}", // Time when the alarm last occurred
        "tally": "\${'{'}actionData.tally{'}'}", // Number of times the alarm has occurred
        "summary": "\${'{'}actionData.summary{'}'}", // Alarm content
        "recoveryTime": "\${'{'}actionData.recoveryTime{'}'}", // Alarm recovery time
        "closeTime": "\${'{'}actionData.closeTime{'}'}", // Alarm close time
        "closeBy": "\${'{'}actionData.closeBy{'}'}", // Who closed the alarm
        "agentId": "\${'{'}actionData.agentId{'}'}" // Belonging engine
    {'}'}
{'}'} `,
  webhook_custom_template_ph:
    'Custom template content, supports parameter-filled templates, e.g., ${alarm.name}',
  http_header: 'HTTP Request Headers',
  http_header_ph:
    'HTTP request headers, enter multiple headers on separate lines, e.g., Accept: text/html',
  webhook_send_ping: 'Send Test PING Event',
  webhook_event_type: 'Event Type',
  webhook_event_type_empty: 'Please select an event',
  daas_licenseType: 'Type',
  daas_licenseType_pipeline: 'Pipeline',
  daas_licenseType_lite: 'Lite',
  daas_licenseType_service: 'Service',
  daas_licenseType_op: 'Standard',
  daas_datasourcePipeline: 'Pipeline',
  daas_datasourcePipelineLimit: 'Pipeline Number',
  daas_datasourcePipeUsageDetails: 'Pipeline Usage Details',
  account_accessCode_confirm: 'Confirm Refresh Access Code?',
  account_accessCode_tip:
    'Refreshing the access code will invalidate the current code, and the system will generate a new one.<b class="color-warning">Update the access code in the engine\'s configuration file and restart it to ensure proper functioning. Handle with care.</b>',
  account_accessCode_success: 'Access code refreshed successfully',
}
