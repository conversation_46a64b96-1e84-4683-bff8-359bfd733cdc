export default {
  // 通用按钮
  button_reload: '重新加载',
  button_button: '新增',
  // 通用消息
  message_loading: '正在加载',
  message_network_connected: '网络已恢复',
  //页面标题
  page_title_overview: '概览',
  page_title_dashboard: '首页',
  page_title_connections: '连接管理',
  page_title_connections_create: '创建连接',
  page_title_connections_edit: '编辑连接',
  page_title_data_pipeline: '数据管道',
  page_title_advanced_features: '高级功能',
  page_title_data_copy: '数据复制',
  page_title_task_edit: '编辑任务',
  page_title_task_details: '任务详情',
  page_title_task_stat: '任务统计',
  page_title_run_monitor: '运行监控',
  page_title_data_develop: '数据转换',
  page_title_data_verify: '数据校验',
  page_title_data_difference_details: '差异详情',
  page_title_data_verification_result: '校验结果',
  page_title_diff_verification_history: '差异校验历史',
  page_title_diff_verification_details: '差异校验详情',
  page_title_shared_mining: '共享挖掘',
  page_title_heartbeat_table: '心跳任务',
  page_title_shared_mining_details: '挖掘详情',
  page_title_function: '函数管理',
  page_title_function_import: '导入函数',
  page_title_function_create: '创建函数',
  page_title_function_edit: '编辑函数',
  page_title_function_details: '函数详情',
  page_title_shared_cache: '共享缓存',
  page_title_shared_cache_create: '创建缓存',
  page_title_shared_cache_edit: '编辑缓存',
  page_title_data_discovery: '数据发现',
  page_title_data_object: '数据对象',
  page_title_data_catalogue: '数据目录',
  page_title_data_service: '数据服务',
  page_title_data_server_list: '服务管理',
  page_title_api_application: '应用管理',
  page_title_api_client: '客户端',
  page_title_api_servers: '服务器',
  page_title_api_audit: '服务审计',
  page_title_api_audit_details: '审计详情',
  page_title_api_monitor: '服务监控',
  page_title_system: '系统管理',
  page_title_data_metadata: '元数据管理',
  page_title_cluster: '集群管理',
  page_title_user: '用户管理',
  page_title_role: '角色管理',
  page_title_setting: '系统设置',
  page_title_webhook_alerts: 'Webhook 告警',
  page_title_license: 'License管理',
  page_title_back_menu: '返回菜单',
  page_title_custom_node: '自定义节点',
  page_title_account: '个人设置',
  page_title_external_storage: '外存管理',
  page_title_verification_create: '新建校验',
  page_title_verification_edit: '编辑校验',
  page_title_verification_history: '校验历史',
  page_title_data_console: '数据面板',
  // -- 多表选择器
  component_table_selector_candidate_label: '待复制表',
  component_table_selector_checked_label: '已选择表',
  component_table_selector_error_not_exit: '表不存在',
  component_table_selector_error: '所选表存在异常',
  component_table_selector_autofix: '清除异常表',
  component_table_selector_bulk_pick: '批量选表',
  component_table_selector_not_checked: '您暂时没有选择表',
  component_table_selector_tables_empty: '您暂时没有表，请点击右上角重新加载表',
  component_table_selector_clipboard_placeholder:
    '请输入表名称并以逗号分隔,例如：table_a,table_b',
  // app
  app_license_expire_warning: 'License剩余 {0} 天到期',
  // Agent
  agent_check_error: 'Agent当前状态异常无法创建连接，请检查',
  // 控制台
  dashboard_status_paused: '已暂停',
  dashboard_status_wait_run: '启动中',
  dashboard_all_total: '全部任务',
  dashboard_copy_total: '复制任务',
  dashboard_sync_total: '开发任务',
  dashboard_valid_total: '校验任务',
  dashboard_current_all_total: '全部任务',
  dashboard_current_copy_total: '复制任务',
  dashboard_current_sync_total: '数据转换任务',
  dashboard_current_valid_total: '校验任务',
  dashboard_copy_overview_title: '复制任务概览',
  dashboard_copy_status_title: '复制任务状态',
  dashboard_sync_overview_title: '开发任务概览',
  dashboard_sync_status_title: '开发任务状态',
  dashboard_valid_title: '数据校验概览',
  dashboard_transfer_overview: '传输总览',
  dashboard_server_title: '集群总览',
  dashboard_total_valid: '全部校验任务',
  dashboard_passed: '校验一致',
  dashboard_countDiff: 'Count不一致',
  dashboard_valueDiff: '内容差异',
  dashboard_public_error: '错误',
  dashboard_public_total: '总计',
  dashboard_initializing: '初始化中',
  dashboard_initialized: '初始化完成',
  dashboard_cdc: '增量中',
  dashboard_Lag: '增量滞后',
  dashboard_server: '服务器',
  dashboard_management: '管理端',
  dashboard_task_transfer: '任务传输',
  dashboard_api_service: 'API服务',
  dashboard_starting: '启动中',
  dashboard_running: '运行中',
  dashboard_stopping: '关闭中',
  dashboard_stopped: '已关闭',
  dashboard_restarting: '重启中',
  dashboard_total_insert: '总插入',
  dashboard_total_update: '总更新',
  dashboard_total_delete: '总删除',
  dashboard_total: '开启校验任务数',
  dashboard_diff: '校验有差异的任务数',
  dashboard_can: '支持校验任务数',
  dashboard_error: '校验出错的任务数',
  dashboard_no_data_here: '这里没用数据哦~',
  dashboard_no_statistics: '暂无{0}统计',
  // 元数据管理
  metadata_db: '所属库',
  metadata_change_name: '改名',
  metadata_name_placeholder: '请输入表名/数据库名',
  metadata_meta_type_directory: '目录',
  metadata_meta_type_table: '数据表',
  metadata_form_create: '创建模型',
  metadata_form_database: '数据库',
  metadata_form_collection: '数据集',
  metadata_form_mongo_view: 'Mongodb视图',
  metadata_detail_original_table_name: '原表名',
  metadata_detail_original_database_name: '原库名',
  // api发布
  modules_active: '已发布',
  modules_pending: '未发布',
  modules_create: '创建 API',
  modules_import: '导入',
  modules_api_test: 'API测试',
  modules_publish_api: '发布 API',
  modules_unpublish_api: '取消发布',
  modules_dialog_import_title: '任务导入',
  modules_dialog_condition: '条件',
  modules_dialog_overwrite_data: '覆盖已有数据',
  modules_dialog_skip_data: '跳过已有数据',
  modules_dialog_group: '分组',
  modules_dialog_file: '文件',
  modules_dialog_upload_files: '上传文件',
  modules_header_api_name: 'API名称',
  modules_header_dataSource: '数据源',
  modules_header_tablename: '表名称',
  modules_header_status: '状态',
  modules_header_basePath: '基础路径',
  modules_header_classifications: '分类',
  modules_header_username: '创建者',
  modules_status_deploying: '部署中',
  modules_status_starting: '正在启动',
  modules_status_running: '运行中',
  modules_status_restart: '更新中',
  modules_status_deploy_fail: '发布API失败',
  modules_status_exit: '已退出',
  modules_status_stop: '已停止',
  modules_status_ready: '有效',
  modules_status_invalid: '无效',
  modules_allacancel: '批量取消',
  modules_allarelease: '批量发布',
  modules_releasefb: '你确定要批量发布以下API吗?',
  modules_releasecancel: '你确定要批量取消以下API吗?',
  modules_api_server_status: 'API 服务状态',
  modules_sure: '你确定要',
  modules_cancel_failed: '取消发布API失败',
  modules_name_placeholder: '请输入表名/数据库名',
  module_form_connection: '数据库',
  module_form_tablename: '表名称',
  module_form_default_Api: '默认CURD API',
  module_form_customer_Api: '自定义API',
  module_form_noPath: '请添加路径',
  module_form_prefix: '前缀',
  module_form_basePath: '基础路径',
  module_form_path: '路径',
  module_form_security: '权限设置',
  module_form_permission: '权限',
  module_form_choose: '选择目录',
  module_form_document: 'API 文档',
  module_form_tags: '数据目录',
  module_form_preview: '数据预览',
  module_form_public_api: '公共的',
  module_form_available_query_field: '可用查询字段',
  module_form_required_query_field: '必须的查询条件',
  module_form_validator_name:
    '只能包含中文、字母、数字、下划线和美元符号,并且数字不能开头',
  module_form_create_a_new_record: '创建新记录',
  module_form_get_record_by_id: '根据id获取记录',
  module_form_update_record_by_id: '根据id更新记录',
  module_form_delete_record_by_id: '根据id删除记录',
  module_form_get_record_list_by_page_and_limit: '分页获取记录',
  module_form_method: '方法',
  module_form_fields: '字段',
  module_form_datatype: '数据类型',
  module_form_condition: '过滤条件',
  module_form_get_api_uri_fail: '获取 API Server Uri 失败',
  module_form_name_null: '名称不能为空',
  query_build_match_condition: '匹配条件',
  query_build_any: '任意',
  query_build_addGroup: '添加组',
  query_build_removeGroup: '删除组',
  query_build_addCondition: '添加条件',
  query_build_removeCondition: '删除条件',
  query_build_show_filter: '显示过滤条件',
  query_build_queryValue: '字段值',
  query_build_add: '添加',
  // 客户端
  application_header_id: '客户端ID',
  application_header_client_name: '客户端名称',
  application_header_grant_type: '授权类型',
  application_header_client_secret: '客户端密钥',
  application_header_redirect_uri: '重定向URI',
  application_header_scopes: '权限范围',
  application_generator: '生成',
  application_show_menu: '显示到菜单',
  application_true: '是',
  application_false: '否',
  application_create: '创建客户端',
  //api 监控
  api_monitor_total_totalCount: 'API总数',
  api_monitor_total_warningApiCount: 'API访问总数',
  api_monitor_total_warningVisitCount: 'API访问告警总数',
  api_monitor_total_visitTotalLine: 'API访问总行数',
  api_monitor_total_transmitTotal: 'API传输总量',
  api_monitor_total_warningCount: 'API告警数',
  api_monitor_total_successCount: 'API成功数',
  api_monitor_total_columns_failed: '失败率(%)',
  api_monitor_total_FailRate: 'API失败率TOP排序',
  api_monitor_total_consumingTime: 'API响应时间TOP',
  api_monitor_total_rTime: '最大响应时间',
  api_monitor_total_clientName: '客户端',
  api_monitor_total_api_list: 'API列表',
  api_monitor_total_api_list_name: 'API名称',
  api_monitor_total_api_list_status: 'API状态',
  api_monitor_total_api_list_visitLine: 'API访问行数',
  api_monitor_total_api_list_visitCount: 'API访问次数',
  api_monitor_total_api_list_transitQuantity: 'API访问传输量',
  api_monitor_total_api_list_status_active: '已发布',
  api_monitor_total_api_list_status_pending: '待发布',
  api_monitor_total_api_list_status_generating: '待生成',
  api_monitor_detail_visitTotalCount: 'API访问成功次数',
  api_monitor_detail_visitQuantity: 'API传输量',
  api_monitor_detail_timeConsuming: 'API访问耗时',
  api_monitor_detail_visitTotalLine: 'API访问行数',
  api_monitor_detail_speed: 'API传输速率',
  api_monitor_detail_responseTime: 'API响应时间',
  api_monitor_detail_monitoring_period: '监控周期',
  api_monitor_detail_Monitoring_conditions: '监控条件',
  // api服务器
  api_server_user: '用户',
  api_server_create: '新建服务端',
  api_server_create_server: '创建服务器',
  api_server_process_id: 'API 服务器唯一标识',
  api_server_client_name: ' API 服务器名称',
  api_server_client_uri: 'API 服务器访问地址',
  api_server_download_API_Server_config: '下载API配置文件',
  api_server_no_available: '没有可用API服务器',
  // api浏览
  dataExplorer_query: '查询',
  dataExplorer_document: 'API 文档',
  dataExplorer_query_time: '查询使用',
  dataExplorer_render_time: '渲染使用',
  dataExplorer_tag_title: '设置标签',
  dataExplorer_show_column: '显示列',
  dataExplorer_new_document: '新增记录',
  dataExplorer_timeout: '超时时间',
  dataExplorer_unauthenticated: '您无权访问API。',
  dataExplorer_message_timeout: '连接API服务器超时,请检查API服务器是否已启动。',
  dataExplorer_publish: '在API服务器上没有找到 {id} 的API，请检查是否已发布。',
  dataExplorer_no_permissions: '您的令牌已过期，请刷新页面重试。',
  dataExplorer_datetype_without_timezone: '时间类型的时区（可选）',
  dataExplorer_mysql_datetype_without_timezone: '影响的类型：DATETIME',
  dataExplorer_export: '导出文件',
  dataExplorer_add_favorite: '收藏',
  dataExplorer_add_favorite_name: '收藏名称',
  dataExplorer_format: '格式化代码',
  dataExplorer_apiservr: 'api服务器',
  dataExplorer_base_path: '基础路径',
  // 服务审计
  apiaudit_name: 'API名称',
  apiaudit_access_type: '访问类型',
  apiaudit_visitor: '访问人员',
  apiaudit_ip: '访问人员IP',
  apiaudit_interview_time: '访问时间',
  apiaudit_visit_result: '访问结果',
  apiaudit_reason_fail: '失败原因',
  apiaudit_log_info: '日志详情',
  apiaudit_parameter: '参数',
  apiaudit_link: '链接',
  apiaudit_access_records: '访问记录数',
  apiaudit_average_access_rate: 'API 平均访问速率',
  apiaudit_access_time: '访问耗时',
  apiaudit_average_response_time: '平均响应时长',
  apiaudit_success: '成功',
  apiaudit_placeholder: '请输入名称/ID',
  // 连接
  connection_list_form_database_type: '数据库类型',
  connection_list_name: '连接名',
  connection_reload_schema_confirm_title: '重新加载 schema',
  connection_reload_schema_confirm_msg:
    '如果此库的schema过多，可能耗时较长，确定要刷新数据源的schema',
  connection_reload_schema_fail: 'Schema 加载失败',
  // Dag
  // 缓存节点提示
  task_list_status_all: '全部状态',
  task_list_important_reminder: '重要提醒',
  task_list_stop_confirm_message:
    '初始化类型的任务暂停后如果再次启动，任务会从头开始同步，确定暂停?',
  //后台数据返回
  task_status_running: '已运行',
  task_status_not_running: '未运行',
  task_info_progress: '进行中',
  // 函数管理
  function_button_import_jar: '导入jar包',
  function_details: '函数详情',
  function_tips_name_repeat: '函数名称重复',
  function_type_label: '函数类型',
  function_type_option_custom: '自定义函数',
  function_type_option_jar: '第三方jar包',
  function_type_option_system: '系统函数',
  function_name_label: '函数名称',
  function_name_placeholder: '请输入函数名称',
  function_name_repeat: '函数名称重复',
  function_class_label: '类名',
  function_file_label: 'jar文件',
  function_button_file_upload: '点击上传',
  function_file_upload_tips: '请上传jar包文件',
  function_file_upload_success: '上传成功',
  function_file_upload_fail: '上传失败',
  function_parameters_describe_label: '参数说明',
  function_parameters_describe_placeholder:
    '支持输入的参数类型以及返回参数类型的具体说明',
  function_return_value_label: '返回值',
  function_return_value_placeholder: '请输入返回值',
  function_describe_placeholder: '请输入描述',
  function_format: '命令格式',
  function_format_placeholder: '请输入命令格式',
  function_jar_file_label: 'jar文件',
  function_package_name_label: '包名',
  function_package_name_placeholder: '请输入包名',
  function_class_name_label: '类名',
  function_method_name_label: '方法名',
  function_script_label: '代码详情',
  function_script_empty: '请输入函数代码',
  function_script_missing_function_name: '缺少函数名',
  function_script_missing_function_body: '缺少函数体',
  function_script_format_error: '函数格式不正确',
  function_script_only_one: '只允许创建一个函数',
  function_import_list_title: '函数列表',
  function_button_load_function: '加载函数',
  function_message_load_function_fail: '加载函数失败',
  function_dialog_setting_title: '函数设置',
  function_message_function_empty: '请上传jar包文件并加载函数',
  function_message_delete_title: '删除函数',
  function_message_delete_content:
    '删除可能会导致已调用该函数的任务报错，确定删除该函数吗？',
  function_tips_max_size: '最大',
  // 解决方案
  solution_customer_job_logs: '客户任务日志',
  solution_error_code: '错误码',
  solution_select_placeholder_type: '请选择类型',
  solution_search_result: '结果',
  solution_search_solutions: '解决方案',
  // 共享挖掘
  shared_cdc_placeholder_task_name: '请输入挖掘任务名搜索',
  shared_cdc_placeholder_connection_name: '请输入连接名称搜索',
  shared_cdc_name: '请输入挖掘名称',
  shared_cdc_setting_select_mode: '存储模式',
  shared_cdc_setting_select_mongodb_tip: '请输入mongodb连接',
  shared_cdc_setting_select_table_tip: '请输入表名',
  shared_cdc_setting_select_time_tip: '请选择日志保存时长',
  shared_cdc_setting_message_edit_save: '保存成功，重启任务后生效',
  share_list_name: '挖掘名称',
  share_list_time_excavation: '挖掘所处时间点',
  share_list_setting: '挖掘设置',
  share_list_status: '状态',
  share_list_time: '挖掘延迟',
  share_list_edit_title: '挖掘编辑',
  share_list_edit_title_start_time: '挖掘开始时间',
  share_detail_title: '挖掘表详情',
  share_form_setting_table_name: '存储MongoDB表名',
  share_form_setting_log_time: '日志保存时长',
  share_form_edit_name: '挖掘名称',
  share_form_edit_title: '是否放弃编辑该挖掘任务',
  share_form_edit_text: '此操作不会保存已修改的内容',
  share_detail_mining_info: '挖掘信息',
  share_detail_name: '挖掘名称',
  share_detail_log_mining_time: '日志挖掘时间',
  share_detail_log_time: '日志保存时长',
  share_detail_call_task: '调用任务',
  share_detail_source_time: '源库时间点',
  share_detail_sycn_time_point: '同步时间点',
  share_detail_mining_status: '挖掘状态',
  share_detail_button_table_info: '表详情',
  share_detail_statistics_time: '统计时间',
  share_detail_incremental_time: '所处的时间点',
  // 设置
  setting_email_template: '邮件模板',
  setting_saveSuccess: '保存成功，一分钟后生效',
  setting_nameserver: '服务器名称',
  setting_Log: '日志',
  setting_SMTP: 'SMTP',
  setting_Job: '任务',
  setting_License: 'License控制',
  setting_expiredate: '到期时间',
  setting_import: '导入',
  setting_apply: '申请 license',
  setting_license_expire_date: 'License过期时间',
  setting_Worker: '进程',
  setting_Download: '下载',
  setting_Log_level: '日志等级',
  setting_maxCpuUsage: '最大CPU使用率(取值范围 0.1 ~ 1)',
  setting_maxHeapMemoryUsage: '最大堆内存使用率(取值范围 0.1 ~ 1)',
  setting_switch_insert_mode_interval:
    ' 增量模式下切换到批量插入模式间隔时间（单位：秒）',
  setting_Email_Communication_Protocol: ' 加密方式',
  setting_SMTP_Server_Port: 'SMTP 服务端口',
  setting_SMTP_Server_User: 'SMTP 服务账号',
  setting_SMTP_Server_password: 'SMTP 服务密码',
  setting_Email_Receivers: 'Email接收邮件地址',
  setting_Email_Send_Address: 'Email发送邮件地址',
  setting_SMTP_Server_Host: 'SMTP 服务Host',
  setting_Send_Email_Title_Prefix: '发送Email标题的前缀（可选）',
  setting_SMTP_Proxy_Host: 'SMTP 代理服务Host (可选）',
  setting_SMTP_Proxy_Port: 'SMTP 代理服务端口 （可选）',
  setting_Email_Template_Running: '任务启动通知',
  setting_Email_Template_Paused: '任务停止通知',
  setting_Email_Template_Error: '任务出错通知',
  setting_Email_Template_Draft: '任务被编辑通知',
  setting_Email_Template_CDC: '任务增量滞后通知',
  setting_Email_Template_DDL: 'DDL错误通知',
  setting_Clean_Message_Time: '清除消息时间',
  setting_Keep_Alive_Message: '保持在线消息',
  setting_Sample_Rate: '采样率',
  setting_task_load_threshold: '负载阈值（百分比）',
  setting_task_load_statistics_time: '负载统计时间（分钟）',
  setting_ApiServer: 'API分发设置',
  setting_Default_Limit: '默认查询返回行数',
  setting_Max_Limit: '最大查询返回行数',
  setting_Send_batch_size: '打包数据条数',
  setting_hint_Send_batch_size: '打包数据条数',
  setting_Mongodb_target_create_date: '是否在目标端数据集添加创建时间',
  setting_Mongodb_target_create_date_docs: '是否在目标端数据集添加创建时间',
  setting_System: '系统资源监控',
  setting_Collect_system_info_interval: '系统资源监控采集频率(秒)',
  setting_Interval_to_collect_system_info:
    '系统资源信息（CPU，内存，硬盘使用率）监控采集频率',
  setting_Job_Sync_Mode: '任务同步模式',
  setting_Worker_Threshold: '进程阈值',
  setting_Worker_Heartbeat_Expire: '进程心跳过期时间(秒)',
  setting_License_Key: '证书秘钥',
  setting_Enter_jobs_log_level__error_warn_info_debug_trace:
    '输入任务日志等级: error/warn/info/debug/trace',
  setting_Email_Receivers_Multiple_separated_by_semicolons:
    '邮件接收者,可输入多个，通过逗号分隔',
  setting_Keep_recent_n_hours_message_before_the_last_processed_message_s_time_:
    '保持最近n小时消息',
  setting_Store_full_record_as_embedded_document_in_target_collection_for_update_operations:
    '缓存一份当前整体数据，合并到目标数据集中',
  setting_Store_before_field_as_embedded_document_in_target_collection_before_update_operation:
    '缓存一份修改前的整体数据，合并到目标数据集中',
  setting_Store_job_script_processor_log_to_cloud: '是否传输任务日志到云端',
  setting_Validator_to_validate_data__s_sample_rate: '校验数据采样率',
  setting_retry_interval_second: '重试间隔(秒)',
  setting_max_retry_time_minute: '最大重试时间(分钟)',
  setting_Process_message_mode__consistency_fast:
    '消息处理模式 consistency/fast',
  setting_Worker_can_execute_the_nums_of_Jobs: '进程可以执行多个任务',
  setting_Worker_heartbeat_expire_time: '进程心跳过期时间',
  setting_Users: ' 用户',
  setting_Show_Page: ' 显示下载页面',
  setting_User_Registery: ' 用户注册管理',
  setting_hint_Show_Page: '显示下载页面',
  setting_hint_User_Registery:
    '用户注册类型设置。值设为 "disabled":禁止注册; 值设为 "self-signup" 启用用户自助注册; 值设为 "manual-approval" 允用户注册,但需要管理员审批。',
  setting_DR_Rehearsal: ' 灾备演习',
  setting_Mongod_path: ' Mongod 路径',
  setting_SSH_User: ' SSH 用户名',
  setting_SSH_Port: ' SSH 端口',
  setting_hint_Mongod_path: ' Mongod 路径',
  setting_hint_SSH_User: ' SSH 用户名, 用来连接Mongod的主机',
  setting_hint_SSH_Port: ' SSH 端口,用来连接Mongod的主机',
  setting_Enable_DR_Rehearsal: ' 允许灾备演习',
  setting_hint_Enable_DR_Rehearsal: ' 灾备演习开关,true表示开,false 表示关',
  setting_Download_Agent_Page: 'Agent 下载页面',
  setting_Background_Analytics: '后台分析',
  setting_Data_quality_analysis_frequency: '数据质量分析间隔(秒)',
  setting_Dashboard_data_analysis_frequency: '面板数据分析间隔(秒)',
  setting_dashboard_Analysis_Interval: '面板数据分析间隔(秒)',
  setting_quality_Analysis_Interval: '数据质量分析间隔(秒)',
  setting_Log_filter_interval: '日志过滤间隔(秒)',
  setting_Filter_the_interval_between_duplicate_logs__seconds__:
    '相同日志在指定时间内只出现一次（1分钟后生效）',
  setting__DK36: '文件下载',
  setting_File_Down_Base_Url: '地址',
  setting_Set_the_average_number_of_events_per_second_to_allow:
    '日志设置每秒允许的事件平均数量',
  setting_Log_Filter_Rate: '日志输出频率(行/秒)',
  setting_Connections: '连接设置',
  setting_Mongodb_Load_Schema_Sample_Size: 'Mongodb加载模型采样记录数(行)',
  setting_hint_Mongodb_Load_Schema_Sample_Size:
    '当MongoDB连接加载模型时，会使用该配置进行采样加载',
  setting_Enable_API_Stats_Batch_Report: ' 启用 API 统计',
  setting_Header: ' UDP 头信息',
  setting_hint_Header: ' UDP 头信息',
  setting_Size_Of_Trigger_API_Stats_Report: ' API 请求缓存最大个数',
  setting_hint_Size_Of_Trigger_API_Stats_Report:
    ' API 请求记录数到达指定个数时批量发送到管理端',
  setting_Time_Span_Of_Trigger_API_Stats_Report: ' API 请求汇报频率(秒)',
  setting_hint_Time_Span_Of_Trigger_API_Stats_Report:
    ' API 请求缓存到指定时间发送到管理端',
  setting_save: ' 保存成功，一分钟后生效',
  setting_Logout_forward_to_this_url: ' 登出跳转地址',
  setting_Check_devices: ' 重要设备检测',
  setting_ops: ' 运维展示',
  setting_server_oversee_url: ' 运维运控URL',
  setting_system: ' 系统全局',
  setting_licenseNoticeDays: ' license 到期提醒',
  setting_license_alarm: ' license 到期提前提醒（天）',
  setting_License_expiry_email_reminder_: 'license 到期提前几天提醒设置',
  setting_newMongodbChangeStream: '111111',
  setting_flow_engine_version: ' 流程引擎版本',
  setting_tapdata_agent_version: `${import.meta.env.VUE_APP_PAGE_TITLE} Agent 版本`,
  setting_doc_base_url: ' 帮助文档URL',
  setting_help: ' 帮助文档',
  setting_Ip_addresses: ' Ipv4地址(多个逗号分隔)',
  setting_hint_Ip_addresses:
    ' 需要检测的设备ipv4地址, 例如: 127.0.0.1, ***********',
  setting_PingTimeout: ' 检测超时(毫秒)',
  setting_hint_PingTimeout: ' 当超过该设置，认为设备无法连通',
  setting_Job_field_replacement: ' 非法字符替换为',
  setting_A_replacement_for_the_invalid_field_name:
    ' 一些数据库对于字段名称有特殊要求，系统将非法的字符在同步时自动做替换。MongoDB[含有".", "$"作为开头]',
  setting_true__store_log_to_cloud__false__only_store_to_local_log_file_:
    'true：将日志存储到云，false：仅存储到本地日志文件。',
  setting_When_one_document_may_be_updated_frequently_within_very_short_period_a_few_updates_within_one_second__for_instance___the_change_stream_event_received_by_downstream_processor_may_return_the__fullDocument__that_is_inconsistent_with_the_actual_version_when_the_update_was_applied_to_that_document__To_avoid_this_inconsistency__enable_this_option_to_store_the_full_document_along_with_the_update_operation__This_will_at_the_expense_of_additional_storage_and_degraded_performance_:
    '当一个文档可能在非常短的时间内频繁更新（例如，在一秒钟之内进行几次更新）时，下游处理器接收到的更改流事件可能会返回与实际版本不一致的“ fullDocument”（与实际版本不一致） 该文件。 为避免这种不一致，请启用此选项以将完整文档与更新操作一起存储。 这将以增加存储空间和降低性能为代价。',
  setting_the_before_field_contains_a_field_for_each_table_column_and_the_value_that_was_in_that_column_before_the_update_operation_:
    'before字段包含每个表列的字段以及更新操作之前该列中的值。',
  setting_Job_heart_timeout: '同步任务心跳超时（毫秒）',
  setting_job_cdc_share_mode: '增量同步任务共享模式',
  setting_job_cdc_share_mode_doc:
    '在增量同步阶段，会根据日志采集任务是否可用，自动采用共享模式。影响的数据库：Oracle',
  setting_job_cdc_share_only: '增量任务强制使用共享模式',
  setting_job_cdc_share_only_doc:
    '当增量同步任务共享模式开启，并且无法找到一个可共享的日志，将会停止任务',
  setting_test_email_success: '测试邮件已发送，请登录接收邮箱查收',
  setting_test_ldap_success: '成功连接Ldap服务',
  setting_test_email_countdown: '操作太频繁了，请稍后重试',
  setting_email_template_from: '发件人',
  setting_email_template_to: '收件人',
  setting_email_template_subject: '主题',
  setting_job_cdc_record: ' 自动保存增量事件',
  setting_job_cdc_record_doc: ' 自动保存增量事件',
  setting_job_cdc_record_ttl: ' 增量事件保存时长(天)',
  setting_job_cdc_record_ttl_doc: ' 增量事件保存时长(天)',
  setting_lagTime: '增量滞后判定时间(秒)',
  setting_connection_schema_update_hour: '数据源schema更新时间',
  setting_connection_schema_update_interval: '数据源schema更新周期（天）',
  setting_creatDuplicateSource: ' 允许创建重复数据源',
  setting_requestFailed: '请求处理失败',
  setting_Mongodb_will_use_this_sample_size_when_load_schema:
    'Mongodb will use this sample size when load schema 当MongoDB连接加载模型时，会使用该配置进行采样加载',
  setting_Switch_to_batch_insert_mode_interval__s__in_cdc_:
    '切换到cdc中的批量插入模式间隔。',
  setting_share_cdc: '共享增量',
  setting_share_cdc_persistence_mode: '共享增量存储模式',
  setting_share_cdc_persistence_memory_size: '共享增量内存缓存行数',
  setting_share_cdc_persistence_memory_size_doc:
    '该配置控制共享增量事件在内存缓存的行数',
  setting_share_cdc_persistence_mode_doc:
    '共享增量存储模式。选项: InMemory, MongoDB, RocksDB',
  setting_share_cdc_persistence_mongodb_uri_db: '存储MongoDB的连接名称',
  setting_share_cdc_persistence_mongodb_uri_db_doc:
    '该项配置只有模式选择MongoDB时生效，输入创建的MongoDB连接名称即可',
  setting_share_cdc_persistence_mongodb_collection: '存储MongoDB的表名',
  setting_share_cdc_persistence_mongodb_collection_doc:
    '该项配置只有模式选择MongoDB时生效，输入存储的表名',
  setting_share_cdc_persistence_rocksdb_path: 'RocksDB存储的本地路径',
  setting_share_cdc_persistence_rocksdb_path_doc:
    '该项配置只有模式选择RocksDB时生效，RocksDB存储的本地路径',
  setting_task_log_file_save_time: '任务日志留存时长(天)',
  setting_task_log_file_save_size: '任务日志留存大小(MB)',
  setting_task_log_file_save_count: '任务日志保留份数',
  setting_agent_log_file_save_time: '引擎日志留存时长(天)',
  setting_agent_log_file_save_size: '引擎日志留存大小(MB)',
  setting_agent_log_file_save_count: '引擎日志留存份数',
  setting_INCREMENTAL_DELAY_LINE_DATA_COEFFICIENT: '增量延迟系数',
  setting_Login: '登录设置',
  setting_Login_Single_Session: '单次会话登录',
  setting_Login_Single_Session_doc: '开启后，同一账号只允许单个会话登录',
  setting_Login_Brief_Tips: '登录简要提示',
  setting_Login_Brief_Tips_doc: '开启后，登录提示将简化',
  setting_LDAP: 'LDAP登录设置',
  setting_Ldap_Login_Enable: '使用LDAP登录',
  setting_Ldap_Server_Host: 'LDAP服务器地址',
  setting_Ldap_Server_Port: 'LDAP服务器端口',
  setting_Ldap_Base_DN: 'LDAP Base DN',
  setting_Ldap_Bind_DN: 'LDAP账号',
  setting_Ldap_Bind_Password: 'LDAP密码',
  setting_Ldap_SSL_Enable: '是否启用SSL',
  setting_Ldap_SSL_Cert: 'SSL证书',
  setting_Ldap_Server_Host_doc:
    'AD的域控制器地址，示例：ldap://ad.example.com 或 ldaps://ad.example.com',
  setting_Ldap_Server_Port_doc:
    'LDAP默认使用 389 端口，LDAPS（加密连接）使用 636 端口',
  setting_Ldap_Base_DN_doc:
    'LDAP查询的起点，用于定义在AD中的搜索范围，多个组用分号间隔，示例：cn=Users,dc=example,dc=com;cn=Test,dc=example,dc=com',
  setting_Ldap_Bind_DN_doc:
    '用于进行身份验证的用户的完整Distinguished Name (DN)，即登录AD服务器的身份，示例：<EMAIL>',
  setting_Ldap_Bind_Password_doc: '与Bind DN对应的用户密码，用于身份验证',
  user_list_user_name_email: '请输入用户名 / 邮箱',
  user_list_change_time: ' 修改时间',
  user_list_creat_user: '创建用户',
  user_list_edit_user: '编辑用户',
  user_list_user_name: '用户名',
  user_list_role: '关联角色',
  user_list_source: '来源',
  user_list_status: '状态',
  user_list_activation: '激活',
  user_list_freeze: '冻结',
  user_list_check: '校验',
  user_list_bulk_activation: '批量激活',
  user_list_bulk_freeze: '批量冻结',
  user_list_bulk_check: '批量校验',
  user_list_del_user: '删除用户 {0} 后，此用户将无法恢复',
  user_list_activetion_user: `激活用户 {0} 后，此用户将可以使用 ${import.meta.env.VUE_APP_PAGE_TITLE} 系统`,
  user_list_freeze_user: `冻结用户 {0} 后，此用户将不可以使用 ${import.meta.env.VUE_APP_PAGE_TITLE} 系统`,
  user_list_check_user: '通过校验用户 {0} 的邮箱后，此用户可以被激活',
  user_list_activetion_success: '激活成功',
  user_list_activetion_error: '激活失败',
  user_list_freeze_success: '冻结成功',
  user_list_freeze_error: '冻结失败',
  user_list_check_success: '通过校验',
  user_list_check_error: '校验失败',
  user_status_notVerified: '未验证',
  user_status_notActivated: '未激活',
  user_status_activated: '已激活',
  user_status_rejected: '已拒绝',
  user_form_role: '关联角色',
  user_form_email: '邮箱',
  user_form_email_must_valid: '请输入有效邮箱地址',
  user_form_password_null: '请输入密码, 长度为 5 ~ 32 个字符',
  user_form_pass_hint: '密码长度不能小于5大于32',
  user_form_password_not_cn: '密码仅允许英文、数字和英文标点符号',
  user_form_activation_code: '访问码',
  user_form_status: '状态',
  cluster_name: '监控名称',
  cluster_status: '状态',
  cluster_service_status: '服务状态',
  cluster_cpu_usage: 'CPU使用率',
  cluster_heap_memory_usage: '堆内存使用率',
  instance_details_shujuyuanziyuan: '数据源资源下载',
  instance_details_xianchengziyuanxia: '同步治理线程资源下载',
  cluster_update: '更新',
  cluster_running: '运行中',
  cluster_stopped: '已停止',
  cluster_sync_gover: '同步治理',
  cluster_manage_sys: '管理后台',
  cluster_add_server_mon: '添加服务监控',
  cluster_agentSetting: 'Agent 服务器设置',
  cluster_server_name: '服务器名称',
  cluster_placeholder_mon_server: '请输入监控的服务名称',
  cluster_placeholder_command: '请输入命令',
  cluster_ip_display: '网卡IP展示',
  cluster_ip_tip: '切换网卡仅改变集群管理页服务器下IP的展示，不影响任何功能',
  cluster_confirm_text: '确认',
  cluster_restart_server: '重启服务',
  cluster_unbind_server: '解绑服务',
  cluster_start_server: '启动服务',
  cluster_startup_after_add: '请启动后添加',
  cluster_startup_after_delete: '请启动后删除',
  cluster_del_message: '确定删除服务器',
  cluster_server_nickname: '服务器名称',
  cluster_command: '命令',
  license_node_name: '节点名',
  license_node_sid: '节点sid',
  license_status: 'License状态',
  license_expire_date: 'License到期时间',
  license_update_time: 'License更新时间',
  license_renew_dialog: '更新License',
  license_normal: '正常',
  license_expiring: '即将到期',
  license_expired: '已过期',
  license_try_out: '试用',
  license_copied_clipboard: '已复制到剪贴板',
  license_select_node: '请先选择节点',
  license_renew_success: '更新成功，页面即将刷新',
  // 自定义节点
  custom_node_name: '节点名称',
  custom_node_name_placeholder: '请输入节点名称搜索',
  notify_setting: '通知设置',
  notify_system_notice: '系统通知',
  notify_user_notice: '用户通知',
  notify_view_more: '查看全部',
  notify_no_notice: '暂无通知',
  notify_view_all_notify: '查看所有通知',
  notify_user_all_notice: '全部通知',
  notify_unread_notice: '未读消息',
  notify_mask_read: '标记本页为已读',
  notify_mask_read_all: '标记全部为已读',
  notify_data_flow: '任务',
  notify_sync: '数据开发',
  notify_migration: '数据复制',
  notify_notice_type: '消息类型',
  notify_notice_level: '消息级别',
  notify_manage_sever: '管理端',
  notify_inspect: '校验任务',
  notify_ddl_deal: 'DDL处理',
  notify_source_name: '源连接',
  notify_database_name: '数据库名',
  notify_schema_name: '模型名',
  notify_system: 'license到期时间',
  notify_started: '已启动',
  notify_paused: '已暂停',
  notify_edited: '被编辑',
  notify_deleted: '被删除',
  notify_abnormally_stopped: '意外停止',
  notify_stopped_by_error: '出错停止',
  notify_startup_failed: '启动失败',
  notify_stop_failed: '停止失败',
  notify_encounter_error_skipped: '运行中跳过一个ERROR',
  notify_cdc_lag: 'CDC滞后超时',
  notify_manage_sever_restart_failed: '管理端服务重启失败',
  notify_api_sever_restart_failed: 'API服务重启失败',
  notify_sync_sever_restart_failed: '同步治理服务重启失败',
  notify_connection_interrupted: '断开连接',
  notify_manage_sever_start_failed: '管理端服务启动失败',
  notify_api_sever_start_failed: 'API服务启动失败',
  notify_sync_sever_start_failed: '同步治理服务启动失败',
  notify_manage_sever_stop_failed: '管理端服务停止失败',
  notify_api_sever_stop_failed: 'API服务停止失败',
  notify_sync_sever_stop_failed: '同步治理服务停止失败',
  notify_api_sever_abnormally_stopped: 'API服务意外停止',
  notify_sync_sever_abnormally_stopped: '同步治理服务意外停止',
  notify_manage_sever_abnormally_Stopped: '管理端服务意外停止',
  notify_manage_sever_started_successfully: '管理端服务已启动',
  notify_api_sever_started_successfully: 'API服务已启动',
  notify_sync_sever_started_successfully: '同步治理服务已启动',
  notify_manage_sever_Stopped_successfully: '管理端服务已停止',
  notify_api_sever_stopped_successfully: 'API服务已停止',
  notify_sync_sever_stopped_successfully: '同步治理服务已停止',
  notify_manage_sever_restarted_successfully: '管理端服务已重启',
  notify_api_sever_restarted_successfully: 'API服务已重启',
  notify_sync_sever_restarted_successfully: '同步治理服务已重启',
  notify_new_sever_created_successfully: '新服务监控被创建',
  notify_new_sever_deleted_Successfully: '新服务监控被删除',
  notify_database_ddl_changed: '监测到数据库DDL变化',
  notify_inspect_verify_job_count: 'Count有差异',
  notify_inspect_verify_job_value: '内容有差异',
  notify_inspect_verify_job_delete: '被删除',
  notify_inspect_verify_job_error: '运行error',
  notify_approaching: '剩余 ',
  notify_system_setting: '系统设置',
  notify_tip:
    '此处通知设置为系统全局通知的设置，任务的通知设置的其优先级高于此处的全局通知设置',
  notify_job_operation_notice: '任务运行通知',
  notify_email_notice: '邮件通知',
  notify_job_started: '任务被启动',
  notify_noticeInterval: '发送间隔',
  notify_operator: '操作人',
  role_list_select_role_name: '请输入角色名',
  role_list_role_name: '角色名称',
  role_list_description: '角色描述',
  role_list_associat_users: '关联用户',
  role_list_create: '创建角色',
  role_list_edit: '编辑角色',
  role_list_default_role: '默认角色',
  role_list_setting_permissions: '设置权限',
  role_list_delete_remind: '确认删除角色 {0}',
  role_list_delete_success: '删除角色成功',
  role_form_yes: '是',
  role_form_no: '否',
  role_form_selectUser: '请选择用户名',
  role_form_connected: '已关联',
  role_form_already_exists: '角色名称重复',
  role_null: '角色名称不能为空',
  role_form_description: '角色描述不能为空',
  role_page_Dashboard_menu: '控制台',
  role_page_datasource_menu: '连接管理',
  role_page_Data_SYNC_menu: '数据复制 & 数据开发',
  role_page_Data_verify_menu: '数据校验',
  role_page_log_collector_menu: '共享挖掘',
  role_page_SYNC_Function_management_menu: '函数管理',
  role_page_custom_node_menu: '自定义节点',
  role_page_shared_cache_menu: '共享缓存',
  role_page_data_search_menu: '数据搜索',
  role_page_data_catalog_menu: '数据目录',
  role_page_data_quality_menu: '数据质量',
  role_page_data_rules_menu: '数据规则',
  role_page_time_to_live_menu: '数据生命周期',
  role_page_data_lineage_menu: '数据地图',
  role_page_API_management_menu: 'API发布',
  role_page_API_data_explorer_menu: 'API浏览',
  role_page_API_doc_test_menu: 'API测试',
  role_page_API_stats_menu: 'API统计',
  role_page_API_clients_menu: 'API客户端',
  role_page_API_server_menu: 'API服务器',
  role_page_data_collect_menu: '数据采集(旧版)',
  role_page_schedule_jobs_menu: '调度任务',
  role_page_Cluster_management_menu: '集群管理',
  role_page_agents_menu: '进程管理',
  role_page_user_management_menu: '用户管理',
  role_page_role_management_menu: '角色管理',
  role_page_system_settings_menu: '系统设置',
  role_page_dictionary_menu: '字典模板管理',
  role_page_Topology_menu: '网络拓扑',
  role_page_servers_oversee_menu: '运维运控',
  role_all_check: '全选',
  role_module_meun_Dashboard: '浏览控制台',
  role_module_meun_datasource: '连接管理',
  role_module_meun_Data_SYNC: '数据复制 & 数据开发',
  role_module_meun_SYNC_Function_management: '函数管理',
  role_module_meun_Data_verify: '数据校验',
  role_module_meun_log_collector: '共享挖掘',
  role_module_meun_custom_node: '自定义节点',
  role_module_meun_shared_cache: '共享缓存',
  role_module_meun_data_search: '数据搜索',
  role_module_meun_data_government: '数据治理分类',
  role_module_meun_data_catalog: '数据目录',
  role_module_meun_data_quality: '数据质量',
  role_module_meun_data_rules: '数据规则',
  role_module_meun_time_to_live: '数据生命周期',
  role_module_meun_data_lineage: '数据地图',
  role_module_meun_API_management: 'API发布',
  role_module_meun_API_data_explorer: 'API浏览',
  role_module_meun_API_doc_test: 'API测试',
  role_module_meun_API_stats: 'API统计',
  role_module_meun_API_clients: 'API客户端',
  role_module_meun_API_server: 'API服务器',
  role_module_meun_data_collect: '数据采集(旧版)',
  role_module_meun_schedule_jobs: '调度任务',
  role_module_meun_Cluster_management: '集群管理',
  role_module_meun_agents: '进程管理',
  role_module_meun_dictionary: '字典模板',
  role_module_meun_user_management: '用户管理',
  role_module_meun_role_management: '角色管理',
  role_module_meun_system_settings: '系统设置',
  role_name_Dashboard: '浏览控制台',
  role_name_system_notice: '消息通知',
  role_name_notice_settings: '消息通知设置',
  role_name_account_operation_history: '操作历史',
  role_name_datasource: '浏览连接管理',
  role_name_datasource_category_management: '连接管理分类管理',
  role_name_datasource_category_application: '连接管理分类应用',
  role_name_datasource_creation: '连接管理创建',
  role_name_datasource_delete: '连接管理删除',
  role_name_datasource_edition: '连接管理编辑',
  role_name_data_transmission: '数据管道',
  role_name_Data_SYNC: '浏览复制开发任务',
  role_name_SYNC_category_management: '任务分类管理',
  role_name_SYNC_category_application: '任务分类应用',
  role_name_SYNC_job_delete: '删除任务',
  role_name_SYNC_job_edition: '编辑任务',
  role_name_SYNC_job_operation: '任务操作',
  role_name_SYNC_job_import: '任务导入',
  role_name_SYNC_job_export: '任务导出',
  role_name_SYNC_Function_management: '浏览函数管理',
  role_name_Data_verify: '浏览数据校验',
  role_name_verify_job_creation: '创建校验任务',
  role_name_verify_job_edition: '编辑执行校验任务',
  role_name_verify_job_delete: '删除校验任务',
  role_name_verify_job_execution: '校验任务运行',
  role_name_log_collector: '浏览共享挖掘',
  role_name_custom_node: '浏览自定义节点',
  role_name_shared_cache: '浏览共享缓存',
  role_name_data_search: '浏览数据搜索',
  role_name_data_government: '数据治理',
  role_name_data_catalog: '浏览数据目录',
  role_name_data_catalog_category_management: '数据目录分类管理',
  role_name_data_catalog_category_application: '数据目录分类应用',
  role_name_data_catalog_edition: '编辑元数据',
  role_name_new_model_creation: '创建模型',
  role_name_meta_data_deleting: '元数据删除',
  role_name_data_quality: '浏览数据质量',
  role_name_data_quality_edition: '编辑数据质量',
  role_name_data_rules: '浏览数据规则',
  role_name_data_rule_management: '数据规则管理',
  role_name_time_to_live: '浏览数据生命周期',
  role_name_time_to_live_management: '数据生命周期管理',
  role_name_data_lineage: '浏览数据地图',
  role_name_data_publish: '浏览数据发布',
  role_name_API_management: '浏览API发布',
  role_name_API_category_application: 'API分类应用',
  role_name_API_category_management: 'API分类管理',
  role_name_API_creation: 'API创建',
  role_name_API_delete: 'API删除',
  role_name_API_edition: 'API编辑',
  role_name_API_publish: '发布API',
  role_name_API_import: 'API导入',
  role_name_API_export: 'API导出',
  role_name_API_data_explorer: '浏览API数据浏览',
  role_name_API_data_explorer_export: '导出API数据',
  role_name_API_data_explorer_deleting: '删除API数据',
  role_name_API_data_explorer_tagging: 'API数据加标签',
  role_name_API_data_time_zone_editing: '修改时区',
  role_name_API_data_creation: '新增API数据',
  role_name_API_data_download: '下载API数据',
  role_name_API_doc_test: '浏览API文档测试',
  role_name_API_stats: '浏览API统计分析',
  role_name_API_clients: '浏览API客户端',
  role_name_API_clients_amangement: 'API客户端管理',
  role_name_API_server: '浏览API服务器',
  role_name_API_server_management: 'API服务器管理',
  role_name_data_collect: '浏览数据采集旧版',
  role_name_data_collect_all_data: '数据采集旧版',
  role_name_system_management: '浏览系统管理',
  role_name_schedule_jobs: '浏览调度任务',
  role_name_schedule_jobs_management: '调度任务管理',
  role_name_Cluster_management: '浏览集群管理',
  role_name_Cluster_operation: '操作Agent服务',
  role_name_status_log: '状态日志',
  role_name_agents: '浏览进程管理',
  role_name_user_management: '浏览用户管理',
  role_name_user_creation: '创建用户',
  role_name_user_edition: '编辑用户',
  role_name_user_delete: '删除用户',
  role_name_user_category_management: '用户分类管理',
  role_name_user_category_application: '用户分类应用',
  role_name_role_management: '浏览角色管理',
  role_name_role_creation: '创建角色',
  role_name_role_edition: '编辑角色',
  role_name_role_delete: '删除角色',
  role_name_system_settings: '浏览系统设置',
  role_name_system_settings_modification: '修改设置',
  role_name_create_new_table_in_SYNC: '任务中创建表',
  role_name_servers_oversee: '浏览运维',
  role_name_dictionary: '浏览字典模板管理',
  role_name_Topology: '浏览网络拓扑',
  milestone_list_status_waiting: '待执行',
  signin_code: '发送验证码',
  signin_verify_code: '请输入验证码',
  signin_verify_email_invalid: '请输入有效邮箱地址',
  signin_verify_code_success: '验证码发送成功',
  signin_email_require: '邮箱地址必填',
  signin_verify_code_not_empty: '验证码必填',
  signin_verify_code_not_incorrect: '验证码错误',
  signin_verify_password_invalid: '验证码至少5个字符',
  signin_verify_password_notCN: '密碼僅允許英文、數字和英文標點符號',
  signin_not_mailbox: 'oops~此邮箱尚未注册',
  meta_table_default: '默认值',
  meta_table_not_null: '非空',
  // 新建
  new_advanced_mode: '标准模式',
  new_more_features: '更多功能',
  new_data_copy: '数据复制',
  new_data_development: '数据开发',
  new_data_copy_desc:
    '对数据库进行跨库复制，适用于数据迁移，容灾备份，系统多活等场景',
  new_data_development_desc:
    '抽取源端数据并加工计算转换、例如行过滤、字段处理、多表合并等',
  new_create_connection: '创建数据源',
  new_create_api: '创建API',
  new_create_connection_desc:
    '数据源是创建传输任务的前提，任务重所有的数据库和表数据节点都来自数据源',
  new_create_api_desc:
    'API即数据发布，可以根据现有collection或者通过同步任务创建新的collection对外发布的API',
  //数据发现-数据对象
  object_list_name: '对象名称',
  object_list_classification: '对象分类',
  object_list_type: '对象类型',
  object_list_source_type: '来源类型',
  object_list_source_information: '来源信息',
  datadiscovery_catalogue_ziyuanbangding: '资源绑定',
  datadiscovery_catalogue_lianjieduixiangming: '连接对象名',
  datadiscovery_catalogue_ziyuanleixing: '资源类型',
  datadiscovery_objectlist_duixiangminglaiyuan: '对象名称/数据源',
  datadiscovery_objectlist_laiyuanfenlei: '来源分类',
  datadiscovery_previewdrawer_shujuxiang: '数据项',
  datadiscovery_previewdrawer_yewumingcheng: '业务名称',
  datadiscovery_previewdrawer_lianjiemiaoshu: '连接描述',
  datadiscovery_previewdrawer_shujuliang: '数据量',
  datadiscovery_previewdrawer_biangengshijian: '变更时间',
  datadiscovery_previewdrawer_guanliyuan: '管理员',
  datadiscovery_previewdrawer_duixiangxiangqing: '对象详情',
  datadiscovery_previewdrawer_yewumiaoshu: '业务描述',
  datadiscovery_previewdrawer_yewuleixing: '业务类型',
  datadiscovery_previewdrawer_suoyin: '索引',
  datadiscovery_previewdrawer_waijian: '外键',
  datadiscovery_previewdrawer_zhujian: '主键',
  // web-core
  app_document: '帮助文档',
  app_qa: '客服',
  app_version: '系统版本',
  app_home: '官网',
  app_account: '个人设置',
  app_signOut: '退出登录',
  app_signOutMsg: '您确定要退出登录吗？',
  app_customerService_technicalSupport: '邮件支持',
  app_customerService_technicalSupportText:
    '在使用过程中，有任何问题，请发送邮件到',
  app_customerService_technicalSupportText1: '，我们会尽快答复。',
  app_customerService_userSupport: '<EMAIL>',
  app_customerService_otherDmands: '微信支持',
  app_customerService_otherDmandsText:
    '请扫描下方企业微信二维码, 联系我们获取支持',
  app_signIn_slogan: '像自来水一样方便地使用您的数据',
  app_signIn_signIn: '登录',
  app_signIn_keepSignIn: '保持登录状态',
  app_signIn_email_placeholder: '请输入邮箱',
  login_email_and_ad_placeholder: '请输入邮箱/LDAP 用户名',
  app_signIn_inviteCode_placeholder: '邀请码',
  app_signIn_password_placeholder: '请输入密码',
  app_signIn_email_require: '邮箱地址必填',
  app_signIn_inviteCode_require: '邀请码必填',
  app_signIn_inviteCode_invalid: '邀请码无效',
  app_signIn_email_invalid: '邮箱或密码错误，请检查后重新输入',
  app_signIn_password_invalid: '密码至少5个字符',
  app_signIn_permission_denied: '没有权限',
  app_signIn_registry: '账号注册',
  app_signIn_registry_tip: '我已同意',
  app_signIn_userPplicy: '用户政策',
  app_signIn_nextStep: '下一步',
  app_signIn_haveAccpunt: '已有账号?',
  app_signIn_backLogin: '返回登录',
  app_signIn_email_existed: 'Email 地址已被注册',
  app_signIn_userPplicy_message: '请选择用户政策',
  app_signIn_modifyPassword: '修改密码',
  app_signIn_newPasswordTip:
    '输入您注册的邮箱和新密码，我们将向您发送用于重置密码的链接',
  app_signIn_newpassword_placeholder: '请设置新密码',
  app_signIn_rememberPasswords: '想起密码?',
  app_signIn_Registration: '注册账号',
  app_signIn_forgetPassword: '忘记密码?',
  app_signIn_confirmationEmail: '账号注册确认邮件已发送至',
  app_signIn_mailbox: '请登录邮箱后点击链接进行确认~',
  app_signIn_receiveEmail: '没有收到邮件？点击',
  app_signIn_resend: '重新发送',
  app_signIn_orClick: '或点击',
  app_signIn_accountSuccess: '已注册成功~',
  app_signIn_clickBtn: '点击下方按钮开启数据传输之旅吧',
  app_signIn_resetClickBtn: '点击下方按钮登录吧',
  app_signIn_goLogin: '去登录',
  app_signIn_connectionFailed: '注册确认链接失败',
  app_signIn_resetConnectionFailed: '重置密码确认链接已失效',
  app_signIn_confirmEmail: '请重新',
  app_signIn_registered: '注册',
  app_signIn_resetAccountSuccess: '的密码已重置成功~',
  app_signIn_passwordResetText: '重置密码确认邮件已发送至',
  app_signIn_hasMailbox: 'oops~此邮箱已经被注册了',
  app_signIn_disableSignup: 'oops~禁止注册',
  app_signIn_getCode: '邀请码获取',
  app_signIn_qrCodeText:
    '如果想试用产品, 请扫描下方企业微信二维码, 联系我们获取',
  app_Home_initialization: '初始化中',
  app_Home_loadingFinished: '初始化完成',
  app_Home_incremental: '增量中',
  app_Home_incrementalLag: '增量滞后',
  message_cancel: '取 消',
  message_confirm: '确 定',
  message_save: '保 存',
  message_clickRelatedTasks: '点击查看相关任务',
  message_noRelatedTask: '暂无相关任务',
  cluster_start: '启动',
  cluster_close: '关闭',
  cluster_restart: '重启',
  cluster_syncGover: '同步治理',
  cluster_delete: '删除',
  cluster_edit: '编辑',
  cluster_cancel: '取 消',
  cluster_confirm: '确 定',
  cluster_reduction: '还原',
  cluster_confirmText: '确认',
  cluster_time: '时间',
  cluster_saveOK: '保存成功',
  cluster_saveFail: '保存失败',
  cluster_deleteOK: '删除成功',
  cluster_deleteFail: '删除失败',
  cluster_closeSever: '关闭服务',
  cluster_restartServer: '重启服务',
  cluster_startServer: '启动服务',
  cluster_deleteOrNot: '是否删除',
  cluster_selectTime: '选择时间',
  cluster_selectDate: '选择日期',
  cluster_serviceCluMange: '服务集群管理',
  cluster_statusLog: '状态日志',
  cluster_placeholderServer: '请输入服务器名称',
  cluster_manageSys: '管理后台',
  cluster_addServerMon: '添加服务监控',
  cluster_serverName: '服务器名称',
  cluster_placeholderMonServer: '请输入监控的服务名称',
  cluster_iPDisplay: '网卡IP展示',
  cluster_ipTip: '切换网卡仅改变集群管理页服务器下IP的展示，不影响任何功能',
  cluster_delTittle: '删除Agent服务器',
  cluster_delMessage: '确定删除服务器',
  cluster_startupAfter_add: '请启动后添加',
  cluster_startupAfter_delete: '请启动后删除',
  cluster_noneText: '不能为空',
  cluster_hostName: '主机名',
  cluster_ipAddress: 'ip地址',
  cluster_uniqueEncode: '唯一编码',
  cluster_logs: '日志信息',
  cluster_serviceType: '服务类型',
  cluster_level: '级别',
  cluster_cpuUsage: 'CPU使用率',
  cluster_heapMemoryUsage: '堆内存使用率',
  button_rename: '改名',
  button_all: '全部',
  dataFlow_saveFail: '任务保存失败，请检查配置信息并确保数据源状态有效',
  dataFlow_leave: '离开',
  dataFlow_backlistText: '返回列表页',
  dataFlow_saveReminder:
    '此任务尚未保存，离开本页面会导致任务配置丢失，确定要离开吗?',
  dataFlow_aggregateNotDataNode: '连接聚合节点的第一个目标数据节点只能是数据集',
  dataFlow_batchSortOperation: '批量分类操作',
  dataFlow_selectRowdata: '请选择行数据',
  dataFlow_clusterClone: '数据库迁移',
  dataFlow_custom: '数据同步',
  dataFlow_searchNode: '查找节点',
  dataFlow_updateModel: '更新模型',
  dataFlow_loadingText: '加载中',
  dataFlow_databseMigrationHead: '数据库迁移 -  新手引导模式',
  dataFlow_dataMigrationHead: '数据同步',
  dataFlow_atabseProcessingHead: '数据处理同步',
  dataFlow_databseFreedomHead: '数据库迁移',
  dataFlow_createNew: '新建',
  dataFlow_DissedNoAction: 'oops~ 被禁用的节点或连线不能被删除、连入或连出',
  dataFlow_notCopy: '被禁用的节点不能被复制',
  dataFlow_guidingMode: '引导模式',
  dataFlow_advancedMode: '标准模式',
  dataFlow_freedomMode: '转标准模式',
  dataFlow_advanceSetting: '更多高级设置',
  dataFlow_closeSetting: '收起',
  dataFlow_openPanel: '展开',
  dataFlow_execution: '开始执行',
  dataFlow_previous: '上一步',
  dataFlow_next: '下一步',
  dataFlow_sourceSetting: '设置源库',
  dataFlow_targetSetting: '设置目标库',
  dataFlow_advancedetting: '高级设置',
  dataFlow_simpleSceneTitle: '创建数据库复制任务',
  dataFlow_sourceLibrarySetting: '源库结构与对象设置',
  dataFlow_databseMigration:
    '以引导的模式帮助新手用户快速了解数据库之间的迁移。数据库迁移能快速地实现数据库之间(内置表批量过滤和改名等设置)的结构、全量和增量迁移。',
  dataFlow_databseProcessing:
    '以引导的模式帮助新手用户快速了解表级的数据处理与同步，此功能除了能实现表级的全量或增量传输除功能外，更注重使用各种处理器(JS处理、字段过滤、聚合处理、行级过滤等)进行复杂的逻辑处理，以满足用户更高的数据处理需求。',
  dataFlow_databseFreedom:
    '数据迁移功能可帮助用户在一个任务内轻松实现多个同构或异构数据库、文件之间的结构迁移、初始化迁移、或增量迁移等功能。',
  dataFlow_dataFreedom:
    '数据同步聚焦在表级别的数据处理与传输，可实现多表合并、数据拆分、关联映射、字段增减合并、内容过滤、聚合处理、JS处理等功能的实时数据同步。',
  dataFlow_moreFeatures: '更多功能',
  dataFlow_creatSource: '创建数据源',
  dataFlow_creatApi: '创建API',
  dataFlow_dataValidation: '数据校验',
  dataFlow_sourceDescription:
    '数据源是创建传输任务的前提，任务中所有的数据库和表等数据节点都来自数据源。数据源包含数据库, File, GridFS, Rest API, View, Udp, Custom connection等',
  dataFlow_apiDescription:
    'API即数据发布，可以根据现有collection或者通过同步任务创建新的collection对外发布的API',
  dataFlow_datavaliDescription:
    '数据校验可对迁移同步任务的数据源与目标之间的数据进行比对校验，校验功能包含快速count校验，全表字段值校验，关联字段值校验，定时自动校验等。',
  dataFlow_multiError_allSelectionError: '选中的任务状态不允许这种操作',
  dataFlow_multiError_notFound: '此任务不存在',
  dataFlow_multiError_statusError: '任务状态不允许这种操作',
  dataFlow_multiError_otherError: '操作失败, 请重试',
  dataFlow_changeName: '改名',
  dataFlow_Enable: '启用',
  dataFlow_Disable: '禁用',
  dataFlow_draftNotStart: '任务配置未完成，无法启动',
  dataFlow_systemHint: '系统提示',
  dataFlow_systemText: '系统检测出有如下任务上次操作后未保存，请问是否继续编辑',
  dataFlow_stystemOpen: '打开',
  dataFlow_stystemOpenAll: '全部打开',
  dataFlow_stystemDeleteAll: '全部删除',
  dataFlow_stystemLgnoreAll: '全部忽略',
  dataFlow_newTaksName: '新任务',
  dataFlow_selectNode: '请选择节点',
  dataFlow_submitExecute: '提交执行',
  dataFlow_submitOnly: '仅提交',
  dataFlow_implementationModalities: '执行方式',
  dataFlow_submitConfirmation: '提交确认',
  dataFlow_SyncPoint: '增量采集开始时刻',
  dataFlow_cdcLabel: '数据源:',
  dataFlow_syncType: '任务类型',
  dataFlow_belongAgent: '所属Agent',
  dataFlow_SyncInfo_localTZ:
    '当前时区传输时间：系统所在时区下，开始传输任务的时刻',
  dataFlow_SyncInfo_current: '当前时区时间：默认当前时间',
  dataFlow_SyncInfo_connTZ:
    '数据库时区传输时间： 数据库所在时区下，开始传输任务的时刻',
  dataFlow_SyncInfo_localTZType: '用户浏览器时区',
  dataFlow_SyncInfo_currentType: '此刻',
  dataFlow_SyncInfo_connTZType: '数据库时区',
  dataFlow_Current: '当前时间',
  dataFlow_SyncTime: '同步时间',
  dataFlow_batchRest: '批量重置',
  dataFlow_batchDelete: '批量删除',
  dataFlow_bulkExport: '批量导出',
  dataFlow_bulkScheuled: '批量启动',
  dataFlow_bulkStopping: '批量停止',
  dataFlow_taskBulkFx: '函数',
  dataFlow_taskBulkOperation: '批量操作',
  dataFlow_upload: '点击上传',
  dataFlow_chooseFile: '选择文件',
  dataFlow_import: '任务导入',
  dataFlow_uploadOK: '上传成功',
  dataFlow_uploadError: '上传失败',
  dataFlow_uploadInfo: '点击查看详情',
  dataFlow_view: '查看',
  dataFlow_dataFlowExport: '导出',
  dataFlow_addTag: '新增标签',
  dataFlow_editTag: '编辑标签',
  dataFlow_bulkImport: '批量导入',
  dataFlow_overWrite: '覆盖已有数据',
  dataFlow_skipData: '跳过已有数据',
  dataFlow_loadingError: '加载失败,请',
  dataFlow_dataLoading: '数据努力加载中...',
  dataFlow_loadLogTip: '运行日志努力加载中，可能需要5~10秒，请稍等......',
  dataFlow_noLogTip: '没有数据',
  dataFlow_clickLoadTxt: '点击加载',
  dataFlow_average: '平均',
  dataFlow_current: '当前',
  dataFlow_allNode: '全部节点',
  dataFlow_taskName: '任务名称',
  dataFlow_ownedUser: '所属用户',
  dataFlow_ownedLibrary: '所属库',
  dataFlow_creatdor: '创建人',
  dataFlow_creationTime: '启动时间',
  dataFlow_state: '状态',
  dataFlow_executionTime: '本次执行时间',
  dataFlow_finishTime: '本次结束时间',
  dataFlow_inputNumber: '本次输入',
  dataFlow_outputNumber: '本次输出',
  dataFlow_sourceLibrary: '源库',
  dataFlow_targetLibrary: '目标库',
  dataFlow_rowCount: '条数',
  dataFlow_inputOutput: '输入输出统计',
  dataFlow_transf: '传输耗时',
  dataFlow_taskDetail: '任务详情',
  dataFlow_nodeDetail: '节点信息',
  dataFlow_timePoint: '增量所处时间点',
  dataFlow_dataScreening: '事件统计',
  dataFlow_unit: '单位',
  dataFlow_article: '条',
  dataFlow_secondUnit: '秒',
  dataFlow_second: '秒',
  dataFlow_min: '分',
  dataFlow_hour: '时',
  dataFlow_day: '日',
  dataFlow_input: '输入',
  dataFlow_output: '输出',
  dataFlow_totalInput: '总输入',
  dataFlow_totalOutput: '总输出',
  dataFlow_totalInsert: '总插入',
  dataFlow_totalUpdate: '总更新',
  dataFlow_totalDelete: '总删除',
  dataFlow_category: '类别',
  dataFlow_replicate: '数据同步差距',
  dataFlow_throughputpop:
    '输入输出统计: 平均每秒源端数据采集的速度以及目标端写入的速度，数值越大越好',
  dataFlow_transtime_pop:
    '传输耗时: 除源节点外，事件处理完的时间减去事件的发生时间。 节点间统计:事件从进入节点到输出到所消耗的时间 任务流统计:所有节点耗时相加，数值越小越好',
  dataFlow_replicate_pop:
    '数据同步差距: 源库和目标库数据最后更新时间的差距，数值越小越好',
  dataFlow_status_paused: '已暂停',
  dataFlow_status_prepare: '准备中',
  dataFlow_status_cdc: '增量中',
  dataFlow_status_initializing: '初始化中',
  dataFlow_status_initialized: '初始化完成',
  dataFlow_status_Lag: '增量滞后',
  dataFlow_status_all: '全部状态',
  dataFlow_lag: '滞后',
  dataFlow_executionStatus: '执行状态',
  dataFlow_searchPlaceholder: '任务名称/节点名/库名称',
  dataFlow_searchAgent: '实例名称',
  dataFlow_dataRange: '创建日期范围',
  dataFlow_startTime: '开始时间',
  dataFlow_endTime: '结束时间',
  dataFlow_separator: '至',
  dataFlow_dataPlaceholder: '选择时间范围',
  dataFlow_taskStatus: '任务状态',
  dataFlow_maxLagTime: '最大增量滞后时间',
  dataFlow_taskStatusPlaceholder: '请选择任务状态',
  dataFlow_taskSettingPlaceholder: '请选择任务同步类型',
  dataFlow_updateTime: '更新时间',
  dataFlow_runningSpeed: '运行速度',
  dataFlow_taskSwitch: '运行开关',
  dataFlow_operate: '操作',
  dataFlow_dataMap: '数据地图',
  dataFlow_edit: '编辑',
  dataFlow_copy: '复制',
  dataFlow_schedule: '定时调度',
  dataFlow_run: '启动任务',
  dataFlow_stop: '停止任务',
  dataFlow_cut: '剪切',
  dataFlow_paste: '粘贴',
  dataFlow_undo: '撤销',
  dataFlow_redo: '重做',
  dataFlow_selectAll: '全选',
  dataFlow_amplification: '放大',
  dataFlow_zoomOut: '缩小',
  dataFlow_down: '向下',
  dataFlow_up: '向上',
  dataFlow_selectMultipleNode: '选择多节点',
  dataFlow_mouseDrag: '鼠标拖拽',
  dataFlow_runningMonitor: '运行监控',
  dataFlow_select_source_connection: '源端连接',
  dataFlow_select_sync_mode: '同步方式',
  dataFlow_mapping: '关联关系',
  dataFlow_select_target_connection: '目标端连接',
  dataFlow_sync_mode: '同步模式',
  dataFlow_sync_type: '同步类型',
  dataFlow_send_email: '发送邮件',
  dataFlow_stopped: ' 当任务停止',
  dataFlow_error: '当任务出错',
  dataFlow_edited: '当任务被编辑',
  dataFlow_started: '当任务开启',
  dataFlow_shareCdcMode: '共享增量读取的模式',
  dataFlow_streaming: '流式读取',
  dataFlow_polling: '轮询读取',
  dataFlow_drop_target_before_start: '开启任务前是否删除目标表',
  dataFlow_run_custom_sql: '重复自定义SQL',
  dataFlow_stop_on_error: '遇到错误停止',
  dataFlow_need_to_create_Index: '自动创建索引',
  dataFlow_transformModelVersion: '系统推演版本',
  dataFlow_noPrimaryKey: '支持无主键同步',
  dataFlow_is_schedule: '定期调度任务',
  dataFlow_cron_expression: '调度cron表达式',
  dataFlow_data_quality_tag: '添加数据质量标签',
  dataFlow_notification_lag: '通知',
  dataFlow_isOpenAutoDDL: '自动处理DDL',
  dataFlow_ddlTip: '注意：自动DDL处理不支持JS处理器、字段处理器',
  dataFlow_transformerConcurrency: '目标写入线程数',
  dataFlow_processorConcurrency: '处理器线程数',
  dataFlow_cdcEngineFilter: '启用引擎过滤',
  dataFlow_cdcFetchSize: '增量批次读取条数',
  dataFlow_cdcFetchSizeTip: '每次读取的数据条数。',
  dataFlow_cdcFetchSizeTip1: '条数越小，增量实时性高，但处理速度相对较慢。',
  dataFlow_cdcFetchSizeTip2: '条数越多，实时性相对较低，但整体处理速度较快。',
  dataFlow_send_email_when_replication: '几秒后重新发送',
  dataFlow_send_email_at_most_one_replication: '超过多少秒取消发送',
  dataFlow_read_cdc_interval: '增量同步间隔',
  dataFlow_cdc_concurrency: '增量同步并发写入',
  dataFlow_read_batch_size: '每次读取数量',
  dataFlow_cdcDataProcess: '增量数据处理机制',
  dataFlow_cdcShareFilterOnServer: '共享挖掘日志过滤',
  dataFlow_batch: '批量',
  dataFlow_onebyone: '逐条',
  dataFlow_mission: '描述',
  dataFlow_yes: '是',
  dataFlow_no: '否',
  dataFlow_cronExpression: '请输入调度表达式',
  dataFlow_selectGrpupFiled: '请选择分组字段',
  dataFlow_selectTargetField: '请选择目标字段',
  dataFlow_aggName: '子处理名称',
  dataFlow_nodeName: '节点名称',
  dataFlow_nodeType: '节点类型',
  dataFlow_aggFunction: '聚合函数',
  dataFlow_aggExpression: '作用目标',
  dataFlow_filterPredicate: '过滤器',
  dataFlow_groupByExpression: '分组字段',
  dataFlow_keepAggreHistoryData: '保留聚合历史数据',
  dataFlow_aggregation: '聚合处理',
  dataFlow_aggrCleanSecond: '清理旧版本数据时间',
  dataFlow_aggrFullSyncSecond: '全量同步时间',
  dataFlow_aggregatePrompt:
    '提示：使用聚合处理节点后，此任务停止后再次启动，任务将会重置',
  dataFlow_nameTip:
    '后续节点的脚本编辑需要引用此子处理的名称进行指定的数据处理，故不同的子处理名称不可重复。',
  dataFlow_enterFilterTable: '请输入过滤表内容',
  dataFlow_lagTime: '增量滞后判断时间设置',
  dataFlow_lagTimeTip:
    '当增量任务延迟大于该值时，则认为任务增量滞后，默认值为0',
  dataFlow_button_submit: '提交执行',
  dataFlow_button_viewConfig: '查看节点配置',
  dataFlow_button_viewMonitoring: '查看监控数据',
  dataFlow_button_setting: '设置',
  dataFlow_button_logs: '日志',
  dataFlow_button_preview: '预览',
  dataFlow_button_capture: '数据检视',
  dataFlow_button_stop_capture: '停止检视',
  dataFlow_button_start: '启动',
  dataFlow_button_stop: '停止',
  dataFlow_button_force_stop: '强制停止',
  dataFlow_button_reset: '重置',
  dataFlow_button_save: '保存',
  dataFlow_button_saveDraft: '保存草稿',
  dataFlow_button_saveing: '保存中',
  dataFlow_button_reloadSchema: '刷新schema',
  dataFlow_button_debug: 'debug测试',
  dataFlow_button_quantitative: '定量',
  dataFlow_button_increment: '增量',
  dataFlow_save_before_running: '请先保存再运行',
  dataFlow_reset_job_msg: '重置任务?',
  dataFlow_reset_job_tip: '提示',
  dataFlow_stop_job_msg: '停止任务?',
  dataFlow_stop_job_force_stop_msg: '强制停止任务?',
  dataFlow_stop_job_tip: '提示',
  dataFlow_file_preview_fields_file_name: '文件名称',
  dataFlow_file_preview_fields_file_size_ondisk: '文件大小(Byte)',
  dataFlow_file_preview_fields_file_modify_time_ondisk: '更新时间',
  dataFlow_file_preview_fields_file_create_time_ondisk: '创建时间',
  dataFlow_file_preview_fields_file_path: '文件路径',
  dataFlow_delete_confirm_title: '是否删除该任务？',
  dataFlow_delete_confirm_message: '删除任务 xxx 后，此任务将无法恢复',
  dataFlow_bulk_delete_confirm_title: '是否批量删除任务？',
  dataFlow_bulk_delete_confirm_message: '批量删除任务后，任务将无法恢复',
  dataFlow_stop_confirm_title: '是否暂停该任务？',
  dataFlow_stop_confirm_message:
    '暂停任务 xxx 后，任务中未完成全量同步的表再次启动时，会重新执行全量同步',
  dataFlow_bulk_stop_confirm_title: '是否批量暂停任务？',
  dataFlow_bulk_stop_confirm_message:
    '批量暂停任务后，任务中未完成全量同步的表再次启动时，会重新执行全量同步',
  dataFlow_force_stop_confirm_title: '是否强制停止该任务？',
  dataFlow_force_stop_confirm_message:
    '强制停止任务 xxx 将立即中断数据传输强制任务快速停止，并重置该任务',
  dataFlow_bulk_force_stop_confirm_title: '是否批量强制停止任务？',
  dataFlow_bulk_force_stop_confirm_message:
    '批量强制停止任务将立即中断数据传输强制任务快速停止，并重置该任务',
  dataFlow_initialize_confirm_title: '是否重置该任务？',
  dataFlow_initialize_confirm_message:
    '重置任务 xxx 将清除任务同步进度，任务将重新执行',
  dataFlow_bulk_initialize_confirm_title: '是否批量重置任务？',
  dataFlow_bulk_initialize_confirm_message:
    '批量重置任务将清除任务同步进度，任务将重新执行',
  dataFlow_importantReminder: '重要提醒',
  dataFlow_modifyEditText: '编辑任务如果修改了',
  dataFlow_nodeLayoutProcess: '节点排版流程',
  dataFlow_nodeAttributes: '节点属性',
  dataFlow_matchingRelationship: '匹配关系',
  dataFlow_afterSubmission: '提交后必须',
  dataFlow_runNomally: '才能正常运行',
  dataFlow_editLayerTip: ' 否则可能导致异常错误，请问您要继续编辑吗?',
  dataFlow_continueEditing: '继续编辑',
  dataFlow_numberType: '必须为数字且不能小于0',
  dataFlow_setting_distinctWriteType: '去重写入机制',
  dataFlow_setting_intellect: '智能去重写入',
  dataFlow_setting_compel: '强制去重写入',
  dataFlow_setting_intellectTip:
    '智能去重写入：对目标已有数据进行智能检测，去重的同时能极大提升传输性能',
  dataFlow_setting_compelTip:
    '强制去重写入：对目标已有数据进行强制去重检测，严格保证精准度但传输性能较低',
  dataFlow_setting_batchTip:
    '批量：对监测到的增量数据进行批量传输处理，性能较高',
  dataFlow_setting_onebyoneTip:
    '逐行：对监测到的增量数据进行逐条处理，性能较差',
  dataFlow_setting_sync_type_tip:
    '关闭数据集节点的聚合设置才能修改传输类型，已开启节点:',
  dataFlow_skipError_title: '跳过错误设置',
  dataFlow_skipError_skipErrorSettings: '任务错误处理',
  dataFlow_skipError_tip:
    '任务上次停止时发生了以下数据相关的错误，请确认这些错误已经被处理。如果希望跳过这些错误，请勾选相应的错误项并点击“跳过错误，启动任务” 。',
  dataFlow_skipError_attention:
    '注意：若导致错误的数据未被处理，跳过错误可能导致这条数据被丢弃。',
  dataFlow_skipError_startJob: '跳过错误，启动任务',
  dataFlow_skipError_cancel: '取消',
  dataFlow_skipError_taskName: '任务名',
  dataFlow_skipError_errorTotal: '共 XX 条,已选择',
  dataFlow_skipError_strip: '条',
  dataFlow_flowEngineVersion: '引擎版本',
  dataFlow_flowEngineV1: 'Flow Engine V1',
  dataFlow_jetFlowEngineV2: 'Jet Flow Engine V2',
  editor_cell_data_node_collection_form_collection_placeholder: '请选择数据集',
  editor_cell_data_node_collection_form_fieldFilterType_retainedField:
    '保留字段',
  editor_cell_data_node_collection_form_fieldFilterType_deleteField: '删除字段',
  editor_cell_data_node_collection_form_fieldFilter_placeholderKeep:
    ' 请选择要保留的字段',
  editor_cell_data_node_collection_form_fieldFilter_placeholderDelete:
    '  请选择要删除的字段',
  editor_cell_data_node_collection_form_filter_fieldFilter: '智能模式',
  editor_cell_data_node_collection_form_filter_sqlFilter: 'SQL模式',
  editor_cell_data_node_collection_form_filter_mqlFilter: 'MQL模式',
  editor_cell_data_node_collection_form_filter_allField: '全部字段',
  editor_cell_data_node_collection_form_filter_rowLimit: '行数限制',
  editor_cell_data_node_collection_form_filter_allRows: '全部行数',
  editor_cell_data_node_collection_form_filter_oneThousandRows: '1000行',
  editor_cell_data_node_collection_form_filter_tenThousandRows: '10000行',
  editor_cell_data_node_table_form_custom_sql_placeholder: '请输入自定义SQL',
  editor_cell_data_node_table_form_custom_sql_mplaceholder: '请输入自定义MQL',
  editor_cell_data_node_table_form_initial_offset_label: '自定义SQL增量条件',
  editor_cell_data_node_table_form_initial_offset_placeholder:
    '请输入自定义SQL增量条件',
  editor_ui_sidebar_setting: '任务设置',
  metadata_createModel: '创建模型',
  metadata_header_name: '表名/所属库',
  metadata_header_last_user_name: '更新用户',
  metadata_metaType_database: '数据库',
  metadata_metaType_api: '数据种类',
  metadata_metaType_job: '任务',
  metadata_metaType_collection: '数据集',
  metadata_metaType_view: '视图',
  metadata_metaType_directory: '目录',
  metadata_metaType_table: '数据表',
  metadata_metaType_dataflow: '任务编排',
  metadata_metaType_mongo_view: 'Mongodb视图',
  metadata_metaType_ftp: 'FTP',
  metadata_metaType_apiendpoint: 'API连接',
  metadata_details_model: '模型',
  metadata_details_collection: '数据集',
  metadata_details_collectionName: '数据集名称',
  metadata_details_createCollection: '创建数据集',
  metadata_details_dataDirectory: '数据目录',
  metadata_details_dataDetails: '数据详情',
  metadata_details_basicAttributes: '基础属性',
  metadata_details_businessAttributes: '业务属性',
  metadata_details_clickAddDes: '点击添加描述',
  metadata_details_propertyDetails: '属性详情',
  metadata_details_comment: '描述',
  metadata_details_originalTableName: '原表名',
  metadata_details_data_type: '数据类型',
  metadata_details_precision: '精确度',
  metadata_details_columnSize: '字段长度',
  metadata_details_scale: '数字长度',
  metadata_details_autoincrement: '自增',
  metadata_details_primary_key_position: '主键',
  metadata_details_foreign_key_position: '外键',
  metadata_details_is_nullable: '非空',
  metadata_details_unique: '唯一',
  metadata_details_owningConnection: '所属连接',
  metadata_details_primaryKey: '主键',
  metadata_details_source: '来源',
  metadata_details_founder: '创建人',
  metadata_details_Modifier: '修改人',
  metadata_details_renamed: '改名',
  metadata_details_searchPlaceholder: '字段名/别名/描述',
  metadata_details_selsectSource: '选择来源',
  metadata_details_createFiled: '新建字段',
  metadata_details_editFild: '编辑字段',
  metadata_details_prohibitOverwriting: ' 批量禁止覆盖',
  metadata_details_batchCoverage: '批量覆盖',
  metadata_details_refreshModel: '刷新模型',
  metadata_details_filedName: '字段名',
  metadata_details_alias: '别名',
  metadata_details_fieldType: '字段类型',
  metadata_details_allowOverwrite: '允许覆盖',
  metadata_details_selfIncreasing: '自增',
  metadata_details_fieldLength: '字段长度',
  metadata_details_accuracy: '精准度',
  metadata_details_numberLength: '数字长度',
  metadata_details_dictionarySettings: '字典设置',
  metadata_details_initialValue: '初始值',
  metadata_details_mappedValue: '映射值',
  metadata_details_enterInitialValue: '输入初始值',
  metadata_details_enterMappedValue: '输入映射值',
  metadata_details_newMapping: '新增映射',
  metadata_details_chooseTemplate: '选择模板',
  metadata_details_foreignKeySetting: '外键设置',
  metadata_details_associationTable: '关联表',
  metadata_details_associationField: '关联字段',
  metadata_details_connectionRelation: '关联关系',
  metadata_details_oneone: '一对一',
  metadata_details_onemany: '一对多',
  metadata_details_manyone: '多对一',
  metadata_details_addRelatedTable: '新增关联表',
  metadata_details_filedAliasName: '字段名/别名',
  metadata_details_Float: '浮点数',
  metadata_details_String: '字符串',
  metadata_details_baseObject: '对象',
  metadata_details_Array: '数组',
  metadata_details_Map: '字典对象',
  metadata_details_Short: '短整型',
  metadata_details_Long: '长整型',
  metadata_details_Double: '双精度',
  metadata_details_Byte: '字节',
  metadata_details_Bytes: '字节数',
  metadata_details_BigDecimal: '十进制',
  metadata_details_Boolean: '布尔值',
  metadata_details_Date: '日期',
  metadata_details_Integer: '整数',
  metadata_details_dictionary_typeNo: '此字段类型不能添加字典模板',
  metadata_details_fieldNameNo: '字段名为空',
  metadata_details_moreAttributes: '更多属性',
  metadata_details_msgFiledName: '请输入字段名称',
  metadata_details_success_Release: '保存成功,请手动重新发布',
  metadata_details_filedName_repeat: '字段名不能重名',
  metadata_details_filedDictionary: '字段字典',
  metadata_details_foreignKeyAssociation: '外键关联',
  metadata_details_tableLayering: '表分层',
  metadata_details_theme: '主题',
  metadata_details_taskReference: '任务引用',
  metadata_details_APIReference: 'API引用',
  metadata_details_creat: '新建',
  metadata_details_businessAttrTitle: '业务属性',
  metadata_details_attrName: '属性名',
  metadata_details_attrKey: '属性值',
  metadata_details_editAliasNameTitle: '编辑别名',
  metadata_details_editCommentTitle: '编辑描述',
  metadata_details_uniquelyIdentifies: '唯一标识',
  metadata_details_query: '查询',
  metadata_details_version_version_control: '版本管理',
  metadata_details_version_version_control_required: '版本管理不能为空',
  metadata_details_version_lastVersion:
    '此元数据已是最新版本，过往保存的历史版本记录将保存在下面列表中',
  metadata_details_version_versionNum: '版本号',
  metadata_details_version_versionComparison: '版本比对',
  metadata_details_version_compared: '对比',
  metadata_details_version_currentVersion: '当前版本',
  metadata_details_version_operator: '操作人',
  metadata_details_version_modifyDescription: '修改说明',
  metadata_details_Modify_property: '修改属性',
  metadata_details_Modify_field: '修改字段',
  metadata_details_Add_property: '新增属性',
  metadata_details_Add_new_field: '新增字段',
  metadata_details_Remove_property: '移除属性',
  metadata_details_Remove_field: '移除字段',
  metadata_details_index_title: '索引',
  metadata_details_index_name: '索引名称',
  metadata_details_index_create: '创建索引',
  metadata_details_index_fields: '时间字段',
  metadata_details_index_unique: '唯一约束',
  metadata_details_index_status: '状态',
  metadata_details_index_create_by: '创建用户',
  metadata_details_index_background: '后台',
  metadata_details_index_properties: '属性',
  metadata_details_index_definition: '字段名称',
  metadata_details_index_options: '选项',
  metadata_details_index_build_in_background: '在后台构建索引',
  metadata_details_index_create_unique: '创建唯一索引',
  metadata_details_index_create_ttl: '创建TTL索引',
  metadata_details_index_name_exists: '索引名称必须唯一',
  metadata_details_index_index_exists: '索引已经存在',
  metadata_details_index_create_by_user: ' 平台用户',
  metadata_details_index_create_by_dba: ' 数据库管理员',
  metadata_details_index_status_creating: ' 正在创建',
  metadata_details_index_status_created: ' 创建完成',
  metadata_details_index_status_creation_failed: ' 创建失败',
  metadata_details_index_status_deleted: ' 已经删除',
  metadata_details_index_drop_index: ' 正在删除索引',
  metadata_details_index_unique_true: ' 唯一',
  metadata_details_index_unique_false: ' 不唯一',
  metadata_details_validation_title: '数据验证',
  metadata_details_validation_field_name: '字段名称',
  metadata_details_validation_rule: '规则',
  metadata_details_validation_ruleTem: '规则模板',
  metadata_details_validation_select_rule: '选择规则',
  metadata_details_validation_ungrouped: '未分组',
  metadata_details_validation_create: '创建数据校验',
  metadata_details_preview_title: '数据预览',
  metadata_details_pipeline_title: '管道',
  metadata_details_pipeline_collection: '数据表',
  metadata_details_pipeline_pipeline: 'MongoDB Pipeline',
  metadata_details_pipeline_viewStatus: '视图状态',
  metadata_details_pipeline_FailedMessage: '失败详情',
  metadata_details_pipeline_penpinSave:
    '点击下方保存按钮仅保存到系统，点击更新按钮将应用到此数据所在的数据库',
  metadata_details_pipeline_apply: '应用',
  metadata_details_pipeline_view_tip: '操作将覆盖同名的视图，是否创建视图',
  metadata_details_pipeline_success: '应用成功',
  metadata_details_pipeline_failed: '应用失败',
  metadata_metadataSearch_title: '元数据检索',
  metadata_metadataSearch_desc:
    '元数据检索提供对表、字段的名称、别名、描述等内容的搜索功能，请先选择搜索表/字段，再输入内容，点击搜索按钮进行搜索',
  metadata_metadataSearch_table: '搜索表',
  metadata_metadataSearch_column: '搜索字段',
  metadata_metadataSearch_search: '搜索',
  metadata_metadataSearch_noSearch: '请按“回车”键发起检索',
  metadata_metadataSearch_noResult: '暂无搜索结果，请确认搜索关键字',
  metadata_metadataSearch_noMore: '无更多检索结果',
  metadata_metadataSearch_more: '点击加载更多',
  metadata_metadataSearch_placeholder: '请输入搜索关键字',
  dialog_placeholderTable:
    '仅支持英文、数字、下划线、点、减号，并以英文字母开头，不允许 system 开头',
  dialog_downAgent_ok: '好的',
  notification_stoppedByError: '出错停止',
  notification_CDCLag: 'CDC滞后超时',
  notification_jobPaused: '任务被停止',
  notification_jobDeleted: '任务被删除',
  notification_jobStateError: '任务状态error',
  notification_jobEncounterError: '任务遇到错误',
  notification_noticeInterval: '发送间隔',
  notification_CDCLagTime: 'CDC滞后通知',
  notification_lagTime: '滞后时间',
  notification_DDL: '数据库DDL变化',
  notification_agentNotice: 'Agent通知',
  notification_serverDisconnected: '服务器断开连接',
  notification_agentStarted: 'Agent服务被启动',
  notification_agentStopped: 'Agent服务被停止',
  notification_agentCreated: 'Agent被创建',
  notification_agentDeleted: 'Agent被删除',
  notification_inspectCount: '校验任务count差异',
  notification_inspectValue: '校验任务内容差异',
  notification_inspectDelete: '校验任务被删除',
  notification_inspectError: '校验任务运行error',
  notification_placeholder_user: '请选择操作人',
  notification_placeholder_keyword: '按数据源/任务名搜索',
  notification_account: '用户 ',
  notification_operation_create: ' 创建了 ',
  notification_operation_update: ' 更新了 ',
  notification_operation_delete: ' 删除了 ',
  notification_operation_start: ' 启动了 ',
  notification_operation_stop: ' 停止了 ',
  notification_operation_forceStop: ' 强制停止了 ',
  notification_operation_reset: ' 重置了 ',
  notification_operation_copy: ' 复制了 ',
  notification_operation_upload: ' 导入了 ',
  notification_operation_download: ' 下载了 ',
  notification_operation_login: ' 登录 ',
  notification_operation_logout: ' 登出 ',
  notification_operation_readAll: '已读 ',
  notification_operation_read: '',
  notification_modular_sync: '同步任务',
  notification_modular_migration: '迁移任务',
  notification_modular_connection: '数据源 ',
  notification_modular_dataflow: '数据传输任务 ',
  notification_modular_inspect: '校验任务',
  notification_modular_ddlDeal: 'DDL处理 ',
  notification_modular_system: '系统',
  notification_modular_user: '用户',
  notification_modular_role: '角色',
  notification_modular_accessCode: '访问码',
  notification_modular_message: '',
  queryBuilder_addCond: '字段条件',
  account_accountSettings: '个人设置 ',
  account_systemSetting: '系统设置',
  account_email: '邮箱',
  account_userName: '用户名',
  account_accessCode: '访问码',
  account_changePassword: '修改密码',
  account_currentPassword: '请输入当前密码',
  account_newPassword: '请输入新密码',
  account_confirmPassword: '再次确认密码',
  account_changeEmail: '修改邮箱',
  account_enterMailbox: '请输入邮箱',
  account_enterNewMailbox: '请输入新邮箱',
  account_changeUsername: '修改用户名',
  account_newUsername: '请输入新的用户名',
  account_sendEmail: '发送验证邮件',
  account_samePawTip: '新密码不能与原密码相同!',
  account_newPawInconsistent: '与新密码不一致!',
  account_pawSaveSuccess: '密码保存成功',
  account_currerPawErrorTip: '当前密码错误，请输入正确的密码',
  account_nameModifySuccess: '名称修改成功',
  account_passwordNotCN: '密码仅允许英文、数字和英文标点符号',
  account_user_null: '用户名不能为空',
  account_has_username: '用户名已存在',
  account_editFail: '用户名修改失败',
  role_allData: '全部角色数据',
  role_functionDataPermission: '功能与数据权限',
  role_module: '模块',
  role_choosePermissionTip:
    '请选择此角色可用的功能和数据权限 （勾选全部角色数据表示可对全部角色的数据进行浏览或操作，不勾选则表示只能对自己的数据进行浏览或操作）',
  role_funcPermission: '功能权限设置',
  role_currentRole: '当前角色',
  role_pageVisible: '页面权限设置',
  role_pageShowTip: '勾选相应模块表示此导航对当前角色可见，不勾选则不显示',
  role_choosePage: '选择页面权限',
  role_bulkOperate: '全选',
  role_chooseAllFunction: '选择全部功能',
  role_chooseAllRole: '全部角色数据',
  role_settingTitle: '设置角色权限',
  user_creatUser: '创建用户',
  timeToLive_w: '周',
  timeToLive_mo: '月',
  timeToLive_y: '年',
  dictionary_isMappedvalue: '映射值不能为空',
  dataRule_classification: '分类',
  dataRule_rule: '规则',
  dataRule_data_type: '字段类型',
  dataRule_data_Nullable: '可为空',
  dataRule_data_Range: '范围',
  dataRule_data_Enum: '枚举',
  dataRule_data_Regex: '正则表达式',
  dataRule_greater_that: '大于',
  dataRule_less_that: '小于',
  dataRule_enum_required: '枚举值不能为空',
  dataRule_gt_lt_none: '范围边界不能同时为',
  dataRule_data_type_required: '数据类型不能为空',
  dataRule_data_regex_required: '正则表达式不能为空',
  dataRule_correct_rules: '请输入正确规则',
  dataRule_pleaseNum: '请输入数字',
  dataRule_dataType_baseFloating: '浮点数',
  dataRule_dataType_baseObject: '对象',
  dataRule_dataType_baseBinarydata: '二进制数据',
  dataRule_dataType_baseString: '字符串',
  dataRule_dataType_baseArray: '数组',
  dataRule_dataType_baseUndefined: '未定义',
  dataRule_dataType_baseBoolean: '布尔值',
  dataRule_dataType_basedate: '日期',
  dataRule_dataType_baseNull: '空',
  dataRule_dataType_baseRegularexpression: '正则表达式',
  dataRule_dataType_baseShorttype: '短整型',
  dataRule_dataType_baseTimestamp: '时间戳',
  dataRule_dataType_baseLonginteger: '长整型',
  modules_apiServerStatus: 'API 服务状态',
  modules_describtion: '描述',
  modules_set_mode: '设置方式',
  daas_notification_alarmnotification_gaojingtongzhi: '告警通知',
  daas_notification_center_xitonggaojing: '系统告警',
  daas_notification_systemalarm_guanbichenggong: '关闭成功',
  daas_notification_systemalarm_gaojingshijian: '告警时间',
  daas_notification_systemalarm_gaojingzhuangtai: '告警状态',
  daas_notification_systemalarm_gaojingduixiang: '告警对象',
  daas_notification_systemalarm_quanbugaojing: '全部告警',
  daas_setting_alarmnotification_gaojingzhibiao: '告警指标',
  daas_setting_alarmnotification_dangjiediandeping: '当节点的平均处理耗时',
  daas_setting_alarmnotification_dangshujuyuanjie: '当数据源节点的平均处理耗时',
  daas_setting_alarmnotification_dangshujuyuanxie: '当数据源协议连接耗时',
  daas_setting_alarmnotification_dangshujuyuanwang: '当数据源网络连接耗时',
  daas_setting_alarmnotification_dangshujuwufa: '当数据无法网络连接耗时',
  daas_setting_alarmnotification_dangrenwudezeng: '当任务的增量延迟',
  daas_setting_alarmnotification_dangrenwutingzhi: '当任务停止时',
  daas_setting_alarmnotification_dangrenwuzengliang: '当任务增量开始时',
  daas_setting_alarmnotification_dangrenwuquanliang: '当任务全量完成时',
  daas_setting_alarmnotification_dangrenwujiaoyan: '当任务校验出错时',
  daas_setting_alarmnotification_dangrenwuyudao: '当任务遇到错误时',
  daas_setting_alarmnotification_dangrenwustop: 'Agent服务停止时',
  daas_setting_alarmnotification_msshigaojing: 'ms时告警',
  daas_setting_alarmnotification_gedian: '个点',
  daas_setting_alarmnotification_lianxu: '连续',
  daas_setting_alarmnotification_cichugaojinggui:
    '此处告警规则设置为系统全局告警规则设置，任务运行监控页面的告警规则设置优先级高于系统全局设置',
  daas_setting_alarmnotification_renwumorengao: '任务默认告警规则设置',
  daas_setting_alarmnotification_morengaojinggui: '默认告警规则',
  daas_setting_alarmnotification_renwugaojingshe: '任务告警设置',
  daas_setting_setting_chulijiediande: '处理节点的平均处理耗时',
  daas_setting_setting_shujuyuanjiedian: '数据源节点的平均处理耗时',
  daas_setting_setting_shujuyuanxieyi: '数据源协议连接耗时',
  daas_setting_setting_shujuyuanwanglu: '数据源网路连接耗时',
  daas_setting_setting_renwudezengliang: '任务的增量延迟',
  daas_setting_settingcenter_gaojingshezhi: '告警设置',
  // 覆写公共模块
  packages_nodeDesign_custom_node_name_required: '请输入节点名称',
  packages_nodeDesign_message_save_ok: '保存成功',
  dataFlow_databseProcessingHead: '数据处理同步',
  dataFlow_databsenProcessing:
    '以引导的模式帮助新手用户快速了解表级的数据处理与同步，此功能除了能实现表级的全量或增量传输除功能外，更注重使用各种处理器(JS处理、字段过滤、聚合处理、行级过滤等)进行复杂的逻辑处理，以满足用户更高的数据处理需求',
  dataFlow_button_milestone: '任务里程碑',
  role_name_API_clients_amanement: 'API客户端管理',
  dataFlow_sharecdcmode: '共享增量阅读模式',
  dataFlow_delete_confirm_Title: '删除任务？',
  dataFlow_delete_confirm_Message: '删除任务XXX后，该任务无法恢复',
  dataFlow_bulk_delete_confirm_Title: '删除中的任务批量？',
  dataFlow_bulk_delete_confirm_Message: '批量删除任务后，任务无法恢复',
  connection_form_agent_msg: 'Agent当前状态异常，无法创建连接，请检查',
  migrate_select_connection_tip:
    '如果您还没有添加数据源，请点击添加数据源按钮添加。为了方便您测试，我们建议您使用数据源数量至少要2',
  app_signIn_signInFail: '邮箱和密码无效',
  dialog_jobSchedule_runDay: '每天2点运行',
  guide_task_type_custom_tips:
    '数据同步侧重于表级数据处理和传输，以满足用户需要实现多表（数据集）、多级数据之间的多表集成、数据拆分、关联映射、字段增减合并、内容过滤、实时数据同步聚合处理案例JS处理等功能，在不影响用户业务的情况下，满足用户对远程或本地数据容灾、跨实例数据同步、查询报表分发、实时性等多种业务场景的需求数据仓库管理员元素。 ',
  guide_btn_to_dashboard: '暂不编辑任务，先去购物',
  app_signIn_registry_sucess_wait_approval:
    '登录前等待管理员批准，5秒后跳转到登录页面',
  daas_deletefile_emptyitem_zanwushuju: '暂无数据',
  daas_components_querycond_xuanzeriqishi: '选择日期时间',
  daas_src_main_qingqiuquanjupei: '请求全局配置(settings)失败: ',
  daas_src_main_baocuntok: '保存token到cookie：',
  daas_api_page_apidocandtest_shouquanjiekou: '授权接口',
  daas_api_page_apidocandtest_daochudaopo: '导出到postman',
  daas_data_discovery_previewdrawer_qingshurumingcheng: '请输入名称',
  daas_data_server_drawer_qingshurucanshu: '请输入参数名称',
  daas_data_server_drawer_paixu: '排序',
  daas_data_server_drawer_meigefenyefan: '每个分页返回的记录数',
  daas_data_server_drawer_fenyebianhao: '分页编号',
  daas_data_server_drawer_zidingyichaxun: '自定义查询',
  daas_data_server_drawer_morenchaxun: '默认查询',
  daas_data_server_drawer_qingxuanzeduixiang: '请选择对象名称',
  daas_data_server_drawer_qingxuanzelianjie: '请选择连接类型',
  daas_data_server_drawer_qingshurufuwu: '请输入服务名称',
  daas_data_server_drawer_selectPermissions: '请选择权限范围',
  daas_data_server_drawer_shilidaima: '示例代码',
  daas_data_server_drawer_shilidaima2: '示例代码',
  daas_data_server_drawer_fanhuijieguo: '返回结果',
  daas_data_server_drawer_diaoyongfangshi: '调用方式',
  daas_data_server_drawer_fuwufangwen: '服务访问',
  daas_data_server_drawer_shuchujieguo: '输出结果',
  daas_data_server_drawer_pailietiaojian: '排列条件',
  daas_data_server_drawer_shaixuantiaojian: '筛选条件',
  daas_data_server_drawer_canshuzhi: '参数值',
  daas_data_server_drawer_canshumingcheng: '参数名称',
  daas_data_server_drawer_shurucanshu: '输入参数',
  daas_data_server_drawer_quanxianfanwei: '权限范围',
  daas_data_server_drawer_jiekouleixing: '接口类型',
  daas_data_server_drawer_fabujiedian: '发布节点',
  daas_data_server_drawer_caozuoleixing: '操作类型',
  daas_data_server_drawer_zanwumiaoshu: '暂无描述',
  daas_data_server_drawer_tiaoshi: '调试',
  daas_data_server_drawer_peizhi: '配置',
  daas_data_server_drawer_chuangjianfuwu: '创建服务',
  daas_data_server_drawer_fuwuxiangqing: '服务详情',
  daas_data_server_list_quedingchexiaogai: '确定撤销该服务？',
  daas_data_server_list_quedingfabugai: '确定发布该服务？',
  daas_data_server_list_querenshanchufu: '确认删除服务？',
  daas_data_server_list_huoqufuwuyu: '获取服务域名失败！',
  daas_data_server_list_fuwuzhuangtai: '服务状态',
  daas_data_server_list_guanlianduixiang: '关联对象',
  daas_data_server_list_fuwumingcheng: '服务名称',
  daas_function_importform_shangchuanwenjianda: '上传文件大小不能超过 {val1}M',
  daas_login_login_dengluchenggong: '登录成功：',
  daas_login_passwordreset_shangweiyanzhengdian: '尚未验证电子邮件',
  daas_login_passwordreset_zhaobudaodianzi: '找不到电子邮件',
  daas_metadata_search_yuanbiaoming: '( 原表名:',
  daas_shared_mining_detail_wajuexiangqingx: '挖掘详情x轴：',
  daas_data_discovery_previewdrawer_jiedian: '节点',
  daas_data_discovery_previewdrawer_renwumiaoshu: '任务描述',
  daas_data_discovery_previewdrawer_yinqingmiaoshu: '引擎描述',
  daas_data_discovery_previewdrawer_yinqingmingcheng: '引擎名称',
  daas_data_discovery_previewdrawer_jiedianshu: '节点数',
  daas_data_discovery_previewdrawer_shuchucanshu: '输出参数',
  daas_data_discovery_previewdrawer_fuwumiaoshu: '服务描述',
  daas_data_discovery_previewdrawer_jiedianmiaoshu: '节点描述',
  daas_data_discovery_previewdrawer_shurujiedian: '输入节点',
  daas_data_discovery_previewdrawer_shuchujiedian: '输出节点',
  daas_router_routes_guanlianrenwuxiang: '关联任务详情',
  daas_data_server_drawer_geshicuowu: '格式错误',
  daas_notification_center_yonghucaozuo: '用户操作',
  daas_data_server_drawer_validate:
    '只能包含中文、字母、数字、下划线和美元符号,并且数字不能开头',
  daas_data_server_drawer_aPI_path_Settings: '访问路径设置',
  daas_data_server_drawer_default_path: '默认访问路径',
  daas_data_server_drawer_custom_path: '自定义访问路径',
  daas_data_server_drawer_prefix: '前缀',
  daas_data_server_drawer_base_path: '基础路径',
  daas_data_server_drawer_path: '访问路径',
  daas_data_server_drawer_confirm_tip:
    '重新生成会导致原API访问路径发生改变，是否确认重新生成？',
  // 覆盖连接编辑、创建处的国际化文案
  packages_business_connection_form_source_and_target_tip: `此数据连接在 ${
    import.meta.env.VUE_APP_PAGE_TITLE
  } 中能同时作为源和目标使用`,
  packages_business_connection_form_source_tip: `此数据连接在 ${
    import.meta.env.VUE_APP_PAGE_TITLE
  } 中只能作为源使用，不能作用为目标`,
  packages_business_connection_form_target_tip: `此数据连接在 ${
    import.meta.env.VUE_APP_PAGE_TITLE
  } 中只能作为目标使用，不能作用为源`,
  daas_cluster_cluster_lianjieshuliang: '连接数量',
  daas_cluster_cluster_mubiaoIPhe: '目标IP和端口',
  daas_cluster_cluster_lianjiezongshu: '连接总数',
  daas_cluster_cluster_yinqingduiwaijian: '引擎对外建立的连接数',
  daas_role_role_ninhaiweibaocun: '您还未保存设置的权限，是否要保存权限设置？',
  daas_role_role_quanbugongneng: ' 全部功能 ',
  daas_role_role_chakanquanbushu: '查看全部数据',
  daas_role_role_gongnengquanxian: '功能权限',
  daas_role_role_yemianquanxian: '页面权限',
  daas_role_role_gongnengmokuai: '功能模块',
  daas_role_role_gouxuanxiangyingmo:
    '勾选相应模块表示此导航对当前角色下用户可见，开启【查看全部数据】则表示角色可以查看和操作该模块下所有的数据，不勾选则只能查看和操作自己创建和被授权的数据。',

  daas_feature_unavailable: '升级版本，解锁更多功能！',
  daas_feature_unavailable_subtitle:
    '这个功能只在企业版和/或云版本中提供。请注册我们的云版本或联系我们获取企业版。',
  daas_feature_unavailable_upgrade_dec: '升级版本，您将获得：',
  daas_feature_unavailable_upgrade_dec_li1: '数据校验（仅企业版）',
  daas_feature_unavailable_upgrade_dec_li2: '共享挖掘',
  daas_feature_unavailable_upgrade_dec_li3: '告警设置',
  daas_feature_unavailable_upgrade_dec_li4: '权限管理（仅企业版）',
  daas_feature_unavailable_upgrade_dec_li5: '更多数据源',
  daas_feature_unavailable_upgrade_dec_li1_desc: `基于自研技术，${import.meta.env.VUE_APP_PAGE_TITLE} 能最大程度保障数据一致性，还支持数据表数据校验，以验证和确保数据流转正确，满足生产环境要求。`,
  daas_feature_unavailable_upgrade_dec_li2_desc: `为减轻源端数据库压力，${import.meta.env.VUE_APP_PAGE_TITLE} 支持共享挖掘增量日志缓存，开启此功能的任务可直接从缓存中获取增量事件，无需重复读取源库增量日志。`,
  daas_feature_unavailable_upgrade_dec_li3_desc: `${import.meta.env.VUE_APP_PAGE_TITLE}支持通过 SMTP 协议发告警邮件，让用户在常用邮箱及时接收异常通知，助其感知异常，保障任务运行稳定可靠。`,
  daas_feature_unavailable_upgrade_dec_li4_desc:
    '角色是权限合集，可为其授予多权限并授予用户，用户继承所有权限，依此设计可先创角色再赋予用户，无需为每个用户配置权限，以简化运维管理和提升安全性。',
  daas_feature_unavailable_upgrade_dec_li5_desc: '',
  daas_feature_unavailable_get_enterprise: '申请企业版',
  daas_feature_unavailable_get_cloud: '免费使用云版',
  daas_feature_unavailable_go_to_compare: '版本功能对比',
  daas_unbind_license: '解绑 License',
  daas_cluster_cluster_lianjieshuliang_detail: '对外建立链接数详情',
  daas_cluster_cluster_view: '集群视图',
  daas_cluster_component_view: '组件视图',
  daas_cluster_engine_hostname: '主机名/IP',
  daas_cluster_connection_count: '连接数',
  daas_cluser_keyword_placeholder: '搜索主机名',

  webhook_alerts: 'Webhook 告警',
  webhook_alerts_detail: 'Webhook 告警详情',
  webhook_alerts_add: '新建 Webhook',
  webhook_address: 'Webhook 地址',
  webhook_params: '参数',
  webhook_switch: '是否启用',
  webhook_send_log: '发送记录',
  webhook_send_log_desc: '（只保留最近 200 条记录）',
  webhook_send_address: '发送地址',
  webhook_server_url: '服务 URL',
  webhook_server_url_empty: '请输入服务 URL',
  webhook_server_url_error: '请输入正确的服务 URL',
  webhook_custom_template: '自定义模版',
  webhook_custom_template_tip: `{'{'}
    "action": "TaskAlter",
    "hookId": "\${'{'}hookId{'}'}",
    "actionTime": "\${'{'}actionTime{'}'}",
    "title": "\${'{'}title{'}'}",
    "content": "\${'{'}content{'}'}",
    "actionData": {'{'}
        "status": "\${'{'}actionData.status{'}'}", //标记当前告警的状态,ING,RECOVER,CLOESE
        "statusTxt":"\${'{'}actionData.statusTxt{'}'}", //标记当前告警的状态文本:正在进行,已恢复,已关闭
        "level": "\${'{'}actionData.level{'}'}", //告警级别RECOVERY,NORMAL,WARNING,CRITICAL,EMERGENCY,ERROR,WARN,INFO
        "component":"\${'{'}actionData.component{'}'}",//引擎告警组件固定为: FE
        "componentTxt": "\${'{'}actionData.componentTxt{'}'}", //引擎告警组件文本值: 引擎
        "type":"\${'{'}actionData.type{'}'}",//告警类型  SYNCHRONIZATIONTASK_ALARM,SHARED_CACHE_ALARM,SHARED_MINING_ALARM,DATA_VERIFICATION_ALARM,ACCURATE_DELAY_ALARM,INSPECT_ALARM
        "typeTxt": "\${'{'}actionData.typeTxt{'}'}", //告警类型文本值,同步任务告警、共享缓存告警、共享挖掘告警、数据校验告警、精准延迟告警
        "metric": "\${'{'}actionData.metric{'}'}",//事件类型: TASK_STATUS_STOP, TASK_STATUS_ERROR, TASK_FULL_COMPLETE, TASK_INCREMENT_START, TASK_INSPECT_ERROR, INSPECT_TASK_ERROR, DATANODE_CANNOT_CONNECT, DATANODE_TCP_CONNECT_CONSUME, DATANODE_HTTP_CONNECT_CONSUME, SYSTEM_FLOW_EGINGE_UP, SYSTEM_FLOW_EGINGE_DOWN, DATANODE_AVERAGE_HANDLE_CONSUME, TASK_INCREMENT_DELAY, PROCESSNODE_AVERAGE_HANDLE_CONSUME, INSPECT_COUNT_ERROR, INSPECT_VALUE_ERROR
        "metricTxt": "\${'{'}actionData.metricTxt{'}'}", //事件类型文本值：任务运行停止，任务运行错误，任务全量完成，任务增量开始，任务校验出错，校验任务遇到错误，数据源无法连接网络，数据源TCP连接完成，数据源连接网络完成，引擎上线，引擎离线，数据源节点的平均处理耗时超过阀值，任务的增量延迟超过阀值，节点的平均处理耗时超过阀值，Count校验结果的差异行数大于阈值，值校验结果的表数据差大于阈值
        "name":"\${'{'}actionData.name{'}'}",//具体的任务名
        "node":"\${'{'}actionData.node{'}'}",//产生告警的节点名，无节点时为空;当为任务告警时，节点直接放任务名
        "currentValue": "\${'{'}actionData.currentValue{'}'}",//触发告警的指标值
        "threshold": "\${'{'}actionData.threshold{'}'}",//触发告警的指标阈值
        "lastOccurrenceTime": "\${'{'}actionData.lastOccurrenceTime{'}'}",//告警最近发生时间
        "tally": "\${'{'}actionData.tally{'}'}",//告警发生次数
        "summary": "\${'{'}actionData.summary{'}'}",//告警内容
        "recoveryTime": "\${'{'}actionData.recoveryTime{'}'}",//告警恢复时间
        "closeTime": "\${'{'}actionData.closeTime{'}'}",//告警关闭时间
        "closeBy": "\${'{'}actionData.closeBy{'}'}",//告警被谁关闭
        "agentId": "\${'{'}actionData.agentId{'}'}", //所属引擎
    {'}'}
{'}'}`,
  webhook_custom_template_ph:
    '自定义模板内容，支持参数填充模板，如：${alarm.name}',
  http_header: 'HTTP 请求头',
  http_header_ph: 'HTTP 请求头,多个请求头请换行输入,示例:Accept: text/html',
  webhook_send_ping: '发送测试 PING 事件',
  webhook_event_type: '事件类型',
  webhook_event_type_empty: '请选择事件',
  daas_licenseType: '授权类型',
  daas_licenseType_pipeline: '通道授权',
  daas_licenseType_lite: 'Lite 版本',
  daas_licenseType_service: '数据服务版',
  daas_licenseType_op: '标准',
  daas_datasourcePipeline: '数据源通道',
  daas_datasourcePipelineLimit: '数据源通道数量',
  daas_datasourcePipeUsageDetails: '通道使用详情',
  account_accessCode_confirm: '确定刷新访问码?',
  account_accessCode_tip:
    '刷新访问码将导致当前访问码失效，系统将生成新的访问码。<b class="color-warning">您需要将新的访问码更新到引擎的配置文件后，重新启动引擎，否则引擎可能会无法正常工作。请谨慎操作！</b>',
  account_accessCode_success: '刷新访问码成功',
}
