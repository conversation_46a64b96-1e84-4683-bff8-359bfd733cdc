<svg width="214" height="126" viewBox="0 0 214 126" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_816_1952)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4 8C4 4.68629 6.68629 2 10 2H203.714C207.028 2 209.714 4.68629 209.714 8V114C209.714 117.314 207.028 120 203.714 120H9.99999C6.68629 120 4 117.314 4 114V8Z" fill="url(#paint0_linear_816_1952)"/>
</g>
<rect opacity="0.15" x="13" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="14" width="0.999991" height="203" transform="rotate(-90 4 14)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="25" width="0.999991" height="203" transform="rotate(-90 4 25)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="36" width="0.999991" height="203" transform="rotate(-90 4 36)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="47" width="0.999991" height="203" transform="rotate(-90 4 47)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="58" width="0.999991" height="203" transform="rotate(-90 4 58)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="69" width="0.999991" height="203" transform="rotate(-90 4 69)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="80" width="0.999991" height="203" transform="rotate(-90 4 80)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="91" width="0.999991" height="203" transform="rotate(-90 4 91)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="102" width="0.999991" height="203" transform="rotate(-90 4 102)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="4" y="113" width="0.999991" height="203" transform="rotate(-90 4 113)" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="24" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="35" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="46" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="57" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="68" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="79" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="90" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="101" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="112" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="123" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="134" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="145" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="156" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="167" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="178" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="189" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<rect opacity="0.15" x="200" y="3" width="1" height="117" fill="white" fill-opacity="0.2"/>
<path opacity="0.5" d="M17 62C17 56.4772 21.4772 52 27 52H187C192.523 52 197 56.4772 197 62V120H17V62Z" fill="#F9FAFE"/>
<path d="M20 65C20 59.4772 24.4772 55 30 55H184C189.523 55 194 59.4772 194 65V120H20V65Z" fill="#FCFCFC"/>
<path d="M20 65C20 59.4772 24.4772 55 30 55H60V120H20V65Z" fill="white"/>
<rect x="28" y="63" width="27" height="8" rx="2" fill="#4278EF"/>
<path d="M62 65C62 62.2386 64.2386 60 67 60H184C186.761 60 189 62.2386 189 65V120H62V65Z" fill="#F3F3F3"/>
<rect x="28" y="74" width="27" height="5" rx="2" fill="#E9E9E9"/>
<rect x="28" y="82" width="27" height="5" rx="2" fill="#E9E9E9"/>
<rect x="167" y="53" width="37" height="37" rx="8" fill="#5575F3"/>
<g clip-path="url(#clip0_816_1952)">
<path d="M185 77.4854C180.568 77.4854 177.045 74.0662 177.045 69.7648C177.045 65.4633 180.568 62.0442 185 62.0442C189.432 62.0442 192.955 65.4633 192.955 69.7648C192.955 74.0662 189.432 77.4854 185 77.4854ZM185 75.2795C188.182 75.2795 190.682 72.853 190.682 69.7648C190.682 66.6765 188.182 64.2501 185 64.2501C181.818 64.2501 179.318 66.6765 179.318 69.7648C179.318 72.853 181.818 75.2795 185 75.2795ZM191.704 75.2795L194.886 78.3677L193.295 79.9118L190.114 76.8236L191.704 75.2795Z" fill="white"/>
</g>
<path d="M112.049 98.8H121.94" stroke="#9F9F9F"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M126 98.5L121 101V96L126 98.5Z" fill="#9F9F9F"/>
<circle cx="94.5" cy="97.5" r="9.5" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M92.5445 94H96.4555C98.4123 93.9988 100 95.6148 100 97.6094C100 99.604 98.4123 101.22 96.4555 101.219H92.5445C90.5877 101.22 89.0012 99.604 89 97.6094C89 95.6148 90.5877 93.9988 92.5445 94ZM96.4606 99.7969C97.0251 99.7969 97.5664 99.568 97.9658 99.1609C98.3652 98.7538 98.5885 98.2007 98.5885 97.6254V97.6056V97.6031C98.5885 96.4053 97.6344 95.434 96.4594 95.4352H92.552C91.3733 95.434 90.4168 96.4041 90.4132 97.6056V97.6254C90.4168 98.8268 91.3733 99.7982 92.552 99.7969H96.4606Z" fill="#DE4231"/>
<circle cx="142.5" cy="97.5" r="9.5" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M139.3 92.5258C138.952 92.2175 138.1 91.9559 137.609 92.0062C137.388 92.0296 137.221 92.1143 137.114 92.2543C136.866 92.5835 136.95 93.0551 138.091 94.5928C138.307 95.2475 138.353 95.5901 138.353 95.7621C138.353 96.1256 138.536 96.5432 138.925 97.0652L138.559 98.3536C138.233 99.5057 138.826 100.993 139.377 101.513C139.672 101.79 139.92 101.75 140.043 101.699C140.288 101.6 140.486 101.352 140.615 100.645C140.626 100.675 140.638 100.704 140.649 100.734C140.704 100.875 140.762 101.024 140.822 101.182C141.217 102.215 141.603 102.785 142.038 102.979C142.209 103.04 142.397 102.963 142.482 102.798C142.577 102.614 142.508 102.386 142.329 102.289C142.221 102.241 141.925 102.009 141.502 100.906C141.146 99.9748 140.887 99.3509 140.709 99.0009C140.649 98.883 140.531 98.8068 140.402 98.8007C140.2 98.7921 140.029 98.953 140.019 99.1605C139.975 100.23 139.869 100.695 139.799 100.89C139.497 100.531 139.021 99.4074 139.261 98.5649L139.679 97.091C139.714 96.9719 139.688 96.8429 139.612 96.7471C139.135 96.1453 139.084 95.8579 139.084 95.7621C139.084 95.4145 138.978 94.9318 138.761 94.2882C138.749 94.2502 138.73 94.2146 138.707 94.1839C138.092 93.3585 137.867 92.9446 137.784 92.7493C138.076 92.764 138.634 92.9249 138.825 93.0932C139.02 93.2652 139.206 93.4469 139.385 93.6385C139.475 93.738 139.612 93.7785 139.741 93.7442C140.886 93.4396 141.9 93.9923 142.838 95.4391C143.841 96.9842 144.509 98.2394 144.82 99.1716C144.852 99.2674 144.919 99.346 145.007 99.389C145.603 99.6874 146.171 100.039 146.708 100.439H145.578C145.492 100.44 145.409 100.472 145.344 100.529C145.19 100.664 145.171 100.901 145.301 101.061C145.664 101.503 146.036 101.936 146.419 102.36C146.429 102.374 146.44 102.386 146.452 102.397C146.601 102.539 146.833 102.53 146.971 102.377C147.108 102.225 147.099 101.985 146.951 101.844C146.756 101.63 146.565 101.414 146.379 101.193H147.635C147.697 101.193 147.759 101.177 147.813 101.145C147.99 101.043 148.052 100.814 147.953 100.632C147.864 100.47 147.656 100.191 146.89 99.653C146.432 99.33 145.954 99.0389 145.459 98.781C145.109 97.8058 144.433 96.5432 143.446 95.0227C142.383 93.3855 141.148 92.6953 139.768 92.9692C139.617 92.8156 139.461 92.667 139.3 92.5258ZM140.891 94.8742C140.753 94.6593 140.308 94.6654 140.165 94.6986C140.27 94.8877 140.381 95.0707 140.501 95.25C140.548 95.3458 140.608 95.4318 140.669 95.5178C140.729 95.6037 140.79 95.6897 140.837 95.7855L140.86 95.7622C141.026 95.6455 141.156 95.18 140.891 94.8742Z" fill="#4A94C6"/>
<defs>
<filter id="filter0_d_816_1952" x="0" y="0" width="213.714" height="126" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.0235194 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_816_1952"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_816_1952" result="shape"/>
</filter>
<linearGradient id="paint0_linear_816_1952" x1="106.857" y1="2" x2="106.857" y2="120" gradientUnits="userSpaceOnUse">
<stop stop-color="#7FCA97"/>
<stop offset="1" stop-color="#7FCA97"/>
</linearGradient>
<clipPath id="clip0_816_1952">
<rect width="18" height="18" fill="white" transform="translate(177 62)"/>
</clipPath>
</defs>
</svg>
