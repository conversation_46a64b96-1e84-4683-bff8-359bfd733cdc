{"name": "cloud", "version": "4.0.0", "private": true, "type": "module", "scripts": {"build": "vite build-only", "dev": "vite", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src", "mock": "nodemon ./mock/mock.js", "serve": "vite preview", "start": "vite --open", "start:dev": "cross-env SERVE_ENV=dev vite --open", "start:local": "cross-env SERVE_ENV=local vite --open", "start:test": "cross-env SERVE_ENV=test vite --open", "submodule": "node ./init_submodule.js"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@formily/core": "^2.0.2", "@formily/json-schema": "^2.0.9", "@formily/reactive": "^2.0.2", "@formily/reactive-vue": "^2.0.2", "@formily/shared": "^2.0.9", "@formily/vue": "^2.0.2", "@tap/api": "workspace:^1.0.0", "@tap/assets": "workspace:^1.0.0", "@tap/business": "workspace:^1.0.0", "@tap/component": "workspace:^1.0.0", "@tap/dag": "workspace:^1.0.0", "@tap/form": "workspace:^1.0.0", "@tap/i18n": "workspace:^1.0.0", "@tap/ldp": "workspace:^1.0.0", "@tap/node-design": "workspace:^1.0.0", "@tap/shared": "workspace:^1.0.0", "@tap/task": "workspace:^1.0.0", "ali-oss": "^6.17.1", "axios": "^0.21.1", "crypto-js": "^4.0.0", "dayjs": "^1.11.2", "driver.js": "^1.2.1", "echarts": "^5.0.2", "element-plus": "^2.4.1", "github-markdown-css": "^4.0.0", "highlight.js": "11.9.0", "mousetrap": "^1.6.5", "qs": "^6.10.3", "vue": "^3.0.0", "vue-echarts": "^6.0.0", "vue-router": "^4.0.8", "vue-virtual-scroller": "2.0.0-beta.8", "vuex": "^4.0.2"}, "devDependencies": {"@cn-xufei/vite-plugin-svg-icons": "^2.1.0", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^3.0.2", "chalk": "^5.4.1", "cross-env": "^7.0.3", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "express-ws": "^5.0.2", "mockjs": "^1.1.0", "nodemon": "^2.0.12", "sass": "^1.69.4", "unplugin-auto-import": "^0.16.6", "unplugin-icons": "^0.17.1", "unplugin-vue-components": "^0.25.2", "vite": "^5.3.1"}}