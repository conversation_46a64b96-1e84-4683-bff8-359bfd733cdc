<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta name="version" content="-version-" />

    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-dfs-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-dfs-16x16.png" />
    <!-- [if IE]><link rel="shortcut icon" href="/favicon-dfs.ico"><![endif] -->

    <title>Tapdata Cloud</title>
    <script>
      var _hmt = _hmt || []
      ;(function () {
        var hm = document.createElement('script')
        hm.src = 'https://hm.baidu.com/hm.js?2b7dfd21af7b3f80683e47fa539f2323'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(hm, s)
      })()
    </script>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-11255929247"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'AW-11255929247');
    </script>
    <!--Bing Ads-->
    <script>
      (function(w,d,t,r,u)
      {
        var f,n,i;
        w[u]=w[u]||[],f=function()
        {
          var o={ti:"187121130", enableAutoSpaTracking: true};
          o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")
        },
          n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function()
        {
          var s=this.readyState;
          s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)
        },
          i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)
      })
      (window,document,"script","//bat.bing.com/bat.js","uetq");
    </script>
    <!--End Bing Ads-->
  </head>
  <body>
    <noscript>
	    <strong>We're sorry but this app doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div class="el-loading-mask is-fullscreen" style="z-index: 3000">
        <div class="el-loading-spinner">
          <svg viewBox="25 25 50 50" class="circular">
            <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
          </svg>
        </div>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
