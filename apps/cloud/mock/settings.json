{"data": [{"category": "Log", "key": "logLevel", "value": "info", "default_value": "info", "documentation": "Enter jobs log level, error/warn/info/debug/trace", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 1, "key_label": "Log level", "user_visible": true, "hot_reloading": true, "id": "1", "enums": ["error", "warn", "info", "debug", "trace"]}, {"category": "SMTP", "key": "smtp.server.port", "value": "", "default_value": " ", "documentation": "SMTP Server Port", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "category_sort": 1, "key_label": "SMTP Server Port", "user_visible": true, "hot_reloading": true, "id": "10", "mask": "###"}, {"category": "SMTP", "key": "smtp.server.user", "value": "", "default_value": " ", "documentation": "SMTP Server User", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 1, "key_label": "SMTP Server User", "user_visible": true, "hot_reloading": true, "id": "11"}, {"category": "SMTP", "key": "smtp.server.password", "value": "******", "default_value": " ", "documentation": "SMTP Server password", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 2, "category_sort": 1, "key_label": "SMTP Server password", "user_visible": true, "hot_reloading": true, "id": "12"}, {"category": "SMTP", "key": "email.receivers", "value": "", "default_value": " ", "documentation": "Email Receivers，Multiple separated by semicolons", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 7, "category_sort": 1, "key_label": "Email Receivers", "user_visible": true, "hot_reloading": true, "id": "13"}, {"category": "Download", "key": "download.showPage", "value": "false", "default_value": "false", "documentation": "Show download page in dashboard or not", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 5, "key_label": "Show Page", "user_visible": false, "hot_reloading": true, "id": "14", "enums": ["true", "false"]}, {"category": "Users", "key": "users.registery", "value": "self-signup", "default_value": "self-signup", "documentation": "Set for sign up.Value as 'disabled' for disabling self-signup; Value as 'self-signup' for enabling self-signup; Value as 'manual-approval' for enabling self-signup, but need to be approved by the administrator", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 6, "key_label": "User Registery", "user_visible": true, "hot_reloading": true, "id": "15", "enums": ["self-signup", "manual-approval", "disabled"]}, {"category": "DR_Rehearsal", "key": "enableDR", "value": "false", "default_value": "false", "documentation": "DR Rehearsal switch,value could be 'true' or 'false'", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 6, "key_label": "Enable DR Rehearsal", "user_visible": true, "hot_reloading": true, "id": "16", "enums": ["true", "false"]}, {"category": "DR_Rehearsal", "key": "mongod<PERSON><PERSON>", "value": "/usr/local/bin", "default_value": "/usr/local/bin", "documentation": "path to the mongod binary", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 6, "key_label": "Mongod path", "user_visible": true, "hot_reloading": true, "id": "17"}, {"category": "DR_Rehearsal", "key": "sshUser", "value": "tapdata", "default_value": "tapdata", "documentation": "SSH username, used to connect to mongod host", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 2, "category_sort": 6, "key_label": "SSH User", "user_visible": true, "hot_reloading": true, "id": "18"}, {"category": "DR_Rehearsal", "key": "sshPort", "value": "22", "default_value": "22", "documentation": "SSH port, used to connect to mongod host", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "category_sort": 6, "key_label": "SSH Port", "user_visible": true, "hot_reloading": true, "id": "19", "mask": "##"}, {"category": "Download", "key": "download.showAgentPage", "value": "true", "default_value": "false", "documentation": "Download Agent Page", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "category_sort": 6, "key_label": "Download Agent Page", "user_visible": false, "hot_reloading": true, "id": "21", "enums": ["true", "false"]}, {"category": "Api", "key": "Api.design", "value": "false", "default_value": "false", "documentation": "API Design Page", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "category_sort": 6, "key_label": "Data publish display.", "user_visible": false, "hot_reloading": true, "id": "22", "enums": ["true", "false"]}, {"category": "Background_Analytics", "key": "qualityAnalysisInterval", "value": "3600", "default_value": "3600", "documentation": "quality Analysis Interval", "scope": "global", "sort": 2, "category_sort": 6, "key_label": "Data quality analysis frequency", "user_visible": true, "hot_reloading": true, "id": "23", "mask": "####"}, {"category": "Background_Analytics", "key": "dashboardAnalysisInterval", "value": "30", "default_value": "30", "documentation": "dashboard Analysis Interval", "scope": "global", "sort": 2, "category_sort": 6, "key_label": "Dashboard data analysis frequency", "user_visible": false, "hot_reloading": true, "id": "24", "mask": "##"}, {"category": "UI", "key": "UI.showLanguageOption", "value": "true", "default_value": "true", "scope": "global", "user_visible": false, "hot_reloading": false, "id": "26", "enums": ["true", "false"]}, {"category": "UI", "key": "UI.defaultLanguage", "value": "en", "default_value": "en", "scope": "global", "user_visible": false, "hot_reloading": false, "id": "27"}, {"category": "UI", "key": "UI.productLogoFile", "value": "logo.svg", "default_value": "logo.svg", "scope": "global", "id": "28"}, {"category": "UI", "key": "UI.productName", "value": "Tapdata", "default_value": "Tapdata", "scope": "global", "id": "29"}, {"category": "UI", "key": "UI.helpURL", "value": "https://docs.tapdata.io", "default_value": "static/docs/index.html", "scope": "global", "id": "30"}, {"category": "Log", "key": "log4jFilterInterval", "value": "20", "default_value": "20", "documentation": "Filter the interval between duplicate logs (seconds).", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 2, "category_sort": 1, "key_label": "Log filter interval", "user_visible": true, "hot_reloading": true, "id": "31"}, {"category": "Log", "key": "log4jFilterIsFormat", "value": "true", "default_value": "true", "documentation": "", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "category_sort": 1, "key_label": "Log filter is formatted.", "user_visible": false, "hot_reloading": true, "id": "32", "enums": ["true", "false"]}, {"category": "System", "key": "buildProfile", "value": "DFS", "scope": "global", "user_visible": false, "hot_reloading": false, "id": "33"}, {"category": "UI", "key": "UI.productTitle", "value": "Tapdata", "default_value": "Tapdata", "scope": "global", "id": "34"}, {"category": "SYBASE", "key": "sybaseCDCBufferLimit", "value": "100000", "default_value": "100000", "scope": "global", "user_visible": false, "id": "38"}, {"category": "SYBASE", "key": "sybaseCDCOffsetAhead", "value": "3000", "default_value": "3000", "scope": "global", "user_visible": false, "id": "39"}, {"category": "LIB", "key": "libSupported", "value": "initialSync,increamentalSync,stats,syncProgress,dataValidate,onData,isMerge,customMapping,dbCloneCDC,sourceAndTarget", "default_value": "initialSync,increamentalSync,stats,syncProgress,dataValidate,onData,isMerge,customMapping,dbCloneCDC,sourceAndTarget", "scope": "global", "user_visible": false, "id": "40"}, {"category": "UDP", "key": "udp.header", "value": "test", "default_value": "test", "documentation": "Enter udp source header.", "scope": "global", "sort": 1, "category_sort": 7, "key_label": "Header", "user_visible": false, "hot_reloading": true, "id": "42"}, {"category": "Job", "key": "mongodb.ts", "value": "false", "default_value": "false", "documentation": "Mongodb target create date docs", "scope": "global", "sort": 2, "category_sort": 8, "key_label": "Mongodb target create date", "user_visible": true, "hot_reloading": true, "id": "44", "enums": ["true", "false"]}, {"category": "UDP", "key": "udp.sendBatchSize", "value": "100", "default_value": "100", "documentation": "Enter udp target send batch size.", "scope": "global", "sort": 2, "category_sort": 7, "key_label": "Send batch size", "user_visible": false, "hot_reloading": true, "id": "46"}, {"category": "File", "key": "file.defaultCharset", "value": "GBK", "default_value": "GBK", "documentation": "File default charset for parse.", "scope": "global", "sort": 1, "category_sort": 8, "key_label": "File Charset", "user_visible": false, "hot_reloading": false, "id": "47"}, {"category": "Database", "key": "database.lobMaxSize", "value": "8388608", "default_value": "8388608", "documentation": "Database lob data type size (byte) limit.", "scope": "global", "sort": 1, "category_sort": 9, "key_label": "<PERSON><PERSON>", "user_visible": false, "hot_reloading": false, "id": "48"}, {"category": "System", "key": "collectSystemInfo", "value": "60", "default_value": "30", "documentation": "Interval to collect system info", "scope": "global", "sort": 1, "category_sort": 1, "key_label": "Collect system info interval", "user_visible": true, "hot_reloading": true, "id": "49"}, {"category": "Job", "key": "sampleRate", "value": "1", "default_value": "1", "documentation": "Validator to validate data 's sample rate", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 4, "category_sort": 2, "key_label": "Sample Rate", "user_visible": true, "hot_reloading": true, "id": "5"}, {"category": "System", "key": "dataRulesTag", "value": "false", "default_value": "false", "documentation": "Need to data rules tag.", "scope": "global", "user_visible": false, "hot_reloading": false, "id": "50", "enums": ["true", "false"]}, {"category": "ApiServer", "key": "defaultLimit", "value": "10", "default_value": "10", "documentation": "De<PERSON><PERSON>", "scope": "global", "sort": 1, "category_sort": 1, "key_label": "De<PERSON><PERSON>", "user_visible": true, "hot_reloading": true, "id": "51"}, {"category": "ApiServer", "key": "maxLimit", "value": "0", "default_value": "1000", "documentation": "<PERSON>", "scope": "global", "sort": 2, "category_sort": 1, "key_label": "<PERSON>", "user_visible": true, "hot_reloading": true, "id": "52"}, {"category": "_DK36", "key": "fileDownBaseUrl", "value": "http://www.bing.com", "default_value": "http://www.bing.com", "documentation": "File Down Base Url", "scope": "dk36", "sort": 1, "category_sort": 1, "key_label": "File Down Base Url", "user_visible": false, "hot_reloading": true, "id": "53"}, {"category": "System", "key": "jsBuildFunc", "value": "uuid,process", "default_value": "uuid,process", "documentation": "Build-in js function.", "scope": "global", "user_visible": false, "hot_reloading": false, "id": "54", "values": ["uuid", "process"]}, {"category": "ApiServer", "key": "apiStatsBatchReport.enableApiStatsBatchReport", "value": "true", "default_value": "true", "documentation": "Enable API Stats Batch Report", "scope": "global", "sort": 3, "category_sort": 1, "key_label": "Enable API Stats Batch Report", "user_visible": true, "hot_reloading": true, "id": "55", "enums": ["true", "false"]}, {"category": "Log", "key": "log4jFilterRate", "value": "16", "default_value": "16", "documentation": "Set the average number of events per second to allow", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "category_sort": 1, "key_label": "Log Filter Rate", "user_visible": true, "hot_reloading": true, "id": "56"}, {"category": "ApiServer", "key": "apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport", "value": "1000", "default_value": "1000", "documentation": "Size Of Trigger API Stats Report", "scope": "global", "sort": 4, "category_sort": 1, "key_label": "Size Of Trigger API Stats Report", "user_visible": true, "hot_reloading": true, "id": "57"}, {"category": "ApiServer", "key": "apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport", "value": "5000", "default_value": "5000", "documentation": "Time Span Of Trigger API Stats Report", "scope": "global", "sort": 5, "category_sort": 1, "key_label": "Time Span Of Trigger API Stats Report", "user_visible": true, "hot_reloading": true, "id": "58"}, {"category": "Connections", "key": "connections.mongodbLoadSchemaSampleSize", "value": "100", "default_value": "100", "documentation": "Mongodb will use this sample size when load schema", "scope": "global", "sort": 5, "category_sort": 1, "key_label": "Mongodb Load Schema Sample Size", "user_visible": true, "hot_reloading": true, "id": "59"}, {"category": "Check_devices", "key": "dkcheck.ipaddresses", "value": "127.0.0.1", "default_value": "127.0.0.1", "documentation": "", "scope": "global", "sort": 1, "category_sort": 99, "key_label": "Ip addresses", "user_visible": false, "hot_reloading": true, "id": "60"}, {"category": "Check_devices", "key": "dkcheck.timeout", "value": 3000, "default_value": 3000, "documentation": "", "scope": "global", "sort": 2, "category_sort": 99, "key_label": "PingTimeout", "user_visible": false, "hot_reloading": true, "id": "61"}, {"category": "Users", "key": "logoutForwardUrl", "value": "", "default_value": "#", "documentation": "Logout forward to this url", "scope": "global", "sort": 5, "category_sort": 1, "key_label": "Logout forward to this url", "user_visible": false, "hot_reloading": true, "id": "62"}, {"category": "Oracle", "key": "Oracle.logminer.mine.config", "value": "automatically", "default_value": "automatically", "documentation": "Oracle logminer mine config", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 10, "key_label": "Oracle logminer mine config", "user_visible": false, "hot_reloading": true, "id": "63"}, {"category": "Job", "key": "job.field_replacement", "value": "_", "default_value": "_", "documentation": "A replacement for the invalid field name", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "category_sort": 2, "key_label": "Job_field_replacement", "user_visible": true, "hot_reloading": true, "id": "64"}, {"category": "ops", "key": "serversOversee", "value": "", "default_value": "-", "documentation": "运控运维URL", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 1, "key_label": "server_oversee_url", "user_visible": true, "hot_reloading": true, "id": "65"}, {"category": "System", "key": "version", "value": "__1V2E3R4S5I6O7N__", "scope": "global", "user_visible": false, "hot_reloading": false, "id": "66"}, {"category": "Job", "key": "switchInsertModeInterval", "value": "120", "default_value": "120", "documentation": "Switch to batch insert mode interval (s) in cdc.", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "category_sort": 1, "key_label": "switch_insert_mode_interval", "user_visible": true, "hot_reloading": true, "id": "67"}, {"category": "SMTP", "key": "email.title.prefix", "value": "", "default_value": " ", "documentation": "Send Email Title Prefix", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 8, "category_sort": 1, "key_label": "Send Email Title Prefix", "user_visible": true, "hot_reloading": true, "id": "68"}, {"category": "Job", "key": "jobHeartTimeout", "value": "60000", "default_value": "60000", "documentation": "Job heart timeout", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "category_sort": 2, "key_label": "Job heart timeout", "user_visible": true, "hot_reloading": true, "id": "69"}, {"category": "Worker", "key": "lastHeartbeat", "value": "60", "default_value": "60", "documentation": "Worker heartbeat expire time", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "category_sort": 3, "key_label": "Worker Heartbeat Expire", "user_visible": true, "hot_reloading": true, "id": "7", "mask": "##"}, {"category": "Job", "key": "job_cdc_share_mode", "value": "false", "default_value": "false", "documentation": "job_cdc_share_mode_doc", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 8, "category_sort": 2, "key_label": "job_cdc_share_mode", "user_visible": true, "hot_reloading": true, "id": "72", "enums": ["true", "false"]}, {"category": "Job", "key": "job_cdc_share_only", "value": "false", "default_value": "false", "documentation": "job_cdc_share_only_doc", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "job_cdc_share_only", "user_visible": true, "hot_reloading": true, "id": "73", "enums": ["true", "false"]}, {"category": "SMTP", "key": "email.server.tls", "value": "SSL", "default_value": "SSL", "documentation": "Email Communication Protocol", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "category_sort": 1, "key_label": "Email Communication Protocol", "user_visible": true, "hot_reloading": true, "id": "74", "enums": ["SSL", "TLS"]}, {"category": "SMTP", "key": "email.send.address", "value": "", "default_value": " ", "documentation": "Email Send Address", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 6, "category_sort": 1, "key_label": "Email Send Address", "user_visible": true, "hot_reloading": true, "id": "75"}, {"category": "notification", "key": "notification", "value": "{\"runNotification\":[{\"label\":\"jobStarted\",\"notice\":true,\"email\":false},{\"label\":\"jobPaused\",\"notice\":true,\"email\":true},{\"label\":\"jobDeleted\",\"notice\":true,\"email\":false},{\"label\":\"jobStateError\",\"notice\":true,\"email\":true},{\"label\":\"jobEncounterError\",\"notice\":true,\"email\":true,\"noticeInterval\":\"noticeInterval\",\"Interval\":12,\"util\":\"hour\"},{\"label\":\"CDCLagTime\",\"notice\":true,\"email\":true,\"lagTime\":\"lagTime\",\"lagTimeInterval\":12,\"lagTimeUtil\":\"second\",\"noticeInterval\":\"noticeInterval\",\"noticeIntervalInterval\":24,\"noticeIntervalUtil\":\"hour\"},{\"label\":\"inspectCount\",\"notice\":true,\"email\":true},{\"label\":\"inspectValue\",\"notice\":true,\"email\":true},{\"label\":\"inspectDelete\",\"notice\":true,\"email\":true},{\"label\":\"inspectError\",\"notice\":true,\"email\":true}],\"systemNotification\":[],\"agentNotification\":[{\"label\":\"serverDisconnected\",\"notice\":true,\"email\":true},{\"label\":\"agentStarted\",\"notice\":true,\"email\":false},{\"label\":\"agentStopped\",\"notice\":true,\"email\":true},{\"label\":\"agentCreated\",\"notice\":true,\"email\":false},{\"label\":\"agentDeleted\",\"notice\":true,\"email\":true}]}", "default_value": "", "documentation": "Notification Setting", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 4, "key_label": "Notification Setting", "user_visible": false, "hot_reloading": true, "id": "76"}, {"category": "Connections", "key": "creatDuplicateSource", "value": "true", "default_value": "true", "documentation": "creatDuplicateSource", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "creatDuplicateSource", "user_visible": true, "hot_reloading": true, "id": "77", "enums": ["true", "false"]}, {"category": "Connections", "key": "connection_schema_update_hour", "value": "02:00", "default_value": "02:00", "documentation": "connection_schema_update_hour", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 6, "category_sort": 2, "key_label": "connection_schema_update_hour", "user_visible": true, "hot_reloading": true, "id": "78", "enums": ["false", "00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"]}, {"category": "Connections", "key": "connection_schema_update_interval", "value": "1", "default_value": "1", "documentation": "connection_schema_update_interval", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 7, "category_sort": 2, "key_label": "connection_schema_update_interval", "user_visible": true, "hot_reloading": true, "id": "79", "enums": ["1", "2", "3", "4", "5", "6", "7", "10", "15", "30", "60", "90"]}, {"category": "License", "key": "license", "value": "user=<EMAIL>,count=1,signature=0cbfaffb4b88441b3446ce1a69ad8ba2", "default_value": "user=<EMAIL>,count=1,signature=0cbfaffb4b88441b3446ce1a69ad8ba2", "documentation": "License Key", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "category_sort": 4, "key_label": "License Key", "user_visible": false, "hot_reloading": true, "id": "8"}, {"category": "Job", "key": "job_cdc_record", "value": "false", "default_value": "false", "documentation": "job_cdc_record_doc", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "job_cdc_record", "user_visible": true, "hot_reloading": true, "id": "80", "enums": ["true", "false"]}, {"category": "Job", "key": "job_cdc_record_ttl", "value": "7", "default_value": "7", "documentation": "job_cdc_record_ttl_doc", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "job_cdc_record_ttl", "user_visible": true, "hot_reloading": true, "id": "81", "enums": ["3", "5", "7", "10", "15", "30", "60", "90"]}, {"category": "Inspect", "key": "InspectSetting", "value": "{\"retentionTime\":3,\"reservedQuantityPerTable\":1000,\"differenceTolerant\":1000,\"rowInspectFrequency\":5,\"rowInspectUtil\":\"minute\",\"rowInspectContinuedTime\":1,\"rowInspectContinuedUtil\":\"day\",\"contentInspectFrequency\":24,\"contentInspectUtil\":\"hour\",\"contentInspectContinuedTime\":30,\"contentInspectContinuedUtil\":\"day\",\"contentInspectStartTime\":\"04:00\"}", "default_value": " ", "documentation": "Inspect Setting", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 6, "category_sort": 1, "key_label": "Inspect Setting", "user_visible": false, "hot_reloading": true, "id": "82"}, {"category": "Job", "key": "mongodb.after", "value": "false", "default_value": "false", "documentation": "When one document may be updated frequently within very short period(a few updates within one second, for instance), the change stream event received by downstream processor may return the \"fullDocument\" that is inconsistent with the actual version when the update was applied to that document. To avoid this inconsistency, enable this option to store the full document along with the update operation. This will at the expense of additional storage and degraded performance.", "scope": "global", "sort": 2, "category_sort": 9, "key_label": "Store full record as embedded document in target collection for update operations", "user_visible": true, "hot_reloading": true, "id": "83", "enums": ["true", "false"]}, {"category": "ApiServer", "key": "APIServerTimeZone", "value": "0", "default_value": "0", "documentation": "APIServer time zone", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "APIServerTimeZone", "user_visible": false, "hot_reloading": false, "id": "84"}, {"category": "system", "key": "showLicenseNotice", "value": "0", "default_value": "0", "documentation": "", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "APIServerTimeZone", "user_visible": false, "hot_reloading": false, "id": "85"}, {"category": "Job", "key": "mongodb.before", "value": "false", "default_value": "false", "documentation": "the before field contains a field for each table column and the value that was in that column before the update operation.", "scope": "global", "sort": 2, "category_sort": 9, "key_label": "Store before field as embedded document in target collection before update operation", "user_visible": true, "hot_reloading": true, "id": "86", "enums": ["true", "false"]}, {"category": "Job", "key": "scriptEngineHttpAppender", "value": "false", "default_value": "false", "documentation": "true: store log to cloud, false: only store to local log file.", "scope": "global", "sort": 2, "category_sort": 9, "key_label": "Store job script processor log to cloud", "user_visible": true, "hot_reloading": true, "id": "87", "enums": ["true", "false"]}, {"category": "system", "key": "licenseNoticeDays", "value": "", "default_value": "90", "documentation": "", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "licenseNoticeDays", "user_visible": true, "hot_reloading": true, "id": "88"}, {"category": "Job", "key": "mongodbChangeStreamLookup", "value": "", "default_value": "false", "documentation": "", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "category_sort": 2, "key_label": "mongodbChangeStreamLookup", "user_visible": false, "hot_reloading": true, "id": "89"}, {"category": "SMTP", "key": "smtp.server.host", "value": "", "default_value": " ", "documentation": "SMTP Server Host", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 4, "category_sort": 1, "key_label": "SMTP Server Host", "user_visible": true, "hot_reloading": true, "id": "9"}, {"category": "system", "key": "integration", "value": "ProxyGateway", "default_value": "", "documentation": "", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "category_sort": 2, "key_label": "integration", "user_visible": false, "hot_reloading": false, "id": "90"}, {"category": "System", "key": "checkLicense", "value": "false", "documentation": "", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "category_sort": 2, "user_visible": false, "hot_reloading": false, "id": "9999"}, {"category": "UI", "key": "UI.showRolepage", "value": "false", "id": "604f4b80e1ca905fa75452d5"}, {"category": "UI", "key": "UI.showDataCatalogPage", "value": "false", "id": "604f4b80e1ca905fa75452d7"}, {"category": "UI", "key": "UI.showDataQualityPage", "value": "false", "id": "604f4b80e1ca905fa75452d9"}, {"category": "UI", "key": "UI.showDashboardPage", "value": "false", "id": "604f4b80e1ca905fa75452db"}, {"category": "UI", "key": "UI.showApplicationPage", "value": "false", "id": "604f4b80e1ca905fa75452dd"}, {"category": "Frontend", "key": "PRODUCT_TITLE", "value": "数据库复制 DRS", "id": "604f4b80e1ca905fa75452e6"}, {"category": "Frontend", "key": "SHOW_LANGUAGE", "value": 0, "id": "604f4b80e1ca905fa75452e8"}, {"category": "Frontend", "key": "DEFAULT_LANGUAGE", "value": "sc", "id": "604f4b80e1ca905fa75452ea"}, {"category": "Frontend", "key": "SHOW_REGISTER", "value": 1, "id": "604f4b80e1ca905fa75452ec"}, {"category": "Frontend", "key": "SHOW_OLD_PAGE", "value": 0, "id": "604f4b80e1ca905fa75452ee"}, {"category": "Frontend", "key": "SHOW_PAGE_TITLE", "value": 0, "id": "604f4b80e1ca905fa75452f0"}, {"category": "Frontend", "key": "SHOW_LICENSE", "value": 0, "id": "604f4b80e1ca905fa75452f2"}, {"category": "Frontend", "key": "SHOW_NOTIFICATION", "value": 0, "id": "604f4b80e1ca905fa75452f4"}, {"category": "Frontend", "key": "SHOW_QA_AND_HELP", "value": 0, "id": "604f4b80e1ca905fa75452f6"}, {"category": "Frontend", "key": "SHOW_SETTING_BUTTON", "value": 0, "id": "604f4b80e1ca905fa75452f8"}, {"category": "Frontend", "key": "SHOW_HOME_BUTTON", "value": 0, "id": "604f4b80e1ca905fa75452fa"}, {"category": "Frontend", "key": "ALLOW_DOWNLOAD_AGENT", "value": 0, "id": "604f4b80e1ca905fa75452fc"}, {"category": "Frontend", "key": "USE_CLOUD_MENU", "value": 1, "id": "604f4b80e1ca905fa75452fe"}, {"category": "Frontend", "key": "SHOW_DK_VERSION", "value": 0, "id": "604f4b80e1ca905fa7545300"}, {"category": "Frontend", "key": "ALLOW_CONNECTION_TYPE", "value": "mysql,oracle,sqlserver,mongodb,postgres,redis,elasticsearch", "id": "604f4b80e1ca905fa7545302", "isArray": true}, {"category": "Frontend", "key": "SHOW_PREVIEW", "value": 0, "id": "604f4b80e1ca905fa7545304"}, {"category": "Frontend", "key": "SHOW_DATA_TRACE", "value": 0, "id": "604f4b80e1ca905fa7545306"}, {"category": "Frontend", "key": "SHOW_CLUSTER_OR_AGENT", "value": "agent", "id": "604f4b80e1ca905fa7545308"}, {"category": "Frontend", "key": "SHOW_SIMPLE_SCENE", "value": 0, "id": "604f4b80e1ca905fa754530a"}, {"category": "Frontend", "key": "SHOW_CLASSIFY", "value": 1, "id": "604f4b80e1ca905fa754530c"}, {"category": "Frontend", "key": "CREATE_DATAFLOW_BY_FORM", "value": 1, "id": "604f4b80e1ca905fa754530e"}, {"category": "Frontend", "key": "HAVE_INSTANCE", "value": 1, "id": "604f4b80e1ca905fa7545324"}, {"category": "Frontend", "key": "COMING_ONLINE_CONNECTION_TYPE", "value": "db2,sybase ase,kafka,gbase-8s", "id": "607ea59663681154b4a30481", "isArray": true}], "code": "ok", "msg": "ok"}