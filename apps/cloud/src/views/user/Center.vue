<script>
import { EditPen } from '@element-plus/icons-vue'
import { NUMBER_MAP } from '@tap/business'

import { IconButton, VTable } from '@tap/component'
import { langMenu } from '@tap/i18n/src/shared/util'
import { openUrl, urlToBase64 } from '@tap/shared'
import CryptoJS from 'crypto-js'
import dayjs from 'dayjs'
import { mapGetters, mapState } from 'vuex'
import InlineInput from '@/components/InlineInput'
import UploadFile from '@/components/UploadFile'
import VerificationCode from '@/components/VerificationCode'
import i18n from '@/i18n'
import { $emit, $off, $on, $once } from '../../../utils/gogocodeTransfer'
import { AGENT_TYPE_MAP } from '../instance/utils'

export default {
  name: 'Center',
  components: {
    InlineInput,
    VerificationCode,
    UploadFile,
    VTable,
    IconButton,
    EditPen,
  },
  inject: ['buried'],
  data() {
    return {
      langMenu,
      agentTypeMap: AGENT_TYPE_MAP,
      nameForm: {
        nickname: '',
        firstName: '',
        lastName: '',
      },
      avatar: '',
      dialogObj: {
        avatar: false,
        password: false,
        bindPhone: false,
        editPhone: false,
        bindWx: false,
        bindEmail: false,
        editEmail: false,
        firstName: false,
      },
      passwordForm: {
        telephone: '',
        code: '',
        emailCode: '',
        countryCode: '86',
        newPassword: '',
        newAgainPassword: '',
      },
      phoneForm: {
        current: '',
        oldCode: '',
        newPhone: '',
        newCode: '',
        countryCode: '86',
      },
      emailForm: {
        email: '',
        code: '',
        newEmail: '',
        newCode: '',
      },
      enData: {
        companyName: '',
        website: '',
        industry: '',
        city: '',
      },
      enForm: {
        companyName: '',
        website: '',
        industry: '',
        city: '',
      },
      keyForm: {
        accessKey: '',
        secretKey: '',
        decodeSecretKey: '',
      },
      isEdit: false,
      accessKeyTooltip: false,
      secretKeyTooltip: false,
      codeColumns: [
        {
          label: i18n.t('dfs_instance_selectlist_shouquanma'),
          prop: 'licenseCode',
        },
        {
          label: i18n.t('dfs_user_center_jihuoshijian2'),
          prop: 'activateTimeLabel',
          width: 320,
        },
        {
          label: i18n.t(
            'dfs_agent_download_subscriptionmodeldialog_tuoguanfangshi',
          ),
          prop: 'agentType',
          slotName: 'agentType',
        },
        {
          label: i18n.t('dfs_user_center_guoqishijian2'),
          prop: 'expiredTimeLabel',
          width: 320,
        },
        {
          label: i18n.t('dfs_instance_selectlist_bangdingshilizhuang'),
          prop: 'bindAgent',
          slotName: 'bindAgent',
        },
        {
          label: i18n.t('public_operation'),
          prop: 'extendArray',
          slotName: 'operation',
          width: 100,
        },
      ],
      recordData: {
        visible: false,
        content: '',
        price: 0,
        statusLabel: i18n.t('dfs_user_center_jiaoyichenggong'),
        items: [
          {
            label: i18n.t('dfs_user_center_fukuanfangshi'),
            value: i18n.t('dfs_user_center_weixinzhifu'),
          },
          {
            label: i18n.t('public_create_time'),
            value: '2023-03-04 17:56:33',
          },
          {
            label: i18n.t('dfs_user_center_zhifushijian'),
            value: '2023-03-04 17:56:40',
          },
          {
            label: i18n.t('dfs_user_center_dingdanhao'),
            value: '2023030419203919321',
          },
        ],
      },
      countryCode: [],
    }
  },
  computed: {
    ...mapGetters(['isDomesticStation', 'language']),
    ...mapState({
      userData: 'user',
    }),
    showPhone() {
      return !!this.userData.telephone
    },
  },
  mounted() {
    this.init()
    this.getCountryCode()

    if (this.$route.query.bind === 'email' && !this.userData.email) {
      this.dialogObj.bindEmail = true
    }
  },
  methods: {
    init() {
      const { userData, nameForm } = this

      this.avatar = userData.avatar
      this.getEnterprise()
      this.getAkAndSk()
      this.resetPasswordForm()
      this.resetPhoneForm()
      this.resetEmailForm()
      nameForm.nickname = userData.nickname
      const { customData = {} } = userData
      nameForm.firstName = customData.firstName
      nameForm.lastName = customData.lastName

      userData.licenseCodes =
        userData.licenseCodes?.map((item) => {
          item.activateTime = item.activateTime
            ? dayjs(item.activateTime).format('YYYY-MM-DD HH:mm:ss')
            : ''
          item.expiredTime = item.expiredTime
            ? dayjs(item.expiredTime).format('YYYY-MM-DD HH:mm:ss')
            : ''
          return item
        }) || []
    },
    getCountryCode() {
      this.$axios.get('config/countryCode.json').then((res) => {
        const countryCode = res.data
        this.countryCode = countryCode?.countryCode
      })
    },
    getEnterprise() {
      this.$axios.get('tm/api/Customer').then((data) => {
        for (const key in this.enData) {
          this.enData[key] = data[key] || ''
          this.enForm[key] = data[key] || ''
        }
      })
    },
    getAkAndSk() {
      this.$axios.get('api/tcm/user/ak').then((data) => {
        const { accessKey, secretKey } = data?.[0] || {}
        const key = '5fa25b06ee34581d'
        this.keyForm.accessKey = accessKey
        this.keyForm.decodeSecretKey = CryptoJS.AES.decrypt(
          {
            ciphertext: CryptoJS.enc.Base64.parse(secretKey),
          },
          CryptoJS.enc.Latin1.parse(key),
          {
            iv: CryptoJS.enc.Latin1.parse(key),
          },
        ).toString(CryptoJS.enc.Utf8)
        this.keyForm.secretKey = this.keyForm.decodeSecretKey.replace(
          /(\w{3})\w*(\w{3})/,
          '$1****$2',
        )
      })
    },
    resetPasswordForm() {
      const { userData, passwordForm } = this
      for (const key in passwordForm) {
        if (key === 'telephone') {
          this.passwordForm.telephone = userData.telephone
        } else if (key === 'countryCode') {
          this.passwordForm.countryCode = userData?.phoneCountryCode || '86'
        } else {
          this.passwordForm[key] = ''
        }
      }
    },
    resetPhoneForm() {
      const { userData, phoneForm } = this
      for (const key in phoneForm) {
        if (key === 'current') {
          this.phoneForm.current = userData.telephone
        } else if (key === 'countryCode') {
          this.phoneForm.countryCode = userData?.phoneCountryCode || '86'
        } else {
          this.phoneForm[key] = ''
        }
      }
    },
    resetEmailForm() {
      const { userData, emailForm } = this
      for (const key in emailForm) {
        if (key === 'email') {
          this.emailForm.email = userData.email
        } else {
          this.emailForm[key] = ''
        }
      }
    },
    updateName(val) {
      const nickname = val
      this.$axios
        .patch('api/tcm/user', {
          nickname,
        })
        .then(() => {
          this.userData.nickname = nickname
          this.$message.success(i18n.t('user_Center_xiuGaiNiChengCheng'))
        })
    },
    updateFirstName(resetLoading) {
      const { firstName, lastName } = this.nameForm
      const params = {
        customData: {
          firstName,
          lastName,
        },
      }
      this.$axios
        .patch('api/tcm/user', params)
        .then((data = {}) => {
          this.userData.customData.firstName = data.customData?.firstName
          this.userData.customData.lastName = data.customData?.lastName
          this.$message.success(i18n.t('public_message_operation_success'))
          this.dialogObj.firstName = false
        })
        .finally(() => {
          resetLoading?.()
        })
    },
    upload(evt) {
      const file = evt.target.files[0]
      const leftThan = file.size / 1024 < 500
      if (!leftThan) {
        this.$message.error(i18n.t('user_Center_shangChuanTouXiangTu'))
        return
      }
      urlToBase64(URL.createObjectURL(file)).then((res) => {
        this.avatar = res
      })
    },
    avatarDisabled() {
      return !this.avatar || this.userData.avatar === this.avatar
    },
    editAvatar() {
      this.avatar = this.userData.avatar
      this.dialogObj.avatar = true
    },
    avatarConfirm() {
      const avatar = encodeURI(this.avatar)
      this.$axios
        .patch('api/tcm/user', {
          avatar,
        })
        .then(() => {
          this.$message.success(i18n.t('user_Center_xiuGaiTouXiangCheng'))
          this.userData.avatar = avatar
          this.refreshRootUser()
          this.dialogObj.avatar = false
        })
    },
    sendCode(phone, scene) {
      return this.$axios.post('api/tcm/sms/captcha', {
        phone,
        scene,
      })
    },
    getCodeOptions(val, scene, type = 'sms') {
      const params = {
        scene,
      }
      if (type === 'sms') {
        params.phone = val
        params.countryCode = this.phoneForm.countryCode
      } else {
        params.email = val
      }
      return {
        method: 'post',
        url: `api/tcm/${type}/captcha`,
        params,
      }
    },
    editPassword() {
      if (this.isDomesticStation && !this.userData.telephone) {
        this.$confirm(
          i18n.t('user_Center_qingXianBangDingShou'),
          i18n.t('user_Center_bangDingShouJi'),
          {
            type: 'warning',
          },
        ).then((resFlag) => {
          if (resFlag) {
            this.dialogObj.bindPhone = true
          }
        })
        return
      }
      this.passwordForm.telephone = this.userData.telephone
      this.dialogObj.password = true
    },
    passwordConfirm(resetLoading) {
      const { passwordForm } = this
      const { newPassword, newAgainPassword } = passwordForm
      if (newPassword !== newAgainPassword) {
        this.$message.error(i18n.t('user_Center_shuRuMiMaBu'))
        resetLoading?.()
        return
      }
      this.$axios
        .patch('api/tcm/user/password', {
          phoneCode: passwordForm.code,
          countryCode: passwordForm.countryCode
            ? passwordForm.countryCode.replace('-', '')
            : '86',
          emailCode: passwordForm.emailCode, // 邮件验证吗
          password: CryptoJS.RC4.encrypt(
            passwordForm.newPassword,
            'XWFSxfs8wFcs',
          ).toString(),
        })
        .then(() => {
          this.$message.success(i18n.t('user_Center_xiuGaiMiMaCheng'))
          this.resetPasswordForm()
          this.dialogObj.password = false
        })
        .finally(() => {
          resetLoading?.()
        })
    },
    // bindPhoneSendCode() {
    //   return this.sendCode(this.phoneForm.current, 'BIND_PHONE')
    // },
    bindPhoneConfirm(resetLoading) {
      const { phoneForm } = this
      this.$axios
        .post('api/tcm/user/phone', {
          phone: phoneForm.current,
          code: phoneForm.oldCode,
          countryCode: phoneForm.countryCode
            ? phoneForm.countryCode.replace('-', '')
            : '86',
        })
        .then(() => {
          this.userData.telephone = phoneForm.current
          this.resetPhoneForm()
          this.$message.success(i18n.t('user_Center_bangDingShouJiCheng'))
          this.dialogObj.bindPhone = false
        })
        .finally(() => {
          resetLoading?.()
        })
    },
    editPhone() {
      this.phoneForm.current = this.userData.telephone
      this.dialogObj.editPhone = true
    },
    editPhoneOldSendCode() {
      return this.$axios.get('tm/api/user/sendCode')
    },
    editPhoneNewSendCode() {
      return this.$axios.get('tm/api/user/sendCode')
    },
    editPhoneDisabled() {
      let flag = false
      const { phoneForm } = this
      for (const key in phoneForm) {
        if (phoneForm[key]) {
          flag = true
        }
      }
      return !flag
    },
    editPhoneConfirm(resetLoading) {
      const { phoneForm } = this
      this.$axios
        .patch('api/tcm/user/phone', {
          oldPhoneCode: phoneForm.oldCode,
          phone: phoneForm.newPhone,
          phoneCode: phoneForm.newCode,
          countryCode: phoneForm.countryCode
            ? phoneForm.countryCode.replace('-', '')
            : '86',
        })
        .then(() => {
          this.userData.telephone = phoneForm.newPhone
          this.resetPhoneForm()
          this.$message.success(i18n.t('user_Center_xiuGaiShouJiCheng'))
          this.dialogObj.editPhone = false
        })
        .finally(() => {
          resetLoading?.()
        })
    },
    unbindWx() {
      this.$confirm(
        i18n.t('user_Center_jieChuHouJiangWu'),
        i18n.t('user_Center_jieChuWeiXin'),
        {
          type: 'warning',
        },
      ).then((resFlag) => {
        if (resFlag) {
          this.$axios.patch('tm/api/user/unbindWx').then(() => {
            this.userData.wx = ''
            this.$message.success(i18n.t('user_Center_jieBangWeiXinCheng'))
            this.dialogObj.editPhone = false
          })
        }
      })
    },
    bindEmailSendCode() {
      return this.$axios.get('tm/api/user/sendCode')
    },
    bindEmailConfirm(resetLoading) {
      const { emailForm } = this
      this.$axios
        .post('api/tcm/user/email', {
          email: emailForm.email,
          code: emailForm.code,
        })
        .then(() => {
          this.$store.commit('setUserEmail', emailForm.email)
          this.userData.email = emailForm.email
          this.resetEmailForm()
          this.$message.success(i18n.t('user_Center_bangDingYouXiangCheng'))
          this.dialogObj.bindEmail = false
        })
        .finally(() => {
          resetLoading?.()
        })
    },
    editEmail() {
      this.emailForm.email = this.userData.email
      this.dialogObj.editEmail = true
    },
    editEmailDisabled() {
      let flag = false
      const { emailForm } = this
      for (const key in emailForm) {
        if (emailForm[key]) {
          flag = true
        }
      }
      return !flag
    },
    editEmailConfirm(resetLoading) {
      const { emailForm } = this
      this.$axios
        .patch('api/tcm/user/email', {
          // email: emailForm.email,
          oldEmailCode: emailForm.code,
          email: emailForm.newEmail,
          emailCode: emailForm.newCode,
        })
        .then(() => {
          this.userData.email = emailForm.newEmail
          this.resetEmailForm()
          this.$message.success(i18n.t('user_Center_xiuGaiYouXiangCheng'))
          this.dialogObj.editEmail = false
        })
        .finally(() => {
          resetLoading?.()
        })
    },
    editEnData() {
      this.enForm = Object.assign({}, this.enData)
      this.isEdit = true
    },
    cancelEditEnData() {
      this.isEdit = false
    },
    saveEnData() {
      const { enForm } = this
      this.$axios
        .patch('tm/api/Customer', {
          companyName: enForm.companyName,
          website: enForm.website,
          industry: enForm.industry,
          city: enForm.city,
        })
        .then(() => {
          this.$message.success(i18n.t('user_Center_xiuGaiQiYeXin'))
          this.enData = Object.assign({}, enForm)
          this.isEdit = false
        })
    },
    refreshRootUser() {
      $emit(this.$root, 'get-user')
    },
    handleCopyAccessKey() {
      this.accessKeyTooltip = true
    },
    handleCopySecretKey() {
      this.secretKeyTooltip = true
    },
    handleRecord(item = {}) {
      const { content, priceLabel, createAt, statusLabel, invoiceId } = item
      this.recordData.content = content
      this.recordData.price = priceLabel
      this.recordData.statusLabel = statusLabel
      this.recordData.items = [
        {
          label: i18n.t('dfs_user_center_fukuanfangshi'),
          value: '-',
        },
        {
          label: i18n.t('public_create_time'),
          value: createAt ? dayjs(createAt).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
          label: i18n.t('dfs_user_center_zhifushijian'),
          value: '-',
        },
        {
          label: i18n.t('dfs_user_center_dingdanhao'),
          value: invoiceId || '-',
        },
      ]
      this.recordData.visible = true
    },
    handleAgent(row = {}) {
      this.$router.push({
        name: 'Instance',
        query: {
          keyword: row.agentId,
        },
      })
    },

    //续订
    handleRenew(row = {}) {
      const { period, periodUnit } = row
      const label =
        NUMBER_MAP[period] +
        (i18n?.locale === 'en' ? ' ' : '') +
        (periodUnit === 'year'
          ? i18n.t('public_time_year')
          : i18n.t('dfs_instance_utils_geyue'))
      this.$confirm(
        i18n.t('dfs_user_center_ninjiangxudingr', {
          val1: row.content,
          val2: label,
        }),
        i18n.t('dfs_user_center_xudingfuwu'),
        {
          type: 'warning',
          dangerouslyUseHTMLString: true,
        },
      ).then((res) => {
        if (res) {
          const { agentId } = row
          const params = {
            agentId,
            successUrl: location.href,
            cancelUrl: location.href,
          }
          this.buried('renewAgentStripe')
          this.$axios
            .post('api/tcm/orders/renew', params)
            .then((data) => {
              openUrl(data.paymentUrl)
              this.buried('renewAgentStripe', '', {
                result: true,
              })
            })
            .catch(() => {
              this.buried('renewAgentStripe', '', {
                result: false,
              })
            })
        }
      })
    },

    async handleUpdateLanguage(val) {
      await this.$axios.patch('api/tcm/user', {
        locale: val,
      })
      this.$store.commit('setLanguage', val)
      location.reload()
    },
  },
  emits: ['get-user'],
}
</script>

<template>
  <div class="user-center">
    <div class="flex gap-6">
      <div class="flex-1 rounded-xl overflow-x-hidden bg-white">
        <div class="fs-6 fw-sub p-4">{{ $t('user_Center_geRenXinXi') }}</div>
        <ElDivider class="m-0" />
        <div class="p-4">
          <div class="user-info-grid">
            <div class="flex align-center gap-4">
              <div
                class="avtar-wrapper flex align-center justify-center overflow-hidden"
              >
                <ElImage
                  v-if="userData.avatar"
                  :src="userData.avatar"
                  class="w-100 h-100"
                />
                <VIcon v-else :size="48">database-user-name</VIcon>
              </div>
              <ElButton @click="editAvatar">{{
                $t('user_Center_shangChuanTouXiang')
              }}</ElButton>
            </div>
            <div class="item-wrapper">
              <div class="user-item__label">{{ $t('user_name') }}</div>
              <div class="user-item__value">{{ userData.username }}</div>
            </div>

            <template v-if="!isDomesticStation">
              <div class="item-wrapper">
                <div class="user-item__label">First Name</div>
                <span class="user-item__value">
                  {{ userData.customData.firstName || '-' }}
                  <IconButton @click="dialogObj.firstName = true"
                    >edit</IconButton
                  >
                </span>
              </div>

              <div class="item-wrapper">
                <div class="user-item__label">Last Name</div>
                <span class="user-item__value">
                  {{ userData.customData.lastName || '-' }}
                  <IconButton @click="dialogObj.firstName = true"
                    >edit</IconButton
                  >
                </span>
              </div>
            </template>

            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('user_Center_yongHuNiCheng') }}
              </div>
              <InlineInput
                class="inline-input"
                :value="userData.nickname"
                type="icon"
                @save="updateName($event)"
              />
            </div>

            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('user_Center_youXiang') }}
              </div>
              <div class="user-item__value">
                {{ userData.email || $t('user_Center_weiBangDing') }}
                <IconButton v-if="userData.email" @click="editEmail"
                  >edit</IconButton
                >
                <IconButton v-else @click="dialogObj.bindEmail = true"
                  >edit</IconButton
                >
              </div>
            </div>

            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('user_phone_number') }}
              </div>
              <div class="user-item__value">
                {{ userData.telephone || $t('user_Center_weiBangDing') }}
                <IconButton v-if="userData.telephone" @click="editPhone"
                  >edit</IconButton
                >
                <IconButton v-else @click="dialogObj.bindPhone = true"
                  >edit</IconButton
                >
              </div>
            </div>

            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('public_connection_form_password') }}
              </div>
              <div class="user-item__value">
                ******
                <IconButton @click="editPassword">edit</IconButton>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-1 rounded-xl overflow-x-hidden bg-white">
        <div class="fs-6 fw-sub p-4 flex align-center">
          <span class="flex-1">
            {{ $t('user_Center_qiYeXinXi') }}
          </span>

          <ElButton v-if="!isEdit" text type="primary" @click="editEnData">
            <el-icon class="mr-1"><EditPen /></el-icon>
            {{ $t('public_button_edit') }}
          </ElButton>

          <template v-else>
            <ElButton text type="primary" @click="cancelEditEnData">{{
              $t('public_button_cancel')
            }}</ElButton>
            <ElButton text type="primary" @click="saveEnData">{{
              $t('public_button_save')
            }}</ElButton>
          </template>
        </div>
        <ElDivider class="m-0" />
        <div class="p-4">
          <div class="user-info-grid">
            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('user_Center_gongSiMingCheng') }}
              </div>
              <div v-if="!isEdit" class="user-item__value">
                {{ enData.companyName || $t('user_Center_weiTianXie') }}
              </div>
              <ElInput
                v-else
                v-model="enForm.companyName"
                class="user-item__value"
              />
            </div>
            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('user_Center_gongSiGuanWang') }}
              </div>
              <div v-if="!isEdit" class="user-item__value">
                {{ enData.website || $t('user_Center_weiTianXie') }}
              </div>
              <ElInput
                v-else
                v-model="enForm.website"
                class="user-item__value"
              />
            </div>
            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('user_Center_suoShuHangYe') }}
              </div>
              <div v-if="!isEdit" class="user-item__value">
                {{ enData.industry || $t('user_Center_weiTianXie') }}
              </div>
              <ElInput
                v-else
                v-model="enForm.industry"
                class="user-item__value"
              />
            </div>
            <div class="item-wrapper">
              <div class="user-item__label">
                {{ $t('user_Center_suoShuChengShi') }}
              </div>
              <div v-if="!isEdit" class="user-item__value">
                {{ enData.city || $t('user_Center_weiTianXie') }}
              </div>
              <ElInput v-else v-model="enForm.city" class="user-item__value" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex gap-6 mt-6">
      <div class="flex-1 rounded-xl overflow-x-hidden bg-white">
        <div class="fs-6 fw-sub p-4">
          {{ $t('dfs_user_center_kaifaxinxi') }}
        </div>
        <ElDivider class="m-0" />
        <div class="p-4">
          <ElAlert
            class="mb-4"
            :closable="false"
            :title="$t('dfs_user_center_acces')"
            type="info"
            show-icon
          />

          <div class="user-info-grid">
            <div class="item-wrapper">
              <div class="user-item__label">Access Key</div>
              <div class="user-item__value">
                {{ keyForm.accessKey }}
                <ElTooltip
                  placement="top"
                  manual
                  :content="$t('agent_deploy_start_install_button_copied')"
                  popper-class="copy-tooltip"
                  :visible="accessKeyTooltip"
                >
                  <IconButton
                    v-clipboard:copy="keyForm.accessKey"
                    v-clipboard:success="handleCopyAccessKey"
                    @mouseleave.native="accessKeyTooltip = false"
                    >copy</IconButton
                  >
                </ElTooltip>
              </div>
            </div>
            <div class="item-wrapper">
              <div class="user-item__label">Secret Key</div>
              <div class="user-item__value">
                {{ keyForm.secretKey }}

                <ElTooltip
                  placement="top"
                  manual
                  :content="$t('agent_deploy_start_install_button_copied')"
                  popper-class="copy-tooltip"
                  :visible="secretKeyTooltip"
                >
                  <IconButton
                    v-clipboard:copy="keyForm.decodeSecretKey"
                    v-clipboard:success="handleCopySecretKey"
                    @mouseleave="secretKeyTooltip = false"
                    >copy</IconButton
                  >
                </ElTooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--<div class="flex gap-6 mt-6">
      <div class="flex-fill rounded-xl overflow-x-hidden bg-white">
        <div class="fs-6 fw-sub p-4">{{ $t('header_setting') }}</div>
        <ElDivider class="m-0"></ElDivider>
        <div class="p-4">
          <div class="item-wrapper">
            <div class="user-item__label">{{ $t('dfs_settings_language') }}</div>
            <div class="user-item__value">
              <ElSelect :model-value="language" @change="handleUpdateLanguage">
                <ElOption v-for="(v, k) in langMenu" :label="v" :value="k" />
              </ElSelect>
            </div>
          </div>
        </div>
      </div>
    </div>-->

    <ElDialog
      v-model="dialogObj.avatar"
      width="435px"
      append-to-body
      :title="$t('user_Center_shangChuanTouXiang')"
      :close-on-click-modal="false"
    >
      <div class="text-center">
        <UploadFile :upload="upload" accept="image/*">
          <img v-if="avatar" :src="avatar" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon cursor-pointer"
            ><el-icon-plus
          /></el-icon>
          <div class="my-4 font-color-light">
            {{ $t('user_Center_zhiChiJPG') }}
          </div>
          <!-- <ElButton type="primary">{{
            $t('user_Center_shangChuanTouXiang')
          }}</ElButton> -->
        </UploadFile>
      </div>
      <div class="text-center mt-6">
        <ElButton @click="dialogObj.avatar = false">{{
          $t('public_button_cancel')
        }}</ElButton>
        <ElButton
          type="primary"
          :disabled="avatarDisabled()"
          auto-loading
          @click="avatarConfirm(arguments[0])"
          >{{ $t('public_button_upload') }}
        </ElButton>
      </div>
    </ElDialog>
    <!--  修改密码  -->
    <ElDialog
      v-model="dialogObj.password"
      width="435px"
      append-to-body
      :title="$t('operation_log_List_xiuGaiMiMa')"
      label-width="120px"
      :close-on-click-modal="false"
    >
      <ElForm
        :model="passwordForm"
        label-width="120px"
        label-position="top"
        @submit.native.prevent
      >
        <!--优先使用手机验证-->
        <template v-if="showPhone">
          <ElFormItem
            prop="telephone"
            :label="$t('user_Center_dangQianShouJi')"
          >
            <ElInput
              v-model="passwordForm.telephone"
              :placeholder="$t('user_Center_qingShuRuDangQian')"
              maxlength="50"
              disabled
            >
              <template #prepend>
                <el-select
                  v-model="passwordForm.countryCode"
                  disabled
                  style="width: 110px"
                  filterable
                >
                  <el-option
                    v-for="item in countryCode"
                    :key="item.dial_code"
                    :label="`+ ${item.dial_code}`"
                    :value="item.dial_code"
                  >
                    <span style="float: left">{{ `+ ${item.dial_code}` }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                      >{{ item.name }}</span
                    ></el-option
                  >
                </el-select>
              </template>
            </ElInput>
          </ElFormItem>
          <ElFormItem
            prop="code"
            :label="$t('user_Center_shouJiYanZhengMa')"
            class="inline-form-item"
          >
            <div class="flex align-center gap-4">
              <ElInput
                v-model="passwordForm.code"
                :placeholder="$t('user_Center_qingShuRuShouJi')"
                maxlength="50"
              />
              <VerificationCode
                :request-options="
                  getCodeOptions(passwordForm.telephone, 'RESET_PASSWORD')
                "
                :disabled="!passwordForm.telephone"
                :style="{ width: '180px', textAlign: 'center' }"
                text
              />
            </div>
          </ElFormItem>
        </template>
        <template v-else>
          <ElFormItem prop="email" :label="$t('user_Center_youXiang')">
            <ElInput
              v-model="emailForm.email"
              disabled
              :placeholder="$t('user_Center_qingShuRuYouXiang')"
              maxlength="50"
            />
          </ElFormItem>
          <ElFormItem
            prop="emailCode"
            :label="$t('user_Center_dangQianYouXiangYan')"
            class="inline-form-item"
          >
            <ElInput
              v-model="passwordForm.emailCode"
              :placeholder="$t('user_Center_qingShuRuYanZheng')"
              maxlength="50"
            />
            <VerificationCode
              :request-options="
                getCodeOptions(emailForm.email, 'RESET_PASSWORD', 'email')
              "
              :disabled="!emailForm.email"
              :style="{ width: '180px', textAlign: 'center' }"
              class="ml-6"
              text
            />
          </ElFormItem>
        </template>

        <ElFormItem prop="newPassword" :label="$t('user_Center_xinMiMa')">
          <ElInput
            v-model="passwordForm.newPassword"
            :placeholder="$t('user_Center_qingShuRuXinMi')"
            maxlength="50"
            show-password
            onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')"
          />
        </ElFormItem>
        <ElFormItem
          prop="newAgainPassword"
          :label="$t('user_Center_queRenMiMa')"
        >
          <ElInput
            v-model="passwordForm.newAgainPassword"
            :placeholder="$t('user_Center_qingShuRuXinMi')"
            maxlength="50"
            show-password
            onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')"
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogObj.password = false">{{
            $t('public_button_cancel')
          }}</ElButton>
          <ElButton
            type="primary"
            auto-loading
            @click="passwordConfirm(arguments[0])"
            >{{ $t('public_button_confirm') }}</ElButton
          >
        </span>
      </template>
    </ElDialog>
    <!-- 绑定手机号 -->
    <ElDialog
      v-model="dialogObj.bindPhone"
      width="435px"
      append-to-body
      :title="$t('operation_log_List_bangDingShouJiHao')"
      :close-on-click-modal="false"
    >
      <ElForm :model="phoneForm" label-width="120px" @submit.prevent>
        <ElFormItem prop="current" :label="$t('user_Center_dangQianShouJi')">
          <ElInput
            v-model="phoneForm.current"
            :placeholder="$t('user_Center_qingShuRuDangQian')"
            maxlength="50"
          >
            <template #prepend>
              <el-select
                v-model="phoneForm.countryCode"
                style="width: 110px"
                filterable
              >
                <el-option
                  v-for="item in countryCode"
                  :label="`+ ${item.dial_code}`"
                  :value="item.dial_code"
                >
                  <span style="float: left">{{ `+ ${item.dial_code}` }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
                    item.name
                  }}</span></el-option
                >
              </el-select>
            </template>
          </ElInput>
        </ElFormItem>
        <ElFormItem
          prop="newPassword"
          :label="$t('user_Center_yanZhengMa')"
          class="inline-form-item"
        >
          <ElInput
            v-model="phoneForm.oldCode"
            :placeholder="$t('user_Center_qingShuRuShouJi')"
            maxlength="50"
          />
          <VerificationCode
            :request-options="getCodeOptions(phoneForm.current, 'BIND_PHONE')"
            :disabled="!phoneForm.current"
            :style="{ width: '120px', textAlign: 'center' }"
            class="ml-6"
            text
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogObj.bindPhone = false">{{
            $t('public_button_cancel')
          }}</ElButton>
          <ElButton
            type="primary"
            :disabled="!phoneForm.oldCode"
            auto-loading
            @click="bindPhoneConfirm(arguments[0])"
            >{{ $t('public_button_confirm') }}</ElButton
          >
        </span>
      </template>
    </ElDialog>
    <!--  {{$t('operation_log_List_xiuGaiShouJiHao')}}  -->
    <ElDialog
      v-model="dialogObj.editPhone"
      :width="$i18n.locale === 'en' ? '600px' : '500px'"
      append-to-body
      :title="$t('operation_log_List_xiuGaiShouJiHao')"
      :close-on-click-modal="false"
    >
      <ElForm
        :model="phoneForm"
        label-width="120px"
        label-position="top"
        @submit.prevent
      >
        <ElFormItem prop="current" :label="$t('user_Center_dangQianShouJi')">
          <ElInput
            v-model="phoneForm.current"
            :placeholder="$t('user_Center_qingShuRuDangQian')"
            maxlength="50"
            disabled
          >
            <template #prepend>
              <el-select
                v-model="phoneForm.countryCode"
                style="width: 110px"
                filterable
                disabled
              >
                <el-option
                  v-for="(item, i) in countryCode"
                  :key="i"
                  :label="`+ ${item.dial_code}`"
                  :value="item.dial_code"
                >
                  <span style="float: left">{{ `+ ${item.dial_code}` }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
                    item.name
                  }}</span></el-option
                >
              </el-select>
            </template>
          </ElInput>
        </ElFormItem>
        <ElFormItem
          prop="newPassword"
          :label="$t('user_Center_jiuShouJiYanZheng')"
          class="inline-form-item"
        >
          <ElInput
            v-model="phoneForm.oldCode"
            :placeholder="$t('user_Center_qingShuRuJiuShou')"
            maxlength="50"
          />
          <VerificationCode
            :request-options="getCodeOptions(phoneForm.current, 'CHANGE_PHONE')"
            :disabled="!phoneForm.current"
            :style="{ width: '180px', textAlign: 'center' }"
            class="ml-6"
            type="primary"
          />
        </ElFormItem>
        <ElFormItem
          prop="newAgainPassword"
          :label="$t('user_Center_xinShouJi')"
        >
          <ElInput
            v-model="phoneForm.newPhone"
            :placeholder="$t('user_Center_qingShuRuXinShou2')"
            maxlength="50"
          >
            <template #prepend>
              <el-select
                v-model="phoneForm.countryCode"
                style="width: 110px"
                filterable
              >
                <el-option
                  v-for="item in countryCode"
                  :label="`+ ${item.dial_code}`"
                  :value="item.dial_code"
                >
                  <span style="float: left">{{ `+ ${item.dial_code}` }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
                    item.name
                  }}</span></el-option
                >
              </el-select>
            </template>
          </ElInput>
        </ElFormItem>
        <ElFormItem
          prop="newAgainPassword"
          :label="$t('user_Center_xinShouJiYanZheng')"
        >
          <ElInput
            v-model="phoneForm.newCode"
            :placeholder="$t('user_Center_qingShuRuXinShou')"
            maxlength="50"
          />
          <VerificationCode
            :request-options="getCodeOptions(phoneForm.newPhone, 'BIND_PHONE')"
            :disabled="!phoneForm.current"
            :style="{ width: '180px', textAlign: 'center' }"
            class="ml-6"
            type="primary"
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogObj.editPhone = false">{{
            $t('public_button_cancel')
          }}</ElButton>
          <ElButton
            type="primary"
            :disabled="editPhoneDisabled()"
            auto-loading
            @click="editPhoneConfirm(arguments[0])"
            >{{ $t('public_button_confirm') }}</ElButton
          >
        </span>
      </template>
    </ElDialog>
    <!--  {{$t('user_Center_bangDingWeiXin')}}  -->
    <ElDialog
      v-model="dialogObj.bindWx"
      width="435px"
      append-to-body
      :title="$t('user_Center_bangDingWeiXin')"
      :close-on-click-modal="true"
    >
      <div class="text-center">
        <img
          src="../../../public/images/user/bindWx.png"
          alt=""
          style="width: 200px"
        />
        <div class="mt-4 font-color-main">
          {{ $t('user_Center_qingShiYongWeiXin') }}
        </div>
      </div>
    </ElDialog>
    <!--  {{$t('operation_log_List_bangDingYouXiang')}}  -->
    <ElDialog
      v-model="dialogObj.bindEmail"
      width="435px"
      append-to-body
      :title="$t('operation_log_List_bangDingYouXiang')"
      :close-on-click-modal="false"
    >
      <ElForm
        :model="emailForm"
        label-width="120px"
        label-position="top"
        @submit.prevent
      >
        <ElFormItem prop="current" :label="$t('user_Center_youXiang')">
          <ElInput
            v-model="emailForm.email"
            :placeholder="$t('user_Center_qingShuRuYouXiang')"
            maxlength="50"
          />
        </ElFormItem>
        <ElFormItem
          prop="newPassword"
          :label="$t('user_Center_yanZhengMa')"
          class="inline-form-item"
        >
          <div class="flex gap-4 w-100">
            <ElInput
              v-model="emailForm.code"
              :placeholder="$t('user_Center_qingShuRuYanZheng')"
              maxlength="50"
              class="flex-1"
            />
            <VerificationCode
              :request-options="
                getCodeOptions(emailForm.email, 'BIND_EMAIL', 'email')
              "
              :disabled="!emailForm.email"
              text
            />
          </div>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogObj.bindEmail = false">{{
            $t('public_button_cancel')
          }}</ElButton>
          <ElButton
            type="primary"
            :disabled="!emailForm.email || !emailForm.code"
            auto-loading
            @click="bindEmailConfirm(arguments[0])"
            >{{ $t('public_button_confirm') }}</ElButton
          >
        </span>
      </template>
    </ElDialog>
    <!--  {{$t('operation_log_List_xiuGaiYouXiang')}}  -->
    <ElDialog
      v-model="dialogObj.editEmail"
      width="435px"
      append-to-body
      :title="$t('operation_log_List_xiuGaiYouXiang')"
      :close-on-click-modal="false"
    >
      <ElForm
        :model="emailForm"
        label-width="120px"
        label-position="top"
        @submit.prevent
      >
        <ElFormItem prop="email" :label="$t('user_Center_youXiang')">
          <ElInput
            v-model="emailForm.email"
            disabled
            :placeholder="$t('user_Center_qingShuRuYouXiang')"
            maxlength="50"
          />
        </ElFormItem>
        <ElFormItem
          prop="code"
          :label="$t('user_Center_dangQianYouXiangYan')"
          class="inline-form-item"
        >
          <div class="flex gap-4 w-100">
            <ElInput
              v-model="emailForm.code"
              :placeholder="$t('user_Center_qingShuRuYanZheng')"
              maxlength="50"
              class="flex-1"
            />
            <VerificationCode
              :request-options="
                getCodeOptions(emailForm.email, 'CHANGE_EMAIL', 'email')
              "
              :disabled="!emailForm.email"
              text
            />
          </div>
        </ElFormItem>
        <ElFormItem prop="newEmail" :label="$t('user_Center_xinYouXiang')">
          <ElInput
            v-model="emailForm.newEmail"
            :placeholder="$t('user_Center_qingShuRuXinYou')"
            maxlength="50"
          />
        </ElFormItem>
        <ElFormItem
          prop="newCode"
          :label="$t('user_Center_xinYouXiangYanZheng')"
          class="inline-form-item"
        >
          <div class="flex gap-4 w-100">
            <ElInput
              v-model="emailForm.newCode"
              :placeholder="$t('user_Center_qingShuRuYanZheng')"
              maxlength="50"
            />
            <VerificationCode
              :request-options="
                getCodeOptions(emailForm.newEmail, 'BIND_EMAIL', 'email')
              "
              :disabled="!emailForm.newEmail"
              text
            />
          </div>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogObj.editEmail = false">{{
            $t('public_button_cancel')
          }}</ElButton>
          <ElButton
            type="primary"
            :disabled="editEmailDisabled()"
            auto-loading
            @click="editEmailConfirm(arguments[0])"
            >{{ $t('public_button_confirm') }}</ElButton
          >
        </span>
      </template>
    </ElDialog>
    <!--  订阅记录  -->
    <ElDialog
      v-model="recordData.visible"
      width="618px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="mt-n11 mx-n2 mb-4 p-4 bg-color-normal text-center rounded-4">
        <div class="font-color-dark text-center fs-5">
          {{ recordData.content }}
        </div>
        <p class="mt-4 font-color-dark fs-1 text-center">
          {{ recordData.price }}
        </p>
        <p class="mt-4 font-color-sslight text-center">
          {{ recordData.statusLabel }}
        </p>
      </div>
      <div
        v-for="(item, index) in recordData.items"
        :key="index"
        class="flex justify-content-between mb-2"
      >
        <span class="font-color-light">{{ item.label }}</span>
        <span class="font-color-dark">{{ item.value }}</span>
      </div>
    </ElDialog>

    <ElDialog
      v-model="dialogObj.firstName"
      width="435px"
      append-to-body
      :title="$t('public_button_revise')"
      :close-on-click-modal="false"
    >
      <ElForm
        :model="nameForm"
        label-width="120px"
        label-position="top"
        @submit.native.prevent
      >
        <ElFormItem prop="email" label="First Name">
          <ElInput v-model="nameForm.firstName" maxlength="50" />
        </ElFormItem>
        <ElFormItem prop="email" label="Last Name">
          <ElInput v-model="nameForm.lastName" maxlength="50" />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogObj.firstName = false">{{
            $t('public_button_cancel')
          }}</ElButton>
          <ElButton
            type="primary"
            auto-loading
            @click="updateFirstName(arguments[0])"
            >{{ $t('public_button_confirm') }}</ElButton
          >
        </span>
      </template>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
.user-item {
  display: flex;
  align-items: center;
}

.user-item__label_en {
  width: 180px !important;
}

.enterprise-item {
  display: flex;
  align-items: center;
  line-height: 34px;
}

.expried {
  padding: 2px 4px;
  color: map.get($color, warning);
  border-radius: 4px;
  border: 1px solid map.get($color, warning);
}

.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;

  &:hover {
    color: var(--el-color-primary);
    border-color: currentColor;
  }
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

:deep(.el-divider--horizontal) {
  margin: 8px 0 16px 0;
}

:deep(.el-form-item__label) {
  text-align: left;
}

:deep(.el-form-item__content) {
  display: flex;
}

:deep(.inline-input) {
  .inline-input-body {
    justify-content: space-between;
  }
}

.click-style {
  padding-left: 10px;
  font-style: normal;
  color: map.get($color, primary);
  font-weight: normal;
  cursor: pointer;
}

.access-key__desc {
  background: #f2f2f2;
}
</style>

<style lang="scss" scoped>
.user-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: end;
  gap: 16px;
}
.avtar-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 100%;
  border: 1px solid #e5e6eb;
}

.user-item__label {
  color: #86909c;
  line-height: 22px;
  margin-bottom: 4px;
}

.user-item__value {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>
