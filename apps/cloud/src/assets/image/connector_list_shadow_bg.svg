<svg width="92" height="260" viewBox="0 0 92 260" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_21262_9160)">
<rect width="92" height="260" rx="16" fill="#F1F2F4"/>
<rect x="0.5" y="0.5" width="91" height="259" rx="15.5" stroke="url(#paint0_linear_21262_9160)"/>
</g>
<defs>
<filter id="filter0_ii_21262_9160" x="-3" y="-3" width="98" height="266" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3" dy="-3"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_21262_9160"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="3"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.533333 0 0 0 0 0.588235 0 0 0 0 0.639216 0 0 0 0.58 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_21262_9160" result="effect2_innerShadow_21262_9160"/>
</filter>
<linearGradient id="paint0_linear_21262_9160" x1="46" y1="0" x2="46" y2="260" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E8E8E8"/>
</linearGradient>
</defs>
</svg>
