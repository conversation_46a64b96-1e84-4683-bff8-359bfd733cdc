/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const ElIconArrowRight: typeof import('@element-plus/icons-vue')['ArrowRight']
  const ElIconCopyDocument: typeof import('@element-plus/icons-vue')['CopyDocument']
  const ElIconRefresh: typeof import('@element-plus/icons-vue')['Refresh']
  const ElIconSearch: typeof import('@element-plus/icons-vue')['Search']
  const ElInput: typeof import('element-plus/es')['ElInput']
  const ElLoadingService: typeof import('element-plus/es')['ElLoadingService']
  const ElMessage: typeof import('element-plus/es')['ElMessage']
  const ElMessageBox: typeof import('element-plus/es')['ElMessageBox']
  const ElOption: typeof import('element-plus/es')['ElOption']
  const ElSelect: typeof import('element-plus/es')['ElSelect']
}
