[INFO ] 2024-09-27 11:55:05.273 - [任务 281] - Start task milestones: 66f5281a3643f36dd46659f8(任务 281) 
[INFO ] 2024-09-27 11:55:05.288 - [任务 281] - Task initialization... 
[INFO ] 2024-09-27 11:55:05.898 - [任务 281] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-09-27 11:55:06.443 - [任务 281] - The engine receives 任务 281 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 11:55:06.613 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] start preload schema,table counts: 1 
[INFO ] 2024-09-27 11:55:06.613 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 11:55:06.623 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] start preload schema,table counts: 1 
[INFO ] 2024-09-27 11:55:06.624 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 11:55:06.630 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] start preload schema,table counts: 3 
[INFO ] 2024-09-27 11:55:08.721 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] start preload schema,table counts: 1 
[INFO ] 2024-09-27 11:55:08.721 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 11:55:08.722 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 11:55:08.722 - [任务 281][主从合并] - Node merge_table_processor(主从合并: d6df3ac4-0696-4cdd-a75f-de1377eaa482) enable batch process 
[INFO ] 2024-09-27 11:55:43.966 - [任务 281][主从合并] - 
Merge lookup relation{
  POLICY(310b9c9c-1c1b-47a9-bda1-ba4aaea895dc)
    ->CLAIM(54a0cef0-fe0c-4328-8d38-ce00b7e84979)
} 
[INFO ] 2024-09-27 11:55:44.053 - [任务 281][test3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 11:55:44.181 - [任务 281][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG 
[INFO ] 2024-09-27 11:55:44.181 - [任务 281][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-09-27 11:55:44.181 - [任务 281][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-09-27 11:55:44.219 - [任务 281][CLAIM] - On the first run, the breakpoint will be initialized 
[ERROR] 2024-09-27 11:55:44.224 - [任务 281][主从合并] - Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG <-- Error Message -->
Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server ************:27018. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed" }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.checkBuildConstructIMap(HazelcastMergeNode.java:778)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initMergeCache(HazelcastMergeNode.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:249)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='', source='tapdata', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.buildConstructIMap(HazelcastMergeNode.java:810)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.checkBuildConstructIMap(HazelcastMergeNode.java:776)
	... 16 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='', source='tapdata', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 19 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='', source='tapdata', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 20 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server ************:27018. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed" }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslStart(SaslAuthenticator.java:130)
	at com.mongodb.internal.connection.SaslAuthenticator.access$100(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:54)
	... 46 more

[INFO ] 2024-09-27 11:55:44.332 - [任务 281][主从合并] - Job suspend in error handle 
[INFO ] 2024-09-27 11:55:44.333 - [任务 281][CLAIM] - batch offset found: {},stream offset found: {"cdcOffset":1727409334,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 11:55:44.357 - [任务 281][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-09-27 11:55:44.357 - [任务 281][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-09-27 11:55:44.357 - [任务 281][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 11:55:44.486 - [任务 281][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1727409334,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 11:55:44.612 - [任务 281] - Node[CLAIM] is waiting for running 
[INFO ] 2024-09-27 11:55:44.846 - [任务 281][POLICY] - Incremental sync starting... 
[INFO ] 2024-09-27 11:55:44.846 - [任务 281][POLICY] - Incremental sync completed 
[INFO ] 2024-09-27 11:55:45.122 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] running status set to false 
[INFO ] 2024-09-27 11:55:45.136 - [任务 281][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 11:55:45.138 - [任务 281][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 11:55:45.138 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] schema data cleaned 
[INFO ] 2024-09-27 11:55:45.138 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] monitor closed 
[INFO ] 2024-09-27 11:55:45.138 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] close complete, cost 18 ms 
[INFO ] 2024-09-27 11:55:45.138 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] running status set to false 
[INFO ] 2024-09-27 11:55:45.139 - [任务 281][CLAIM] - Initial sync started 
[INFO ] 2024-09-27 11:55:45.139 - [任务 281][CLAIM] - Initial sync completed 
[INFO ] 2024-09-27 11:55:45.139 - [任务 281][CLAIM] - Incremental sync starting... 
[INFO ] 2024-09-27 11:55:45.142 - [任务 281][CLAIM] - Incremental sync completed 
[INFO ] 2024-09-27 11:55:45.142 - [任务 281][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 11:55:45.143 - [任务 281][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 11:55:45.144 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] schema data cleaned 
[INFO ] 2024-09-27 11:55:45.144 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] monitor closed 
[INFO ] 2024-09-27 11:55:45.144 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] close complete, cost 6 ms 
[INFO ] 2024-09-27 11:55:45.145 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] running status set to false 
[INFO ] 2024-09-27 11:55:45.145 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] schema data cleaned 
[INFO ] 2024-09-27 11:55:45.145 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] monitor closed 
[INFO ] 2024-09-27 11:55:45.146 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] close complete, cost 1 ms 
[INFO ] 2024-09-27 11:55:45.146 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] running status set to false 
[INFO ] 2024-09-27 11:55:45.146 - [任务 281][test3] - PDK connector node stopped: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 11:55:45.146 - [任务 281][test3] - PDK connector node released: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 11:55:45.147 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] schema data cleaned 
[INFO ] 2024-09-27 11:55:45.149 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] monitor closed 
[INFO ] 2024-09-27 11:55:45.353 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] close complete, cost 3 ms 
[INFO ] 2024-09-27 11:55:46.487 - [任务 281] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 11:55:46.609 - [任务 281] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@336133cd 
[INFO ] 2024-09-27 11:55:46.609 - [任务 281] - Stop task milestones: 66f5281a3643f36dd46659f8(任务 281)  
[INFO ] 2024-09-27 11:55:46.694 - [任务 281] - Stopped task aspect(s) 
[INFO ] 2024-09-27 11:55:46.695 - [任务 281] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 11:55:46.795 - [任务 281] - Remove memory task client succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
[INFO ] 2024-09-27 11:55:46.795 - [任务 281] - Destroy memory task client cache succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
[INFO ] 2024-09-27 12:03:03.254 - [任务 281] - Start task milestones: 66f5281a3643f36dd46659f8(任务 281) 
[INFO ] 2024-09-27 12:03:03.465 - [任务 281] - Task initialization... 
[INFO ] 2024-09-27 12:03:03.872 - [任务 281] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-09-27 12:03:04.685 - [任务 281] - The engine receives 任务 281 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 12:03:04.745 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] start preload schema,table counts: 1 
[INFO ] 2024-09-27 12:03:04.745 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] start preload schema,table counts: 1 
[INFO ] 2024-09-27 12:03:04.745 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 12:03:04.746 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 12:03:04.955 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] start preload schema,table counts: 1 
[INFO ] 2024-09-27 12:03:04.958 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 12:03:04.968 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] start preload schema,table counts: 3 
[INFO ] 2024-09-27 12:03:04.968 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 12:03:05.052 - [任务 281][主从合并] - Node merge_table_processor(主从合并: d6df3ac4-0696-4cdd-a75f-de1377eaa482) enable batch process 
[INFO ] 2024-09-27 12:03:05.053 - [任务 281][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-09-27 12:03:05.053 - [任务 281][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-09-27 12:03:05.053 - [任务 281][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 12:03:05.070 - [任务 281][主从合并] - 
Merge lookup relation{
  POLICY(310b9c9c-1c1b-47a9-bda1-ba4aaea895dc)
    ->CLAIM(54a0cef0-fe0c-4328-8d38-ce00b7e84979)
} 
[INFO ] 2024-09-27 12:03:05.157 - [任务 281][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG 
[INFO ] 2024-09-27 12:03:05.157 - [任务 281][CLAIM] - batch offset found: {},stream offset found: {"cdcOffset":1727409778,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 12:03:05.269 - [任务 281][test3] - Write batch size: 100, max wait ms per batch: 500 
[ERROR] 2024-09-27 12:03:05.270 - [任务 281][主从合并] - Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG <-- Error Message -->
Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server ************:27018. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed" }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
Cache name: HazelcastMergeNode_CLAIM_54a0cef0-fe0c-4328-8d38-ce00b7e84979__TPORIG
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.checkBuildConstructIMap(HazelcastMergeNode.java:778)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initMergeCache(HazelcastMergeNode.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:249)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='', source='tapdata', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.buildConstructIMap(HazelcastMergeNode.java:810)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.checkBuildConstructIMap(HazelcastMergeNode.java:776)
	... 16 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='', source='tapdata', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 19 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='', source='tapdata', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 20 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server ************:27018. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed" }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslStart(SaslAuthenticator.java:130)
	at com.mongodb.internal.connection.SaslAuthenticator.access$100(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:54)
	... 46 more

[INFO ] 2024-09-27 12:03:05.346 - [任务 281][主从合并] - Job suspend in error handle 
[INFO ] 2024-09-27 12:03:05.346 - [任务 281][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-09-27 12:03:05.347 - [任务 281][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-09-27 12:03:05.347 - [任务 281][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 12:03:05.553 - [任务 281][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1727409778,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 12:03:05.759 - [任务 281] - Node[CLAIM] is waiting for running 
[INFO ] 2024-09-27 12:03:05.913 - [任务 281][POLICY] - Initial sync started 
[INFO ] 2024-09-27 12:03:05.928 - [任务 281][POLICY] - Starting batch read, table name: POLICY 
[INFO ] 2024-09-27 12:03:05.928 - [任务 281][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-09-27 12:03:06.018 - [任务 281] - [DynamicAdjustMemory] Sampling table POLICY's data, all data size: 42.33KB, row count: 20, single row data size: 2.12KB 
[INFO ] 2024-09-27 12:03:06.019 - [任务 281][POLICY] - Query table 'POLICY' counts: 601 
[INFO ] 2024-09-27 12:03:06.083 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] running status set to false 
[INFO ] 2024-09-27 12:03:06.083 - [任务 281][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 12:03:06.091 - [任务 281][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 12:03:06.091 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] schema data cleaned 
[INFO ] 2024-09-27 12:03:06.105 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] monitor closed 
[INFO ] 2024-09-27 12:03:06.106 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] close complete, cost 29 ms 
[INFO ] 2024-09-27 12:03:06.115 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] running status set to false 
[INFO ] 2024-09-27 12:03:06.116 - [任务 281][CLAIM] - Initial sync started 
[INFO ] 2024-09-27 12:03:06.116 - [任务 281][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 12:03:06.122 - [任务 281][CLAIM] - Initial sync completed 
[INFO ] 2024-09-27 12:03:06.123 - [任务 281][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 12:03:06.137 - [任务 281][CLAIM] - Incremental sync starting... 
[INFO ] 2024-09-27 12:03:06.138 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] schema data cleaned 
[INFO ] 2024-09-27 12:03:06.138 - [任务 281][CLAIM] - Incremental sync completed 
[INFO ] 2024-09-27 12:03:06.138 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] monitor closed 
[INFO ] 2024-09-27 12:03:06.138 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] close complete, cost 27 ms 
[INFO ] 2024-09-27 12:03:06.139 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] running status set to false 
[INFO ] 2024-09-27 12:03:06.139 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] schema data cleaned 
[INFO ] 2024-09-27 12:03:06.139 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] monitor closed 
[INFO ] 2024-09-27 12:03:06.139 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] close complete, cost 2 ms 
[INFO ] 2024-09-27 12:03:06.139 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] running status set to false 
[INFO ] 2024-09-27 12:03:06.139 - [任务 281][test3] - PDK connector node stopped: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 12:03:06.140 - [任务 281][test3] - PDK connector node released: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 12:03:06.140 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] schema data cleaned 
[INFO ] 2024-09-27 12:03:06.140 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] monitor closed 
[INFO ] 2024-09-27 12:03:06.140 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] close complete, cost 3 ms 
[INFO ] 2024-09-27 12:03:06.245 - [任务 281][POLICY] - Initial sync completed 
[INFO ] 2024-09-27 12:03:06.344 - [任务 281][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.RuntimeException: java.lang.InterruptedException 
[ERROR] 2024-09-27 12:03:06.345 - [任务 281][POLICY] - java.lang.RuntimeException: java.lang.RuntimeException: java.lang.InterruptedException <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: java.lang.InterruptedException

<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.locks.AbstractQueuedSynchronizer.doAcquireSharedNanos(AbstractQueuedSynchronizer.java:1041)
	java.util.concurrent.locks.AbstractQueuedSynchronizer.tryAcquireSharedNanos(AbstractQueuedSynchronizer.java:1332)
	java.util.concurrent.CountDownLatch.await(CountDownLatch.java:277)
	io.tapdata.mongodb.batch.MongoBatchReader.batchReadCursor(MongoBatchReader.java:198)
	io.tapdata.mongodb.batch.MongoBatchReader.batchReadCollection(MongoBatchReader.java:117)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.mongodb.MongodbConnector.errorHandle(MongodbConnector.java:1759)
	at io.tapdata.mongodb.MongodbConnector.lambda$batchRead$28(MongodbConnector.java:1523)
	at io.tapdata.mongodb.batch.MongoBatchReader.doException(MongoBatchReader.java:241)
	at io.tapdata.mongodb.batch.MongoBatchReader.batchReadCursor(MongoBatchReader.java:203)
	at io.tapdata.mongodb.batch.MongoBatchReader.batchReadCollection(MongoBatchReader.java:117)
	at io.tapdata.mongodb.MongodbConnector.batchRead(MongodbConnector.java:1525)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:480)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.doAcquireSharedNanos(AbstractQueuedSynchronizer.java:1041)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.tryAcquireSharedNanos(AbstractQueuedSynchronizer.java:1332)
	at java.util.concurrent.CountDownLatch.await(CountDownLatch.java:277)
	at io.tapdata.mongodb.batch.MongoBatchReader.batchReadCursor(MongoBatchReader.java:198)
	... 25 more

[INFO ] 2024-09-27 12:03:08.122 - [任务 281] - Task [任务 281] cannot retry, reason: Task retry service not start 
[INFO ] 2024-09-27 12:03:08.138 - [任务 281] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 12:03:08.138 - [任务 281] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7e7b96fb 
[INFO ] 2024-09-27 12:03:08.256 - [任务 281] - Stop task milestones: 66f5281a3643f36dd46659f8(任务 281)  
[INFO ] 2024-09-27 12:03:08.423 - [任务 281] - Stopped task aspect(s) 
[INFO ] 2024-09-27 12:03:08.548 - [任务 281] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 12:03:08.550 - [任务 281] - Remove memory task client succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
[INFO ] 2024-09-27 12:03:08.550 - [任务 281] - Destroy memory task client cache succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
[INFO ] 2024-09-27 12:06:33.172 - [任务 281] - Task initialization... 
[INFO ] 2024-09-27 12:06:33.382 - [任务 281] - Start task milestones: 66f5281a3643f36dd46659f8(任务 281) 
[INFO ] 2024-09-27 12:06:41.732 - [任务 281] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-09-27 12:06:43.245 - [任务 281] - The engine receives 任务 281 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 12:06:43.809 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] start preload schema,table counts: 3 
[INFO ] 2024-09-27 12:06:43.814 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] start preload schema,table counts: 1 
[INFO ] 2024-09-27 12:06:43.815 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] preload schema finished, cost 2 ms 
[INFO ] 2024-09-27 12:06:44.879 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 12:06:44.889 - [任务 281][主从合并] - Node merge_table_processor(主从合并: d6df3ac4-0696-4cdd-a75f-de1377eaa482) enable batch process 
[INFO ] 2024-09-27 12:06:47.695 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] start preload schema,table counts: 1 
[INFO ] 2024-09-27 12:06:47.718 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] start preload schema,table counts: 1 
[INFO ] 2024-09-27 12:06:47.719 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 12:06:47.719 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] preload schema finished, cost 13 ms 
[INFO ] 2024-09-27 12:07:04.595 - [任务 281][主从合并] - 
Merge lookup relation{
  POLICY(310b9c9c-1c1b-47a9-bda1-ba4aaea895dc)
    ->CLAIM(54a0cef0-fe0c-4328-8d38-ce00b7e84979)
} 
[INFO ] 2024-09-27 12:07:04.796 - [任务 281][主从合并] - Create merge cache imap name: -389107495, external storage: ExternalStorageDto[name='Target MongoDB External Storage', type='mongodb', uri='mongodb://************:27018/tapdata?directConnection=true', table='null', ttlDay=0] 
[INFO ] 2024-09-27 12:07:04.987 - [任务 281][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-09-27 12:07:04.988 - [任务 281][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-09-27 12:07:04.989 - [任务 281][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 12:07:05.011 - [任务 281][test3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 12:07:05.158 - [任务 281][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-09-27 12:07:05.159 - [任务 281][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-09-27 12:07:05.159 - [任务 281][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 12:07:05.165 - [任务 281][CLAIM] - batch offset found: {},stream offset found: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 12:07:05.203 - [任务 281][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-09-27 12:07:05.204 - [任务 281][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-09-27 12:07:05.410 - [任务 281][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 12:07:05.605 - [任务 281] - Node[CLAIM] is waiting for running 
[INFO ] 2024-09-27 12:07:05.606 - [任务 281][POLICY] - Initial sync started 
[INFO ] 2024-09-27 12:07:05.606 - [任务 281][POLICY] - Starting batch read, table name: POLICY 
[INFO ] 2024-09-27 12:07:05.620 - [任务 281][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-09-27 12:07:05.715 - [任务 281] - [DynamicAdjustMemory] Sampling table POLICY's data, all data size: 42.31KB, row count: 20, single row data size: 2.12KB 
[INFO ] 2024-09-27 12:07:05.724 - [任务 281][POLICY] - Query table 'POLICY' counts: 601 
[INFO ] 2024-09-27 12:07:05.725 - [任务 281][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 12:07:05.934 - [任务 281][POLICY] - Initial sync completed 
[INFO ] 2024-09-27 12:07:06.944 - [任务 281][test3] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@5cdceb5c: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"CLAIM.CLAIM_ID"}]}],"tableId":"d6df3ac4-0696-4cdd-a75f-de1377eaa482","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-09-27 12:07:07.042 - [任务 281][test3] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@5cdceb5c: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"CLAIM.CLAIM_ID"}]}],"tableId":"d6df3ac4-0696-4cdd-a75f-de1377eaa482","type":101}) 
[INFO ] 2024-09-27 12:07:07.791 - [任务 281] - Node[POLICY] finish, notify next layer to run 
[INFO ] 2024-09-27 12:07:07.796 - [任务 281][CLAIM] - Initial sync started 
[INFO ] 2024-09-27 12:07:07.798 - [任务 281] - Next layer have been notified: [null] 
[INFO ] 2024-09-27 12:07:07.807 - [任务 281][CLAIM] - Starting batch read, table name: CLAIM 
[INFO ] 2024-09-27 12:07:07.808 - [任务 281][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-09-27 12:07:07.818 - [任务 281][CLAIM] - Query table 'CLAIM' counts: 1101 
[INFO ] 2024-09-27 12:07:07.841 - [任务 281] - [DynamicAdjustMemory] Sampling table CLAIM's data, all data size: 268.08KB, row count: 100, single row data size: 2.68KB 
[INFO ] 2024-09-27 12:07:07.842 - [任务 281][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 12:07:07.842 - [任务 281][CLAIM] - Initial sync completed 
[INFO ] 2024-09-27 12:07:10.754 - [任务 281][POLICY] - Incremental sync starting... 
[INFO ] 2024-09-27 12:07:10.757 - [任务 281][POLICY] - Initial sync completed 
[INFO ] 2024-09-27 12:07:10.763 - [任务 281][POLICY] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 12:07:10.854 - [任务 281][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-09-27 12:07:10.854 - [任务 281][CLAIM] - Incremental sync starting... 
[INFO ] 2024-09-27 12:07:10.855 - [任务 281][CLAIM] - Initial sync completed 
[INFO ] 2024-09-27 12:07:10.856 - [任务 281][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 12:07:11.061 - [任务 281][CLAIM] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[WARN ] 2024-09-27 14:07:52.206 - [任务 281][test3] - Save to snapshot failed, collection: Task/syncProgress/66f5281a3643f36dd46659f8, object: {310b9c9c-1c1b-47a9-bda1-ba4aaea895dc,6eae59a1-67d8-403e-bd45-f631e5102fec=SyncProgress{eventSerialNo=9, syncStage='CDC', batchOffset='{POLICY={batch_read_connector_status=OVER}}', streamOffset='{cdcOffset=1727410019, opLogOffset=null, mongo_cdc_offset_flag=true}'}, 54a0cef0-fe0c-4328-8d38-ce00b7e84979,6eae59a1-67d8-403e-bd45-f631e5102fec=SyncProgress{eventSerialNo=6, syncStage='CDC', batchOffset='{CLAIM={batch_read_connector_status=OVER}}', streamOffset='{cdcOffset=1727410019, opLogOffset=null, mongo_cdc_offset_flag=true}'}}, errors: RestException{uri='https://cloud.tapdata.net/console/v3/tm/api/Task/syncProgress/66f5281a3643f36dd46659f8?access_token=3afefa7aaa82484fa99a7a9728577d263c1c1a1664d04995840e37eb31839dd2', method='POST', param={["310b9c9c-1c1b-47a9-bda1-ba4aaea895dc","6eae59a1-67d8-403e-bd45-f631e5102fec"]={"offset":null,"eventTime":null,"eventSerialNo":9,"sourceTime":1727410025158,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcAEUAAZQT0xJQ1kBZAARamF2YS51dGlsLkhhc2hNYXABFAAb\r\nYmF0Y2hfcmVhZF9jb25uZWN0b3Jfc3RhdHVzARQABE9WRVKoqA==\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000=Í±\u000E0\u0014@Ñ)Ä¥ãk¤$Æ  ¶ð6i#\b> \u0005ùz\\oNr+-\bù.r¬bw=OX®÷(¨\rD73ÑâqÂ.yYØ4À¡\b³æ\u001A\"P1È\u0014ñ'o|\u000F²\u0014¤\u000E\\IÖåaÒ\u0016\u0001wH¸b=\u001AçH\f'\u0006\"O¾ÿëÖK2SJÜ±ªËU¦ß+ßëÎÃ\u0004\u0015\b]´$G«ÕÎ@ÜûÞ\u000FÕÍAO¶\u0000\u0000\u0000","type":"NORMAL"}, ["54a0cef0-fe0c-4328-8d38-ce00b7e84979","6eae59a1-67d8-403e-bd45-f631e5102fec"]={"offset":null,"eventTime":null,"eventSerialNo":6,"sourceTime":1727410024981,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcAEUAAVDTEFJTQFkABFqYXZhLnV0aWwuSGFzaE1hcAEUABti\r\nYXRjaF9yZWFkX2Nvbm5lY3Rvcl9zdGF0dXMBFAAET1ZFUqio\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000=Í±\u000E0\u0014@Ñ)Ä¥ãk¤$Æ  ¶ð6i#\b> \u0005ùz\\oNr+-\bù.r¬bw=OX®÷(¨\rD73ÑâqÂ.yYØ4À¡\b³æ\u001A\"P1È\u0014ñ'o|\u000F²\u0014¤\u000E\\IÖåaÒ\u0016\u0001wH¸b=\u001AçH\f'\u0006\"O¾ÿëÖK2SJÜ±ªËU¦ß+ßëÎÃ\u0004\u0015\b]´$G«ÕÎ@ÜûÞ\u000FÕÍAO¶\u0000\u0000\u0000","type":"NORMAL"}}, code='503', data=null, reqId=null}: null 
[INFO ] 2024-09-27 15:30:26.281 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] running status set to false 
[INFO ] 2024-09-27 18:59:33.567 - [任务 281] - Start task milestones: 66f5281a3643f36dd46659f8(任务 281) 
[INFO ] 2024-09-27 18:59:33.570 - [任务 281] - Task initialization... 
[INFO ] 2024-09-27 18:59:35.012 - [任务 281] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-09-27 18:59:35.721 - [任务 281] - The engine receives 任务 281 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 18:59:36.507 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] start preload schema,table counts: 1 
[INFO ] 2024-09-27 18:59:36.511 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] start preload schema,table counts: 1 
[INFO ] 2024-09-27 18:59:36.511 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] start preload schema,table counts: 3 
[INFO ] 2024-09-27 18:59:36.511 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] start preload schema,table counts: 1 
[INFO ] 2024-09-27 18:59:36.512 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 18:59:36.512 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 18:59:36.512 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 18:59:36.513 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 18:59:36.514 - [任务 281][主从合并] - Node merge_table_processor(主从合并: d6df3ac4-0696-4cdd-a75f-de1377eaa482) enable batch process 
[INFO ] 2024-09-27 18:59:36.647 - [任务 281][主从合并] - 
Merge lookup relation{
  POLICY(310b9c9c-1c1b-47a9-bda1-ba4aaea895dc)
    ->CLAIM(54a0cef0-fe0c-4328-8d38-ce00b7e84979)
} 
[INFO ] 2024-09-27 18:59:36.949 - [任务 281][主从合并] - Create merge cache imap name: -389107495, external storage: ExternalStorageDto[name='Target MongoDB External Storage', type='mongodb', uri='mongodb://************:27018/tapdata?directConnection=true', table='null', ttlDay=0] 
[INFO ] 2024-09-27 18:59:36.950 - [任务 281][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-09-27 18:59:36.950 - [任务 281][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-09-27 18:59:37.287 - [任务 281][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-09-27 18:59:37.288 - [任务 281][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-09-27 18:59:37.338 - [任务 281][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 18:59:37.339 - [任务 281][CLAIM] - batch offset found: {"CLAIM":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 18:59:37.496 - [任务 281][test3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 18:59:37.593 - [任务 281][CLAIM] - Incremental sync starting... 
[INFO ] 2024-09-27 18:59:37.594 - [任务 281][CLAIM] - Initial sync completed 
[INFO ] 2024-09-27 18:59:37.607 - [任务 281][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[WARN ] 2024-09-27 18:59:37.733 - [任务 281][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is {"ok": 0.0, "errmsg": "Authentication failed.", "code": 18, "codeName": "AuthenticationFailed", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1727434773, "i": 8}}, "signature": {"hash": {"$binary": {"base64": "schllf3tgnvj0llUBP24YFJKqWo=", "subType": "00"}}, "keyId": 7376103549123428362}}, "operationTime": {"$timestamp": {"t": 1727434773, "i": 8}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:102)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:49)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-27 18:59:37.772 - [任务 281][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-09-27 18:59:37.773 - [任务 281][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-09-27 18:59:37.773 - [任务 281][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 18:59:37.779 - [任务 281][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 18:59:38.040 - [任务 281][POLICY] - Incremental sync starting... 
[INFO ] 2024-09-27 18:59:38.040 - [任务 281][POLICY] - Initial sync completed 
[INFO ] 2024-09-27 18:59:38.040 - [任务 281][POLICY] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[WARN ] 2024-09-27 18:59:38.087 - [任务 281][POLICY] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is {"ok": 0.0, "errmsg": "Authentication failed.", "code": 18, "codeName": "AuthenticationFailed", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1727434773, "i": 8}}, "signature": {"hash": {"$binary": {"base64": "schllf3tgnvj0llUBP24YFJKqWo=", "subType": "00"}}, "keyId": 7376103549123428362}}, "operationTime": {"$timestamp": {"t": 1727434773, "i": 8}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:102)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:49)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-27 18:59:38.854 - [任务 281][test3] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@41f2beb5: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"CLAIM.CLAIM_ID"}]}],"tableId":"d6df3ac4-0696-4cdd-a75f-de1377eaa482","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-09-27 18:59:38.854 - [任务 281][test3] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@41f2beb5: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"CLAIM.CLAIM_ID"}]}],"tableId":"d6df3ac4-0696-4cdd-a75f-de1377eaa482","type":101}) 
[INFO ] 2024-09-27 19:00:00.526 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] running status set to false 
[INFO ] 2024-09-27 19:00:00.534 - [任务 281][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 19:00:00.538 - [任务 281][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 19:00:00.539 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] schema data cleaned 
[INFO ] 2024-09-27 19:00:00.539 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] monitor closed 
[INFO ] 2024-09-27 19:00:00.548 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] close complete, cost 46 ms 
[INFO ] 2024-09-27 19:00:00.552 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] running status set to false 
[INFO ] 2024-09-27 19:00:00.553 - [任务 281][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 19:00:00.555 - [任务 281][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 19:00:00.555 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] schema data cleaned 
[INFO ] 2024-09-27 19:00:00.557 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] monitor closed 
[INFO ] 2024-09-27 19:00:00.558 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] close complete, cost 15 ms 
[INFO ] 2024-09-27 19:00:00.558 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] running status set to false 
[INFO ] 2024-09-27 19:00:00.566 - [任务 281][主从合并] - Destroy merge cache resource: -389107495 
[INFO ] 2024-09-27 19:00:00.567 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] schema data cleaned 
[INFO ] 2024-09-27 19:00:00.567 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] monitor closed 
[INFO ] 2024-09-27 19:00:00.568 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] close complete, cost 11 ms 
[INFO ] 2024-09-27 19:00:00.570 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] running status set to false 
[INFO ] 2024-09-27 19:00:00.577 - [任务 281][test3] - PDK connector node stopped: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 19:00:00.577 - [任务 281][test3] - PDK connector node released: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 19:00:00.577 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] schema data cleaned 
[INFO ] 2024-09-27 19:00:00.582 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] monitor closed 
[INFO ] 2024-09-27 19:00:00.620 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] close complete, cost 12 ms 
[INFO ] 2024-09-27 19:00:00.620 - [任务 281][CLAIM] - Incremental sync completed 
[INFO ] 2024-09-27 19:00:00.629 - [任务 281][CLAIM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available 
[INFO ] 2024-09-27 19:00:00.629 - [任务 281][POLICY] - Incremental sync completed 
[INFO ] 2024-09-27 19:00:00.714 - [任务 281][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available 
[ERROR] 2024-09-27 19:00:00.717 - [任务 281][POLICY] - com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available <-- Error Message -->
com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available
	com.mongodb.internal.connection.ConcurrentPool.poolClosedException(ConcurrentPool.java:297)
	com.mongodb.internal.connection.DefaultConnectionPool$StateAndGeneration.throwIfClosedOrPaused(DefaultConnectionPool.java:1530)
	com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:174)
	com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:166)
	com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:97)
	...

<-- Full Stack Trace -->
com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available
	at com.mongodb.internal.connection.ConcurrentPool.poolClosedException(ConcurrentPool.java:297)
	at com.mongodb.internal.connection.DefaultConnectionPool$StateAndGeneration.throwIfClosedOrPaused(DefaultConnectionPool.java:1530)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:174)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:166)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:97)
	at com.mongodb.internal.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:173)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:193)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:375)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.CommandReadOperation.execute(CommandReadOperation.java:48)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.MongoDatabaseImpl.executeCommand(MongoDatabaseImpl.java:196)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:165)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:160)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:150)
	at io.tapdata.mongodb.MongodbUtil.getVersion(MongodbUtil.java:60)
	at io.tapdata.mongodb.MongodbConnector.createStreamReader(MongodbConnector.java:1629)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1563)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[ERROR] 2024-09-27 19:00:00.719 - [任务 281][CLAIM] - com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available <-- Error Message -->
com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available
	com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:91)
	com.mongodb.internal.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:173)
	com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:193)
	com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:375)
	com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	...

<-- Full Stack Trace -->
com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoServerUnavailableException: The server at localhost:27017 is no longer available
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:91)
	at com.mongodb.internal.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:173)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:193)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:375)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.CommandReadOperation.execute(CommandReadOperation.java:48)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.MongoDatabaseImpl.executeCommand(MongoDatabaseImpl.java:196)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:165)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:160)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:150)
	at io.tapdata.mongodb.MongodbUtil.getVersion(MongodbUtil.java:60)
	at io.tapdata.mongodb.MongodbConnector.createStreamReader(MongodbConnector.java:1629)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1563)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[INFO ] 2024-09-27 19:00:04.408 - [任务 281] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 19:00:04.408 - [任务 281] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ab8145a 
[INFO ] 2024-09-27 19:00:04.516 - [任务 281] - Stop task milestones: 66f5281a3643f36dd46659f8(任务 281)  
[INFO ] 2024-09-27 19:00:06.546 - [任务 281] - Stopped task aspect(s) 
[INFO ] 2024-09-27 19:00:06.663 - [任务 281] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 19:00:06.663 - [任务 281] - Remove memory task client succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
[INFO ] 2024-09-27 19:00:06.663 - [任务 281] - Destroy memory task client cache succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
[INFO ] 2024-09-27 19:00:14.176 - [任务 281] - Start task milestones: 66f5281a3643f36dd46659f8(任务 281) 
[INFO ] 2024-09-27 19:00:14.176 - [任务 281] - Task initialization... 
[INFO ] 2024-09-27 19:00:14.729 - [任务 281] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-09-27 19:00:15.681 - [任务 281] - The engine receives 任务 281 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 19:00:15.843 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] start preload schema,table counts: 1 
[INFO ] 2024-09-27 19:00:15.844 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] start preload schema,table counts: 3 
[INFO ] 2024-09-27 19:00:15.844 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 19:00:15.844 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 19:00:15.844 - [任务 281][主从合并] - Node merge_table_processor(主从合并: d6df3ac4-0696-4cdd-a75f-de1377eaa482) enable batch process 
[INFO ] 2024-09-27 19:00:15.884 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] start preload schema,table counts: 1 
[INFO ] 2024-09-27 19:00:15.884 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 19:00:15.888 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] start preload schema,table counts: 1 
[INFO ] 2024-09-27 19:00:15.958 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 19:00:15.958 - [任务 281][主从合并] - 
Merge lookup relation{
  POLICY(310b9c9c-1c1b-47a9-bda1-ba4aaea895dc)
    ->CLAIM(54a0cef0-fe0c-4328-8d38-ce00b7e84979)
} 
[INFO ] 2024-09-27 19:00:16.119 - [任务 281][主从合并] - Create merge cache imap name: -389107495, external storage: ExternalStorageDto[name='Target MongoDB External Storage', type='mongodb', uri='mongodb://************:27018/tapdata?directConnection=true', table='null', ttlDay=0] 
[INFO ] 2024-09-27 19:00:16.119 - [任务 281][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-09-27 19:00:16.119 - [任务 281][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-09-27 19:00:16.122 - [任务 281][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 19:00:16.217 - [任务 281][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 19:00:16.218 - [任务 281][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-09-27 19:00:16.219 - [任务 281][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-09-27 19:00:16.220 - [任务 281][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 19:00:16.225 - [任务 281][CLAIM] - batch offset found: {"CLAIM":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-09-27 19:00:16.245 - [任务 281][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-09-27 19:00:16.246 - [任务 281][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-09-27 19:00:16.347 - [任务 281][test3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 19:00:16.566 - [任务 281][CLAIM] - Incremental sync starting... 
[INFO ] 2024-09-27 19:00:16.566 - [任务 281][CLAIM] - Initial sync completed 
[INFO ] 2024-09-27 19:00:16.580 - [任务 281][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[WARN ] 2024-09-27 19:00:16.619 - [任务 281][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is {"ok": 0.0, "errmsg": "Authentication failed.", "code": 18, "codeName": "AuthenticationFailed", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1727434804, "i": 2}}, "signature": {"hash": {"$binary": {"base64": "jzSYwYJzfABX8vVuOT2bTjjNpKw=", "subType": "00"}}, "keyId": 7376103549123428362}}, "operationTime": {"$timestamp": {"t": 1727434804, "i": 2}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:102)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:49)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-27 19:00:16.671 - [任务 281][POLICY] - Incremental sync starting... 
[INFO ] 2024-09-27 19:00:16.678 - [任务 281][POLICY] - Initial sync completed 
[INFO ] 2024-09-27 19:00:16.678 - [任务 281][POLICY] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1727410019,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[WARN ] 2024-09-27 19:00:16.800 - [任务 281][POLICY] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is {"ok": 0.0, "errmsg": "Authentication failed.", "code": 18, "codeName": "AuthenticationFailed", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1727434804, "i": 2}}, "signature": {"hash": {"$binary": {"base64": "jzSYwYJzfABX8vVuOT2bTjjNpKw=", "subType": "00"}}, "keyId": 7376103549123428362}}, "operationTime": {"$timestamp": {"t": 1727434804, "i": 2}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:102)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:49)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-27 19:00:18.358 - [任务 281][test3] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@69f81904: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"CLAIM.CLAIM_ID"}]}],"tableId":"d6df3ac4-0696-4cdd-a75f-de1377eaa482","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-09-27 19:00:18.359 - [任务 281][test3] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@69f81904: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"CLAIM.CLAIM_ID"}]}],"tableId":"d6df3ac4-0696-4cdd-a75f-de1377eaa482","type":101}) 
[INFO ] 2024-09-27 19:00:37.057 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] running status set to false 
[INFO ] 2024-09-27 19:00:37.058 - [任务 281][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 19:00:37.060 - [任务 281][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-310b9c9c-1c1b-47a9-bda1-ba4aaea895dc 
[INFO ] 2024-09-27 19:00:37.060 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] schema data cleaned 
[INFO ] 2024-09-27 19:00:37.062 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] monitor closed 
[INFO ] 2024-09-27 19:00:37.063 - [任务 281][POLICY] - Node POLICY[310b9c9c-1c1b-47a9-bda1-ba4aaea895dc] close complete, cost 48 ms 
[INFO ] 2024-09-27 19:00:37.067 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] running status set to false 
[INFO ] 2024-09-27 19:00:37.067 - [任务 281][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 19:00:37.068 - [任务 281][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-54a0cef0-fe0c-4328-8d38-ce00b7e84979 
[INFO ] 2024-09-27 19:00:37.068 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] schema data cleaned 
[INFO ] 2024-09-27 19:00:37.069 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] monitor closed 
[INFO ] 2024-09-27 19:00:37.069 - [任务 281][CLAIM] - Node CLAIM[54a0cef0-fe0c-4328-8d38-ce00b7e84979] close complete, cost 6 ms 
[INFO ] 2024-09-27 19:00:37.069 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] running status set to false 
[INFO ] 2024-09-27 19:00:37.069 - [任务 281][主从合并] - Destroy merge cache resource: -389107495 
[INFO ] 2024-09-27 19:00:37.073 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] schema data cleaned 
[INFO ] 2024-09-27 19:00:37.073 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] monitor closed 
[INFO ] 2024-09-27 19:00:37.074 - [任务 281][主从合并] - Node 主从合并[d6df3ac4-0696-4cdd-a75f-de1377eaa482] close complete, cost 4 ms 
[INFO ] 2024-09-27 19:00:37.075 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] running status set to false 
[INFO ] 2024-09-27 19:00:37.078 - [任务 281][test3] - PDK connector node stopped: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 19:00:37.078 - [任务 281][test3] - PDK connector node released: HazelcastTargetPdkDataNode-6eae59a1-67d8-403e-bd45-f631e5102fec 
[INFO ] 2024-09-27 19:00:37.079 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] schema data cleaned 
[INFO ] 2024-09-27 19:00:37.080 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] monitor closed 
[INFO ] 2024-09-27 19:00:37.151 - [任务 281][test3] - Node test3[6eae59a1-67d8-403e-bd45-f631e5102fec] close complete, cost 5 ms 
[INFO ] 2024-09-27 19:00:37.152 - [任务 281][POLICY] - Incremental sync completed 
[INFO ] 2024-09-27 19:00:37.154 - [任务 281][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[INFO ] 2024-09-27 19:00:37.154 - [任务 281][CLAIM] - Incremental sync completed 
[INFO ] 2024-09-27 19:00:37.156 - [任务 281][CLAIM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[ERROR] 2024-09-27 19:00:37.256 - [任务 281][CLAIM] - java.lang.NullPointerException <-- Error Message -->
java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.mongodb.MongodbUtil.getVersion(MongodbUtil.java:59)
	io.tapdata.mongodb.MongodbConnector.createStreamReader(MongodbConnector.java:1629)
	io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1563)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.mongodb.MongodbUtil.getVersion(MongodbUtil.java:59)
	at io.tapdata.mongodb.MongodbConnector.createStreamReader(MongodbConnector.java:1629)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1563)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[ERROR] 2024-09-27 19:00:37.263 - [任务 281][POLICY] - java.lang.NullPointerException <-- Error Message -->
java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.mongodb.MongodbUtil.getVersion(MongodbUtil.java:59)
	io.tapdata.mongodb.MongodbConnector.createStreamReader(MongodbConnector.java:1629)
	io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1563)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.mongodb.MongodbUtil.getVersion(MongodbUtil.java:59)
	at io.tapdata.mongodb.MongodbConnector.createStreamReader(MongodbConnector.java:1629)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1563)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[INFO ] 2024-09-27 19:00:42.093 - [任务 281] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 19:00:42.099 - [任务 281] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@403d1b00 
[INFO ] 2024-09-27 19:00:42.290 - [任务 281] - Stop task milestones: 66f5281a3643f36dd46659f8(任务 281)  
[INFO ] 2024-09-27 19:00:42.290 - [任务 281] - Stopped task aspect(s) 
[INFO ] 2024-09-27 19:00:42.290 - [任务 281] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 19:00:42.400 - [任务 281] - Remove memory task client succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
[INFO ] 2024-09-27 19:00:42.603 - [任务 281] - Destroy memory task client cache succeed, task: 任务 281[66f5281a3643f36dd46659f8] 
