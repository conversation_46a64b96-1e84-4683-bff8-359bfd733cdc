{
  "compilerOptions": {
    /* 基本选项 */
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["ESNext", "DOM"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "node", // 选择模块解析策略： 'node' (Node.js) or 'classic' (TypeScript pre-1.6)
    "paths": {
      "@tap/*": ["packages/*/src"]
    },
    "allowJs": true,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true, // 允许从没有设置默认导出的模块中默认导入。
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "include": [
    "apps/daas/src/**/*.ts",
    "packages/*/src/**/*.ts",
    "packages/*/src/**/*.tsx",
    "packages/*/src/**/*.vue"
  ],
  "exclude": ["node_modules"]
}
