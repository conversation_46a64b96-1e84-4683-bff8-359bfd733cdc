const path = require('path');
const fs = require('fs');
const backend = require('../process/backend');
const moment = require('moment');
const {sendBehavior} = require("./sendBehavior");

exports.update = async function(conf, arguments) {
    const log4js = require("log4js");
    const dateString = moment().format('YYYYMMDDHHmmss');
    log4js.configure({
        appenders: { cheese: {compress: true, alwaysIncludePattern:true , daysToKeep: 7, type: "dateFile", filename: conf.WORK_DIR + "/logs/tapdata-agent/update"+dateString+".log" } },
        categories: { default: { appenders: ["cheese"], level: "info" } }
    });
    const logger = log4js.getLogger("cheese");
    logger.info('stop service.');
    let WORK_DIR = 'update';
    for (let i = 0; i < arguments.length; i++) {
        if (arguments[i] === '--updateDir') {
            WORK_DIR = arguments[i + 1];
        }
    }
    const homePaht = conf.TAPDATA_HOME === '' ? '/' : conf.TAPDATA_HOME
    let checkBackStr = path.join(homePaht, "tapdata");
    if (conf._linux)
        fs.chmodSync(checkBackStr, fs.constants.S_IXUSR);

    //let stdio = ['ignore', 'pipe', 'pipe'];
    // let subProcess = require('child_process').spawn(checkBackStr, ['stop', '-f'], {//,'--workDir',conf.WORK_DIR
    //     'cwd': conf.TAPDATA_HOME,
    //     'stdio': stdio,
    //     //'env': env,
    //     'detached': true,
    //     'windowsHide': true
    // });
    //subProcess.unref();
    let connect = {};
    connect.write = function (msg,systemObj) {
        logger.info(msg);
    };
    await backend.stopBackend(conf, false, connect);
    await require("../process/agent").stopAPI(conf, ['stop','agent']);
    setTimeout(() => {
        try{
            fs.mkdirSync(path.join(conf.TAPDATA_HOME,'components'));
        }catch(e){}
        try{
            fs.mkdirSync(path.join(conf.TAPDATA_HOME,'etc'));
        }catch(e){}
        let from = path.join(conf.TAPDATA_HOME, WORK_DIR, 'tapdata-agent');
        let to = path.join(conf.TAPDATA_HOME, 'components', 'tapdata-agent.jar');
        let bak = path.join(conf.TAPDATA_HOME, 'tapdata_bak', 'tapdata-agent.jar');
        let from1 = path.join(conf.TAPDATA_HOME, WORK_DIR, 'log4j2.yml');
        let to1 = path.join(conf.TAPDATA_HOME, 'etc', 'log4j2.yml');
        let bak1 = path.join(conf.TAPDATA_HOME, 'tapdata_bak', 'log4j2.yml');
        let from2 = path.join(conf.TAPDATA_HOME, WORK_DIR, 'tapdata');
        let to2 = path.join(conf.TAPDATA_HOME, 'tapdata');
        let bak2 = path.join(conf.TAPDATA_HOME,'tapdata_bak', 'tapdata');
        //let from3 = path.join(conf.TAPDATA_HOME, WORK_DIR, '.version');
        //let to3 = path.join(conf.TAPDATA_HOME, '.version');
        let bak3 = path.join(conf.TAPDATA_HOME,'tapdata_bak', '.version');
        let from4 = path.join(conf.TAPDATA_HOME, WORK_DIR, 'tapdata.exe');
        let to4 = path.join(conf.TAPDATA_HOME, 'tapdata.exe');
        let bak4 = path.join(conf.TAPDATA_HOME,'tapdata_bak', 'tapdata.exe');
        try{
            fs.mkdirSync( path.join(conf.TAPDATA_HOME,'tapdata_bak'));
        }catch(e){}
        try{
            fs.unlinkSync(bak);
        }catch (e) {}
        try{
            fs.unlinkSync(bak1);
        }catch (e) {}
        try{
            fs.unlinkSync(bak2);
        }catch (e) {}
        try{
            fs.unlinkSync(bak3);
        }catch (e) {}
        try{
            fs.unlinkSync(bak4);
        }catch (e) {}

        try{
            let hto = false;
            let hto1 = false;
            let hto2 = false;
            let hto4 = false;
            try{
                fs.accessSync(to)
                hto = true
            }catch (e) {}
            try{
                fs.accessSync(to1)
                hto1 = true
            }catch (e) {}
            try{
                fs.accessSync(to2)
                hto2 = true
            }catch (e) {}
            try{
                fs.accessSync(to4)
                hto4 = true
            }catch (e) {}
            if(hto) {
                fs.copyFileSync(to, bak);
            }
            if(hto1) {
                fs.copyFileSync(to1, bak1);
            }
            if(!conf._windows) {
                if(hto2) {
                    fs.copyFileSync(to2, bak2);
                }
            }else {
                // fs.copyFileSync(to3, bak3);
                if(hto4) {
                    fs.copyFileSync(to4, bak4);
                }
            }
        }catch (e) {
            logger.error(e)
            sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
            spaw('fail',checkBackStr,conf,logger);
            return;
        }
        try{
            fs.unlinkSync(to)
        }catch (e) {
            logger.error(e)
            sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
            spaw('fail',checkBackStr,conf,logger);
            return;
        }
        try{
            fs.unlinkSync(to1)
        }catch (e) {
            fs.copyFileSync(bak, to);
            logger.error(e)
            sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
            spaw('fail',checkBackStr,conf,logger);
            return;
        }
        if(!conf._windows) {
            try {
                fs.unlinkSync(to2)
            } catch (e) {
                fs.copyFileSync(bak, to);
                fs.copyFileSync(bak1, to1);
                logger.error(e)
                sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
                spaw('fail', checkBackStr, conf,logger);
                return;
            }
        }else {
            try {
                fs.unlinkSync(to4)
            } catch (e) {
                fs.copyFileSync(bak, to);
                fs.copyFileSync(bak1, to1);
                //fs.copyFileSync(bak2, to2);
                logger.error(e)
                sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
                spaw('fail', checkBackStr, conf,logger);
                return;
            }
        }
        try{
            fs.copyFileSync(from, to)
        }catch (e) {
            fs.copyFileSync(bak, to);
            fs.copyFileSync(bak1, to1);
            if(!conf._windows) {
                fs.copyFileSync(bak2, to2);
            }else {
                fs.copyFileSync(bak4, to4);
            }
            logger.error(e)
            sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
            spaw('fail',checkBackStr,conf,logger);
            return;
        }
        try{
            fs.copyFileSync(from1, to1)
        }catch (e) {
            fs.unlinkSync(to)
            fs.copyFileSync(bak, to);
            fs.copyFileSync(bak1, to1);
            if(!conf._windows) {
                fs.copyFileSync(bak2, to2);
            }else {
                fs.copyFileSync(bak4, to4);
            }
            logger.error(e)
            sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
            spaw('fail',checkBackStr,conf,logger);
            return;
        }
        if(!conf._windows) {
            try {
                fs.copyFileSync(from2, to2)
            } catch (e) {
                fs.unlinkSync(to)
                fs.unlinkSync(to1)
                fs.copyFileSync(bak, to);
                fs.copyFileSync(bak1, to1);
                fs.copyFileSync(bak2, to2);
                //fs.copyFileSync(bak4, to4);
                logger.error(e)
                sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
                spaw('fail', checkBackStr, conf,logger);
                return
            }
        }else {
            try {
                fs.copyFileSync(from4, to4)
            } catch (e) {
                fs.unlinkSync(to)
                fs.unlinkSync(to1)
                //fs.unlinkSync(to2)
                fs.copyFileSync(bak, to);
                fs.copyFileSync(bak1, to1);
                //fs.copyFileSync(bak2, to2);
                fs.copyFileSync(bak4, to4);
                logger.error(e)
                sendBehavior(conf,{code:'handleFile',result:'failed',msg:e});
                spaw('fail', checkBackStr, conf,logger);
                return
            }
        }
        try {
            fs.unlinkSync(path.join(conf.TAPDATA_HOME, '.tapdata-agent'));
        }catch (e) {}
        try {
            fs.unlinkSync(path.join(conf.TAPDATA_HOME, 'tapdata-agent'));
        }catch (e) {}
        try {
            fs.unlinkSync(path.join(conf.TAPDATA_HOME, 'tapdata-agent.exe'));
        }catch (e) {}
        logger.info('finished update fields.');
        logger.info('restart backend.');
        sendBehavior(conf,{code:'handleFile',result:'successed',msg:''});
        try {
            spaw('done', checkBackStr, conf,logger);
        }catch (e) {
            logger.error(e);
        }
    }, 10000)
    return true
}

function spaw(status,checkBackStr,conf,logger){
    console.info('is Linux',conf._linux)
    let stdio = ['ignore', 'pipe', 'pipe'];
    if (conf._linux)
        fs.chmodSync(checkBackStr, fs.constants.S_IXUSR);
    //console.info(checkBackStr)
    let subProcess1 = require('child_process').spawn(checkBackStr, ['start', 'backend', status], {//,'--workDir',conf.WORK_DIR
        'cwd': conf.TAPDATA_HOME,
        'stdio': stdio,
        //'env': env,
        'detached': true,
        'windowsHide': true
    });
    subProcess1.stderr.on('data',(msg)=>{
        logger.error(msg.toString());
    });
    subProcess1.stdout.on('data',(msg)=>{
        if(!msg.toString().includes("<<<< ")){
            logger.info(msg.toString());
        }
    });
    subProcess1.unref();
}
