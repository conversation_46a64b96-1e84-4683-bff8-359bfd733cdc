const fs = require("fs");
const path = require("path");
const basic = require("../basic");
const readline = require("readline");
const YAML = require("yamljs");
const CryptoJS = require("crypto-js");
const {MongoClient} = require("mongodb");
const apiserver = require("../process/apiserver");
const processesManagement = require('../connection').processesManagement;
module.exports = async (arguments)=>{

    let sourcePath = '';
    for(let i=0;i<arguments.length;i++){
        if(arguments[i] === '--source' && arguments.length > i+1){
            sourcePath = arguments[i+1];
        }
    }
    if(sourcePath === ''){
        console.error("Update error.Please check your argument.");
        return;
    }

    let WORK_DIR = '';
    if(fs.existsSync(path.join(sourcePath,'.workDir'))){
        WORK_DIR = fs.readFileSync(path.join(sourcePath,'.workDir'),'utf8').toString();
    }
    if(WORK_DIR === ''){
        console.error("Update error.Please check source software workdir.");
        return;
    }
    arguments.push('--workDir');
    arguments.push(WORK_DIR);
    let sourceConf = require("../conf")(undefined, arguments);
    let conf = require("../conf")(undefined, arguments);
    sourceConf.TAPDATA_HOME = sourcePath;
    sourceConf.COMPONENTS_DIR = path.join(sourcePath,'components');

    const sourceStatus = await basic.getStatus(sourceConf);
    if(sourceStatus.frontMPid === '' && sourceStatus.backendPid === '' && sourceStatus.apiMPid === ''){
        console.error("Update error.Source software is not running.");
        return;
    }
    await require("../process/agent").stopAPI(sourceConf, ['stop','agent'])

    const checkGod = require('../checkGod');
    let updateStatus = '';
    for(let i=0;i<arguments.length;i++) {
        if (['done', 'fail'].includes(arguments[i])) {
            updateStatus = arguments[i]
        }
    }
    await checkGod(conf,updateStatus);
    let CLIConnect = {
        'write':function(msg){//info
            try {
                if(msg.includes("<<<< ")){
                    readline.clearLine(process.stdout, 0);
                    readline.moveCursor(process.stdout, 0,-1);
                    msg = msg.replace('<<<< ','');

                }
                console.info(msg);
            }catch(e){
                console.error(e);
            }
        }
    };
    if(sourceStatus.frontMPid !== ''){
        try {
            await processesManagement(['stop','frontend'], sourceConf, CLIConnect);
        } catch (e) {
            console.error(e);
            console.error('Stop Frontend error.Please check the frontend status.');
            return;
        }
        console.info('start frontend...');
        try {
            await processesManagement(['start','frontend'], conf, CLIConnect);
        } catch (e) {
            console.error(e);
            console.error('Start Frontend error.Please check the frontend status.');
            return;
        }
    }
    //升级API
    if(sourceStatus.apiMPid !== ''){
        try {
            await processesManagement(['stop','apiserver'], sourceConf, CLIConnect);
        } catch (e) {
            console.error(e);
            console.error('Stop Frontend error.Please check the frontend status.');
            return;
        }
        console.info('start frontend...');
        try {
            await apiserver.doStartAPI(conf, arguments);
            await processesManagement(['start','apiserver'], conf, CLIConnect);
        } catch (e) {
            console.error(e);
            console.error('Start Frontend error.Please check the frontend status.');
            return;
        }
    }
    //升级引擎
    if(sourceStatus.backendPid !== ''){
        //检测原引擎心跳是否正常
        let sourceProcessId = '';
        try{
            sourceProcessId = fs.readFileSync(path.join(WORK_DIR,'agent.yml'),'utf8').toString();
            sourceProcessId = sourceProcessId.split('agentId:')[1].split('}')[0].trim();
        }catch (e) {
            console.error("Update error.Can't read the processId.");
            return;
        }
        console.info('Check processId OK.');

        let sConfig = getConfig(WORK_DIR);
        if(!sConfig){
            console.error('Update error.Can\'t read the Mongodb config.')
            return;
        }
        const mongoDBClient = await getMongoDB(sConfig,WORK_DIR);
        if(!mongoDBClient){
            console.error('Update error.Can\'t connect to Mongodb.')
            return;
        }
        const mongoDB = mongoDBClient.db();
        let Setting = await mongoDB.collection("Settings").findOne({key:'jobHeartTimeout'});
        if(!Setting){
            console.error("Update error.Can't get the Job Heart Timeout setting.");
            await mongoDBClient.close();
            return;
        }
        const jobHeartTimeout = (new Date().getTime()) - (parseInt(Setting.value) * 2);
        const targetWorker = await mongoDB.collection("Workers").findOne({process_id:sourceProcessId,ping_time:{$gte:jobHeartTimeout}});
        if(!targetWorker){
            console.error("Update error.Source engine is not running.");
            await mongoDBClient.close();
            return;
        }

        try {
            await processesManagement(['stop','backend','-f'], sourceConf, CLIConnect);
        } catch (e) {
            console.error(e);
            console.error('Stop Backend error.Please check the Backend status.');
            return;
        }

        try {
            await processesManagement(['start','backend'], conf, CLIConnect);
        } catch (e) {
            console.error(e);
            console.error('Stop Backend error.Please check the Backend status.');
            return;
        }

        await mongoDB.collection("Workers").updateMany({process_id:sourceProcessId},{$set:{ping_time:1}});
        let now = new Date().getTime();
        console.info('waiting backend started...');
        while(true){
            const sourceWorker = await mongoDB.collection("Workers").findOne({process_id:sourceProcessId,ping_time:{$gte:jobHeartTimeout}});
            if(sourceWorker){
                break;
            }
            if(new Date().getTime() - now > (180 * 1000)){
                console.error('Backend start timeout.Please check the Backend status.');
                return;
            }
            await basic.sleep(5000);
        }
        const waittingStart = await mongoDB.collection("Task").countDocuments({agentId:sourceProcessId,pingTime:{$gte:jobHeartTimeout},status:'running'});
        await mongoDB.collection("Task").updateMany({agentId:sourceProcessId,pingTime: {$gte:jobHeartTimeout},status:'running'},{$set:{agentId:sourceProcessId,pingTime:1}});
        console.info('Transfer task:' + waittingStart + ' running task:0,please waiting...' );
        let waitTimes = 120;
        while (true){
            let runningNumber = await mongoDB.collection("Task").countDocuments({agentId:sourceProcessId,pingTime:{$gte:jobHeartTimeout},status:'running'});
            readline.clearLine(process.stdout, 0);
            readline.moveCursor(process.stdout, 0,-1);
            console.info('Transfer task:' + waittingStart + ' running task:' + runningNumber + ',please waiting...' );
            if(runningNumber >= waittingStart){
                console.info( "Update finished.All Task are running.");
                break;
            }
            if(waitTimes<0){
                console.info( "Task update time out.Please check your Task status.");
                break;
            }
            await basic.sleep(1000);
            waitTimes--;
        }
    }
}

function getConfig(sWorkDir){
    let sUri;
    let MONGO_SSL_CA = '';
    let MONGO_SSL_CERT_KEY = '';
    let CERT_DIR = 'cert';
    let CA_FILENAME = 'ca.pem';
    let CERTKEY_FILENAME = 'certificateKey.pem';
    let sslPEMKeyFilePassword = '';
    let ssl = '';
    if(fs.existsSync(path.join(sWorkDir,'application.yml'))){
        try{
            let sApplication = fs.readFileSync(path.join(sWorkDir,'application.yml'),'utf8').toString();
            sApplication = YAML.parse(sApplication.toString());
            if (!basic.isEmpty(sApplication['spring']['data']['mongodb']['password'])) {
                sApplication['spring']['data']['mongodb']['password'] = CryptoJS.RC4.decrypt(sApplication['spring']['data']['mongodb']['password'], basic.getKey()).toString(CryptoJS.enc.Utf8);
            }
            if (!basic.isEmpty(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
                sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = CryptoJS.RC4.decrypt(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'], basic.getKey()).toString(CryptoJS.enc.Utf8);
            }
            sUri = constructMetaDBSSLParams(constructMetaDBURI(sApplication), sApplication);
            if ("true" === sApplication['spring']['data']['mongodb']['ssl'] && !basic.isEmpty(sApplication['spring']['data']['mongodb']['sslCA'])) {
                MONGO_SSL_CA = fs.readFileSync(path.join(sWorkDir, CERT_DIR, CA_FILENAME));
                MONGO_SSL_CERT_KEY = fs.readFileSync(path.join(sWorkDir, CERT_DIR, CERTKEY_FILENAME));
                ssl = 'true';
            }
            if (!basic.isEmpty(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
                sslPEMKeyFilePassword = CryptoJS.RC4.decrypt(sApplication['spring']['data']['mongodb']['sslPEMKeyFilePassword'], basic.getKey()).toString(CryptoJS.enc.Utf8);
            }
            return {
                uri:sUri,
                MONGO_SSL_CA:MONGO_SSL_CA,
                MONGO_SSL_CERT_KEY:MONGO_SSL_CERT_KEY,
                ssl:ssl,
                sslPEMKeyFilePassword:sslPEMKeyFilePassword
            }
        }catch (e) {
            console.error(e);
            console.info("Update error.Can't read the source application.yml.please check the config.");
        }
    }else{
        console.info("Update error.Source application.yml is not exists.");
    }
}


function constructMetaDBURI(yamlObj) {
    let uri = yamlObj['spring']['data']['mongodb']['mongoConnectionString'];
    if(basic.isEmpty(uri))return '';
    if (uri.indexOf('mongodb://') < 0) {
        uri = 'mongodb://' + uri;
    }
    if (!isEmpty(yamlObj['spring']['data']['mongodb']['username']) &&
        !isEmpty(yamlObj['spring']['data']['mongodb']['password']) &&
        !isEmpty(yamlObj['spring']['data']['mongodb']['authenticationDatabase'])) {
        let encodeUsername = encodeURIComponent(yamlObj['spring']['data']['mongodb']['username']);
        let encodePassword = encodeURIComponent(yamlObj['spring']['data']['mongodb']['password']);
        uri = uri.replace("mongodb://", "mongodb://" + encodeUsername + ":" + encodePassword + '@');
        if (uri.indexOf('?') > 0) {
            uri += '&authSource=' + yamlObj['spring']['data']['mongodb']['authenticationDatabase']
        } else {
            uri += '?authSource=' + yamlObj['spring']['data']['mongodb']['authenticationDatabase']
        }
    }
    return uri;
}

function isEmpty(obj) {
    return typeof obj === "undefined" || obj === null || obj === "";
}

function constructMetaDBSSLParams(uri, obj) {
    let mongo_conn = uri;//obj['spring']['data']['mongodb']['uri'];
    if (!isEmpty(mongo_conn)) {
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCertKey'])) {
            mongo_conn += " --ssl --sslAllowInvalidHostnames --sslAllowInvalidCertificates --sslPEMKeyFile " + obj['spring']['data']['mongodb']['sslCertKey'];
        }
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCertKey']) && !isEmpty(obj['spring']['data']['mongodb']['sslPEMKeyFilePassword'])) {
            mongo_conn += " --sslPEMKeyPassword " + obj['spring']['data']['mongodb']['sslPEMKeyFilePassword'];
        }
        if (!isEmpty(obj['spring']['data']['mongodb']['sslCA'])) {
            mongo_conn += " --sslCAFile " + obj['spring']['data']['mongodb']['sslCA'];
        }
    }
    return mongo_conn;
}

async function getMongoDB(conf,workDir){
    const MongoClient = require('mongodb').MongoClient;
    let mongoUri = conf.uri;
    let mongoOpts = {
        'useNewUrlParser': true,
        'useUnifiedTopology': true
    };
    if(conf.ssl === 'true' && !basic.isEmpty(conf.CA_FILENAME) && !basic.isEmpty(conf.CERTKEY_FILENAME)){
        mongoOpts['sslValidate'] = true;
        try {
            const ca = [fs.readFileSync(path.join(workDir, conf.CERT_DIR, conf.CA_FILENAME))];
            const cert = fs.readFileSync(path.join(workDir, conf.CERT_DIR, conf.CERTKEY_FILENAME));
            mongoOpts['ssl'] = true;
            mongoOpts['sslCA'] = ca;
            mongoOpts['sslCert'] = cert;
            mongoOpts['sslKey'] = cert;
            mongoOpts['checkServerIdentity'] = false;
        }catch (e) {
            console.info("Update error.Can't read the SSL files.");
            return;
        }
    }
    try {
        if (!basic.isEmpty(conf.sslPEMKeyFilePassword)) {
            mongoOpts['sslPass'] = conf.sslPEMKeyFilePassword;
        }
        const client = new MongoClient(mongoUri, mongoOpts);
        await client.connect();
        return client;
    }catch (e) {
        console.info("Update error.");
        console.info(e);
    }
}