
const MONGODB_PROCESS_NAME = 'mongodb',
    BUILD_IN_MONGODB = 'build-in',
    SELF_BUILD_MONGODB = 'self-build';
const path = require('path');
const fs = require('fs');
const YAML = require('yamljs');
const { spawnSync, spawn} = require('child_process');
const { MongoClient } = require('mongodb');

const reportInfo = {server: MONGODB_PROCESS_NAME, level: 'INFO'};

const enableBuildInMongoDB = function(conf) {
    return conf.mongodbDeployType === BUILD_IN_MONGODB
}

const fileExists = function(filePath) {
    if (fs.existsSync(filePath)) {
        return fs.statSync(filePath).isFile()
    }
    return false;
}
const directoryExists = function(filePath) {
    if (fs.existsSync(filePath)) {
        return fs.statSync(filePath).isDirectory()
    }
    return false;
}
const ensureDirectoryExists = function(filePath) {
    if (directoryExists(filePath)) {
        return
    }
    fs.mkdirSync(filePath, {recursive: true});
}

const mongodbPaths = function(conf) {
    const mongodFileName = conf._windows ? 'mongod.exe' : 'mongod';
    const mongodFilePath = path.join(conf.COMPONENTS_DIR, mongodFileName);

    const workDir = path.resolve(conf.WORK_DIR)
    const mongoWorkDirPath = path.join(workDir, 'mongodb')
    const mongoConfigFilePath = path.join(mongoWorkDirPath, 'mongod.conf')
    const mongoDataPath = path.join(mongoWorkDirPath, 'data')
    const mongoLogPath = path.join(mongoWorkDirPath, 'log')
    const mongoLogFilePath = path.join(mongoLogPath, 'mongodb.log')
    const mongoPidFilePath = path.join(mongoWorkDirPath, 'mongod.pid')
    return {
        mongoWorkDirPath,
        mongoConfigFilePath,
        mongoDataPath,
        mongoLogPath,
        mongoLogFilePath,
        mongoPidFilePath,
        mongodFilePath
    }
}

const buildMongoConfig = function(mongodbPath, conf) {
    const mongoConfig = {
        storage: {
            dbPath: mongodbPath.mongoDataPath,
            journal: {
                enabled: true,
            }
        },
        systemLog: {
            logAppend: true,
            path: mongodbPath.mongoLogFilePath,
            logRotate: 'rename',
            destination: 'file',
            timeStampFormat: 'iso8601-utc'
        },
        net: {
            port: Number(conf.mongodbPort) || 27017,
            bindIp: '0.0.0.0'
        },
        processManagement: {
            fork: true,
            pidFilePath: mongodbPath.mongoPidFilePath
        },
        replication: {
            replSetName: 'rs0'
        }
    }
    if (conf._windows)
        delete mongoConfig.processManagement.fork
    return mongoConfig
}

const  writeMongoConfig = function(mongoConfig, mongoConfigFilePath) {
    let yamlStr = YAML.stringify(mongoConfig, 4);
    fs.writeFileSync(mongoConfigFilePath, yamlStr, { encoding: 'utf8' });
}
const createMongoClient = async function(mongoConfig, timeout) {
    return new MongoClient(`mongodb://127.0.0.1:${mongoConfig.net.port || 27017}/`, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        connectTimeoutMS: timeout
    })
}
const checkMongoDBReady = async function(mongoConfig){
    const client = await createMongoClient(mongoConfig, 30000);
    try {
        await client.connect();
        await client.close();
        return true;
    } catch (e) {
        console.error('connect mongodb error', e)
        return false;
    }
}

const initReplicaSet = async function(mongoConfig) {
    const client = await createMongoClient(mongoConfig, 10000);
    try {
        await client.connect()
        const adminDb = await client.db('admin');
        const result = await adminDb.command({
            replSetInitiate: {}
        })
        await client.close();
        return { result: result.ok === 1 };
    } catch (error) {
        await client.close();
        if (error.codeName === 'AlreadyInitialized')
            return { result: true };
        return { result: false, error: error.errmsg };
    }
}

const getAllProcess = async function() {
    let processList = []
    if (process.platform === 'win32') {
        processList = await require('../ps_win').getProcessesWindows();
    } else {
        processList = await require("getprocesses").getProcesses();
    }
    return processList
}
const getProcessByPid = async function(pid) {
    const processList = await getAllProcess();
    for (let p of processList) {
        if (p.pid === pid)
            return p;
    }
    return null;
}

const getProcessByCommand = async function(command) {
    const processList = await getAllProcess();
    for (let p of processList) {
        if (p.command === command)
            return p;
    }
    return null;
}

const startMongod = async function(binaryFilePath, configFilePath) {
    if (process.platform === 'win32')
        return await startMongod_win32(binaryFilePath, configFilePath);
    else
        return await startMongod_unix(binaryFilePath, configFilePath)
}

const startMongod_win32 = function(binaryFilePath, configFilePath) {
    return new Promise((resolve, reject) => {
        const childProcess = spawn(binaryFilePath, ['-f', configFilePath], {
            detached: true,
            stdio: ['ignore', 'pipe', 'pipe'],
            windowsHide: true
        });
        const result = {
            stdout: '',
            stderr: '',
            code: 0,
        }
        let timeoutId = setTimeout(() => {
            childProcess.unref();
            resolve(result)
        }, 3000);

        childProcess.on('error', (err) => {
            result.error = err.toString();
            clearTimeout(timeoutId);
            reject(err);
        })
        childProcess.on('spawn', () => {
            childProcess.stdout.on('data', (data) => {
                result.stdout += data
            })
            childProcess.stderr.on('data', (data) => {
                result.stderr += data
            })
        })
        childProcess.on('exit', (code) => {
            result.code = code;
        })
    })
}

const startMongod_unix = async function(binaryFilePath, configFilePath) {
    if (!fileExists(binaryFilePath))
        throw new Error(`${binaryFilePath} does not exist`);
    if (!fileExists(configFilePath))
        throw new Error(`${configFilePath} does not exist`);

    const mongodProcess = await getProcessByCommand(binaryFilePath)
    if (mongodProcess && mongodProcess.arguments.join(' ').indexOf(configFilePath) >= 0) {
        throw new Error(`MongoDB is already started. Skipping start operation.`)
    }

    const commandResult = spawnSync(binaryFilePath, ['-f', configFilePath]);

    return {
        stdout: commandResult.stdout.toString(),
        stderr: commandResult.stderr.toString(),
        code: commandResult.status,
        pid: commandResult.pid,
        error: (commandResult.error || '' ).toString()
    }
}

const deployMongoDB = async function(mongodbPath, conf) {

    ensureDirectoryExists(mongodbPath.mongoDataPath);
    ensureDirectoryExists(mongodbPath.mongoLogPath);

    const mongoConfig = buildMongoConfig(mongodbPath, conf);

    writeMongoConfig(mongoConfig, mongodbPath.mongoConfigFilePath)
    const startResult = await startMongod(mongodbPath.mongodFilePath, mongodbPath.mongoConfigFilePath);
    if (startResult.code !== 0) {
        throw new Error(`Start MongoDB failed, exit code: ${startResult.code}, error: ${startResult.stderr || ''} ${startResult.error || ''}`);
    } else {
        console.log(startResult)
    }
    const mongodbStarted = await checkMongoDBReady(mongoConfig, mongodbPath.mongoConfigFilePath);
    if (!mongodbStarted)
        throw new Error(`MongoDB ready timeout, may be caused by a failure to start the mongod process.`);
    const {result, error} = await initReplicaSet(mongoConfig)
    if (!result)
        throw new Error('Initiate MongoDB replica set failed, error message ' + error)

    return true;
}
const startMongoDB = async function(conf, arguments, connect) {

    if (!enableBuildInMongoDB(conf))
        return true;

    const mongodbPath = mongodbPaths(conf);

    if (!fileExists(mongodbPath.mongodFilePath)) {
        connect.write(`MongoDB start failed, not found file ${mongodbPath.mongodFilePath}.`, reportInfo);
        return false;
    }

    let result = false
    if (fileExists(mongodbPath.mongoConfigFilePath)) {
        const startResult = await startMongod(mongodbPath.mongodFilePath, mongodbPath.mongoConfigFilePath);
        if (startResult.code !== 0) {
            throw new Error(`MongoDB start failed, exit code: ${startResult.code}, error: ${startResult.stderr || ''} ${startResult.error || ''}`);
        } else {
            console.log(startResult);
            result = true;
        }
    } else {
        result = await deployMongoDB(mongodbPath, conf)
    }
    connect.write(`MongoDB started.`, reportInfo);
    return result;

}

const stopMongoDB = async function(conf, restart, connect) {

    const mongodbPath = mongodbPaths(conf);
    if (!fileExists(mongodbPath.mongoConfigFilePath)) {
        connect.write(`Not found MongoDB config file, skip stop mongodb.`, Object.assign({}, reportInfo, {level: 'ERROR'}));
        return
    }
    try {
        const configFileData = fs.readFileSync(mongodbPath.mongoConfigFilePath, { encoding: 'utf8' })
        const mongoConfig = YAML.parse(configFileData.toString())

        const mongodProcess = await getProcessByCommand(mongodbPath.mongodFilePath)
        let oldPid;
        if (mongodProcess) {
            try {
                oldPid = mongodProcess.pid;
                process.kill(mongodProcess.pid, 'SIGTERM')
                connect.write(`MongoDB stopped.`, reportInfo);
            } catch (e) {
                console.error(e)
            }
        } else {
            connect.write('MongoDB is already stopped. Skipping stop operation.', reportInfo);
        }

        if (restart) {
            if (oldPid) {
                let timeout = new Date().getTime() + 20000;
                while (timeout > new Date().getTime()) {
                    const oldProcess = await getProcessByPid(oldPid)
                    if (oldProcess === null)
                        break;
                }
            }
            await startMongod(mongodbPath.mongodFilePath, mongodbPath.mongoConfigFilePath)
            connect.write(`MongoDB started.`, reportInfo);
        }
    } catch (error) {
        throw error;
    }

}

module.exports = {
    MONGODB_PROCESS_NAME,
    BUILD_IN_MONGODB,
    SELF_BUILD_MONGODB,
    startMongoDB,
    stopMongoDB
}