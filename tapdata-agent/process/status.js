const basic = require('../basic');
module.exports = {
    backendProcess: function (conf) {
        let execString = "";
        if (conf._linux) {
            execString = "echo $(ps aux | grep -a -E \"" + conf.COMPONENTS_DIR + "/tapdata-agent-*\" | grep -av grep | awk {'print $2'})";
        }
        if (conf._windows) {
            execString = "tasklist /v /fo csv | findstr /i \"java\"";
        }
        //let execString = "echo $(ps aux | grep -a -E \"" + conf.COMPONENTS_DIR + "/tapdata-agent-*\" | grep -av grep | awk {'print $2'})";
        let msg = basic.execSync(execString,conf);
        return msg.toString().replace("\n","");
    },
    tapdataManagement: function (conf) {
        let execString = "";
        if (conf._linux) {
            execString = "echo $(ps aux | grep -a " + conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/server.js | grep -av grep | awk {'print $2'})";
        }
        if (conf._windows) {
            execString = "tasklist /v /fo csv | findstr /i \"node\"";
        }
        //let execString = "echo $(ps aux | grep -a " + conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/server.js | grep -av grep | awk {'print $2'})";
        let msg = basic.execSync(execString,conf);
        return msg.toString().replace("\n","");
    },
    apiserverManageProcess: function (conf) {
        let execString = "echo $(ps -ef | grep -a node | grep -a \"" + conf.COMPONENTS_DIR + "/apiserver/index.js\" | grep -av grep | awk '{print $2}')";
        let msg = basic.execSync(execString,conf);
        return msg.toString().replace("\n","");
    },
    apiserverProcess: function (conf) {
        let execString = "echo $(ps -ef | grep -a node | grep -a \"" + conf.COMPONENTS_DIR + "/apiserver/app.js\" | grep -av grep | awk '{print $2}')";
        let msg = basic.execSync(execString,conf);
        return msg.toString().replace("\n","");
    },
    osmonitorProcess: function (conf) {
        let execString = "echo $(ps aux | grep -a \"" + conf.COMPONENTS_DIR + "/os-monitor/index.js\" | grep -av grep | awk '{print $2}')";
        let msg = basic.execSync(execString,conf);
        return msg.toString().replace("\n","");
    }
};

/*
    function backendRunningTasks (){
        let execString = "echo $(ps aux | grep -a -E \""+TAPDATA_HOME+"/"+SCRIPT_DIR+"/task-scripts\" | grep -av grep | awk {'print $2'})";
        let msg = execSync(execString);
        return msg.toString();
    }
    function pm2Process (){
        let execString = "echo $(ps aux | grep -a PM2 | grep -av grep | grep `whoami` | awk {'print $2'})";
        let msg = execSync(execString);
        return msg.toString();
    }

    function drProcess (){
        let execString = "echo $(ps aux | grep -a `pwd`/"+SCRIPT_DIR+"/'dr_initial.js' | grep -av grep | awk {'print $2'})";
        let msg = execSync(execString);
        return msg.toString();
    }
    function bkJob (){
        let execString = "echo $(ps aux | grep -a `pwd`/"+SCRIPT_DIR+"/'bg_jobs.js' | grep -av grep | awk {'print $2'})";
        let msg = execSync(execString);
        return msg.toString();
    }
     */