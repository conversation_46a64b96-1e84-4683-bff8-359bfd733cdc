/**
 系统环境校验
 */
const child_process = require('child_process');
const path = require('path');
const fs = require('fs');
const basic = require('../basic');
const checkJDK = function (life,arguments,conf) {
    if(arguments[0] === 'start' && (arguments.length === 1 || arguments[1] === 'backend' || (arguments[1] === 'frontend' && basic.isTMJava(conf)))) {
        console.info("Testing JDK...");
        let str = "java -version";
        child_process.exec(str,{windowsHide:true}, (error, stdout, stderr) => {
                if (error) {
                    if(conf.appType === 'DFS') {
                        handleJava(conf, life).then((res) => {
                            if (res) {
                                life.emit("checked");
                            } else {
                                console.error("Please install java for running tapdata.");
                            }
                        }).catch((e) => {
                            console.error("Please install java for running tapdata.", e);
                        });
                        return;
                    }else{
                        console.error("Please install java for running tapdata.");
                        return;
                    }
                }
                let JDKVersion = stderr.split("version")[1].split('"')[1].substring(0, 3);
                if ((JDKVersion === "1.8" || JDKVersion === "11." || JDKVersion === "17.")
                ) {
                    console.info("java version:" + JDKVersion);//getMessage("00006")
                } else {
                    console.error("java version:" + JDKVersion);
                    console.error("We need Java version 1.8.");
                    if(conf.appType === 'DFS') {
                        handleJava(conf, life).then((res) => {
                            if (res) {
                                life.emit("checked");
                            } else {
                                console.error("Please install java for running tapdata.");
                            }
                        }).catch((e) => {
                            console.error("Please install java for running tapdata.", e);
                        });
                        return;
                    }else{
                        console.error("Please install java for running tapdata.");
                        return;
                    }
                }
                console.info("Java environment OK.");
                life.emit("checked");
                //checkMongoBD(life);
            }
        );
    }else{
        life.emit("checked");
    }
};
/*
function checkMongoBD(life){
    console.info("开始检测MongoDB");
    let str = "mongo -version";
    child_process.exec(str,(error) => {// stdout, stderr
            if (error) {
                console.error("请安装Mongodb并配置环境变量 https://docs.mongodb.com/manual/administration/install-on-linux/");
                return;
            }
            console.info("MongoDB环境正常");
        life.emit("checked");
        }
    );
}
 */
//module.exports=checkJDK;
module.exports = function (life,arguments,conf) {
    checkJDK(life,arguments,conf);
};

async function handleJava(conf,life){
    try{
        fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib','java','bin','java'), fs.constants.F_OK);
        conf.javaPath = path.join(conf.TAPDATA_HOME, 'lib','java','bin','java');
        checkSelfJDK (life,conf.javaPath);
        return true;
    }catch (e) {
    }
    try{
        fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib','java','bin','java.exe'), fs.constants.F_OK);
        conf.javaPath = path.join(conf.TAPDATA_HOME, 'lib','java','bin','java.exe');
        checkSelfJDK (life,conf.javaPath);
        return true;
    }catch (e) {

    }
    //建立目录
    try {
        fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib'), fs.constants.F_OK);
    } catch (e) {
        fs.mkdirSync(path.join(conf.TAPDATA_HOME, 'lib'));
    }
    if(conf._windows) {
        await basic.downloadFile('https://resource.tapdata.net/package/feagent/static/', 'jre-8u321-windows-x64-test.tar.gz', 'jre-8u321-windows-x64.tar.gz');
        fs.copyFileSync('jre-8u321-windows-x64.tar.gz', path.join(conf.TAPDATA_HOME, 'lib', 'jre-8u321-windows-x64.tar.gz'));
        fs.unlinkSync('jre-8u321-windows-x64.tar.gz', {'recursive': true});
    } else {
        await basic.downloadFile('https://resource.tapdata.net/package/feagent/static/', 'jre-8u361-linux-x64.tar.gz', 'jre-8u361-linux-x64.tar.gz');
        fs.copyFileSync('jre-8u361-linux-x64.tar.gz', path.join(conf.TAPDATA_HOME, 'lib', 'jre-8u361-linux-x64.tar.gz'));
        fs.unlinkSync('jre-8u361-linux-x64.tar.gz', {'recursive': true});
    }
    const tar = require('tar');
    console.info("Unpack the files...");
    let arr = fs.readdirSync(path.join(conf.TAPDATA_HOME, 'lib'));
    for (let x in arr) {
        if (/jre+[\s\S]*?\.tar\.gz$/.test(arr[x])) {
            try {
                fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib', 'java'), fs.constants.F_OK);
                fs.rmdirSync(path.join(conf.TAPDATA_HOME, 'lib', 'java'), {'recursive': true});
            } catch (e) {
            }
            try {
                await tar.x(  // or tar.extract(
                    {
                        file: path.join(conf.TAPDATA_HOME, 'lib', arr[x]),
                        C: path.join(conf.TAPDATA_HOME, 'lib')
                    }
                );
                let arr1 = fs.readdirSync(path.join(conf.TAPDATA_HOME, 'lib'));
                for (let y in arr1) {
                    if (/jre1+[\s\S]*?$/.test(arr1[y])) {
                        fs.accessSync(path.join(conf.TAPDATA_HOME, 'lib', arr1[y]), fs.constants.F_OK);
                        fs.renameSync(path.join(conf.TAPDATA_HOME, 'lib', arr1[y]), path.join(conf.TAPDATA_HOME, 'lib', 'java'));
                        if (basic.isDebug(arguments)) {
                            console.info("rename java dir");
                        }
                    }
                }
                fs.unlinkSync(path.join(conf.TAPDATA_HOME, 'lib', arr[x]), {'recursive': true});
            } catch (e) {
                console.info("Unpack the JRE files error:" + e);
                return;
            }
        }
    }
    return true;
}

function checkSelfJDK (life,javaPath) {
        let str = '"'+ javaPath + '" -version';
        child_process.exec(str,{windowsHide:true}, (error, stdout, stderr) => {
                if (error) {
                    console.error("Please install java for running tapdata.");
                    return;
                }
                let JDKVersion = stderr.split("version")[1].split('"')[1].substring(0, 3);
                if ((JDKVersion === "1.8")
                ) {
                    console.info("java version:" + JDKVersion);//getMessage("00006")
                } else {
                    console.error("java version:" + JDKVersion);
                    console.error("We need Java version 1.8.");
                }
                console.info("Java environment OK.");
                //life.emit("checked");
            }
        );
}