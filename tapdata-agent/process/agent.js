const getProcesses = require("getprocesses");
const path = require("path");
const basic = require('../basic');
module.exports = {
    stopAPI:async function (conf,arguments) {//停止API
        if(arguments.length>1 && arguments[0] === 'stop' && arguments[1] === 'agent') {
            let agent = conf.TAPDATA_HOME;
            let newAgent = conf.TAPDATA_HOME;
            if (conf._windows) {
                agent += "\\tapdata-agent";
                agent = agent.replace(/\//g, '\\');
                newAgent = "\\tapdata-agent";
            } else {
                agent += "/.tapdata-agent";
                newAgent += "/tapdata-agent";
            }
            let list = await getProcesses.getProcesses();
            for (let i = 0; i < list.length; i++) {
                if ((list[i].command.indexOf(agent) >= 0 || list[i].command.indexOf(newAgent) >= 0 ) &&
                    list[i].arguments.join(' ').indexOf("components")<0 &&
                    list[i].arguments.join(' ').indexOf("./subReport")<0 &&
                    list[i].arguments.join(' ').indexOf("./subReportStats")<0 &&
                    list[i].arguments.join(' ').indexOf("./subProcess")<0) {
                    basic.killPid(list[i].pid, conf);
                    console.info("Agent down.");
                    return true;
                }
            }
            return true;
        }else{
            return false;
        }
    }
};