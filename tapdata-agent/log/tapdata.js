/**
 * <AUTHOR>
 * @date 3/31/19
 * @description
 */
const request = require('request');
const appConfig = require('./config');

let token = null;
let urlNum = 0;
let urlNow = 0;
exports.getToken = getToken = function(cb){
	cb = cb || function(){};
	if( token ){
		cb(token);
	} else {
		urlNow = urlNum;
		reqToken(cb);
	}
};

function reqToken(cb){
	console.info('get Token '+ appConfig.tapDataServer.urls[urlNow] + appConfig.tapDataServer.tokenUrl);
	request.post({
		url: appConfig.tapDataServer.urls[urlNow] + appConfig.tapDataServer.tokenUrl,
		form: {
			accesscode: appConfig.tapDataServer.accessCode
		},
		timeout: 2000
	}, (err, response, body) => {
		if (err) {
			console.error('Get access token error', err);
			//cb(false);
		} else if (response.statusCode === 200) {
			let result = JSON.parse(body);
			token = result.id;
			console.log('Get access token success,', body);
			cb(token);
			if (result.ttl)
				setTimeout(() => {
					token = null;
				}, (result.ttl - 3600) * 1000); // 提前一小时获取token
			return;
		} else {
			console.error('Get access token error,', body);
			//cb(false)
		}
		if(urlNow + 1 === urlNum){
			cb(false);
			return;
		}
		if(urlNow + 1 >= appConfig.tapDataServer.urls.length)
			urlNow = 0;
		else
			urlNow++;
		reqToken(cb);
	})
}