const fs = require('fs');
const readline = require('readline');
const path = require('path');
let level = '';
let timet = '';
let apiFiles = 0;
module.exports = function(type,times,conf,systemInfo,callback,callback1){
    let fReadName = '';
    if('apiServer' === type) {
        fReadName = path.join(conf.WORK_DIR,'logs','api-server.log');
        console.info(fReadName);
        readFileToArr(fReadName,type,times,systemInfo,callback,callback1);
    }else if('engine' === type){
        fReadName = path.join(conf.WORK_DIR,'logs','tapdata-agent.log');
        console.info(fReadName);
        readFileToArr(fReadName,type,times,systemInfo,callback,callback1);
    }else if('management' === type){
        let files = fs.readdirSync(path.join(conf.WORK_DIR,'logs'));
        for (let x = 0; x<files.length;x++) {
            console.info(files[x]);
            if(files[x].indexOf('tapdata-web.log')>=0 && files[x].substring(files[x].length-3) !== '.gz'){
                apiFiles++;
                readFileToArr(path.join(conf.WORK_DIR,'logs',files[x]),type,times,systemInfo,callback,callback1);
            }
        }
    }
};

function readFileToArr(fReadName,type,times,systemInfo,callback,callback1){
    console.info("!!! reading file :"+fReadName);
    const fRead = fs.createReadStream(fReadName);
    level = '';
    timet = '';
    let arr = [];
    //const now = new Date().getTime();
    const objReadline = readline.createInterface({
        input:fRead
    });
    objReadline.on('line',function (line) {
        if(arr.length===1000)
        {
            let narr = arr;
            callback(narr);
            arr = [];
        }
        let msg = false;
        if('apiServer' === type) {
            msg = apiServerLog(line,systemInfo, times);
        }else if('engine' === type){
            msg = backendLog(line,systemInfo,times);
        }else if('management' === type){
            msg = managementLog(line,systemInfo,times);
        }
        if(msg) {
            arr.push(msg);
        }
    });
    objReadline.on('close',function () {
        callback(arr);
        apiFiles--;
        if('management' === type) {
            if(apiFiles === 0) {
                callback1({'uuid': systemInfo.uuid, 'server': type});
            }
        }else{
            callback1({'uuid': systemInfo.uuid, 'server': type});
        }
    });
}
function apiServerLog(str,systemInfo,times){
    if (str.substring(0, 33).split('[').length < 2) {
        if (timet && timet !== '')
            return {
                'level': level,
                'date': timet,
                'message': str,
                'threadName':'apiServer',
                'loggerName':'tapdataAgent',
                'hostname':systemInfo.hostname,
                'ip':systemInfo.ip,
                'uuid':systemInfo.uuid
            };
        return false;
    }
    try {
        if(str.substring(0,1) !== '[' || str.substring(24,25) !== ']'){
            return false;
        }
        let time = str.substring(1, 24);
        if(new Date(time).getTime()<=times){
            return false;
        }
        timet = time;
        const strArr = str.split("[")[2].split("]");
        if(strArr[0].length<15) {
            level = strArr[0];
        }else{
            level = strArr[0].split("levelStr: '")[1].split("'")[0]
        }
        let msg = str.substring(str.indexOf("]",str.indexOf("]")+1)+1);
        return {
            'level':level,
            'date':time,
            'message':msg,
            'threadName':'apiServer',
            'loggerName':'tapdataAgent',
            'hostname':systemInfo.hostname,
            'ip':systemInfo.ip,
            'uuid':systemInfo.uuid
        };
    }catch(e){
        return false;
    }
}
function backendLog(str,systemInfo,times){
    if (str.substring(0, 50).split('[').length < 2) {
        if (timet && timet !== '')
            return {
                'level': level,
                'date': timet,
                'message': str,
                'threadName':'engine',
                'loggerName':'tapdataAgent',
                'hostname':systemInfo.hostname,
                'ip':systemInfo.ip,
                'uuid':systemInfo.uuid
            };
        return false;
    }
    try {
        let time = str.split(']')[1].split('[')[0];
        if(new Date(time).getTime()<=times){
            return false;
        }
        timet = time;
        level = str.split(']')[0].substring(1).replace(/\s*/g,"");
        let msg = str.substring(str.indexOf("]",str.indexOf("]")+1)+1);
        return {
            'level':level,
            'date':time,
            'message':msg,
            'threadName':'engine',
            'loggerName':'tapdataAgent',
            'hostname':systemInfo.hostname,
            'ip':systemInfo.ip,
            'uuid':systemInfo.uuid
        };
    }catch(e){
        return false;
    }
}
function managementLog(str,systemInfo,times){
    if (str.substring(24,27) !== ' - ') {
        if (timet && timet !== '')
            return {
                'level': level,
                'date': timet,
                'message': str,
                'threadName':'management',
                'loggerName':'tapdataAgent',
                'hostname':systemInfo.hostname,
                'ip':systemInfo.ip,
                'uuid':systemInfo.uuid
            };
        return false;
    }
    try {
        let time = str.substring(0,24);
        if(new Date(time).getTime()<=times){
            return false;
        }
        timet = time;
        let ss = str.substring(27);
        level = ss.substring(0,ss.indexOf(':')).toUpperCase();
        let msg = ss.substring(ss.indexOf(':'));
        return {
            'level':level,
            'date':time,
            'message':msg,
            'threadName':'management',
            'loggerName':'tapdataAgent',
            'hostname':systemInfo.hostname,
            'ip':systemInfo.ip,
            'uuid':systemInfo.uuid
        };
    }catch(e){
        return false;
    }
}
//readFileToArr("D:/api-server.log",'apiServer',(data)=>{console.info("---------------");});