const prompts = require('prompts');
const YAML = require('yamljs');
//const os = require("os");
const fs = require('fs');
const basic = require("../basic");
const CryptoJS = require("crypto-js");
module.exports = function (arg, conf) {
    let questions = [];
    let WORK_DIR = conf.WORK_DIR;//os.homedir() + "/.tapdata";
    const data = fs.readFileSync(WORK_DIR + '/application.yml');
    let yamlObj = YAML.parse(data.toString());
    if (yamlObj['spring']['data']['mongodb']['username'] !== '') {
        questions.push({
                type: 'confirm',
                name: 'mongoPassword',
                message: "Does MongoDB require reset password?(y/n): ",
                initial: true
            },
            {
                type: prev => prev ? 'text' : null,
                name: 'mongo_password',
                message: "Please enter MongoDB password: "
            });
    }
    if (yamlObj['spring']['data']['mongodb']['ssl'] === 'true') {
        questions.push({
                type: 'confirm',
                name: 'keyPassword',
                message: "Does MongoDB require reset certificate key file password?(y/n): ",
                initial: true
            },
            {
                type: prev => prev ? 'text' : null,
                name: 'mongo_sslPEMKeyFilePassword',
                message: "Please enter MongoDB certificate key file password: "
            });
    }
    if (questions.length === 0) {
        console.info("MongoDB and SSL does not require a password.");
        return;
    }
    if(arg.length>1 && arg[1]){
        yamlObj['spring']['data']['mongodb']['password'] = CryptoJS.RC4.encrypt(arg[1], basic.getKey()).toString();
        let yamlStr = YAML.stringify(yamlObj, 5);
        fs.writeFileSync(WORK_DIR + '/application.yml', yamlStr);
    }else {
        (async () => {
            const response = await prompts(questions);
            if (response['mongoPassword'] && response['mongo_password'] !== "") {
                yamlObj['spring']['data']['mongodb']['password'] = CryptoJS.RC4.encrypt(response['mongo_password'], basic.getKey()).toString();
            }
            if (response['keyPassword'] && response['mongo_sslPEMKeyFilePassword'] !== "") {
                yamlObj['spring']['data']['mongodb']['sslPEMKeyFilePassword'] = CryptoJS.RC4.encrypt(response['mongo_sslPEMKeyFilePassword'], basic.getKey()).toString()
            }
            let yamlStr = YAML.stringify(yamlObj, 5);
            fs.writeFileSync(WORK_DIR + '/application.yml', yamlStr);
        })();
    }
};
