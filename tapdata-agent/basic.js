const fs = require("fs");
const path = require('path');
const readline = require('readline');
const sign = require("./util/AKSK").sign;
const WebSocket = require('ws');
const prompts = require('prompts');
const {exec} = require("child_process");
const si = require('systeminformation');
const {MongoClient} = require("mongodb");
const crypto = require("crypto");
const os = require("os");

module.exports = basic = {
    execSync: function (str) {//,conf
        /*
        let env = process.env;
        env.TAPDATA_HOME = conf.TAPDATA_HOME;
        env.TAPDATA_MONGO_URI = conf.uri;
        env.TAPDATA_PORT = conf.tapdata_port;
        env.API_SERVER_PORT = conf.api_server_port;
        env.TAPDATA_MONGO_CONN = conf.mongo_conn;
        env.TAPDATA_WORK_DIR = conf.WORK_DIR;
         */
        try {
            return require('child_process').execSync(str,{windowsHide:true});//, {'evn': env}
        } catch (e) {
            console.error(e);
        }
    },
    isEmpty:function(obj){//判断为空
        return typeof obj === "undefined" || obj === null || obj === "";
    },
    printHelp:function(){//显示帮助信息
        console.info("usage: tapdata [option] [subsystem]");
        console.info("Option:");
        console.info("  start                 Start Tapdata");
        console.info("  stop                  Stop Tapdata");
        console.info("  restart               Restart Tapdata");
        console.info("  status                Check the running status");
        console.info("  init                  Initialize Tapdata Configuration");
        console.info("  resetpassword         Reset MongoDB password or certificate key file password");
        console.info("  --version             Tapdata version");
        console.info("  register [filePath]   Register connector");
        console.info("  help                  Show this usage information");
        console.info("  ");
        console.info("Subsystem:");
        console.info("  frontend              Tapdata management portal");
        console.info("  backend               Data Processing Node");
        console.info("  apiserver             API Server Node");
        console.info("  mongodb               MongoDB");
        console.info("  deployConnector       Async deploy connectors in the directory 'connectors/dist'");
    },
    killPid:function(pid,conf,hideError){
        let str = '';
        if(conf._linux){
            str = "kill -9 "+pid;
        }
        if(conf._windows){
            str = "taskkill /f /pid " + pid;
        }
        try {
            require('child_process').execSync(str,{windowsHide:true});
        }catch (e) {
            if(hideError){
                return;
            }
            console.info(e);
            return false
        }
        return true
    },
    getExecStr(conf,str){
        if (conf._windows){
            str = str.replace(/\//g,"\\");
        }
        return str;
    },
    getKey(){
        return "tapdata2019_12_29";
    },
    isDebug(arguments){
        if(!arguments) return false;
        for(let i=0;i<arguments.length;i++){
            if(arguments[i] === '-debug')
                return true;
        }
        return false;
        //let args = arguments || [];
        //return args.indexOf('-debug') >= 0;
    },
    md5: function (str) {
        return !str ? '' : require('md5')(str);
    },
    getStatus:async function(conf,customMonitor){
        if(customMonitor) {
            for (let i = 0; i < customMonitor.length; i++) {
                customMonitor[i].status = 'stopped';
            }
        }
        const getProcesses = require("getprocesses");
        let list =[];
        if (conf._windows) {
            list = await require("./ps_win").getProcessesWindows();
        }else {
            list = await getProcesses.getProcesses();//snapshot('pid', 'name', 'path', 'cmdline');
        }
        let frontMPid = "";
        let frontPid = "";
        let backendPid = "";
        let apiMPid = "";
        let apiPid = "";
        let osiPid = "";
        let mongodbPid = "";
        let checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/server.js";// conf.TAPDATA_HOME + "/node_modules/pm2"; //conf.COMPONENTS_DIR +
        let checkMFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/server/index.js";
        let checkBackStr = conf.COMPONENTS_DIR + "/tapdata-agent";
        let checkMApiSrt = conf.COMPONENTS_DIR + "/apiserver/index";
        let checkApiSrt = conf.COMPONENTS_DIR + "/apiserver";
        let checkOsSrt = conf.COMPONENTS_DIR + "/os-monitor";
        let checkMongoDB = conf.COMPONENTS_DIR + "/mongod";
        if (conf._windows) {
            //checkFrontStr = conf.COMPONENTS_DIR + "/" + conf.FRONTEND_DIR_NAME + "/node_modules/pm2/lib/ProcessContainer.js";
            checkFrontStr = checkFrontStr.replace(/\//g, '\\');
            checkMFrontStr = checkMFrontStr.replace(/\//g, '\\');
            checkBackStr = checkBackStr.replace(/\//g, '\\');
            checkMApiSrt = checkMApiSrt.replace(/\//g, '\\');
            checkApiSrt = checkApiSrt.replace(/\//g, '\\');
            checkOsSrt = checkOsSrt.replace(/\//g, '\\');
            checkMongoDB = checkMongoDB.replace(/\//g, '\\');
        }
        if(basic.isTMJava(conf)){
            checkMFrontStr = path.join(conf.COMPONENTS_DIR,'tm.jar')
        }
        for (let i = 0; i < list.length; i++) {
            if (list[i].arguments.join(' ').indexOf(checkMFrontStr) >= 0) {
                frontMPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkFrontStr) >= 0) {
                frontPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkBackStr) >= 0) {
                backendPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkMApiSrt) >= 0) {
                apiMPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkApiSrt) >= 0 &&
                list[i].arguments.join(' ').indexOf("app") >= 0) {
                apiPid += " " + list[i].pid;
            }
            if (list[i].arguments.join(' ').indexOf(checkOsSrt) >= 0) {
                osiPid += " " + list[i].pid;
            }
            if ((list[i].command + ' ' + list[i].arguments.join(' ')).indexOf(checkMongoDB) >= 0) {
                mongodbPid += " " + list[i].pid;
            }
            if(customMonitor) {
                for (let z = 0; z < customMonitor.length; z++) {
                    if (
                        list[i].command.indexOf(customMonitor[z].command) >= 0 &&
                        (
                            this.isEmpty(customMonitor[z].arguments) ||
                            list[i].arguments.join(' ').indexOf(customMonitor[z].arguments) >= 0
                        )
                    ) {
                        customMonitor[z].status = 'running';
                    }
                }
            }
        }
        return {
            'frontMPid':frontMPid,
            'frontPid':frontPid,
            'backendPid':backendPid,
            'apiMPid':apiMPid,
            'apiPid':apiPid,
            'osiPid':osiPid,
            'mongodbPid': mongodbPid
        };
    },
    getWorkDir:function(arguments){
        const os = require("os");
        const path = require('path');
        const fs = require("fs");
        let TAPDATA_HOME = path.dirname(process.execPath);//process.cwd();//process.argv[1]
        if(TAPDATA_HOME === "/")TAPDATA_HOME = "";
        let WORK_DIR = '';
        for (let i = 0; i < arguments.length; i++) {
            if (arguments[i] === '--workDir') {
                WORK_DIR = arguments[i + 1];
                if(WORK_DIR.substring(WORK_DIR.length-1,WORK_DIR.length) === "/" || WORK_DIR.substring(WORK_DIR.length-1,WORK_DIR.length) === "\\"){
                    WORK_DIR = WORK_DIR.substring(0,WORK_DIR.length-1);
                }
                if(fs.existsSync(path.join(TAPDATA_HOME,'.workDir'))){
                    fs.unlinkSync(path.join(TAPDATA_HOME,'.workDir'));
                }
                fs.writeFileSync(path.join(TAPDATA_HOME,'.workDir'),WORK_DIR,'utf8');
                break;
            }
        }
        if (!WORK_DIR || WORK_DIR === '') {
            if(fs.existsSync(path.join(TAPDATA_HOME,'.workDir'))){
                WORK_DIR = fs.readFileSync(path.join(TAPDATA_HOME,'.workDir'),'utf8').toString();
            }
        }
        if (!WORK_DIR || WORK_DIR === '') {
            WORK_DIR = path.dirname(process.execPath);//os.homedir();
            //path.join(WORK_DIR, ".tapdata");
        }
        return WORK_DIR;
    },
    handleSendMsg:function(ws,type,msg){
        let obj = {
            'type': type,
            'timestamp': new Date().getTime(),
            'data': msg
        };
        obj.sign = basic.signString(JSON.stringify(obj));
        if(ws) {
            try {
                if(ws.send) {
                    ws.send(JSON.stringify(obj));
                }
            }catch(e){
                //console.error(e)
            }
            try {
                if(ws.write && obj.data.msg) {
                    ws.write(obj.data.msg);
                }
            }catch(e){
                //console.error(e)
            }
        }
    },
    signString:function(str){
        return basic.md5('tapdata' + str + '20200202');
    },
    deleteBackendStatus:function(conf){
        try {
            fs.unlinkSync(path.join(conf.WORK_DIR , '.agentStartMsg.json'));
        } catch (e) {
            //console.info(e)
        }
    },
    readBackendStatus:function(conf){
        try{
            let data = fs.readFileSync(path.join(conf.WORK_DIR , '.agentStartMsg.json'), 'utf-8');
            return JSON.parse(data)
        }catch(e){
        }
        return false
    },
    deleteTMStatus:function(conf){
        try {
            fs.unlinkSync(path.join(conf.WORK_DIR , '.tmStartMsg.json'));
        } catch (e) {
            //console.info(e)
        }
    },
    readTMStatus:function(conf){
        try{
            let data = fs.readFileSync(path.join(conf.WORK_DIR , '.tmStartMsg.json'), 'utf-8');
            return JSON.parse(data)
        }catch(e){
        }
        return false
    },
    dateFormat: function(fmt, date) {
    let ret;
    const opt = {
        "Y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "H+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "S+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
    };
    for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
            fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        }
    }
    return fmt;
    },
    downloadFile:function(downloadUrl,filename,writeFilename){
    return new Promise(function (resolve, reject) {
        console.info('Download ' + filename + '...');
        const url = downloadUrl + filename;
        const file = fs.createWriteStream(writeFilename);
        let http;
        if(downloadUrl.includes("https")) {
            http = require('https');
        }else{
            http = require('http');
        }
        http.get(url, (res, err) => {
            if (err){
                file.close();
                fs.unlinkSync(writeFilename);
                reject();
            }
            if (res.statusCode !== 200) {
                console.info(res);
                console.info('Download file failed');
                reject();
            }
            res.on('end', async () => {
                resolve();
            });
            let cur = 0;
            let len = parseInt(res.headers['content-length'], 10);
            let total = len / 1048576;
            let remaining = 0;
            let start = Date.now();
            let remainingMinutes = 0;
            let remainingSeconds = 0;
            let speed = 0;
            let speedStr = "";
            console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " MB");
            res.on('data',(m)=>{
                cur += m.length;
                readline.clearLine(process.stdout, 0);
                readline.moveCursor(process.stdout, 0,-1);
                speed = Math.floor(cur / (Date.now() - start + 1) / 1024 * 1000);
                speedStr = speed + " KB/s";
                if (speed > 1024) {
                    speedStr = (speed / 1024).toFixed(1) + " MB/s";
                }
                remaining = Math.floor((len - cur) / (cur / (Date.now() - start + 1)) / 1000);
                remainingMinutes = Math.floor(remaining / 60);
                remainingSeconds = remaining - 60 * remainingMinutes;
                console.info( "Downloading " + (100.0 * cur / len).toFixed(2) + "% " + (cur / 1048576).toFixed(2) + " MB" + ", Total size: " + total.toFixed(2) + " MB, avg speed: " + speedStr + ", remaining time: " + remainingMinutes + "m " + remainingSeconds + "s     ");
            });
            file.on('finish', () => {
                file.close();
            }).on('error', (err) => {
                try {
                    fs.unlinkSync(writeFilename);
                } catch (e) {
                }
            });
            res.pipe(file);
        });
    });
    },
    connWs: function(conf){
        let urls = conf.backend_url.split(',');
        for (let i = 0; i < urls.length; i++) {
            if (urls[i].indexOf('https://') > -1){
                urls[i] = 'wss://' + urls[i].split('/api')[0].replace('https://', '');
            }
            else {
                urls[i] = 'ws://' + urls[i].split('/api')[0].replace('http://', '');
            }
            /*
            if(urls[i].indexOf('cloud.tapdata.net') > -1){
                urls[i] = urls[i].replace('/console', '');
            }
             */
        }
        let url = urls[GetRandomNum(urls.length - 1)];
        if(conf.accessKey && conf.accessKey !== '') {
            url = url.replace('/tm_xdfwdsax','/tm');
            const u = url+'/ws/cluster/?access_token=PM51PWywjurdBe8KwXNpUkbB4yceUhOGwyREly6AmxF0Pr2b1qOynCdX06CTpk3G';
            url = sign(conf.accessKey, conf.secretKey, u);
        }
        let ws;
        if(url.indexOf('wss://') > -1){
            ws = new WebSocket(url, [], {handshakeTimeout: 2000,rejectUnauthorized: false});
        }else {
            ws = new WebSocket(url, [], {handshakeTimeout: 2000});//'ws://localhost:3000',[],{handshakeTimeout:500}
        }
        return ws;
    },
    exit: async function(){
        const questions = [
            {
                type: 'text',
                name: 'args',
                message: 'Enter to confirm'
            }
        ]
        const response = await prompts(questions);
    },
    isTMJava: function(conf){
        return fs.existsSync(path.join(conf.COMPONENTS_DIR,'tm.jar'))
    },
    sleep: function (ms){
        return new Promise(resolve => setTimeout(resolve,ms));
    },
    getNetStat: function(pid){
        return new Promise((resolve, reject) => {
            pid = pid.trim()
            if(!pid || pid === ''){
                resolve([])
                return;
            }
            si.networkConnections((data)=>{
                let rsData = {};
                let rs = [];
                for(let x in data){
                    try{
                        if(data[x].pid && data[x].pid.toString() === pid && data[x].state === 'ESTABLISHED'){
                            let ip = data[x].peeraddress + ':' + data[x].peerport;
                            if(!rsData[ip]){
                                rsData[ip] = 0
                            }
                            rsData[ip]++;
                        }
                    }catch (e) {
                    }
                }
                for(let x in rsData){
                    rs.push({numbers:rsData[x],ip:x})
                }
                resolve(rs);
            })
        });
    },
    getAccesscode : async function(conf){
        console.info('begin get AccessCode');
        let client = {};
        try {
            let mongoUri = conf.uri;
            let mongoOpts = {
                'serverSelectionTimeoutMS':3000,
                'useNewUrlParser': true,
                'useUnifiedTopology': true
            };
            if(conf.ssl === 'true' && !this.isEmpty(conf.CA_FILENAME) && !this.isEmpty(conf.CERTKEY_FILENAME)){
                mongoOpts['sslValidate'] = true;
                const ca = [fs.readFileSync(path.join(conf.WORK_DIR, conf.CERT_DIR, conf.CA_FILENAME))];
                const cert = fs.readFileSync(path.join(conf.WORK_DIR, conf.CERT_DIR, conf.CERTKEY_FILENAME));
                mongoOpts['ssl'] = true;
                mongoOpts['sslCA'] = ca;
                mongoOpts['sslCert'] = cert;
                mongoOpts['sslKey'] = cert;
                mongoOpts['checkServerIdentity'] = false;
            }
            if(!this.isEmpty(conf.sslPEMKeyFilePassword)){
                mongoOpts['sslPass'] = conf.sslPEMKeyFilePassword;
            }
            client = new MongoClient(mongoUri, mongoOpts);
            let user;
            await client.connect();
            const db = client.db();
            let haveUser = false;
            let now = new Date().getTime();
            while (true){
                user = await db.collection("user").findOne({"email":"<EMAIL>"});
                if(!user){
                    user = await db.collection("User").findOne({"email":"<EMAIL>"});
                }
                if(!user){
                    if(new Date().getTime() - now > 30000){
                        break;
                    }
                    await this.sleep(1000);
                }else{
                    haveUser = true;
                    break;
                }
            }
            if(!haveUser){
                console.info("user is not exists.");
                return;
            }
            return user.accesscode;
        }catch (e) {
            if(e){
                console.info(e);
            }
        } finally {
            if(client.close){
                client.close();
            }
        }
    },
    deploy: async function(conf,connect,reportInfo,bUrl,pdkFilesName,needMoveConn){
        let mongoUri = conf.uri;
        let mongoOpts = {
            'useNewUrlParser': true,
            'useUnifiedTopology': true
        };
        if(conf.ssl === 'true' && !this.isEmpty(conf.CA_FILENAME) && !this.isEmpty(conf.CERTKEY_FILENAME)){
            mongoOpts['sslValidate'] = true;
            try {
                const ca = [fs.readFileSync(path.join(conf.WORK_DIR, conf.CERT_DIR, conf.CA_FILENAME))];
                const cert = fs.readFileSync(path.join(conf.WORK_DIR, conf.CERT_DIR, conf.CERTKEY_FILENAME));
                mongoOpts['ssl'] = true;
                mongoOpts['sslCA'] = ca;
                mongoOpts['sslCert'] = cert;
                mongoOpts['sslKey'] = cert;
                mongoOpts['checkServerIdentity'] = false;
            }catch (e) {
                console.info(e);
                throw(e);
            }
        }
        if(!this.isEmpty(conf.sslPEMKeyFilePassword)){
            mongoOpts['sslPass'] = conf.sslPEMKeyFilePassword;
        }
        const client = new MongoClient(mongoUri, mongoOpts);
        let user;
        try {
            await client.connect();
            const db = client.db();
            let haveUser = false;
            let now = new Date().getTime();
            while (true){
                user = await db.collection("user").findOne({"email":"<EMAIL>"});
                if(!user){
                    user = await db.collection("User").findOne({"email":"<EMAIL>"});
                }
                if(!user){
                    if(new Date().getTime() - now > 30000){
                        break;
                    }
                    await this.sleep(1000);
                }else{
                    haveUser = true;
                    break;
                }
            }
            if(!haveUser){
                connect.write('deploy error:user is not exists.',reportInfo);
                console.info("deploy error:user is not exists.");
                return;
            }
            for(let x in pdkFilesName){
                try{
                    await deployConn(connect,arguments,conf,user,bUrl,pdkFilesName[x],reportInfo,needMoveConn, x);
                }catch (e) {
                    console.info('deploy ',pdkFilesName[x],' fail.')
                }
            }
            console.info('deployed connector.');
        }catch (e) {
            if(e){
                console.info(e);
            }
        } finally {
            client.close();
        }
    },
    decodeToken(token){
        let key = '5fa25b06ee34581d';
        let iv = '5fa25b06ee34581d';
        try{
            let tokenStr = decrypt(key, iv, token);
            return JSON.parse(tokenStr);
        }catch (e) {
            console.error(e);
            return {};
        }
    },
    readBigFileMD5(path){
        return new Promise((resolve, reject)=>{
            const stream = fs.createReadStream(path);
            const hash = crypto.createHash('md5');
            stream.on('data',(chunk)=>{
                hash.update(chunk,'utf-8');
            })
            stream.on('end',()=>{
                const md5Sum = hash.digest('hex');
                resolve(md5Sum);
            });
            stream.on('error',(err)=>{
                reject(err)
            })
        })
    },
    isLinux(){
        const _platform = os.platform();
        return (_platform === 'linux');
    }
};

function GetRandomNum(Range) {
    let Rand = Math.random();
    return (Math.round(Rand * Range));
}


function deployConn(connect,arguments,conf,user,bUrl,pdkFile,reportInfo,needMoveConn, index){
    return new Promise(function (resolve, reject) {
        connect.write('  > deploying connector :' + pdkFile + ` at ${Number(index) + 1} ....`, reportInfo);
        let file = pdkFile;
        if(needMoveConn){
            file = path.join(conf.TAPDATA_HOME,'connectors','dist',pdkFile);
        }
        let execString = [
            '-jar',
            path.join(conf.TAPDATA_HOME, 'lib', 'pdk-deploy.jar'),
            'register',
            '-a',
            user.accesscode,
            '-t',
            bUrl,
            file
        ]
        if (basic.isDebug(arguments)) {
            connect.write('deploy command:' + 'java ' + execString.join(' '), reportInfo);
            console.info('deploy connector...');
        }
        let stdio = ['ignore', 'pipe', 'pipe'];
        if (basic.isDebug(arguments)) {
            stdio = ['ignore', 'pipe', 'pipe'];
        }
        let subProcess = require('child_process').spawn('java', execString, {
            'cwd': path.join(conf.TAPDATA_HOME, 'lib'),
            'stdio': stdio,
            'windowsHide': true,
            'detached': true
        });
        let n = 0;
        let haveErr = false;
        subProcess.stdout.on('data', (msg) => {
            if (msg.includes('[ERROR]') || msg.includes('        at')) {
                haveErr = true;
                connect.write(msg.toString(), reportInfo);
                console.info('deploy error:' + msg.toString());
                n = 1;
            } else if (n > 0 && n < 21) {
                connect.write(msg.toString(), reportInfo);
                console.info('deploy error:' + msg.toString());
                n++;
            }
        });
        subProcess.stderr.on('data', (msg) => {
            haveErr = true;
            connect.write('deploy error:' + msg.toString(), reportInfo);
            console.info('deploy debug:' + msg.toString());
        });
        subProcess.unref();
        subProcess.on('close', () => {
            //deployed = true;
            if(!haveErr){
                if(needMoveConn){
                    moveConn(conf,pdkFile);
                }
                connect.write('    deployed connector :' + pdkFile, reportInfo);
                console.info('deployed connector.');
                resolve();
            }else{
                reject();
            }
        })
    })
}

function moveConn(conf,file){
    try {
        fs.accessSync(path.join(conf.TAPDATA_HOME,'connectors','bak'), fs.constants.F_OK);
    } catch (e) {
        fs.mkdirSync(path.join(conf.TAPDATA_HOME,'connectors','bak'));
    }
    try{
        fs.renameSync(path.join(conf.TAPDATA_HOME,'connectors','dist',file), path.join(conf.TAPDATA_HOME,'connectors','bak',file));
    }catch (e) {
    }
}

function decrypt(key, iv, crypted) {
    crypted = new Buffer.from(crypted, 'base64').toString('binary');
    let decipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
    let decoded = decipher.update(crypted, 'binary', 'utf8');
    decoded += decipher.final('utf8');
    let rs = new Buffer.from(decoded,'base64').toString()
    return rs;
};