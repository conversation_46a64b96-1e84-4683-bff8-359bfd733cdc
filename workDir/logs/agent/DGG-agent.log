[INFO ] 2024-09-20 14:16:33.693  [main] Application - disabledAlgorithms [SSLv3, TLSv1, TLSv1.1, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, include jdk.disabled.namedCurves]->[SSLv3, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, include jdk.disabled.namedCurves]
[INFO ] 2024-09-20 14:16:33.777  [main] Application - Starting application, code version 2024-09-20T06:08:34Z
[INFO ] 2024-09-20 14:16:34.002  [background-preinit] Version - HV000001: Hibernate Validator 6.0.7.Final
[INFO ] 2024-09-20 14:16:34.179  [main] Application - Starting Application on liangjiaweideMacBook-Pro-2.local with PID 2839 (/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/iengine-app/target/classes started by liang<PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/DGG-oss)
[INFO ] 2024-09-20 14:16:34.180  [main] Application - No active profile set, falling back to default profiles: default
[WARN ] 2024-09-20 14:16:34.228  [background-preinit] Jackson2ObjectMapperBuilder - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
[INFO ] 2024-09-20 14:16:34.242  [main] AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@71c27ee8: startup date [Fri Sep 20 14:16:34 CST 2024]; root of context hierarchy
[INFO ] 2024-09-20 14:16:35.575  [main] ConnectorManager - DGG_MONGO_CONN env variable does not set, will use default 
[INFO ] 2024-09-20 14:16:35.575  [main] ConnectorManager - ssl env variable does not set, will use default false
[INFO ] 2024-09-20 14:16:35.575  [main] ConnectorManager - cloud_accessCode env variable does not set, will use default "".
[INFO ] 2024-09-20 14:16:35.576  [main] ConnectorManager - cloud_retryTime env variable does not set, will use default 3
[INFO ] 2024-09-20 14:16:35.577  [main] CloudSignUtil - ak/sk needSign false, accessKey null, secretKey null
[INFO ] 2024-09-20 14:16:35.579  [main] ConnectorManager - mode env variable does not set, will use default cluster
[INFO ] 2024-09-20 14:16:35.611  [main] ConnectorManager - 
Initialed variable
 - mongoURI: mongodb://wim:******@localhost:27017/tapdv13?authSource=admin
 - ssl: false
 - sslCA: 
 - sslPEM: 
 - mongodbConnParams: 
 - baseURLs: [http://localhost:3000/api/]
 - accessCode: 
 - restRetryTime: 3
 - mode: cluster
 - app_type: DAAS
 - process id: wim_flow_engine
 - job tags: null
 - region: null
 - zone: null
 - worker dir: ./workDir
[INFO ] 2024-09-20 14:16:35.904  [main] cluster - Cluster created with settings {hosts=[localhost:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms', maxWaitQueueSize=500}
[INFO ] 2024-09-20 14:16:35.949  [cluster-ClusterId{value='66ed134318626e0b17a38a0d', description='null'}-localhost:27017] connection - Opened connection [connectionId{localValue:1, serverValue:317}] to localhost:27017
[INFO ] 2024-09-20 14:16:35.954  [cluster-ClusterId{value='66ed134318626e0b17a38a0d', description='null'}-localhost:27017] cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, version=ServerVersion{versionList=[4, 2, 24]}, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3603958, setName='replset', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, lastWriteDate=Fri Sep 20 14:16:35 CST 2024, lastUpdateTimeNanos=1176255681111291}
[INFO ] 2024-09-20 14:16:36.054  [main] ConnectorManager - Available processors number: 12
[INFO ] 2024-09-20 14:16:36.059  [main] ConnectorManager - Java class path: /Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/cat.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/charsets.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/crs-agent.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/dnsns.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/jaccess.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/localedata.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/nashorn.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/sunec.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/ext/zipfs.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jce.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jfr.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/jsse.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/management-agent.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/resources.jar:/Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home/jre/lib/rt.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/iengine-app/target/classes:/Users/<USER>/IdeaProjects/DGG-oss/DGG-enterprise/iengine-enterprise/target/classes:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/api/target/classes:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.0.4.RELEASE/spring-test-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/io/DGG/DGG-storage-module/1.0-SNAPSHOT/DGG-storage-module-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/DGG/DGG-pdk-runner/1.9-SNAPSHOT/DGG-pdk-runner-1.9-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/DGG/DGG-pdk-api/1.3.9-SNAPSHOT/DGG-pdk-api-1.3.9-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.5.0/ehcache-3.5.0.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/2.3.0/jaxb-runtime-2.3.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/2.3.0/jaxb-core-2.3.0.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/2.3.0/txw2-2.3.0.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/3.0.5/istack-commons-runtime-3.0.5.jar:/Users/<USER>/.m2/repository/org/jvnet/staxex/stax-ex/1.7.8/stax-ex-1.7.8.jar:/Users/<USER>/.m2/repository/com/sun/xml/fastinfoset/FastInfoset/1.2.13/FastInfoset-1.2.13.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.5/jjwt-jackson-0.11.5.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.9/commons-codec-1.9.jar:/Users/<USER>/.m2/repository/io/DGG/modules-api/1.0-SNAPSHOT/modules-api-1.0-SNAPSHOT.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG-common-lib/plugin-kit/DGG-api/target/classes:/Users/<USER>/.m2/repository/com/github/luben/zstd-jni/1.5.2-5/zstd-jni-1.5.2-5.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.21/commons-compress-1.21.jar:/Users/<USER>/.m2/repository/org/rocksdb/rocksdbjni/7.3.1/rocksdbjni-7.3.1.jar:/Users/<USER>/.m2/repository/io/DGG/async-tools-module/1.0-SNAPSHOT/async-tools-module-1.0-SNAPSHOT.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/proxy-client-module/target/classes:/Users/<USER>/.m2/repository/io/DGG/websocket-client-module/1.0-SNAPSHOT/websocket-client-module-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.22.Final/netty-all-4.1.22.Final.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/observable-module/target/classes:/Users/<USER>/.m2/repository/net/openhft/chronicle-queue/5.21.91/chronicle-queue-5.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-core/2.21.91/chronicle-core-2.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-analytics/2.21ea0/chronicle-analytics-2.21ea0.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-bytes/2.21.89/chronicle-bytes-2.21.89.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-wire/2.21.91/chronicle-wire-2.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/compiler/2.21ea80/compiler-2.21ea80.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-threads/2.21.86/chronicle-threads-2.21.86.jar:/Users/<USER>/.m2/repository/net/openhft/affinity/3.21ea5/affinity-3.21ea5.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.4/commons-cli-1.4.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/milestone-module/target/classes:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/skip-error-event-module/target/classes:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/test-run-module/target/classes:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/deduction-module/target/classes:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/custom-sql-filter-module/target/classes:/Users/<USER>/.m2/repository/io/DGG/service-skeleton-module/1.0-SNAPSHOT/service-skeleton-module-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/DGG/script-engine-module/1.0-SNAPSHOT/script-engine-module-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/graalvm/js/js/21.3.0/js-21.3.0.jar:/Users/<USER>/.m2/repository/org/graalvm/regex/regex/21.3.0/regex-21.3.0.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-api/21.3.0/truffle-api-21.3.0.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/graal-sdk/21.3.0/graal-sdk-21.3.0.jar:/Users/<USER>/.m2/repository/com/ibm/icu/icu4j/69.1/icu4j-69.1.jar:/Users/<USER>/.m2/repository/org/graalvm/js/js-scriptengine/21.3.0/js-scriptengine-21.3.0.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.0.0.RELEASE/spring-boot-starter-web-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.0.0.RELEASE/spring-boot-starter-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.0.0.RELEASE/spring-boot-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.0.0.RELEASE/spring-boot-autoconfigure-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.0.0.RELEASE/spring-boot-starter-json-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.4/jackson-datatype-jdk8-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.4/jackson-module-parameter-names-2.9.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.0.0.RELEASE/spring-boot-starter-tomcat-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.28/tomcat-embed-core-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.28/tomcat-embed-el-8.5.28.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.28/tomcat-embed-websocket-8.5.28.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.7.Final/hibernate-validator-6.0.7.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.4.RELEASE/spring-web-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.0.4.RELEASE/spring-webmvc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.0.4.RELEASE/spring-aop-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.0.4.RELEASE/spring-expression-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.2.4.Final/hibernate-validator-5.2.4.Final.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.3.4/classmate-1.3.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.9.4/jackson-dataformat-yaml-2.9.4.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.19/snakeyaml-1.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.12.6/jackson-core-2.12.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/5.0.4.RELEASE/spring-websocket-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.0.4.RELEASE/spring-context-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.0.4.RELEASE/spring-core-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.0.4.RELEASE/spring-jcl-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.0.4.RELEASE/spring-messaging-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.0.4.RELEASE/spring-beans-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.9/commons-lang3-3.9.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.0.0.RELEASE/spring-boot-starter-jdbc-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/2.7.8/HikariCP-2.7.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.0.4.RELEASE/spring-jdbc-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.0.4.RELEASE/spring-tx-5.0.4.RELEASE.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.3/jsqlparser-4.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-mongodb/2.10.0/log4j-mongodb-2.10.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongo-java-driver/3.9.1/mongo-java-driver-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-1.2-api/2.17.1/log4j-1.2-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/2.0.0.RELEASE/spring-boot-starter-data-mongodb-2.0.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver/3.6.3/mongodb-driver-3.6.3.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/3.6.3/bson-3.6.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.6.3/mongodb-driver-core-3.6.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/2.0.5.RELEASE/spring-data-mongodb-2.0.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.0.5.RELEASE/spring-data-commons-2.0.5.RELEASE.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/iengine-common/target/classes:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.5/httpclient-4.5.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.9/httpcore-4.4.9.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.12.6.1/jackson-databind-2.12.6.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.12.6/jackson-annotations-2.12.6.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.60/bcprov-jdk15on-1.60.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.12.6/jackson-datatype-jsr310-2.12.6.jar:/Users/<USER>/.m2/repository/org/json/json/20180813/json-20180813.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar:/Users/<USER>/.m2/repository/commons-net/commons-net/3.6/commons-net-3.6.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.jar:/Users/<USER>/.m2/repository/com/github/albfernandez/juniversalchardet/2.3.0/juniversalchardet-2.3.0.jar:/Users/<USER>/.m2/repository/org/apache/avro/avro/1.9.0/avro-1.9.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/30.1.1-android/guava-30.1.1-android.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-compat-qual/2.5.5/checker-compat-qual-2.5.5.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.5.1/error_prone_annotations-2.5.1.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka_2.12/2.3.1/kafka_2.12-2.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-scala_2.12/2.9.4/jackson-module-scala_2.12-2.9.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-paranamer/2.9.4/jackson-module-paranamer-2.9.4.jar:/Users/<USER>/.m2/repository/com/thoughtworks/paranamer/paranamer/2.8/paranamer-2.8.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-csv/2.9.4/jackson-dataformat-csv-2.9.4.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/Users/<USER>/.m2/repository/com/yammer/metrics/metrics-core/2.2.0/metrics-core-2.2.0.jar:/Users/<USER>/.m2/repository/com/typesafe/scala-logging/scala-logging_2.12/3.9.0/scala-logging_2.12-3.9.0.jar:/Users/<USER>/.m2/repository/com/101tec/zkclient/0.11/zkclient-0.11.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.14/zookeeper-3.4.14.jar:/Users/<USER>/.m2/repository/com/github/spotbugs/spotbugs-annotations/3.1.9/spotbugs-annotations-3.1.9.jar:/Users/<USER>/.m2/repository/org/apache/yetus/audience-annotations/0.5.0/audience-annotations-0.5.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-clients/2.3.1/kafka-clients-2.3.1.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.6.0/lz4-java-1.6.0.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.7.3/snappy-java-1.1.7.3.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-streams/2.3.1/kafka-streams-2.3.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-json/2.3.1/connect-json-2.3.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-api/2.3.1/connect-api-2.3.1.jar:/Users/<USER>/.m2/repository/org/quartz-scheduler/quartz/2.2.1/quartz-2.2.1.jar:/Users/<USER>/.m2/repository/c3p0/c3p0/0.9.1.1/c3p0-0.9.1.1.jar:/Users/<USER>/.m2/repository/org/quartz-scheduler/quartz-jobs/2.2.1/quartz-jobs-2.2.1.jar:/Users/<USER>/.m2/repository/com/hankcs/hanlp/portable-1.5.3/hanlp-portable-1.5.3.jar:/Users/<USER>/.m2/repository/com/hankcs/nlp/hanlp-lucene-plugin/1.1.2/hanlp-lucene-plugin-1.1.2.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/5.1.0/lucene-queryparser-5.1.0.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analyzers-common/5.1.0/lucene-analyzers-common-5.1.0.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.20/lombok-1.18.20.jar:/Users/<USER>/.m2/repository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/Users/<USER>/.m2/repository/com/vividsolutions/jts/1.13/jts-1.13.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/2.6.2/caffeine-2.6.2.jar:/Users/<USER>/.m2/repository/com/github/os72/protobuf-dynamic/1.0.1/protobuf-dynamic-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.19.1/protobuf-java-3.19.1.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java-util/3.19.1/protobuf-java-util-3.19.1.jar:/Users/<USER>/.m2/repository/org/voovan/voovan-framework/4.3.8/voovan-framework-4.3.8.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/manager/tm-sdk/target/classes:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_httpserver/0.12.0/simpleclient_httpserver-0.12.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.12.0/simpleclient-0.12.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.12.0/simpleclient_tracer_otel-0.12.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.12.0/simpleclient_tracer_common-0.12.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.12.0/simpleclient_tracer_otel_agent-0.12.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.12.0/simpleclient_common-0.12.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_hotspot/0.12.0/simpleclient_hotspot-0.12.0.jar:/Users/<USER>/.m2/repository/com/github/oshi/oshi-core/5.8.3/oshi-core-5.8.3.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.10.0/jna-5.10.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna-platform/5.10.0/jna-platform-5.10.0.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG-common-lib/plugin-kit/DGG-common/target/classes:/Users/<USER>/IdeaProjects/DGG-oss/DGG/manager/tm-common/target/classes:/Users/<USER>/.m2/repository/com/DGG/common/2.2.2/common-2.2.2.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/.m2/repository/ognl/ognl/3.1.26/ognl-3.1.26.jar:/Users/<USER>/.m2/repository/io/github/openlg/graphlib/1.0.1/graphlib-1.0.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/4.7.0/mongodb-driver-sync-4.7.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.3/commons-collections4-4.3.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.5.2/hutool-all-5.5.2.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.7.15/hutool-core-5.7.15.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.9.1/okhttp-4.9.1.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/2.8.0/okio-2.8.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.4.0/kotlin-stdlib-1.4.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.4.0/kotlin-stdlib-common-1.4.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.9/commons-text-1.9.jar:/Users/<USER>/.m2/repository/com/hazelcast/hazelcast/5.2.1/hazelcast-5.2.1.jar:/Users/<USER>/IdeaProjects/DGG-oss/hazelcast-persistence/target/classes:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.12.6/jackson-dataformat-xml-2.12.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.9.4/jackson-module-jaxb-annotations-2.9.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2/stax2-api-4.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.0.1/woodstox-core-6.0.1.jar:/Users/<USER>/.m2/repository/org/mapdb/mapdb/3.0.8/mapdb-3.0.8.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections-api/12.0.0.M3/eclipse-collections-api-12.0.0.M3.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections/12.0.0.M3/eclipse-collections-12.0.0.M3.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections-forkjoin/12.0.0.M3/eclipse-collections-forkjoin-12.0.0.M3.jar:/Users/<USER>/.m2/repository/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0.jar:/Users/<USER>/.m2/repository/org/mapdb/elsa/3.0.0-M5/elsa-3.0.0-M5.jar:/Users/<USER>/.m2/repository/it/unimi/dsi/fastutil/8.5.13/fastutil-8.5.13.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/validator/target/classes:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.5/commons-csv-1.5.jar:/Users/<USER>/.m2/repository/org/kopitubruk/util/JSONUtil/1.10.2/JSONUtil-1.10.2.jar:/Users/<USER>/.m2/repository/org/simplejavamail/simple-java-mail/5.0.3/simple-java-mail-5.0.3.jar:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.1/javax.mail-1.6.1.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/repository/com/github/bbottema/emailaddress-rfc2822/1.0.1/emailaddress-rfc2822-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.1/jsr305-3.0.1.jar:/Users/<USER>/.m2/repository/org/samba/jcifs/jcifs/1.3.17/jcifs-1.3.17.jar:/Users/<USER>/.m2/repository/io/DGG/error-code-core/1.0-SNAPSHOT/error-code-core-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/DGG/pdk-error-code/1.0-SNAPSHOT/pdk-error-code-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/io/DGG/error-code-scanner/1.0-SNAPSHOT/error-code-scanner-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.10/reflections-0.9.10.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.19.0-GA/javassist-3.19.0-GA.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/annotations/2.0.1/annotations-2.0.1.jar:/Users/<USER>/IdeaProjects/DGG-oss/DGG/iengine/modules/task-resource-supervisor-module/target/classes:/Users/<USER>/.m2/repository/io/DGG/jython-standalone/2.7.3/jython-standalone-2.7.3.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/8.11.2/lucene-core-8.11.2.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2023.3/captureAgent/debugger-agent.jar.
 Agent version: -
[INFO ] 2024-09-20 14:16:36.231  [main] connection - Opened connection [connectionId{localValue:2, serverValue:318}] to localhost:27017
[INFO ] 2024-09-20 14:16:36.276  [main] ConnectorManager - Login params: accessCode=3324cfdf-7d3e-4792-bd32-571638d4562f, endpoint=[http://localhost:3000/api/]
[INFO ] 2024-09-20 14:16:36.326  [main] Version - flow engine version: 
[INFO ] 2024-09-20 14:16:37.330  [main] Reflections - Reflections took 243 ms to scan 29 urls, producing 473 keys and 2051 values 
[INFO ] 2024-09-20 14:16:37.535  [main] Reflections - Reflections took 114 ms to scan 25 urls, producing 429 keys and 1714 values 
[INFO ] 2024-09-20 14:16:37.723  [main] Reflections - Reflections took 162 ms to scan 29 urls, producing 473 keys and 2051 values 
[INFO ] 2024-09-20 14:16:37.749  [main] SettingService - [Setting] - Loading DGG settings...
[java.lang.Thread.getStackTrace(Thread.java:1564), io.DGG.common.SettingService.loadSettings(SettingService.java:40), io.DGG.Schedule.ConnectorManager.init(ConnectorManager.java:212), sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method), sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62), sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.lang.reflect.Method.invoke(Method.java:498), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:369), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:312), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:135), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:423), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1702), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:583), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:502), org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:312), org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:228), org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:310), org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200), org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:760), org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:868), org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549), org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752), org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:388), org.springframework.boot.SpringApplication.run(SpringApplication.java:327), io.DGG.Application.main(Application.java:131)]
[INFO ] 2024-09-20 14:16:39.809  [main] Reflections - Reflections took 140 ms to scan 29 urls, producing 473 keys and 2051 values 
[INFO ] 2024-09-20 14:16:39.967  [main] Reflections - Reflections took 129 ms to scan 29 urls, producing 473 keys and 2051 values 
[INFO ] 2024-09-20 14:16:40.328  [main] TransformerManager - Transformer init variables
 - process id: wim_flow_engine
 - job tags: 
 - region: 
 - zone: 
 - worker dir: ./workDir
[INFO ] 2024-09-20 14:16:40.389  [main] HazelcastUtil - -- listing properties --
hazelcast.operation.call.timeout.millis=300000
hazelcast.logging.type=log4j2

[INFO ] 2024-09-20 14:16:40.432  [main] AddressPicker - [LOCAL] [dev] [5.2.1] Interfaces is disabled, trying to pick one address from TCP-IP config addresses: []
[WARN ] 2024-09-20 14:16:40.433  [main] AddressPicker - [LOCAL] [dev] [5.2.1] Could not find a matching address to start with! Picking one of non-loopback addresses.
[INFO ] 2024-09-20 14:16:40.467  [main] logo - [************]:5701 [dev] [5.2.1] 
	+       +  o    o     o     o---o o----o o      o---o     o     o----o o--o--o
	+ +   + +  |    |    / \       /  |      |     /         / \    |         |   
	+ + + + +  o----o   o   o     o   o----o |    o         o   o   o----o    |   
	+ +   + +  |    |  /     \   /    |      |     \       /     \       |    |   
	+       +  o    o o       o o---o o----o o----o o---o o       o o----o    o   
[INFO ] 2024-09-20 14:16:40.467  [main] system - [************]:5701 [dev] [5.2.1] Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
[INFO ] 2024-09-20 14:16:40.467  [main] system - [************]:5701 [dev] [5.2.1] Hazelcast Platform 5.2.1 (20240902 - 76bc184) starting at [************]:5701
[INFO ] 2024-09-20 14:16:40.467  [main] system - [************]:5701 [dev] [5.2.1] Cluster name: dev
[INFO ] 2024-09-20 14:16:40.467  [main] system - [************]:5701 [dev] [5.2.1] Integrity Checker is disabled. Fail-fast on corrupted executables will not be performed. For more information, see the documentation for Integrity Checker.
[INFO ] 2024-09-20 14:16:40.467  [main] system - [************]:5701 [dev] [5.2.1] Jet is enabled
[INFO ] 2024-09-20 14:16:40.972  [main] security - [************]:5701 [dev] [5.2.1] Enable DEBUG/FINE log level for log category com.hazelcast.system.security  or use -Dhazelcast.security.recommendations system property to see 🔒 security recommendations and the status of current config.
[INFO ] 2024-09-20 14:16:41.022  [main] Node - [************]:5701 [dev] [5.2.1] Using TCP/IP discovery
[WARN ] 2024-09-20 14:16:41.023  [main] CPSubsystem - [************]:5701 [dev] [5.2.1] CP Subsystem is not enabled. CP data structures will operate in UNSAFE mode! Please note that UNSAFE mode will not provide strong consistency guarantees.
[INFO ] 2024-09-20 14:16:41.213  [main] JetServiceBackend - [************]:5701 [dev] [5.2.1] Setting number of cooperative threads and default parallelism to 12
[INFO ] 2024-09-20 14:16:41.221  [main] Diagnostics - [************]:5701 [dev] [5.2.1] Diagnostics disabled. To enable add -Dhazelcast.diagnostics.enabled=true to the JVM arguments.
[INFO ] 2024-09-20 14:16:41.229  [main] LifecycleService - [************]:5701 [dev] [5.2.1] [************]:5701 is STARTING
[INFO ] 2024-09-20 14:16:41.250  [main] ClusterService - [************]:5701 [dev] [5.2.1] 

Members {size:1, ver:1} [
	Member [************]:5701 - 8c4c39d5-3faf-4fd2-a430-f5d21f97aa9a this
]

[INFO ] 2024-09-20 14:16:41.258  [main] JobCoordinationService - [************]:5701 [dev] [5.2.1] Jet started scanning for jobs
[INFO ] 2024-09-20 14:16:41.260  [main] LifecycleService - [************]:5701 [dev] [5.2.1] [************]:5701 is STARTED
[INFO ] 2024-09-20 14:16:41.312  [main] ThreadPoolTaskScheduler - Initializing ExecutorService  'taskScheduler'
[INFO ] 2024-09-20 14:16:41.315  [main] DGGTaskScheduler - [Task scheduler] instance no: wim_flow_engine
[INFO ] 2024-09-20 14:16:41.395  [main] cluster - Cluster created with settings {hosts=[localhost:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms', maxWaitQueueSize=500}
[INFO ] 2024-09-20 14:16:41.417  [cluster-ClusterId{value='66ed134918626e0b17a38a0e', description='null'}-localhost:27017] connection - Opened connection [connectionId{localValue:3, serverValue:319}] to localhost:27017
[INFO ] 2024-09-20 14:16:41.420  [cluster-ClusterId{value='66ed134918626e0b17a38a0e', description='null'}-localhost:27017] cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, version=ServerVersion{versionList=[4, 2, 24]}, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=1001459, setName='replset', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, lastWriteDate=Fri Sep 20 14:16:40 CST 2024, lastUpdateTimeNanos=1176261145462250}
[INFO ] 2024-09-20 14:16:41.504  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=wim_flow_engine&access_token=d0c5fbc533ae4b3f9114bd27fd8a5fc76b5fcade395b4850a23e9a27bb933f2b&singletonTag=75ea929f-8af1-4ddd-9b64-8c5b8e58c9c1
[INFO ] 2024-09-20 14:16:41.951  [main] AnnotationMBeanExporter - Registering beans for JMX exposure on startup
[INFO ] 2024-09-20 14:16:41.982  [main] Application - Started Application in 8.141 seconds (JVM running for 10.767)
[INFO ] 2024-09-20 14:16:41.987  [main] PDK - Application [Looking for Aspect annotations...]
[INFO ] 2024-09-20 14:16:42.269  [main] Reflections - Reflections took 278 ms to scan 29 urls, producing 473 keys and 2051 values 
[INFO ] 2024-09-20 14:16:42.274  [main] PDK - Application [Looking for Aspect annotations takes 286]
[INFO ] 2024-09-20 14:16:42.275  [main] StartResultUtil - Write start result to file: ./workDir/.agentStartMsg.json
  {"msg":"","version":"-","status":"ok"}
[INFO ] 2024-09-20 14:16:43.039  [main] PDK - LOG [[1]: Start to unzip py-lib/jython-standalone-2.7.3.jar>>>]
[INFO ] 2024-09-20 14:16:43.481  [main] PDK - LOG [[2]: Unzip py-lib/jython-standalone-2.7.3.jar fail, File parameter 'file is not writable: '/Users/<USER>/IdeaProjects/DGG-oss/temp_engine/org/python/google/common/collect/LinkedHashMultimap$ValueSet.class']
[WARN ] 2024-09-20 14:16:43.950  [main] PDK - LOG [Unable to load Python's third-party dependencies from the third-party dependencies package directory]
[INFO ] 2024-09-20 14:16:45.029  [main] DGGTaskScheduler - Stop task which agent id is wim_flow_engine and status is stopping
[INFO ] 2024-09-20 14:22:37.434  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:22:37.434  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 14:28:37.473  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 14:28:37.473  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:33:37.505  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:33:37.503  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 14:39:37.529  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:39:37.529  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 14:44:37.549  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 14:44:37.550  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:49:37.589  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:49:37.589  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 14:54:37.614  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 14:54:37.615  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:59:37.642  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 14:59:37.642  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 15:04:37.668  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 15:04:37.668  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 15:10:37.700  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 15:10:37.700  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 15:15:37.724  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2024-09-20 15:15:37.724  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2024-09-20 15:16:13.090  [Thread-25] AnnotationConfigApplicationContext - Closing org.springframework.context.annotation.AnnotationConfigApplicationContext@71c27ee8: startup date [Fri Sep 20 14:16:34 CST 2024]; root of context hierarchy
[INFO ] 2024-09-20 15:16:13.096  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.2.1] Running shutdown hook... Current node state: ACTIVE
[INFO ] 2024-09-20 15:16:13.098  [hz.ShutdownThread] LifecycleService - [************]:5701 [dev] [5.2.1] [************]:5701 is SHUTTING_DOWN
[WARN ] 2024-09-20 15:16:13.103  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.2.1] Terminating forcefully...
[INFO ] 2024-09-20 15:16:13.106  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.2.1] Shutting down connection manager...
[INFO ] 2024-09-20 15:16:13.115  [Thread-25] AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
[INFO ] 2024-09-20 15:16:13.117  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.2.1] Shutting down node engine...
[INFO ] 2024-09-20 15:16:13.117  [Thread-25] ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
[INFO ] 2024-09-20 15:16:13.167  [hz.ShutdownThread] NodeExtension - [************]:5701 [dev] [5.2.1] Destroying node NodeExtension.
[INFO ] 2024-09-20 15:16:13.168  [hz.ShutdownThread] Node - [************]:5701 [dev] [5.2.1] Hazelcast Shutdown is completed in 65 ms.
[INFO ] 2024-09-20 15:16:13.169  [hz.ShutdownThread] LifecycleService - [************]:5701 [dev] [5.2.1] [************]:5701 is SHUTDOWN
