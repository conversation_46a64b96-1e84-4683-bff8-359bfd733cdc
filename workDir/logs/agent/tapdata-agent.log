[INFO ] 2025-07-01 00:02:41.054  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:02:41.054  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:07:41.122  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:07:41.122  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:12:41.146  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:12:41.146  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:17:41.226  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:17:41.226  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:22:41.247  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:22:41.247  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:27:41.267  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:27:41.268  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:32:41.283  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:32:41.283  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:37:41.303  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:37:41.303  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:42:41.326  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:42:41.326  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:47:41.343  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:47:41.343  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:52:41.365  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:52:41.365  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 00:57:41.383  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 00:57:41.383  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:02:41.411  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:02:41.411  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:07:41.432  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:07:41.432  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:12:41.452  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:12:41.452  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:17:41.470  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:17:41.470  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:23:41.493  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:23:41.493  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:28:41.511  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:28:41.511  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:33:41.537  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:33:41.537  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:38:41.573  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:38:41.573  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:43:41.611  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:43:41.611  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:48:41.644  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:48:41.644  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:53:41.680  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:53:41.680  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 01:58:41.707  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 01:58:41.707  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:00:00.251  [Thread-websocket-handle-message--2-thread-14] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6850dced7745a06476bbc204","connectionName":"PGMasterWim","databaseType":"PostgreSQL","pdkHash":"8dea069f269dd428d82e60e75d0d91b6dc61be7be26eae7bc407d7b95d3b0594","pdkType":"pdk","schemaVersion":"b4af9cbb-70c7-467a-9aed-26f2873b01bb","type":"testConnection"}
[INFO ] 2025-07-01 02:00:00.253  [Thread-499] TestConnectionHandler - Starting validate connections name: PGMasterWim.
[INFO ] 2025-07-01 02:00:00.403  [Thread-websocket-handle-message--2-thread-15] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"68465ee2c024cb5dd54697a2","connectionName":"Sybase190COM","databaseType":"Sybase","pdkHash":"76f3183394a343a8002cd56f5330ff5c7adca22aaeca3ab23c56ec3e0fdc2233","pdkType":"pdk","schemaVersion":"d342ed0c-9883-472a-ac34-9c0e2310f666","type":"testConnection"}
[INFO ] 2025-07-01 02:00:00.404  [Thread-500] TestConnectionHandler - Starting validate connections name: Sybase190COM.
[INFO ] 2025-07-01 02:00:00.541  [Thread-websocket-handle-message--2-thread-17] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"68465984c024cb5dd5469789","connectionName":"Sybase190","databaseType":"Sybase","pdkHash":"76f3183394a343a8002cd56f5330ff5c7adca22aaeca3ab23c56ec3e0fdc2233","pdkType":"pdk","schemaVersion":"a1b25337-0576-4daf-8642-bd075646494c","type":"testConnection"}
[INFO ] 2025-07-01 02:00:00.542  [Thread-501] TestConnectionHandler - Starting validate connections name: Sybase190.
[INFO ] 2025-07-01 02:00:00.681  [Thread-websocket-handle-message--2-thread-16] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6842271b25a89e2512babc6e","connectionName":"PGMaster","databaseType":"PostgreSQL","pdkHash":"8dea069f269dd428d82e60e75d0d91b6dc61be7be26eae7bc407d7b95d3b0594","pdkType":"pdk","schemaVersion":"e7e026a0-5d17-406d-969c-593e832c9a41","type":"testConnection"}
[INFO ] 2025-07-01 02:00:00.682  [Thread-502] TestConnectionHandler - Starting validate connections name: PGMaster.
[INFO ] 2025-07-01 02:00:00.781  [Thread-websocket-handle-message--2-thread-18] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"683e845c708f9e2c3ac88a59","connectionName":"SybaseTargetWim2","databaseType":"Sybase","pdkHash":"76f3183394a343a8002cd56f5330ff5c7adca22aaeca3ab23c56ec3e0fdc2233","pdkType":"pdk","schemaVersion":"ca6f14cb-0d70-4800-80a6-f43d4d6ccf4c","type":"testConnection"}
[INFO ] 2025-07-01 02:00:00.781  [Thread-503] TestConnectionHandler - Starting validate connections name: SybaseTargetWim2.
[INFO ] 2025-07-01 02:00:00.908  [Thread-websocket-handle-message--2-thread-19] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"683926666ea75627668ec570","connectionName":"TestMysql - Copy","databaseType":"Mysql","pdkHash":"a5af410b12afca476edf4a650c133ddf135bf76542a67787ed6f7f7d53ba712","pdkType":"pdk","schemaVersion":"525da03e-81f9-4a15-8959-39a5a09d96c5","type":"testConnection"}
[INFO ] 2025-07-01 02:00:00.912  [Thread-504] TestConnectionHandler - Starting validate connections name: TestMysql - Copy.
[INFO ] 2025-07-01 02:00:01.004  [Thread-websocket-handle-message--2-thread-20] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"683924746ea75627668ec4fd","connectionName":"TestMysql","databaseType":"Mysql","pdkHash":"a5af410b12afca476edf4a650c133ddf135bf76542a67787ed6f7f7d53ba712","pdkType":"pdk","schemaVersion":"bab0028e-f05e-4f62-97ed-5e168bdb0d30","type":"testConnection"}
[INFO ] 2025-07-01 02:00:01.005  [Thread-505] TestConnectionHandler - Starting validate connections name: TestMysql.
[INFO ] 2025-07-01 02:00:01.107  [Thread-websocket-handle-message--2-thread-21] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"683825b9b19fe72f5ab321c7","connectionName":"SourcePG","databaseType":"PostgreSQL","pdkHash":"8dea069f269dd428d82e60e75d0d91b6dc61be7be26eae7bc407d7b95d3b0594","pdkType":"pdk","schemaVersion":"a90fbedf-11cb-4f5c-9a15-bb38c48ca616","type":"testConnection"}
[INFO ] 2025-07-01 02:00:01.107  [Thread-506] TestConnectionHandler - Starting validate connections name: SourcePG.
[INFO ] 2025-07-01 02:00:01.245  [Thread-websocket-handle-message--2-thread-22] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"68381efdb19fe72f5ab31c56","connectionName":"SybaseTarget","databaseType":"Sybase","pdkHash":"76f3183394a343a8002cd56f5330ff5c7adca22aaeca3ab23c56ec3e0fdc2233","pdkType":"pdk","schemaVersion":"f80e9b0f-30c8-408f-a0f8-5ec657f969c4","type":"testConnection"}
[INFO ] 2025-07-01 02:00:01.246  [Thread-507] TestConnectionHandler - Starting validate connections name: SybaseTarget.
[ERROR] 2025-07-01 02:00:01.313  [Thread-504] PDK - ExternalJarManager [Copy encrypted jar file /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__685a094c6389fb6bc2f6d915__.jar to /Users/<USER>/IdeaProjects/tapdata-oss/connectors/tap-running/mysql-connector-v1.0-SNAPSHOT__685a094c6389fb6bc2f6d915___1bf4b4f6-0499-4bf3-be6f-6ac77d7c3a42.jar failed]
[INFO ] 2025-07-01 02:00:01.500  [Thread-websocket-handle-message--2-thread-23] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"683808a0b19fe72f5ab30ed9","connectionName":"PGWim","databaseType":"PostgreSQL","pdkHash":"8dea069f269dd428d82e60e75d0d91b6dc61be7be26eae7bc407d7b95d3b0594","pdkType":"pdk","schemaVersion":"373ac0ef-4bd6-4540-acc7-4f424ae0ceaa","type":"testConnection"}
[INFO ] 2025-07-01 02:00:01.522  [Thread-508] TestConnectionHandler - Starting validate connections name: PGWim.
[INFO ] 2025-07-01 02:00:01.523  [Thread-websocket-handle-message--2-thread-24] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6835cc611862315d53be876b","connectionName":"SqlServer","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"e04ec219-1a24-4353-a21f-60485d8ed451","type":"testConnection"}
[INFO ] 2025-07-01 02:00:01.523  [Thread-509] TestConnectionHandler - Starting validate connections name: SqlServer.
[INFO ] 2025-07-01 02:00:01.731  [Thread-websocket-handle-message--2-thread-1] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"68355709ab9bb35e3414a515","connectionName":"PG - Copy","databaseType":"PostgreSQL","pdkHash":"8dea069f269dd428d82e60e75d0d91b6dc61be7be26eae7bc407d7b95d3b0594","pdkType":"pdk","schemaVersion":"f59dc03f-4f94-4d84-9f44-4579e1687d97","type":"testConnection"}
[INFO ] 2025-07-01 02:00:01.732  [Thread-510] TestConnectionHandler - Starting validate connections name: PG - Copy.
[INFO ] 2025-07-01 02:00:01.867  [Thread-499] PDK - ConnectionValidator [methodEnd - CONNECTION_TEST | message - (org.postgresql.util.PSQLException: FATAL: password authentication failed for user "wim")]
[ERROR] 2025-07-01 02:00:02.069  [Thread-509] PDK - ExternalJarManager [Copy encrypted jar file /Users/<USER>/IdeaProjects/tapdata-oss/dist/mssql-connector-v1.0-SNAPSHOT__6835cc1a1862315d53be86fd__.jar to /Users/<USER>/IdeaProjects/tapdata-oss/connectors/tap-running/mssql-connector-v1.0-SNAPSHOT__6835cc1a1862315d53be86fd___ff35eab2-4466-4148-bf40-ce46075fa44d.jar failed]
[INFO ] 2025-07-01 02:00:02.071  [Thread-websocket-handle-message--2-thread-2] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"68355620ab9bb35e3414a38b","connectionName":"Sybase - Copy","databaseType":"Sybase","pdkHash":"76f3183394a343a8002cd56f5330ff5c7adca22aaeca3ab23c56ec3e0fdc2233","pdkType":"pdk","schemaVersion":"058c2edb-21e5-41f6-bc0f-68d52f23f6a6","type":"testConnection"}
[INFO ] 2025-07-01 02:00:02.074  [Thread-511] TestConnectionHandler - Starting validate connections name: Sybase - Copy.
[INFO ] 2025-07-01 02:00:02.112  [LOAD-SCHEMA-FIELDS-[PGMaster]] LoadSchemaRunner - Starting load schema fields, connection name: PGMaster
[INFO ] 2025-07-01 02:00:02.112  [LOAD-SCHEMA-FIELDS-[SybaseTargetWim2]] LoadSchemaRunner - Starting load schema fields, connection name: SybaseTargetWim2
[INFO ] 2025-07-01 02:00:02.307  [Thread-websocket-handle-message--2-thread-3] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6833df3ac7568322c2077513","connectionName":"PG","databaseType":"PostgreSQL","pdkHash":"8dea069f269dd428d82e60e75d0d91b6dc61be7be26eae7bc407d7b95d3b0594","pdkType":"pdk","schemaVersion":"ee07411a-5689-468b-ae59-696efab7527c","type":"testConnection"}
[INFO ] 2025-07-01 02:00:02.308  [Thread-512] TestConnectionHandler - Starting validate connections name: PG.
[INFO ] 2025-07-01 02:00:02.884  [LOAD-SCHEMA-FIELDS-[TestMysql - Copy]] LoadSchemaRunner - Starting load schema fields, connection name: TestMysql - Copy
[INFO ] 2025-07-01 02:00:02.890  [LOAD-SCHEMA-FIELDS-[PGMasterWim]] LoadSchemaRunner - Starting load schema fields, connection name: PGMasterWim
[INFO ] 2025-07-01 02:00:02.894  [LOAD-SCHEMA-FIELDS-[SybaseTarget]] LoadSchemaRunner - Starting load schema fields, connection name: SybaseTarget
[INFO ] 2025-07-01 02:00:02.894  [LOAD-SCHEMA-FIELDS-[TestMysql]] LoadSchemaRunner - Starting load schema fields, connection name: TestMysql
[INFO ] 2025-07-01 02:00:03.121  [Thread-506] PDK - ConnectionValidator [methodEnd - CONNECTION_TEST | message - (java.net.ConnectException: Connection refused)]
[INFO ] 2025-07-01 02:00:03.301  [LOAD-SCHEMA-FIELDS-[Sybase - Copy]] LoadSchemaRunner - Starting load schema fields, connection name: Sybase - Copy
[INFO ] 2025-07-01 02:00:03.347  [Thread-508] PDK - ConnectionValidator [methodEnd - CONNECTION_TEST | message - (java.net.ConnectException: Connection refused)]
[INFO ] 2025-07-01 02:00:03.441  [Thread-510] PDK - ConnectionValidator [methodEnd - CONNECTION_TEST | message - (java.net.ConnectException: Connection refused)]
[INFO ] 2025-07-01 02:00:03.569  [LOAD-SCHEMA-FIELDS-[SourcePG]] LoadSchemaRunner - Starting load schema fields, connection name: SourcePG
[INFO ] 2025-07-01 02:00:03.779  [Thread-512] PDK - ConnectionValidator [methodEnd - CONNECTION_TEST | message - (java.net.ConnectException: Connection refused)]
[INFO ] 2025-07-01 02:00:03.827  [LOAD-SCHEMA-FIELDS-[PGWim]] LoadSchemaRunner - Starting load schema fields, connection name: PGWim
[INFO ] 2025-07-01 02:00:04.019  [LOAD-SCHEMA-FIELDS-[PG - Copy]] LoadSchemaRunner - Starting load schema fields, connection name: PG - Copy
[INFO ] 2025-07-01 02:00:04.133  [LOAD-SCHEMA-FIELDS-[PG]] LoadSchemaRunner - Starting load schema fields, connection name: PG
[INFO ] 2025-07-01 02:00:04.383  [LOAD-SCHEMA-FIELDS-[PGMasterWim]] PDK - LoadSchemaRunner [methodEnd - INIT | message - (org.postgresql.util.PSQLException: FATAL: password authentication failed for user "wim")]
[ERROR] 2025-07-01 02:00:04.406  [LOAD-SCHEMA-FIELDS-[PGMasterWim]] PDK - LoadSchemaRunner [Load schema failed: org.postgresql.util.PSQLException: FATAL: password authentication failed for user "wim"
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectUserPwdInvalid(PostgresExceptionCollector.java:32)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.postgresql.util.PSQLException: FATAL: password authentication failed for user "wim"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:704)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:213)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more
]
[ERROR] 2025-07-01 02:00:04.444  [LOAD-SCHEMA-FIELDS-[PGMasterWim]] LoadSchemaRunner - Load schema fields error, connection name: PGMasterWim, message: Unable to connect to postgres, incorrect username or password, username: wim
  org.postgresql.util.PSQLException: FATAL: password authentication failed for user "wim"
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectUserPwdInvalid(PostgresExceptionCollector.java:32)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.postgresql.util.PSQLException: FATAL: password authentication failed for user "wim"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:704)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:213)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more

io.tapdata.exception.TapPdkUserPwdInvalidEx: Unable to connect to postgres, incorrect username or password, username: wim
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectUserPwdInvalid(PostgresExceptionCollector.java:32) ~[?:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51) ~[?:?]
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73) ~[?:?]
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99) ~[?:?]
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285) ~[?:?]
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: org.postgresql.util.PSQLException: FATAL: password authentication failed for user "wim"
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:704) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:213) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268) ~[?:?]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[?:?]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273) ~[?:?]
	at org.postgresql.Driver.makeConnection(Driver.java:446) ~[?:?]
	at org.postgresql.Driver.connect(Driver.java:298) ~[?:?]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-6.3.0.jar:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49) ~[?:?]
	... 18 more
[INFO ] 2025-07-01 02:00:05.046  [LOAD-SCHEMA-FIELDS-[SourcePG]] PDK - LoadSchemaRunner [methodEnd - INIT | message - (java.net.ConnectException: Connection refused)]
[ERROR] 2025-07-01 02:00:05.049  [LOAD-SCHEMA-FIELDS-[SourcePG]] PDK - LoadSchemaRunner [Load schema failed: java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more
]
[ERROR] 2025-07-01 02:00:05.051  [LOAD-SCHEMA-FIELDS-[SourcePG]] LoadSchemaRunner - Load schema fields error, connection name: SourcePG, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
  java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more

io.tapdata.exception.TapPdkRetryableEx: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151) ~[?:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52) ~[?:?]
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73) ~[?:?]
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99) ~[?:?]
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285) ~[?:?]
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260) ~[?:?]
	at org.postgresql.core.PGStream.<init>(PGStream.java:121) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268) ~[?:?]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[?:?]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273) ~[?:?]
	at org.postgresql.Driver.makeConnection(Driver.java:446) ~[?:?]
	at org.postgresql.Driver.connect(Driver.java:298) ~[?:?]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-6.3.0.jar:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49) ~[?:?]
	... 18 more
[INFO ] 2025-07-01 02:00:05.158  [LOAD-SCHEMA-FIELDS-[PGWim]] PDK - LoadSchemaRunner [methodEnd - INIT | message - (java.net.ConnectException: Connection refused)]
[ERROR] 2025-07-01 02:00:05.160  [LOAD-SCHEMA-FIELDS-[PGWim]] PDK - LoadSchemaRunner [Load schema failed: java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more
]
[ERROR] 2025-07-01 02:00:05.163  [LOAD-SCHEMA-FIELDS-[PGWim]] LoadSchemaRunner - Load schema fields error, connection name: PGWim, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
  java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more

io.tapdata.exception.TapPdkRetryableEx: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151) ~[?:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52) ~[?:?]
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73) ~[?:?]
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99) ~[?:?]
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285) ~[?:?]
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260) ~[?:?]
	at org.postgresql.core.PGStream.<init>(PGStream.java:121) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268) ~[?:?]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[?:?]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273) ~[?:?]
	at org.postgresql.Driver.makeConnection(Driver.java:446) ~[?:?]
	at org.postgresql.Driver.connect(Driver.java:298) ~[?:?]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-6.3.0.jar:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49) ~[?:?]
	... 18 more
[INFO ] 2025-07-01 02:00:05.248  [LOAD-SCHEMA-FIELDS-[PG - Copy]] PDK - LoadSchemaRunner [methodEnd - INIT | message - (java.net.ConnectException: Connection refused)]
[ERROR] 2025-07-01 02:00:05.250  [LOAD-SCHEMA-FIELDS-[PG - Copy]] PDK - LoadSchemaRunner [Load schema failed: java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more
]
[ERROR] 2025-07-01 02:00:05.251  [LOAD-SCHEMA-FIELDS-[PG - Copy]] LoadSchemaRunner - Load schema fields error, connection name: PG - Copy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
  java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more

io.tapdata.exception.TapPdkRetryableEx: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151) ~[?:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52) ~[?:?]
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73) ~[?:?]
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99) ~[?:?]
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285) ~[?:?]
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260) ~[?:?]
	at org.postgresql.core.PGStream.<init>(PGStream.java:121) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268) ~[?:?]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[?:?]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273) ~[?:?]
	at org.postgresql.Driver.makeConnection(Driver.java:446) ~[?:?]
	at org.postgresql.Driver.connect(Driver.java:298) ~[?:?]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-6.3.0.jar:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49) ~[?:?]
	... 18 more
[INFO ] 2025-07-01 02:00:05.407  [LOAD-SCHEMA-FIELDS-[PG]] PDK - LoadSchemaRunner [methodEnd - INIT | message - (java.net.ConnectException: Connection refused)]
[ERROR] 2025-07-01 02:00:05.418  [LOAD-SCHEMA-FIELDS-[PG]] PDK - LoadSchemaRunner [Load schema failed: java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more
]
[ERROR] 2025-07-01 02:00:05.420  [LOAD-SCHEMA-FIELDS-[PG]] LoadSchemaRunner - Load schema fields error, connection name: PG, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
  java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46)
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388)
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260)
	at org.postgresql.core.PGStream.<init>(PGStream.java:121)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273)
	at org.postgresql.Driver.makeConnection(Driver.java:446)
	at org.postgresql.Driver.connect(Driver.java:298)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more

io.tapdata.exception.TapPdkRetryableEx: PDK retry exception (Server Error Code null): when operate table: unknown, java.net.ConnectException: Connection refused
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151) ~[?:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52) ~[?:?]
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73) ~[?:?]
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryVersion(PostgresJdbcContext.java:46) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.initConnection(PostgresConnector.java:388) ~[?:?]
	at io.tapdata.connector.postgres.PostgresConnector.onStart(PostgresConnector.java:99) ~[?:?]
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285) ~[?:?]
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:547) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:633) ~[?:?]
	at org.postgresql.core.PGStream.createSocket(PGStream.java:260) ~[?:?]
	at org.postgresql.core.PGStream.<init>(PGStream.java:121) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:140) ~[?:?]
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:268) ~[?:?]
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:54) ~[?:?]
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:273) ~[?:?]
	at org.postgresql.Driver.makeConnection(Driver.java:446) ~[?:?]
	at org.postgresql.Driver.connect(Driver.java:298) ~[?:?]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-6.3.0.jar:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49) ~[?:?]
	... 18 more
[INFO ] 2025-07-01 02:00:05.992  [LOAD-SCHEMA-FIELDS-[PGMaster]] LoadSchemaRunner - Finished load schema fields, connection name: PGMaster, progress: 33/33
[INFO ] 2025-07-01 02:00:14.004  [LOAD-SCHEMA-FIELDS-[Sybase190]] LoadSchemaRunner - Starting load schema fields, connection name: Sybase190
[INFO ] 2025-07-01 02:00:14.756  [LOAD-SCHEMA-FIELDS-[TestMysql]] LoadSchemaRunner - Finished load schema fields, connection name: TestMysql, progress: 255/255
[INFO ] 2025-07-01 02:00:14.756  [LOAD-SCHEMA-FIELDS-[TestMysql - Copy]] LoadSchemaRunner - Finished load schema fields, connection name: TestMysql - Copy, progress: 255/255
[INFO ] 2025-07-01 02:00:15.123  [LOAD-SCHEMA-FIELDS-[Sybase190COM]] LoadSchemaRunner - Starting load schema fields, connection name: Sybase190COM
[INFO ] 2025-07-01 02:00:29.173  [LOAD-SCHEMA-FIELDS-[SybaseTargetWim2]] LoadSchemaRunner - Finished load schema fields, connection name: SybaseTargetWim2, progress: 63/63
[INFO ] 2025-07-01 02:00:29.835  [LOAD-SCHEMA-FIELDS-[SybaseTarget]] LoadSchemaRunner - Finished load schema fields, connection name: SybaseTarget, progress: 63/63
[INFO ] 2025-07-01 02:00:30.283  [LOAD-SCHEMA-FIELDS-[Sybase - Copy]] LoadSchemaRunner - Finished load schema fields, connection name: Sybase - Copy, progress: 63/63
[INFO ] 2025-07-01 02:00:34.517  [LOAD-SCHEMA-FIELDS-[SqlServer]] LoadSchemaRunner - Starting load schema fields, connection name: SqlServer
[INFO ] 2025-07-01 02:01:45.856  [LOAD-SCHEMA-FIELDS-[SqlServer]] PDK - LoadSchemaRunner [methodEnd - INIT | message - (PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9)]
[ERROR] 2025-07-01 02:01:45.861  [LOAD-SCHEMA-FIELDS-[SqlServer]] PDK - LoadSchemaRunner [Load schema failed: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.queryTimeZoneOffset(MssqlJdbcRunner.java:95)
	at io.tapdata.connector.mssql.MssqlConnector.onStart(MssqlConnector.java:96)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more
]
[ERROR] 2025-07-01 02:01:45.863  [LOAD-SCHEMA-FIELDS-[SqlServer]] LoadSchemaRunner - Load schema fields error, connection name: SqlServer, message: PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9
  com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.queryTimeZoneOffset(MssqlJdbcRunner.java:95)
	at io.tapdata.connector.mssql.MssqlConnector.onStart(MssqlConnector.java:96)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 18 more

io.tapdata.exception.TapPdkRetryableEx: PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69) ~[?:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52) ~[?:?]
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31) ~[?:?]
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73) ~[?:?]
	at io.tapdata.connector.mssql.MssqlJdbcRunner.queryTimeZoneOffset(MssqlJdbcRunner.java:95) ~[?:?]
	at io.tapdata.connector.mssql.MssqlConnector.onStart(MssqlConnector.java:96) ~[?:?]
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285) ~[?:?]
	at io.tapdata.pdk.core.api.ConnectionNode.connectorInit(ConnectionNode.java:53) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:164) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:0c94486f-2837-486e-9995-be25db1b34e9
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422) ~[?:?]
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411) ~[?:?]
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153) ~[?:?]
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966) ~[?:?]
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628) ~[?:?]
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471) ~[?:?]
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470) ~[?:?]
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915) ~[?:?]
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115) ~[HikariCP-6.3.0.jar:?]
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112) ~[HikariCP-6.3.0.jar:?]
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49) ~[?:?]
	... 18 more
[INFO ] 2025-07-01 02:03:41.747  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:03:41.748  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:06:35.087  [LOAD-SCHEMA-FIELDS-[Sybase190]] PDK - LoadSchemaRunner [methodEnd - DISCOVER_SCHEMA | message - (java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.)]
[ERROR] 2025-07-01 02:06:35.089  [LOAD-SCHEMA-FIELDS-[Sybase190]] PDK - LoadSchemaRunner [Load schema failed: java.lang.Exception: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:235)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:171)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241)
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229)
	... 6 more
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:438)
	at io.tapdata.common.CommonDbConnector.discoverSchema(CommonDbConnector.java:85)
	at io.tapdata.sybase.SybaseConnectorV2.discoverSchema(SybaseConnectorV2.java:450)
	at io.tapdata.Runnable.LoadSchemaRunner.lambda$pdkDiscoverSchema$4(LoadSchemaRunner.java:243)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 14 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:239)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233)
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461)
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:419)
	... 18 more
Caused by: java.sql.SQLException: JZ0C0: Connection is already closed.
	at com.sybase.jdbc42.jdbc.ErrorMessage.raiseError(ErrorMessage.java:839)
	at com.sybase.jdbc42.jdbc.SybConnection.checkConnection(SybConnection.java:4151)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:765)
	at com.sybase.jdbc42.jdbc.SybStatement.closeImplicitly(SybStatement.java:740)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:725)
	at com.sybase.jdbc42.jdbc.MdaManager.loadMetaData(MdaManager.java:587)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:191)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:174)
	at com.sybase.jdbc42.jdbc.SybConnection.checkMDA(SybConnection.java:4296)
	at com.sybase.jdbc42.jdbc.SybConnection.getMDA(SybConnection.java:3820)
	at com.sybase.jdbc42.tds.Tds.setOption(Tds.java:1943)
	at com.sybase.jdbc42.jdbc.SybConnection.setAutoCommit(SybConnection.java:1923)
	at io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:146)
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:170)
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:235)
	... 30 more
]
[INFO ] 2025-07-01 02:07:08.173  [LOAD-SCHEMA-FIELDS-[Sybase190COM]] PDK - LoadSchemaRunner [methodEnd - DISCOVER_SCHEMA | message - (java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.)]
[ERROR] 2025-07-01 02:07:08.174  [LOAD-SCHEMA-FIELDS-[Sybase190COM]] PDK - LoadSchemaRunner [Load schema failed: java.lang.Exception: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:235)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:171)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241)
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229)
	... 6 more
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:438)
	at io.tapdata.common.CommonDbConnector.discoverSchema(CommonDbConnector.java:85)
	at io.tapdata.sybase.SybaseConnectorV2.discoverSchema(SybaseConnectorV2.java:450)
	at io.tapdata.Runnable.LoadSchemaRunner.lambda$pdkDiscoverSchema$4(LoadSchemaRunner.java:243)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 14 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:239)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233)
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461)
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:419)
	... 18 more
Caused by: java.sql.SQLException: JZ0C0: Connection is already closed.
	at com.sybase.jdbc42.jdbc.ErrorMessage.raiseError(ErrorMessage.java:839)
	at com.sybase.jdbc42.jdbc.SybConnection.checkConnection(SybConnection.java:4151)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:765)
	at com.sybase.jdbc42.jdbc.SybStatement.closeImplicitly(SybStatement.java:740)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:725)
	at com.sybase.jdbc42.jdbc.MdaManager.loadMetaData(MdaManager.java:587)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:191)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:174)
	at com.sybase.jdbc42.jdbc.SybConnection.checkMDA(SybConnection.java:4296)
	at com.sybase.jdbc42.jdbc.SybConnection.getMDA(SybConnection.java:3820)
	at com.sybase.jdbc42.tds.Tds.setOption(Tds.java:1943)
	at com.sybase.jdbc42.jdbc.SybConnection.setAutoCommit(SybConnection.java:1923)
	at io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:146)
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:170)
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:235)
	... 30 more
]
[WARN ] 2025-07-01 02:07:17.669  [LOAD-SCHEMA-FIELDS-[Sybase190]] PDK - ErrorKit [Ignore error: java.sql.SQLException: JZ0C0: Connection is already closed.]
[ERROR] 2025-07-01 02:07:17.671  [LOAD-SCHEMA-FIELDS-[Sybase190]] LoadSchemaRunner - Load schema fields error, connection name: Sybase190, message: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
  java.lang.Exception: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:235)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:171)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241)
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229)
	... 6 more
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:438)
	at io.tapdata.common.CommonDbConnector.discoverSchema(CommonDbConnector.java:85)
	at io.tapdata.sybase.SybaseConnectorV2.discoverSchema(SybaseConnectorV2.java:450)
	at io.tapdata.Runnable.LoadSchemaRunner.lambda$pdkDiscoverSchema$4(LoadSchemaRunner.java:243)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 14 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:239)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233)
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461)
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:419)
	... 18 more
Caused by: java.sql.SQLException: JZ0C0: Connection is already closed.
	at com.sybase.jdbc42.jdbc.ErrorMessage.raiseError(ErrorMessage.java:839)
	at com.sybase.jdbc42.jdbc.SybConnection.checkConnection(SybConnection.java:4151)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:765)
	at com.sybase.jdbc42.jdbc.SybStatement.closeImplicitly(SybStatement.java:740)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:725)
	at com.sybase.jdbc42.jdbc.MdaManager.loadMetaData(MdaManager.java:587)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:191)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:174)
	at com.sybase.jdbc42.jdbc.SybConnection.checkMDA(SybConnection.java:4296)
	at com.sybase.jdbc42.jdbc.SybConnection.getMDA(SybConnection.java:3820)
	at com.sybase.jdbc42.tds.Tds.setOption(Tds.java:1943)
	at com.sybase.jdbc42.jdbc.SybConnection.setAutoCommit(SybConnection.java:1923)
	at io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:146)
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:170)
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:235)
	... 30 more

java.lang.Exception: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:235) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:171) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: io.tapdata.pdk.core.error.TapPdkRunnerUnknownException: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229) ~[classes/:?]
	... 6 more
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:438) ~[?:?]
	at io.tapdata.common.CommonDbConnector.discoverSchema(CommonDbConnector.java:85) ~[?:?]
	at io.tapdata.sybase.SybaseConnectorV2.discoverSchema(SybaseConnectorV2.java:450) ~[?:?]
	at io.tapdata.Runnable.LoadSchemaRunner.lambda$pdkDiscoverSchema$4(LoadSchemaRunner.java:243) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229) ~[classes/:?]
	... 6 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:239) ~[?:?]
	at java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233) ~[?:?]
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461) ~[?:?]
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423) ~[?:?]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	... 1 more
Caused by: java.sql.SQLException: JZ0C0: Connection is already closed.
	at com.sybase.jdbc42.jdbc.ErrorMessage.raiseError(ErrorMessage.java:839) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.checkConnection(SybConnection.java:4151) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:765) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybStatement.closeImplicitly(SybStatement.java:740) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:725) ~[?:?]
	at com.sybase.jdbc42.jdbc.MdaManager.loadMetaData(MdaManager.java:587) ~[?:?]
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:191) ~[?:?]
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:174) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.checkMDA(SybConnection.java:4296) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.getMDA(SybConnection.java:3820) ~[?:?]
	at com.sybase.jdbc42.tds.Tds.setOption(Tds.java:1943) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.setAutoCommit(SybConnection.java:1923) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:146) ~[?:?]
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:170) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:235) ~[?:?]
	at java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233) ~[?:?]
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461) ~[?:?]
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423) ~[?:?]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	... 1 more
[ERROR] 2025-07-01 02:07:23.358  [LOAD-SCHEMA-FIELDS-[Sybase190COM]] LoadSchemaRunner - Load schema fields error, connection name: Sybase190COM, message: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
  java.lang.Exception: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:235)
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:171)
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264)
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156)
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127)
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241)
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229)
	... 6 more
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:438)
	at io.tapdata.common.CommonDbConnector.discoverSchema(CommonDbConnector.java:85)
	at io.tapdata.sybase.SybaseConnectorV2.discoverSchema(SybaseConnectorV2.java:450)
	at io.tapdata.Runnable.LoadSchemaRunner.lambda$pdkDiscoverSchema$4(LoadSchemaRunner.java:243)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 14 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:239)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233)
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461)
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:419)
	... 18 more
Caused by: java.sql.SQLException: JZ0C0: Connection is already closed.
	at com.sybase.jdbc42.jdbc.ErrorMessage.raiseError(ErrorMessage.java:839)
	at com.sybase.jdbc42.jdbc.SybConnection.checkConnection(SybConnection.java:4151)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:765)
	at com.sybase.jdbc42.jdbc.SybStatement.closeImplicitly(SybStatement.java:740)
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:725)
	at com.sybase.jdbc42.jdbc.MdaManager.loadMetaData(MdaManager.java:587)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:191)
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:174)
	at com.sybase.jdbc42.jdbc.SybConnection.checkMDA(SybConnection.java:4296)
	at com.sybase.jdbc42.jdbc.SybConnection.getMDA(SybConnection.java:3820)
	at com.sybase.jdbc42.tds.Tds.setOption(Tds.java:1943)
	at com.sybase.jdbc42.jdbc.SybConnection.setAutoCommit(SybConnection.java:1923)
	at io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:146)
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:170)
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:235)
	... 30 more

java.lang.Exception: Load pdk schema failed, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:235) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.run(LoadSchemaRunner.java:171) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.testPdkConnection(TestConnectionHandler.java:264) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.handleSync(TestConnectionHandler.java:156) ~[classes/:?]
	at io.tapdata.websocket.handler.TestConnectionHandler.lambda$handle$2(TestConnectionHandler.java:127) ~[classes/:?]
	at io.tapdata.aspect.supervisor.AspectRunnableUtil.lambda$aspectRunnable$1(AspectRunnableUtil.java:11) ~[classes/:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: io.tapdata.pdk.core.error.TapPdkRunnerUnknownException: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229) ~[classes/:?]
	... 6 more
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.base.ConnectorBase.multiThreadDiscoverSchema(ConnectorBase.java:438) ~[?:?]
	at io.tapdata.common.CommonDbConnector.discoverSchema(CommonDbConnector.java:85) ~[?:?]
	at io.tapdata.sybase.SybaseConnectorV2.discoverSchema(SybaseConnectorV2.java:450) ~[?:?]
	at io.tapdata.Runnable.LoadSchemaRunner.lambda$pdkDiscoverSchema$4(LoadSchemaRunner.java:243) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103) ~[classes/:?]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.pdkDiscoverSchema(LoadSchemaRunner.java:241) ~[classes/:?]
	at io.tapdata.Runnable.LoadSchemaRunner.loadPdkSchema(LoadSchemaRunner.java:229) ~[classes/:?]
	... 6 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: JZ0C0: Connection is already closed.
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:239) ~[?:?]
	at java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233) ~[?:?]
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461) ~[?:?]
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423) ~[?:?]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	... 1 more
Caused by: java.sql.SQLException: JZ0C0: Connection is already closed.
	at com.sybase.jdbc42.jdbc.ErrorMessage.raiseError(ErrorMessage.java:839) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.checkConnection(SybConnection.java:4151) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:765) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybStatement.closeImplicitly(SybStatement.java:740) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybStatement.close(SybStatement.java:725) ~[?:?]
	at com.sybase.jdbc42.jdbc.MdaManager.loadMetaData(MdaManager.java:587) ~[?:?]
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:191) ~[?:?]
	at com.sybase.jdbc42.jdbc.MdaManager.<init>(MdaManager.java:174) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.checkMDA(SybConnection.java:4296) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.getMDA(SybConnection.java:3820) ~[?:?]
	at com.sybase.jdbc42.tds.Tds.setOption(Tds.java:1943) ~[?:?]
	at com.sybase.jdbc42.jdbc.SybConnection.setAutoCommit(SybConnection.java:1923) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:146) ~[?:?]
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:170) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.lambda$queryAllPks$5(SybaseContext.java:235) ~[?:?]
	at java.util.ArrayList.forEach(ArrayList.java:1511) ~[?:?]
	at io.tapdata.sybase.extend.SybaseContext.queryAllPks(SybaseContext.java:233) ~[?:?]
	at io.tapdata.sybase.SybaseConnectorV2.singleThreadDiscoverSchema(SybaseConnectorV2.java:461) ~[?:?]
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423) ~[?:?]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) ~[?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[?:?]
	... 1 more
[INFO ] 2025-07-01 02:08:41.777  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:08:41.777  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:13:41.811  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:13:41.811  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:18:41.833  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:18:41.833  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:23:41.912  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:23:41.912  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:28:41.943  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:28:41.943  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:34:41.979  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:34:41.979  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:39:42.020  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:39:42.020  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:44:42.047  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:44:42.047  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:49:42.079  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:49:42.079  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:54:42.107  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 02:54:42.107  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:59:42.132  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 02:59:42.132  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:04:42.158  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:04:42.158  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:09:42.189  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:09:42.189  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:14:42.220  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:14:42.220  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:19:42.257  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:19:42.257  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:24:42.285  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:24:42.286  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:29:42.320  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:29:42.321  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:34:42.352  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:34:42.352  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:39:42.381  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:39:42.381  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:44:42.409  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:44:42.410  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:50:42.393  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:50:42.393  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 03:55:42.410  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 03:55:42.411  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:00:42.431  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:00:42.431  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:05:42.448  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:05:42.448  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:10:42.481  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:10:42.481  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:15:42.511  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:15:42.511  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:20:42.534  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:20:42.534  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:25:42.559  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:25:42.559  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:30:42.583  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:30:42.583  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:35:42.599  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:35:42.599  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:40:42.636  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:40:42.636  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:45:42.664  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:45:42.664  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:50:42.683  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:50:42.683  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 04:55:42.738  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 04:55:42.738  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:00:42.777  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:00:42.777  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:05:42.813  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:05:42.813  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:10:42.837  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:10:42.838  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:15:42.863  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:15:42.863  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:20:42.886  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:20:42.887  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:25:42.918  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:25:42.918  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:30:42.952  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:30:42.952  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:35:42.987  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:35:42.987  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:40:43.024  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:40:43.024  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:45:43.061  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:45:43.061  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:50:43.088  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:50:43.089  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 05:55:43.119  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 05:55:43.119  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:00:43.146  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:00:43.146  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:06:43.129  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:06:43.129  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:11:43.154  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:11:43.154  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:16:43.185  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:16:43.185  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:21:43.213  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:21:43.213  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:26:43.247  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:26:43.247  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:31:43.274  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:31:43.274  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:36:43.303  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:36:43.303  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:41:43.339  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:41:43.339  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:46:43.362  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:46:43.362  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:51:43.389  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 06:51:43.389  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:56:43.419  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 06:56:43.419  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:01:43.451  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:01:43.451  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:06:43.473  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:06:43.473  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:11:43.545  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:11:43.545  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:16:43.574  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:16:43.574  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:21:43.610  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:21:43.610  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:26:43.642  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:26:43.642  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:31:43.673  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:31:43.673  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:36:43.691  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:36:43.691  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:41:43.718  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:41:43.718  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:46:43.738  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:46:43.738  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:51:43.764  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:51:43.764  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 07:56:43.792  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 07:56:43.792  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 08:01:43.826  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 08:01:43.826  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 08:06:43.862  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 08:06:43.862  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 08:11:43.887  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 08:11:43.888  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 08:16:43.921  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 08:16:43.921  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 08:21:43.961  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 08:21:43.961  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 08:26:43.993  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 08:26:43.993  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[WARN ] 2025-07-01 08:30:48.562  [hz.wim_flow_engine.InvocationMonitorThread] InvocationMonitor - [*******]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 104494 ms
[INFO ] 2025-07-01 08:30:48.596  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0, status code: 1000, reason: null
[WARN ] 2025-07-01 08:30:52.529  [hz.wim_flow_engine.cached.thread-26] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 104515 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-07-01 08:30:58.609  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0
[INFO ] 2025-07-01 08:30:58.617  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-07-01 08:30:58.617  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-07-01 08:46:28.050  [hz.wim_flow_engine.InvocationMonitorThread] InvocationMonitor - [*******]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 928455 ms
[INFO ] 2025-07-01 08:46:30.966  [hz.wim_flow_engine.cached.thread-6] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-07-01 08:30:57.533 to 2025-07-01 08:46:30.951 since last heartbeat (+928418 ms)
[WARN ] 2025-07-01 08:46:30.966  [hz.wim_flow_engine.cached.thread-6] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 928418 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-07-01 08:46:32.037  [WebSocketClient-AsyncIO-12] ManagementWebsocketHandler - Web socket closed, session: ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0, status code: 1000, reason: null
[WARN ] 2025-07-01 09:03:09.685  [hz.wim_flow_engine.InvocationMonitorThread] InvocationMonitor - [*******]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 995692 ms
[INFO ] 2025-07-01 09:03:11.644  [hz.wim_flow_engine.cached.thread-6] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-07-01 08:46:30.951 to 2025-07-01 09:03:11.643 since last heartbeat (+995692 ms)
[WARN ] 2025-07-01 09:03:11.644  [hz.wim_flow_engine.cached.thread-6] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 995692 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-07-01 09:03:12.838  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-07-01 09:03:12.838  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-07-01 09:03:12.863  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0
[WARN ] 2025-07-01 09:18:44.254  [hz.wim_flow_engine.InvocationMonitorThread] InvocationMonitor - [*******]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 929571 ms
[INFO ] 2025-07-01 09:18:45.785  [nioEventLoopGroup-17-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-07-01 09:18:45.785  [nioEventLoopGroup-17-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-07-01 09:18:45.785  [nioEventLoopGroup-17-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-07-01 09:18:46.101  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-07-01 09:18:46.130  [TapCompletableFutureEx-stop-join-checker-Sybase~PG 中文字-6854ce517745a06476bbeaeb] PDK - WebsocketPushChannel [Login successfully, 127.0.0.1 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzODkyMjcxNzUwIiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoid2ltX2Zsb3dfZW5naW5lXzYwYjc5YzMyNTY1ZDQzMjhiMGU3ZTZhZjc4NmMwOGJkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MTMzMjc1NiwiaWF0IjoxNzUxMzMyNzI2fQ.VHA8yonKQBQ2bDFNosMvENzVsn37SsXBjwsbgsFKCfM]
[INFO ] 2025-07-01 09:18:46.139  [TapCompletableFutureEx-stop-join-checker-Sybase~PG 中文字-6854ce517745a06476bbeaeb] PDK - WebsocketPushChannel [Connect uri ws://127.0.0.1:8246/engine/82021bab77276c64441afbbf7c078a43 wsPort 8246]
[INFO ] 2025-07-01 09:18:46.152  [TapCompletableFutureEx-stop-join-checker-Sybase~PG 中文字-6854ce517745a06476bbeaeb] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-07-01 09:18:46.152  [TapCompletableFutureEx-stop-join-checker-Sybase~PG 中文字-6854ce517745a06476bbeaeb] PDK - WebsocketPushChannel [WS connected successfully, 127.0.0.1 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzODkyMjcxNzUwIiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoid2ltX2Zsb3dfZW5naW5lXzYwYjc5YzMyNTY1ZDQzMjhiMGU3ZTZhZjc4NmMwOGJkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MTMzMjc1NiwiaWF0IjoxNzUxMzMyNzI2fQ.VHA8yonKQBQ2bDFNosMvENzVsn37SsXBjwsbgsFKCfM]
[INFO ] 2025-07-01 09:18:46.157  [nioEventLoopGroup-19-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-07-01 09:18:46.158  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 09:18:46.215  [hz.wim_flow_engine.cached.thread-6] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-07-01 09:03:11.643 to 2025-07-01 09:18:46.208 since last heartbeat (+929565 ms)
[WARN ] 2025-07-01 09:18:46.215  [hz.wim_flow_engine.cached.thread-6] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 929565 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-07-01 09:18:47.303  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-07-01 09:18:47.303  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-07-01 09:18:52.303  [WebSocketClient-AsyncIO-754] ManagementWebsocketHandler - Web socket closed, session: ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0, status code: 1000, reason: null
[INFO ] 2025-07-01 09:18:53.507  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-07-01 09:18:53.507  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-07-01 09:18:53.525  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0
[WARN ] 2025-07-01 09:19:01.996  [hz.wim_flow_engine.InvocationMonitorThread] InvocationMonitor - [*******]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 2958945 ms
[INFO ] 2025-07-01 09:19:03.059  [WebSocketClient-AsyncIO-754] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-07-01 09:19:03.059  [WebSocketClient-AsyncIO-754] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-07-01 09:19:36.103  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-07-01 09:19:36.103  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-07-01 09:19:36.442  [hz.wim_flow_engine.InvocationMonitorThread] InvocationMonitor - [*******]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 32451 ms
[WARN ] 2025-07-01 09:19:39.387  [hz.wim_flow_engine.cached.thread-37] ClusterHeartbeatManager - [*******]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 32435 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-07-01 09:19:40.489  [WebSocketClient-AsyncIO-3] ManagementWebsocketHandler - Web socket closed, session: ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0, status code: 1000, reason: null
[INFO ] 2025-07-01 09:19:46.273  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=d6e3708eca3e442c8e388ac0ce5cb346718736db737e48e5bc95325e89d38efb&singletonTag=5f4a0401-6423-46bf-aaf3-7cf7e712a8a0
[INFO ] 2025-07-01 09:19:50.490  [WebSocketClient-AsyncIO-3] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-07-01 09:19:50.497  [WebSocketClient-AsyncIO-3] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-07-01 09:19:56.382  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-07-01 09:19:56.383  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-07-01 09:20:49.420  [hz.wim_flow_engine.InvocationMonitorThread] InvocationMonitor - [*******]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 32426 ms
[INFO ] 2025-07-01 09:24:35.390  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 09:24:35.390  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 09:30:35.410  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 09:30:35.410  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 09:35:35.426  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 09:35:35.427  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 09:40:35.451  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 09:40:35.451  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 09:45:35.485  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 09:45:35.485  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 09:51:35.487  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 09:51:35.487  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 09:56:35.505  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 09:56:35.505  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 10:01:35.520  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 10:01:35.520  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 10:05:26.064  [hz.ShutdownThread] Node - [*******]:5701 [dev] [5.5.0] Running shutdown hook... Current node state: ACTIVE
[INFO ] 2025-07-01 10:05:26.070  [hz.ShutdownThread] LifecycleService - [*******]:5701 [dev] [5.5.0] [*******]:5701 is SHUTTING_DOWN
[WARN ] 2025-07-01 10:05:26.076  [hz.ShutdownThread] Node - [*******]:5701 [dev] [5.5.0] Terminating forcefully...
[INFO ] 2025-07-01 10:05:26.077  [hz.ShutdownThread] Node - [*******]:5701 [dev] [5.5.0] Shutting down connection manager...
[INFO ] 2025-07-01 10:05:26.082  [hz.ShutdownThread] Node - [*******]:5701 [dev] [5.5.0] Shutting down node engine...
[INFO ] 2025-07-01 10:05:26.120  [hz.ShutdownThread] NodeExtension - [*******]:5701 [dev] [5.5.0] Destroying node NodeExtension.
[INFO ] 2025-07-01 10:05:26.120  [hz.ShutdownThread] Node - [*******]:5701 [dev] [5.5.0] Hazelcast Shutdown is completed in 44 ms.
[INFO ] 2025-07-01 10:05:26.121  [hz.ShutdownThread] LifecycleService - [*******]:5701 [dev] [5.5.0] [*******]:5701 is SHUTDOWN
[INFO ] 2025-07-01 10:06:04.549  [main] Application - disabledAlgorithms [SSLv3, TLSv1, TLSv1.1, DTLSv1.0, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, ECDH]->[SSLv3, DTLSv1.0, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, ECDH]
[INFO ] 2025-07-01 10:06:04.616  [main] Application - Starting application, code version 2025-06-28T15:51:48Z
[INFO ] 2025-07-01 10:06:05.177  [background-preinit] Version - HV000001: Hibernate Validator 9.0.0.CR1
[INFO ] 2025-07-01 10:06:05.231  [main] Application - Starting Application using Java 17.0.14 with PID 52843 (/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/iengine-app/target/classes started by liangjiawei in /Users/<USER>/IdeaProjects/tapdata-oss)
[INFO ] 2025-07-01 10:06:05.236  [main] Application - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-07-01 10:06:06.232  [main] RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[INFO ] 2025-07-01 10:06:06.347  [main] RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 108 ms. Found 0 MongoDB repository interfaces.
[INFO ] 2025-07-01 10:06:06.682  [main] ConnectorManager - TAPDATA_MONGO_CONN env variable does not set, will use default 
[INFO ] 2025-07-01 10:06:06.683  [main] ConnectorManager - ssl env variable does not set, will use default false
[INFO ] 2025-07-01 10:06:06.683  [main] ConnectorManager - cloud_accessCode env variable does not set, will use default "".
[INFO ] 2025-07-01 10:06:06.684  [main] ConnectorManager - cloud_retryTime env variable does not set, will use default 3
[INFO ] 2025-07-01 10:06:06.686  [main] CloudSignUtil - ak/sk needSign false, accessKey null, secretKey null
[INFO ] 2025-07-01 10:06:06.686  [main] ConnectorManager - mode env variable does not set, will use default cluster
[INFO ] 2025-07-01 10:06:06.704  [main] ConnectorManager - 
Initialed variable
 - mongoURI: mongodb://wim:******@localhost:27017/tapdatavhadev?authSource=admin
 - ssl: false
 - sslCA: 
 - sslPEM: 
 - mongodbConnParams: 
 - baseURLs: [http://127.0.0.1:3000/api/]
 - accessCode: 
 - restRetryTime: 3
 - mode: cluster
 - app_type: DAAS
 - process id: wim_flow_engine
 - job tags: null
 - region: null
 - zone: null
 - worker dir: /Users/<USER>/IdeaProjects/tapdata-oss/workDir
[WARN ] 2025-07-01 10:06:07.163  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[WARN ] 2025-07-01 10:06:07.184  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[INFO ] 2025-07-01 10:06:07.322  [main] ConnectorManager - Available processors number: 12
[INFO ] 2025-07-01 10:06:07.325  [main] ConnectorManager - Java class path: /Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/iengine-app/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-enterprise/iengine-enterprise/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/api/target/classes:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.7/spring-test-6.2.7.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.3.0/mongodb-driver-sync-5.3.0.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.4.0/bson-5.4.0.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.4.0/mongodb-driver-core-5.4.0.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.4.0/bson-record-codec-5.4.0.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-modules/tapdata-storage-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-pdk-runner/target/classes:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/github/luben/zstd-jni/1.5.2-5/zstd-jni-1.5.2-5.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rocksdb/rocksdbjni/7.3.1/rocksdbjni-7.3.1.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-modules/async-tools-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/proxy-client-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-modules/websocket-client-module/target/classes:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.119.Final/netty-all-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.119.Final/netty-codec-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.119.Final/netty-codec-dns-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-haproxy/4.1.119.Final/netty-codec-haproxy-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.119.Final/netty-codec-http-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.119.Final/netty-codec-http2-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-memcache/4.1.119.Final/netty-codec-memcache-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-mqtt/4.1.119.Final/netty-codec-mqtt-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-redis/4.1.119.Final/netty-codec-redis-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-smtp/4.1.119.Final/netty-codec-smtp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.119.Final/netty-codec-socks-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-stomp/4.1.119.Final/netty-codec-stomp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-xml/4.1.119.Final/netty-codec-xml-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.119.Final/netty-handler-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.119.Final/netty-handler-proxy-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-ssl-ocsp/4.1.119.Final/netty-handler-ssl-ocsp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.119.Final/netty-resolver-dns-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-rxtx/4.1.119.Final/netty-transport-rxtx-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-sctp/4.1.119.Final/netty-transport-sctp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-udt/4.1.119.Final/netty-transport-udt-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-kqueue/4.1.119.Final/netty-transport-classes-kqueue-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.119.Final/netty-resolver-dns-classes-macos-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-aarch_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-riscv64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-kqueue/4.1.119.Final/netty-transport-native-kqueue-4.1.119.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-kqueue/4.1.119.Final/netty-transport-native-kqueue-4.1.119.Final-osx-aarch_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.119.Final/netty-resolver-dns-native-macos-4.1.119.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.119.Final/netty-resolver-dns-native-macos-4.1.119.Final-osx-aarch_64.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-modules/modules-api/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-api/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-pdk-api/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/observable-module/target/classes:/Users/<USER>/.m2/repository/net/openhft/chronicle-queue/5.21.91/chronicle-queue-5.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-core/2.21.91/chronicle-core-2.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-analytics/2.21ea0/chronicle-analytics-2.21ea0.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-bytes/2.21.89/chronicle-bytes-2.21.89.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-wire/2.21.91/chronicle-wire-2.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/compiler/2.21ea80/compiler-2.21ea80.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-threads/2.21.86/chronicle-threads-2.21.86.jar:/Users/<USER>/.m2/repository/net/openhft/affinity/3.21ea5/affinity-3.21ea5.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.4/commons-cli-1.4.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/milestone-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/skip-error-event-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/test-run-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/deduction-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/custom-sql-filter-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-modules/service-skeleton-module/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-modules/script-engines-module/target/classes:/Users/<USER>/.m2/repository/org/graalvm/js/js-language/24.1.2/js-language-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/regex/regex/24.1.2/regex-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-api/24.1.2/truffle-api-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/shadowed/icu4j/24.1.2/icu4j-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-runtime/24.1.2/truffle-runtime-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-enterprise/24.1.2/truffle-enterprise-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-compiler/24.1.2/truffle-compiler-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/jniutils/24.1.2/jniutils-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/nativebridge/24.1.2/nativebridge-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/js/js-scriptengine/24.1.2/js-scriptengine-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/polyglot/polyglot/24.1.2/polyglot-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/collections/24.1.2/collections-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/nativeimage/24.1.2/nativeimage-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/word/24.1.2/word-24.1.2.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/33.4.0-jre/guava-33.4.0-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.43.0/checker-qual-3.43.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.18.0/commons-io-2.18.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.0/spring-boot-starter-web-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.0/spring-boot-starter-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.0/spring-boot-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.0/spring-boot-autoconfigure-3.5.0.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.0/spring-boot-starter-json-3.5.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.0/jackson-module-parameter-names-2.19.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.0/spring-boot-starter-tomcat-3.5.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.0/micrometer-observation-1.15.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.0/micrometer-commons-1.15.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.7/spring-webmvc-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/9.0.0.CR1/hibernate-validator-9.0.0.CR1.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.0/jackson-dataformat-yaml-2.19.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.4/snakeyaml-2.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.7/spring-websocket-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.7/spring-messaging-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0-M3/commons-collections4-4.5.0-M3.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.0/spring-boot-starter-jdbc-3.5.0.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.7/spring-jdbc-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.7/spring-tx-6.2.7.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.3/jsqlparser-4.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-1.2-api/2.17.1/log4j-1.2-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.0/spring-boot-starter-data-mongodb-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.0/spring-data-mongodb-4.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.0/spring-data-commons-3.5.0.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/iengine-common/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/Users/<USER>/.m2/repository/org/json/json/20250107/json-20250107.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.12.1/gson-2.12.1.jar:/Users/<USER>/.m2/repository/commons-net/commons-net/3.11.1/commons-net-3.11.1.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.11.0/commons-beanutils-1.11.0.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.3/commons-logging-1.1.3.jar:/Users/<USER>/.m2/repository/org/dom4j/dom4j/2.1.4/dom4j-2.1.4.jar:/Users/<USER>/.m2/repository/com/github/albfernandez/juniversalchardet/2.3.0/juniversalchardet-2.3.0.jar:/Users/<USER>/.m2/repository/org/apache/avro/avro/1.12.0/avro-1.12.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka_2.13/3.9.1/kafka_2.13-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-server-common/3.9.1/kafka-server-common-3.9.1.jar:/Users/<USER>/.m2/repository/org/pcollections/pcollections/4.0.1/pcollections-4.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-group-coordinator-api/3.9.1/kafka-group-coordinator-api-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-group-coordinator/3.9.1/kafka-group-coordinator-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-transaction-coordinator/3.9.1/kafka-transaction-coordinator-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-metadata/3.9.1/kafka-metadata-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-storage-api/3.9.1/kafka-storage-api-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-tools-api/3.9.1/kafka-tools-api-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-raft/3.9.1/kafka-raft-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-storage/3.9.1/kafka-storage-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-server/3.9.1/kafka-server-3.9.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/argparse4j/argparse4j/0.7.0/argparse4j-0.7.0.jar:/Users/<USER>/.m2/repository/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar:/Users/<USER>/.m2/repository/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-csv/2.19.0/jackson-dataformat-csv-2.19.0.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/Users/<USER>/.m2/repository/org/bitbucket/b_c/jose4j/0.9.4/jose4j-0.9.4.jar:/Users/<USER>/.m2/repository/com/yammer/metrics/metrics-core/2.2.0/metrics-core-2.2.0.jar:/Users/<USER>/.m2/repository/org/scala-lang/modules/scala-collection-compat_2.13/2.10.0/scala-collection-compat_2.13-2.10.0.jar:/Users/<USER>/.m2/repository/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar:/Users/<USER>/.m2/repository/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar:/Users/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.1.12.1/metrics-core-4.1.12.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-clients/3.9.1/kafka-clients-3.9.1.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.10.7/snappy-java-1.1.10.7.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-streams/3.9.1/kafka-streams-3.9.1.jar:/Users/<USER>/.m2/repository/org/quartz-scheduler/quartz/2.5.0/quartz-2.5.0.jar:/Users/<USER>/.m2/repository/com/hankcs/hanlp/portable-1.5.3/hanlp-portable-1.5.3.jar:/Users/<USER>/.m2/repository/com/hankcs/nlp/hanlp-lucene-plugin/1.1.2/hanlp-lucene-plugin-1.1.2.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analysis-common/9.12.1/lucene-analysis-common-9.12.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/9.12.1/lucene-queryparser-9.12.1.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/Users/<USER>/.m2/repository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/Users/<USER>/.m2/repository/com/vividsolutions/jts/1.13/jts-1.13.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar:/Users/<USER>/.m2/repository/com/github/os72/protobuf-dynamic/1.0.1/protobuf-dynamic-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java-util/4.29.3/protobuf-java-util-4.29.3.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/4.29.3/protobuf-java-4.29.3.jar:/Users/<USER>/.m2/repository/org/voovan/voovan-framework/4.3.8/voovan-framework-4.3.8.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/manager/tm-sdk/target/classes:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_httpserver/0.16.0/simpleclient_httpserver-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.16.0/simpleclient_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_hotspot/0.16.0/simpleclient_hotspot-0.16.0.jar:/Users/<USER>/.m2/repository/com/github/oshi/oshi-core/5.8.3/oshi-core-5.8.3.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.10.0/jna-5.10.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna-platform/5.10.0/jna-platform-5.10.0.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/plugin-kit/tapdata-common/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/manager/tm-common/target/classes:/Users/<USER>/.m2/repository/com/tapdata/common/2.2.2/common-2.2.2.jar:/Users/<USER>/.m2/repository/ognl/ognl/3.1.26/ognl-3.1.26.jar:/Users/<USER>/.m2/repository/io/github/openlg/graphlib/1.1.0/graphlib-1.1.0.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.36/hutool-crypto-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-json/5.8.36/hutool-json-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-extra/5.8.36/hutool-extra-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-setting/5.8.36/hutool-setting-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-log/5.8.36/hutool-log-5.8.36.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.36/hutool-core-5.8.36.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.12.0/okhttp-4.12.0.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.6.0/okio-3.6.0.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.6.0/okio-jvm-3.6.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.9.25/kotlin-stdlib-common-1.9.25.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.9.25/kotlin-stdlib-jdk8-1.9.25.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.9.25/kotlin-stdlib-jdk7-1.9.25.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/2.1.10/kotlin-stdlib-2.1.10.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.0/commons-text-1.13.0.jar:/Users/<USER>/.m2/repository/com/hazelcast/hazelcast/5.5.0/hazelcast-5.5.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.18.3/jackson-dataformat-xml-2.18.3.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2/stax2-api-4.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/7.1.0/woodstox-core-7.1.0.jar:/Users/<USER>/.m2/repository/com/hazelcast/hazelcast-persistence/5.5.0-SNAPSHOT/hazelcast-persistence-5.5.0-20250513.112933-3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/mapdb/mapdb/3.0.8/mapdb-3.0.8.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections-api/13.0.0/eclipse-collections-api-13.0.0.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections/13.0.0/eclipse-collections-13.0.0.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections-forkjoin/13.0.0/eclipse-collections-forkjoin-13.0.0.jar:/Users/<USER>/.m2/repository/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0.jar:/Users/<USER>/.m2/repository/org/mapdb/elsa/3.0.0-M5/elsa-3.0.0-M5.jar:/Users/<USER>/.m2/repository/it/unimi/dsi/fastutil/8.5.13/fastutil-8.5.13.jar:/Users/<USER>/.m2/repository/org/openjdk/nashorn/nashorn-core/15.6/nashorn-core-15.6.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-commons/7.3.1/asm-commons-7.3.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-analysis/7.3.1/asm-analysis-7.3.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-tree/7.1/asm-tree-7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-util/7.3.1/asm-util-7.3.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.4.4/httpclient5-5.4.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.3.4/httpcore5-5.3.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.3.4/httpcore5-h2-5.3.4.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/validator/target/classes:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.5/commons-csv-1.5.jar:/Users/<USER>/.m2/repository/org/simplejavamail/simple-java-mail/5.0.3/simple-java-mail-5.0.3.jar:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.0/javax.mail-1.6.0.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/repository/com/github/bbottema/emailaddress-rfc2822/1.0.1/emailaddress-rfc2822-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.1/jsr305-3.0.1.jar:/Users/<USER>/.m2/repository/org/samba/jcifs/jcifs/1.3.17/jcifs-1.3.17.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/error-code-root/error-code-core/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/error-code-root/pdk-error-code/target/classes:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-common-lib/error-code-root/error-code-scanner/target/classes:/Users/<USER>/.m2/repository/org/reflections/reflections/0.10.2/reflections-0.10.2.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.28.0-GA/javassist-3.28.0-GA.jar:/Users/<USER>/IdeaProjects/tapdata-oss/tapdata/iengine/modules/task-resource-supervisor-module/target/classes:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/9.12.1/lucene-core-9.12.1.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Applications/IntelliJ IDEA.app/Contents/lib/idea_rt.jar.
 Agent version: -
[INFO ] 2025-07-01 10:06:07.489  [main] ConnectorManager - Login params: accessCode=3324cfdf-7d3e-4792-bd32-571638d4562f, endpoint=[http://127.0.0.1:3000/api/]
[INFO ] 2025-07-01 10:06:07.548  [main] Version - flow engine version: 
[INFO ] 2025-07-01 10:06:08.649  [main] SettingService - [Setting] - Loading tapdata settings...
[java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.common.SettingService.loadSettings(SettingService.java:40), io.tapdata.Schedule.ConnectorManager.init(ConnectorManager.java:216), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529), org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339), org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373), org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337), org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202), org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222), org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188), org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123), org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987), org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627), org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753), org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439), org.springframework.boot.SpringApplication.run(SpringApplication.java:318), io.tapdata.Application.main(Application.java:133)]
[INFO ] 2025-07-01 10:06:11.030  [main] TransformerManager - Transformer init variables
 - process id: wim_flow_engine
 - job tags: 
 - region: 
 - zone: 
 - worker dir: /Users/<USER>/IdeaProjects/tapdata-oss/workDir
[INFO ] 2025-07-01 10:06:11.087  [main] HazelcastUtil - -- listing properties --
hazelcast.operation.call.timeout.millis=300000
hazelcast.logging.type=log4j2

[INFO ] 2025-07-01 10:06:11.132  [main] AddressPicker - [LOCAL] [dev] [5.5.0] Interfaces is disabled, trying to pick one address from TCP-IP config addresses: []
[WARN ] 2025-07-01 10:06:11.132  [main] AddressPicker - [LOCAL] [dev] [5.5.0] Could not find a matching address to start with! Picking one of non-loopback addresses.
[INFO ] 2025-07-01 10:06:11.165  [main] logo - [*******]:5701 [dev] [5.5.0] 
	+       +  o    o     o     o---o o----o o      o---o     o     o----o o--o--o
	+ +   + +  |    |    / \       /  |      |     /         / \    |         |   
	+ + + + +  o----o   o   o     o   o----o |    o         o   o   o----o    |   
	+ +   + +  |    |  /     \   /    |      |     \       /     \       |    |   
	+       +  o    o o       o o---o o----o o----o o---o o       o o----o    o   
[INFO ] 2025-07-01 10:06:11.166  [main] system - [*******]:5701 [dev] [5.5.0] Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
[INFO ] 2025-07-01 10:06:11.166  [main] system - [*******]:5701 [dev] [5.5.0] Hazelcast Platform 5.5.0 (20250306 - 658e6f0) starting at [*******]:5701
[INFO ] 2025-07-01 10:06:11.166  [main] system - [*******]:5701 [dev] [5.5.0] Cluster name: dev
[INFO ] 2025-07-01 10:06:11.166  [main] system - [*******]:5701 [dev] [5.5.0] Integrity Checker is disabled. Fail-fast on corrupted executables will not be performed. For more information, see the documentation for Integrity Checker.
[INFO ] 2025-07-01 10:06:11.166  [main] system - [*******]:5701 [dev] [5.5.0] Jet is enabled
[INFO ] 2025-07-01 10:06:11.504  [main] security - [*******]:5701 [dev] [5.5.0] Enable DEBUG/FINE log level for log category com.hazelcast.system.security  or use -Dhazelcast.security.recommendations system property to see 🔒 security recommendations and the status of current config.
[INFO ] 2025-07-01 10:06:11.556  [main] Node - [*******]:5701 [dev] [5.5.0] Using TCP/IP discovery
[WARN ] 2025-07-01 10:06:11.557  [main] CPSubsystem - [*******]:5701 [dev] [5.5.0] CP Subsystem is not enabled. CP data structures will operate in UNSAFE mode! Please note that UNSAFE mode will not provide strong consistency guarantees.
[INFO ] 2025-07-01 10:06:11.834  [main] JetServiceBackend - [*******]:5701 [dev] [5.5.0] Setting number of cooperative threads and default parallelism to 12
[INFO ] 2025-07-01 10:06:11.843  [main] Diagnostics - [*******]:5701 [dev] [5.5.0] Diagnostics disabled. To enable add -Dhazelcast.diagnostics.enabled=true to the JVM arguments.
[INFO ] 2025-07-01 10:06:11.847  [main] LifecycleService - [*******]:5701 [dev] [5.5.0] [*******]:5701 is STARTING
[INFO ] 2025-07-01 10:06:11.870  [main] ClusterService - [*******]:5701 [dev] [5.5.0] 

Members {size:1, ver:1} [
	Member [*******]:5701 - 23121649-073a-474f-8708-d15ea0c42ea9 this
]

[INFO ] 2025-07-01 10:06:11.877  [main] JobCoordinationService - [*******]:5701 [dev] [5.5.0] Jet started scanning for jobs
[INFO ] 2025-07-01 10:06:11.880  [main] LifecycleService - [*******]:5701 [dev] [5.5.0] [*******]:5701 is STARTED
[INFO ] 2025-07-01 10:06:11.898  [main] TapdataTaskScheduler - [Task scheduler] instance no: wim_flow_engine
[WARN ] 2025-07-01 10:06:11.961  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[WARN ] 2025-07-01 10:06:11.967  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[INFO ] 2025-07-01 10:06:12.051  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://127.0.0.1:3000/ws/agent?agentId=wim_flow_engine&access_token=529f20a3726d4ac1ba4cb7b071db7b27a35c92ac3a484cfcb374b77746e7f4d4&singletonTag=4d74a802-9fff-4ceb-814c-a9bd2614ef12
[INFO ] 2025-07-01 10:06:12.260  [main] Application - Started Application in 7.569 seconds (process running for 9.096)
[INFO ] 2025-07-01 10:06:12.275  [main] PDK - Application [Looking for Aspect annotations...]
[INFO ] 2025-07-01 10:06:12.352  [main] PDK - Application [Looking for Aspect annotations takes 75]
[INFO ] 2025-07-01 10:06:12.356  [main] StartResultUtil - Write start result to file: /Users/<USER>/IdeaProjects/tapdata-oss/workDir/.agentStartMsg.json
  {"msg":"","codeVersion":"2025-06-28T15:51:48Z","jvmZoneId":"Asia/Hong_Kong","osZoneId":"Asia/Shanghai","version":"-","status":"ok"}
[WARN ] 2025-07-01 10:06:13.259  [main] PDK - ApplicationStartAspectHandler [Can not load python engine, msg: Cannot invoke "javax.script.ScriptEngine.eval(String)" because "scriptEnginePy" is null]
[INFO ] 2025-07-01 10:06:13.260  [main] TapdataTaskScheduler - Stop task which agent id is wim_flow_engine and status is stopping
[INFO ] 2025-07-01 10:06:13.280  [main] ConnectorNodeService - Global connector thread pool started, interval ms: 300000, timeout ms: 1800000
[INFO ] 2025-07-01 10:12:08.451  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_wim_flow_engine]]
[INFO ] 2025-07-01 10:12:08.451  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-07-01 10:12:51.383  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1310688310', storageMode=MongoDB, inMemSize=1000, uri='mongodb://wim:******@localhost:27017/tapdatavhadev?authSource=admin', database='tapdatavhadev', collection='ExternalStorage_1310688310', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-07-01 10:12:51.633  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Init IMap store config succeed, name: 1310688310
[INFO ] 2025-07-01 10:12:51.665  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_7df450d1-d1dc-4a2c-8c0b-bab6db43d7ca', storageMode=MongoDB, inMemSize=1000, uri='mongodb://wim:******@localhost:27017/tapdatavhadev?authSource=admin', database='tapdatavhadev', collection='ExternalStorage_PdkStateMap_7df450d1-d1dc-4a2c-8c0b-bab6db43d7ca', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-07-01 10:12:51.888  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_7df450d1-d1dc-4a2c-8c0b-bab6db43d7ca
[INFO ] 2025-07-01 10:12:51.940  [Thread-websocket-handle-message--2-thread-17] PartitionStateManager - [*******]:5701 [dev] [5.5.0] Initializing cluster partition table arrangement...
[INFO ] 2025-07-01 10:12:52.180  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='GlobalStateMap', storageMode=MongoDB, inMemSize=1000, uri='mongodb://wim:******@localhost:27017/tapdatavhadev?authSource=admin', database='tapdatavhadev', collection='HazelcastPersistence', exclusiveCollection=false, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-07-01 10:12:52.229  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Init IMap store config succeed, name: GlobalStateMap
[ERROR] 2025-07-01 10:12:52.294  [Thread-websocket-handle-message--2-thread-17] PDK - ExternalJarManager [Copy encrypted jar file /Users/<USER>/IdeaProjects/tapdata-oss/dist/sybase-connector-v1.0-SNAPSHOT__68623f8a83db06530f8fb9ba__.jar to /Users/<USER>/IdeaProjects/tapdata-oss/connectors/tap-running/sybase-connector-v1.0-SNAPSHOT__68623f8a83db06530f8fb9ba___4628a667-f98a-4457-babd-249b329037b9.jar failed]
[INFO ] 2025-07-01 10:12:52.959  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-1293440239', storageMode=MongoDB, inMemSize=1000, uri='mongodb://wim:******@localhost:27017/tapdatavhadev?authSource=admin', database='tapdatavhadev', collection='ExternalStorage_-1293440239', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-07-01 10:12:53.156  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Init IMap store config succeed, name: -1293440239
[INFO ] 2025-07-01 10:12:53.159  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_e160ca77-b43e-4e20-92a9-8193f52c0696', storageMode=MongoDB, inMemSize=1000, uri='mongodb://wim:******@localhost:27017/tapdatavhadev?authSource=admin', database='tapdatavhadev', collection='ExternalStorage_PdkStateMap_e160ca77-b43e-4e20-92a9-8193f52c0696', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-07-01 10:12:53.351  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_e160ca77-b43e-4e20-92a9-8193f52c0696
[ERROR] 2025-07-01 10:12:53.434  [Thread-websocket-handle-message--2-thread-17] PDK - ExternalJarManager [Copy encrypted jar file /Users/<USER>/IdeaProjects/tapdata-oss/dist/postgres-connector-v1.0-SNAPSHOT__685a094e6389fb6bc2f6d9d3__.jar to /Users/<USER>/IdeaProjects/tapdata-oss/connectors/tap-running/postgres-connector-v1.0-SNAPSHOT__685a094e6389fb6bc2f6d9d3___b1ad98d2-6faa-478e-828d-4fe3a5edac08.jar failed]
[INFO ] 2025-07-01 10:12:54.431  [Thread-websocket-handle-message--2-thread-17] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='wim_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=686343ff3f2f272f5eafc399, opType=reset, force=false}}, code='null'}.
