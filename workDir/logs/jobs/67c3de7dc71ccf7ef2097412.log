[TRACE] 2025-03-02 12:33:47.991 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 12:33:47.992 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 12:33:48.188 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 12:33:48.305 - [任务 30] - <PERSON>de performs snapshot read asynchronously 
[TRACE] 2025-03-02 12:33:48.450 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 12:33:48.533 - [任务 30] - Task started 
[TRACE] 2025-03-02 12:33:48.536 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:33:48.538 - [任务 30][Sybase] - Node Sybase[2362957d-bdab-49d2-b458-691de78f16ca] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:33:48.542 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 1 ms 
[TRACE] 2025-03-02 12:33:48.543 - [任务 30][Sybase] - Node Sybase[2362957d-bdab-49d2-b458-691de78f16ca] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 12:33:48.747 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 12:33:49.304 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 12:33:49.304 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 12:33:49.305 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 12:33:49.310 - [任务 30][Sybase] - Apply table structure to target database 
[TRACE] 2025-03-02 12:34:29.492 - [任务 30][Sybase] - The table testTimeStampWithIndex has already exist. 
[INFO ] 2025-03-02 12:34:29.708 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 12:34:29.709 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 12:34:29.709 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 12:34:29.709 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-03-02 12:34:29.729 - [任务 30][Sybase] - Table: testTimeStampWithIndex already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-02 12:34:30.337 - [任务 30][Sybase] - The table testTimeStampWithNoIndex has already exist. 
[WARN ] 2025-03-02 12:34:30.686 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:34:30.777 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_74ecc387_a933_4a30_a05f_f647e9d59521 
[INFO ] 2025-03-02 12:34:30.779 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 12:34:30.779 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 12:34:30.963 - [任务 30][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-03-02 12:34:30.965 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 12:34:30.966 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 12:34:30.966 - [任务 30][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[INFO ] 2025-03-02 12:34:31.098 - [任务 30][Pg] - Table testTimeStampWithIndex has been completed batch read 
[INFO ] 2025-03-02 12:34:31.098 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 12:34:31.098 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 12:34:31.163 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[INFO ] 2025-03-02 12:34:31.228 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 12:34:31.228 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 12:34:31.228 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 12:34:31.228 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 12:34:31.229 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 12:34:31.229 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithIndex, testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 12:34:31.229 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 12:34:31.229 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-03-02 12:34:31.296 - [任务 30][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_ithNoIndex021292472fb1  on  lisTest.dbo.testTimeStampWithNoIndex ( _no_pk_hash  asc)] 
[INFO ] 2025-03-02 12:34:31.296 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_74ecc387_a933_4a30_a05f_f647e9d59521 
[TRACE] 2025-03-02 12:34:32.849 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 12:34:32.856 - [任务 30][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 12:34:32.857 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-03-02 12:34:33.061 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithIndex, testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 12:46:20.335 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 12:49:31.481 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 12:49:31.487 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 12:49:32.349 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 12:49:32.485 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 12:49:32.487 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 12:49:32.654 - [任务 30] - Task started 
[TRACE] 2025-03-02 12:49:32.767 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:49:32.768 - [任务 30][Sybase] - Node Sybase[56184de8-6d37-4779-bdfe-4e59b14e02aa] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:49:32.771 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 12:49:32.772 - [任务 30][Sybase] - Node Sybase[56184de8-6d37-4779-bdfe-4e59b14e02aa] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 12:49:32.993 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 12:49:33.683 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 12:49:33.685 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 12:49:33.686 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 12:49:33.702 - [任务 30][Sybase] - Apply table structure to target database 
[INFO ] 2025-03-02 12:49:34.241 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 12:49:34.241 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 12:49:34.241 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 12:49:34.242 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 12:49:35.053 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:49:35.136 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_3936d797_3611_4179_897f_97edc21009e5 
[TRACE] 2025-03-02 12:49:35.144 - [任务 30][Sybase] - The table testTimeStampWithIndex has already exist. 
[INFO ] 2025-03-02 12:49:35.153 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 12:49:35.153 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 12:49:35.357 - [任务 30][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-03-02 12:49:35.357 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 12:49:35.367 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 12:49:35.367 - [任务 30][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[INFO ] 2025-03-02 12:49:35.530 - [任务 30][Pg] - Table testTimeStampWithIndex has been completed batch read 
[INFO ] 2025-03-02 12:49:35.536 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 12:49:35.538 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 12:49:35.598 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[TRACE] 2025-03-02 12:49:35.602 - [任务 30][Sybase] - Table: testTimeStampWithIndex already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2025-03-02 12:49:35.640 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 12:49:35.640 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 12:49:35.640 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 12:49:35.641 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 12:49:35.641 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 12:49:35.653 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithIndex, testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 12:49:35.656 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 12:49:35.729 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:49:35.729 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_3936d797_3611_4179_897f_97edc21009e5 
[TRACE] 2025-03-02 12:49:36.135 - [任务 30][Sybase] - The table testTimeStampWithNoIndex has already exist. 
[WARN ] 2025-03-02 12:49:37.152 - [任务 30][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_ithNoIndex021292472fb1  on  lisTest.dbo.testTimeStampWithNoIndex ( _no_pk_hash  asc)] 
[TRACE] 2025-03-02 12:49:37.973 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithIndex, testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 12:49:40.142 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 12:49:40.147 - [任务 30][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 12:49:40.150 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-03-02 12:50:04.302 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 12:50:04.487 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 12:50:04.518 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740890973667 
[TRACE] 2025-03-02 12:50:04.519 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740890973667 
[TRACE] 2025-03-02 12:50:04.523 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 12:50:04.524 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 12:50:04.535 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 343 ms 
[TRACE] 2025-03-02 12:50:04.535 - [任务 30][Sybase] - Node Sybase[56184de8-6d37-4779-bdfe-4e59b14e02aa] running status set to false 
[TRACE] 2025-03-02 12:50:04.823 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_56184de8-6d37-4779-bdfe-4e59b14e02aa_1740890973408 
[TRACE] 2025-03-02 12:50:04.824 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_56184de8-6d37-4779-bdfe-4e59b14e02aa_1740890973408 
[TRACE] 2025-03-02 12:50:04.825 - [任务 30][Sybase] - Node Sybase[56184de8-6d37-4779-bdfe-4e59b14e02aa] schema data cleaned 
[TRACE] 2025-03-02 12:50:04.827 - [任务 30][Sybase] - Node Sybase[56184de8-6d37-4779-bdfe-4e59b14e02aa] monitor closed 
[TRACE] 2025-03-02 12:50:05.033 - [任务 30][Sybase] - Node Sybase[56184de8-6d37-4779-bdfe-4e59b14e02aa] close complete, cost 294 ms 
[TRACE] 2025-03-02 12:50:08.135 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 12:50:08.136 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1aeb8c42 
[TRACE] 2025-03-02 12:50:08.293 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 12:50:08.295 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 12:50:08.296 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 12:50:08.340 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 12:50:08.342 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 12:50:08.343 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 12:50:33.206 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 12:50:33.207 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 12:50:33.499 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 12:50:33.602 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 12:50:33.602 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 12:50:33.657 - [任务 30] - Task started 
[TRACE] 2025-03-02 12:50:33.658 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:50:33.660 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:50:33.671 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 12:50:33.671 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 12:50:33.878 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 12:50:34.406 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 12:50:34.407 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 12:50:34.408 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 12:50:49.606 - [任务 30][Sybase] - Apply table structure to target database 
[TRACE] 2025-03-02 12:51:36.415 - [任务 30][Sybase] - The table testTimeStampWithIndex has already exist. 
[INFO ] 2025-03-02 12:51:36.629 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 12:51:36.630 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 12:51:36.630 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 12:51:36.631 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-03-02 12:51:36.832 - [任务 30][Sybase] - Table: testTimeStampWithIndex already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-02 12:51:36.976 - [任务 30][Sybase] - The table testTimeStampWithNoIndex has already exist. 
[WARN ] 2025-03-02 12:51:37.382 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:51:37.447 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_b5593f82_e4ee_411e_b2b5_315ca72daed6 
[INFO ] 2025-03-02 12:51:37.450 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 12:51:37.450 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 12:51:37.559 - [任务 30][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-03-02 12:51:37.561 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 12:51:37.561 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 12:51:37.565 - [任务 30][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[INFO ] 2025-03-02 12:51:37.708 - [任务 30][Pg] - Table testTimeStampWithIndex has been completed batch read 
[INFO ] 2025-03-02 12:51:37.709 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 12:51:37.709 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 12:51:37.801 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[WARN ] 2025-03-02 12:51:37.804 - [任务 30][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_ithNoIndex021292472fb1  on  lisTest.dbo.testTimeStampWithNoIndex ( _no_pk_hash  asc)] 
[INFO ] 2025-03-02 12:51:38.078 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 12:51:38.078 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 12:51:38.078 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 12:51:38.078 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 12:51:38.081 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 12:51:38.081 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithIndex, testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 12:51:38.081 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 12:51:38.152 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:51:38.152 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_b5593f82_e4ee_411e_b2b5_315ca72daed6 
[TRACE] 2025-03-02 12:51:40.409 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithIndex, testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 12:51:41.222 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 12:51:41.224 - [任务 30][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 12:51:41.225 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-03-02 12:51:47.716 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 12:51:48.118 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 12:51:48.134 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891034398 
[TRACE] 2025-03-02 12:51:48.134 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891034398 
[TRACE] 2025-03-02 12:51:48.135 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 12:51:48.135 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 12:51:48.137 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 493 ms 
[TRACE] 2025-03-02 12:51:48.141 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] running status set to false 
[TRACE] 2025-03-02 12:51:48.716 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_1bfe6bcb-a0e1-4827-8fd7-053ca25aa471_1740891034302 
[TRACE] 2025-03-02 12:51:48.718 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_1bfe6bcb-a0e1-4827-8fd7-053ca25aa471_1740891034302 
[TRACE] 2025-03-02 12:51:48.719 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] schema data cleaned 
[TRACE] 2025-03-02 12:51:48.724 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] monitor closed 
[TRACE] 2025-03-02 12:51:48.724 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] close complete, cost 586 ms 
[TRACE] 2025-03-02 12:51:51.065 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 12:51:51.067 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2edd076b 
[TRACE] 2025-03-02 12:51:51.218 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 12:51:51.219 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 12:51:51.219 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 12:51:51.220 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 12:51:51.249 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 12:51:51.249 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 12:53:51.575 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 12:53:51.577 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 12:53:51.784 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 12:53:51.941 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 12:53:51.941 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 12:53:51.985 - [任务 30] - Task started 
[TRACE] 2025-03-02 12:53:51.985 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:53:51.985 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 12:53:52.045 - [任务 30][Pg] - Enable partition table support for source database 
[TRACE] 2025-03-02 12:53:52.046 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] start preload schema,table counts: 2 
[TRACE] 2025-03-02 12:53:52.046 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 12:53:52.779 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 12:53:52.780 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 12:53:52.780 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 12:53:52.783 - [任务 30][Sybase] - Apply table structure to target database 
[INFO ] 2025-03-02 12:53:53.233 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 12:53:53.236 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 12:53:53.236 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 12:53:53.236 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 12:53:54.047 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:53:54.115 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_36962e2e_75cc_4615_bb6c_89475ef002e0 
[INFO ] 2025-03-02 12:53:54.116 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 12:53:54.116 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 12:53:54.298 - [任务 30][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-03-02 12:53:54.300 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 12:53:54.301 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 12:53:54.446 - [任务 30][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[INFO ] 2025-03-02 12:53:54.446 - [任务 30][Pg] - Table testTimeStampWithIndex has been completed batch read 
[INFO ] 2025-03-02 12:53:54.447 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 12:53:54.447 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 12:53:54.580 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[INFO ] 2025-03-02 12:53:54.581 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 12:53:54.582 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 12:53:54.582 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 12:53:54.582 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 12:53:54.588 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 12:53:54.588 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithIndex, testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 12:53:54.589 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 12:53:54.671 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:53:54.672 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_36962e2e_75cc_4615_bb6c_89475ef002e0 
[TRACE] 2025-03-02 12:53:55.849 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithIndex, testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 12:53:58.595 - [任务 30][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 12:53:58.598 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 12:53:58.598 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-03-02 13:02:18.086 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 13:02:18.428 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 13:02:18.433 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891232615 
[TRACE] 2025-03-02 13:02:18.434 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891232615 
[TRACE] 2025-03-02 13:02:18.434 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 13:02:18.446 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 13:02:18.447 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 367 ms 
[TRACE] 2025-03-02 13:02:18.447 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] running status set to false 
[TRACE] 2025-03-02 13:02:18.716 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_1bfe6bcb-a0e1-4827-8fd7-053ca25aa471_1740891232695 
[TRACE] 2025-03-02 13:02:18.758 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_1bfe6bcb-a0e1-4827-8fd7-053ca25aa471_1740891232695 
[TRACE] 2025-03-02 13:02:18.764 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] schema data cleaned 
[TRACE] 2025-03-02 13:02:18.764 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] monitor closed 
[TRACE] 2025-03-02 13:02:18.764 - [任务 30][Sybase] - Node Sybase[1bfe6bcb-a0e1-4827-8fd7-053ca25aa471] close complete, cost 277 ms 
[TRACE] 2025-03-02 13:02:21.841 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 13:02:21.842 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@326d0f86 
[TRACE] 2025-03-02 13:02:21.967 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 13:02:21.967 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 13:02:21.968 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 13:02:21.971 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 13:02:22.003 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:02:22.004 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[INFO ] 2025-03-02 13:02:23.280 - [任务 30] - This task already stopped. 
[TRACE] 2025-03-02 13:03:12.274 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 13:03:12.275 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 13:03:12.487 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 13:03:12.491 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 13:03:12.630 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 13:03:12.631 - [任务 30] - Task started 
[TRACE] 2025-03-02 13:03:12.700 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:03:12.701 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:03:12.701 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 13:03:12.703 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 13:03:12.914 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 13:03:13.461 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 13:03:13.462 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 13:03:13.462 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 13:03:13.474 - [任务 30][Sybase] - Apply table structure to target database 
[TRACE] 2025-03-02 13:03:14.087 - [任务 30][Sybase] - The table testTimeStampWithNoIndex has already exist. 
[TRACE] 2025-03-02 13:03:14.392 - [任务 30][Sybase] - Table: testTimeStampWithNoIndex already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2025-03-02 13:03:14.392 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 13:03:14.392 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 13:03:14.392 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 13:03:14.392 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 13:03:15.369 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:03:15.443 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_53e79350_0d79_46cd_9bb1_14e6f8ff7fbb 
[INFO ] 2025-03-02 13:03:15.581 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 13:03:15.581 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 13:03:15.582 - [任务 30][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-03-02 13:03:15.617 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 13:03:15.617 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 13:03:15.617 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 13:03:15.757 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[INFO ] 2025-03-02 13:03:15.758 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 13:03:15.758 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 13:03:15.759 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 13:03:15.759 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 13:03:15.759 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 13:03:15.761 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 13:03:15.761 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 13:03:15.829 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:03:15.829 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_53e79350_0d79_46cd_9bb1_14e6f8ff7fbb 
[TRACE] 2025-03-02 13:03:17.405 - [任务 30][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unable to write data to sybase due to violation of unique constraint.
 - Table name: testTimeStampWithNoIndex
 - Target field: ["_no_pk_hash"]
 - Data to be written(null): null
 - Unique constraint: null 
[ERROR] 2025-03-02 13:03:17.459 - [任务 30][Sybase] - Unable to write data to sybase due to violation of unique constraint.
 - Table name: testTimeStampWithNoIndex
 - Target field: ["_no_pk_hash"]
 - Data to be written(null): null
 - Unique constraint: null <-- Error Message -->
Unable to write data to sybase due to violation of unique constraint.
 - Table name: testTimeStampWithNoIndex
 - Target field: ["_no_pk_hash"]
 - Data to be written(null): null
 - Unique constraint: null

<-- Simple Stack Trace -->
Caused by: com.sybase.jdbc4.jdbc.SybBatchUpdateException: JZ0BE: BatchUpdateException: Error occurred while executing batch statement: Can't update a TIMESTAMP column.

	com.sybase.jdbc4.jdbc.ErrorMessage.raiseBatchUpdateException(ErrorMessage.java:1331)
	com.sybase.jdbc4.jdbc.SybStatement.batchLoop(SybStatement.java:2380)
	com.sybase.jdbc4.jdbc.SybPreparedStatement.executeBatch(SybPreparedStatement.java:2282)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:132)
	io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	...

<-- Full Stack Trace -->
com.sybase.jdbc4.jdbc.SybBatchUpdateException: JZ0BE: BatchUpdateException: Error occurred while executing batch statement: Can't update a TIMESTAMP column.

	at io.tapdata.sybase.SybaseExceptionCollector.collectViolateUnique(SybaseExceptionCollector.java:28)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:139)
	at io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:620)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$68(HazelcastTargetPdkDataNode.java:1100)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$69(HazelcastTargetPdkDataNode.java:1096)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1051)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1026)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$36(HazelcastTargetPdkDataNode.java:725)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:924)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:842)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:791)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:751)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:637)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:723)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:722)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.sybase.jdbc4.jdbc.SybBatchUpdateException: JZ0BE: BatchUpdateException: Error occurred while executing batch statement: Can't update a TIMESTAMP column.

	at com.sybase.jdbc4.jdbc.ErrorMessage.raiseBatchUpdateException(ErrorMessage.java:1331)
	at com.sybase.jdbc4.jdbc.SybStatement.batchLoop(SybStatement.java:2380)
	at com.sybase.jdbc4.jdbc.SybPreparedStatement.executeBatch(SybPreparedStatement.java:2282)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:132)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	... 32 more

[TRACE] 2025-03-02 13:03:17.461 - [任务 30][Sybase] - Job suspend in error handle 
[TRACE] 2025-03-02 13:03:17.560 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[INFO ] 2025-03-02 13:03:17.562 - [任务 30][Pg] - Retry operation null failed, total cost 05:03:17.538000 
[TRACE] 2025-03-02 13:03:17.574 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891793544 
[TRACE] 2025-03-02 13:03:17.574 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891793544 
[TRACE] 2025-03-02 13:03:17.574 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 13:03:17.574 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 13:03:17.578 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 69 ms 
[TRACE] 2025-03-02 13:03:17.595 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] running status set to false 
[TRACE] 2025-03-02 13:03:17.595 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 13:03:17.596 - [任务 30][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.InterruptedException 
[ERROR] 2025-03-02 13:03:17.807 - [任务 30][Pg] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:782)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:664)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:134)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	... 14 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at org.apache.kafka.connect.storage.OffsetStorageReaderImpl.offsets(OffsetStorageReaderImpl.java:101)
	at io.debezium.connector.common.BaseSourceTask.getPreviousOffset(BaseSourceTask.java:311)
	at io.debezium.connector.postgresql.PostgresConnectorTask.start(PostgresConnectorTask.java:88)
	at io.debezium.connector.common.BaseSourceTask.start(BaseSourceTask.java:130)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:759)
	at io.tapdata.connector.postgres.cdc.DebeziumCdcRunner.startCdcRunner(DebeziumCdcRunner.java:44)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[TRACE] 2025-03-02 13:03:18.092 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_83232b05-b0c4-49bf-836b-8bcfcbfcbcbe_1740891793349 
[TRACE] 2025-03-02 13:03:18.095 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_83232b05-b0c4-49bf-836b-8bcfcbfcbcbe_1740891793349 
[TRACE] 2025-03-02 13:03:18.095 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] schema data cleaned 
[TRACE] 2025-03-02 13:03:18.095 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] monitor closed 
[TRACE] 2025-03-02 13:03:18.098 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] close complete, cost 519 ms 
[INFO ] 2025-03-02 13:03:22.077 - [任务 30] - Task [任务 30] cannot retry, reason: Task retry service not start 
[TRACE] 2025-03-02 13:03:22.077 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 13:03:22.077 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@867f2c6 
[TRACE] 2025-03-02 13:03:22.208 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 13:03:22.208 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 13:03:22.208 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 13:03:22.209 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 13:03:22.246 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:03:22.247 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:03:40.837 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 13:03:40.837 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 13:03:41.025 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 13:03:41.025 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 13:03:41.084 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 13:03:41.084 - [任务 30] - Task started 
[TRACE] 2025-03-02 13:03:41.116 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:03:41.117 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:03:41.117 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 13:03:41.117 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 13:03:41.117 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 13:03:41.962 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 13:03:41.963 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 13:03:41.964 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 13:03:41.964 - [任务 30][Sybase] - Apply table structure to target database 
[INFO ] 2025-03-02 13:03:42.287 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 13:03:42.288 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 13:03:42.288 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 13:03:42.288 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 13:03:43.277 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:03:43.581 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_abda7d35_a4b7_4b0c_af02_7cee284e64a9 
[INFO ] 2025-03-02 13:03:43.587 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 13:03:43.589 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 13:03:43.681 - [任务 30][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-03-02 13:03:43.683 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 13:03:43.684 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 13:03:43.685 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 13:03:43.807 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[INFO ] 2025-03-02 13:03:43.807 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 13:03:43.807 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 13:03:43.807 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 13:03:43.808 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 13:03:43.808 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 13:03:43.814 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 13:03:43.816 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 13:03:43.881 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:03:43.881 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_abda7d35_a4b7_4b0c_af02_7cee284e64a9 
[TRACE] 2025-03-02 13:03:44.803 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 13:03:44.808 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-03-02 13:03:45.615 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 13:06:29.082 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 13:06:29.389 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 13:06:29.390 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891821700 
[TRACE] 2025-03-02 13:06:29.391 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740891821700 
[TRACE] 2025-03-02 13:06:29.391 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 13:06:29.396 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 13:06:29.396 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 329 ms 
[TRACE] 2025-03-02 13:06:29.605 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] running status set to false 
[TRACE] 2025-03-02 13:06:29.639 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_83232b05-b0c4-49bf-836b-8bcfcbfcbcbe_1740891821880 
[TRACE] 2025-03-02 13:06:29.639 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_83232b05-b0c4-49bf-836b-8bcfcbfcbcbe_1740891821880 
[TRACE] 2025-03-02 13:06:29.640 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] schema data cleaned 
[TRACE] 2025-03-02 13:06:29.640 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] monitor closed 
[TRACE] 2025-03-02 13:06:29.846 - [任务 30][Sybase] - Node Sybase[83232b05-b0c4-49bf-836b-8bcfcbfcbcbe] close complete, cost 245 ms 
[TRACE] 2025-03-02 13:06:32.419 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 13:06:32.424 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@75a680bd 
[TRACE] 2025-03-02 13:06:32.424 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 13:06:32.559 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 13:06:32.559 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 13:06:32.592 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 13:06:32.594 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:06:32.594 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:07:32.102 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 13:07:32.104 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 13:07:32.511 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 13:07:32.511 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 13:07:32.570 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 13:07:32.570 - [任务 30] - Task started 
[TRACE] 2025-03-02 13:07:32.607 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:07:32.609 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:07:32.609 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 13:07:32.609 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 13:07:32.810 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 13:07:33.770 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 13:07:33.780 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 13:07:33.780 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 13:07:33.991 - [任务 30][Sybase] - Apply table structure to target database 
[INFO ] 2025-03-02 13:07:33.998 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 13:07:33.999 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 13:07:34.000 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[INFO ] 2025-03-02 13:07:34.008 - [任务 30][Pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-03-02 13:07:34.009 - [任务 30][Pg] - Use existing batch read offset: {"testTimeStampWithNoIndex":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":13471615856,\"lsn_commit\":13471615856,\"lsn\":13471615856,\"ts_usec\":1740891987548078}"} 
[TRACE] 2025-03-02 13:07:34.009 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 13:07:34.228 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 13:07:34.233 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 13:07:34.233 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 13:07:34.234 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":13471615856,\"lsn_commit\":13471615856,\"lsn\":13471615856,\"ts_usec\":1740891987548078}"} 
[INFO ] 2025-03-02 13:07:34.238 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 13:07:34.246 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:07:34.474 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_abda7d35_a4b7_4b0c_af02_7cee284e64a9 
[TRACE] 2025-03-02 13:07:36.504 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 13:09:14.247 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 13:09:14.639 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 13:09:14.653 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740892052896 
[TRACE] 2025-03-02 13:09:14.653 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740892052896 
[TRACE] 2025-03-02 13:09:14.654 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 13:09:14.654 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 13:09:14.656 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 424 ms 
[TRACE] 2025-03-02 13:09:14.656 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] running status set to false 
[TRACE] 2025-03-02 13:09:14.956 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_d6c7fd12-2fe3-4219-8b79-8a7e136cf084_1740892053111 
[TRACE] 2025-03-02 13:09:14.957 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_d6c7fd12-2fe3-4219-8b79-8a7e136cf084_1740892053111 
[TRACE] 2025-03-02 13:09:14.958 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] schema data cleaned 
[TRACE] 2025-03-02 13:09:14.961 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] monitor closed 
[TRACE] 2025-03-02 13:09:14.961 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] close complete, cost 305 ms 
[TRACE] 2025-03-02 13:09:17.762 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 13:09:17.764 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1b3f486c 
[TRACE] 2025-03-02 13:09:17.768 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 13:09:17.893 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 13:09:17.894 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 13:09:17.926 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 13:09:17.929 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:09:17.929 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:09:41.710 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 13:09:41.792 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 13:09:41.792 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 13:09:41.877 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 13:09:41.881 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 13:09:41.926 - [任务 30] - Task started 
[TRACE] 2025-03-02 13:09:41.929 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:09:41.929 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] start preload schema,table counts: 1 
[TRACE] 2025-03-02 13:09:41.930 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 13:09:41.930 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 13:09:41.930 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 13:09:42.831 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 13:09:42.831 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 13:09:42.831 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 13:09:42.833 - [任务 30][Sybase] - Apply table structure to target database 
[INFO ] 2025-03-02 13:09:43.162 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 13:09:43.162 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 13:09:43.162 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 13:09:43.162 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 13:09:43.978 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:09:44.219 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_879b42c1_636e_4508_93a4_0a1c7202db1c 
[INFO ] 2025-03-02 13:09:44.222 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 13:09:44.222 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 13:09:44.282 - [任务 30][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-03-02 13:09:44.282 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 13:09:44.345 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 13:09:44.345 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 13:09:44.345 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[INFO ] 2025-03-02 13:09:44.651 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 13:09:44.652 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 13:09:44.652 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 13:09:44.653 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 13:09:44.653 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 13:09:44.667 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 13:09:44.667 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 13:09:44.668 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:09:44.874 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_879b42c1_636e_4508_93a4_0a1c7202db1c 
[TRACE] 2025-03-02 13:09:46.237 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 2 ms 
[INFO ] 2025-03-02 13:09:46.242 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-03-02 13:09:47.192 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 13:17:00.736 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 13:17:00.869 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 13:17:00.882 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740892182577 
[TRACE] 2025-03-02 13:17:00.882 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740892182577 
[TRACE] 2025-03-02 13:17:00.882 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 13:17:00.883 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 13:17:00.888 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 190 ms 
[TRACE] 2025-03-02 13:17:00.888 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] running status set to false 
[TRACE] 2025-03-02 13:17:01.154 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_d6c7fd12-2fe3-4219-8b79-8a7e136cf084_1740892182759 
[TRACE] 2025-03-02 13:17:01.155 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_d6c7fd12-2fe3-4219-8b79-8a7e136cf084_1740892182759 
[TRACE] 2025-03-02 13:17:01.155 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] schema data cleaned 
[TRACE] 2025-03-02 13:17:01.155 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] monitor closed 
[TRACE] 2025-03-02 13:17:01.157 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] close complete, cost 270 ms 
[TRACE] 2025-03-02 13:17:03.350 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 13:17:03.351 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@433f86be 
[TRACE] 2025-03-02 13:17:03.351 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 13:17:03.479 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 13:17:03.480 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 13:17:03.481 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 13:17:03.510 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:17:03.510 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:19:14.852 - [任务 30] - Task initialization... 
[TRACE] 2025-03-02 13:19:14.852 - [任务 30] - Start task milestones: 67c3de7dc71ccf7ef2097412(任务 30) 
[INFO ] 2025-03-02 13:19:15.031 - [任务 30] - Loading table structure completed 
[TRACE] 2025-03-02 13:19:15.031 - [任务 30] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 13:19:15.082 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 13:19:15.083 - [任务 30] - Task started 
[TRACE] 2025-03-02 13:19:15.116 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] start preload schema,table counts: 2 
[TRACE] 2025-03-02 13:19:15.123 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 13:19:15.123 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] start preload schema,table counts: 2 
[TRACE] 2025-03-02 13:19:15.123 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 13:19:15.123 - [任务 30][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 13:19:15.977 - [任务 30][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 13:19:15.978 - [任务 30][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 13:19:15.978 - [任务 30][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 13:19:15.984 - [任务 30][Sybase] - Apply table structure to target database 
[INFO ] 2025-03-02 13:19:16.585 - [任务 30][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 13:19:16.592 - [任务 30][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 13:19:16.592 - [任务 30][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 13:19:16.592 - [任务 30][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 13:19:17.332 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:19:17.631 - [任务 30][Pg] - new logical replication slot created, slotName:tapdata_cdc_e8ed8a88_71c9_4061_a82a_cc01a4b8d82d 
[INFO ] 2025-03-02 13:19:17.717 - [任务 30][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 13:19:17.718 - [任务 30][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 13:19:17.718 - [任务 30][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-03-02 13:19:17.733 - [任务 30][Pg] - Initial sync started 
[INFO ] 2025-03-02 13:19:17.733 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 13:19:17.734 - [任务 30][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[INFO ] 2025-03-02 13:19:17.865 - [任务 30][Pg] - Table testTimeStampWithIndex has been completed batch read 
[INFO ] 2025-03-02 13:19:17.865 - [任务 30][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 13:19:17.926 - [任务 30][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[TRACE] 2025-03-02 13:19:17.926 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[INFO ] 2025-03-02 13:19:18.220 - [任务 30][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[TRACE] 2025-03-02 13:19:18.220 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 13:19:18.221 - [任务 30][Pg] - Skip table [testTimeStampWithIndex] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-03-02 13:19:18.221 - [任务 30][Pg] - Initial sync completed 
[INFO ] 2025-03-02 13:19:18.221 - [任务 30][Pg] - Batch read completed. 
[TRACE] 2025-03-02 13:19:18.221 - [任务 30][Pg] - Query snapshot row size completed: Pg(623bae5f-d5dd-4155-bec7-f07ca4f61610) 
[TRACE] 2025-03-02 13:19:18.221 - [任务 30][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 13:19:18.221 - [任务 30][Pg] - Initial sync completed 
[TRACE] 2025-03-02 13:19:18.222 - [任务 30][Pg] - Starting stream read, table list: [testTimeStampWithIndex, testTimeStampWithNoIndex], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 13:19:18.222 - [任务 30][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 13:19:18.286 - [任务 30][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 13:19:18.287 - [任务 30][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_e8ed8a88_71c9_4061_a82a_cc01a4b8d82d 
[TRACE] 2025-03-02 13:19:19.909 - [任务 30][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithIndex, testTimeStampWithNoIndex], data change syncing 
[TRACE] 2025-03-02 13:19:24.331 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 13:19:24.334 - [任务 30][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 13:19:24.334 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-03-02 13:19:24.334 - [任务 30][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 13:19:24.334 - [任务 30][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 13:19:24.334 - [任务 30][Sybase] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-03-02 13:19:49.125 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] running status set to false 
[TRACE] 2025-03-02 13:19:49.300 - [任务 30][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 13:19:49.320 - [任务 30][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740892755720 
[TRACE] 2025-03-02 13:19:49.320 - [任务 30][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_623bae5f-d5dd-4155-bec7-f07ca4f61610_1740892755720 
[TRACE] 2025-03-02 13:19:49.320 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] schema data cleaned 
[TRACE] 2025-03-02 13:19:49.321 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] monitor closed 
[TRACE] 2025-03-02 13:19:49.323 - [任务 30][Pg] - Node Pg[623bae5f-d5dd-4155-bec7-f07ca4f61610] close complete, cost 198 ms 
[TRACE] 2025-03-02 13:19:49.323 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] running status set to false 
[TRACE] 2025-03-02 13:19:49.608 - [任务 30][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_d6c7fd12-2fe3-4219-8b79-8a7e136cf084_1740892755900 
[TRACE] 2025-03-02 13:19:49.609 - [任务 30][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_d6c7fd12-2fe3-4219-8b79-8a7e136cf084_1740892755900 
[TRACE] 2025-03-02 13:19:49.609 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] schema data cleaned 
[TRACE] 2025-03-02 13:19:49.611 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] monitor closed 
[TRACE] 2025-03-02 13:19:49.611 - [任务 30][Sybase] - Node Sybase[d6c7fd12-2fe3-4219-8b79-8a7e136cf084] close complete, cost 288 ms 
[TRACE] 2025-03-02 13:19:53.677 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 13:19:53.678 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@676c043c 
[TRACE] 2025-03-02 13:19:53.857 - [任务 30] - Stop task milestones: 67c3de7dc71ccf7ef2097412(任务 30)  
[TRACE] 2025-03-02 13:19:53.858 - [任务 30] - Stopped task aspect(s) 
[TRACE] 2025-03-02 13:19:53.863 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 13:19:53.863 - [任务 30] - Task stopped. 
[TRACE] 2025-03-02 13:19:53.900 - [任务 30] - Remove memory task client succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
[TRACE] 2025-03-02 13:19:53.900 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[67c3de7dc71ccf7ef2097412] 
