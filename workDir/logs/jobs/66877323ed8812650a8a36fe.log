[INFO ] 2024-07-05 12:14:27.927 - [来自TAPDATA2Oracle的共享挖掘任务] - Start task milestones: 66877323ed8812650a8a36fe(来自TAPDATA2Oracle的共享挖掘任务) 
[INFO ] 2024-07-05 12:14:28.054 - [来自TAPDATA2Oracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 12:14:28.096 - [来自TAPDATA2Oracle的共享挖掘任务] - The engine receives 来自TAPDATA2Oracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 12:14:28.125 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] start preload schema,table counts: 2 
[INFO ] 2024-07-05 12:14:28.125 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 12:14:28.158 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 12:14:28.160 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 12:14:28.170 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c769, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1700608932, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:14:28.183 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:14:28.215 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 12:14:29.409 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" read batch size: 2000 
[INFO ] 2024-07-05 12:14:29.410 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" event queue capacity: 4000 
[INFO ] 2024-07-05 12:14:29.410 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 12:14:29.718 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68921056,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:14:29.718 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 12:14:29.920 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Starting stream read, table list: [C##TAPDATA2.POLICY, C##TAPDATA2._tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":68921056,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:14:29.968 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - total start mining scn: 68921056 
[INFO ] 2024-07-05 12:14:31.393 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 14:29:51.294 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] running status set to false 
[INFO ] 2024-07-05 14:29:51.386 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 14:29:51.462 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 14:29:51.515 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 69009131 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 69009131 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 
[WARN ] 2024-07-05 14:29:51.694 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkShareCDCNode-06032439a5734eb5b632d1cd9df2dc96 
[INFO ] 2024-07-05 14:29:51.695 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - PDK connector node released: HazelcastSourcePdkShareCDCNode-06032439a5734eb5b632d1cd9df2dc96 
[INFO ] 2024-07-05 14:29:51.718 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] schema data cleaned 
[INFO ] 2024-07-05 17:28:14.139 - [来自TAPDATA2Oracle的共享挖掘任务] - Start task milestones: 66877323ed8812650a8a36fe(来自TAPDATA2Oracle的共享挖掘任务) 
[INFO ] 2024-07-05 17:28:15.725 - [来自TAPDATA2Oracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 17:28:16.219 - [来自TAPDATA2Oracle的共享挖掘任务] - The engine receives 来自TAPDATA2Oracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 17:28:17.337 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 17:28:17.337 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] start preload schema,table counts: 2 
[INFO ] 2024-07-05 17:28:17.338 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 17:28:17.346 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 17:28:17.913 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 17:28:17.913 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c769, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1700608932, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 17:28:18.464 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 8327 
[INFO ] 2024-07-05 17:28:18.586 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 17:28:18.590 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 17:28:39.924 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" read batch size: 2000 
[INFO ] 2024-07-05 17:28:39.924 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" event queue capacity: 4000 
[INFO ] 2024-07-05 17:28:39.929 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-05 17:28:40.095 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":69009052,"pendingScn":69009056,"timestamp":1720160981000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 17:28:40.096 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 17:28:40.291 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Starting stream read, table list: [C##TAPDATA2.POLICY, C##TAPDATA2._tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":69009052,"pendingScn":69009056,"timestamp":1720160981000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 17:28:40.974 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - total start mining scn: 69009052 
[INFO ] 2024-07-05 17:28:42.186 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 18:28:18.525 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 18:28:18.534 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 11898 
[INFO ] 2024-07-05 18:47:03.255 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-05 18:47:03.256 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - java.lang.RuntimeException: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据 <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据

<-- Simple Stack Trace -->
Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:227)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:111)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	at io.tapdata.connector.oracle.OracleConnector.streamReadMultiConnection(OracleConnector.java:424)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$25(HazelcastSourcePdkDataNode.java:701)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CPreparedStatement.doOall8(T4CPreparedStatement.java:152)
	at oracle.jdbc.driver.T4CPreparedStatement.executeForDescribe(T4CPreparedStatement.java:937)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OraclePreparedStatement.executeInternal(OraclePreparedStatement.java:3746)
	at oracle.jdbc.driver.OraclePreparedStatement.executeQuery(OraclePreparedStatement.java:3855)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1098)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:207)
	... 21 more

[INFO ] 2024-07-05 18:47:03.458 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Job suspend in error handle 
[INFO ] 2024-07-05 18:47:03.636 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] running status set to false 
[INFO ] 2024-07-05 18:47:04.656 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:09.682 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:14.727 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:21.824 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 18:47:22.031 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 18:47:25.714 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:30.805 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:35.693 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:40.785 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:45.650 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:50.655 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:51.891 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-06032439a5734eb5b632d1cd9df2dc96 
[INFO ] 2024-07-05 18:47:51.892 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - PDK connector node released: HazelcastSourcePdkShareCDCNode-06032439a5734eb5b632d1cd9df2dc96 
[INFO ] 2024-07-05 18:47:51.894 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] schema data cleaned 
[INFO ] 2024-07-05 18:47:51.894 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] monitor closed 
[INFO ] 2024-07-05 18:47:51.897 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[06032439a5734eb5b632d1cd9df2dc96] close complete, cost 48261 ms 
[INFO ] 2024-07-05 18:47:51.898 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[b8612e245fe44cbd8722c9e9f5163a4e] running status set to false 
[INFO ] 2024-07-05 18:47:51.926 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-05 18:47:51.927 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-05 18:47:51.927 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[b8612e245fe44cbd8722c9e9f5163a4e] schema data cleaned 
[INFO ] 2024-07-05 18:47:51.929 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[b8612e245fe44cbd8722c9e9f5163a4e] monitor closed 
[INFO ] 2024-07-05 18:47:51.929 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[b8612e245fe44cbd8722c9e9f5163a4e] close complete, cost 31 ms 
[INFO ] 2024-07-05 18:47:55.672 - [来自TAPDATA2Oracle的共享挖掘任务] - Task [来自TAPDATA2Oracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:47:55.682 - [来自TAPDATA2Oracle的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 18:47:55.687 - [来自TAPDATA2Oracle的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5bd83f6d 
[INFO ] 2024-07-05 18:47:55.690 - [来自TAPDATA2Oracle的共享挖掘任务] - Stop task milestones: 66877323ed8812650a8a36fe(来自TAPDATA2Oracle的共享挖掘任务)  
[INFO ] 2024-07-05 18:47:55.812 - [来自TAPDATA2Oracle的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-05 18:47:55.828 - [来自TAPDATA2Oracle的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 18:47:55.830 - [来自TAPDATA2Oracle的共享挖掘任务] - Remove memory task client succeed, task: 来自TAPDATA2Oracle的共享挖掘任务[66877323ed8812650a8a36fe] 
[INFO ] 2024-07-05 18:47:55.830 - [来自TAPDATA2Oracle的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自TAPDATA2Oracle的共享挖掘任务[66877323ed8812650a8a36fe] 
[INFO ] 2024-07-05 19:28:18.882 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 19:28:18.920 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 12973 
