[INFO ] 2024-07-12 11:14:36.003 - [Heartbeat-SourceMysqlTestHeartBeat] - Start task milestones: 66909f9b7c91bf6e98248c7e(Heartbeat-SourceMysqlTestHeartBeat) 
[INFO ] 2024-07-12 11:14:36.048 - [Heartbeat-SourceMysqlTestHeartBeat] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-12 11:14:36.048 - [Heartbeat-SourceMysqlTestHeartBeat] - The engine receives Heartbeat-SourceMysqlTestHeartBeat task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 11:14:41.542 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ded8d82-9f74-43f8-b352-fe0fba518aff] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:14:44.571 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[74c53a5a-1924-45c4-b387-2e8cc9ec2b4e] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:14:44.622 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[74c53a5a-1924-45c4-b387-2e8cc9ec2b4e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:14:44.623 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ded8d82-9f74-43f8-b352-fe0fba518aff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:14:45.334 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-12 11:14:45.334 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-12 11:14:45.334 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 11:14:45.335 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1720754085334,"lastTimes":1720754085334,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-12 11:14:45.413 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-12 11:14:45.413 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-12 11:14:45.422 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-12 11:14:45.422 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 11:14:45.429 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-12 11:14:45.430 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1720754085334,"lastTimes":1720754085334,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-12 11:14:45.430 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-12 11:14:45.430 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 11:14:45.479 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-12 11:14:45.479 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Table "test._tapdata_heartbeat_table" exists, skip auto create table 
[INFO ] 2024-07-12 11:23:19.508 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ded8d82-9f74-43f8-b352-fe0fba518aff] running status set to false 
[INFO ] 2024-07-12 11:23:19.509 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-12 11:23:19.512 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-1ded8d82-9f74-43f8-b352-fe0fba518aff 
[INFO ] 2024-07-12 11:23:19.513 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-1ded8d82-9f74-43f8-b352-fe0fba518aff 
[INFO ] 2024-07-12 11:23:19.513 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ded8d82-9f74-43f8-b352-fe0fba518aff] schema data cleaned 
[INFO ] 2024-07-12 11:23:19.515 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ded8d82-9f74-43f8-b352-fe0fba518aff] monitor closed 
[INFO ] 2024-07-12 11:23:19.515 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ded8d82-9f74-43f8-b352-fe0fba518aff] close complete, cost 36 ms 
[INFO ] 2024-07-12 11:23:19.516 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[74c53a5a-1924-45c4-b387-2e8cc9ec2b4e] running status set to false 
[INFO ] 2024-07-12 11:23:19.532 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-74c53a5a-1924-45c4-b387-2e8cc9ec2b4e 
[INFO ] 2024-07-12 11:23:19.533 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-74c53a5a-1924-45c4-b387-2e8cc9ec2b4e 
[INFO ] 2024-07-12 11:23:19.533 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[74c53a5a-1924-45c4-b387-2e8cc9ec2b4e] schema data cleaned 
[INFO ] 2024-07-12 11:23:19.534 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[74c53a5a-1924-45c4-b387-2e8cc9ec2b4e] monitor closed 
[INFO ] 2024-07-12 11:23:19.534 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[74c53a5a-1924-45c4-b387-2e8cc9ec2b4e] close complete, cost 18 ms 
[INFO ] 2024-07-12 11:23:21.929 - [Heartbeat-SourceMysqlTestHeartBeat] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 11:23:21.930 - [Heartbeat-SourceMysqlTestHeartBeat] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37a0d05d 
[INFO ] 2024-07-12 11:23:22.055 - [Heartbeat-SourceMysqlTestHeartBeat] - Stop task milestones: 66909f9b7c91bf6e98248c7e(Heartbeat-SourceMysqlTestHeartBeat)  
[INFO ] 2024-07-12 11:23:22.055 - [Heartbeat-SourceMysqlTestHeartBeat] - Stopped task aspect(s) 
[INFO ] 2024-07-12 11:23:22.055 - [Heartbeat-SourceMysqlTestHeartBeat] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 11:23:22.082 - [Heartbeat-SourceMysqlTestHeartBeat] - Remove memory task client succeed, task: Heartbeat-SourceMysqlTestHeartBeat[66909f9b7c91bf6e98248c7e] 
[INFO ] 2024-07-12 11:23:22.084 - [Heartbeat-SourceMysqlTestHeartBeat] - Destroy memory task client cache succeed, task: Heartbeat-SourceMysqlTestHeartBeat[66909f9b7c91bf6e98248c7e] 
