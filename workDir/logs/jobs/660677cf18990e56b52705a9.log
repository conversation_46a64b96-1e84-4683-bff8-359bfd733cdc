[INFO ] 2024-03-29 16:15:37.634 - [suppliers_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 16:15:37.634 - [suppliers_import_import_import_import_import_import_import] - Start task milestones: 660677cf18990e56b52705a9(suppliers_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 16:15:37.819 - [suppliers_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 16:15:37.820 - [suppliers_import_import_import_import_import_import_import] - The engine receives suppliers_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 16:15:38.062 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.078 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] start preload schema,table counts: 3 
[INFO ] 2024-03-29 16:15:38.083 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.083 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.083 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.083 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.083 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.083 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.084 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:15:38.262 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] preload schema finished, cost 185 ms 
[INFO ] 2024-03-29 16:15:38.263 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] preload schema finished, cost 187 ms 
[INFO ] 2024-03-29 16:15:38.263 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] preload schema finished, cost 186 ms 
[INFO ] 2024-03-29 16:15:38.264 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] preload schema finished, cost 186 ms 
[INFO ] 2024-03-29 16:15:38.264 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] preload schema finished, cost 184 ms 
[INFO ] 2024-03-29 16:15:38.264 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] preload schema finished, cost 186 ms 
[INFO ] 2024-03-29 16:15:38.264 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] preload schema finished, cost 184 ms 
[INFO ] 2024-03-29 16:15:38.264 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] preload schema finished, cost 185 ms 
[INFO ] 2024-03-29 16:15:38.275 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] preload schema finished, cost 218 ms 
[INFO ] 2024-03-29 16:15:38.279 - [suppliers_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 16:15:38.279 - [suppliers_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Suppliers(9486c6f4-e3af-4f29-9da4-560f3334862b)
    ->Products(ce1a3c42-a480-4840-91a2-f2d87eba45fe)
} 
[INFO ] 2024-03-29 16:15:38.480 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 16:15:38.747 - [suppliers_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:15:38.792 - [suppliers_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 16:15:38.792 - [suppliers_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 16:15:39.138 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Source node "Suppliers" read batch size: 500 
[INFO ] 2024-03-29 16:15:39.139 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Source node "Suppliers" event queue capacity: 1000 
[INFO ] 2024-03-29 16:15:39.139 - [suppliers_import_import_import_import_import_import_import][Suppliers] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:15:39.285 - [suppliers_import_import_import_import_import_import_import][Suppliers] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371031,"gtidSet":""} 
[INFO ] 2024-03-29 16:15:39.285 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Initial sync started 
[INFO ] 2024-03-29 16:15:39.293 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Starting batch read, table name: Suppliers, offset: null 
[INFO ] 2024-03-29 16:15:39.293 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Table Suppliers is going to be initial synced 
[INFO ] 2024-03-29 16:15:39.360 - [suppliers_import_import_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 16:15:39.360 - [suppliers_import_import_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 16:15:39.360 - [suppliers_import_import_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:15:39.391 - [suppliers_import_import_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371031,"gtidSet":""} 
[INFO ] 2024-03-29 16:15:39.392 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Query table 'Suppliers' counts: 1 
[INFO ] 2024-03-29 16:15:39.571 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Initial sync completed 
[INFO ] 2024-03-29 16:15:39.579 - [suppliers_import_import_import_import_import_import_import] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 16:15:39.686 - [suppliers_import_import_import_import_import_import_import][suppliers] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-03-29 16:15:40.825 - [suppliers_import_import_import_import_import_import_import] - Node[Suppliers] finish, notify next layer to run 
[INFO ] 2024-03-29 16:15:40.830 - [suppliers_import_import_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 16:15:40.830 - [suppliers_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 16:15:40.830 - [suppliers_import_import_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 16:15:40.830 - [suppliers_import_import_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 16:15:40.865 - [suppliers_import_import_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 16:15:41.072 - [suppliers_import_import_import_import_import_import_import][Products] - Initial sync completed 
[ERROR] 2024-03-29 16:15:41.401 - [suppliers_import_import_import_import_import_import_import][merge] - - Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai} <-- Error Message -->
- Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 16:15:41.404 - [suppliers_import_import_import_import_import_import_import][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 16:15:41.831 - [suppliers_import_import_import_import_import_import_import] - Task [suppliers_import_import_import_import_import_import_import] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 16:15:41.936 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] running status set to false 
[INFO ] 2024-03-29 16:15:41.936 - [suppliers_import_import_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 16:15:41.936 - [suppliers_import_import_import_import_import_import_import][Products] - Incremental sync completed 
[INFO ] 2024-03-29 16:15:41.969 - [suppliers_import_import_import_import_import_import_import][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-67987063-6271-4b47-bdd6-2db4d6273759 
[INFO ] 2024-03-29 16:15:41.970 - [suppliers_import_import_import_import_import_import_import][Products] - PDK connector node released: HazelcastSourcePdkDataNode-67987063-6271-4b47-bdd6-2db4d6273759 
[INFO ] 2024-03-29 16:15:41.970 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] schema data cleaned 
[INFO ] 2024-03-29 16:15:41.979 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] monitor closed 
[INFO ] 2024-03-29 16:15:41.979 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] close complete, cost 58 ms 
[INFO ] 2024-03-29 16:15:42.082 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] running status set to false 
[INFO ] 2024-03-29 16:15:42.082 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.082 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] monitor closed 
[INFO ] 2024-03-29 16:15:42.090 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] close complete, cost 110 ms 
[INFO ] 2024-03-29 16:15:42.159 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] running status set to false 
[INFO ] 2024-03-29 16:15:42.160 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.160 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] monitor closed 
[INFO ] 2024-03-29 16:15:42.161 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] close complete, cost 70 ms 
[INFO ] 2024-03-29 16:15:42.165 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] running status set to false 
[INFO ] 2024-03-29 16:15:42.165 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Incremental sync starting... 
[INFO ] 2024-03-29 16:15:42.175 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Incremental sync completed 
[INFO ] 2024-03-29 16:15:42.176 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node stopped: HazelcastSourcePdkDataNode-eaf7610a-47ad-4e52-b383-b121cc482229 
[INFO ] 2024-03-29 16:15:42.176 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node released: HazelcastSourcePdkDataNode-eaf7610a-47ad-4e52-b383-b121cc482229 
[INFO ] 2024-03-29 16:15:42.176 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.176 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] monitor closed 
[INFO ] 2024-03-29 16:15:42.177 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] close complete, cost 15 ms 
[INFO ] 2024-03-29 16:15:42.177 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] running status set to false 
[INFO ] 2024-03-29 16:15:42.194 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node stopped: ScriptExecutor-TargetMysql-840f40b1-e56e-4b02-a4b2-ecd01a8469f4 
[INFO ] 2024-03-29 16:15:42.195 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node released: ScriptExecutor-TargetMysql-840f40b1-e56e-4b02-a4b2-ecd01a8469f4 
[INFO ] 2024-03-29 16:15:42.199 - [suppliers_import_import_import_import_import_import_import][Suppliers] - [ScriptExecutorsManager-660677cf18990e56b52705a9-ec69139d-e277-4983-823d-6158efddd843-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.199 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node stopped: ScriptExecutor-TestMongo-91505fc6-76a2-48b6-8e6f-4a09e1a12ade 
[INFO ] 2024-03-29 16:15:42.199 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node released: ScriptExecutor-TestMongo-91505fc6-76a2-48b6-8e6f-4a09e1a12ade 
[INFO ] 2024-03-29 16:15:42.200 - [suppliers_import_import_import_import_import_import_import][Suppliers] - [ScriptExecutorsManager-660677cf18990e56b52705a9-ec69139d-e277-4983-823d-6158efddd843-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.200 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.201 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] monitor closed 
[INFO ] 2024-03-29 16:15:42.201 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] close complete, cost 23 ms 
[INFO ] 2024-03-29 16:15:42.201 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] running status set to false 
[INFO ] 2024-03-29 16:15:42.257 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.258 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] monitor closed 
[INFO ] 2024-03-29 16:15:42.260 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] close complete, cost 57 ms 
[INFO ] 2024-03-29 16:15:42.260 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] running status set to false 
[INFO ] 2024-03-29 16:15:42.307 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.308 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] monitor closed 
[INFO ] 2024-03-29 16:15:42.308 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] close complete, cost 47 ms 
[INFO ] 2024-03-29 16:15:42.308 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] running status set to false 
[INFO ] 2024-03-29 16:15:42.329 - [suppliers_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG 
[INFO ] 2024-03-29 16:15:42.329 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.329 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] monitor closed 
[INFO ] 2024-03-29 16:15:42.330 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] close complete, cost 22 ms 
[INFO ] 2024-03-29 16:15:42.409 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] running status set to false 
[INFO ] 2024-03-29 16:15:42.410 - [suppliers_import_import_import_import_import_import_import][suppliers] - PDK connector node stopped: HazelcastTargetPdkDataNode-9b01fe65-3da8-4e76-8e23-9b7e2ef8d462 
[INFO ] 2024-03-29 16:15:42.410 - [suppliers_import_import_import_import_import_import_import][suppliers] - PDK connector node released: HazelcastTargetPdkDataNode-9b01fe65-3da8-4e76-8e23-9b7e2ef8d462 
[INFO ] 2024-03-29 16:15:42.410 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] schema data cleaned 
[INFO ] 2024-03-29 16:15:42.410 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] monitor closed 
[INFO ] 2024-03-29 16:15:42.411 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] close complete, cost 80 ms 
[INFO ] 2024-03-29 16:15:46.900 - [suppliers_import_import_import_import_import_import_import] - Task [suppliers_import_import_import_import_import_import_import] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 16:15:46.917 - [suppliers_import_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 16:15:46.919 - [suppliers_import_import_import_import_import_import_import] - Stop task milestones: 660677cf18990e56b52705a9(suppliers_import_import_import_import_import_import_import)  
[INFO ] 2024-03-29 16:15:46.929 - [suppliers_import_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 16:15:46.929 - [suppliers_import_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 16:15:46.960 - [suppliers_import_import_import_import_import_import_import] - Remove memory task client succeed, task: suppliers_import_import_import_import_import_import_import[660677cf18990e56b52705a9] 
[INFO ] 2024-03-29 16:15:46.961 - [suppliers_import_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: suppliers_import_import_import_import_import_import_import[660677cf18990e56b52705a9] 
[INFO ] 2024-03-29 16:17:00.562 - [suppliers_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 16:17:00.562 - [suppliers_import_import_import_import_import_import_import] - Start task milestones: 660677cf18990e56b52705a9(suppliers_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 16:17:00.732 - [suppliers_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 16:17:00.732 - [suppliers_import_import_import_import_import_import_import] - The engine receives suppliers_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 16:17:00.812 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.812 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.812 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.813 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] start preload schema,table counts: 3 
[INFO ] 2024-03-29 16:17:00.813 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.813 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.813 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.813 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.813 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:17:00.859 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] preload schema finished, cost 45 ms 
[INFO ] 2024-03-29 16:17:00.860 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] preload schema finished, cost 46 ms 
[INFO ] 2024-03-29 16:17:00.860 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] preload schema finished, cost 46 ms 
[INFO ] 2024-03-29 16:17:00.865 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] preload schema finished, cost 48 ms 
[INFO ] 2024-03-29 16:17:00.865 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] preload schema finished, cost 53 ms 
[INFO ] 2024-03-29 16:17:00.865 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] preload schema finished, cost 53 ms 
[INFO ] 2024-03-29 16:17:00.865 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] preload schema finished, cost 52 ms 
[INFO ] 2024-03-29 16:17:00.897 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] preload schema finished, cost 54 ms 
[INFO ] 2024-03-29 16:17:00.898 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 16:17:00.918 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] preload schema finished, cost 105 ms 
[INFO ] 2024-03-29 16:17:00.919 - [suppliers_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 16:17:00.919 - [suppliers_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Suppliers(9486c6f4-e3af-4f29-9da4-560f3334862b)
    ->Products(ce1a3c42-a480-4840-91a2-f2d87eba45fe)
} 
[INFO ] 2024-03-29 16:17:01.117 - [suppliers_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:17:01.118 - [suppliers_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 16:17:01.118 - [suppliers_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 16:17:01.394 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Source node "Suppliers" read batch size: 500 
[INFO ] 2024-03-29 16:17:01.405 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Source node "Suppliers" event queue capacity: 1000 
[INFO ] 2024-03-29 16:17:01.408 - [suppliers_import_import_import_import_import_import_import][Suppliers] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:17:01.408 - [suppliers_import_import_import_import_import_import_import][Suppliers] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371031,"gtidSet":""} 
[INFO ] 2024-03-29 16:17:01.529 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Initial sync started 
[INFO ] 2024-03-29 16:17:01.537 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Starting batch read, table name: Suppliers, offset: null 
[INFO ] 2024-03-29 16:17:01.564 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Table Suppliers is going to be initial synced 
[INFO ] 2024-03-29 16:17:01.582 - [suppliers_import_import_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 16:17:01.583 - [suppliers_import_import_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 16:17:01.583 - [suppliers_import_import_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:17:01.594 - [suppliers_import_import_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371031,"gtidSet":""} 
[INFO ] 2024-03-29 16:17:01.595 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Query table 'Suppliers' counts: 1 
[INFO ] 2024-03-29 16:17:01.595 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Initial sync completed 
[INFO ] 2024-03-29 16:17:01.753 - [suppliers_import_import_import_import_import_import_import] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 16:17:01.754 - [suppliers_import_import_import_import_import_import_import][suppliers] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 16:17:02.275 - [suppliers_import_import_import_import_import_import_import] - Node[Suppliers] finish, notify next layer to run 
[INFO ] 2024-03-29 16:17:02.276 - [suppliers_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 16:17:02.276 - [suppliers_import_import_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 16:17:02.276 - [suppliers_import_import_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 16:17:02.343 - [suppliers_import_import_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 16:17:02.347 - [suppliers_import_import_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 16:17:02.555 - [suppliers_import_import_import_import_import_import_import][Products] - Initial sync completed 
[ERROR] 2024-03-29 16:17:02.973 - [suppliers_import_import_import_import_import_import_import][merge] - - Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai} <-- Error Message -->
- Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG
- Pk or unique field: [productId]
- Data: {supplierId=1, productName=Chai}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 16:17:02.974 - [suppliers_import_import_import_import_import_import_import][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 16:17:03.361 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] running status set to false 
[INFO ] 2024-03-29 16:17:03.361 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Incremental sync starting... 
[INFO ] 2024-03-29 16:17:03.361 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Incremental sync completed 
[INFO ] 2024-03-29 16:17:03.392 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node stopped: HazelcastSourcePdkDataNode-eaf7610a-47ad-4e52-b383-b121cc482229 
[INFO ] 2024-03-29 16:17:03.393 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node released: HazelcastSourcePdkDataNode-eaf7610a-47ad-4e52-b383-b121cc482229 
[INFO ] 2024-03-29 16:17:03.393 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.395 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] monitor closed 
[INFO ] 2024-03-29 16:17:03.396 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[eaf7610a-47ad-4e52-b383-b121cc482229] close complete, cost 38 ms 
[INFO ] 2024-03-29 16:17:03.396 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] running status set to false 
[INFO ] 2024-03-29 16:17:03.403 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node stopped: ScriptExecutor-TargetMysql-5d2b90c2-997d-4ecc-9d5a-81676c10f7e3 
[INFO ] 2024-03-29 16:17:03.403 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node released: ScriptExecutor-TargetMysql-5d2b90c2-997d-4ecc-9d5a-81676c10f7e3 
[INFO ] 2024-03-29 16:17:03.403 - [suppliers_import_import_import_import_import_import_import][Suppliers] - [ScriptExecutorsManager-660677cf18990e56b52705a9-ec69139d-e277-4983-823d-6158efddd843-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.406 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node stopped: ScriptExecutor-TestMongo-bfecb54a-a87b-49ab-961b-922394425883 
[INFO ] 2024-03-29 16:17:03.406 - [suppliers_import_import_import_import_import_import_import][Suppliers] - PDK connector node released: ScriptExecutor-TestMongo-bfecb54a-a87b-49ab-961b-922394425883 
[INFO ] 2024-03-29 16:17:03.406 - [suppliers_import_import_import_import_import_import_import][Suppliers] - [ScriptExecutorsManager-660677cf18990e56b52705a9-ec69139d-e277-4983-823d-6158efddd843-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.406 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.407 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] monitor closed 
[INFO ] 2024-03-29 16:17:03.407 - [suppliers_import_import_import_import_import_import_import][Suppliers] - Node Suppliers[ec69139d-e277-4983-823d-6158efddd843] close complete, cost 11 ms 
[INFO ] 2024-03-29 16:17:03.409 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] running status set to false 
[INFO ] 2024-03-29 16:17:03.409 - [suppliers_import_import_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 16:17:03.409 - [suppliers_import_import_import_import_import_import_import][Products] - Incremental sync completed 
[INFO ] 2024-03-29 16:17:03.413 - [suppliers_import_import_import_import_import_import_import][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-67987063-6271-4b47-bdd6-2db4d6273759 
[INFO ] 2024-03-29 16:17:03.413 - [suppliers_import_import_import_import_import_import_import][Products] - PDK connector node released: HazelcastSourcePdkDataNode-67987063-6271-4b47-bdd6-2db4d6273759 
[INFO ] 2024-03-29 16:17:03.413 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.413 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] monitor closed 
[INFO ] 2024-03-29 16:17:03.414 - [suppliers_import_import_import_import_import_import_import][Products] - Node Products[67987063-6271-4b47-bdd6-2db4d6273759] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:17:03.414 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] running status set to false 
[INFO ] 2024-03-29 16:17:03.504 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.504 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] monitor closed 
[INFO ] 2024-03-29 16:17:03.506 - [suppliers_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[9c8c2199-a580-4c0e-af14-7a9e2d0f3351] close complete, cost 91 ms 
[INFO ] 2024-03-29 16:17:03.506 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] running status set to false 
[INFO ] 2024-03-29 16:17:03.559 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.559 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] monitor closed 
[INFO ] 2024-03-29 16:17:03.561 - [suppliers_import_import_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[7db4e348-0330-473c-ab66-6d86b406a4d3] close complete, cost 55 ms 
[INFO ] 2024-03-29 16:17:03.602 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] running status set to false 
[INFO ] 2024-03-29 16:17:03.603 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.603 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] monitor closed 
[INFO ] 2024-03-29 16:17:03.603 - [suppliers_import_import_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[9486c6f4-e3af-4f29-9da4-560f3334862b] close complete, cost 42 ms 
[INFO ] 2024-03-29 16:17:03.603 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] running status set to false 
[INFO ] 2024-03-29 16:17:03.650 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.652 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] monitor closed 
[INFO ] 2024-03-29 16:17:03.653 - [suppliers_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[ce1a3c42-a480-4840-91a2-f2d87eba45fe] close complete, cost 48 ms 
[INFO ] 2024-03-29 16:17:03.653 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] running status set to false 
[INFO ] 2024-03-29 16:17:03.675 - [suppliers_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_ce1a3c42-a480-4840-91a2-f2d87eba45fe__TPORIG 
[INFO ] 2024-03-29 16:17:03.675 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.675 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] monitor closed 
[INFO ] 2024-03-29 16:17:03.676 - [suppliers_import_import_import_import_import_import_import][merge] - Node merge[025b234d-ad48-488b-beb6-f9470d936857] close complete, cost 22 ms 
[INFO ] 2024-03-29 16:17:03.722 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] running status set to false 
[INFO ] 2024-03-29 16:17:03.722 - [suppliers_import_import_import_import_import_import_import][suppliers] - PDK connector node stopped: HazelcastTargetPdkDataNode-9b01fe65-3da8-4e76-8e23-9b7e2ef8d462 
[INFO ] 2024-03-29 16:17:03.722 - [suppliers_import_import_import_import_import_import_import][suppliers] - PDK connector node released: HazelcastTargetPdkDataNode-9b01fe65-3da8-4e76-8e23-9b7e2ef8d462 
[INFO ] 2024-03-29 16:17:03.722 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] schema data cleaned 
[INFO ] 2024-03-29 16:17:03.724 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] monitor closed 
[INFO ] 2024-03-29 16:17:03.725 - [suppliers_import_import_import_import_import_import_import][suppliers] - Node suppliers[9b01fe65-3da8-4e76-8e23-9b7e2ef8d462] close complete, cost 47 ms 
[INFO ] 2024-03-29 16:17:07.058 - [suppliers_import_import_import_import_import_import_import] - Task [suppliers_import_import_import_import_import_import_import] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 16:17:07.083 - [suppliers_import_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 16:17:07.083 - [suppliers_import_import_import_import_import_import_import] - Stop task milestones: 660677cf18990e56b52705a9(suppliers_import_import_import_import_import_import_import)  
[INFO ] 2024-03-29 16:17:07.101 - [suppliers_import_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 16:17:07.101 - [suppliers_import_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 16:17:07.121 - [suppliers_import_import_import_import_import_import_import] - Remove memory task client succeed, task: suppliers_import_import_import_import_import_import_import[660677cf18990e56b52705a9] 
[INFO ] 2024-03-29 16:17:07.125 - [suppliers_import_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: suppliers_import_import_import_import_import_import_import[660677cf18990e56b52705a9] 
