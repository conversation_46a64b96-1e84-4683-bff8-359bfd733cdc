[INFO ] 2024-07-28 12:32:17.614 - [Heartbeat-SourceMongo] - Start task milestones: 66a5c9d0025e317d958d88fb(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 12:32:18.678 - [Heartbeat-SourceMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-28 12:32:18.696 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 12:32:18.698 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@b48e29e 
[INFO ] 2024-07-28 12:32:18.699 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5c9d0025e317d958d88fb(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 12:32:18.829 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 12:32:18.830 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 12:32:18.832 - [Heartbeat-SourceMongo] - Node [id 05f99ae8-1500-4204-a4b2-dc5dbd8ac598, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 05f99ae8-1500-4204-a4b2-dc5dbd8ac598, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

