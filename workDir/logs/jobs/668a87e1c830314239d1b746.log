[INFO ] 2024-07-12 10:22:35.318 - [任务 49][库存] - Node 库存[78d54f27-b8fd-451f-97ad-51907d09b57d] running status set to false 
[INFO ] 2024-07-12 10:22:35.358 - [任务 49] - Task initialization... 
[INFO ] 2024-07-12 10:22:35.603 - [任务 49] - Start task milestones: 668a87e1c830314239d1b746(任务 49) 
[INFO ] 2024-07-12 10:22:37.577 - [任务 49] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 10:22:38.032 - [任务 49] - The engine receives 任务 49 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 10:22:38.842 - [任务 49][库存 - Copy] - Node 库存 - Copy[2e1bde6e-904c-4a89-8a6a-c62449063746] start preload schema,table counts: 1 
[INFO ] 2024-07-12 10:22:38.842 - [任务 49][库存] - Node 库存[78d54f27-b8fd-451f-97ad-51907d09b57d] start preload schema,table counts: 1 
[INFO ] 2024-07-12 10:22:38.843 - [任务 49][库存 - Copy] - Node 库存 - Copy[2e1bde6e-904c-4a89-8a6a-c62449063746] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 10:22:38.843 - [任务 49][库存] - Node 库存[78d54f27-b8fd-451f-97ad-51907d09b57d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 10:22:40.332 - [任务 49][库存 - Copy] - Node(库存 - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-12 10:22:40.333 - [任务 49][库存 - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 10:22:41.117 - [任务 49][库存] - Source node "库存" read batch size: 100 
[INFO ] 2024-07-12 10:22:41.119 - [任务 49][库存] - Source node "库存" event queue capacity: 200 
[INFO ] 2024-07-12 10:22:41.129 - [任务 49][库存] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-12 10:22:41.132 - [任务 49][库存] - batch offset found: {},stream offset found: {"name":"db67eea6-1cb1-447c-af1d-4d13522073bc","offset":{"{\"server\":\"db67eea6-1cb1-447c-af1d-4d13522073bc\"}":"{\"ts_sec\":1720683483,\"file\":\"binlog.000002\",\"pos\":2954403,\"server_id\":1}"}} 
[INFO ] 2024-07-12 10:22:41.242 - [任务 49][库存] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-12 10:22:41.243 - [任务 49][库存] - Incremental sync starting... 
[INFO ] 2024-07-12 10:22:41.245 - [任务 49][库存] - Initial sync completed 
[INFO ] 2024-07-12 10:22:41.246 - [任务 49][库存] - Starting stream read, table list: [发货明细表], offset: {"name":"db67eea6-1cb1-447c-af1d-4d13522073bc","offset":{"{\"server\":\"db67eea6-1cb1-447c-af1d-4d13522073bc\"}":"{\"ts_sec\":1720683483,\"file\":\"binlog.000002\",\"pos\":2954403,\"server_id\":1}"}} 
[INFO ] 2024-07-12 10:22:41.299 - [任务 49][库存] - Starting mysql cdc, server name: db67eea6-1cb1-447c-af1d-4d13522073bc 
[INFO ] 2024-07-12 10:22:41.505 - [任务 49][库存] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1440304902
  time.precision.mode: adaptive_time_microseconds
  database.server.name: db67eea6-1cb1-447c-af1d-4d13522073bc
  database.port: 3306
  threadName: Debezium-Mysql-Connector-db67eea6-1cb1-447c-af1d-4d13522073bc
  database.hostname: ***********
  database.password: ********
  name: db67eea6-1cb1-447c-af1d-4d13522073bc
  pdk.offset.string: {"name":"db67eea6-1cb1-447c-af1d-4d13522073bc","offset":{"{\"server\":\"db67eea6-1cb1-447c-af1d-4d13522073bc\"}":"{\"ts_sec\":1720683483,\"file\":\"binlog.000002\",\"pos\":2954403,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: 库存.发货明细表
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: 库存
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-12 10:22:41.659 - [任务 49][库存] - Connector Mysql incremental start succeed, tables: [发货明细表], data change syncing 
[INFO ] 2024-07-12 10:51:23.118 - [任务 49][库存] - Node 库存[78d54f27-b8fd-451f-97ad-51907d09b57d] running status set to false 
