[INFO ] 2024-07-29 00:35:58.513 - [任务 1] - Task initialization... 
[INFO ] 2024-07-29 00:35:58.674 - [任务 1] - Start task milestones: 66a6730089e7bf330708a829(任务 1) 
[INFO ] 2024-07-29 00:35:59.685 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 00:35:59.918 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 00:36:00.214 - [任务 1][PoTest] - Node PoTest[9da315fe-2abd-45e7-b3e8-ee20050395ba] start preload schema,table counts: 1 
[INFO ] 2024-07-29 00:36:00.216 - [任务 1][TESTPO] - Node TESTPO[6003f07c-c603-4d38-8337-26cc1b7a786a] start preload schema,table counts: 1 
[INFO ] 2024-07-29 00:36:00.216 - [任务 1][PoTest] - Node PoTest[9da315fe-2abd-45e7-b3e8-ee20050395ba] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 00:36:00.422 - [任务 1][TESTPO] - Node TESTPO[6003f07c-c603-4d38-8337-26cc1b7a786a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 00:36:01.231 - [任务 1][PoTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 00:36:01.324 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-29 00:36:01.324 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-29 00:36:01.325 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 00:36:01.471 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722184561,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-29 00:36:01.541 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-29 00:36:01.551 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-29 00:36:01.551 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-29 00:36:01.632 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 00:36:01.634 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-29 00:36:01.634 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-29 00:36:01.638 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-29 00:36:01.642 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-29 00:36:01.642 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722184561,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-29 00:36:01.849 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
