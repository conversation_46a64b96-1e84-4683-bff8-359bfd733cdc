[INFO ] 2024-11-14 07:34:50.435 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:50.435 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:50.435 - [任务 287(100)][32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] - Node 32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f[32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:34:50.435 - [任务 287(100)][32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] - Node 32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f[32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:50.437 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:50.437 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:34:50.642 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:34:51.176 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:34:51.176 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540890912 
[INFO ] 2024-11-14 07:34:51.176 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540890912 
[INFO ] 2024-11-14 07:34:51.176 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:34:51.176 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:34:51.176 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 39 ms 
[INFO ] 2024-11-14 07:34:51.381 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:34:51.906 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:34:51.918 - [任务 287(100)][32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] - Node 32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f[32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] running status set to false 
[INFO ] 2024-11-14 07:34:51.918 - [任务 287(100)][32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] - Node 32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f[32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] schema data cleaned 
[INFO ] 2024-11-14 07:34:51.919 - [任务 287(100)][32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] - Node 32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f[32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] monitor closed 
[INFO ] 2024-11-14 07:34:51.921 - [任务 287(100)][32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] - Node 32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f[32ce25d8-b5e6-4ed5-9aac-5c3d95fa348f] close complete, cost 8 ms 
[INFO ] 2024-11-14 07:34:51.929 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-d3c26069-2101-44c1-b818-81d728d6c3de 
[INFO ] 2024-11-14 07:34:51.930 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-d3c26069-2101-44c1-b818-81d728d6c3de 
[INFO ] 2024-11-14 07:34:51.932 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:34:51.932 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:34:51.932 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:34:51.932 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 31 ms 
[INFO ] 2024-11-14 07:34:51.935 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:34:51.935 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:34:51.937 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:34:56.636 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:56.638 - [任务 287(100)][bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] - Node bb55b2ef-233d-4ae0-9e0e-0541b6fbef75[bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:34:56.638 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:56.638 - [任务 287(100)][bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] - Node bb55b2ef-233d-4ae0-9e0e-0541b6fbef75[bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:56.638 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:56.638 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:56.843 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:34:56.909 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:34:56.909 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540896709 
[INFO ] 2024-11-14 07:34:56.909 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540896709 
[INFO ] 2024-11-14 07:34:56.909 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:34:56.909 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:34:56.910 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 22 ms 
[INFO ] 2024-11-14 07:34:57.122 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:34:57.397 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:34:57.408 - [任务 287(100)][bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] - Node bb55b2ef-233d-4ae0-9e0e-0541b6fbef75[bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] running status set to false 
[INFO ] 2024-11-14 07:34:57.413 - [任务 287(100)][bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] - Node bb55b2ef-233d-4ae0-9e0e-0541b6fbef75[bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] schema data cleaned 
[INFO ] 2024-11-14 07:34:57.413 - [任务 287(100)][bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] - Node bb55b2ef-233d-4ae0-9e0e-0541b6fbef75[bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] monitor closed 
[INFO ] 2024-11-14 07:34:57.420 - [任务 287(100)][bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] - Node bb55b2ef-233d-4ae0-9e0e-0541b6fbef75[bb55b2ef-233d-4ae0-9e0e-0541b6fbef75] close complete, cost 9 ms 
[INFO ] 2024-11-14 07:34:57.420 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-ebca1ef5-a4f3-467f-9de4-d59d1858bb02 
[INFO ] 2024-11-14 07:34:57.421 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-ebca1ef5-a4f3-467f-9de4-d59d1858bb02 
[INFO ] 2024-11-14 07:34:57.421 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:34:57.422 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:34:57.422 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:34:57.428 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 82 ms 
[INFO ] 2024-11-14 07:34:57.434 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:34:57.434 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:34:57.434 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:34:58.518 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:58.518 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:58.519 - [任务 287(100)][22bdad84-df1d-4134-8fc1-335573b8f11a] - Node 22bdad84-df1d-4134-8fc1-335573b8f11a[22bdad84-df1d-4134-8fc1-335573b8f11a] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:34:58.520 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.521 - [任务 287(100)][22bdad84-df1d-4134-8fc1-335573b8f11a] - Node 22bdad84-df1d-4134-8fc1-335573b8f11a[22bdad84-df1d-4134-8fc1-335573b8f11a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.521 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.521 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:34:58.854 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:34:58.895 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540898609 
[INFO ] 2024-11-14 07:34:58.895 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540898609 
[INFO ] 2024-11-14 07:34:58.895 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:34:58.895 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:34:58.895 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 68 ms 
[INFO ] 2024-11-14 07:34:58.901 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:58.901 - [任务 287(100)][f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] - Node f0cf3b2a-ca65-44ed-bfc1-8928192aebc8[f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:34:58.901 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:58.901 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.903 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:34:58.903 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:58.904 - [任务 287(100)][03e8efc8-ed7a-475b-b184-80917aa2ca84] - Node 03e8efc8-ed7a-475b-b184-80917aa2ca84[03e8efc8-ed7a-475b-b184-80917aa2ca84] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:34:58.904 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.904 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:34:58.905 - [任务 287(100)][f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] - Node f0cf3b2a-ca65-44ed-bfc1-8928192aebc8[f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.905 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:34:58.905 - [任务 287(100)][03e8efc8-ed7a-475b-b184-80917aa2ca84] - Node 03e8efc8-ed7a-475b-b184-80917aa2ca84[03e8efc8-ed7a-475b-b184-80917aa2ca84] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.905 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:34:58.905 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:34:59.111 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:34:59.175 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:34:59.198 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540898998 
[INFO ] 2024-11-14 07:34:59.198 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540898998 
[INFO ] 2024-11-14 07:34:59.198 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.198 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:34:59.219 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 24 ms 
[INFO ] 2024-11-14 07:34:59.221 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:34:59.433 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:34:59.441 - [任务 287(100)][22bdad84-df1d-4134-8fc1-335573b8f11a] - Node 22bdad84-df1d-4134-8fc1-335573b8f11a[22bdad84-df1d-4134-8fc1-335573b8f11a] running status set to false 
[INFO ] 2024-11-14 07:34:59.441 - [任务 287(100)][22bdad84-df1d-4134-8fc1-335573b8f11a] - Node 22bdad84-df1d-4134-8fc1-335573b8f11a[22bdad84-df1d-4134-8fc1-335573b8f11a] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.444 - [任务 287(100)][22bdad84-df1d-4134-8fc1-335573b8f11a] - Node 22bdad84-df1d-4134-8fc1-335573b8f11a[22bdad84-df1d-4134-8fc1-335573b8f11a] monitor closed 
[INFO ] 2024-11-14 07:34:59.445 - [任务 287(100)][22bdad84-df1d-4134-8fc1-335573b8f11a] - Node 22bdad84-df1d-4134-8fc1-335573b8f11a[22bdad84-df1d-4134-8fc1-335573b8f11a] close complete, cost 31 ms 
[INFO ] 2024-11-14 07:34:59.450 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-06b8f740-4646-495f-a750-1ef2a7d40afb 
[INFO ] 2024-11-14 07:34:59.450 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-06b8f740-4646-495f-a750-1ef2a7d40afb 
[INFO ] 2024-11-14 07:34:59.451 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.451 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.451 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:34:59.452 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 44 ms 
[INFO ] 2024-11-14 07:34:59.454 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:34:59.454 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:34:59.455 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:34:59.581 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:34:59.582 - [任务 287(100)][03e8efc8-ed7a-475b-b184-80917aa2ca84] - Node 03e8efc8-ed7a-475b-b184-80917aa2ca84[03e8efc8-ed7a-475b-b184-80917aa2ca84] running status set to false 
[INFO ] 2024-11-14 07:34:59.582 - [任务 287(100)][03e8efc8-ed7a-475b-b184-80917aa2ca84] - Node 03e8efc8-ed7a-475b-b184-80917aa2ca84[03e8efc8-ed7a-475b-b184-80917aa2ca84] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.583 - [任务 287(100)][03e8efc8-ed7a-475b-b184-80917aa2ca84] - Node 03e8efc8-ed7a-475b-b184-80917aa2ca84[03e8efc8-ed7a-475b-b184-80917aa2ca84] monitor closed 
[INFO ] 2024-11-14 07:34:59.584 - [任务 287(100)][03e8efc8-ed7a-475b-b184-80917aa2ca84] - Node 03e8efc8-ed7a-475b-b184-80917aa2ca84[03e8efc8-ed7a-475b-b184-80917aa2ca84] close complete, cost 5 ms 
[INFO ] 2024-11-14 07:34:59.584 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-71a037cd-b1e8-40e5-9c12-ef97685682a0 
[INFO ] 2024-11-14 07:34:59.584 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-71a037cd-b1e8-40e5-9c12-ef97685682a0 
[INFO ] 2024-11-14 07:34:59.584 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.584 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.584 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:34:59.590 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 16 ms 
[INFO ] 2024-11-14 07:34:59.594 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:34:59.594 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:34:59.594 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:34:59.606 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:34:59.625 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540898999 
[INFO ] 2024-11-14 07:34:59.625 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540898999 
[INFO ] 2024-11-14 07:34:59.625 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:34:59.625 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:34:59.672 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 23 ms 
[INFO ] 2024-11-14 07:34:59.672 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:35:00.006 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:00.007 - [任务 287(100)][8601d898-e923-4d8f-8aa0-8df03909517b] - Node 8601d898-e923-4d8f-8aa0-8df03909517b[8601d898-e923-4d8f-8aa0-8df03909517b] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:35:00.007 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:00.007 - [任务 287(100)][8601d898-e923-4d8f-8aa0-8df03909517b] - Node 8601d898-e923-4d8f-8aa0-8df03909517b[8601d898-e923-4d8f-8aa0-8df03909517b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:00.008 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:00.008 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:00.008 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:35:00.127 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:35:00.127 - [任务 287(100)][f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] - Node f0cf3b2a-ca65-44ed-bfc1-8928192aebc8[f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] running status set to false 
[INFO ] 2024-11-14 07:35:00.127 - [任务 287(100)][f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] - Node f0cf3b2a-ca65-44ed-bfc1-8928192aebc8[f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] schema data cleaned 
[INFO ] 2024-11-14 07:35:00.127 - [任务 287(100)][f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] - Node f0cf3b2a-ca65-44ed-bfc1-8928192aebc8[f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] monitor closed 
[INFO ] 2024-11-14 07:35:00.127 - [任务 287(100)][f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] - Node f0cf3b2a-ca65-44ed-bfc1-8928192aebc8[f0cf3b2a-ca65-44ed-bfc1-8928192aebc8] close complete, cost 0 ms 
[INFO ] 2024-11-14 07:35:00.130 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-26acb7cc-0c4d-42e3-af2c-79162caf9382 
[INFO ] 2024-11-14 07:35:00.131 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-26acb7cc-0c4d-42e3-af2c-79162caf9382 
[INFO ] 2024-11-14 07:35:00.131 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:35:00.132 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:35:00.133 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:35:00.135 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 19 ms 
[INFO ] 2024-11-14 07:35:00.136 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:35:00.137 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:35:00.137 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:35:00.344 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:35:00.352 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540900082 
[INFO ] 2024-11-14 07:35:00.352 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540900082 
[INFO ] 2024-11-14 07:35:00.352 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:35:00.352 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:35:00.352 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 54 ms 
[INFO ] 2024-11-14 07:35:00.378 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:35:00.693 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:35:00.694 - [任务 287(100)][8601d898-e923-4d8f-8aa0-8df03909517b] - Node 8601d898-e923-4d8f-8aa0-8df03909517b[8601d898-e923-4d8f-8aa0-8df03909517b] running status set to false 
[INFO ] 2024-11-14 07:35:00.695 - [任务 287(100)][8601d898-e923-4d8f-8aa0-8df03909517b] - Node 8601d898-e923-4d8f-8aa0-8df03909517b[8601d898-e923-4d8f-8aa0-8df03909517b] schema data cleaned 
[INFO ] 2024-11-14 07:35:00.695 - [任务 287(100)][8601d898-e923-4d8f-8aa0-8df03909517b] - Node 8601d898-e923-4d8f-8aa0-8df03909517b[8601d898-e923-4d8f-8aa0-8df03909517b] monitor closed 
[INFO ] 2024-11-14 07:35:00.699 - [任务 287(100)][8601d898-e923-4d8f-8aa0-8df03909517b] - Node 8601d898-e923-4d8f-8aa0-8df03909517b[8601d898-e923-4d8f-8aa0-8df03909517b] close complete, cost 6 ms 
[INFO ] 2024-11-14 07:35:00.699 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-d11572f5-a25b-441b-8ac8-bf216752b0af 
[INFO ] 2024-11-14 07:35:00.700 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-d11572f5-a25b-441b-8ac8-bf216752b0af 
[INFO ] 2024-11-14 07:35:00.700 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:35:00.700 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:35:00.700 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:35:00.703 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 21 ms 
[INFO ] 2024-11-14 07:35:00.703 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:35:00.704 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:35:00.910 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:35:01.667 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:01.670 - [任务 287(100)][c085c9a6-7b00-4a7e-9347-1e6e56bd9079] - Node c085c9a6-7b00-4a7e-9347-1e6e56bd9079[c085c9a6-7b00-4a7e-9347-1e6e56bd9079] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:35:01.670 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:01.670 - [任务 287(100)][c085c9a6-7b00-4a7e-9347-1e6e56bd9079] - Node c085c9a6-7b00-4a7e-9347-1e6e56bd9079[c085c9a6-7b00-4a7e-9347-1e6e56bd9079] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:01.670 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:01.670 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:01.670 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:35:01.938 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:35:01.938 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540901741 
[INFO ] 2024-11-14 07:35:01.938 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540901741 
[INFO ] 2024-11-14 07:35:01.938 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:35:01.939 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:35:01.939 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 15 ms 
[INFO ] 2024-11-14 07:35:02.139 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:35:02.251 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:35:02.261 - [任务 287(100)][c085c9a6-7b00-4a7e-9347-1e6e56bd9079] - Node c085c9a6-7b00-4a7e-9347-1e6e56bd9079[c085c9a6-7b00-4a7e-9347-1e6e56bd9079] running status set to false 
[INFO ] 2024-11-14 07:35:02.261 - [任务 287(100)][c085c9a6-7b00-4a7e-9347-1e6e56bd9079] - Node c085c9a6-7b00-4a7e-9347-1e6e56bd9079[c085c9a6-7b00-4a7e-9347-1e6e56bd9079] schema data cleaned 
[INFO ] 2024-11-14 07:35:02.261 - [任务 287(100)][c085c9a6-7b00-4a7e-9347-1e6e56bd9079] - Node c085c9a6-7b00-4a7e-9347-1e6e56bd9079[c085c9a6-7b00-4a7e-9347-1e6e56bd9079] monitor closed 
[INFO ] 2024-11-14 07:35:02.265 - [任务 287(100)][c085c9a6-7b00-4a7e-9347-1e6e56bd9079] - Node c085c9a6-7b00-4a7e-9347-1e6e56bd9079[c085c9a6-7b00-4a7e-9347-1e6e56bd9079] close complete, cost 1 ms 
[INFO ] 2024-11-14 07:35:02.265 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-87e0ba9e-7dc9-4069-84ba-29ce9567d6fa 
[INFO ] 2024-11-14 07:35:02.265 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-87e0ba9e-7dc9-4069-84ba-29ce9567d6fa 
[INFO ] 2024-11-14 07:35:02.266 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:35:02.266 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:35:02.267 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:35:02.267 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 17 ms 
[INFO ] 2024-11-14 07:35:02.268 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:35:02.269 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:35:02.269 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:35:07.217 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:07.218 - [任务 287(100)][857056f1-82b3-4804-a29c-e1e3af4c360c] - Node 857056f1-82b3-4804-a29c-e1e3af4c360c[857056f1-82b3-4804-a29c-e1e3af4c360c] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:35:07.219 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:07.219 - [任务 287(100)][857056f1-82b3-4804-a29c-e1e3af4c360c] - Node 857056f1-82b3-4804-a29c-e1e3af4c360c[857056f1-82b3-4804-a29c-e1e3af4c360c] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:07.219 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:07.219 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:07.219 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:35:07.335 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:07.335 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:07.335 - [任务 287(100)][040d3734-80f6-4d98-b94e-870ac52a4834] - Node 040d3734-80f6-4d98-b94e-870ac52a4834[040d3734-80f6-4d98-b94e-870ac52a4834] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:35:07.336 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:07.336 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:07.336 - [任务 287(100)][040d3734-80f6-4d98-b94e-870ac52a4834] - Node 040d3734-80f6-4d98-b94e-870ac52a4834[040d3734-80f6-4d98-b94e-870ac52a4834] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:07.336 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:35:07.628 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:35:07.628 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540907425 
[INFO ] 2024-11-14 07:35:07.628 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540907425 
[INFO ] 2024-11-14 07:35:07.628 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:35:07.629 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:35:07.629 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 20 ms 
[INFO ] 2024-11-14 07:35:07.773 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:35:07.773 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:35:07.789 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540907436 
[INFO ] 2024-11-14 07:35:07.789 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540907436 
[INFO ] 2024-11-14 07:35:07.789 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:35:07.789 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:35:07.789 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 16 ms 
[INFO ] 2024-11-14 07:35:07.806 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:35:08.178 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:35:08.195 - [任务 287(100)][857056f1-82b3-4804-a29c-e1e3af4c360c] - Node 857056f1-82b3-4804-a29c-e1e3af4c360c[857056f1-82b3-4804-a29c-e1e3af4c360c] running status set to false 
[INFO ] 2024-11-14 07:35:08.196 - [任务 287(100)][857056f1-82b3-4804-a29c-e1e3af4c360c] - Node 857056f1-82b3-4804-a29c-e1e3af4c360c[857056f1-82b3-4804-a29c-e1e3af4c360c] schema data cleaned 
[INFO ] 2024-11-14 07:35:08.197 - [任务 287(100)][857056f1-82b3-4804-a29c-e1e3af4c360c] - Node 857056f1-82b3-4804-a29c-e1e3af4c360c[857056f1-82b3-4804-a29c-e1e3af4c360c] monitor closed 
[INFO ] 2024-11-14 07:35:08.197 - [任务 287(100)][857056f1-82b3-4804-a29c-e1e3af4c360c] - Node 857056f1-82b3-4804-a29c-e1e3af4c360c[857056f1-82b3-4804-a29c-e1e3af4c360c] close complete, cost 6 ms 
[INFO ] 2024-11-14 07:35:08.204 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-28233d86-4281-4308-b266-03aeb7d0b073 
[INFO ] 2024-11-14 07:35:08.204 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-28233d86-4281-4308-b266-03aeb7d0b073 
[INFO ] 2024-11-14 07:35:08.205 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:35:08.205 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:35:08.212 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:35:08.212 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 40 ms 
[INFO ] 2024-11-14 07:35:08.223 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:35:08.223 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:35:08.223 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:35:08.338 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:35:08.339 - [任务 287(100)][040d3734-80f6-4d98-b94e-870ac52a4834] - Node 040d3734-80f6-4d98-b94e-870ac52a4834[040d3734-80f6-4d98-b94e-870ac52a4834] running status set to false 
[INFO ] 2024-11-14 07:35:08.339 - [任务 287(100)][040d3734-80f6-4d98-b94e-870ac52a4834] - Node 040d3734-80f6-4d98-b94e-870ac52a4834[040d3734-80f6-4d98-b94e-870ac52a4834] schema data cleaned 
[INFO ] 2024-11-14 07:35:08.342 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-17e09565-0cb7-4fc5-ae3f-ae4d9212d76e 
[INFO ] 2024-11-14 07:35:08.342 - [任务 287(100)][040d3734-80f6-4d98-b94e-870ac52a4834] - Node 040d3734-80f6-4d98-b94e-870ac52a4834[040d3734-80f6-4d98-b94e-870ac52a4834] monitor closed 
[INFO ] 2024-11-14 07:35:08.342 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-17e09565-0cb7-4fc5-ae3f-ae4d9212d76e 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)][040d3734-80f6-4d98-b94e-870ac52a4834] - Node 040d3734-80f6-4d98-b94e-870ac52a4834[040d3734-80f6-4d98-b94e-870ac52a4834] close complete, cost 3 ms 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 15 ms 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:35:08.343 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:35:09.770 - [任务 287(100)][1d5d90c9-fba0-442d-903b-894ee94e8b34] - Node 1d5d90c9-fba0-442d-903b-894ee94e8b34[1d5d90c9-fba0-442d-903b-894ee94e8b34] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:35:09.771 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:09.771 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:35:09.771 - [任务 287(100)][1d5d90c9-fba0-442d-903b-894ee94e8b34] - Node 1d5d90c9-fba0-442d-903b-894ee94e8b34[1d5d90c9-fba0-442d-903b-894ee94e8b34] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:35:09.771 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:35:09.771 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:35:09.771 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:35:10.030 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:35:10.044 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540909849 
[INFO ] 2024-11-14 07:35:10.044 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731540909849 
[INFO ] 2024-11-14 07:35:10.044 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:35:10.044 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:35:10.045 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 15 ms 
[INFO ] 2024-11-14 07:35:10.248 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:35:10.360 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:35:10.369 - [任务 287(100)][1d5d90c9-fba0-442d-903b-894ee94e8b34] - Node 1d5d90c9-fba0-442d-903b-894ee94e8b34[1d5d90c9-fba0-442d-903b-894ee94e8b34] running status set to false 
[INFO ] 2024-11-14 07:35:10.369 - [任务 287(100)][1d5d90c9-fba0-442d-903b-894ee94e8b34] - Node 1d5d90c9-fba0-442d-903b-894ee94e8b34[1d5d90c9-fba0-442d-903b-894ee94e8b34] schema data cleaned 
[INFO ] 2024-11-14 07:35:10.370 - [任务 287(100)][1d5d90c9-fba0-442d-903b-894ee94e8b34] - Node 1d5d90c9-fba0-442d-903b-894ee94e8b34[1d5d90c9-fba0-442d-903b-894ee94e8b34] monitor closed 
[INFO ] 2024-11-14 07:35:10.370 - [任务 287(100)][1d5d90c9-fba0-442d-903b-894ee94e8b34] - Node 1d5d90c9-fba0-442d-903b-894ee94e8b34[1d5d90c9-fba0-442d-903b-894ee94e8b34] close complete, cost 3 ms 
[INFO ] 2024-11-14 07:35:10.374 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-f09278e2-ded6-423e-a15f-6a4a59ea193f 
[INFO ] 2024-11-14 07:35:10.374 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-f09278e2-ded6-423e-a15f-6a4a59ea193f 
[INFO ] 2024-11-14 07:35:10.374 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:35:10.375 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:35:10.375 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:35:10.377 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 16 ms 
[INFO ] 2024-11-14 07:35:10.377 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:35:10.377 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:35:10.585 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:37:12.018 - [任务 287(100)][79134c90-5a15-41ac-9c71-8801926f7d41] - Node 79134c90-5a15-41ac-9c71-8801926f7d41[79134c90-5a15-41ac-9c71-8801926f7d41] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:37:12.018 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:12.018 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:12.018 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:37:12.018 - [任务 287(100)][79134c90-5a15-41ac-9c71-8801926f7d41] - Node 79134c90-5a15-41ac-9c71-8801926f7d41[79134c90-5a15-41ac-9c71-8801926f7d41] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:12.018 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:37:12.018 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:37:12.049 - [任务 287(100)][64779d68-de81-4413-a9dd-5da9ce215564] - Node 64779d68-de81-4413-a9dd-5da9ce215564[64779d68-de81-4413-a9dd-5da9ce215564] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:37:12.049 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:12.049 - [任务 287(100)][64779d68-de81-4413-a9dd-5da9ce215564] - Node 64779d68-de81-4413-a9dd-5da9ce215564[64779d68-de81-4413-a9dd-5da9ce215564] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:12.049 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:12.049 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:12.050 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:12.254 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:37:12.318 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:37:12.318 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541032111 
[INFO ] 2024-11-14 07:37:12.318 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541032111 
[INFO ] 2024-11-14 07:37:12.319 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.319 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:37:12.349 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 19 ms 
[INFO ] 2024-11-14 07:37:12.349 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:37:12.543 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:37:12.543 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541032117 
[INFO ] 2024-11-14 07:37:12.544 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541032117 
[INFO ] 2024-11-14 07:37:12.544 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.544 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:37:12.544 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 66 ms 
[INFO ] 2024-11-14 07:37:12.558 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:37:12.745 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:37:12.747 - [任务 287(100)][79134c90-5a15-41ac-9c71-8801926f7d41] - Node 79134c90-5a15-41ac-9c71-8801926f7d41[79134c90-5a15-41ac-9c71-8801926f7d41] running status set to false 
[INFO ] 2024-11-14 07:37:12.747 - [任务 287(100)][79134c90-5a15-41ac-9c71-8801926f7d41] - Node 79134c90-5a15-41ac-9c71-8801926f7d41[79134c90-5a15-41ac-9c71-8801926f7d41] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.747 - [任务 287(100)][79134c90-5a15-41ac-9c71-8801926f7d41] - Node 79134c90-5a15-41ac-9c71-8801926f7d41[79134c90-5a15-41ac-9c71-8801926f7d41] monitor closed 
[INFO ] 2024-11-14 07:37:12.747 - [任务 287(100)][79134c90-5a15-41ac-9c71-8801926f7d41] - Node 79134c90-5a15-41ac-9c71-8801926f7d41[79134c90-5a15-41ac-9c71-8801926f7d41] close complete, cost 2 ms 
[INFO ] 2024-11-14 07:37:12.762 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-5d7991eb-e955-401a-b1fe-9a5f14088d54 
[INFO ] 2024-11-14 07:37:12.762 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-5d7991eb-e955-401a-b1fe-9a5f14088d54 
[INFO ] 2024-11-14 07:37:12.763 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.763 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.763 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:37:12.763 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 31 ms 
[INFO ] 2024-11-14 07:37:12.767 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:37:12.767 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:37:12.768 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:37:12.936 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:37:12.943 - [任务 287(100)][64779d68-de81-4413-a9dd-5da9ce215564] - Node 64779d68-de81-4413-a9dd-5da9ce215564[64779d68-de81-4413-a9dd-5da9ce215564] running status set to false 
[INFO ] 2024-11-14 07:37:12.943 - [任务 287(100)][64779d68-de81-4413-a9dd-5da9ce215564] - Node 64779d68-de81-4413-a9dd-5da9ce215564[64779d68-de81-4413-a9dd-5da9ce215564] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.943 - [任务 287(100)][64779d68-de81-4413-a9dd-5da9ce215564] - Node 64779d68-de81-4413-a9dd-5da9ce215564[64779d68-de81-4413-a9dd-5da9ce215564] monitor closed 
[INFO ] 2024-11-14 07:37:12.943 - [任务 287(100)][64779d68-de81-4413-a9dd-5da9ce215564] - Node 64779d68-de81-4413-a9dd-5da9ce215564[64779d68-de81-4413-a9dd-5da9ce215564] close complete, cost 2 ms 
[INFO ] 2024-11-14 07:37:12.944 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-bd92d624-3b64-4d29-8a3c-7043d00d10c1 
[INFO ] 2024-11-14 07:37:12.945 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-bd92d624-3b64-4d29-8a3c-7043d00d10c1 
[INFO ] 2024-11-14 07:37:12.945 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.945 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:37:12.945 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:37:12.945 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 22 ms 
[INFO ] 2024-11-14 07:37:12.947 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:37:12.947 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:37:12.947 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:37:21.659 - [任务 287(100)][711141b5-c504-4a75-9b6a-ee28b6d7c478] - Node 711141b5-c504-4a75-9b6a-ee28b6d7c478[711141b5-c504-4a75-9b6a-ee28b6d7c478] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:37:21.659 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:21.659 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:21.659 - [任务 287(100)][711141b5-c504-4a75-9b6a-ee28b6d7c478] - Node 711141b5-c504-4a75-9b6a-ee28b6d7c478[711141b5-c504-4a75-9b6a-ee28b6d7c478] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:21.659 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:21.660 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:21.660 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:37:21.944 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:37:21.970 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541041752 
[INFO ] 2024-11-14 07:37:21.971 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541041752 
[INFO ] 2024-11-14 07:37:21.971 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:37:21.971 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:37:21.971 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 30 ms 
[INFO ] 2024-11-14 07:37:22.181 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:37:22.422 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:37:22.423 - [任务 287(100)][711141b5-c504-4a75-9b6a-ee28b6d7c478] - Node 711141b5-c504-4a75-9b6a-ee28b6d7c478[711141b5-c504-4a75-9b6a-ee28b6d7c478] running status set to false 
[INFO ] 2024-11-14 07:37:22.423 - [任务 287(100)][711141b5-c504-4a75-9b6a-ee28b6d7c478] - Node 711141b5-c504-4a75-9b6a-ee28b6d7c478[711141b5-c504-4a75-9b6a-ee28b6d7c478] schema data cleaned 
[INFO ] 2024-11-14 07:37:22.429 - [任务 287(100)][711141b5-c504-4a75-9b6a-ee28b6d7c478] - Node 711141b5-c504-4a75-9b6a-ee28b6d7c478[711141b5-c504-4a75-9b6a-ee28b6d7c478] monitor closed 
[INFO ] 2024-11-14 07:37:22.429 - [任务 287(100)][711141b5-c504-4a75-9b6a-ee28b6d7c478] - Node 711141b5-c504-4a75-9b6a-ee28b6d7c478[711141b5-c504-4a75-9b6a-ee28b6d7c478] close complete, cost 8 ms 
[INFO ] 2024-11-14 07:37:22.441 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-1e56365e-4a73-4167-9c3f-5998b8c4123e 
[INFO ] 2024-11-14 07:37:22.450 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-1e56365e-4a73-4167-9c3f-5998b8c4123e 
[INFO ] 2024-11-14 07:37:22.450 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:37:22.453 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:37:22.454 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:37:22.456 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 44 ms 
[INFO ] 2024-11-14 07:37:22.456 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:37:22.456 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:37:22.662 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:37:22.693 - [任务 287(100)][e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] - Node e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5[e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:37:22.693 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:22.693 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:22.693 - [任务 287(100)][e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] - Node e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5[e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:22.693 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:22.693 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:22.895 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:37:22.991 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:37:22.992 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541042780 
[INFO ] 2024-11-14 07:37:22.992 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541042780 
[INFO ] 2024-11-14 07:37:22.992 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:37:22.992 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:37:22.992 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 23 ms 
[INFO ] 2024-11-14 07:37:23.208 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:37:23.417 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:37:23.427 - [任务 287(100)][e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] - Node e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5[e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] running status set to false 
[INFO ] 2024-11-14 07:37:23.430 - [任务 287(100)][e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] - Node e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5[e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] schema data cleaned 
[INFO ] 2024-11-14 07:37:23.431 - [任务 287(100)][e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] - Node e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5[e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] monitor closed 
[INFO ] 2024-11-14 07:37:23.432 - [任务 287(100)][e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] - Node e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5[e3e97fb1-e392-4d3a-bf6e-fb3488dd3cf5] close complete, cost 5 ms 
[INFO ] 2024-11-14 07:37:23.435 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-c00057b3-cc8f-47b3-be32-2beff95763f8 
[INFO ] 2024-11-14 07:37:23.436 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-c00057b3-cc8f-47b3-be32-2beff95763f8 
[INFO ] 2024-11-14 07:37:23.436 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:37:23.436 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:37:23.436 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:37:23.437 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 31 ms 
[INFO ] 2024-11-14 07:37:23.438 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:37:23.438 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:37:23.643 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:37:24.982 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:24.983 - [任务 287(100)][1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] - Node 1ac6679c-4dcf-44d5-b878-e8b2dad1e83a[1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:37:24.983 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:37:24.983 - [任务 287(100)][1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] - Node 1ac6679c-4dcf-44d5-b878-e8b2dad1e83a[1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:24.983 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:37:24.983 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:37:25.191 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:37:25.317 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:37:25.317 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541045087 
[INFO ] 2024-11-14 07:37:25.317 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541045087 
[INFO ] 2024-11-14 07:37:25.317 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:37:25.317 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:37:25.317 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 35 ms 
[INFO ] 2024-11-14 07:37:25.349 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:37:25.672 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:37:25.673 - [任务 287(100)][1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] - Node 1ac6679c-4dcf-44d5-b878-e8b2dad1e83a[1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] running status set to false 
[INFO ] 2024-11-14 07:37:25.673 - [任务 287(100)][1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] - Node 1ac6679c-4dcf-44d5-b878-e8b2dad1e83a[1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] schema data cleaned 
[INFO ] 2024-11-14 07:37:25.673 - [任务 287(100)][1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] - Node 1ac6679c-4dcf-44d5-b878-e8b2dad1e83a[1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] monitor closed 
[INFO ] 2024-11-14 07:37:25.674 - [任务 287(100)][1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] - Node 1ac6679c-4dcf-44d5-b878-e8b2dad1e83a[1ac6679c-4dcf-44d5-b878-e8b2dad1e83a] close complete, cost 2 ms 
[INFO ] 2024-11-14 07:37:25.682 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-8bf75e9a-a2b0-4af7-a831-24dfd43db920 
[INFO ] 2024-11-14 07:37:25.682 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-8bf75e9a-a2b0-4af7-a831-24dfd43db920 
[INFO ] 2024-11-14 07:37:25.682 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:37:25.682 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:37:25.683 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:37:25.684 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 14 ms 
[INFO ] 2024-11-14 07:37:25.684 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:37:25.684 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:37:25.895 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:39:00.773 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:39:00.775 - [任务 287(100)][1dc35f22-0793-47ed-a4cf-5a40f0e9b382] - Node 1dc35f22-0793-47ed-a4cf-5a40f0e9b382[1dc35f22-0793-47ed-a4cf-5a40f0e9b382] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:39:00.775 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:39:00.775 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:39:00.775 - [任务 287(100)][1dc35f22-0793-47ed-a4cf-5a40f0e9b382] - Node 1dc35f22-0793-47ed-a4cf-5a40f0e9b382[1dc35f22-0793-47ed-a4cf-5a40f0e9b382] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:39:00.775 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:39:00.775 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:39:01.035 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:39:01.050 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541140855 
[INFO ] 2024-11-14 07:39:01.050 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541140855 
[INFO ] 2024-11-14 07:39:01.050 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:39:01.050 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:39:01.051 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 15 ms 
[INFO ] 2024-11-14 07:39:01.256 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:39:01.384 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:39:01.394 - [任务 287(100)][1dc35f22-0793-47ed-a4cf-5a40f0e9b382] - Node 1dc35f22-0793-47ed-a4cf-5a40f0e9b382[1dc35f22-0793-47ed-a4cf-5a40f0e9b382] running status set to false 
[INFO ] 2024-11-14 07:39:01.395 - [任务 287(100)][1dc35f22-0793-47ed-a4cf-5a40f0e9b382] - Node 1dc35f22-0793-47ed-a4cf-5a40f0e9b382[1dc35f22-0793-47ed-a4cf-5a40f0e9b382] schema data cleaned 
[INFO ] 2024-11-14 07:39:01.395 - [任务 287(100)][1dc35f22-0793-47ed-a4cf-5a40f0e9b382] - Node 1dc35f22-0793-47ed-a4cf-5a40f0e9b382[1dc35f22-0793-47ed-a4cf-5a40f0e9b382] monitor closed 
[INFO ] 2024-11-14 07:39:01.395 - [任务 287(100)][1dc35f22-0793-47ed-a4cf-5a40f0e9b382] - Node 1dc35f22-0793-47ed-a4cf-5a40f0e9b382[1dc35f22-0793-47ed-a4cf-5a40f0e9b382] close complete, cost 1 ms 
[INFO ] 2024-11-14 07:39:01.399 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-2b052c3b-a9d3-4143-ab5a-e9bbc9bcdaad 
[INFO ] 2024-11-14 07:39:01.399 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-2b052c3b-a9d3-4143-ab5a-e9bbc9bcdaad 
[INFO ] 2024-11-14 07:39:01.400 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:39:01.400 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:39:01.400 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:39:01.402 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 19 ms 
[INFO ] 2024-11-14 07:39:01.402 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:39:01.402 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:39:01.403 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:39:06.704 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:39:06.705 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:39:06.706 - [任务 287(100)][db989cc7-61a2-4d9f-bbc1-b332adf1b104] - Node db989cc7-61a2-4d9f-bbc1-b332adf1b104[db989cc7-61a2-4d9f-bbc1-b332adf1b104] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:39:06.707 - [任务 287(100)][db989cc7-61a2-4d9f-bbc1-b332adf1b104] - Node db989cc7-61a2-4d9f-bbc1-b332adf1b104[db989cc7-61a2-4d9f-bbc1-b332adf1b104] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:39:06.707 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:39:06.707 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:39:06.707 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:39:22.878 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:39:30.499 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541146795 
[INFO ] 2024-11-14 07:39:30.506 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541146795 
[INFO ] 2024-11-14 07:39:30.506 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:39:30.506 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:39:30.506 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 23525 ms 
[INFO ] 2024-11-14 07:39:30.711 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:39:30.938 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:39:30.939 - [任务 287(100)][db989cc7-61a2-4d9f-bbc1-b332adf1b104] - Node db989cc7-61a2-4d9f-bbc1-b332adf1b104[db989cc7-61a2-4d9f-bbc1-b332adf1b104] running status set to false 
[INFO ] 2024-11-14 07:39:30.939 - [任务 287(100)][db989cc7-61a2-4d9f-bbc1-b332adf1b104] - Node db989cc7-61a2-4d9f-bbc1-b332adf1b104[db989cc7-61a2-4d9f-bbc1-b332adf1b104] schema data cleaned 
[INFO ] 2024-11-14 07:39:30.939 - [任务 287(100)][db989cc7-61a2-4d9f-bbc1-b332adf1b104] - Node db989cc7-61a2-4d9f-bbc1-b332adf1b104[db989cc7-61a2-4d9f-bbc1-b332adf1b104] monitor closed 
[INFO ] 2024-11-14 07:39:30.940 - [任务 287(100)][db989cc7-61a2-4d9f-bbc1-b332adf1b104] - Node db989cc7-61a2-4d9f-bbc1-b332adf1b104[db989cc7-61a2-4d9f-bbc1-b332adf1b104] close complete, cost 5 ms 
[INFO ] 2024-11-14 07:39:30.956 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-199ed510-6122-43d2-bb81-102b466bb722 
[INFO ] 2024-11-14 07:39:30.956 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-199ed510-6122-43d2-bb81-102b466bb722 
[INFO ] 2024-11-14 07:39:30.956 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:39:30.956 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:39:30.957 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:39:30.959 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 31 ms 
[INFO ] 2024-11-14 07:39:30.959 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:39:30.960 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:39:31.170 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:39:57.929 - [任务 287(100)][623ec9fb-113f-4b8d-86a8-6ce67a1fe322] - Node 623ec9fb-113f-4b8d-86a8-6ce67a1fe322[623ec9fb-113f-4b8d-86a8-6ce67a1fe322] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:39:57.930 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:39:57.930 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:39:57.930 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:39:57.930 - [任务 287(100)][623ec9fb-113f-4b8d-86a8-6ce67a1fe322] - Node 623ec9fb-113f-4b8d-86a8-6ce67a1fe322[623ec9fb-113f-4b8d-86a8-6ce67a1fe322] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:39:57.930 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:39:57.932 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:40:00.943 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:40:01.016 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541198025 
[INFO ] 2024-11-14 07:40:01.016 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541198025 
[INFO ] 2024-11-14 07:40:01.017 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:40:01.017 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:40:01.045 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 2794 ms 
[INFO ] 2024-11-14 07:40:01.045 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:40:01.433 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:40:01.434 - [任务 287(100)][623ec9fb-113f-4b8d-86a8-6ce67a1fe322] - Node 623ec9fb-113f-4b8d-86a8-6ce67a1fe322[623ec9fb-113f-4b8d-86a8-6ce67a1fe322] running status set to false 
[INFO ] 2024-11-14 07:40:01.434 - [任务 287(100)][623ec9fb-113f-4b8d-86a8-6ce67a1fe322] - Node 623ec9fb-113f-4b8d-86a8-6ce67a1fe322[623ec9fb-113f-4b8d-86a8-6ce67a1fe322] schema data cleaned 
[INFO ] 2024-11-14 07:40:01.434 - [任务 287(100)][623ec9fb-113f-4b8d-86a8-6ce67a1fe322] - Node 623ec9fb-113f-4b8d-86a8-6ce67a1fe322[623ec9fb-113f-4b8d-86a8-6ce67a1fe322] monitor closed 
[INFO ] 2024-11-14 07:40:01.434 - [任务 287(100)][623ec9fb-113f-4b8d-86a8-6ce67a1fe322] - Node 623ec9fb-113f-4b8d-86a8-6ce67a1fe322[623ec9fb-113f-4b8d-86a8-6ce67a1fe322] close complete, cost 0 ms 
[INFO ] 2024-11-14 07:40:01.437 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-dd8d9f9c-7168-4b11-87cb-ce4cc092a563 
[INFO ] 2024-11-14 07:40:01.437 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-dd8d9f9c-7168-4b11-87cb-ce4cc092a563 
[INFO ] 2024-11-14 07:40:01.437 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:40:01.438 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:40:01.438 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:40:01.439 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 18 ms 
[INFO ] 2024-11-14 07:40:01.442 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:40:01.442 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:40:01.443 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:41:21.696 - [任务 287(100)][d2ac54f2-52ab-4728-9746-90167d33cf17] - Node d2ac54f2-52ab-4728-9746-90167d33cf17[d2ac54f2-52ab-4728-9746-90167d33cf17] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:41:21.697 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:41:21.697 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:41:21.697 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:41:21.697 - [任务 287(100)][d2ac54f2-52ab-4728-9746-90167d33cf17] - Node d2ac54f2-52ab-4728-9746-90167d33cf17[d2ac54f2-52ab-4728-9746-90167d33cf17] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:41:21.698 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:41:21.699 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:41:21.955 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:41:25.107 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541281776 
[INFO ] 2024-11-14 07:41:25.107 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541281776 
[INFO ] 2024-11-14 07:41:25.107 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:41:25.107 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:43:10.525 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 3152 ms 
[INFO ] 2024-11-14 07:43:10.748 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:43:11.421 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:43:11.424 - [任务 287(100)][d2ac54f2-52ab-4728-9746-90167d33cf17] - Node d2ac54f2-52ab-4728-9746-90167d33cf17[d2ac54f2-52ab-4728-9746-90167d33cf17] running status set to false 
[INFO ] 2024-11-14 07:43:11.424 - [任务 287(100)][d2ac54f2-52ab-4728-9746-90167d33cf17] - Node d2ac54f2-52ab-4728-9746-90167d33cf17[d2ac54f2-52ab-4728-9746-90167d33cf17] schema data cleaned 
[INFO ] 2024-11-14 07:43:11.424 - [任务 287(100)][d2ac54f2-52ab-4728-9746-90167d33cf17] - Node d2ac54f2-52ab-4728-9746-90167d33cf17[d2ac54f2-52ab-4728-9746-90167d33cf17] monitor closed 
[INFO ] 2024-11-14 07:43:11.424 - [任务 287(100)][d2ac54f2-52ab-4728-9746-90167d33cf17] - Node d2ac54f2-52ab-4728-9746-90167d33cf17[d2ac54f2-52ab-4728-9746-90167d33cf17] close complete, cost 1 ms 
[INFO ] 2024-11-14 07:43:11.437 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-bec140fc-fc16-4bc6-9cc0-520017a74f22 
[INFO ] 2024-11-14 07:43:11.438 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-bec140fc-fc16-4bc6-9cc0-520017a74f22 
[INFO ] 2024-11-14 07:43:11.438 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:43:11.439 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:43:11.439 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:43:11.441 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 35 ms 
[INFO ] 2024-11-14 07:43:11.442 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:43:11.442 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:43:11.442 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 07:44:18.472 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:44:18.472 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 07:44:18.472 - [任务 287(100)][ee4fd119-721d-4f74-977d-a3d6c66b5574] - Node ee4fd119-721d-4f74-977d-a3d6c66b5574[ee4fd119-721d-4f74-977d-a3d6c66b5574] start preload schema,table counts: 0 
[INFO ] 2024-11-14 07:44:18.472 - [任务 287(100)][ee4fd119-721d-4f74-977d-a3d6c66b5574] - Node ee4fd119-721d-4f74-977d-a3d6c66b5574[ee4fd119-721d-4f74-977d-a3d6c66b5574] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:44:18.473 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 07:44:18.473 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 07:44:18.473 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 07:44:18.762 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 07:44:18.762 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541458565 
[INFO ] 2024-11-14 07:44:18.762 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731541458565 
[INFO ] 2024-11-14 07:44:18.762 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 07:44:18.762 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 07:44:18.763 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 15 ms 
[INFO ] 2024-11-14 07:44:18.968 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 07:44:19.086 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 07:44:19.087 - [任务 287(100)][ee4fd119-721d-4f74-977d-a3d6c66b5574] - Node ee4fd119-721d-4f74-977d-a3d6c66b5574[ee4fd119-721d-4f74-977d-a3d6c66b5574] running status set to false 
[INFO ] 2024-11-14 07:44:19.087 - [任务 287(100)][ee4fd119-721d-4f74-977d-a3d6c66b5574] - Node ee4fd119-721d-4f74-977d-a3d6c66b5574[ee4fd119-721d-4f74-977d-a3d6c66b5574] schema data cleaned 
[INFO ] 2024-11-14 07:44:19.087 - [任务 287(100)][ee4fd119-721d-4f74-977d-a3d6c66b5574] - Node ee4fd119-721d-4f74-977d-a3d6c66b5574[ee4fd119-721d-4f74-977d-a3d6c66b5574] monitor closed 
[INFO ] 2024-11-14 07:44:19.087 - [任务 287(100)][ee4fd119-721d-4f74-977d-a3d6c66b5574] - Node ee4fd119-721d-4f74-977d-a3d6c66b5574[ee4fd119-721d-4f74-977d-a3d6c66b5574] close complete, cost 1 ms 
[INFO ] 2024-11-14 07:44:19.100 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-90f67616-4350-41d1-a4f2-bce0224a8ca0 
[INFO ] 2024-11-14 07:44:19.100 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-90f67616-4350-41d1-a4f2-bce0224a8ca0 
[INFO ] 2024-11-14 07:44:19.101 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 07:44:19.101 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 07:44:19.101 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 07:44:19.103 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 21 ms 
[INFO ] 2024-11-14 07:44:19.103 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 07:44:19.104 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 07:44:19.104 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:08:00.599 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:08:00.600 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:08:00.600 - [任务 287(100)][a46f7aea-4611-4bcc-92dd-42f27d89c6b2] - Node a46f7aea-4611-4bcc-92dd-42f27d89c6b2[a46f7aea-4611-4bcc-92dd-42f27d89c6b2] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:08:00.600 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:08:00.600 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:08:00.601 - [任务 287(100)][a46f7aea-4611-4bcc-92dd-42f27d89c6b2] - Node a46f7aea-4611-4bcc-92dd-42f27d89c6b2[a46f7aea-4611-4bcc-92dd-42f27d89c6b2] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:08:00.602 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:08:04.544 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:08:06.344 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731542880738 
[INFO ] 2024-11-14 08:08:06.344 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731542880738 
[INFO ] 2024-11-14 08:08:06.344 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:08:06.391 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:08:06.391 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 5412 ms 
[INFO ] 2024-11-14 08:08:06.391 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:08:06.804 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:08:06.806 - [任务 287(100)][a46f7aea-4611-4bcc-92dd-42f27d89c6b2] - Node a46f7aea-4611-4bcc-92dd-42f27d89c6b2[a46f7aea-4611-4bcc-92dd-42f27d89c6b2] running status set to false 
[INFO ] 2024-11-14 08:08:06.806 - [任务 287(100)][a46f7aea-4611-4bcc-92dd-42f27d89c6b2] - Node a46f7aea-4611-4bcc-92dd-42f27d89c6b2[a46f7aea-4611-4bcc-92dd-42f27d89c6b2] schema data cleaned 
[INFO ] 2024-11-14 08:08:06.806 - [任务 287(100)][a46f7aea-4611-4bcc-92dd-42f27d89c6b2] - Node a46f7aea-4611-4bcc-92dd-42f27d89c6b2[a46f7aea-4611-4bcc-92dd-42f27d89c6b2] monitor closed 
[INFO ] 2024-11-14 08:08:06.806 - [任务 287(100)][a46f7aea-4611-4bcc-92dd-42f27d89c6b2] - Node a46f7aea-4611-4bcc-92dd-42f27d89c6b2[a46f7aea-4611-4bcc-92dd-42f27d89c6b2] close complete, cost 1 ms 
[INFO ] 2024-11-14 08:08:06.818 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-47bc1ad1-5ef2-4866-ac8f-63e3214f8b9a 
[INFO ] 2024-11-14 08:08:06.818 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-47bc1ad1-5ef2-4866-ac8f-63e3214f8b9a 
[INFO ] 2024-11-14 08:08:06.818 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:08:06.818 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:08:06.818 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:08:06.818 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 25 ms 
[INFO ] 2024-11-14 08:08:06.820 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:08:06.820 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:08:07.025 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:08:22.511 - [任务 287(100)][b288cdc1-3be9-4221-bcc6-3858f7d854fb] - Node b288cdc1-3be9-4221-bcc6-3858f7d854fb[b288cdc1-3be9-4221-bcc6-3858f7d854fb] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:08:22.511 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:08:22.511 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:08:22.511 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 08:08:22.512 - [任务 287(100)][b288cdc1-3be9-4221-bcc6-3858f7d854fb] - Node b288cdc1-3be9-4221-bcc6-3858f7d854fb[b288cdc1-3be9-4221-bcc6-3858f7d854fb] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 08:08:22.512 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 08:08:22.513 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:08:32.831 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:09:56.249 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731542902596 
[INFO ] 2024-11-14 08:09:56.251 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731542902596 
[INFO ] 2024-11-14 08:09:56.251 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:09:56.251 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:09:56.318 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 93445 ms 
[INFO ] 2024-11-14 08:09:56.318 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:09:56.775 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:09:56.777 - [任务 287(100)][b288cdc1-3be9-4221-bcc6-3858f7d854fb] - Node b288cdc1-3be9-4221-bcc6-3858f7d854fb[b288cdc1-3be9-4221-bcc6-3858f7d854fb] running status set to false 
[INFO ] 2024-11-14 08:09:56.777 - [任务 287(100)][b288cdc1-3be9-4221-bcc6-3858f7d854fb] - Node b288cdc1-3be9-4221-bcc6-3858f7d854fb[b288cdc1-3be9-4221-bcc6-3858f7d854fb] schema data cleaned 
[INFO ] 2024-11-14 08:09:56.787 - [任务 287(100)][b288cdc1-3be9-4221-bcc6-3858f7d854fb] - Node b288cdc1-3be9-4221-bcc6-3858f7d854fb[b288cdc1-3be9-4221-bcc6-3858f7d854fb] monitor closed 
[INFO ] 2024-11-14 08:09:56.787 - [任务 287(100)][b288cdc1-3be9-4221-bcc6-3858f7d854fb] - Node b288cdc1-3be9-4221-bcc6-3858f7d854fb[b288cdc1-3be9-4221-bcc6-3858f7d854fb] close complete, cost 14 ms 
[INFO ] 2024-11-14 08:09:56.790 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-b9d9df7b-2950-4d01-a741-77fc6bdcdc60 
[INFO ] 2024-11-14 08:09:56.791 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-b9d9df7b-2950-4d01-a741-77fc6bdcdc60 
[INFO ] 2024-11-14 08:09:56.791 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:09:56.792 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:09:56.792 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:09:56.796 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 30 ms 
[INFO ] 2024-11-14 08:09:56.796 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:09:56.796 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:09:56.797 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:10:14.497 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:10:14.497 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:10:14.498 - [任务 287(100)][4e776679-584a-410e-8b48-f7ad058bf99f] - Node 4e776679-584a-410e-8b48-f7ad058bf99f[4e776679-584a-410e-8b48-f7ad058bf99f] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:10:14.498 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:10:14.498 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:10:14.498 - [任务 287(100)][4e776679-584a-410e-8b48-f7ad058bf99f] - Node 4e776679-584a-410e-8b48-f7ad058bf99f[4e776679-584a-410e-8b48-f7ad058bf99f] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:10:14.499 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:10:14.759 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:10:14.772 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543014575 
[INFO ] 2024-11-14 08:10:14.772 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543014575 
[INFO ] 2024-11-14 08:10:14.772 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:10:14.773 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:10:14.773 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 13 ms 
[INFO ] 2024-11-14 08:11:18.957 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:11:19.384 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:11:19.388 - [任务 287(100)][4e776679-584a-410e-8b48-f7ad058bf99f] - Node 4e776679-584a-410e-8b48-f7ad058bf99f[4e776679-584a-410e-8b48-f7ad058bf99f] running status set to false 
[INFO ] 2024-11-14 08:11:19.394 - [任务 287(100)][4e776679-584a-410e-8b48-f7ad058bf99f] - Node 4e776679-584a-410e-8b48-f7ad058bf99f[4e776679-584a-410e-8b48-f7ad058bf99f] schema data cleaned 
[INFO ] 2024-11-14 08:11:19.397 - [任务 287(100)][4e776679-584a-410e-8b48-f7ad058bf99f] - Node 4e776679-584a-410e-8b48-f7ad058bf99f[4e776679-584a-410e-8b48-f7ad058bf99f] monitor closed 
[INFO ] 2024-11-14 08:11:19.398 - [任务 287(100)][4e776679-584a-410e-8b48-f7ad058bf99f] - Node 4e776679-584a-410e-8b48-f7ad058bf99f[4e776679-584a-410e-8b48-f7ad058bf99f] close complete, cost 2 ms 
[INFO ] 2024-11-14 08:11:19.405 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-552a497c-404d-4b9c-b775-851cc8dc21a3 
[INFO ] 2024-11-14 08:11:19.406 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-552a497c-404d-4b9c-b775-851cc8dc21a3 
[INFO ] 2024-11-14 08:11:19.406 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:11:19.406 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:11:19.406 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:11:19.408 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 38 ms 
[INFO ] 2024-11-14 08:11:19.410 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:11:19.410 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:11:19.612 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:13:07.720 - [任务 287(100)][468317ac-bd12-40fb-b7b2-c9d5807cf53d] - Node 468317ac-bd12-40fb-b7b2-c9d5807cf53d[468317ac-bd12-40fb-b7b2-c9d5807cf53d] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:13:07.723 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:13:07.727 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:13:07.728 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:13:07.729 - [任务 287(100)][468317ac-bd12-40fb-b7b2-c9d5807cf53d] - Node 468317ac-bd12-40fb-b7b2-c9d5807cf53d[468317ac-bd12-40fb-b7b2-c9d5807cf53d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:13:07.730 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:13:07.731 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:13:08.905 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:15:32.837 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543188184 
[INFO ] 2024-11-14 08:15:32.838 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543188184 
[INFO ] 2024-11-14 08:15:32.839 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:15:32.839 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:15:32.841 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 143938 ms 
[INFO ] 2024-11-14 08:15:33.049 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:15:33.630 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:15:33.632 - [任务 287(100)][468317ac-bd12-40fb-b7b2-c9d5807cf53d] - Node 468317ac-bd12-40fb-b7b2-c9d5807cf53d[468317ac-bd12-40fb-b7b2-c9d5807cf53d] running status set to false 
[INFO ] 2024-11-14 08:15:33.633 - [任务 287(100)][468317ac-bd12-40fb-b7b2-c9d5807cf53d] - Node 468317ac-bd12-40fb-b7b2-c9d5807cf53d[468317ac-bd12-40fb-b7b2-c9d5807cf53d] schema data cleaned 
[INFO ] 2024-11-14 08:15:33.637 - [任务 287(100)][468317ac-bd12-40fb-b7b2-c9d5807cf53d] - Node 468317ac-bd12-40fb-b7b2-c9d5807cf53d[468317ac-bd12-40fb-b7b2-c9d5807cf53d] monitor closed 
[INFO ] 2024-11-14 08:15:33.648 - [任务 287(100)][468317ac-bd12-40fb-b7b2-c9d5807cf53d] - Node 468317ac-bd12-40fb-b7b2-c9d5807cf53d[468317ac-bd12-40fb-b7b2-c9d5807cf53d] close complete, cost 17 ms 
[INFO ] 2024-11-14 08:15:33.656 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-ec3bb2b5-7f42-44d0-8946-dcf68645071d 
[INFO ] 2024-11-14 08:15:33.659 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-ec3bb2b5-7f42-44d0-8946-dcf68645071d 
[INFO ] 2024-11-14 08:15:33.659 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:15:33.663 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:15:33.664 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:15:33.679 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 62 ms 
[INFO ] 2024-11-14 08:15:33.682 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:15:33.682 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:15:33.887 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:16:38.716 - [任务 287(100)][82105941-bc6f-4d63-a65a-768159f1afc0] - Node 82105941-bc6f-4d63-a65a-768159f1afc0[82105941-bc6f-4d63-a65a-768159f1afc0] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:16:38.718 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:16:38.719 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:16:38.720 - [任务 287(100)][82105941-bc6f-4d63-a65a-768159f1afc0] - Node 82105941-bc6f-4d63-a65a-768159f1afc0[82105941-bc6f-4d63-a65a-768159f1afc0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:16:38.720 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:16:38.721 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 08:16:38.721 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:16:39.027 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:16:39.057 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543398831 
[INFO ] 2024-11-14 08:16:39.058 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543398831 
[INFO ] 2024-11-14 08:16:39.058 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:16:39.059 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:16:39.060 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 45 ms 
[INFO ] 2024-11-14 08:16:39.269 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:16:39.452 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:16:39.460 - [任务 287(100)][82105941-bc6f-4d63-a65a-768159f1afc0] - Node 82105941-bc6f-4d63-a65a-768159f1afc0[82105941-bc6f-4d63-a65a-768159f1afc0] running status set to false 
[INFO ] 2024-11-14 08:16:39.461 - [任务 287(100)][82105941-bc6f-4d63-a65a-768159f1afc0] - Node 82105941-bc6f-4d63-a65a-768159f1afc0[82105941-bc6f-4d63-a65a-768159f1afc0] schema data cleaned 
[INFO ] 2024-11-14 08:16:39.461 - [任务 287(100)][82105941-bc6f-4d63-a65a-768159f1afc0] - Node 82105941-bc6f-4d63-a65a-768159f1afc0[82105941-bc6f-4d63-a65a-768159f1afc0] monitor closed 
[INFO ] 2024-11-14 08:16:39.461 - [任务 287(100)][82105941-bc6f-4d63-a65a-768159f1afc0] - Node 82105941-bc6f-4d63-a65a-768159f1afc0[82105941-bc6f-4d63-a65a-768159f1afc0] close complete, cost 8 ms 
[INFO ] 2024-11-14 08:16:39.469 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-5ef7c109-beb0-49ba-ac12-be2c8780b5be 
[INFO ] 2024-11-14 08:16:39.469 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-5ef7c109-beb0-49ba-ac12-be2c8780b5be 
[INFO ] 2024-11-14 08:16:39.470 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:16:39.471 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:16:39.472 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:16:39.475 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 29 ms 
[INFO ] 2024-11-14 08:16:39.477 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:16:39.477 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:16:39.481 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:16:44.445 - [任务 287(100)][8884a65a-1a45-42da-9c4b-21bc0f620217] - Node 8884a65a-1a45-42da-9c4b-21bc0f620217[8884a65a-1a45-42da-9c4b-21bc0f620217] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:16:44.445 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:16:44.446 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:16:44.447 - [任务 287(100)][8884a65a-1a45-42da-9c4b-21bc0f620217] - Node 8884a65a-1a45-42da-9c4b-21bc0f620217[8884a65a-1a45-42da-9c4b-21bc0f620217] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:16:44.456 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:16:44.459 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:16:44.459 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:16:44.735 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:17:32.456 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543404526 
[INFO ] 2024-11-14 08:17:32.460 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543404526 
[INFO ] 2024-11-14 08:17:32.460 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:17:32.460 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:17:32.465 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 47745 ms 
[INFO ] 2024-11-14 08:17:32.466 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:17:32.838 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:17:32.847 - [任务 287(100)][8884a65a-1a45-42da-9c4b-21bc0f620217] - Node 8884a65a-1a45-42da-9c4b-21bc0f620217[8884a65a-1a45-42da-9c4b-21bc0f620217] running status set to false 
[INFO ] 2024-11-14 08:17:32.849 - [任务 287(100)][8884a65a-1a45-42da-9c4b-21bc0f620217] - Node 8884a65a-1a45-42da-9c4b-21bc0f620217[8884a65a-1a45-42da-9c4b-21bc0f620217] schema data cleaned 
[INFO ] 2024-11-14 08:17:32.850 - [任务 287(100)][8884a65a-1a45-42da-9c4b-21bc0f620217] - Node 8884a65a-1a45-42da-9c4b-21bc0f620217[8884a65a-1a45-42da-9c4b-21bc0f620217] monitor closed 
[INFO ] 2024-11-14 08:17:32.851 - [任务 287(100)][8884a65a-1a45-42da-9c4b-21bc0f620217] - Node 8884a65a-1a45-42da-9c4b-21bc0f620217[8884a65a-1a45-42da-9c4b-21bc0f620217] close complete, cost 5 ms 
[INFO ] 2024-11-14 08:17:32.874 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-c2662a59-d301-445d-99ac-cc34ca963a69 
[INFO ] 2024-11-14 08:17:32.875 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-c2662a59-d301-445d-99ac-cc34ca963a69 
[INFO ] 2024-11-14 08:17:32.875 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:17:32.875 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:17:32.875 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:17:32.878 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 48 ms 
[INFO ] 2024-11-14 08:17:32.878 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:17:32.879 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:17:32.879 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:22:38.804 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:22:38.805 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:22:38.806 - [任务 287(100)][a70bef56-435b-4ac6-af5f-01520ba0c5e8] - Node a70bef56-435b-4ac6-af5f-01520ba0c5e8[a70bef56-435b-4ac6-af5f-01520ba0c5e8] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:22:38.808 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:22:38.814 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:22:38.821 - [任务 287(100)][a70bef56-435b-4ac6-af5f-01520ba0c5e8] - Node a70bef56-435b-4ac6-af5f-01520ba0c5e8[a70bef56-435b-4ac6-af5f-01520ba0c5e8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:22:38.821 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:22:39.087 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:22:39.116 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543758890 
[INFO ] 2024-11-14 08:22:39.118 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731543758890 
[INFO ] 2024-11-14 08:22:39.118 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:22:39.118 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:22:39.118 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 44 ms 
[INFO ] 2024-11-14 08:22:39.145 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:22:39.522 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:22:39.532 - [任务 287(100)][a70bef56-435b-4ac6-af5f-01520ba0c5e8] - Node a70bef56-435b-4ac6-af5f-01520ba0c5e8[a70bef56-435b-4ac6-af5f-01520ba0c5e8] running status set to false 
[INFO ] 2024-11-14 08:22:39.539 - [任务 287(100)][a70bef56-435b-4ac6-af5f-01520ba0c5e8] - Node a70bef56-435b-4ac6-af5f-01520ba0c5e8[a70bef56-435b-4ac6-af5f-01520ba0c5e8] schema data cleaned 
[INFO ] 2024-11-14 08:22:39.546 - [任务 287(100)][a70bef56-435b-4ac6-af5f-01520ba0c5e8] - Node a70bef56-435b-4ac6-af5f-01520ba0c5e8[a70bef56-435b-4ac6-af5f-01520ba0c5e8] monitor closed 
[INFO ] 2024-11-14 08:22:39.546 - [任务 287(100)][a70bef56-435b-4ac6-af5f-01520ba0c5e8] - Node a70bef56-435b-4ac6-af5f-01520ba0c5e8[a70bef56-435b-4ac6-af5f-01520ba0c5e8] close complete, cost 24 ms 
[INFO ] 2024-11-14 08:22:39.546 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-2d4fc8be-111a-454e-8c2d-1c4c8fb8ed2c 
[INFO ] 2024-11-14 08:22:39.547 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-2d4fc8be-111a-454e-8c2d-1c4c8fb8ed2c 
[INFO ] 2024-11-14 08:22:39.547 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:22:39.548 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:22:39.549 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:22:39.552 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 32 ms 
[INFO ] 2024-11-14 08:22:39.552 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:22:39.553 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:22:39.554 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:51:54.728 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:51:54.730 - [任务 287(100)][5110cf64-e8bc-42a5-860f-ba3fecbd9484] - Node 5110cf64-e8bc-42a5-860f-ba3fecbd9484[5110cf64-e8bc-42a5-860f-ba3fecbd9484] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:51:54.730 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:51:54.730 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:51:54.730 - [任务 287(100)][5110cf64-e8bc-42a5-860f-ba3fecbd9484] - Node 5110cf64-e8bc-42a5-860f-ba3fecbd9484[5110cf64-e8bc-42a5-860f-ba3fecbd9484] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:51:54.730 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:51:54.735 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:51:55.072 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:51:55.107 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731545514841 
[INFO ] 2024-11-14 08:51:55.107 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731545514841 
[INFO ] 2024-11-14 08:51:55.112 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:51:55.113 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:51:55.130 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 57 ms 
[INFO ] 2024-11-14 08:51:55.131 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:51:55.689 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:51:55.766 - [任务 287(100)][5110cf64-e8bc-42a5-860f-ba3fecbd9484] - Node 5110cf64-e8bc-42a5-860f-ba3fecbd9484[5110cf64-e8bc-42a5-860f-ba3fecbd9484] running status set to false 
[INFO ] 2024-11-14 08:51:55.767 - [任务 287(100)][5110cf64-e8bc-42a5-860f-ba3fecbd9484] - Node 5110cf64-e8bc-42a5-860f-ba3fecbd9484[5110cf64-e8bc-42a5-860f-ba3fecbd9484] schema data cleaned 
[INFO ] 2024-11-14 08:51:55.770 - [任务 287(100)][5110cf64-e8bc-42a5-860f-ba3fecbd9484] - Node 5110cf64-e8bc-42a5-860f-ba3fecbd9484[5110cf64-e8bc-42a5-860f-ba3fecbd9484] monitor closed 
[INFO ] 2024-11-14 08:51:55.772 - [任务 287(100)][5110cf64-e8bc-42a5-860f-ba3fecbd9484] - Node 5110cf64-e8bc-42a5-860f-ba3fecbd9484[5110cf64-e8bc-42a5-860f-ba3fecbd9484] close complete, cost 110 ms 
[INFO ] 2024-11-14 08:51:55.837 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-2c3885ca-c3cc-48a2-b12a-385253e6d080 
[INFO ] 2024-11-14 08:51:55.838 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-2c3885ca-c3cc-48a2-b12a-385253e6d080 
[INFO ] 2024-11-14 08:51:55.839 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:51:55.839 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:51:55.840 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:51:55.840 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 184 ms 
[INFO ] 2024-11-14 08:51:55.846 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:51:55.846 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:51:55.848 - [任务 287(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 08:51:55.969 - [任务 287(100)][cd1e705c-2d2b-4c93-a312-8579e497a2a7] - Node cd1e705c-2d2b-4c93-a312-8579e497a2a7[cd1e705c-2d2b-4c93-a312-8579e497a2a7] start preload schema,table counts: 0 
[INFO ] 2024-11-14 08:51:55.969 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:51:55.969 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] start preload schema,table counts: 1 
[INFO ] 2024-11-14 08:51:55.977 - [任务 287(100)][cd1e705c-2d2b-4c93-a312-8579e497a2a7] - Node cd1e705c-2d2b-4c93-a312-8579e497a2a7[cd1e705c-2d2b-4c93-a312-8579e497a2a7] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:51:55.977 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:51:55.977 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 08:51:55.980 - [任务 287(100)][增强JS] - Node js_processor(增强JS: 8a234799-9af0-4d96-802d-1aa6cf867ecd) enable batch process 
[INFO ] 2024-11-14 08:51:56.222 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] running status set to false 
[INFO ] 2024-11-14 08:51:56.243 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731545516047 
[INFO ] 2024-11-14 08:51:56.245 - [任务 287(100)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode_a681b048-a65a-48fc-8220-0721fcaec985_1731545516047 
[INFO ] 2024-11-14 08:51:56.245 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] schema data cleaned 
[INFO ] 2024-11-14 08:51:56.245 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] monitor closed 
[INFO ] 2024-11-14 08:51:56.248 - [任务 287(100)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[a681b048-a65a-48fc-8220-0721fcaec985] close complete, cost 21 ms 
[INFO ] 2024-11-14 08:51:56.283 - [任务 287(100)][增强JS][src=user_script]  - Init standardized JS engine 
[INFO ] 2024-11-14 08:51:56.677 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] running status set to false 
[INFO ] 2024-11-14 08:51:56.693 - [任务 287(100)][cd1e705c-2d2b-4c93-a312-8579e497a2a7] - Node cd1e705c-2d2b-4c93-a312-8579e497a2a7[cd1e705c-2d2b-4c93-a312-8579e497a2a7] running status set to false 
[INFO ] 2024-11-14 08:51:56.694 - [任务 287(100)][cd1e705c-2d2b-4c93-a312-8579e497a2a7] - Node cd1e705c-2d2b-4c93-a312-8579e497a2a7[cd1e705c-2d2b-4c93-a312-8579e497a2a7] schema data cleaned 
[INFO ] 2024-11-14 08:51:56.694 - [任务 287(100)][cd1e705c-2d2b-4c93-a312-8579e497a2a7] - Node cd1e705c-2d2b-4c93-a312-8579e497a2a7[cd1e705c-2d2b-4c93-a312-8579e497a2a7] monitor closed 
[INFO ] 2024-11-14 08:51:56.705 - [任务 287(100)][cd1e705c-2d2b-4c93-a312-8579e497a2a7] - Node cd1e705c-2d2b-4c93-a312-8579e497a2a7[cd1e705c-2d2b-4c93-a312-8579e497a2a7] close complete, cost 18 ms 
[INFO ] 2024-11-14 08:51:56.705 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-f2e5bd77-0bba-4b8b-b99a-3da7b9ce882a 
[INFO ] 2024-11-14 08:51:56.705 - [任务 287(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-f2e5bd77-0bba-4b8b-b99a-3da7b9ce882a 
[INFO ] 2024-11-14 08:51:56.706 - [任务 287(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67352717bb2de53147e9dec0-8a234799-9af0-4d96-802d-1aa6cf867ecd-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 08:51:56.707 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] schema data cleaned 
[INFO ] 2024-11-14 08:51:56.708 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] monitor closed 
[INFO ] 2024-11-14 08:51:56.710 - [任务 287(100)][增强JS] - Node 增强JS[8a234799-9af0-4d96-802d-1aa6cf867ecd] close complete, cost 40 ms 
[INFO ] 2024-11-14 08:51:56.711 - [任务 287(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 08:51:56.711 - [任务 287(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 08:51:56.921 - [任务 287(100)] - Stopped task aspect(s) 
