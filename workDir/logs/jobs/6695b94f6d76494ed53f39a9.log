[INFO ] 2024-07-26 10:10:27.414 - [Heart<PERSON>-<PERSON><PERSON>] - Start task milestones: 6695b94f6d76494ed53f39a9(Heartbeat-<PERSON><PERSON>) 
[INFO ] 2024-07-26 10:10:28.712 - [Heartbeat-<PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 10:10:28.913 - [Heartbeat-Mon<PERSON>] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 10:10:29.658 - [Heartbeat-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e18627c9-e470-4ca1-a5c3-76fa56e26b26] start preload schema,table counts: 1 
[INFO ] 2024-07-26 10:10:29.659 - [Heartbeat-Mon<PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f3a0781b-4ecd-4679-93d1-53e24a51a879] start preload schema,table counts: 1 
[INFO ] 2024-07-26 10:10:29.659 - [<PERSON><PERSON>-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e18627c9-e470-4ca1-a5c3-76fa56e26b26] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 10:10:29.659 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f3a0781b-4ecd-4679-93d1-53e24a51a879] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 10:10:30.217 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 10:10:30.220 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 10:10:30.241 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-26 10:10:30.438 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721088337325,"lastTimes":1721907111620,"lastTN":2258,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":201878,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 10:10:30.446 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721088337325,"lastTimes":1721907111620,"lastTN":2258,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":201878,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 10:10:30.467 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 10:10:30.471 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 10:10:30.765 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-26 18:10:34.018 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f3a0781b-4ecd-4679-93d1-53e24a51a879] running status set to false 
[INFO ] 2024-07-26 18:10:34.018 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:10:34.020 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-f3a0781b-4ecd-4679-93d1-53e24a51a879 
[INFO ] 2024-07-26 18:10:34.021 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-f3a0781b-4ecd-4679-93d1-53e24a51a879 
[INFO ] 2024-07-26 18:10:34.021 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f3a0781b-4ecd-4679-93d1-53e24a51a879] schema data cleaned 
[INFO ] 2024-07-26 18:10:34.021 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f3a0781b-4ecd-4679-93d1-53e24a51a879] monitor closed 
[INFO ] 2024-07-26 18:10:34.022 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f3a0781b-4ecd-4679-93d1-53e24a51a879] close complete, cost 6 ms 
[INFO ] 2024-07-26 18:10:34.039 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e18627c9-e470-4ca1-a5c3-76fa56e26b26] running status set to false 
[INFO ] 2024-07-26 18:10:34.039 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-e18627c9-e470-4ca1-a5c3-76fa56e26b26 
[INFO ] 2024-07-26 18:10:34.039 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-e18627c9-e470-4ca1-a5c3-76fa56e26b26 
[INFO ] 2024-07-26 18:10:34.039 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e18627c9-e470-4ca1-a5c3-76fa56e26b26] schema data cleaned 
[INFO ] 2024-07-26 18:10:34.039 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e18627c9-e470-4ca1-a5c3-76fa56e26b26] monitor closed 
[INFO ] 2024-07-26 18:10:34.243 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e18627c9-e470-4ca1-a5c3-76fa56e26b26] close complete, cost 17 ms 
[INFO ] 2024-07-26 18:10:36.502 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:10:36.507 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@12428edf 
[INFO ] 2024-07-26 18:10:36.507 - [Heartbeat-Mongo] - Stop task milestones: 6695b94f6d76494ed53f39a9(Heartbeat-Mongo)  
[INFO ] 2024-07-26 18:10:36.630 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:10:36.630 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:10:36.655 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[6695b94f6d76494ed53f39a9] 
[INFO ] 2024-07-26 18:10:36.657 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[6695b94f6d76494ed53f39a9] 
