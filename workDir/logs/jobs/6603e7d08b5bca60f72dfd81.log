[INFO ] 2024-03-27 17:33:58.186 - [任务 26] - Task initialization... 
[INFO ] 2024-03-27 17:33:58.188 - [任务 26] - Start task milestones: 6603e7d08b5bca60f72dfd81(任务 26) 
[INFO ] 2024-03-27 17:33:58.266 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 17:33:58.339 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 17:33:58.408 - [任务 26][CLAIM] - Node CLAIM[09672dd8-81df-46cd-83f6-c91771b8f3d1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 17:33:58.409 - [任务 26][test4] - Node test4[da688155-1872-4ac2-86c7-b97dfcbc2a99] start preload schema,table counts: 1 
[INFO ] 2024-03-27 17:33:58.450 - [任务 26][CLAIM] - Node CLAIM[09672dd8-81df-46cd-83f6-c91771b8f3d1] preload schema finished, cost 37 ms 
[INFO ] 2024-03-27 17:33:58.450 - [任务 26][test4] - Node test4[da688155-1872-4ac2-86c7-b97dfcbc2a99] preload schema finished, cost 42 ms 
[INFO ] 2024-03-27 17:33:59.218 - [任务 26][test4] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 17:33:59.370 - [任务 26][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 17:33:59.375 - [任务 26][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 17:33:59.375 - [任务 26][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 17:33:59.402 - [任务 26][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145769777,"gtidSet":""} 
[INFO ] 2024-03-27 17:33:59.406 - [任务 26][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-03-27 17:33:59.452 - [任务 26][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 17:33:59.452 - [任务 26][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 17:33:59.458 - [任务 26][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 17:33:59.518 - [任务 26][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 17:33:59.576 - [任务 26][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 17:33:59.582 - [任务 26][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 17:33:59.582 - [任务 26][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 17:33:59.586 - [任务 26][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145769777,"gtidSet":""} 
[INFO ] 2024-03-27 17:33:59.654 - [任务 26][CLAIM] - Starting mysql cdc, server name: fa102a2b-9466-427c-b5c8-0814b83c5f08 
[INFO ] 2024-03-27 17:33:59.709 - [任务 26][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2126838306
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fa102a2b-9466-427c-b5c8-0814b83c5f08
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fa102a2b-9466-427c-b5c8-0814b83c5f08
  database.hostname: 127.0.0.1
  database.password: ********
  name: fa102a2b-9466-427c-b5c8-0814b83c5f08
  pdk.offset.string: {"name":"fa102a2b-9466-427c-b5c8-0814b83c5f08","offset":{"{\"server\":\"fa102a2b-9466-427c-b5c8-0814b83c5f08\"}":"{\"file\":\"binlog.000020\",\"pos\":145769777,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 17:33:59.941 - [任务 26][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 17:34:29.495 - [任务 26][CLAIM] - Read DDL: alter table CLAIM drop  column `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 17:37:54.282 - [任务 26][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapDropFieldEvent
  - Offset: MysqlStreamOffset{name='fa102a2b-9466-427c-b5c8-0814b83c5f08', offset={{"server":"fa102a2b-9466-427c-b5c8-0814b83c5f08"}={"ts_sec":1711532069,"file":"binlog.000020","pos":145770025,"server_id":1}}} 
[INFO ] 2024-03-27 17:37:54.512 - [任务 26][CLAIM] - Source node received an ddl event: TapDropFieldEvent{tableId='CLAIM', fieldName='name1'} 
[INFO ] 2024-03-27 17:41:25.636 - [任务 26][CLAIM] - Read DDL: alter table CLAIM drop  column `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 17:41:25.640 - [任务 26][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapDropFieldEvent
  - Offset: MysqlStreamOffset{name='fa102a2b-9466-427c-b5c8-0814b83c5f08', offset={{"server":"fa102a2b-9466-427c-b5c8-0814b83c5f08"}={"ts_sec":1711532069,"file":"binlog.000020","pos":145770025,"server_id":1}}} 
[INFO ] 2024-03-27 17:41:25.640 - [任务 26][CLAIM] - Source node received an ddl event: TapDropFieldEvent{tableId='CLAIM', fieldName='name1'} 
[INFO ] 2024-03-27 17:48:15.479 - [任务 26][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 17:48:15.558 - [任务 26][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 17:48:15.573 - [任务 26][CLAIM] - Incremental sync completed 
[ERROR] 2024-03-27 17:48:15.596 - [任务 26][CLAIM] - java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms <-- Error Message -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms

<-- Simple Stack Trace -->
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	java.util.concurrent.FutureTask.run(FutureTask.java)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	... 6 more

[INFO ] 2024-03-27 17:48:15.600 - [任务 26][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-27 17:48:15.863 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-27 17:48:15.864 - [任务 26][CLAIM] - Node CLAIM[09672dd8-81df-46cd-83f6-c91771b8f3d1] running status set to false 
[INFO ] 2024-03-27 17:48:15.864 - [任务 26][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-09672dd8-81df-46cd-83f6-c91771b8f3d1 
[INFO ] 2024-03-27 17:48:15.864 - [任务 26][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-09672dd8-81df-46cd-83f6-c91771b8f3d1 
[INFO ] 2024-03-27 17:48:15.864 - [任务 26][CLAIM] - Node CLAIM[09672dd8-81df-46cd-83f6-c91771b8f3d1] schema data cleaned 
[INFO ] 2024-03-27 17:48:15.864 - [任务 26][CLAIM] - Node CLAIM[09672dd8-81df-46cd-83f6-c91771b8f3d1] monitor closed 
[INFO ] 2024-03-27 17:48:15.864 - [任务 26][CLAIM] - Node CLAIM[09672dd8-81df-46cd-83f6-c91771b8f3d1] close complete, cost 202244 ms 
[INFO ] 2024-03-27 17:48:15.864 - [任务 26][test4] - Node test4[da688155-1872-4ac2-86c7-b97dfcbc2a99] running status set to false 
[WARN ] 2024-03-27 17:48:15.865 - [任务 26][test4] - Save to snapshot failed, collection: Task/syncProgress/6603e7d08b5bca60f72dfd81, object: {09672dd8-81df-46cd-83f6-c91771b8f3d1,da688155-1872-4ac2-86c7-b97dfcbc2a99=SyncProgress{eventSerialNo=15, syncStage='CDC', batchOffset='{CLAIM={}}', streamOffset='MysqlStreamOffset{name='fa102a2b-9466-427c-b5c8-0814b83c5f08', offset={{"server":"fa102a2b-9466-427c-b5c8-0814b83c5f08"}={"ts_sec":1711532069,"file":"binlog.000020","pos":145770025,"server_id":1}}}'}}, errors: Failed to call rest api, msg no exception. 
[INFO ] 2024-03-27 17:48:18.775 - [任务 26][test4] - PDK connector node stopped: HazelcastTargetPdkDataNode-da688155-1872-4ac2-86c7-b97dfcbc2a99 
[INFO ] 2024-03-27 17:48:18.777 - [任务 26][test4] - PDK connector node released: HazelcastTargetPdkDataNode-da688155-1872-4ac2-86c7-b97dfcbc2a99 
[INFO ] 2024-03-27 17:48:18.777 - [任务 26][test4] - Node test4[da688155-1872-4ac2-86c7-b97dfcbc2a99] schema data cleaned 
[INFO ] 2024-03-27 17:48:18.777 - [任务 26][test4] - Node test4[da688155-1872-4ac2-86c7-b97dfcbc2a99] monitor closed 
[INFO ] 2024-03-27 17:48:18.778 - [任务 26][test4] - Node test4[da688155-1872-4ac2-86c7-b97dfcbc2a99] close complete, cost 3120 ms 
[INFO ] 2024-03-27 17:48:20.626 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-27 17:48:20.633 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 17:48:20.650 - [任务 26] - Stop task milestones: 6603e7d08b5bca60f72dfd81(任务 26)  
[INFO ] 2024-03-27 17:48:20.659 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-03-27 17:48:20.660 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 17:48:20.673 - [任务 26] - Remove memory task client succeed, task: 任务 26[6603e7d08b5bca60f72dfd81] 
[INFO ] 2024-03-27 17:48:20.676 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6603e7d08b5bca60f72dfd81] 
