[INFO ] 2024-07-15 11:43:55.525 - [来自TestMysql的共享挖掘任务] - Start task milestones: 66949afb1df4b966216a4a93(来自TestMysql的共享挖掘任务) 
[INFO ] 2024-07-15 11:43:55.686 - [来自TestMysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 11:43:55.688 - [来自TestMysql的共享挖掘任务] - The engine receives 来自TestMysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 11:43:55.762 - [来自TestMysql的共享挖掘任务][TestMysql] - Node TestMysql[1ed70c3586f847379a85c074de0d1e4e] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:43:55.763 - [来自TestMysql的共享挖掘任务][TestMysql] - Node TestMysql[1ed70c3586f847379a85c074de0d1e4e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:43:55.783 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 11:43:55.783 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 11:43:55.814 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 11:43:55.987 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: -1 
[INFO ] 2024-07-15 11:43:55.987 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 11:43:57.322 - [来自TestMysql的共享挖掘任务][TestMysql] - Source node "TestMysql" read batch size: 2000 
[INFO ] 2024-07-15 11:43:57.322 - [来自TestMysql的共享挖掘任务][TestMysql] - Source node "TestMysql" event queue capacity: 4000 
[INFO ] 2024-07-15 11:43:57.322 - [来自TestMysql的共享挖掘任务][TestMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 11:43:57.329 - [来自TestMysql的共享挖掘任务][TestMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":111411645,"gtidSet":""} 
[INFO ] 2024-07-15 11:43:57.329 - [来自TestMysql的共享挖掘任务][TestMysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-15 11:43:57.380 - [来自TestMysql的共享挖掘任务][TestMysql] - Starting stream read, table list: [POLICY], offset: {"filename":"binlog.000032","position":111411645,"gtidSet":""} 
[INFO ] 2024-07-15 11:43:57.438 - [来自TestMysql的共享挖掘任务][TestMysql] - Starting mysql cdc, server name: 2b4a9927-e5d1-4b2a-8071-bfa11bafc8fa 
[INFO ] 2024-07-15 11:43:57.498 - [来自TestMysql的共享挖掘任务][TestMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1655285128
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2b4a9927-e5d1-4b2a-8071-bfa11bafc8fa
  database.port: 3306
  threadName: Debezium-Mysql-Connector-2b4a9927-e5d1-4b2a-8071-bfa11bafc8fa
  database.hostname: localhost
  database.password: ********
  name: 2b4a9927-e5d1-4b2a-8071-bfa11bafc8fa
  pdk.offset.string: {"name":"2b4a9927-e5d1-4b2a-8071-bfa11bafc8fa","offset":{"{\"server\":\"2b4a9927-e5d1-4b2a-8071-bfa11bafc8fa\"}":"{\"file\":\"binlog.000032\",\"pos\":111411645,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-15 11:43:57.915 - [来自TestMysql的共享挖掘任务][TestMysql] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 12:36:16.724 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 13:36:16.720 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 14:00:00.374 - [来自TestMysql的共享挖掘任务][TestMysql] - Node TestMysql[1ed70c3586f847379a85c074de0d1e4e] running status set to false 
[INFO ] 2024-07-15 14:00:00.397 - [来自TestMysql的共享挖掘任务][TestMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-15 14:00:00.397 - [来自TestMysql的共享挖掘任务][TestMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-15 14:00:00.411 - [来自TestMysql的共享挖掘任务][TestMysql] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-1ed70c3586f847379a85c074de0d1e4e 
[INFO ] 2024-07-15 14:00:00.411 - [来自TestMysql的共享挖掘任务][TestMysql] - PDK connector node released: HazelcastSourcePdkShareCDCNode-1ed70c3586f847379a85c074de0d1e4e 
[INFO ] 2024-07-15 14:00:00.412 - [来自TestMysql的共享挖掘任务][TestMysql] - Node TestMysql[1ed70c3586f847379a85c074de0d1e4e] schema data cleaned 
[INFO ] 2024-07-15 14:00:00.412 - [来自TestMysql的共享挖掘任务][TestMysql] - Node TestMysql[1ed70c3586f847379a85c074de0d1e4e] monitor closed 
[INFO ] 2024-07-15 14:00:00.414 - [来自TestMysql的共享挖掘任务][TestMysql] - Node TestMysql[1ed70c3586f847379a85c074de0d1e4e] close complete, cost 144 ms 
[INFO ] 2024-07-15 14:00:00.414 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c7df6aa9a1b14e1c9611a05003c8691b] running status set to false 
[INFO ] 2024-07-15 14:00:00.417 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 14:00:00.417 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 14:00:00.417 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c7df6aa9a1b14e1c9611a05003c8691b] schema data cleaned 
[INFO ] 2024-07-15 14:00:00.417 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c7df6aa9a1b14e1c9611a05003c8691b] monitor closed 
[INFO ] 2024-07-15 14:00:00.417 - [来自TestMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[c7df6aa9a1b14e1c9611a05003c8691b] close complete, cost 3 ms 
[INFO ] 2024-07-15 14:00:00.838 - [来自TestMysql的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 14:00:00.961 - [来自TestMysql的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72a2e9a6 
[INFO ] 2024-07-15 14:00:00.962 - [来自TestMysql的共享挖掘任务] - Stop task milestones: 66949afb1df4b966216a4a93(来自TestMysql的共享挖掘任务)  
[INFO ] 2024-07-15 14:00:00.984 - [来自TestMysql的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:00:00.985 - [来自TestMysql的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 14:00:01.026 - [来自TestMysql的共享挖掘任务] - Remove memory task client succeed, task: 来自TestMysql的共享挖掘任务[66949afb1df4b966216a4a93] 
[INFO ] 2024-07-15 14:00:01.026 - [来自TestMysql的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自TestMysql的共享挖掘任务[66949afb1df4b966216a4a93] 
