[INFO ] 2024-07-09 17:59:00.384 - [任务 56] - Task initialization... 
[INFO ] 2024-07-09 17:59:00.386 - [任务 56] - Start task milestones: 668d08b947ed812566108c31(任务 56) 
[INFO ] 2024-07-09 17:59:00.652 - [任务 56] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-09 17:59:00.823 - [任务 56] - The engine receives 任务 56 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-09 17:59:00.823 - [任务 56][dummy_test] - Node dummy_test[c3f81d5c-4b42-49c6-85ab-61758b7930a9] start preload schema,table counts: 1 
[INFO ] 2024-07-09 17:59:00.823 - [任务 56][AAA1] - Node AAA1[4ee7401e-d8b8-4563-b9bf-7bd24dcada98] start preload schema,table counts: 1 
[INFO ] 2024-07-09 17:59:00.824 - [任务 56][AAA1] - Node AAA1[4ee7401e-d8b8-4563-b9bf-7bd24dcada98] preload schema finished, cost 1 ms 
[INFO ] 2024-07-09 17:59:00.824 - [任务 56][dummy_test] - Node dummy_test[c3f81d5c-4b42-49c6-85ab-61758b7930a9] preload schema finished, cost 1 ms 
[INFO ] 2024-07-09 17:59:01.634 - [任务 56][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-09 17:59:02.200 - [任务 56][AAA1] - Source node "AAA1" read batch size: 100 
[INFO ] 2024-07-09 17:59:02.200 - [任务 56][AAA1] - Source node "AAA1" event queue capacity: 200 
[INFO ] 2024-07-09 17:59:02.200 - [任务 56][AAA1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-09 17:59:03.089 - [任务 56][AAA1] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":725183222,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-09 17:59:03.093 - [任务 56][AAA1] - Initial sync started 
[INFO ] 2024-07-09 17:59:03.106 - [任务 56][AAA1] - Starting batch read, table name: AAA1, offset: null 
[INFO ] 2024-07-09 17:59:03.106 - [任务 56][AAA1] - Table AAA1 is going to be initial synced 
[INFO ] 2024-07-09 17:59:04.143 - [任务 56][AAA1] - Query table 'AAA1' counts: 517609 
[INFO ] 2024-07-09 18:00:31.827 - [任务 56][AAA1] - Table [AAA1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-09 18:00:31.830 - [任务 56][AAA1] - Initial sync completed 
[INFO ] 2024-07-09 18:00:31.830 - [任务 56][AAA1] - Incremental sync starting... 
[INFO ] 2024-07-09 18:00:31.830 - [任务 56][AAA1] - Initial sync completed 
[INFO ] 2024-07-09 18:00:31.831 - [任务 56][AAA1] - Starting stream read, table list: [AAA1], offset: {"sortString":null,"offsetValue":null,"lastScn":725183222,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-09 18:00:33.054 - [任务 56][AAA1] - total start mining scn: 725183222 
[INFO ] 2024-07-09 18:00:34.687 - [任务 56][AAA1] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/data/oracle/app/oracle/oradata/tap11g132/redo05.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-09 18:14:59.194 - [任务 56][AAA1] - Node AAA1[4ee7401e-d8b8-4563-b9bf-7bd24dcada98] running status set to false 
[INFO ] 2024-07-09 18:14:59.194 - [任务 56][AAA1] - Log Miner is shutting down... 
[INFO ] 2024-07-09 18:14:59.207 - [任务 56][AAA1] - Log Miner has been closed! 
[INFO ] 2024-07-09 18:14:59.208 - [任务 56][AAA1] - Incremental sync completed 
[INFO ] 2024-07-09 18:14:59.208 - [任务 56][AAA1] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-07-09 18:14:59.354 - [任务 56][AAA1] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:414)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-07-09 18:14:59.354 - [任务 56][AAA1] - PDK connector node stopped: HazelcastSourcePdkDataNode-4ee7401e-d8b8-4563-b9bf-7bd24dcada98 
[INFO ] 2024-07-09 18:14:59.356 - [任务 56][AAA1] - PDK connector node released: HazelcastSourcePdkDataNode-4ee7401e-d8b8-4563-b9bf-7bd24dcada98 
[INFO ] 2024-07-09 18:14:59.356 - [任务 56][AAA1] - Node AAA1[4ee7401e-d8b8-4563-b9bf-7bd24dcada98] schema data cleaned 
[INFO ] 2024-07-09 18:14:59.359 - [任务 56][AAA1] - Node AAA1[4ee7401e-d8b8-4563-b9bf-7bd24dcada98] monitor closed 
[INFO ] 2024-07-09 18:14:59.361 - [任务 56][AAA1] - Node AAA1[4ee7401e-d8b8-4563-b9bf-7bd24dcada98] close complete, cost 179 ms 
[INFO ] 2024-07-09 18:14:59.361 - [任务 56][dummy_test] - Node dummy_test[c3f81d5c-4b42-49c6-85ab-61758b7930a9] running status set to false 
[INFO ] 2024-07-09 18:14:59.368 - [任务 56][dummy_test] - Stop connector: first 1720519143670 8ms, last 1720519231802 502ms, counts: 517609/88634ms, min: 0, max: 507, QPS: 5881/s 
[INFO ] 2024-07-09 18:14:59.375 - [任务 56][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-c3f81d5c-4b42-49c6-85ab-61758b7930a9 
[INFO ] 2024-07-09 18:14:59.375 - [任务 56][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-c3f81d5c-4b42-49c6-85ab-61758b7930a9 
[INFO ] 2024-07-09 18:14:59.376 - [任务 56][dummy_test] - Node dummy_test[c3f81d5c-4b42-49c6-85ab-61758b7930a9] schema data cleaned 
[INFO ] 2024-07-09 18:14:59.376 - [任务 56][dummy_test] - Node dummy_test[c3f81d5c-4b42-49c6-85ab-61758b7930a9] monitor closed 
[INFO ] 2024-07-09 18:14:59.581 - [任务 56][dummy_test] - Node dummy_test[c3f81d5c-4b42-49c6-85ab-61758b7930a9] close complete, cost 18 ms 
[INFO ] 2024-07-09 18:14:59.706 - [任务 56] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-09 18:14:59.706 - [任务 56] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1b695d6a 
[INFO ] 2024-07-09 18:14:59.830 - [任务 56] - Stop task milestones: 668d08b947ed812566108c31(任务 56)  
[INFO ] 2024-07-09 18:14:59.831 - [任务 56] - Stopped task aspect(s) 
[INFO ] 2024-07-09 18:14:59.831 - [任务 56] - Snapshot order controller have been removed 
[INFO ] 2024-07-09 18:14:59.863 - [任务 56] - Remove memory task client succeed, task: 任务 56[668d08b947ed812566108c31] 
[INFO ] 2024-07-09 18:14:59.864 - [任务 56] - Destroy memory task client cache succeed, task: 任务 56[668d08b947ed812566108c31] 
