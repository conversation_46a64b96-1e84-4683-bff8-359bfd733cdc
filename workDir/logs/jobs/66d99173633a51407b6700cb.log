[INFO ] 2024-09-09 00:44:53.146 - [测试主从] - Start task milestones: 66d99173633a51407b6700cb(测试主从) 
[INFO ] 2024-09-09 00:44:53.166 - [测试主从] - Task initialization... 
[INFO ] 2024-09-09 00:44:53.280 - [测试主从] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-09 00:44:53.280 - [测试主从] - The engine receives 测试主从 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-09 00:44:53.322 - [测试主从][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] start preload schema,table counts: 1 
[INFO ] 2024-09-09 00:44:53.322 - [测试主从][Ka<PERSON>ka] - <PERSON><PERSON>[c3fe3f73-e104-4c52-8e74-d54fe608b693] start preload schema,table counts: 1 
[INFO ] 2024-09-09 00:44:53.322 - [测试主从][Kafka] - <PERSON><PERSON>[c3fe3f73-e104-4c52-8e74-d54fe608b693] preload schema finished, cost 0 ms 
[INFO ] 2024-09-09 00:44:53.322 - [测试主从][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] preload schema finished, cost 1 ms 
[INFO ] 2024-09-09 00:44:54.226 - [测试主从][Kafka] - Node(Kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-09 00:44:54.232 - [测试主从][Kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-09 00:44:54.323 - [测试主从][Kafka] - The table t1 has already exist. 
[WARN ] 2024-09-09 00:44:55.751 - [测试主从][Mysql] - The time of each node is inconsistent, please check nodes: {hostPort=*************:23306, time=1725813894903} and {hostPort=*************:33306, time=1725785094743} 
[INFO ] 2024-09-09 00:44:55.760 - [测试主从][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-09-09 00:44:55.761 - [测试主从][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-09-09 00:44:55.762 - [测试主从][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-09 00:44:56.036 - [测试主从][Mysql] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000079","position":47063763,"gtidSet":""} 
[INFO ] 2024-09-09 00:44:56.096 - [测试主从][Mysql] - Initial sync started 
[INFO ] 2024-09-09 00:44:56.096 - [测试主从][Mysql] - Starting batch read, table name: t1, offset: null 
[INFO ] 2024-09-09 00:44:56.277 - [测试主从][Mysql] - Table t1 is going to be initial synced 
[INFO ] 2024-09-09 00:44:56.277 - [测试主从][Mysql] - Table [t1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-09 00:44:56.331 - [测试主从][Mysql] - Query table 't1' counts: 1101 
[INFO ] 2024-09-09 00:44:56.332 - [测试主从][Mysql] - Initial sync completed 
[INFO ] 2024-09-09 00:44:56.332 - [测试主从][Mysql] - Incremental sync starting... 
[INFO ] 2024-09-09 00:44:56.332 - [测试主从][Mysql] - Initial sync completed 
[INFO ] 2024-09-09 00:44:56.332 - [测试主从][Mysql] - Starting stream read, table list: [t1], offset: {"filename":"mysql-bin.000079","position":47063763,"gtidSet":""} 
[INFO ] 2024-09-09 00:44:56.642 - [测试主从][Mysql] - Starting mysql cdc, server name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42 
[INFO ] 2024-09-09 00:44:56.848 - [测试主从][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 642085319
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.port: 23306
  threadName: Debezium-Mysql-Connector-3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.hostname: *************
  database.password: ********
  name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  pdk.offset.string: {"name":"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42","offset":{"{\"server\":\"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42\"}":"{\"file\":\"mysql-bin.000079\",\"pos\":47063763,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 00:44:56.876 - [测试主从][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 00:47:06.897 - [测试主从][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 00:47:06.900 - [测试主从][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.io.EOFException: Failed to read next byte from position 2985982
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 00:48:35.410 - [测试主从][Mysql] - Starting mysql cdc, server name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42 
[INFO ] 2024-09-09 00:48:35.416 - [测试主从][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1030176616
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.port: 23306
  threadName: Debezium-Mysql-Connector-3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.hostname: *************
  database.password: ********
  name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  pdk.offset.string: {"name":"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42","offset":{"{\"server\":\"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42\"}":"{\"file\":\"mysql-bin.000079\",\"pos\":50040937,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 00:48:35.742 - [测试主从][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 00:48:35.742 - [测试主从][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 00:48:45.025 - [测试主从][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 00:48:45.227 - [测试主从][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:108. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 00:49:51.393 - [测试主从][Mysql] - Starting mysql cdc, server name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42 
[INFO ] 2024-09-09 00:49:51.597 - [测试主从][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 863895819
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.port: 23306
  threadName: Debezium-Mysql-Connector-3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.hostname: *************
  database.password: ********
  name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  pdk.offset.string: {"name":"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42","offset":{"{\"server\":\"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42\"}":"{\"file\":\"mysql-bin.000081\",\"pos\":9672430,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 00:49:51.614 - [测试主从][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 00:49:51.820 - [测试主从][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 00:49:52.162 - [测试主从][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 00:49:52.368 - [测试主从][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:86. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 00:50:54.886 - [测试主从][Mysql] - Starting mysql cdc, server name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42 
[INFO ] 2024-09-09 00:50:55.089 - [测试主从][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1187871063
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.port: 23306
  threadName: Debezium-Mysql-Connector-3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  database.hostname: *************
  database.password: ********
  name: 3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42
  pdk.offset.string: {"name":"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42","offset":{"{\"server\":\"3c42d9d7-d0ce-428e-a0f2-0ea5339f2a42\"}":"{\"file\":\"mysql-bin.000081\",\"pos\":9672430,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 00:50:55.149 - [测试主从][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 00:50:55.352 - [测试主从][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 00:50:55.690 - [测试主从][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 00:50:55.891 - [测试主从][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:86. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 00:51:28.424 - [测试主从][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] running status set to false 
[WARN ] 2024-09-09 00:51:28.425 - [测试主从][Mysql] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-aa66232d-6b51-44da-a5ac-be49df413f39 
[INFO ] 2024-09-09 00:51:28.429 - [测试主从][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-aa66232d-6b51-44da-a5ac-be49df413f39 
[INFO ] 2024-09-09 00:51:28.429 - [测试主从][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] schema data cleaned 
[INFO ] 2024-09-09 00:51:28.430 - [测试主从][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] monitor closed 
[INFO ] 2024-09-09 00:51:28.437 - [测试主从][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] close complete, cost 27 ms 
[INFO ] 2024-09-09 00:51:28.437 - [测试主从][Kafka] - Node Kafka[c3fe3f73-e104-4c52-8e74-d54fe608b693] running status set to false 
[INFO ] 2024-09-09 02:28:25.873 - [测试主从-kafka] - Start task milestones: 66d99173633a51407b6700cb(测试主从-kafka) 
[INFO ] 2024-09-09 02:28:26.087 - [测试主从-kafka] - Task initialization... 
[INFO ] 2024-09-09 02:28:26.278 - [测试主从-kafka] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-09 02:28:26.280 - [测试主从-kafka] - The engine receives 测试主从-kafka task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-09 02:28:26.346 - [测试主从-kafka][Kafka] - Node Kafka[c3fe3f73-e104-4c52-8e74-d54fe608b693] start preload schema,table counts: 1 
[INFO ] 2024-09-09 02:28:26.348 - [测试主从-kafka][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] start preload schema,table counts: 1 
[INFO ] 2024-09-09 02:28:26.348 - [测试主从-kafka][Kafka] - Node Kafka[c3fe3f73-e104-4c52-8e74-d54fe608b693] preload schema finished, cost 0 ms 
[INFO ] 2024-09-09 02:28:26.348 - [测试主从-kafka][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] preload schema finished, cost 0 ms 
[INFO ] 2024-09-09 02:28:27.639 - [测试主从-kafka][Kafka] - Node(Kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-09 02:28:27.640 - [测试主从-kafka][Kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-09 02:28:27.804 - [测试主从-kafka][Kafka] - The table t1 has already exist. 
[WARN ] 2024-09-09 02:28:28.412 - [测试主从-kafka][Mysql] - The time of each node is inconsistent, please check nodes: {hostPort=*************:23306, time=1725820107906} and {hostPort=*************:33306, time=1725791307825} 
[INFO ] 2024-09-09 02:28:28.473 - [测试主从-kafka][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-09-09 02:28:28.474 - [测试主从-kafka][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-09-09 02:28:28.475 - [测试主从-kafka][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-09 02:28:28.677 - [测试主从-kafka][Mysql] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000081","position":22175661,"gtidSet":""} 
[INFO ] 2024-09-09 02:28:28.736 - [测试主从-kafka][Mysql] - Initial sync started 
[INFO ] 2024-09-09 02:28:28.741 - [测试主从-kafka][Mysql] - Starting batch read, table name: t1, offset: null 
[INFO ] 2024-09-09 02:28:28.916 - [测试主从-kafka][Mysql] - Table t1 is going to be initial synced 
[INFO ] 2024-09-09 02:28:28.917 - [测试主从-kafka][Mysql] - Table [t1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-09 02:28:28.998 - [测试主从-kafka][Mysql] - Query table 't1' counts: 0 
[INFO ] 2024-09-09 02:28:28.999 - [测试主从-kafka][Mysql] - Initial sync completed 
[INFO ] 2024-09-09 02:28:29.000 - [测试主从-kafka][Mysql] - Incremental sync starting... 
[INFO ] 2024-09-09 02:28:29.001 - [测试主从-kafka][Mysql] - Initial sync completed 
[INFO ] 2024-09-09 02:28:29.001 - [测试主从-kafka][Mysql] - Starting stream read, table list: [t1], offset: {"filename":"mysql-bin.000081","position":22175661,"gtidSet":""} 
[INFO ] 2024-09-09 02:28:29.260 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:28:29.463 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1211621675
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000081\",\"pos\":22175661,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:28:29.571 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:31:23.623 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:31:23.815 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.io.EOFException: Failed to read next byte from position 2501682
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:32:32.802 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:32:33.007 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 53062140
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000081\",\"pos\":24669385,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:32:33.062 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:32:33.269 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:32:33.583 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:32:33.788 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:86. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:33:39.159 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:33:39.360 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 485833827
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000081\",\"pos\":24669385,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:33:39.449 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:33:39.654 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:33:49.083 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:33:49.083 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:86. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:34:52.203 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:34:52.409 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 125971402
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000083\",\"pos\":9672430,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:34:52.441 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:34:52.642 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:34:52.960 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:34:53.163 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:83. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:35:54.798 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:35:54.801 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 199444191
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000083\",\"pos\":9672430,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:35:55.110 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:35:55.111 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:35:55.663 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:35:55.663 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:83. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:36:58.033 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:36:58.035 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 330113182
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000083\",\"pos\":9672430,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:36:58.236 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:36:58.242 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:36:58.834 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:36:58.834 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:83. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:38:01.979 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:38:02.181 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 155053753
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000083\",\"pos\":9672430,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:38:02.221 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:38:02.222 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:38:02.721 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:38:02.721 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:83. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:39:05.802 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:39:05.805 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1346681096
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000083\",\"pos\":9672430,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:39:06.133 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:39:06.135 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:42:10.913 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:42:10.913 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:83. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:43:13.739 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:43:13.741 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1030157462
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000083\",\"pos\":14280170,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:43:14.055 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:43:14.059 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 02:43:21.004 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-09 02:43:21.205 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:83. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-09 02:44:22.103 - [测试主从-kafka][Mysql] - Starting mysql cdc, server name: ab9a3a45-19db-4c75-a626-aae81980d2b6 
[INFO ] 2024-09-09 02:44:22.305 - [测试主从-kafka][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 898328023
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.port: 23306
  threadName: Debezium-Mysql-Connector-ab9a3a45-19db-4c75-a626-aae81980d2b6
  database.hostname: *************
  database.password: ********
  name: ab9a3a45-19db-4c75-a626-aae81980d2b6
  pdk.offset.string: {"name":"ab9a3a45-19db-4c75-a626-aae81980d2b6","offset":{"{\"server\":\"ab9a3a45-19db-4c75-a626-aae81980d2b6\"}":"{\"file\":\"mysql-bin.000085\",\"pos\":4,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-09 02:44:22.330 - [测试主从-kafka][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-09 02:44:22.533 - [测试主从-kafka][Mysql] - Connector Mysql incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2024-09-09 03:00:00.348 - [测试主从-kafka] - Stop task milestones: 66d99173633a51407b6700cb(测试主从-kafka)  
[INFO ] 2024-09-09 03:00:00.448 - [测试主从-kafka][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] running status set to false 
[INFO ] 2024-09-09 03:00:00.448 - [测试主从-kafka][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-09 03:00:00.449 - [测试主从-kafka][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-09-09 03:00:00.584 - [测试主从-kafka][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-aa66232d-6b51-44da-a5ac-be49df413f39 
[INFO ] 2024-09-09 03:00:00.588 - [测试主从-kafka][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-aa66232d-6b51-44da-a5ac-be49df413f39 
[INFO ] 2024-09-09 03:00:00.588 - [测试主从-kafka][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] schema data cleaned 
[INFO ] 2024-09-09 03:00:00.594 - [测试主从-kafka][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] monitor closed 
[INFO ] 2024-09-09 03:00:00.594 - [测试主从-kafka][Mysql] - Node Mysql[aa66232d-6b51-44da-a5ac-be49df413f39] close complete, cost 219 ms 
[INFO ] 2024-09-09 03:00:00.619 - [测试主从-kafka][Kafka] - Node Kafka[c3fe3f73-e104-4c52-8e74-d54fe608b693] running status set to false 
[INFO ] 2024-09-09 03:00:00.620 - [测试主从-kafka][Kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode-c3fe3f73-e104-4c52-8e74-d54fe608b693 
[INFO ] 2024-09-09 03:00:00.620 - [测试主从-kafka][Kafka] - PDK connector node released: HazelcastTargetPdkDataNode-c3fe3f73-e104-4c52-8e74-d54fe608b693 
[INFO ] 2024-09-09 03:00:00.620 - [测试主从-kafka][Kafka] - Node Kafka[c3fe3f73-e104-4c52-8e74-d54fe608b693] schema data cleaned 
[INFO ] 2024-09-09 03:00:00.622 - [测试主从-kafka][Kafka] - Node Kafka[c3fe3f73-e104-4c52-8e74-d54fe608b693] monitor closed 
[INFO ] 2024-09-09 03:00:00.622 - [测试主从-kafka][Kafka] - Node Kafka[c3fe3f73-e104-4c52-8e74-d54fe608b693] close complete, cost 26 ms 
[INFO ] 2024-09-09 03:00:05.036 - [测试主从-kafka] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-09 03:00:05.037 - [测试主从-kafka] - Stopped task aspect(s) 
[INFO ] 2024-09-09 03:00:05.037 - [测试主从-kafka] - Snapshot order controller have been removed 
[INFO ] 2024-09-09 03:00:05.099 - [测试主从-kafka] - Remove memory task client succeed, task: 测试主从-kafka[66d99173633a51407b6700cb] 
[INFO ] 2024-09-09 03:00:05.100 - [测试主从-kafka] - Destroy memory task client cache succeed, task: 测试主从-kafka[66d99173633a51407b6700cb] 
