[INFO ] 2024-07-04 14:25:52.036 - [任务 48] - Start task milestones: 66841a76976c4665eeaed6f9(任务 48) 
[INFO ] 2024-07-04 14:25:52.053 - [任务 48] - Task initialization... 
[INFO ] 2024-07-04 14:25:53.518 - [任务 48] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-04 14:25:53.580 - [任务 48] - The engine receives 任务 48 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 14:25:54.331 - [任务 48][TargetMongo] - Node TargetMongo[93569447-8c0c-4b90-b566-9a49f2f3bfd7] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:25:54.380 - [任务 48][TargetMongo] - Node TargetMongo[93569447-8c0c-4b90-b566-9a49f2f3bfd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-04 14:25:54.396 - [任务 48][SouceMysql] - Node SouceMysql[7a21267f-6a61-4d47-8a6b-2ed8a5b3909b] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:25:54.401 - [任务 48][SouceMysql] - Node SouceMysql[7a21267f-6a61-4d47-8a6b-2ed8a5b3909b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:25:55.893 - [任务 48][TargetMongo] - Node(TargetMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-04 14:25:55.898 - [任务 48][TargetMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-04 14:25:56.055 - [任务 48][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-04 14:25:56.056 - [任务 48][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-04 14:25:56.056 - [任务 48][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 14:25:56.062 - [任务 48][SouceMysql] - batch offset found: {},stream offset found: {"sequenceMap":{"POLICY":15,"_tapdata_heartbeat_table":6148},"streamOffset":{"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720006645,\"file\":\"binlog.000032\",\"pos\":19991378,\"row\":1,\"server_id\":1,\"event\":2}"}}} 
[INFO ] 2024-07-04 14:25:56.140 - [任务 48][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-04 14:25:56.140 - [任务 48][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-04 14:25:56.175 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-04 14:25:56.178 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Step 1 - Check connection SouceMysql enable share cdc: true 
[INFO ] 2024-07-04 14:25:56.178 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 48 enable share cdc: true 
[INFO ] 2024-07-04 14:25:56.212 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SouceMysql的共享挖掘任务 
[INFO ] 2024-07-04 14:25:56.231 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-04 14:25:56.236 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66841ae566ab5ede8aa3b331, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-7751783, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:25:56.242 - [任务 48][SouceMysql] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_POLICY_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-7751783', head seq: 0, tail seq: 14 
[INFO ] 2024-07-04 14:25:56.243 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-04 14:25:56.243 - [任务 48][SouceMysql] - Init share cdc reader completed 
[INFO ] 2024-07-04 14:25:56.249 - [任务 48][SouceMysql] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-04 14:25:56.251 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-04 14:25:56.252 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-04 14:25:56.262 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66841ae566ab5ede8aa3b331, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-7751783, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:25:56.262 - [任务 48][SouceMysql] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_POLICY_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-7751783', head seq: 0, tail seq: 14 
[INFO ] 2024-07-04 14:25:56.263 - [任务 48][SouceMysql] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SouceMysql的共享挖掘任务_POLICY_任务 48, external storage name: ExternalStorage_SHARE_CDC_-7751783 
[INFO ] 2024-07-04 14:25:56.263 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-04 14:25:56.263 - [任务 48][SouceMysql] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-04 14:25:56.263 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 15 
[INFO ] 2024-07-04 14:25:56.474 - [任务 48][SouceMysql] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=15} 
[INFO ] 2024-07-04 14:27:56.563 - [任务 48][SouceMysql] - Node SouceMysql[7a21267f-6a61-4d47-8a6b-2ed8a5b3909b] running status set to false 
[INFO ] 2024-07-04 14:27:56.596 - [任务 48][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-04 14:27:56.612 - [任务 48][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-7a21267f-6a61-4d47-8a6b-2ed8a5b3909b 
[INFO ] 2024-07-04 14:27:56.613 - [任务 48][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-7a21267f-6a61-4d47-8a6b-2ed8a5b3909b 
[INFO ] 2024-07-04 14:27:56.613 - [任务 48][SouceMysql] - Node SouceMysql[7a21267f-6a61-4d47-8a6b-2ed8a5b3909b] schema data cleaned 
[INFO ] 2024-07-04 14:27:56.614 - [任务 48][SouceMysql] - Node SouceMysql[7a21267f-6a61-4d47-8a6b-2ed8a5b3909b] monitor closed 
[INFO ] 2024-07-04 14:27:56.614 - [任务 48][SouceMysql] - Node SouceMysql[7a21267f-6a61-4d47-8a6b-2ed8a5b3909b] close complete, cost 53 ms 
[INFO ] 2024-07-04 14:27:56.621 - [任务 48][TargetMongo] - Node TargetMongo[93569447-8c0c-4b90-b566-9a49f2f3bfd7] running status set to false 
[INFO ] 2024-07-04 14:27:56.621 - [任务 48][TargetMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-93569447-8c0c-4b90-b566-9a49f2f3bfd7 
[INFO ] 2024-07-04 14:27:56.621 - [任务 48][TargetMongo] - PDK connector node released: HazelcastTargetPdkDataNode-93569447-8c0c-4b90-b566-9a49f2f3bfd7 
[INFO ] 2024-07-04 14:27:56.621 - [任务 48][TargetMongo] - Node TargetMongo[93569447-8c0c-4b90-b566-9a49f2f3bfd7] schema data cleaned 
[INFO ] 2024-07-04 14:27:56.622 - [任务 48][TargetMongo] - Node TargetMongo[93569447-8c0c-4b90-b566-9a49f2f3bfd7] monitor closed 
[INFO ] 2024-07-04 14:27:56.622 - [任务 48][TargetMongo] - Node TargetMongo[93569447-8c0c-4b90-b566-9a49f2f3bfd7] close complete, cost 7 ms 
[INFO ] 2024-07-04 14:28:01.611 - [任务 48] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 14:28:01.642 - [任务 48] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3c6681bc 
[INFO ] 2024-07-04 14:28:01.642 - [任务 48] - Stop task milestones: 66841a76976c4665eeaed6f9(任务 48)  
[INFO ] 2024-07-04 14:28:01.770 - [任务 48] - Stopped task aspect(s) 
[INFO ] 2024-07-04 14:28:01.770 - [任务 48] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 14:28:01.812 - [任务 48] - Remove memory task client succeed, task: 任务 48[66841a76976c4665eeaed6f9] 
[INFO ] 2024-07-04 14:28:01.814 - [任务 48] - Destroy memory task client cache succeed, task: 任务 48[66841a76976c4665eeaed6f9] 
