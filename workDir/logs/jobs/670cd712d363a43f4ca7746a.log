[INFO ] 2024-10-14 16:36:50.721 - [任务 10] - Start task milestones: 670cd712d363a43f4ca7746a(任务 10) 
[INFO ] 2024-10-14 16:36:50.854 - [任务 10] - Task initialization... 
[INFO ] 2024-10-14 16:36:50.888 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-14 16:36:50.966 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 16:36:50.974 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:36:50.974 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:36:50.974 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:36:50.974 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:36:54.055 - [任务 10][TestItem] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 16:37:01.653 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-14 16:37:01.654 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-14 16:37:01.654 - [任务 10][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 16:37:01.657 - [任务 10][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":9433,"gtidSet":""} 
[INFO ] 2024-10-14 16:37:01.719 - [任务 10][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-14 16:37:01.719 - [任务 10][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-14 16:37:01.757 - [任务 10][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-14 16:37:01.758 - [任务 10][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-14 16:37:01.760 - [任务 10][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 3 
[INFO ] 2024-10-14 16:37:01.760 - [任务 10][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-14 16:37:01.760 - [任务 10][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-14 16:37:01.760 - [任务 10][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-14 16:37:01.833 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-14 16:37:01.834 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 1 - Check connection mysql3306 enable share cdc: true 
[INFO ] 2024-10-14 16:37:01.834 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 10 enable share cdc: true 
[INFO ] 2024-10-14 16:37:01.855 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from mysql3306 
[INFO ] 2024-10-14 16:37:01.881 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 16:37:01.882 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-14 16:37:01.883 - [任务 10][BMSQL_ITEM] - Init share cdc reader completed 
[INFO ] 2024-10-14 16:37:01.883 - [任务 10][BMSQL_ITEM] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-14 16:37:01.883 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-14 16:37:01.883 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-10-14 16:37:02.018 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-14 16:37:02.018 - [任务 10][BMSQL_ITEM] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from mysql3306_BMSQL_ITEM_任务 10, external storage name: ExternalStorage_SHARE_CDC_794615392 
[INFO ] 2024-10-14 16:37:02.030 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_ITEM] 
[INFO ] 2024-10-14 16:37:02.030 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_ITEM) by timestamp(2024-10-14T08:37:01.652Z): 1 
[INFO ] 2024-10-14 16:37:02.030 - [任务 10][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-14 16:37:02.030 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_ITEM' log, sequence: 1 
[INFO ] 2024-10-14 16:37:02.238 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find by BMSQL_ITEM filter: {sequence=1} 
[INFO ] 2024-10-14 16:41:18.684 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=BMSQL_ITEM, timestamp=1728895273000, date=Mon Oct 14 16:41:13 CST 2024, after=Document{{I_NAME=name4, I_PRICE=null, I_ID=4, I_DATA=null, I_IM_ID=null}}, op=i, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAV3sidHNfc2VjIjoxNzI4ODk1MjczLCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6OTUxMiwicm93IjoxLCJzZXJ2ZXJfaWQiOjEsImV2ZW50IjoyfXg=
, type=DATA, connectionId=670754025fe35676bdffab25, isReplaceEvent=false, _ts=1728895274}} 
[INFO ] 2024-10-14 17:43:36.868 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] running status set to false 
[INFO ] 2024-10-14 17:43:36.872 - [任务 10][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-14 17:43:36.878 - [任务 10][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 17:43:36.878 - [任务 10][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 17:43:36.881 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] schema data cleaned 
[INFO ] 2024-10-14 17:43:36.899 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] monitor closed 
[INFO ] 2024-10-14 17:43:36.899 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] close complete, cost 76 ms 
[INFO ] 2024-10-14 17:43:36.918 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] running status set to false 
[INFO ] 2024-10-14 17:43:36.918 - [任务 10][TestItem] - PDK connector node stopped: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 17:43:36.918 - [任务 10][TestItem] - PDK connector node released: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 17:43:36.918 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] schema data cleaned 
[INFO ] 2024-10-14 17:43:36.919 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] monitor closed 
[INFO ] 2024-10-14 17:43:37.126 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] close complete, cost 21 ms 
[INFO ] 2024-10-14 17:43:38.493 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 17:43:38.493 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bb22fd4 
[INFO ] 2024-10-14 17:43:38.620 - [任务 10] - Stop task milestones: 670cd712d363a43f4ca7746a(任务 10)  
[INFO ] 2024-10-14 17:43:38.620 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-10-14 17:43:38.620 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 17:43:38.661 - [任务 10] - Remove memory task client succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 17:43:38.663 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 17:44:36.486 - [任务 10] - Start task milestones: 670cd712d363a43f4ca7746a(任务 10) 
[INFO ] 2024-10-14 17:44:36.487 - [任务 10] - Task initialization... 
[INFO ] 2024-10-14 17:44:36.693 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-14 17:44:36.749 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 17:44:36.749 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] start preload schema,table counts: 1 
[INFO ] 2024-10-14 17:44:36.749 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 17:44:36.749 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] start preload schema,table counts: 1 
[INFO ] 2024-10-14 17:44:36.749 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 17:44:39.162 - [任务 10][TestItem] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 17:44:42.195 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-14 17:44:42.195 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-14 17:44:42.195 - [任务 10][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 17:44:42.196 - [任务 10][BMSQL_ITEM] - batch offset found: {},stream offset found: 1728892800000 
[INFO ] 2024-10-14 17:44:42.237 - [任务 10][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-14 17:44:42.237 - [任务 10][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-14 17:44:42.272 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-14 17:44:42.272 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 1 - Check connection mysql3306 enable share cdc: true 
[INFO ] 2024-10-14 17:44:42.272 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 10 enable share cdc: true 
[INFO ] 2024-10-14 17:44:42.286 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from mysql3306 
[INFO ] 2024-10-14 17:44:42.286 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 17:44:42.359 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-14 17:44:42.360 - [任务 10][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-14 17:44:42.365 - [任务 10][BMSQL_ITEM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Find from storage failed; Error: Log storage[BMSQL_ITEM] detected unusable, first log timestamp(2024-10-14T08:36:51Z) is greater than task cdc start timestamp(2024-10-14T08:00:00Z) 
[ERROR] 2024-10-14 17:44:42.372 - [任务 10][BMSQL_ITEM] - Unknown PDK exception occur, io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Find from storage failed; Error: Log storage[BMSQL_ITEM] detected unusable, first log timestamp(2024-10-14T08:36:51Z) is greater than task cdc start timestamp(2024-10-14T08:00:00Z) <-- Error Message -->
Unknown PDK exception occur, io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Find from storage failed; Error: Log storage[BMSQL_ITEM] detected unusable, first log timestamp(2024-10-14T08:36:51Z) is greater than task cdc start timestamp(2024-10-14T08:00:00Z)

<-- Simple Stack Trace -->
Caused by: io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Log storage[BMSQL_ITEM] detected unusable, first log timestamp(2024-10-14T08:36:51Z) is greater than task cdc start timestamp(2024-10-14T08:00:00Z)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.checkTableStartPointValid(ShareCdcPDKTaskReader.java:259)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.canShareCdc(ShareCdcPDKTaskReader.java:214)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.init(ShareCdcPDKTaskReader.java:146)
	io.tapdata.flow.engine.V2.sharecdc.impl.ShareCdcFactory.shareCdcReader(ShareCdcFactory.java:36)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$46(HazelcastSourcePdkDataNode.java:936)
	...

<-- Full Stack Trace -->
io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Find from storage failed; Error: Log storage[BMSQL_ITEM] detected unusable, first log timestamp(2024-10-14T08:36:51Z) is greater than task cdc start timestamp(2024-10-14T08:00:00Z)
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doShareCdc$47(HazelcastSourcePdkDataNode.java:941)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:68)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doShareCdc(HazelcastSourcePdkDataNode.java:924)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:647)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Find from storage failed; Error: Log storage[BMSQL_ITEM] detected unusable, first log timestamp(2024-10-14T08:36:51Z) is greater than task cdc start timestamp(2024-10-14T08:00:00Z)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.checkTableStartPointValid(ShareCdcPDKTaskReader.java:279)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.canShareCdc(ShareCdcPDKTaskReader.java:214)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.init(ShareCdcPDKTaskReader.java:146)
	at io.tapdata.flow.engine.V2.sharecdc.impl.ShareCdcFactory.shareCdcReader(ShareCdcFactory.java:36)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$46(HazelcastSourcePdkDataNode.java:936)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 12 more
Caused by: io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Log storage[BMSQL_ITEM] detected unusable, first log timestamp(2024-10-14T08:36:51Z) is greater than task cdc start timestamp(2024-10-14T08:00:00Z)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.checkTableStartPointValid(ShareCdcPDKTaskReader.java:259)
	... 17 more

[INFO ] 2024-10-14 17:44:42.372 - [任务 10][BMSQL_ITEM] - Job suspend in error handle 
[INFO ] 2024-10-14 17:44:42.762 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] running status set to false 
[INFO ] 2024-10-14 17:44:42.762 - [任务 10][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 17:44:42.762 - [任务 10][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 17:44:42.762 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] schema data cleaned 
[INFO ] 2024-10-14 17:44:42.763 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] monitor closed 
[INFO ] 2024-10-14 17:44:42.763 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] close complete, cost 21 ms 
[INFO ] 2024-10-14 17:44:42.769 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] running status set to false 
[INFO ] 2024-10-14 17:44:42.769 - [任务 10][TestItem] - PDK connector node stopped: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 17:44:42.770 - [任务 10][TestItem] - PDK connector node released: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 17:44:42.770 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] schema data cleaned 
[INFO ] 2024-10-14 17:44:42.770 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] monitor closed 
[INFO ] 2024-10-14 17:44:42.971 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] close complete, cost 7 ms 
[INFO ] 2024-10-14 17:44:43.744 - [任务 10] - Task [任务 10] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-14 17:44:43.744 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 17:44:43.744 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@63a94208 
[INFO ] 2024-10-14 17:44:43.746 - [任务 10] - Stop task milestones: 670cd712d363a43f4ca7746a(任务 10)  
[INFO ] 2024-10-14 17:44:43.873 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-10-14 17:44:43.873 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 17:44:43.907 - [任务 10] - Remove memory task client succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 17:44:43.908 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 17:45:37.077 - [任务 10] - Start task milestones: 670cd712d363a43f4ca7746a(任务 10) 
[INFO ] 2024-10-14 17:45:37.077 - [任务 10] - Task initialization... 
[INFO ] 2024-10-14 17:45:37.278 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-14 17:45:37.281 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 17:45:37.344 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] start preload schema,table counts: 1 
[INFO ] 2024-10-14 17:45:37.345 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] start preload schema,table counts: 1 
[INFO ] 2024-10-14 17:45:37.345 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 17:45:37.345 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 17:45:39.994 - [任务 10][TestItem] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 17:45:42.672 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-14 17:45:42.672 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-14 17:45:42.672 - [任务 10][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 17:45:42.672 - [任务 10][BMSQL_ITEM] - batch offset found: {},stream offset found: 1728895019000 
[INFO ] 2024-10-14 17:45:42.715 - [任务 10][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-14 17:45:42.716 - [任务 10][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-14 17:45:42.761 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-14 17:45:42.761 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 1 - Check connection mysql3306 enable share cdc: true 
[INFO ] 2024-10-14 17:45:42.761 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 10 enable share cdc: true 
[INFO ] 2024-10-14 17:45:42.779 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from mysql3306 
[INFO ] 2024-10-14 17:45:42.779 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 17:45:42.788 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-14 17:45:42.789 - [任务 10][BMSQL_ITEM] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from mysql3306_BMSQL_ITEM_任务 10', name space: 'tapdv13-c.ExternalStorage_SHARE_CDC_794615392', head seq: 0, tail seq: 2 
[INFO ] 2024-10-14 17:45:42.791 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-10-14 17:45:42.791 - [任务 10][BMSQL_ITEM] - Init share cdc reader completed 
[INFO ] 2024-10-14 17:45:42.791 - [任务 10][BMSQL_ITEM] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-14 17:45:42.791 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-14 17:45:42.796 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-10-14 17:45:42.796 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-14 17:45:42.798 - [任务 10][BMSQL_ITEM] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from mysql3306_BMSQL_ITEM_任务 10', name space: 'tapdv13-c.ExternalStorage_SHARE_CDC_794615392', head seq: 0, tail seq: 2 
[INFO ] 2024-10-14 17:45:42.798 - [任务 10][BMSQL_ITEM] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from mysql3306_BMSQL_ITEM_任务 10, external storage name: ExternalStorage_SHARE_CDC_794615392 
[INFO ] 2024-10-14 17:45:42.805 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_ITEM] 
[INFO ] 2024-10-14 17:45:42.805 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_ITEM) by timestamp(2024-10-14T08:36:59Z): 1 
[INFO ] 2024-10-14 17:45:42.807 - [任务 10][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-14 17:45:42.807 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_ITEM' log, sequence: 1 
[INFO ] 2024-10-14 17:45:42.812 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find by BMSQL_ITEM filter: {sequence=1} 
[INFO ] 2024-10-14 17:45:42.812 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=BMSQL_ITEM, timestamp=1728895273000, date=Mon Oct 14 16:41:13 CST 2024, after=Document{{I_NAME=name4, I_PRICE=null, I_ID=4, I_DATA=null, I_IM_ID=null}}, op=i, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAV3sidHNfc2VjIjoxNzI4ODk1MjczLCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6OTUxMiwicm93IjoxLCJzZXJ2ZXJfaWQiOjEsImV2ZW50IjoyfXg=
, type=DATA, connectionId=670754025fe35676bdffab25, isReplaceEvent=false, _ts=1728895274}} 
[INFO ] 2024-10-14 17:45:44.024 - [任务 10][TestItem] - Table 'TestItem' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-14 17:45:45.833 - [任务 10][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-14 17:45:45.841 - [任务 10][BMSQL_ITEM] - Exception skipping - The current exception does not match the skip exception strategy, message: Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T08:44:19Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAV3sidHNfc2VjIjoxNzI4ODk1NDU5LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6OTgxNCwicm93IjoxLCJzZXJ2ZXJfaWQiOjEsImV2ZW50IjoyfXg=

  type=DATA
  connectionId=null
} 
[ERROR] 2024-10-14 17:45:45.844 - [任务 10][BMSQL_ITEM] - Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T08:44:19Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAV3sidHNfc2VjIjoxNzI4ODk1NDU5LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6OTgxNCwicm93IjoxLCJzZXJ2ZXJfaWQiOjEsImV2ZW50IjoyfXg=

  type=DATA
  connectionId=null
} <-- Error Message -->
Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T08:44:19Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAV3sidHNfc2VjIjoxNzI4ODk1NDU5LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6OTgxNCwicm93IjoxLCJzZXJ2ZXJfaWQiOjEsImV2ZW50IjoyfXg=

  type=DATA
  connectionId=null
}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T08:44:19Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAV3sidHNfc2VjIjoxNzI4ODk1NDU5LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6OTgxNCwicm93IjoxLCJzZXJ2ZXJfaWQiOjEsImV2ZW50IjoyfXg=

  type=DATA
  connectionId=null
}
	io.tapdata.sharecdc.impl.ShareCdcBaseReader.logContentVerify(ShareCdcBaseReader.java:182)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.tapEventWrapper(ShareCdcPDKTaskReader.java:573)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$null$6(ShareCdcPDKTaskReader.java:511)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$read$7(ShareCdcPDKTaskReader.java:511)
	...

<-- Full Stack Trace -->
Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T08:44:19Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAV3sidHNfc2VjIjoxNzI4ODk1NDU5LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6OTgxNCwicm93IjoxLCJzZXJ2ZXJfaWQiOjEsImV2ZW50IjoyfXg=

  type=DATA
  connectionId=null
}
	at io.tapdata.sharecdc.impl.ShareCdcBaseReader.logContentVerify(ShareCdcBaseReader.java:182)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.tapEventWrapper(ShareCdcPDKTaskReader.java:573)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$null$6(ShareCdcPDKTaskReader.java:511)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$read$7(ShareCdcPDKTaskReader.java:511)
	at io.tapdata.flow.engine.V2.util.SkipIdleProcessor.process(SkipIdleProcessor.java:95)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.read(ShareCdcPDKTaskReader.java:465)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-10-14 17:45:45.844 - [任务 10][BMSQL_ITEM] - Job suspend in error handle 
[INFO ] 2024-10-14 17:45:46.269 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] running status set to false 
[INFO ] 2024-10-14 17:45:46.297 - [任务 10][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 17:45:46.297 - [任务 10][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 17:45:46.297 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] schema data cleaned 
[INFO ] 2024-10-14 17:45:46.297 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] monitor closed 
[INFO ] 2024-10-14 17:45:46.298 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] close complete, cost 39 ms 
[INFO ] 2024-10-14 17:45:46.298 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] running status set to false 
[INFO ] 2024-10-14 17:45:46.308 - [任务 10][TestItem] - PDK connector node stopped: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 17:45:46.309 - [任务 10][TestItem] - PDK connector node released: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 17:45:46.309 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] schema data cleaned 
[INFO ] 2024-10-14 17:45:46.309 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] monitor closed 
[INFO ] 2024-10-14 17:45:46.511 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] close complete, cost 11 ms 
[INFO ] 2024-10-14 17:45:48.983 - [任务 10] - Task [任务 10] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-14 17:45:48.983 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 17:45:48.983 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@67432701 
[INFO ] 2024-10-14 17:45:48.984 - [任务 10] - Stop task milestones: 670cd712d363a43f4ca7746a(任务 10)  
[INFO ] 2024-10-14 17:45:49.104 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-10-14 17:45:49.106 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 17:45:49.134 - [任务 10] - Remove memory task client succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 17:45:49.134 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 17:46:21.603 - [任务 10] - Start task milestones: 670cd712d363a43f4ca7746a(任务 10) 
[INFO ] 2024-10-14 17:46:21.604 - [任务 10] - Task initialization... 
[INFO ] 2024-10-14 17:46:21.793 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-14 17:46:21.793 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 17:46:21.839 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] start preload schema,table counts: 1 
[INFO ] 2024-10-14 17:46:21.840 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] start preload schema,table counts: 1 
[INFO ] 2024-10-14 17:46:21.840 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 17:46:22.045 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 17:46:24.096 - [任务 10][TestItem] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 17:46:27.438 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-14 17:46:27.438 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-14 17:46:27.438 - [任务 10][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 17:46:27.480 - [任务 10][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":10037,"gtidSet":""} 
[INFO ] 2024-10-14 17:46:27.481 - [任务 10][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-14 17:46:27.481 - [任务 10][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-14 17:46:27.486 - [任务 10][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-14 17:46:27.525 - [任务 10][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-14 17:46:27.526 - [任务 10][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 5 
[INFO ] 2024-10-14 17:46:27.526 - [任务 10][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-14 17:46:27.526 - [任务 10][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-14 17:46:27.526 - [任务 10][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-14 17:46:27.600 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-14 17:46:27.601 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 1 - Check connection mysql3306 enable share cdc: true 
[INFO ] 2024-10-14 17:46:27.601 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 10 enable share cdc: true 
[INFO ] 2024-10-14 17:46:27.619 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from mysql3306 
[INFO ] 2024-10-14 17:46:27.630 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 17:46:27.630 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-14 17:46:27.630 - [任务 10][BMSQL_ITEM] - Init share cdc reader completed 
[INFO ] 2024-10-14 17:46:27.630 - [任务 10][BMSQL_ITEM] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-14 17:46:27.630 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-14 17:46:27.630 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-10-14 17:46:27.635 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-14 17:46:27.690 - [任务 10][BMSQL_ITEM] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from mysql3306_BMSQL_ITEM_任务 10, external storage name: ExternalStorage_SHARE_CDC_794615392 
[INFO ] 2024-10-14 17:46:27.690 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_ITEM] 
[INFO ] 2024-10-14 17:46:27.694 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_ITEM) by timestamp(2024-10-14T09:46:27.438Z): 3 
[INFO ] 2024-10-14 17:46:27.694 - [任务 10][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-14 17:46:27.694 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_ITEM' log, sequence: 3 
[INFO ] 2024-10-14 17:46:27.898 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find by BMSQL_ITEM filter: {sequence=3} 
[INFO ] 2024-10-14 17:46:28.711 - [任务 10][TestItem] - Table 'TestItem' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-14 17:46:53.719 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=BMSQL_ITEM, timestamp=1728899210000, date=Mon Oct 14 17:46:50 CST 2024, after=Document{{I_NAME=name6, I_PRICE=null, I_ID=6, I_DATA=null, I_IM_ID=null}}, op=i, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAWHsidHNfc2VjIjoxNzI4ODk5MjEwLCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6MTAxMTYsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14
, type=DATA, connectionId=670754025fe35676bdffab25, isReplaceEvent=false, _ts=1728899211}} 
[INFO ] 2024-10-14 18:01:29.761 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] running status set to false 
[INFO ] 2024-10-14 18:01:29.761 - [任务 10][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-14 18:01:29.772 - [任务 10][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 18:01:29.772 - [任务 10][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 18:01:29.773 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] schema data cleaned 
[INFO ] 2024-10-14 18:01:29.773 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] monitor closed 
[INFO ] 2024-10-14 18:01:29.774 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] close complete, cost 35 ms 
[INFO ] 2024-10-14 18:01:29.774 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] running status set to false 
[INFO ] 2024-10-14 18:01:29.786 - [任务 10][TestItem] - PDK connector node stopped: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 18:01:29.786 - [任务 10][TestItem] - PDK connector node released: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 18:01:29.786 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] schema data cleaned 
[INFO ] 2024-10-14 18:01:29.786 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] monitor closed 
[INFO ] 2024-10-14 18:01:29.992 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] close complete, cost 13 ms 
[INFO ] 2024-10-14 18:01:33.930 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 18:01:33.930 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76c175f7 
[INFO ] 2024-10-14 18:01:33.935 - [任务 10] - Stop task milestones: 670cd712d363a43f4ca7746a(任务 10)  
[INFO ] 2024-10-14 18:01:34.057 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-10-14 18:01:34.057 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 18:01:34.077 - [任务 10] - Remove memory task client succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 18:01:34.080 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 18:03:44.952 - [任务 10] - Start task milestones: 670cd712d363a43f4ca7746a(任务 10) 
[INFO ] 2024-10-14 18:03:45.167 - [任务 10] - Task initialization... 
[INFO ] 2024-10-14 18:03:45.211 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-14 18:03:45.212 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 18:03:45.265 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] start preload schema,table counts: 1 
[INFO ] 2024-10-14 18:03:45.265 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] start preload schema,table counts: 1 
[INFO ] 2024-10-14 18:03:45.265 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 18:03:45.265 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 18:03:47.664 - [任务 10][TestItem] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 18:03:50.671 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-14 18:03:50.671 - [任务 10][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-14 18:03:50.671 - [任务 10][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 18:03:50.672 - [任务 10][BMSQL_ITEM] - batch offset found: {},stream offset found: 1728899940000 
[INFO ] 2024-10-14 18:03:50.715 - [任务 10][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-14 18:03:50.715 - [任务 10][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-14 18:03:50.758 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-14 18:03:50.758 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 1 - Check connection mysql3306 enable share cdc: true 
[INFO ] 2024-10-14 18:03:50.758 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 10 enable share cdc: true 
[INFO ] 2024-10-14 18:03:50.769 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from mysql3306 
[INFO ] 2024-10-14 18:03:50.779 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 18:03:50.779 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-14 18:03:50.843 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-10-14 18:03:50.844 - [任务 10][BMSQL_ITEM] - Init share cdc reader completed 
[INFO ] 2024-10-14 18:03:50.844 - [任务 10][BMSQL_ITEM] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-14 18:03:50.844 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-14 18:03:50.849 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-10-14 18:03:50.849 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-14 18:03:50.850 - [任务 10][BMSQL_ITEM] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from mysql3306_BMSQL_ITEM_任务 10', name space: 'tapdv13-c.ExternalStorage_SHARE_CDC_794615392', head seq: 0, tail seq: 4 
[INFO ] 2024-10-14 18:03:50.850 - [任务 10][BMSQL_ITEM] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from mysql3306_BMSQL_ITEM_任务 10, external storage name: ExternalStorage_SHARE_CDC_794615392 
[INFO ] 2024-10-14 18:03:50.853 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_ITEM] 
[INFO ] 2024-10-14 18:03:50.854 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_ITEM) by timestamp(2024-10-14T09:59:00Z): 4 
[INFO ] 2024-10-14 18:03:50.854 - [任务 10][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-14 18:03:50.854 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_ITEM' log, sequence: 4 
[INFO ] 2024-10-14 18:03:50.855 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Find by BMSQL_ITEM filter: {sequence=4} 
[INFO ] 2024-10-14 18:03:50.855 - [任务 10][BMSQL_ITEM] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=BMSQL_ITEM, timestamp=1728900048000, date=Mon Oct 14 18:00:48 CST 2024, after=null, op=i, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAWHsidHNfc2VjIjoxNzI4OTAwMDQ4LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6MTA0MTgsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14
, type=DATA, connectionId=670754025fe35676bdffab25, isReplaceEvent=false, _ts=1728900049}} 
[INFO ] 2024-10-14 18:03:53.871 - [任务 10][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-14 18:03:53.887 - [任务 10][BMSQL_ITEM] - Exception skipping - The current exception does not match the skip exception strategy, message: Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T10:00:48Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAWHsidHNfc2VjIjoxNzI4OTAwMDQ4LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6MTA0MTgsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14

  type=DATA
  connectionId=null
} 
[ERROR] 2024-10-14 18:03:53.890 - [任务 10][BMSQL_ITEM] - Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T10:00:48Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAWHsidHNfc2VjIjoxNzI4OTAwMDQ4LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6MTA0MTgsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14

  type=DATA
  connectionId=null
} <-- Error Message -->
Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T10:00:48Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAWHsidHNfc2VjIjoxNzI4OTAwMDQ4LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6MTA0MTgsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14

  type=DATA
  connectionId=null
}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T10:00:48Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAWHsidHNfc2VjIjoxNzI4OTAwMDQ4LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6MTA0MTgsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14

  type=DATA
  connectionId=null
}
	io.tapdata.sharecdc.impl.ShareCdcBaseReader.logContentVerify(ShareCdcBaseReader.java:182)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.tapEventWrapper(ShareCdcPDKTaskReader.java:573)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$null$6(ShareCdcPDKTaskReader.java:511)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$read$7(ShareCdcPDKTaskReader.java:511)
	...

<-- Full Stack Trace -->
Log data: LogContent{
  fromTable='BMSQL_ITEM'
  tableNamespaces='null'
  timestamp=2024-10-14T10:00:48Z
  op=i
  before=null
  after=null
  ddl event=
  offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJGZkOWFmOTc1LWY0NzAtNDBjMi04NTQwLWI4Y2NlNjJlZGIyOHNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiJmZDlhZjk3NS1mNDcwLTQwYzItODU0MC1i
OGNjZTYyZWRiMjgifXQAWHsidHNfc2VjIjoxNzI4OTAwMDQ4LCJmaWxlIjoiYmlubG9nLjAwMDAz
NiIsInBvcyI6MTA0MTgsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14

  type=DATA
  connectionId=null
}
	at io.tapdata.sharecdc.impl.ShareCdcBaseReader.logContentVerify(ShareCdcBaseReader.java:182)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.tapEventWrapper(ShareCdcPDKTaskReader.java:573)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$null$6(ShareCdcPDKTaskReader.java:511)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$read$7(ShareCdcPDKTaskReader.java:511)
	at io.tapdata.flow.engine.V2.util.SkipIdleProcessor.process(SkipIdleProcessor.java:95)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.read(ShareCdcPDKTaskReader.java:465)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-10-14 18:03:53.895 - [任务 10][BMSQL_ITEM] - Job suspend in error handle 
[INFO ] 2024-10-14 18:03:54.260 - [任务 10] - Task [任务 10] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-14 18:03:54.273 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] running status set to false 
[INFO ] 2024-10-14 18:03:54.284 - [任务 10][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 18:03:54.284 - [任务 10][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-0e0cff4c-76c2-4d49-b6ae-fe9431960ae1 
[INFO ] 2024-10-14 18:03:54.285 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] schema data cleaned 
[INFO ] 2024-10-14 18:03:54.285 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] monitor closed 
[INFO ] 2024-10-14 18:03:54.285 - [任务 10][BMSQL_ITEM] - Node BMSQL_ITEM[0e0cff4c-76c2-4d49-b6ae-fe9431960ae1] close complete, cost 29 ms 
[INFO ] 2024-10-14 18:03:54.285 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] running status set to false 
[INFO ] 2024-10-14 18:03:54.293 - [任务 10][TestItem] - PDK connector node stopped: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 18:03:54.293 - [任务 10][TestItem] - PDK connector node released: HazelcastTargetPdkDataNode-0eee86b3-fc4a-4ec1-830c-abe7cd293812 
[INFO ] 2024-10-14 18:03:54.293 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] schema data cleaned 
[INFO ] 2024-10-14 18:03:54.293 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] monitor closed 
[INFO ] 2024-10-14 18:03:54.499 - [任务 10][TestItem] - Node TestItem[0eee86b3-fc4a-4ec1-830c-abe7cd293812] close complete, cost 8 ms 
[INFO ] 2024-10-14 18:03:59.216 - [任务 10] - Task [任务 10] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-14 18:03:59.216 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 18:03:59.219 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@ac4c916 
[INFO ] 2024-10-14 18:03:59.220 - [任务 10] - Stop task milestones: 670cd712d363a43f4ca7746a(任务 10)  
[INFO ] 2024-10-14 18:03:59.350 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-10-14 18:03:59.351 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 18:03:59.369 - [任务 10] - Remove memory task client succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
[INFO ] 2024-10-14 18:03:59.370 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[670cd712d363a43f4ca7746a] 
