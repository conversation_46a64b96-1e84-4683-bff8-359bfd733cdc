[INFO ] 2024-09-04 16:22:32.704 - [任务 7(101)] - 66d6a826a40a37725da9ded8 task start 
[INFO ] 2024-09-04 16:22:32.781 - [任务 7(101)][dc38e850-3dd8-40c3-a108-eefc3d3c1c86] - Node dc38e850-3dd8-40c3-a108-eefc3d3c1c86[dc38e850-3dd8-40c3-a108-eefc3d3c1c86] start preload schema,table counts: 0 
[INFO ] 2024-09-04 16:22:32.781 - [任务 7(101)][增强JS] - Node 增强JS[3915a137-1d77-48ed-b7d0-60460187fa6e] start preload schema,table counts: 1 
[INFO ] 2024-09-04 16:22:32.781 - [任务 7(101)][hq_wz_base] - Node hq_wz_base[c441ce89-3969-46d1-914c-6b793e3c2b9e] start preload schema,table counts: 1 
[INFO ] 2024-09-04 16:22:32.781 - [任务 7(101)][dc38e850-3dd8-40c3-a108-eefc3d3c1c86] - Node dc38e850-3dd8-40c3-a108-eefc3d3c1c86[dc38e850-3dd8-40c3-a108-eefc3d3c1c86] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 16:22:32.956 - [任务 7(101)][增强JS] - Node 增强JS[3915a137-1d77-48ed-b7d0-60460187fa6e] preload schema finished, cost 170 ms 
[INFO ] 2024-09-04 16:22:32.956 - [任务 7(101)][hq_wz_base] - Node hq_wz_base[c441ce89-3969-46d1-914c-6b793e3c2b9e] preload schema finished, cost 170 ms 
[INFO ] 2024-09-04 16:22:32.956 - [任务 7(101)][增强JS] - Node js_processor(增强JS: 3915a137-1d77-48ed-b7d0-60460187fa6e) enable batch process 
[INFO ] 2024-09-04 16:22:33.015 - [任务 7(101)][hq_wz_base] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId sqlserver not found for associateId HazelcastSampleSourcePdkDataNode-c441ce89-3969-46d1-914c-6b793e3c2b9e 
[ERROR] 2024-09-04 16:22:33.140 - [任务 7(101)][hq_wz_base] - start source runner failed: Failed to create pdk connector node, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId sqlserver not found for associateId HazelcastSampleSourcePdkDataNode-c441ce89-3969-46d1-914c-6b793e3c2b9e <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId sqlserver not found for associateId HazelcastSampleSourcePdkDataNode-c441ce89-3969-46d1-914c-6b793e3c2b9e

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId sqlserver not found for associateId HazelcastSampleSourcePdkDataNode-c441ce89-3969-46d1-914c-6b793e3c2b9e
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId sqlserver not found for associateId HazelcastSampleSourcePdkDataNode-c441ce89-3969-46d1-914c-6b793e3c2b9e
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId sqlserver not found for associateId HazelcastSampleSourcePdkDataNode-c441ce89-3969-46d1-914c-6b793e3c2b9e
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId sqlserver not found for associateId HazelcastSampleSourcePdkDataNode-c441ce89-3969-46d1-914c-6b793e3c2b9e
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-09-04 16:22:35.592 - [任务 7(101)][hq_wz_base] - Node hq_wz_base[c441ce89-3969-46d1-914c-6b793e3c2b9e] running status set to false 
[INFO ] 2024-09-04 16:22:35.595 - [任务 7(101)][hq_wz_base] - PDK connector node stopped: null 
[INFO ] 2024-09-04 16:22:35.596 - [任务 7(101)][hq_wz_base] - PDK connector node released: null 
[INFO ] 2024-09-04 16:22:35.596 - [任务 7(101)][hq_wz_base] - Node hq_wz_base[c441ce89-3969-46d1-914c-6b793e3c2b9e] schema data cleaned 
[INFO ] 2024-09-04 16:22:35.596 - [任务 7(101)][hq_wz_base] - Node hq_wz_base[c441ce89-3969-46d1-914c-6b793e3c2b9e] monitor closed 
[INFO ] 2024-09-04 16:22:35.596 - [任务 7(101)][hq_wz_base] - Node hq_wz_base[c441ce89-3969-46d1-914c-6b793e3c2b9e] close complete, cost 11 ms 
[INFO ] 2024-09-04 16:23:02.958 - [任务 7(101)][增强JS] - Node 增强JS[3915a137-1d77-48ed-b7d0-60460187fa6e] running status set to false 
[INFO ] 2024-09-04 16:23:02.959 - [任务 7(101)][增强JS] - Node 增强JS[3915a137-1d77-48ed-b7d0-60460187fa6e] schema data cleaned 
[INFO ] 2024-09-04 16:23:02.959 - [任务 7(101)][增强JS] - Node 增强JS[3915a137-1d77-48ed-b7d0-60460187fa6e] monitor closed 
[INFO ] 2024-09-04 16:23:02.959 - [任务 7(101)][增强JS] - Node 增强JS[3915a137-1d77-48ed-b7d0-60460187fa6e] close complete, cost 1 ms 
[INFO ] 2024-09-04 16:23:02.962 - [任务 7(101)][dc38e850-3dd8-40c3-a108-eefc3d3c1c86] - Node dc38e850-3dd8-40c3-a108-eefc3d3c1c86[dc38e850-3dd8-40c3-a108-eefc3d3c1c86] running status set to false 
[INFO ] 2024-09-04 16:23:02.962 - [任务 7(101)][dc38e850-3dd8-40c3-a108-eefc3d3c1c86] - Node dc38e850-3dd8-40c3-a108-eefc3d3c1c86[dc38e850-3dd8-40c3-a108-eefc3d3c1c86] schema data cleaned 
[INFO ] 2024-09-04 16:23:02.962 - [任务 7(101)][dc38e850-3dd8-40c3-a108-eefc3d3c1c86] - Node dc38e850-3dd8-40c3-a108-eefc3d3c1c86[dc38e850-3dd8-40c3-a108-eefc3d3c1c86] monitor closed 
[INFO ] 2024-09-04 16:23:02.962 - [任务 7(101)][dc38e850-3dd8-40c3-a108-eefc3d3c1c86] - Node dc38e850-3dd8-40c3-a108-eefc3d3c1c86[dc38e850-3dd8-40c3-a108-eefc3d3c1c86] close complete, cost 0 ms 
[INFO ] 2024-09-04 16:23:03.019 - [任务 7(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-04 16:23:03.020 - [任务 7(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-04 16:23:03.024 - [任务 7(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-04 16:23:03.024 - [任务 7(101)] - test run task 66d6a826a40a37725da9ded8 complete, cost 30354ms 
