[INFO ] 2024-05-31 12:28:57.832 - [任务 2] - Start task milestones: 66594f6ff6745e7b9367490c(任务 2) 
[INFO ] 2024-05-31 12:28:57.945 - [任务 2] - Task initialization... 
[INFO ] 2024-05-31 12:28:57.946 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-31 12:28:58.329 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-31 12:28:58.330 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] start preload schema,table counts: 1 
[INFO ] 2024-05-31 12:28:58.460 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] start preload schema,table counts: 1 
[INFO ] 2024-05-31 12:28:58.460 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] preload schema finished, cost 57 ms 
[INFO ] 2024-05-31 12:28:58.666 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] preload schema finished, cost 133 ms 
[INFO ] 2024-05-31 12:28:59.497 - [任务 2][aggTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-31 12:28:59.498 - [任务 2][orders] - Source node "orders" read batch size: 100 
[INFO ] 2024-05-31 12:28:59.502 - [任务 2][orders] - Source node "orders" event queue capacity: 200 
[INFO ] 2024-05-31 12:28:59.507 - [任务 2][orders] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-05-31 12:28:59.774 - [任务 2][orders] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-05-31 12:28:59.774 - [任务 2][orders] - new logical replication slot created, slotName:tapdata_cdc_03c12b4b_932a_4903_9a6e_c69e7f89915c 
[INFO ] 2024-05-31 12:28:59.876 - [任务 2][orders] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-05-31 12:28:59.882 - [任务 2][orders] - Initial sync started 
[INFO ] 2024-05-31 12:28:59.883 - [任务 2][orders] - Starting batch read, table name: orders, offset: null 
[INFO ] 2024-05-31 12:28:59.885 - [任务 2][orders] - Table orders is going to be initial synced 
[INFO ] 2024-05-31 12:28:59.939 - [任务 2][orders] - Query table 'orders' counts: 1 
[INFO ] 2024-05-31 12:28:59.940 - [任务 2][orders] - Initial sync completed 
[INFO ] 2024-05-31 12:28:59.940 - [任务 2][orders] - Incremental sync starting... 
[INFO ] 2024-05-31 12:28:59.942 - [任务 2][orders] - Initial sync completed 
[INFO ] 2024-05-31 12:28:59.943 - [任务 2][orders] - Starting stream read, table list: [orders], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[WARN ] 2024-05-31 12:28:59.974 - [任务 2][orders] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-05-31 12:28:59.975 - [任务 2][orders] - Using an existing logical replication slot, slotName:tapdata_cdc_03c12b4b_932a_4903_9a6e_c69e7f89915c 
[WARN ] 2024-05-31 12:29:00.181 - [任务 2][aggTest] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-31 12:29:00.384 - [任务 2][orders] - Connector PostgreSQL incremental start succeed, tables: [orders], data change syncing 
[INFO ] 2024-05-31 12:29:39.871 - [任务 2] - Stop task milestones: 66594f6ff6745e7b9367490c(任务 2)  
[INFO ] 2024-05-31 12:29:39.959 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] running status set to false 
[INFO ] 2024-05-31 12:29:40.073 - [任务 2][orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-127375c8-9fec-40a6-829a-5ce81ec06736 
[INFO ] 2024-05-31 12:29:40.073 - [任务 2][orders] - PDK connector node released: HazelcastSourcePdkDataNode-127375c8-9fec-40a6-829a-5ce81ec06736 
[INFO ] 2024-05-31 12:29:40.073 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] schema data cleaned 
[INFO ] 2024-05-31 12:29:40.074 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] monitor closed 
[INFO ] 2024-05-31 12:29:40.076 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] close complete, cost 117 ms 
[INFO ] 2024-05-31 12:29:40.076 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] running status set to false 
[INFO ] 2024-05-31 12:29:40.094 - [任务 2][aggTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-7036ba53-cc96-44bd-8d9d-a9cef7b9a32c 
[INFO ] 2024-05-31 12:29:40.094 - [任务 2][aggTest] - PDK connector node released: HazelcastTargetPdkDataNode-7036ba53-cc96-44bd-8d9d-a9cef7b9a32c 
[INFO ] 2024-05-31 12:29:40.094 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] schema data cleaned 
[INFO ] 2024-05-31 12:29:40.094 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] monitor closed 
[INFO ] 2024-05-31 12:29:40.095 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] close complete, cost 18 ms 
[ERROR] 2024-05-31 12:29:40.300 - [任务 2][aggTest] - Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.mongodb.writer.MongodbWriter.<init>(MongodbWriter.java:59)
	io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1216)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:833)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:779)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:773)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:482)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.mongodb.writer.MongodbWriter.<init>(MongodbWriter.java:59)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1216)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	... 26 more

[INFO ] 2024-05-31 12:29:40.899 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-31 12:29:40.902 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-05-31 12:29:40.902 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-05-31 12:29:41.047 - [任务 2] - Remove memory task client succeed, task: 任务 2[66594f6ff6745e7b9367490c] 
[INFO ] 2024-05-31 12:29:41.048 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66594f6ff6745e7b9367490c] 
[INFO ] 2024-05-31 12:30:21.224 - [任务 2] - Start task milestones: 66594f6ff6745e7b9367490c(任务 2) 
[INFO ] 2024-05-31 12:30:21.299 - [任务 2] - Task initialization... 
[INFO ] 2024-05-31 12:30:21.441 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-31 12:30:21.442 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-31 12:30:21.507 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] start preload schema,table counts: 1 
[INFO ] 2024-05-31 12:30:21.511 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] start preload schema,table counts: 1 
[INFO ] 2024-05-31 12:30:21.595 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] preload schema finished, cost 92 ms 
[INFO ] 2024-05-31 12:30:21.595 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] preload schema finished, cost 93 ms 
[INFO ] 2024-05-31 12:30:22.398 - [任务 2][aggTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-31 12:30:22.712 - [任务 2][orders] - Source node "orders" read batch size: 100 
[INFO ] 2024-05-31 12:30:22.713 - [任务 2][orders] - Source node "orders" event queue capacity: 200 
[INFO ] 2024-05-31 12:30:22.713 - [任务 2][orders] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-05-31 12:30:22.948 - [任务 2][orders] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-05-31 12:30:22.949 - [任务 2][orders] - new logical replication slot created, slotName:tapdata_cdc_b3cc2de1_4669_4f2d_b515_ece580da6ba1 
[INFO ] 2024-05-31 12:30:23.036 - [任务 2][orders] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-05-31 12:30:23.036 - [任务 2][orders] - Initial sync started 
[INFO ] 2024-05-31 12:30:23.047 - [任务 2][orders] - Starting batch read, table name: orders, offset: null 
[INFO ] 2024-05-31 12:30:23.053 - [任务 2][orders] - Table orders is going to be initial synced 
[INFO ] 2024-05-31 12:30:23.116 - [任务 2][orders] - Query table 'orders' counts: 1 
[INFO ] 2024-05-31 12:30:23.116 - [任务 2][orders] - Initial sync completed 
[INFO ] 2024-05-31 12:30:23.116 - [任务 2][orders] - Incremental sync starting... 
[INFO ] 2024-05-31 12:30:23.120 - [任务 2][orders] - Initial sync completed 
[INFO ] 2024-05-31 12:30:23.124 - [任务 2][orders] - Starting stream read, table list: [orders], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[WARN ] 2024-05-31 12:30:23.153 - [任务 2][orders] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-05-31 12:30:23.154 - [任务 2][orders] - Using an existing logical replication slot, slotName:tapdata_cdc_b3cc2de1_4669_4f2d_b515_ece580da6ba1 
[INFO ] 2024-05-31 12:30:35.161 - [任务 2][orders] - Connector PostgreSQL incremental start succeed, tables: [orders], data change syncing 
[WARN ] 2024-05-31 12:31:37.479 - [任务 2][aggTest] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-31 12:31:37.890 - [任务 2][orders] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.io.EOFException
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-31 12:31:50.815 - [任务 2] - Stop task milestones: 66594f6ff6745e7b9367490c(任务 2)  
[INFO ] 2024-05-31 12:31:50.829 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] running status set to false 
[INFO ] 2024-05-31 12:31:50.862 - [任务 2][orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-127375c8-9fec-40a6-829a-5ce81ec06736 
[INFO ] 2024-05-31 12:31:50.862 - [任务 2][orders] - PDK connector node released: HazelcastSourcePdkDataNode-127375c8-9fec-40a6-829a-5ce81ec06736 
[INFO ] 2024-05-31 12:31:50.865 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] schema data cleaned 
[INFO ] 2024-05-31 12:31:50.865 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] monitor closed 
[INFO ] 2024-05-31 12:31:50.868 - [任务 2][orders] - Node orders[127375c8-9fec-40a6-829a-5ce81ec06736] close complete, cost 63 ms 
[INFO ] 2024-05-31 12:31:50.869 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] running status set to false 
[ERROR] 2024-05-31 12:31:50.889 - [任务 2][orders] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-33) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-33) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-33) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.postgres.PostgresJdbcContext.queryTimeZone(PostgresJdbcContext.java:47)
	io.tapdata.connector.postgres.cdc.PostgresCdcRunner.<init>(PostgresCdcRunner.java:55)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-33) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.postgres.PostgresJdbcContext.queryTimeZone(PostgresJdbcContext.java:47)
	at io.tapdata.connector.postgres.cdc.PostgresCdcRunner.<init>(PostgresCdcRunner.java:55)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:371)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:714)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:735)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-33) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[INFO ] 2024-05-31 12:31:50.891 - [任务 2][aggTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-7036ba53-cc96-44bd-8d9d-a9cef7b9a32c 
[INFO ] 2024-05-31 12:31:50.891 - [任务 2][aggTest] - PDK connector node released: HazelcastTargetPdkDataNode-7036ba53-cc96-44bd-8d9d-a9cef7b9a32c 
[INFO ] 2024-05-31 12:31:50.891 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] schema data cleaned 
[INFO ] 2024-05-31 12:31:50.891 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] monitor closed 
[INFO ] 2024-05-31 12:31:50.969 - [任务 2][aggTest] - Node aggTest[7036ba53-cc96-44bd-8d9d-a9cef7b9a32c] close complete, cost 23 ms 
[ERROR] 2024-05-31 12:31:50.970 - [任务 2][aggTest] - Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.mongodb.writer.MongodbWriter.<init>(MongodbWriter.java:59)
	io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1216)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: aggTest
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:833)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:779)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:773)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:482)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.mongodb.writer.MongodbWriter.<init>(MongodbWriter.java:59)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1216)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	... 26 more

[INFO ] 2024-05-31 12:31:52.244 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-31 12:31:52.245 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-05-31 12:31:52.245 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-05-31 12:31:52.272 - [任务 2] - Remove memory task client succeed, task: 任务 2[66594f6ff6745e7b9367490c] 
[INFO ] 2024-05-31 12:31:52.275 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66594f6ff6745e7b9367490c] 
