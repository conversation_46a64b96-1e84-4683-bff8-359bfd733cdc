[INFO ] 2024-07-24 04:00:55.166 - [任务 18] - Task initialization... 
[INFO ] 2024-07-24 04:00:55.275 - [任务 18] - Start task milestones: 669f93c6e29f7d4f8d097853(任务 18) 
[INFO ] 2024-07-24 04:00:55.275 - [任务 18] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-24 04:00:55.348 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 04:00:55.348 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] start preload schema,table counts: 1 
[INFO ] 2024-07-24 04:00:55.349 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] start preload schema,table counts: 1 
[INFO ] 2024-07-24 04:00:55.349 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 04:00:55.349 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 04:00:56.127 - [任务 18][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 04:00:56.169 - [任务 18][Inventory] - Source node "Inventory" read batch size: 100 
[INFO ] 2024-07-24 04:00:56.171 - [任务 18][Inventory] - Source node "Inventory" event queue capacity: 200 
[INFO ] 2024-07-24 04:00:56.171 - [任务 18][Inventory] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 04:00:56.246 - [任务 18][Inventory] - batch offset found: {},stream offset found: {"currentStartLSN":"00000029000018800001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 04:00:56.246 - [任务 18][Inventory] - Initial sync started 
[INFO ] 2024-07-24 04:00:56.246 - [任务 18][Inventory] - Starting batch read, table name: Inventory 
[INFO ] 2024-07-24 04:00:56.304 - [任务 18][Inventory] - Table Inventory is going to be initial synced 
[INFO ] 2024-07-24 04:00:56.304 - [任务 18][Inventory] - Table [Inventory] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 04:00:56.331 - [任务 18][Inventory] - Query table 'Inventory' counts: 4 
[INFO ] 2024-07-24 04:00:56.331 - [任务 18][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 04:00:56.331 - [任务 18][Inventory] - Incremental sync starting... 
[INFO ] 2024-07-24 04:00:56.331 - [任务 18][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 04:00:56.390 - [任务 18][Inventory] - Starting stream read, table list: [Inventory], offset: {"currentStartLSN":"00000029000018800001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 04:00:56.391 - [任务 18][Inventory] - opened cdc tables: [Inventory] 
[INFO ] 2024-07-24 04:00:56.481 - [任务 18][Inventory] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 04:00:56.481 - [任务 18][Inventory] - Connector SQL Server incremental start succeed, tables: [Inventory], data change syncing 
[INFO ] 2024-07-24 04:26:05.261 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] running status set to false 
[INFO ] 2024-07-24 04:26:05.262 - [任务 18][Inventory] - Incremental sync completed 
[INFO ] 2024-07-24 04:26:08.275 - [任务 18][Inventory] - PDK connector node stopped: HazelcastSourcePdkDataNode-7127ab3a-8261-4e3b-a48b-e2eec00c9e17 
[INFO ] 2024-07-24 04:26:08.276 - [任务 18][Inventory] - PDK connector node released: HazelcastSourcePdkDataNode-7127ab3a-8261-4e3b-a48b-e2eec00c9e17 
[INFO ] 2024-07-24 04:26:08.276 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] schema data cleaned 
[INFO ] 2024-07-24 04:26:08.278 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] monitor closed 
[INFO ] 2024-07-24 04:26:08.280 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] close complete, cost 3040 ms 
[INFO ] 2024-07-24 04:26:08.280 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] running status set to false 
[INFO ] 2024-07-24 04:26:08.299 - [任务 18][dummy_test] - Stop connector: first 1721764856288 1350ms, last 1721765068173 836ms, counts: 7/212721ms, min: 812, max: 1350, QPS: 0/s 
[INFO ] 2024-07-24 04:26:08.301 - [任务 18][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-0ecb1680-8397-4c4f-8732-454aab68a810 
[INFO ] 2024-07-24 04:26:08.301 - [任务 18][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-0ecb1680-8397-4c4f-8732-454aab68a810 
[INFO ] 2024-07-24 04:26:08.301 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] schema data cleaned 
[INFO ] 2024-07-24 04:26:08.302 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] monitor closed 
[INFO ] 2024-07-24 04:26:08.302 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] close complete, cost 23 ms 
[INFO ] 2024-07-24 04:26:12.257 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 04:26:12.264 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@27f4d629 
[INFO ] 2024-07-24 04:26:12.385 - [任务 18] - Stop task milestones: 669f93c6e29f7d4f8d097853(任务 18)  
[INFO ] 2024-07-24 04:26:12.387 - [任务 18] - Stopped task aspect(s) 
[INFO ] 2024-07-24 04:26:12.387 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 04:26:12.408 - [任务 18] - Remove memory task client succeed, task: 任务 18[669f93c6e29f7d4f8d097853] 
[INFO ] 2024-07-24 04:26:12.411 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[669f93c6e29f7d4f8d097853] 
[INFO ] 2024-07-24 04:35:42.845 - [任务 18] - Task initialization... 
[INFO ] 2024-07-24 04:35:42.974 - [任务 18] - Start task milestones: 669f93c6e29f7d4f8d097853(任务 18) 
[INFO ] 2024-07-24 04:35:42.974 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 04:35:43.061 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 04:35:43.061 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] start preload schema,table counts: 1 
[INFO ] 2024-07-24 04:35:43.061 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] start preload schema,table counts: 1 
[INFO ] 2024-07-24 04:35:43.061 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 04:35:43.062 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 04:35:43.904 - [任务 18][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 04:35:43.905 - [任务 18][Inventory] - Source node "Inventory" read batch size: 100 
[INFO ] 2024-07-24 04:35:43.905 - [任务 18][Inventory] - Source node "Inventory" event queue capacity: 200 
[INFO ] 2024-07-24 04:35:43.905 - [任务 18][Inventory] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 04:35:43.927 - [任务 18][Inventory] - batch offset found: {},stream offset found: {"currentStartLSN":"00000029000026F00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 04:35:43.985 - [任务 18][Inventory] - Initial sync started 
[INFO ] 2024-07-24 04:35:43.985 - [任务 18][Inventory] - Starting batch read, table name: Inventory 
[INFO ] 2024-07-24 04:35:44.059 - [任务 18][Inventory] - Table Inventory is going to be initial synced 
[INFO ] 2024-07-24 04:35:44.059 - [任务 18][Inventory] - Table [Inventory] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 04:35:44.089 - [任务 18][Inventory] - Query table 'Inventory' counts: 4 
[INFO ] 2024-07-24 04:35:44.089 - [任务 18][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 04:35:44.089 - [任务 18][Inventory] - Incremental sync starting... 
[INFO ] 2024-07-24 04:35:44.090 - [任务 18][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 04:35:44.091 - [任务 18][Inventory] - Starting stream read, table list: [Inventory], offset: {"currentStartLSN":"00000029000026F00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 04:35:44.229 - [任务 18][Inventory] - opened cdc tables: [Inventory] 
[INFO ] 2024-07-24 04:35:44.232 - [任务 18][Inventory] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 04:35:44.438 - [任务 18][Inventory] - Connector SQL Server incremental start succeed, tables: [Inventory], data change syncing 
[INFO ] 2024-07-24 04:36:03.838 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] running status set to false 
[INFO ] 2024-07-24 04:36:04.375 - [任务 18][Inventory] - Incremental sync completed 
[INFO ] 2024-07-24 04:36:06.881 - [任务 18][Inventory] - PDK connector node stopped: HazelcastSourcePdkDataNode-7127ab3a-8261-4e3b-a48b-e2eec00c9e17 
[INFO ] 2024-07-24 04:36:06.881 - [任务 18][Inventory] - PDK connector node released: HazelcastSourcePdkDataNode-7127ab3a-8261-4e3b-a48b-e2eec00c9e17 
[INFO ] 2024-07-24 04:36:06.881 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] schema data cleaned 
[INFO ] 2024-07-24 04:36:06.882 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] monitor closed 
[INFO ] 2024-07-24 04:36:06.886 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] close complete, cost 3077 ms 
[INFO ] 2024-07-24 04:36:06.886 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] running status set to false 
[INFO ] 2024-07-24 04:36:06.895 - [任务 18][dummy_test] - Stop connector: first 1721766944041 1357ms, last 1721766944041 1357ms, counts: 4/1357ms, min: 1357, max: 1357, QPS: 4/s 
[INFO ] 2024-07-24 04:36:06.895 - [任务 18][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-0ecb1680-8397-4c4f-8732-454aab68a810 
[INFO ] 2024-07-24 04:36:06.895 - [任务 18][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-0ecb1680-8397-4c4f-8732-454aab68a810 
[INFO ] 2024-07-24 04:36:06.895 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] schema data cleaned 
[INFO ] 2024-07-24 04:36:06.895 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] monitor closed 
[INFO ] 2024-07-24 04:36:06.896 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] close complete, cost 10 ms 
[INFO ] 2024-07-24 04:36:08.020 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 04:36:08.020 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6ec96505 
[INFO ] 2024-07-24 04:36:08.147 - [任务 18] - Stop task milestones: 669f93c6e29f7d4f8d097853(任务 18)  
[INFO ] 2024-07-24 04:36:08.163 - [任务 18] - Stopped task aspect(s) 
[INFO ] 2024-07-24 04:36:08.163 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 04:36:08.193 - [任务 18] - Remove memory task client succeed, task: 任务 18[669f93c6e29f7d4f8d097853] 
[INFO ] 2024-07-24 04:36:08.193 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[669f93c6e29f7d4f8d097853] 
[INFO ] 2024-07-24 05:02:46.804 - [任务 18] - Task initialization... 
[INFO ] 2024-07-24 05:02:46.805 - [任务 18] - Start task milestones: 669f93c6e29f7d4f8d097853(任务 18) 
[INFO ] 2024-07-24 05:02:46.929 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 05:02:46.929 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 05:02:46.972 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] start preload schema,table counts: 1 
[INFO ] 2024-07-24 05:02:46.972 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] start preload schema,table counts: 1 
[INFO ] 2024-07-24 05:02:46.972 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 05:02:46.972 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 05:02:47.573 - [任务 18][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 05:02:47.897 - [任务 18][Inventory] - Source node "Inventory" read batch size: 100 
[INFO ] 2024-07-24 05:02:47.897 - [任务 18][Inventory] - Source node "Inventory" event queue capacity: 200 
[INFO ] 2024-07-24 05:02:47.897 - [任务 18][Inventory] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 05:02:47.920 - [任务 18][Inventory] - batch offset found: {},stream offset found: {"currentStartLSN":"00000029000030680001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 05:02:47.957 - [任务 18][Inventory] - Initial sync started 
[INFO ] 2024-07-24 05:02:47.957 - [任务 18][Inventory] - Starting batch read, table name: Inventory 
[INFO ] 2024-07-24 05:02:48.053 - [任务 18][Inventory] - Table Inventory is going to be initial synced 
[INFO ] 2024-07-24 05:02:48.054 - [任务 18][Inventory] - Table [Inventory] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 05:02:48.088 - [任务 18][Inventory] - Query table 'Inventory' counts: 4 
[INFO ] 2024-07-24 05:02:48.088 - [任务 18][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 05:02:48.088 - [任务 18][Inventory] - Incremental sync starting... 
[INFO ] 2024-07-24 05:02:48.088 - [任务 18][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 05:02:48.131 - [任务 18][Inventory] - Starting stream read, table list: [Inventory], offset: {"currentStartLSN":"00000029000030680001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 05:02:48.131 - [任务 18][Inventory] - opened cdc tables: [Inventory] 
[INFO ] 2024-07-24 05:02:48.209 - [任务 18][Inventory] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 05:02:48.209 - [任务 18][Inventory] - Connector SQL Server incremental start succeed, tables: [Inventory], data change syncing 
[INFO ] 2024-07-24 05:21:21.694 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] running status set to false 
[INFO ] 2024-07-24 05:21:22.235 - [任务 18][Inventory] - Incremental sync completed 
[INFO ] 2024-07-24 05:21:24.721 - [任务 18][Inventory] - PDK connector node stopped: HazelcastSourcePdkDataNode-7127ab3a-8261-4e3b-a48b-e2eec00c9e17 
[INFO ] 2024-07-24 05:21:24.721 - [任务 18][Inventory] - PDK connector node released: HazelcastSourcePdkDataNode-7127ab3a-8261-4e3b-a48b-e2eec00c9e17 
[INFO ] 2024-07-24 05:21:24.721 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] schema data cleaned 
[INFO ] 2024-07-24 05:21:24.721 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] monitor closed 
[INFO ] 2024-07-24 05:21:24.723 - [任务 18][Inventory] - Node Inventory[7127ab3a-8261-4e3b-a48b-e2eec00c9e17] close complete, cost 3042 ms 
[INFO ] 2024-07-24 05:21:24.723 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] running status set to false 
[INFO ] 2024-07-24 05:21:24.733 - [任务 18][dummy_test] - Stop connector: first 1721768568033 1058ms, last 1721768568033 1058ms, counts: 4/1058ms, min: 1058, max: 1058, QPS: 4/s 
[INFO ] 2024-07-24 05:21:24.737 - [任务 18][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-0ecb1680-8397-4c4f-8732-454aab68a810 
[INFO ] 2024-07-24 05:21:24.737 - [任务 18][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-0ecb1680-8397-4c4f-8732-454aab68a810 
[INFO ] 2024-07-24 05:21:24.737 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] schema data cleaned 
[INFO ] 2024-07-24 05:21:24.737 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] monitor closed 
[INFO ] 2024-07-24 05:21:24.943 - [任务 18][dummy_test] - Node dummy_test[0ecb1680-8397-4c4f-8732-454aab68a810] close complete, cost 14 ms 
[INFO ] 2024-07-24 05:21:25.166 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 05:21:25.166 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@345e72e8 
[INFO ] 2024-07-24 05:21:25.309 - [任务 18] - Stop task milestones: 669f93c6e29f7d4f8d097853(任务 18)  
[INFO ] 2024-07-24 05:21:25.310 - [任务 18] - Stopped task aspect(s) 
[INFO ] 2024-07-24 05:21:25.310 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 05:21:25.339 - [任务 18] - Remove memory task client succeed, task: 任务 18[669f93c6e29f7d4f8d097853] 
[INFO ] 2024-07-24 05:21:25.340 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[669f93c6e29f7d4f8d097853] 
