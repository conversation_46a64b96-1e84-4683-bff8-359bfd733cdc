[INFO ] 2024-06-27 18:52:56.142 - [任务 3] - Start task milestones: 667d447313fc7a5f4e98006a(任务 3) 
[INFO ] 2024-06-27 18:52:56.143 - [任务 3] - Task initialization... 
[INFO ] 2024-06-27 18:52:56.389 - [任务 3] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-06-27 18:52:56.493 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 18:52:56.493 - [任务 3][test1] - Node test1[1d90a12f-20ad-485e-bc92-ba202f79e62d] start preload schema,table counts: 1 
[INFO ] 2024-06-27 18:52:56.493 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] start preload schema,table counts: 1 
[INFO ] 2024-06-27 18:52:56.493 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 18:52:56.698 - [任务 3][test1] - Node test1[1d90a12f-20ad-485e-bc92-ba202f79e62d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 18:52:57.361 - [任务 3][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-06-27 18:52:57.361 - [任务 3][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-06-27 18:52:57.362 - [任务 3][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 18:52:57.363 - [任务 3][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1719485577360,"lastTimes":1719485577360,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-06-27 18:52:57.457 - [任务 3][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 18:52:57.457 - [任务 3][dummy_test] - Initial sync started 
[INFO ] 2024-06-27 18:52:57.464 - [任务 3][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-06-27 18:52:57.464 - [任务 3][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-06-27 18:52:57.481 - [任务 3][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-06-27 18:52:57.482 - [任务 3][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-06-27 18:52:57.482 - [任务 3][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-27 18:52:57.484 - [任务 3][dummy_test] - Query table 'dummy_test' counts: 1 
[INFO ] 2024-06-27 18:52:57.486 - [任务 3][dummy_test] - Initial sync completed 
[INFO ] 2024-06-27 18:52:57.486 - [任务 3][dummy_test] - Incremental sync starting... 
[INFO ] 2024-06-27 18:52:57.491 - [任务 3][dummy_test] - Initial sync completed 
[INFO ] 2024-06-27 18:52:57.493 - [任务 3][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1719485577360,"lastTimes":1719485577360,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-06-27 18:52:57.497 - [任务 3][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-06-27 18:52:57.497 - [任务 3][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2024-06-27 18:56:23.147 - [任务 3][test1] - Expiring 1 record(s) for test1-0:120001 ms has passed since batch creation
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@8359237: {"after":{"created":1719485661810,"id":"e7c2210e-d8cd-43b5-a102-f197d5427b47","title":"pjoYTRDm"},"containsIllegalDate":false,"referenceTime":1719485661810,"tableId":"dummy_test","time":1719485661810,"type":300}
 - Stack trace: org.apache.kafka.common.errors.TimeoutException: Expiring 1 record(s) for test1-0:120001 ms has passed since batch creation
 
[INFO ] 2024-06-27 18:56:38.442 - [任务 3] - Stop task milestones: 667d447313fc7a5f4e98006a(任务 3)  
[INFO ] 2024-06-27 18:56:38.875 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] running status set to false 
[INFO ] 2024-06-27 18:56:38.875 - [任务 3][dummy_test] - Stop connector 
[INFO ] 2024-06-27 18:56:38.899 - [任务 3][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-3cff8155-1550-449d-86bb-79ed474892cc 
[INFO ] 2024-06-27 18:56:38.899 - [任务 3][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-3cff8155-1550-449d-86bb-79ed474892cc 
[INFO ] 2024-06-27 18:56:38.899 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] schema data cleaned 
[INFO ] 2024-06-27 18:56:38.900 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] monitor closed 
[INFO ] 2024-06-27 18:56:38.900 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] close complete, cost 38 ms 
[INFO ] 2024-06-27 18:56:38.900 - [任务 3][test1] - Node test1[1d90a12f-20ad-485e-bc92-ba202f79e62d] running status set to false 
[INFO ] 2024-06-27 18:56:38.914 - [任务 3][test1] - Exception skipping - The current exception does not match the skip exception strategy, message: error occur when await 
[ERROR] 2024-06-27 18:56:38.915 - [任务 3][test1] - error occur when await <-- Error Message -->
error occur when await

<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.locks.AbstractQueuedSynchronizer.doAcquireSharedNanos(AbstractQueuedSynchronizer.java:1041)
	java.util.concurrent.locks.AbstractQueuedSynchronizer.tryAcquireSharedNanos(AbstractQueuedSynchronizer.java:1332)
	java.util.concurrent.CountDownLatch.await(CountDownLatch.java:277)
	io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:354)
	io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:280)
	...

<-- Full Stack Trace -->
java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:55)
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:359)
	at io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:851)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:803)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:497)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:676)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:589)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:570)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:519)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:488)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:534)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.InterruptedException
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.doAcquireSharedNanos(AbstractQueuedSynchronizer.java:1041)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.tryAcquireSharedNanos(AbstractQueuedSynchronizer.java:1332)
	at java.util.concurrent.CountDownLatch.await(CountDownLatch.java:277)
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:354)
	... 29 more

[INFO ] 2024-06-27 18:57:02.612 - [任务 3][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d90a12f-20ad-485e-bc92-ba202f79e62d 
[INFO ] 2024-06-27 18:57:02.613 - [任务 3][test1] - PDK connector node released: HazelcastTargetPdkDataNode-1d90a12f-20ad-485e-bc92-ba202f79e62d 
[INFO ] 2024-06-27 18:57:02.613 - [任务 3][test1] - Node test1[1d90a12f-20ad-485e-bc92-ba202f79e62d] schema data cleaned 
[INFO ] 2024-06-27 18:57:02.613 - [任务 3][test1] - Node test1[1d90a12f-20ad-485e-bc92-ba202f79e62d] monitor closed 
[INFO ] 2024-06-27 18:57:02.828 - [任务 3][test1] - Node test1[1d90a12f-20ad-485e-bc92-ba202f79e62d] close complete, cost 23712 ms 
[INFO ] 2024-06-27 18:57:06.723 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 18:57:06.723 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-06-27 18:57:06.724 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 18:57:06.758 - [任务 3] - Remove memory task client succeed, task: 任务 3[667d447313fc7a5f4e98006a] 
[INFO ] 2024-06-27 18:57:06.759 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[667d447313fc7a5f4e98006a] 
[INFO ] 2024-06-27 18:57:32.495 - [任务 3] - Start task milestones: 667d447313fc7a5f4e98006a(任务 3) 
[INFO ] 2024-06-27 18:57:32.495 - [任务 3] - Task initialization... 
[INFO ] 2024-06-27 18:57:32.586 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 18:57:32.661 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 18:57:32.662 - [任务 3][test1] - Node test2[1d90a12f-20ad-485e-bc92-ba202f79e62d] start preload schema,table counts: 1 
[INFO ] 2024-06-27 18:57:32.662 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] start preload schema,table counts: 1 
[INFO ] 2024-06-27 18:57:32.662 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 18:57:32.662 - [任务 3][test1] - Node test2[1d90a12f-20ad-485e-bc92-ba202f79e62d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 18:57:33.432 - [任务 3][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-06-27 18:57:33.433 - [任务 3][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-06-27 18:57:33.436 - [任务 3][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 18:57:33.437 - [任务 3][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1719485853429,"lastTimes":1719485853429,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-06-27 18:57:33.508 - [任务 3][dummy_test] - Initial sync started 
[INFO ] 2024-06-27 18:57:33.513 - [任务 3][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-06-27 18:57:33.517 - [任务 3][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-06-27 18:57:33.517 - [任务 3][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-06-27 18:57:33.529 - [任务 3][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-06-27 18:57:33.529 - [任务 3][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-27 18:57:33.534 - [任务 3][dummy_test] - Query table 'dummy_test' counts: 1 
[INFO ] 2024-06-27 18:57:33.534 - [任务 3][dummy_test] - Initial sync completed 
[INFO ] 2024-06-27 18:57:33.534 - [任务 3][dummy_test] - Incremental sync starting... 
[INFO ] 2024-06-27 18:57:33.534 - [任务 3][dummy_test] - Initial sync completed 
[INFO ] 2024-06-27 18:57:33.538 - [任务 3][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1719485853429,"lastTimes":1719485853429,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-06-27 18:57:33.538 - [任务 3][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-06-27 18:57:33.538 - [任务 3][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-06-27 18:57:33.940 - [任务 3][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 19:01:22.009 - [任务 3] - Stop task milestones: 667d447313fc7a5f4e98006a(任务 3)  
[INFO ] 2024-06-27 19:01:22.354 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] running status set to false 
[INFO ] 2024-06-27 19:01:22.370 - [任务 3][dummy_test] - Stop connector 
[INFO ] 2024-06-27 19:01:22.370 - [任务 3][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-3cff8155-1550-449d-86bb-79ed474892cc 
[INFO ] 2024-06-27 19:01:22.371 - [任务 3][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-3cff8155-1550-449d-86bb-79ed474892cc 
[INFO ] 2024-06-27 19:01:22.371 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] schema data cleaned 
[INFO ] 2024-06-27 19:01:22.372 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] monitor closed 
[INFO ] 2024-06-27 19:01:22.373 - [任务 3][dummy_test] - Node dummy_test[3cff8155-1550-449d-86bb-79ed474892cc] close complete, cost 20 ms 
[INFO ] 2024-06-27 19:01:22.373 - [任务 3][test1] - Node test2[1d90a12f-20ad-485e-bc92-ba202f79e62d] running status set to false 
[INFO ] 2024-06-27 19:01:22.386 - [任务 3][test1] - Exception skipping - The current exception does not match the skip exception strategy, message: error occur when await 
[ERROR] 2024-06-27 19:01:22.388 - [任务 3][test1] - error occur when await <-- Error Message -->
error occur when await

<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.locks.AbstractQueuedSynchronizer.doAcquireSharedNanos(AbstractQueuedSynchronizer.java:1041)
	java.util.concurrent.locks.AbstractQueuedSynchronizer.tryAcquireSharedNanos(AbstractQueuedSynchronizer.java:1332)
	java.util.concurrent.CountDownLatch.await(CountDownLatch.java:277)
	io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:354)
	io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:280)
	...

<-- Full Stack Trace -->
java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:55)
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:359)
	at io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:851)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:803)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:497)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:676)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:589)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:570)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:519)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:488)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:534)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.InterruptedException
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.doAcquireSharedNanos(AbstractQueuedSynchronizer.java:1041)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.tryAcquireSharedNanos(AbstractQueuedSynchronizer.java:1332)
	at java.util.concurrent.CountDownLatch.await(CountDownLatch.java:277)
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:354)
	... 29 more

[INFO ] 2024-06-27 19:01:43.468 - [任务 3][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d90a12f-20ad-485e-bc92-ba202f79e62d 
[INFO ] 2024-06-27 19:01:43.469 - [任务 3][test1] - PDK connector node released: HazelcastTargetPdkDataNode-1d90a12f-20ad-485e-bc92-ba202f79e62d 
[INFO ] 2024-06-27 19:01:43.470 - [任务 3][test1] - Node test2[1d90a12f-20ad-485e-bc92-ba202f79e62d] schema data cleaned 
[INFO ] 2024-06-27 19:01:43.471 - [任务 3][test1] - Node test2[1d90a12f-20ad-485e-bc92-ba202f79e62d] monitor closed 
[INFO ] 2024-06-27 19:01:43.471 - [任务 3][test1] - Node test2[1d90a12f-20ad-485e-bc92-ba202f79e62d] close complete, cost 21098 ms 
[INFO ] 2024-06-27 19:01:47.110 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 19:01:47.114 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-06-27 19:01:47.114 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 19:01:47.161 - [任务 3] - Remove memory task client succeed, task: 任务 3[667d447313fc7a5f4e98006a] 
[INFO ] 2024-06-27 19:01:47.162 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[667d447313fc7a5f4e98006a] 
