[INFO ] 2024-07-25 14:26:12.474 - [TestSame2] - Task initialization... 
[INFO ] 2024-07-25 14:26:12.673 - [TestSame2] - Start task milestones: 66a1efb6349bc63fe9d88081(TestSame2) 
[INFO ] 2024-07-25 14:26:12.673 - [TestSame2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:26:12.709 - [TestSame2] - The engine receives TestSame2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:26:12.766 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:26:12.766 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:26:12.766 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:26:12.766 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:26:14.039 - [TestSame2][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:26:14.039 - [TestSame2][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:26:14.060 - [TestSame2][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.060 - [TestSame2][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:26:14.111 - [TestSame2][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.111 - [TestSame2][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:26:14.155 - [TestSame2][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.155 - [TestSame2][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:26:14.198 - [TestSame2][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.198 - [TestSame2][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:26:14.245 - [TestSame2][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.245 - [TestSame2][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:26:14.255 - [TestSame2][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:26:14.255 - [TestSame2][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:26:14.277 - [TestSame2][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:26:14.277 - [TestSame2][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.277 - [TestSame2][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:26:14.279 - [TestSame2][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632550,"pendingScn":40632550,"timestamp":null} 
[INFO ] 2024-07-25 14:26:14.279 - [TestSame2][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:26:14.331 - [TestSame2][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.331 - [TestSame2][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:26:14.352 - [TestSame2][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:26:14.353 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:26:14.387 - [TestSame2][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:14.391 - [TestSame2][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.391 - [TestSame2][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:26:14.456 - [TestSame2][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.457 - [TestSame2][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:26:14.478 - [TestSame2][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:26:14.953 - [TestSame2][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:14.953 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:26:14.954 - [TestSame2][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:14.991 - [TestSame2][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:14.991 - [TestSame2][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:26:14.991 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:26:14.998 - [TestSame2][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.018 - [TestSame2][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:26:15.122 - [TestSame2][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:15.122 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:26:15.123 - [TestSame2][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.162 - [TestSame2][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:26:15.725 - [TestSame2][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:15.725 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:26:15.725 - [TestSame2][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.763 - [TestSame2][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:15.774 - [TestSame2][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:26:15.774 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:26:15.801 - [TestSame2][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.801 - [TestSame2][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:26:17.225 - [TestSame2][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:17.225 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:26:17.225 - [TestSame2][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:17.258 - [TestSame2][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:26:26.061 - [TestSame2][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:26.062 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:26:26.062 - [TestSame2][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:26.105 - [TestSame2][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:26.116 - [TestSame2][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:26:26.116 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:26:26.116 - [TestSame2][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:26.182 - [TestSame2][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:26:26.942 - [TestSame2][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:26.942 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:26:26.942 - [TestSame2][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:26:26.942 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:26:26.944 - [TestSame2][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632550,"pendingScn":40632550,"timestamp":null} 
[INFO ] 2024-07-25 14:26:27.513 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:28.192 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:30.458 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:33.215 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:35.778 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:36.360 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:36.861 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:37.142 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:37.794 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:38.228 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:38.742 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:41.215 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:45.884 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:48.589 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:50.925 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:53.507 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:57.977 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:00.489 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:03.123 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:05.504 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:06.074 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:08.421 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:13.094 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:15.436 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:18.087 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:20.412 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:22.838 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:23.448 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:25.847 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:26.453 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:31.100 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:33.632 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:36.070 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:38.606 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:43.166 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:45.607 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:48.222 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:50.594 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:53.217 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:57.637 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:00.136 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:02.672 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:05.269 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:09.884 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:12.422 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:14.928 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:17.595 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:19.905 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:20.334 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:24.947 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:27.428 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:29.944 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:32.428 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:37.034 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:39.640 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:42.394 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:44.651 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:47.220 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:49.794 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:50.252 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:54.803 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:57.342 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:00.033 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:02.535 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:06.898 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:09.533 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:12.009 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:14.526 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:17.154 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:21.663 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:24.212 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:26.814 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:29.378 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:34.083 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:36.646 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:39.199 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:41.752 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:44.275 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:48.843 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:51.530 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:53.982 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:56.591 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:59.047 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:03.694 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:06.166 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:08.715 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:11.307 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:15.766 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:18.385 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:20.812 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:23.336 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:27.930 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:30.329 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:32.972 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:35.370 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:40.026 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:42.667 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:45.327 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:47.660 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:50.244 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:54.915 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:57.345 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:59.960 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:02.434 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:07.030 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:09.437 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:11.991 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:14.464 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:19.127 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:21.424 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:23.878 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:26.518 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:30.809 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:33.465 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:35.905 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:38.214 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:42.900 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:45.192 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:47.847 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:50.182 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:54.742 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:57.264 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:59.893 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:02.398 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:06.781 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:09.428 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:11.804 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:14.464 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:18.950 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:21.262 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:23.738 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:26.263 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:30.869 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:33.523 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:35.818 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:38.476 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:42.823 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:45.466 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:47.817 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:50.463 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:55.067 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:57.483 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:00.037 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:02.472 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:06.985 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:09.633 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:12.162 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:14.477 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:19.066 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:21.487 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:24.148 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:26.654 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:31.145 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:33.530 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:36.164 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:38.597 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:41.196 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:45.774 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:48.443 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:50.887 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:53.232 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:55.876 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:56.159 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:00.770 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:03.327 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:05.692 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:08.236 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:12.772 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:15.289 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:17.784 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:20.421 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:24.977 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:27.342 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:29.961 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:32.375 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:37.004 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:39.380 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:42.000 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:44.638 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:48.983 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:51.599 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:53.971 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:56.581 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:01.062 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:02.433 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] running status set to false 
[INFO ] 2024-07-25 14:35:02.435 - [TestSame2][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:35:02.455 - [TestSame2][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 14:35:02.455 - [TestSame2][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 14:35:02.456 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] schema data cleaned 
[INFO ] 2024-07-25 14:35:02.456 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] monitor closed 
[INFO ] 2024-07-25 14:35:02.456 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] close complete, cost 26 ms 
[INFO ] 2024-07-25 14:35:02.456 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] running status set to false 
[INFO ] 2024-07-25 14:35:02.469 - [TestSame2][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 14:35:02.469 - [TestSame2][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 14:35:02.469 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] schema data cleaned 
[INFO ] 2024-07-25 14:35:02.469 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] monitor closed 
[INFO ] 2024-07-25 14:35:02.478 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] close complete, cost 13 ms 
[INFO ] 2024-07-25 14:35:02.478 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:02.880 - [TestSame2][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 14:35:06.293 - [TestSame2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:35:06.294 - [TestSame2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2c215053 
[INFO ] 2024-07-25 14:35:06.419 - [TestSame2] - Stop task milestones: 66a1efb6349bc63fe9d88081(TestSame2)  
[INFO ] 2024-07-25 14:35:06.419 - [TestSame2] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:35:06.419 - [TestSame2] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:35:06.441 - [TestSame2] - Remove memory task client succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
[INFO ] 2024-07-25 14:35:06.444 - [TestSame2] - Destroy memory task client cache succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
[INFO ] 2024-07-25 14:36:25.969 - [TestSame2] - Task initialization... 
[INFO ] 2024-07-25 14:36:25.969 - [TestSame2] - Start task milestones: 66a1efb6349bc63fe9d88081(TestSame2) 
[INFO ] 2024-07-25 14:36:26.174 - [TestSame2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:36:26.258 - [TestSame2] - The engine receives TestSame2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:36:26.258 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:36:26.259 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:36:26.259 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:36:26.259 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:36:26.948 - [TestSame2][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:36:26.948 - [TestSame2][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:36:26.972 - [TestSame2][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:26.973 - [TestSame2][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:36:27.034 - [TestSame2][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.034 - [TestSame2][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:36:27.070 - [TestSame2][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.070 - [TestSame2][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:36:27.106 - [TestSame2][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.106 - [TestSame2][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:36:27.128 - [TestSame2][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:36:27.129 - [TestSame2][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:36:27.146 - [TestSame2][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:36:27.146 - [TestSame2][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.146 - [TestSame2][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:36:27.162 - [TestSame2][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632838,"pendingScn":40632838,"timestamp":null} 
[INFO ] 2024-07-25 14:36:27.162 - [TestSame2][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:36:27.181 - [TestSame2][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.181 - [TestSame2][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:36:27.227 - [TestSame2][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:36:27.227 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:36:27.248 - [TestSame2][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:27.249 - [TestSame2][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.249 - [TestSame2][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:36:27.449 - [TestSame2][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:36:27.563 - [TestSame2][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.563 - [TestSame2][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:36:27.606 - [TestSame2][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:36:27.606 - [TestSame2][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:36:28.089 - [TestSame2][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:28.090 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:36:28.090 - [TestSame2][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:28.128 - [TestSame2][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:28.128 - [TestSame2][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:36:28.128 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:36:28.128 - [TestSame2][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:28.226 - [TestSame2][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:36:28.226 - [TestSame2][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:28.226 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:36:28.226 - [TestSame2][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:28.429 - [TestSame2][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:36:28.682 - [TestSame2][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:28.684 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:36:28.684 - [TestSame2][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:28.699 - [TestSame2][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:28.699 - [TestSame2][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:36:28.699 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:36:28.699 - [TestSame2][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:28.900 - [TestSame2][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:36:29.932 - [TestSame2][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:29.932 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:36:30.032 - [TestSame2][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:30.032 - [TestSame2][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:36:38.601 - [TestSame2][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:38.601 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:36:38.623 - [TestSame2][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:38.624 - [TestSame2][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:36:38.633 - [TestSame2][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:38.638 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:36:38.638 - [TestSame2][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:36:38.842 - [TestSame2][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:36:39.149 - [TestSame2][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:36:39.149 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:36:39.150 - [TestSame2][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:36:39.150 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:36:39.355 - [TestSame2][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632838,"pendingScn":40632838,"timestamp":null} 
[INFO ] 2024-07-25 14:36:39.469 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:42.072 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:42.424 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:42.929 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:45.668 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:46.237 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:46.644 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:48.982 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:51.449 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:54.087 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:56.386 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:59.033 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:59.462 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:01.901 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:02.235 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:06.796 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:09.201 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:09.769 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:12.264 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:14.885 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:17.289 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:17.792 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:18.154 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:20.693 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:23.239 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:27.797 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:30.393 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:32.740 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:35.369 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:39.772 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:42.403 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:44.783 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:47.356 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:51.982 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:54.353 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:56.878 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:59.453 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:04.092 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:06.636 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:09.048 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:11.621 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:16.242 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:18.572 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:21.203 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:23.850 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:26.309 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:30.960 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:33.350 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:35.976 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:38.516 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:43.126 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:45.754 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:48.145 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:50.658 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:53.174 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:57.821 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:00.467 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:02.841 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:05.387 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:10.024 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:12.661 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:15.257 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:17.586 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:20.182 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:24.778 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:27.411 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:27.914 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:30.509 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:33.072 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:35.818 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:38.432 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:42.961 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:45.610 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:48.211 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:50.769 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:53.146 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:57.727 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:00.379 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:02.821 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:05.379 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:09.891 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:12.288 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:14.832 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:17.463 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:21.887 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:24.492 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:27.133 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:28.225 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] running status set to false 
[INFO ] 2024-07-25 14:40:28.225 - [TestSame2][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:40:28.253 - [TestSame2][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 14:40:28.253 - [TestSame2][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 14:40:28.254 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] schema data cleaned 
[INFO ] 2024-07-25 14:40:28.254 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] monitor closed 
[INFO ] 2024-07-25 14:40:28.278 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] close complete, cost 35 ms 
[INFO ] 2024-07-25 14:40:28.278 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] running status set to false 
[INFO ] 2024-07-25 14:40:28.278 - [TestSame2][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 14:40:28.278 - [TestSame2][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 14:40:28.278 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] schema data cleaned 
[INFO ] 2024-07-25 14:40:28.278 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] monitor closed 
[INFO ] 2024-07-25 14:40:28.480 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] close complete, cost 21 ms 
[INFO ] 2024-07-25 14:40:30.296 - [TestSame2][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 14:40:30.296 - [TestSame2][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 14:40:30.501 - [TestSame2][DamengSource] - java.lang.RuntimeException: java.lang.RuntimeException: check log file error <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: check log file error
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	... 20 more

[INFO ] 2024-07-25 14:40:31.696 - [TestSame2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:40:31.696 - [TestSame2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@290eedc8 
[INFO ] 2024-07-25 14:40:31.816 - [TestSame2] - Stop task milestones: 66a1efb6349bc63fe9d88081(TestSame2)  
[INFO ] 2024-07-25 14:40:31.816 - [TestSame2] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:40:31.816 - [TestSame2] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:40:31.841 - [TestSame2] - Remove memory task client succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
[INFO ] 2024-07-25 14:40:31.841 - [TestSame2] - Destroy memory task client cache succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
[INFO ] 2024-07-25 14:40:46.489 - [TestSame2] - Task initialization... 
[INFO ] 2024-07-25 14:40:46.693 - [TestSame2] - Start task milestones: 66a1efb6349bc63fe9d88081(TestSame2) 
[INFO ] 2024-07-25 14:40:46.725 - [TestSame2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:40:46.830 - [TestSame2] - The engine receives TestSame2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:40:46.830 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:40:46.830 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:40:46.830 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:40:46.830 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:40:47.483 - [TestSame2][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:40:47.483 - [TestSame2][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:40:47.506 - [TestSame2][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.506 - [TestSame2][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:40:47.581 - [TestSame2][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:40:47.581 - [TestSame2][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:40:47.581 - [TestSame2][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:40:47.593 - [TestSame2][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.616 - [TestSame2][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:40:47.618 - [TestSame2][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632963,"pendingScn":40632963,"timestamp":null} 
[INFO ] 2024-07-25 14:40:47.618 - [TestSame2][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:40:47.643 - [TestSame2][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.644 - [TestSame2][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:40:47.675 - [TestSame2][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:40:47.675 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:40:47.697 - [TestSame2][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:47.697 - [TestSame2][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.769 - [TestSame2][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:40:47.772 - [TestSame2][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.772 - [TestSame2][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:40:47.800 - [TestSame2][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:40:47.840 - [TestSame2][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.840 - [TestSame2][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:40:47.890 - [TestSame2][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.930 - [TestSame2][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:40:47.930 - [TestSame2][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.930 - [TestSame2][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:40:47.963 - [TestSame2][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:40:47.963 - [TestSame2][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:40:48.669 - [TestSame2][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:48.669 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:40:48.669 - [TestSame2][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:48.697 - [TestSame2][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:48.697 - [TestSame2][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:40:48.697 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:40:48.697 - [TestSame2][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:48.818 - [TestSame2][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:40:48.819 - [TestSame2][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:48.819 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:40:48.819 - [TestSame2][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:49.023 - [TestSame2][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:40:49.533 - [TestSame2][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:49.534 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:40:49.534 - [TestSame2][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:49.557 - [TestSame2][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:49.567 - [TestSame2][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:40:49.567 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:40:49.600 - [TestSame2][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:49.600 - [TestSame2][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:40:51.094 - [TestSame2][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:51.094 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:40:51.152 - [TestSame2][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:51.152 - [TestSame2][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:40:58.295 - [TestSame2][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:58.296 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:40:58.296 - [TestSame2][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:58.323 - [TestSame2][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:58.443 - [TestSame2][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:40:58.443 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:40:58.443 - [TestSame2][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:40:58.443 - [TestSame2][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:40:58.923 - [TestSame2][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:40:58.924 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:40:58.924 - [TestSame2][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:40:58.924 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:40:59.126 - [TestSame2][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632963,"pendingScn":40632963,"timestamp":null} 
[INFO ] 2024-07-25 14:40:59.331 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:01.724 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:02.528 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:06.843 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:09.466 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:11.790 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:14.421 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:18.941 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:19.163 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:19.808 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:22.271 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:22.513 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:23.121 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:23.354 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:23.960 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:26.393 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:30.705 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:33.278 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:35.665 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:38.212 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:42.656 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:45.425 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:48.117 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:50.842 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:53.434 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:58.091 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:00.603 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:03.135 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:05.786 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:10.146 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:12.785 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:15.344 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:17.665 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:18.149 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:20.611 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:23.242 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:27.839 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:30.174 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:32.807 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:37.172 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:39.643 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:40.153 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:42.777 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:45.076 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:47.722 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:50.370 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:54.879 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:57.532 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:00.240 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:02.885 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:05.517 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:08.012 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:08.614 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:11.208 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:16.026 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:18.658 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:21.095 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:23.703 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:26.347 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:30.981 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:31.541 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:32.155 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:32.764 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:37.170 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:39.789 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:42.359 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:45.001 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:47.626 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:50.168 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:50.866 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:53.370 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:58.069 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:00.692 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:03.459 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:06.058 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:08.802 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:11.365 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:15.981 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:18.569 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:21.407 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:23.821 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:26.429 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:31.244 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:33.852 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:36.501 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:39.326 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:42.117 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:44.724 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:47.550 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:52.098 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:54.739 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:57.554 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:00.005 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:02.757 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:05.406 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:10.226 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:12.916 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:15.554 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:18.200 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:21.050 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:22.264 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:25.299 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:28.061 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:30.896 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:33.810 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:36.640 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:39.412 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:42.155 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:44.980 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:47.824 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:50.376 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:53.363 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:56.127 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:56.939 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:59.752 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:02.549 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:05.392 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:10.130 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:13.011 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:15.708 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:18.495 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:21.175 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:24.226 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:26.952 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:29.809 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:32.515 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:35.225 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:40.005 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:40.569 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:43.420 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:46.066 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:48.516 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:51.323 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:53.890 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:54.292 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:56.915 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:59.673 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:00.286 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:03.008 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:05.455 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:10.092 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:12.710 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:13.525 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:13.998 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:15.019 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:15.534 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:16.144 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:18.896 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:21.841 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:24.462 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:27.259 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:29.932 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:32.678 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:35.503 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:38.366 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:41.407 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:46.354 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:49.078 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:51.917 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:52.529 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:55.223 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:58.031 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:00.602 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:03.225 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:05.846 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:08.638 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:13.380 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:16.024 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:18.609 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:21.390 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:24.177 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:26.777 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:29.321 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:34.194 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:36.889 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:39.736 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:42.182 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:44.740 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:47.328 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:52.018 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:54.609 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:57.620 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:00.297 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:02.771 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:05.395 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:10.158 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:12.775 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:15.405 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:18.195 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:20.855 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:23.582 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:26.457 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:31.264 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:33.909 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:36.563 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:39.254 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:41.732 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:44.360 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:49.112 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:51.604 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:54.484 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:57.197 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:59.948 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:02.846 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:05.728 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:10.292 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:12.935 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:15.395 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:18.076 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:20.723 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:23.410 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:28.055 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:30.658 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:33.330 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:36.177 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:38.946 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:41.767 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:44.531 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:49.300 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:51.805 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:54.362 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:57.063 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:59.680 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:02.482 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:07.043 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:09.724 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:12.412 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:14.762 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:17.656 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:22.391 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:24.998 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:27.702 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:30.376 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:33.248 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:35.869 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:38.818 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:43.433 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:45.868 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:48.509 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:51.351 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:53.967 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:56.340 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:01.256 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:03.822 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:06.451 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:09.061 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:11.637 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:16.328 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:18.809 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:21.500 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:23.976 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:26.445 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:30.977 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:33.496 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:36.438 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:39.342 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:42.267 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:45.352 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:48.398 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:51.470 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:54.436 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:57.289 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:00.593 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:03.405 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:06.719 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:09.405 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:12.254 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:15.271 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:18.583 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:21.113 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:23.700 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:26.792 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:29.550 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:32.646 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:35.528 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:38.585 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:41.771 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:44.979 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:47.943 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:48.660 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:51.756 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:52.706 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:55.651 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:56.402 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:57.440 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:00.491 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:01.402 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:02.238 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:03.233 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:06.368 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:07.196 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:09.749 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:12.503 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:15.708 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:19.099 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:22.479 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:25.289 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:28.099 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:31.397 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:32.296 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:35.590 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:38.989 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:41.898 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:45.150 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:48.466 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:51.455 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:54.562 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:57.576 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:00.600 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:04.056 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:07.011 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:10.407 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:13.185 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:16.061 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:19.225 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:20.270 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:21.290 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:24.141 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:26.535 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:29.144 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:29.771 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:30.163 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:32.812 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:35.225 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:35.848 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:38.291 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:43.030 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:45.467 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:48.124 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:50.522 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:55.294 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:57.971 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:00.629 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:03.289 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:03.900 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:06.581 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:09.275 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:11.972 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:14.394 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:19.303 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:21.684 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:24.397 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:26.921 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:29.469 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:32.051 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:32.673 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:37.153 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:39.838 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:42.256 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:44.838 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:47.515 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:52.121 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:54.611 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:57.121 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:59.737 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:00.295 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:02.865 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:03.385 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:05.930 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:08.607 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:09.195 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:09.608 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:12.063 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:12.686 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:15.286 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:17.741 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:18.357 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:20.976 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:23.515 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:28.144 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:30.826 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:33.233 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:35.780 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:38.478 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:42.934 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:45.629 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:48.109 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:50.919 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:53.330 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:58.219 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:00.809 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:03.713 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:06.525 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:09.214 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:11.697 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:16.226 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:18.934 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:21.354 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:24.139 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:26.818 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:29.942 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:32.626 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:35.302 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:35.918 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:36.291 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:38.913 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:41.343 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:41.865 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:44.464 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:49.148 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:51.706 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:54.259 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:57.130 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:59.819 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:02.390 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:06.975 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:09.868 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:12.297 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:14.971 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:17.390 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:22.147 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:24.723 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:27.534 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:30.105 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:32.595 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:37.064 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:40.138 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:43.130 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:45.716 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:48.399 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:49.018 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:51.391 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:54.066 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:56.622 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:01.250 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:04.105 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:06.570 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:09.091 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:11.761 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:16.472 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:18.902 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:21.458 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:24.050 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:26.751 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:31.338 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:33.864 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:36.552 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:39.001 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:41.625 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:46.370 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:48.860 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:51.462 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:54.141 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:56.615 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:01.211 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:04.030 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:06.898 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:09.347 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:11.924 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:14.603 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:19.319 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:21.733 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:24.409 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:27.010 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:29.572 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:34.196 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:36.799 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:39.264 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:41.897 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:44.535 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:49.110 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:51.959 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:54.376 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:56.920 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:59.503 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:04.324 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:06.958 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:09.362 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:11.928 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:14.475 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:19.344 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:19.806 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:22.416 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:25.184 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:27.737 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:30.223 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:33.054 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:35.465 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:40.161 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:42.794 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:45.191 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:48.052 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:48.428 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:51.043 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:53.594 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:58.265 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:00.912 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:03.377 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:06.034 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:08.688 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:13.300 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:15.688 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:18.493 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:20.931 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:23.585 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:28.076 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:30.726 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:33.289 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:35.864 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:38.423 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:43.299 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:45.874 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:48.454 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:50.994 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:53.558 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:58.274 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:00.928 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:03.282 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:05.845 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:08.397 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:12.975 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:15.525 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:18.063 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:20.590 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:25.238 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:27.841 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:30.484 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:32.878 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:35.434 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:40.002 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:42.634 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:45.244 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:47.788 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:52.607 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:55.367 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:57.736 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:00.294 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:02.840 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:07.420 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:10.006 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:12.657 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:15.075 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:17.613 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:22.247 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:24.789 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:27.315 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:29.952 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:32.593 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:36.962 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:39.502 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:42.022 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:44.648 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:45.255 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:47.824 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:50.594 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:55.194 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:57.748 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:00.389 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:02.886 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:05.419 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:09.967 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:12.595 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:15.233 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:17.624 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:22.424 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:22.762 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:25.431 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:27.976 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:30.530 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:33.165 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:33.780 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:36.203 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:38.735 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:43.373 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:43.980 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:46.720 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:49.118 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:51.900 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:54.464 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:56.884 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:59.419 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:07:02.248 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:07:02.763 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:07:20.006 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:09:03.418 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:11:59.086 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:31.807 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:32.616 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:33.222 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:33.844 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:34.452 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:34.862 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:35.469 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:35.858 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:38.706 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:43.188 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:45.770 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:48.329 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:51.104 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:53.540 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:58.157 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:00.730 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:03.540 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:06.360 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:08.942 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:13.489 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:15.914 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:18.717 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:21.067 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:23.818 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:28.438 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:31.009 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:33.654 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:36.274 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:38.866 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:43.455 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:46.082 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:48.661 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:51.300 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:53.937 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:58.389 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:00.957 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:03.543 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:06.136 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:08.702 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:13.321 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:15.884 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:18.636 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:21.234 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:23.724 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:28.341 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:30.932 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:33.628 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:36.218 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:38.831 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:41.477 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:46.092 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:48.675 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:51.721 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:52.657 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:55.790 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:56.632 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:01.539 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:04.781 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:07.474 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:10.570 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:13.212 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:15.809 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:18.632 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:21.087 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:23.660 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:28.300 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:31.124 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:33.700 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:36.459 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:38.903 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:43.446 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:44.056 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:44.666 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:49.152 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:51.739 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:54.589 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:57.058 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:00.040 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:03.002 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:05.642 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:10.291 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:12.944 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:15.609 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:18.064 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:20.716 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:21.241 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:23.846 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:28.474 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:31.735 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:32.547 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:32.981 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:35.634 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:38.198 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:38.804 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:43.487 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:46.070 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:48.519 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:48.519 - [TestSame2][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 15:16:48.526 - [TestSame2][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 15:16:48.536 - [TestSame2][DamengSource] - java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常 <-- Error Message -->
java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常

<-- Simple Stack Trace -->
Caused by: java.io.EOFException: null
	dm.jdbc.b.a.c.a(ByteArrayNode.java:132)
	dm.jdbc.b.a.a.a(Buffer.java:365)
	dm.jdbc.b.a.e(DBAccess.java:315)
	dm.jdbc.b.a.a(DBAccess.java:225)
	dm.jdbc.b.a.a(DBAccess.java:701)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: dm.jdbc.driver.DMException: 网络通信异常
	at dm.jdbc.driver.DBError.throwException(DBError.java:759)
	at dm.jdbc.b.a.a(DBAccess.java:235)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:51)
	... 20 more
Caused by: java.io.EOFException
	at dm.jdbc.b.a.c.a(ByteArrayNode.java:132)
	at dm.jdbc.b.a.a.a(Buffer.java:365)
	at dm.jdbc.b.a.e(DBAccess.java:315)
	at dm.jdbc.b.a.a(DBAccess.java:225)
	... 28 more

[INFO ] 2024-07-25 15:16:48.536 - [TestSame2][DamengSource] - Job suspend in error handle 
[INFO ] 2024-07-25 15:16:48.715 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] running status set to false 
[INFO ] 2024-07-25 15:16:48.715 - [TestSame2][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 15:16:48.717 - [TestSame2][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 15:16:48.717 - [TestSame2][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 15:16:48.717 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] schema data cleaned 
[INFO ] 2024-07-25 15:16:48.718 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] monitor closed 
[INFO ] 2024-07-25 15:16:48.718 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] close complete, cost 3 ms 
[INFO ] 2024-07-25 15:16:48.718 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] running status set to false 
[INFO ] 2024-07-25 15:16:48.725 - [TestSame2][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 15:16:48.725 - [TestSame2][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 15:16:48.725 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] schema data cleaned 
[INFO ] 2024-07-25 15:16:48.725 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] monitor closed 
[INFO ] 2024-07-25 15:16:48.726 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] close complete, cost 7 ms 
[INFO ] 2024-07-25 15:16:51.709 - [TestSame2] - Task [TestSame2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-25 15:16:51.736 - [TestSame2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 15:16:51.737 - [TestSame2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@18c7f5b7 
[INFO ] 2024-07-25 15:16:51.865 - [TestSame2] - Stop task milestones: 66a1efb6349bc63fe9d88081(TestSame2)  
[INFO ] 2024-07-25 15:16:51.865 - [TestSame2] - Stopped task aspect(s) 
[INFO ] 2024-07-25 15:16:51.865 - [TestSame2] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 15:16:51.882 - [TestSame2] - Remove memory task client succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
[INFO ] 2024-07-25 15:16:51.882 - [TestSame2] - Destroy memory task client cache succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
[INFO ] 2024-07-25 16:15:15.074 - [TestSame2] - Task initialization... 
[INFO ] 2024-07-25 16:15:15.277 - [TestSame2] - Start task milestones: 66a1efb6349bc63fe9d88081(TestSame2) 
[INFO ] 2024-07-25 16:15:15.277 - [TestSame2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 16:15:15.325 - [TestSame2] - The engine receives TestSame2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 16:15:15.382 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:15:15.382 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:15:15.383 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 16:15:15.383 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 16:15:16.264 - [TestSame2][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 16:15:16.264 - [TestSame2][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 16:15:16.296 - [TestSame2][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 16:15:16.296 - [TestSame2][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 16:15:16.296 - [TestSame2][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 16:15:16.296 - [TestSame2][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.296 - [TestSame2][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 16:15:16.308 - [TestSame2][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40635611,"pendingScn":40635611,"timestamp":null} 
[INFO ] 2024-07-25 16:15:16.308 - [TestSame2][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 16:15:16.372 - [TestSame2][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 16:15:16.372 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 16:15:16.391 - [TestSame2][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:16.391 - [TestSame2][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.391 - [TestSame2][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 16:15:16.447 - [TestSame2][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.447 - [TestSame2][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 16:15:16.485 - [TestSame2][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.486 - [TestSame2][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 16:15:16.543 - [TestSame2][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 16:15:16.544 - [TestSame2][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.544 - [TestSame2][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 16:15:16.583 - [TestSame2][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.583 - [TestSame2][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 16:15:16.637 - [TestSame2][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.637 - [TestSame2][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 16:15:16.678 - [TestSame2][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.678 - [TestSame2][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 16:15:16.713 - [TestSame2][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:15:16.713 - [TestSame2][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 16:15:17.240 - [TestSame2][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:17.240 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 16:15:17.263 - [TestSame2][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:17.264 - [TestSame2][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:17.270 - [TestSame2][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 16:15:17.271 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 16:15:17.273 - [TestSame2][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:17.358 - [TestSame2][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 16:15:17.361 - [TestSame2][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:17.361 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 16:15:17.362 - [TestSame2][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:17.568 - [TestSame2][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 16:15:17.855 - [TestSame2][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:17.855 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 16:15:17.855 - [TestSame2][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:17.869 - [TestSame2][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:17.869 - [TestSame2][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 16:15:17.870 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 16:15:17.870 - [TestSame2][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:17.995 - [TestSame2][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 16:15:19.124 - [TestSame2][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:19.125 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 16:15:19.125 - [TestSame2][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:19.213 - [TestSame2][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 16:15:25.385 - [TestSame2][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:25.385 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 16:15:25.422 - [TestSame2][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:25.422 - [TestSame2][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 16:15:25.428 - [TestSame2][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:25.428 - [TestSame2][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 16:15:25.442 - [TestSame2][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 16:15:25.442 - [TestSame2][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 16:15:25.931 - [TestSame2][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:15:25.931 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 16:15:25.931 - [TestSame2][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 16:15:25.931 - [TestSame2][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 16:15:26.136 - [TestSame2][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40635611,"pendingScn":40635611,"timestamp":null} 
[INFO ] 2024-07-25 16:15:26.534 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:26.964 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:27.555 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:28.234 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:28.843 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:31.179 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:33.615 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:36.170 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:40.598 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:43.098 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:45.505 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:50.355 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:50.810 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:51.084 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:51.545 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:56.073 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:58.665 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:01.035 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:03.657 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:08.115 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:08.502 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:10.811 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:13.410 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:15.692 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:18.316 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:22.650 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:25.294 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:27.623 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:28.016 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:30.062 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] running status set to false 
[INFO ] 2024-07-25 16:16:30.062 - [TestSame2][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 16:16:30.071 - [TestSame2][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 16:16:30.071 - [TestSame2][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-1607f9b5-def8-423b-bfa3-c9662f3e4063 
[INFO ] 2024-07-25 16:16:30.071 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] schema data cleaned 
[INFO ] 2024-07-25 16:16:30.071 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] monitor closed 
[INFO ] 2024-07-25 16:16:30.072 - [TestSame2][DamengSource] - Node DamengSource[1607f9b5-def8-423b-bfa3-c9662f3e4063] close complete, cost 18 ms 
[INFO ] 2024-07-25 16:16:30.072 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] running status set to false 
[INFO ] 2024-07-25 16:16:30.117 - [TestSame2][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:30.117 - [TestSame2][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 16:16:30.117 - [TestSame2][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-c73d1a22-36bd-4d99-af3a-fbdd47c79ed1 
[INFO ] 2024-07-25 16:16:30.118 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] schema data cleaned 
[INFO ] 2024-07-25 16:16:30.118 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] monitor closed 
[INFO ] 2024-07-25 16:16:30.118 - [TestSame2][Mysql3307] - Node Mysql3307[c73d1a22-36bd-4d99-af3a-fbdd47c79ed1] close complete, cost 45 ms 
[INFO ] 2024-07-25 16:16:30.484 - [TestSame2][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 16:16:33.282 - [TestSame2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 16:16:33.282 - [TestSame2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@ce4878f 
[INFO ] 2024-07-25 16:16:33.283 - [TestSame2] - Stop task milestones: 66a1efb6349bc63fe9d88081(TestSame2)  
[INFO ] 2024-07-25 16:16:33.296 - [TestSame2] - Stopped task aspect(s) 
[INFO ] 2024-07-25 16:16:33.296 - [TestSame2] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 16:16:33.332 - [TestSame2] - Remove memory task client succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
[INFO ] 2024-07-25 16:16:33.332 - [TestSame2] - Destroy memory task client cache succeed, task: TestSame2[66a1efb6349bc63fe9d88081] 
