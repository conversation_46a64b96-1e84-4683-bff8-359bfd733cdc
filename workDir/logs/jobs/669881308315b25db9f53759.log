[INFO ] 2024-07-18 10:43:08.035 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Task initialization... 
[INFO ] 2024-07-18 10:43:08.237 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Start task milestones: 669881308315b25db9f53759(t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575) 
[INFO ] 2024-07-18 10:43:08.352 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:43:08.522 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - The engine receives t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:43:08.599 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[cda6967c-8963-4c76-8828-5f098eeeee35] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:43:08.599 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[53ae6aa4-3e40-42c2-b015-b2af311ab44e] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:43:08.600 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[cda6967c-8963-4c76-8828-5f098eeeee35] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:43:08.600 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[53ae6aa4-3e40-42c2-b015-b2af311ab44e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:43:08.903 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:43:09.193 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Source node "qa_mock_1000w_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:43:09.193 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Source node "qa_mock_1000w_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:43:09.193 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:43:09.194 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-18 10:43:09.440 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:43:09.446 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Starting batch read, table name: mock_1000w, offset: null 
[INFO ] 2024-07-18 10:43:09.446 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Table mock_1000w is going to be initial synced 
[INFO ] 2024-07-18 10:43:09.451 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Start mock_1000w batch read 
[INFO ] 2024-07-18 10:43:09.658 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Query table 'mock_1000w' counts: 10000000 
[INFO ] 2024-07-18 10:43:39.458 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Compile mock_1000w batch read 
[INFO ] 2024-07-18 10:43:39.459 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Table [mock_1000w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:43:39.459 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:43:40.480 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[53ae6aa4-3e40-42c2-b015-b2af311ab44e] running status set to false 
[INFO ] 2024-07-18 10:43:40.481 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 10:43:40.493 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[cda6967c-8963-4c76-8828-5f098eeeee35] running status set to false 
[INFO ] 2024-07-18 10:43:40.493 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-53ae6aa4-3e40-42c2-b015-b2af311ab44e 
[INFO ] 2024-07-18 10:43:40.498 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-53ae6aa4-3e40-42c2-b015-b2af311ab44e 
[INFO ] 2024-07-18 10:43:40.500 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[53ae6aa4-3e40-42c2-b015-b2af311ab44e] schema data cleaned 
[INFO ] 2024-07-18 10:43:40.502 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[53ae6aa4-3e40-42c2-b015-b2af311ab44e] monitor closed 
[INFO ] 2024-07-18 10:43:40.507 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[53ae6aa4-3e40-42c2-b015-b2af311ab44e] close complete, cost 32 ms 
[INFO ] 2024-07-18 10:43:40.523 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Stop connector: first 1721270589466 139ms, last 1721270619438 9ms, counts: 10000000/29981ms, min: 2, max: 139, QPS: 344827/s 
[INFO ] 2024-07-18 10:43:40.526 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-cda6967c-8963-4c76-8828-5f098eeeee35 
[INFO ] 2024-07-18 10:43:40.526 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-cda6967c-8963-4c76-8828-5f098eeeee35 
[INFO ] 2024-07-18 10:43:40.528 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[cda6967c-8963-4c76-8828-5f098eeeee35] schema data cleaned 
[INFO ] 2024-07-18 10:43:40.534 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[cda6967c-8963-4c76-8828-5f098eeeee35] monitor closed 
[INFO ] 2024-07-18 10:43:40.535 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[cda6967c-8963-4c76-8828-5f098eeeee35] close complete, cost 46 ms 
[INFO ] 2024-07-18 10:43:43.503 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:43:43.511 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@54af0a5 
[INFO ] 2024-07-18 10:43:43.680 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Stop task milestones: 669881308315b25db9f53759(t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575)  
[INFO ] 2024-07-18 10:43:43.680 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:43:43.680 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:43:43.737 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Remove memory task client succeed, task: t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575[669881308315b25db9f53759] 
[INFO ] 2024-07-18 10:43:43.739 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575] - Destroy memory task client cache succeed, task: t_1.1-mock_to_mock_20fields_1717403468657_3537-1721270575[669881308315b25db9f53759] 
