[TRACE] 2025-05-13 14:39:31.123 - [任务 53] - Task initialization... 
[TRACE] 2025-05-13 14:39:31.125 - [任务 53] - Start task milestones: 6822e89b995bd0727260e25a(任务 53) 
[INFO ] 2025-05-13 14:39:31.323 - [任务 53] - Loading table structure completed 
[TRACE] 2025-05-13 14:39:31.323 - [任务 53] - <PERSON>de performs snapshot read asynchronously 
[TRACE] 2025-05-13 14:39:31.478 - [任务 53] - The engine receives 任务 53 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-13 14:39:31.479 - [任务 53] - Task started 
[TRACE] 2025-05-13 14:39:31.508 - [任务 53][SybaseL] - Node SybaseL[78521fcc-7691-4f9f-80ba-b74016ca3f11] start preload schema,table counts: 1 
[TRACE] 2025-05-13 14:39:31.508 - [任务 53][表编辑] - Node 表编辑[47fb9290-ea9b-4340-bd96-e6f610fef90a] start preload schema,table counts: 1 
[TRACE] 2025-05-13 14:39:31.509 - [任务 53][SybaseL] - Node SybaseL[78521fcc-7691-4f9f-80ba-b74016ca3f11] preload schema finished, cost 0 ms 
[TRACE] 2025-05-13 14:39:31.509 - [任务 53][表编辑] - Node 表编辑[47fb9290-ea9b-4340-bd96-e6f610fef90a] preload schema finished, cost 0 ms 
[TRACE] 2025-05-13 14:39:31.566 - [任务 53][表编辑] - Node table_rename_processor(表编辑: 47fb9290-ea9b-4340-bd96-e6f610fef90a) enable batch process 
[TRACE] 2025-05-13 14:39:31.567 - [任务 53][PG - Copy] - Node PG - Copy[4424596f-de38-45d3-b780-5394ce847b4d] start preload schema,table counts: 1 
[TRACE] 2025-05-13 14:39:31.567 - [任务 53][PG - Copy] - Node PG - Copy[4424596f-de38-45d3-b780-5394ce847b4d] preload schema finished, cost 0 ms 
[INFO ] 2025-05-13 14:39:31.567 - [任务 53][PG - Copy] - Enable partition table support for source database 
[INFO ] 2025-05-13 14:39:32.256 - [任务 53][SybaseL] - Sink connector(SybaseL) initialization completed 
[TRACE] 2025-05-13 14:39:32.259 - [任务 53][SybaseL] - Node(SybaseL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-13 14:39:32.259 - [任务 53][SybaseL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-13 14:39:32.460 - [任务 53][SybaseL] - Apply table structure to target database 
[INFO ] 2025-05-13 14:39:32.499 - [任务 53][PG - Copy] - Source connector(PG - Copy) initialization completed 
[TRACE] 2025-05-13 14:39:32.499 - [任务 53][PG - Copy] - Source node "PG - Copy" read batch size: 100 
[TRACE] 2025-05-13 14:39:32.499 - [任务 53][PG - Copy] - Source node "PG - Copy" event queue capacity: 200 
[TRACE] 2025-05-13 14:39:32.499 - [任务 53][PG - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-13 14:39:32.551 - [任务 53][PG - Copy] - Starting batch read from 1 tables 
[TRACE] 2025-05-13 14:39:32.551 - [任务 53][PG - Copy] - Initial sync started 
[INFO ] 2025-05-13 14:39:32.551 - [任务 53][PG - Copy] - Starting batch read from table: test_timestamp 
[TRACE] 2025-05-13 14:39:32.551 - [任务 53][PG - Copy] - Table test_timestamp is going to be initial synced 
[TRACE] 2025-05-13 14:39:32.553 - [任务 53][PG - Copy] - Query snapshot row size completed: PG - Copy(4424596f-de38-45d3-b780-5394ce847b4d) 
[INFO ] 2025-05-13 14:39:32.558 - [任务 53][PG - Copy] - Table test_timestamp has been completed batch read 
[TRACE] 2025-05-13 14:39:32.559 - [任务 53][PG - Copy] - Initial sync completed 
[INFO ] 2025-05-13 14:39:32.559 - [任务 53][PG - Copy] - Batch read completed. 
[INFO ] 2025-05-13 14:39:32.559 - [任务 53][PG - Copy] - Task run completed 
[TRACE] 2025-05-13 14:41:06.532 - [任务 53][SybaseL] - Process after table "wim_test_timestamp" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-13 14:41:06.532 - [任务 53][SybaseL] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-13 14:41:06.623 - [任务 53][PG - Copy] - Node PG - Copy[4424596f-de38-45d3-b780-5394ce847b4d] running status set to false 
[TRACE] 2025-05-13 14:41:06.662 - [任务 53][表编辑] - Node 表编辑[47fb9290-ea9b-4340-bd96-e6f610fef90a] running status set to false 
[TRACE] 2025-05-13 14:41:06.662 - [任务 53][表编辑] - Node 表编辑[47fb9290-ea9b-4340-bd96-e6f610fef90a] schema data cleaned 
[TRACE] 2025-05-13 14:41:06.662 - [任务 53][表编辑] - Node 表编辑[47fb9290-ea9b-4340-bd96-e6f610fef90a] monitor closed 
[TRACE] 2025-05-13 14:41:06.664 - [任务 53][SybaseL] - Node SybaseL[78521fcc-7691-4f9f-80ba-b74016ca3f11] running status set to false 
[TRACE] 2025-05-13 14:41:06.664 - [任务 53][PG - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_4424596f-de38-45d3-b780-5394ce847b4d_1747118372346 
[TRACE] 2025-05-13 14:41:06.664 - [任务 53][表编辑] - Node 表编辑[47fb9290-ea9b-4340-bd96-e6f610fef90a] close complete, cost 3 ms 
[TRACE] 2025-05-13 14:41:06.664 - [任务 53][PG - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_4424596f-de38-45d3-b780-5394ce847b4d_1747118372346 
[TRACE] 2025-05-13 14:41:06.664 - [任务 53][PG - Copy] - Node PG - Copy[4424596f-de38-45d3-b780-5394ce847b4d] schema data cleaned 
[TRACE] 2025-05-13 14:41:06.665 - [任务 53][PG - Copy] - Node PG - Copy[4424596f-de38-45d3-b780-5394ce847b4d] monitor closed 
[TRACE] 2025-05-13 14:41:06.665 - [任务 53][PG - Copy] - Node PG - Copy[4424596f-de38-45d3-b780-5394ce847b4d] close complete, cost 42 ms 
[TRACE] 2025-05-13 14:41:06.720 - [任务 53][SybaseL] - PDK connector node stopped: HazelcastTargetPdkDataNode_78521fcc-7691-4f9f-80ba-b74016ca3f11_1747118372175 
[TRACE] 2025-05-13 14:41:06.720 - [任务 53][SybaseL] - PDK connector node released: HazelcastTargetPdkDataNode_78521fcc-7691-4f9f-80ba-b74016ca3f11_1747118372175 
[TRACE] 2025-05-13 14:41:06.720 - [任务 53][SybaseL] - Node SybaseL[78521fcc-7691-4f9f-80ba-b74016ca3f11] schema data cleaned 
[TRACE] 2025-05-13 14:41:06.720 - [任务 53][SybaseL] - Node SybaseL[78521fcc-7691-4f9f-80ba-b74016ca3f11] monitor closed 
[TRACE] 2025-05-13 14:41:06.721 - [任务 53][SybaseL] - Node SybaseL[78521fcc-7691-4f9f-80ba-b74016ca3f11] close complete, cost 56 ms 
[TRACE] 2025-05-13 14:41:11.523 - [任务 53] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-13 14:41:11.524 - [任务 53] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57ea66b9 
[TRACE] 2025-05-13 14:41:11.643 - [任务 53] - Stop task milestones: 6822e89b995bd0727260e25a(任务 53)  
[TRACE] 2025-05-13 14:41:11.643 - [任务 53] - Stopped task aspect(s) 
[TRACE] 2025-05-13 14:41:11.643 - [任务 53] - Snapshot order controller have been removed 
[INFO ] 2025-05-13 14:41:11.643 - [任务 53] - Task stopped. 
[TRACE] 2025-05-13 14:41:16.654 - [任务 53] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-13 14:41:16.655 - [任务 53] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57ea66b9 
[TRACE] 2025-05-13 14:41:16.655 - [任务 53] - Stopped task aspect(s) 
[INFO ] 2025-05-13 14:41:16.656 - [任务 53] - Task stopped. 
[TRACE] 2025-05-13 14:41:16.689 - [任务 53] - Remove memory task client succeed, task: 任务 53[6822e89b995bd0727260e25a] 
[TRACE] 2025-05-13 14:41:16.689 - [任务 53] - Destroy memory task client cache succeed, task: 任务 53[6822e89b995bd0727260e25a] 
