[INFO ] 2024-09-14 15:16:01.501 - [测试时区任务] - Start task milestones: 66e537f35e45c90c790e0b66(测试时区任务) 
[INFO ] 2024-09-14 15:16:01.501 - [测试时区任务] - Task initialization... 
[INFO ] 2024-09-14 15:16:05.910 - [测试时区任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-14 15:16:05.911 - [测试时区任务] - The engine receives 测试时区任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-14 15:16:06.338 - [测试时区任务][local3307] - Node local3307[d93bda12-195d-4a80-85bf-ce864728d2a8] start preload schema,table counts: 1 
[INFO ] 2024-09-14 15:16:06.341 - [测试时区任务][Master] - Node Master[1394406f-9bcb-438b-a46e-c5e5b403a67b] start preload schema,table counts: 1 
[INFO ] 2024-09-14 15:16:06.346 - [测试时区任务][local3307] - Node local3307[d93bda12-195d-4a80-85bf-ce864728d2a8] preload schema finished, cost 0 ms 
[INFO ] 2024-09-14 15:16:06.352 - [测试时区任务][Master] - Node Master[1394406f-9bcb-438b-a46e-c5e5b403a67b] preload schema finished, cost 0 ms 
[WARN ] 2024-09-14 15:16:07.324 - [测试时区任务][Master] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726298166997} and {hostPort=localhost:43306, time=1726269366995} 
[INFO ] 2024-09-14 15:16:07.325 - [测试时区任务][Master] - Source node "Master" read batch size: 100 
[INFO ] 2024-09-14 15:16:07.326 - [测试时区任务][Master] - Source node "Master" event queue capacity: 200 
[INFO ] 2024-09-14 15:16:07.327 - [测试时区任务][Master] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-14 15:16:07.347 - [测试时区任务][Master] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000022","position":5763738,"gtidSet":""} 
[INFO ] 2024-09-14 15:16:07.400 - [测试时区任务][Master] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-14 15:16:07.400 - [测试时区任务][Master] - Initial sync started 
[INFO ] 2024-09-14 15:16:07.401 - [测试时区任务][Master] - Starting batch read, table name: testDateTime 
[INFO ] 2024-09-14 15:16:15.786 - [测试时区任务][Master] - Table testDateTime is going to be initial synced 
[INFO ] 2024-09-14 15:16:15.899 - [测试时区任务][local3307] - Node(local3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-14 15:16:15.901 - [测试时区任务][local3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-14 15:16:15.901 - [测试时区任务][Master] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-09-14 15:16:15.937 - [测试时区任务][Master] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-14 15:16:15.937 - [测试时区任务][Master] - Initial sync completed 
[INFO ] 2024-09-14 15:16:15.939 - [测试时区任务][Master] - Incremental sync starting... 
[INFO ] 2024-09-14 15:16:15.939 - [测试时区任务][Master] - Initial sync completed 
[INFO ] 2024-09-14 15:16:16.030 - [测试时区任务][Master] - Starting stream read, table list: [testDateTime], offset: {"filename":"mysql-bin.000022","position":5763738,"gtidSet":""} 
[INFO ] 2024-09-14 15:16:16.032 - [测试时区任务][Master] - Starting mysql cdc, server name: 319b3cdc-6942-473d-b762-739f1a19b00b 
[INFO ] 2024-09-14 15:16:16.241 - [测试时区任务][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1878613711
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 319b3cdc-6942-473d-b762-739f1a19b00b
  database.port: 33306
  threadName: Debezium-Mysql-Connector-319b3cdc-6942-473d-b762-739f1a19b00b
  database.hostname: localhost
  database.password: ********
  name: 319b3cdc-6942-473d-b762-739f1a19b00b
  pdk.offset.string: {"name":"319b3cdc-6942-473d-b762-739f1a19b00b","offset":{"{\"server\":\"319b3cdc-6942-473d-b762-739f1a19b00b\"}":"{\"file\":\"mysql-bin.000022\",\"pos\":5763738,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-14 15:16:16.339 - [测试时区任务][Master] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-09-14 15:29:45.909 - [测试时区任务][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-14 15:29:45.910 - [测试时区任务][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.io.EOFException: Failed to read next byte from position 1312
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-14 15:30:46.328 - [测试时区任务][Master] - Starting mysql cdc, server name: 319b3cdc-6942-473d-b762-739f1a19b00b 
[INFO ] 2024-09-14 15:30:46.456 - [测试时区任务][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1246274098
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 319b3cdc-6942-473d-b762-739f1a19b00b
  database.port: 33306
  threadName: Debezium-Mysql-Connector-319b3cdc-6942-473d-b762-739f1a19b00b
  database.hostname: localhost
  database.password: ********
  name: 319b3cdc-6942-473d-b762-739f1a19b00b
  pdk.offset.string: {"name":"319b3cdc-6942-473d-b762-739f1a19b00b","offset":{"{\"server\":\"319b3cdc-6942-473d-b762-739f1a19b00b\"}":"{\"file\":\"mysql-bin.000022\",\"pos\":5763602,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-14 15:30:46.465 - [测试时区任务][Master] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-14 15:30:46.674 - [测试时区任务][Master] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-09-14 15:30:47.551 - [测试时区任务][local3307] - Table 'testDateTime' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-09-14 15:40:46.235 - [测试时区任务][Master] - Node Master[1394406f-9bcb-438b-a46e-c5e5b403a67b] running status set to false 
[INFO ] 2024-09-14 15:40:46.336 - [测试时区任务][Master] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-14 15:40:46.340 - [测试时区任务][Master] - Mysql binlog reader stopped 
[INFO ] 2024-09-14 15:40:46.340 - [测试时区任务][Master] - Incremental sync completed 
[INFO ] 2024-09-14 15:40:46.351 - [测试时区任务][Master] - PDK connector node stopped: HazelcastSourcePdkDataNode-1394406f-9bcb-438b-a46e-c5e5b403a67b 
[INFO ] 2024-09-14 15:40:46.355 - [测试时区任务][Master] - PDK connector node released: HazelcastSourcePdkDataNode-1394406f-9bcb-438b-a46e-c5e5b403a67b 
[INFO ] 2024-09-14 15:40:46.355 - [测试时区任务][Master] - Node Master[1394406f-9bcb-438b-a46e-c5e5b403a67b] schema data cleaned 
[INFO ] 2024-09-14 15:40:46.370 - [测试时区任务][Master] - Node Master[1394406f-9bcb-438b-a46e-c5e5b403a67b] monitor closed 
[INFO ] 2024-09-14 15:40:46.371 - [测试时区任务][Master] - Node Master[1394406f-9bcb-438b-a46e-c5e5b403a67b] close complete, cost 144 ms 
[INFO ] 2024-09-14 15:40:46.435 - [测试时区任务][local3307] - Node local3307[d93bda12-195d-4a80-85bf-ce864728d2a8] running status set to false 
[INFO ] 2024-09-14 15:40:46.435 - [测试时区任务][local3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-d93bda12-195d-4a80-85bf-ce864728d2a8 
[INFO ] 2024-09-14 15:40:46.435 - [测试时区任务][local3307] - PDK connector node released: HazelcastTargetPdkDataNode-d93bda12-195d-4a80-85bf-ce864728d2a8 
[INFO ] 2024-09-14 15:40:46.437 - [测试时区任务][local3307] - Node local3307[d93bda12-195d-4a80-85bf-ce864728d2a8] schema data cleaned 
[INFO ] 2024-09-14 15:40:46.443 - [测试时区任务][local3307] - Node local3307[d93bda12-195d-4a80-85bf-ce864728d2a8] monitor closed 
[INFO ] 2024-09-14 15:40:46.444 - [测试时区任务][local3307] - Node local3307[d93bda12-195d-4a80-85bf-ce864728d2a8] close complete, cost 71 ms 
[INFO ] 2024-09-14 15:40:47.278 - [测试时区任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-14 15:40:47.280 - [测试时区任务] - Closed task auto recovery instance
  io.DGG.inspect.AutoRecovery@24a11d3d 
[INFO ] 2024-09-14 15:40:47.431 - [测试时区任务] - Stop task milestones: 66e537f35e45c90c790e0b66(测试时区任务)  
[INFO ] 2024-09-14 15:40:47.431 - [测试时区任务] - Stopped task aspect(s) 
[INFO ] 2024-09-14 15:40:47.507 - [测试时区任务] - Snapshot order controller have been removed 
[INFO ] 2024-09-14 15:40:47.509 - [测试时区任务] - Remove memory task client succeed, task: 测试时区任务[66e537f35e45c90c790e0b66] 
[INFO ] 2024-09-14 15:40:47.509 - [测试时区任务] - Destroy memory task client cache succeed, task: 测试时区任务[66e537f35e45c90c790e0b66] 
