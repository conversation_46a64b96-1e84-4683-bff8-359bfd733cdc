[INFO ] 2024-09-20 13:47:18.240 - [任务 8] - Start task milestones: 66ed0c51a2796f48c9399367(任务 8) 
[INFO ] 2024-09-20 13:47:18.240 - [任务 8] - Task initialization... 
[INFO ] 2024-09-20 13:47:18.362 - [任务 8] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-20 13:47:18.458 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-20 13:47:18.513 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:47:18.513 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:47:18.514 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] preload schema finished, cost 1 ms 
[INFO ] 2024-09-20 13:47:18.514 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] preload schema finished, cost 1 ms 
[INFO ] 2024-09-20 13:47:18.716 - [任务 8][mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4 
[ERROR] 2024-09-20 13:47:18.728 - [任务 8][mysql3307] - Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4 <-- Error Message -->
Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more

[INFO ] 2024-09-20 13:47:18.736 - [任务 8][mysql3307] - Job suspend in error handle 
[INFO ] 2024-09-20 13:47:18.990 - [任务 8][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-20 13:47:18.990 - [任务 8][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-20 13:47:18.990 - [任务 8][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-20 13:47:18.996 - [任务 8][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000035","position":377543847,"gtidSet":""} 
[INFO ] 2024-09-20 13:47:18.996 - [任务 8][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-20 13:47:19.059 - [任务 8][Mysql3306] - Initial sync started 
[INFO ] 2024-09-20 13:47:19.059 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] running status set to false 
[INFO ] 2024-09-20 13:47:19.059 - [任务 8][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-20 13:47:19.060 - [任务 8][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-20 13:47:19.060 - [任务 8][Mysql3306] - Incremental sync completed 
[INFO ] 2024-09-20 13:47:19.081 - [任务 8][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-1edff4a5-34a2-4aec-87dd-6f5e593edb89 
[INFO ] 2024-09-20 13:47:19.081 - [任务 8][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-1edff4a5-34a2-4aec-87dd-6f5e593edb89 
[INFO ] 2024-09-20 13:47:19.081 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] schema data cleaned 
[INFO ] 2024-09-20 13:47:19.090 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] monitor closed 
[INFO ] 2024-09-20 13:47:19.090 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] close complete, cost 31 ms 
[INFO ] 2024-09-20 13:47:19.094 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] running status set to false 
[INFO ] 2024-09-20 13:47:19.094 - [任务 8][mysql3307] - PDK connector node stopped: null 
[INFO ] 2024-09-20 13:47:19.094 - [任务 8][mysql3307] - PDK connector node released: null 
[INFO ] 2024-09-20 13:47:19.094 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] schema data cleaned 
[INFO ] 2024-09-20 13:47:19.094 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] monitor closed 
[INFO ] 2024-09-20 13:47:19.094 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] close complete, cost 4 ms 
[INFO ] 2024-09-20 13:47:22.248 - [任务 8] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-20 13:47:22.249 - [任务 8] - Stop task milestones: 66ed0c51a2796f48c9399367(任务 8)  
[INFO ] 2024-09-20 13:47:22.275 - [任务 8] - Stopped task aspect(s) 
[INFO ] 2024-09-20 13:47:22.275 - [任务 8] - Snapshot order controller have been removed 
[INFO ] 2024-09-20 13:47:22.302 - [任务 8] - Remove memory task client succeed, task: 任务 8[66ed0c51a2796f48c9399367] 
[INFO ] 2024-09-20 13:47:22.303 - [任务 8] - Destroy memory task client cache succeed, task: 任务 8[66ed0c51a2796f48c9399367] 
[INFO ] 2024-09-20 13:49:36.542 - [任务 8] - Start task milestones: 66ed0c51a2796f48c9399367(任务 8) 
[INFO ] 2024-09-20 13:49:36.542 - [任务 8] - Task initialization... 
[INFO ] 2024-09-20 13:49:36.748 - [任务 8] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-20 13:49:36.801 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-20 13:49:36.801 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:49:36.862 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:49:36.864 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:49:36.864 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:49:36.971 - [任务 8][mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4 
[ERROR] 2024-09-20 13:49:36.972 - [任务 8][mysql3307] - Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4 <-- Error Message -->
Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_024332ef-3708-486b-9013-0fe3df1ee6f4
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more

[INFO ] 2024-09-20 13:49:37.176 - [任务 8][mysql3307] - Job suspend in error handle 
[INFO ] 2024-09-20 13:49:37.183 - [任务 8][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-20 13:49:37.183 - [任务 8][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-20 13:49:37.183 - [任务 8][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-20 13:49:37.188 - [任务 8][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000035","position":377543847,"gtidSet":""} 
[INFO ] 2024-09-20 13:49:37.189 - [任务 8][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-20 13:49:37.331 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] running status set to false 
[INFO ] 2024-09-20 13:49:37.334 - [任务 8][Mysql3306] - Initial sync started 
[INFO ] 2024-09-20 13:49:37.334 - [任务 8][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-20 13:49:37.335 - [任务 8][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-1edff4a5-34a2-4aec-87dd-6f5e593edb89 
[INFO ] 2024-09-20 13:49:37.335 - [任务 8][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-1edff4a5-34a2-4aec-87dd-6f5e593edb89 
[INFO ] 2024-09-20 13:49:37.337 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] schema data cleaned 
[INFO ] 2024-09-20 13:49:37.337 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] monitor closed 
[INFO ] 2024-09-20 13:49:37.342 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] close complete, cost 46 ms 
[INFO ] 2024-09-20 13:49:37.342 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] running status set to false 
[INFO ] 2024-09-20 13:49:37.346 - [任务 8][mysql3307] - PDK connector node stopped: null 
[INFO ] 2024-09-20 13:49:37.347 - [任务 8][mysql3307] - PDK connector node released: null 
[INFO ] 2024-09-20 13:49:37.347 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] schema data cleaned 
[INFO ] 2024-09-20 13:49:37.348 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] monitor closed 
[INFO ] 2024-09-20 13:49:37.573 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] close complete, cost 6 ms 
[INFO ] 2024-09-20 13:49:37.941 - [任务 8] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-20 13:49:37.941 - [任务 8] - Stop task milestones: 66ed0c51a2796f48c9399367(任务 8)  
[INFO ] 2024-09-20 13:49:37.980 - [任务 8] - Stopped task aspect(s) 
[INFO ] 2024-09-20 13:49:37.981 - [任务 8] - Snapshot order controller have been removed 
[INFO ] 2024-09-20 13:49:37.999 - [任务 8] - Remove memory task client succeed, task: 任务 8[66ed0c51a2796f48c9399367] 
[INFO ] 2024-09-20 13:49:38.002 - [任务 8] - Destroy memory task client cache succeed, task: 任务 8[66ed0c51a2796f48c9399367] 
[INFO ] 2024-09-20 13:50:40.406 - [任务 8] - Start task milestones: 66ed0c51a2796f48c9399367(任务 8) 
[INFO ] 2024-09-20 13:50:40.407 - [任务 8] - Task initialization... 
[INFO ] 2024-09-20 13:50:40.509 - [任务 8] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-20 13:50:40.580 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-20 13:50:40.580 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:50:40.580 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:50:40.580 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:50:40.782 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:50:41.532 - [任务 8][mysql3307] - Node(mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-20 13:50:41.532 - [任务 8][mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-20 13:50:41.658 - [任务 8][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-20 13:50:41.658 - [任务 8][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-20 13:50:41.659 - [任务 8][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-20 13:50:41.661 - [任务 8][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000035","position":377543847,"gtidSet":""} 
[INFO ] 2024-09-20 13:50:41.721 - [任务 8][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-20 13:50:41.721 - [任务 8][Mysql3306] - Initial sync started 
[INFO ] 2024-09-20 13:50:41.722 - [任务 8][Mysql3306] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-09-20 13:50:41.723 - [任务 8][Mysql3306] - Table POLICY is going to be initial synced 
[INFO ] 2024-09-20 13:50:41.836 - [任务 8][Mysql3306] - Query table 'POLICY' counts: 601 
[INFO ] 2024-09-20 13:50:41.837 - [任务 8][Mysql3306] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-20 13:50:41.837 - [任务 8][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-20 13:50:41.838 - [任务 8][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-20 13:50:41.839 - [任务 8][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-20 13:50:41.839 - [任务 8][Mysql3306] - Starting stream read, table list: [POLICY], offset: {"filename":"binlog.000035","position":377543847,"gtidSet":""} 
[INFO ] 2024-09-20 13:50:41.869 - [任务 8][Mysql3306] - Starting mysql cdc, server name: 92ff4d6f-fc2c-4b16-a399-c7f68ff1b047 
[INFO ] 2024-09-20 13:50:41.870 - [任务 8][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1019287403
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 92ff4d6f-fc2c-4b16-a399-c7f68ff1b047
  database.port: 3306
  threadName: Debezium-Mysql-Connector-92ff4d6f-fc2c-4b16-a399-c7f68ff1b047
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: 92ff4d6f-fc2c-4b16-a399-c7f68ff1b047
  pdk.offset.string: {"name":"92ff4d6f-fc2c-4b16-a399-c7f68ff1b047","offset":{"{\"server\":\"92ff4d6f-fc2c-4b16-a399-c7f68ff1b047\"}":"{\"file\":\"binlog.000035\",\"pos\":377543847,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-20 13:50:42.080 - [任务 8][Mysql3306] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-09-20 13:51:10.579 - [任务 8] - Stop task milestones: 66ed0c51a2796f48c9399367(任务 8)  
[INFO ] 2024-09-20 13:51:10.987 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] running status set to false 
[INFO ] 2024-09-20 13:51:10.998 - [任务 8][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-20 13:51:10.998 - [任务 8][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-09-20 13:51:11.028 - [任务 8][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-1edff4a5-34a2-4aec-87dd-6f5e593edb89 
[INFO ] 2024-09-20 13:51:11.028 - [任务 8][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-1edff4a5-34a2-4aec-87dd-6f5e593edb89 
[INFO ] 2024-09-20 13:51:11.028 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] schema data cleaned 
[INFO ] 2024-09-20 13:51:11.028 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] monitor closed 
[INFO ] 2024-09-20 13:51:11.032 - [任务 8][Mysql3306] - Node Mysql3306[1edff4a5-34a2-4aec-87dd-6f5e593edb89] close complete, cost 107 ms 
[INFO ] 2024-09-20 13:51:11.033 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] running status set to false 
[INFO ] 2024-09-20 13:51:11.046 - [任务 8][mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-024332ef-3708-486b-9013-0fe3df1ee6f4 
[INFO ] 2024-09-20 13:51:11.046 - [任务 8][mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-024332ef-3708-486b-9013-0fe3df1ee6f4 
[INFO ] 2024-09-20 13:51:11.046 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] schema data cleaned 
[INFO ] 2024-09-20 13:51:11.047 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] monitor closed 
[INFO ] 2024-09-20 13:51:11.050 - [任务 8][mysql3307] - Node mysql3307[024332ef-3708-486b-9013-0fe3df1ee6f4] close complete, cost 16 ms 
[INFO ] 2024-09-20 13:51:15.599 - [任务 8] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-20 13:51:15.599 - [任务 8] - Stopped task aspect(s) 
[INFO ] 2024-09-20 13:51:15.600 - [任务 8] - Snapshot order controller have been removed 
[INFO ] 2024-09-20 13:51:15.672 - [任务 8] - Remove memory task client succeed, task: 任务 8[66ed0c51a2796f48c9399367] 
[INFO ] 2024-09-20 13:51:15.673 - [任务 8] - Destroy memory task client cache succeed, task: 任务 8[66ed0c51a2796f48c9399367] 
