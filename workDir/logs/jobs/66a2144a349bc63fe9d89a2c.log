[INFO ] 2024-07-26 10:10:27.471 - [任务 31] - Task initialization... 
[INFO ] 2024-07-26 10:10:27.485 - [任务 31] - Start task milestones: 66a2144a349bc63fe9d89a2c(任务 31) 
[INFO ] 2024-07-26 10:10:28.711 - [任务 31] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 10:10:29.120 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 10:10:29.586 - [任务 31][Mysql] - Node Mysql[0e07f547-391b-4baa-9e52-4a891fbe41ce] start preload schema,table counts: 1 
[INFO ] 2024-07-26 10:10:29.588 - [任务 31][Mysql3307] - Node Mysql3307[91655e8e-bac0-4d20-a1d2-c355e141c079] start preload schema,table counts: 1 
[INFO ] 2024-07-26 10:10:29.589 - [任务 31][Mysql] - Node <PERSON>l[0e07f547-391b-4baa-9e52-4a891fbe41ce] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 10:10:29.590 - [任务 31][Mysql3307] - Node Mysql3307[91655e8e-bac0-4d20-a1d2-c355e141c079] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 10:10:30.766 - [任务 31][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-26 10:10:30.767 - [任务 31][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 10:10:30.770 - [任务 31][Mysql3307] - Source node "Mysql3307" read batch size: 100 
[INFO ] 2024-07-26 10:10:30.771 - [任务 31][Mysql3307] - Source node "Mysql3307" event queue capacity: 200 
[INFO ] 2024-07-26 10:10:30.771 - [任务 31][Mysql3307] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-26 10:10:30.799 - [任务 31][Mysql3307] - batch offset found: {},stream offset found: {"name":"7de7430f-a6f3-4a28-aff3-fd3cf46a259d","offset":{"{\"server\":\"7de7430f-a6f3-4a28-aff3-fd3cf46a259d\"}":"{\"ts_sec\":1721904607,\"file\":\"binlog.000020\",\"pos\":214548423,\"server_id\":1}"}} 
[INFO ] 2024-07-26 10:10:30.799 - [任务 31][Mysql3307] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 10:10:30.883 - [任务 31][Mysql3307] - Incremental sync starting... 
[INFO ] 2024-07-26 10:10:30.885 - [任务 31][Mysql3307] - Initial sync completed 
[INFO ] 2024-07-26 10:10:30.886 - [任务 31][Mysql3307] - Starting stream read, table list: [bmsql_customer_test], offset: {"name":"7de7430f-a6f3-4a28-aff3-fd3cf46a259d","offset":{"{\"server\":\"7de7430f-a6f3-4a28-aff3-fd3cf46a259d\"}":"{\"ts_sec\":1721904607,\"file\":\"binlog.000020\",\"pos\":214548423,\"server_id\":1}"}} 
[INFO ] 2024-07-26 10:10:30.949 - [任务 31][Mysql3307] - Starting mysql cdc, server name: 7de7430f-a6f3-4a28-aff3-fd3cf46a259d 
[INFO ] 2024-07-26 10:10:30.950 - [任务 31][Mysql3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 597180326
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 7de7430f-a6f3-4a28-aff3-fd3cf46a259d
  database.port: 3307
  threadName: Debezium-Mysql-Connector-7de7430f-a6f3-4a28-aff3-fd3cf46a259d
  database.hostname: localhost
  database.password: ********
  name: 7de7430f-a6f3-4a28-aff3-fd3cf46a259d
  pdk.offset.string: {"name":"7de7430f-a6f3-4a28-aff3-fd3cf46a259d","offset":{"{\"server\":\"7de7430f-a6f3-4a28-aff3-fd3cf46a259d\"}":"{\"ts_sec\":1721904607,\"file\":\"binlog.000020\",\"pos\":214548423,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.bmsql_customer_test
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-26 10:10:31.765 - [任务 31][Mysql3307] - Connector Mysql incremental start succeed, tables: [bmsql_customer_test], data change syncing 
[INFO ] 2024-07-26 13:15:31.057 - [任务 31][Mysql3307] - Node Mysql3307[91655e8e-bac0-4d20-a1d2-c355e141c079] running status set to false 
[INFO ] 2024-07-26 13:15:31.086 - [任务 31][Mysql3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-26 13:15:31.086 - [任务 31][Mysql3307] - Mysql binlog reader stopped 
[INFO ] 2024-07-26 13:15:31.087 - [任务 31][Mysql3307] - Incremental sync completed 
[INFO ] 2024-07-26 13:15:31.097 - [任务 31][Mysql3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-91655e8e-bac0-4d20-a1d2-c355e141c079 
[INFO ] 2024-07-26 13:15:31.097 - [任务 31][Mysql3307] - PDK connector node released: HazelcastSourcePdkDataNode-91655e8e-bac0-4d20-a1d2-c355e141c079 
[INFO ] 2024-07-26 13:15:31.098 - [任务 31][Mysql3307] - Node Mysql3307[91655e8e-bac0-4d20-a1d2-c355e141c079] schema data cleaned 
[INFO ] 2024-07-26 13:15:31.101 - [任务 31][Mysql3307] - Node Mysql3307[91655e8e-bac0-4d20-a1d2-c355e141c079] monitor closed 
[INFO ] 2024-07-26 13:15:31.101 - [任务 31][Mysql3307] - Node Mysql3307[91655e8e-bac0-4d20-a1d2-c355e141c079] close complete, cost 75 ms 
[INFO ] 2024-07-26 13:15:31.185 - [任务 31][Mysql] - Node Mysql[0e07f547-391b-4baa-9e52-4a891fbe41ce] running status set to false 
[INFO ] 2024-07-26 13:15:31.185 - [任务 31][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-0e07f547-391b-4baa-9e52-4a891fbe41ce 
[INFO ] 2024-07-26 13:15:31.185 - [任务 31][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-0e07f547-391b-4baa-9e52-4a891fbe41ce 
[INFO ] 2024-07-26 13:15:31.185 - [任务 31][Mysql] - Node Mysql[0e07f547-391b-4baa-9e52-4a891fbe41ce] schema data cleaned 
[INFO ] 2024-07-26 13:15:31.186 - [任务 31][Mysql] - Node Mysql[0e07f547-391b-4baa-9e52-4a891fbe41ce] monitor closed 
[INFO ] 2024-07-26 13:15:31.186 - [任务 31][Mysql] - Node Mysql[0e07f547-391b-4baa-9e52-4a891fbe41ce] close complete, cost 83 ms 
[INFO ] 2024-07-26 13:15:33.739 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 13:15:33.741 - [任务 31] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2504a57c 
[INFO ] 2024-07-26 13:15:33.750 - [任务 31] - Stop task milestones: 66a2144a349bc63fe9d89a2c(任务 31)  
[INFO ] 2024-07-26 13:15:33.878 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-07-26 13:15:33.878 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 13:15:33.905 - [任务 31] - Remove memory task client succeed, task: 任务 31[66a2144a349bc63fe9d89a2c] 
[INFO ] 2024-07-26 13:15:33.905 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[66a2144a349bc63fe9d89a2c] 
