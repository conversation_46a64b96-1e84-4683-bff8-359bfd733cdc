[INFO ] 2024-07-29 04:03:52.134 - [testDecimal] - Task initialization... 
[INFO ] 2024-07-29 04:03:52.135 - [testDecimal] - Start task milestones: 66a6a3f466c385580759bd86(testDecimal) 
[INFO ] 2024-07-29 04:03:52.398 - [testDecimal] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 04:03:52.398 - [testDecimal] - The engine receives testDecimal task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 04:03:52.429 - [testDecimal][TDecimal] - Node TDecimal[482b4cfd-a882-4b71-a68d-d394c1cd45ed] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:03:52.429 - [testDecimal][testDecimal] - Node testDecimal[24e2933b-1966-481c-9bed-7b87db4c63e7] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:03:52.429 - [testDecimal][TDecimal] - Node TDecimal[482b4cfd-a882-4b71-a68d-d394c1cd45ed] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 04:03:52.429 - [testDecimal][testDecimal] - Node testDecimal[24e2933b-1966-481c-9bed-7b87db4c63e7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 04:03:53.236 - [testDecimal][TDecimal] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 04:03:53.307 - [testDecimal][testDecimal] - Source node "testDecimal" read batch size: 100 
[INFO ] 2024-07-29 04:03:53.308 - [testDecimal][testDecimal] - Source node "testDecimal" event queue capacity: 200 
[INFO ] 2024-07-29 04:03:53.309 - [testDecimal][testDecimal] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 04:03:53.309 - [testDecimal][testDecimal] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 04:03:53.309 - [testDecimal][testDecimal] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 04:03:53.380 - [testDecimal][testDecimal] - Initial sync started 
[INFO ] 2024-07-29 04:03:53.381 - [testDecimal][testDecimal] - Starting batch read, table name: testDecimal, offset: null 
[INFO ] 2024-07-29 04:03:53.381 - [testDecimal][testDecimal] - Table testDecimal is going to be initial synced 
[INFO ] 2024-07-29 04:03:53.405 - [testDecimal][testDecimal] - Table [testDecimal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 04:03:53.415 - [testDecimal][testDecimal] - Query table 'testDecimal' counts: 1 
[INFO ] 2024-07-29 04:03:53.415 - [testDecimal][testDecimal] - Initial sync completed 
[INFO ] 2024-07-29 04:03:53.924 - [testDecimal][testDecimal] - Node testDecimal[24e2933b-1966-481c-9bed-7b87db4c63e7] running status set to false 
[INFO ] 2024-07-29 04:03:53.924 - [testDecimal][TDecimal] - Node TDecimal[482b4cfd-a882-4b71-a68d-d394c1cd45ed] running status set to false 
[INFO ] 2024-07-29 04:03:53.958 - [testDecimal][testDecimal] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 04:03:53.958 - [testDecimal][testDecimal] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 04:03:53.958 - [testDecimal][TDecimal] - PDK connector node stopped: HazelcastTargetPdkDataNode-482b4cfd-a882-4b71-a68d-d394c1cd45ed 
[INFO ] 2024-07-29 04:03:53.960 - [testDecimal][TDecimal] - PDK connector node released: HazelcastTargetPdkDataNode-482b4cfd-a882-4b71-a68d-d394c1cd45ed 
[INFO ] 2024-07-29 04:03:53.961 - [testDecimal][TDecimal] - Node TDecimal[482b4cfd-a882-4b71-a68d-d394c1cd45ed] schema data cleaned 
[INFO ] 2024-07-29 04:03:53.961 - [testDecimal][TDecimal] - Node TDecimal[482b4cfd-a882-4b71-a68d-d394c1cd45ed] monitor closed 
[INFO ] 2024-07-29 04:03:53.978 - [testDecimal][TDecimal] - Node TDecimal[482b4cfd-a882-4b71-a68d-d394c1cd45ed] close complete, cost 39 ms 
[INFO ] 2024-07-29 04:03:53.978 - [testDecimal][testDecimal] - PDK connector node stopped: HazelcastSourcePdkDataNode-24e2933b-1966-481c-9bed-7b87db4c63e7 
[INFO ] 2024-07-29 04:03:53.978 - [testDecimal][testDecimal] - PDK connector node released: HazelcastSourcePdkDataNode-24e2933b-1966-481c-9bed-7b87db4c63e7 
[INFO ] 2024-07-29 04:03:53.979 - [testDecimal][testDecimal] - Node testDecimal[24e2933b-1966-481c-9bed-7b87db4c63e7] schema data cleaned 
[INFO ] 2024-07-29 04:03:53.979 - [testDecimal][testDecimal] - Node testDecimal[24e2933b-1966-481c-9bed-7b87db4c63e7] monitor closed 
[INFO ] 2024-07-29 04:03:53.979 - [testDecimal][testDecimal] - Node testDecimal[24e2933b-1966-481c-9bed-7b87db4c63e7] close complete, cost 57 ms 
[INFO ] 2024-07-29 04:03:58.653 - [testDecimal] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 04:03:58.654 - [testDecimal] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f140635 
[INFO ] 2024-07-29 04:03:58.767 - [testDecimal] - Stop task milestones: 66a6a3f466c385580759bd86(testDecimal)  
[INFO ] 2024-07-29 04:03:58.795 - [testDecimal] - Stopped task aspect(s) 
[INFO ] 2024-07-29 04:03:58.795 - [testDecimal] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 04:03:58.819 - [testDecimal] - Remove memory task client succeed, task: testDecimal[66a6a3f466c385580759bd86] 
[INFO ] 2024-07-29 04:03:58.822 - [testDecimal] - Destroy memory task client cache succeed, task: testDecimal[66a6a3f466c385580759bd86] 
