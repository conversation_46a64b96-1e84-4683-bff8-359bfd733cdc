[INFO ] 2024-09-11 11:33:40.919 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:33:40.949 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:33:40.950 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:33:40.950 - [任务 2(101)][9dcdffa4-7f51-4880-ae93-935cf827b4bf] - Node 9dcdffa4-7f51-4880-ae93-935cf827b4bf[9dcdffa4-7f51-4880-ae93-935cf827b4bf] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:33:40.951 - [任务 2(101)][9dcdffa4-7f51-4880-ae93-935cf827b4bf] - Node 9dcdffa4-7f51-4880-ae93-935cf827b4bf[9dcdffa4-7f51-4880-ae93-935cf827b4bf] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:33:41.063 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 108 ms 
[INFO ] 2024-09-11 11:33:41.063 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 108 ms 
[INFO ] 2024-09-11 11:33:41.063 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:33:41.198 - [任务 2(101)][AA_0530_T1] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[ERROR] 2024-09-11 11:33:41.216 - [任务 2(101)][AA_0530_T1] - start source runner failed: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d

<-- Simple Stack Trace -->
Caused by: io.DGG.entity.error.CoreException: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	io.DGG.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.DGG.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.DGG.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.DGG.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.DGG.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.DGG.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-09-11 11:33:43.789 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:33:43.789 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:33:43.789 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:33:43.789 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:33:43.792 - [任务 2(101)][9dcdffa4-7f51-4880-ae93-935cf827b4bf] - Node 9dcdffa4-7f51-4880-ae93-935cf827b4bf[9dcdffa4-7f51-4880-ae93-935cf827b4bf] running status set to false 
[INFO ] 2024-09-11 11:33:43.793 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: null 
[INFO ] 2024-09-11 11:33:43.793 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 4 ms 
[INFO ] 2024-09-11 11:33:43.793 - [任务 2(101)][9dcdffa4-7f51-4880-ae93-935cf827b4bf] - Node 9dcdffa4-7f51-4880-ae93-935cf827b4bf[9dcdffa4-7f51-4880-ae93-935cf827b4bf] schema data cleaned 
[INFO ] 2024-09-11 11:33:43.793 - [任务 2(101)][AA_0530_T1] - PDK connector node released: null 
[INFO ] 2024-09-11 11:33:43.793 - [任务 2(101)][9dcdffa4-7f51-4880-ae93-935cf827b4bf] - Node 9dcdffa4-7f51-4880-ae93-935cf827b4bf[9dcdffa4-7f51-4880-ae93-935cf827b4bf] monitor closed 
[INFO ] 2024-09-11 11:33:43.793 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:33:43.794 - [任务 2(101)][9dcdffa4-7f51-4880-ae93-935cf827b4bf] - Node 9dcdffa4-7f51-4880-ae93-935cf827b4bf[9dcdffa4-7f51-4880-ae93-935cf827b4bf] close complete, cost 3 ms 
[INFO ] 2024-09-11 11:33:43.794 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:33:43.817 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 13 ms 
[INFO ] 2024-09-11 11:33:43.818 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:33:43.822 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:33:43.822 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:33:43.822 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 2957ms 
[INFO ] 2024-09-11 11:33:50.950 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:33:50.950 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:33:50.950 - [任务 2(101)][473cade3-4aa7-41ec-9f67-b1225f4b62cc] - Node 473cade3-4aa7-41ec-9f67-b1225f4b62cc[473cade3-4aa7-41ec-9f67-b1225f4b62cc] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:33:50.950 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:33:50.950 - [任务 2(101)][473cade3-4aa7-41ec-9f67-b1225f4b62cc] - Node 473cade3-4aa7-41ec-9f67-b1225f4b62cc[473cade3-4aa7-41ec-9f67-b1225f4b62cc] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:33:50.984 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 33 ms 
[INFO ] 2024-09-11 11:33:50.985 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 34 ms 
[INFO ] 2024-09-11 11:33:51.191 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:33:51.250 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:33:51.250 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:33:51.252 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:33:51.252 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:33:51.252 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:33:51.253 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 46 ms 
[INFO ] 2024-09-11 11:33:51.715 - [任务 2(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: System is not defined 
[ERROR] 2024-09-11 11:33:51.917 - [任务 2(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: System is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: System is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: System is not defined
	<js>.process(<eval>:2)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: System is not defined
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: System is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: System is not defined
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-11 11:33:54.262 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:33:54.293 - [任务 2(101)][473cade3-4aa7-41ec-9f67-b1225f4b62cc] - Node 473cade3-4aa7-41ec-9f67-b1225f4b62cc[473cade3-4aa7-41ec-9f67-b1225f4b62cc] running status set to false 
[INFO ] 2024-09-11 11:33:54.293 - [任务 2(101)][473cade3-4aa7-41ec-9f67-b1225f4b62cc] - Node 473cade3-4aa7-41ec-9f67-b1225f4b62cc[473cade3-4aa7-41ec-9f67-b1225f4b62cc] schema data cleaned 
[INFO ] 2024-09-11 11:33:54.293 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-6330c937-395e-4e71-892b-9d3a957b36f6 
[INFO ] 2024-09-11 11:33:54.293 - [任务 2(101)][473cade3-4aa7-41ec-9f67-b1225f4b62cc] - Node 473cade3-4aa7-41ec-9f67-b1225f4b62cc[473cade3-4aa7-41ec-9f67-b1225f4b62cc] monitor closed 
[INFO ] 2024-09-11 11:33:54.294 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-6330c937-395e-4e71-892b-9d3a957b36f6 
[INFO ] 2024-09-11 11:33:54.294 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:33:54.294 - [任务 2(101)][473cade3-4aa7-41ec-9f67-b1225f4b62cc] - Node 473cade3-4aa7-41ec-9f67-b1225f4b62cc[473cade3-4aa7-41ec-9f67-b1225f4b62cc] close complete, cost 16 ms 
[INFO ] 2024-09-11 11:33:54.297 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:33:54.298 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:33:54.319 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 38 ms 
[INFO ] 2024-09-11 11:33:54.319 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:33:54.320 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:33:54.320 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:33:54.529 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 3435ms 
[INFO ] 2024-09-11 11:34:10.189 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:34:10.245 - [任务 2(101)][ac21b994-9b9c-4423-83dd-93e03da9154d] - Node ac21b994-9b9c-4423-83dd-93e03da9154d[ac21b994-9b9c-4423-83dd-93e03da9154d] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:34:10.245 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:10.245 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:10.245 - [任务 2(101)][ac21b994-9b9c-4423-83dd-93e03da9154d] - Node ac21b994-9b9c-4423-83dd-93e03da9154d[ac21b994-9b9c-4423-83dd-93e03da9154d] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:34:10.258 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 13 ms 
[INFO ] 2024-09-11 11:34:10.258 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:34:10.459 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 15 ms 
[INFO ] 2024-09-11 11:34:10.459 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:34:10.466 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:10.467 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:10.467 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:34:10.467 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:34:10.468 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 9 ms 
[INFO ] 2024-09-11 11:34:10.774 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:34:10.776 - [任务 2(101)][ac21b994-9b9c-4423-83dd-93e03da9154d] - Node ac21b994-9b9c-4423-83dd-93e03da9154d[ac21b994-9b9c-4423-83dd-93e03da9154d] running status set to false 
[INFO ] 2024-09-11 11:34:10.776 - [任务 2(101)][ac21b994-9b9c-4423-83dd-93e03da9154d] - Node ac21b994-9b9c-4423-83dd-93e03da9154d[ac21b994-9b9c-4423-83dd-93e03da9154d] schema data cleaned 
[INFO ] 2024-09-11 11:34:10.776 - [任务 2(101)][ac21b994-9b9c-4423-83dd-93e03da9154d] - Node ac21b994-9b9c-4423-83dd-93e03da9154d[ac21b994-9b9c-4423-83dd-93e03da9154d] monitor closed 
[INFO ] 2024-09-11 11:34:10.777 - [任务 2(101)][ac21b994-9b9c-4423-83dd-93e03da9154d] - Node ac21b994-9b9c-4423-83dd-93e03da9154d[ac21b994-9b9c-4423-83dd-93e03da9154d] close complete, cost 3 ms 
[INFO ] 2024-09-11 11:34:10.787 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-5e35b67e-e979-4a0b-985f-68a456e63965 
[INFO ] 2024-09-11 11:34:10.788 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-5e35b67e-e979-4a0b-985f-68a456e63965 
[INFO ] 2024-09-11 11:34:10.788 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:34:10.791 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:34:10.791 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:34:10.809 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 26 ms 
[INFO ] 2024-09-11 11:34:10.809 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:34:10.809 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:34:10.809 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:34:10.809 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 636ms 
[INFO ] 2024-09-11 11:34:21.467 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:34:21.563 - [任务 2(101)][43a088e1-dee5-422f-81e3-be6d25cd85d9] - Node 43a088e1-dee5-422f-81e3-be6d25cd85d9[43a088e1-dee5-422f-81e3-be6d25cd85d9] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:34:21.564 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:21.564 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:21.564 - [任务 2(101)][43a088e1-dee5-422f-81e3-be6d25cd85d9] - Node 43a088e1-dee5-422f-81e3-be6d25cd85d9[43a088e1-dee5-422f-81e3-be6d25cd85d9] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:34:21.579 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 19 ms 
[INFO ] 2024-09-11 11:34:21.580 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 22 ms 
[INFO ] 2024-09-11 11:34:21.784 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:34:21.841 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:34:21.842 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:21.842 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:21.842 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:34:21.842 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:34:22.043 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 20 ms 
[INFO ] 2024-09-11 11:34:22.085 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:34:22.090 - [任务 2(101)][43a088e1-dee5-422f-81e3-be6d25cd85d9] - Node 43a088e1-dee5-422f-81e3-be6d25cd85d9[43a088e1-dee5-422f-81e3-be6d25cd85d9] running status set to false 
[INFO ] 2024-09-11 11:34:22.091 - [任务 2(101)][43a088e1-dee5-422f-81e3-be6d25cd85d9] - Node 43a088e1-dee5-422f-81e3-be6d25cd85d9[43a088e1-dee5-422f-81e3-be6d25cd85d9] schema data cleaned 
[INFO ] 2024-09-11 11:34:22.095 - [任务 2(101)][43a088e1-dee5-422f-81e3-be6d25cd85d9] - Node 43a088e1-dee5-422f-81e3-be6d25cd85d9[43a088e1-dee5-422f-81e3-be6d25cd85d9] monitor closed 
[INFO ] 2024-09-11 11:34:22.095 - [任务 2(101)][43a088e1-dee5-422f-81e3-be6d25cd85d9] - Node 43a088e1-dee5-422f-81e3-be6d25cd85d9[43a088e1-dee5-422f-81e3-be6d25cd85d9] close complete, cost 12 ms 
[INFO ] 2024-09-11 11:34:22.095 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-dd8abf38-3b86-4bd2-95e1-b0d1320f1f2a 
[INFO ] 2024-09-11 11:34:22.095 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-dd8abf38-3b86-4bd2-95e1-b0d1320f1f2a 
[INFO ] 2024-09-11 11:34:22.095 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:34:22.099 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:34:22.103 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:34:22.103 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 25 ms 
[INFO ] 2024-09-11 11:34:22.108 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:34:22.111 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:34:22.115 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:34:22.115 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 674ms 
[INFO ] 2024-09-11 11:34:31.772 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:34:31.772 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:31.778 - [任务 2(101)][8cfa7401-3419-4760-a6af-c334bf1e4ecd] - Node 8cfa7401-3419-4760-a6af-c334bf1e4ecd[8cfa7401-3419-4760-a6af-c334bf1e4ecd] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:34:31.779 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:31.779 - [任务 2(101)][8cfa7401-3419-4760-a6af-c334bf1e4ecd] - Node 8cfa7401-3419-4760-a6af-c334bf1e4ecd[8cfa7401-3419-4760-a6af-c334bf1e4ecd] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:34:31.799 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 25 ms 
[INFO ] 2024-09-11 11:34:31.799 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:34:31.964 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 28 ms 
[INFO ] 2024-09-11 11:34:32.149 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:34:32.149 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:32.153 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:32.153 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:34:32.153 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:34:32.153 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 35 ms 
[INFO ] 2024-09-11 11:34:32.687 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:34:32.692 - [任务 2(101)][8cfa7401-3419-4760-a6af-c334bf1e4ecd] - Node 8cfa7401-3419-4760-a6af-c334bf1e4ecd[8cfa7401-3419-4760-a6af-c334bf1e4ecd] running status set to false 
[INFO ] 2024-09-11 11:34:32.692 - [任务 2(101)][8cfa7401-3419-4760-a6af-c334bf1e4ecd] - Node 8cfa7401-3419-4760-a6af-c334bf1e4ecd[8cfa7401-3419-4760-a6af-c334bf1e4ecd] schema data cleaned 
[INFO ] 2024-09-11 11:34:32.693 - [任务 2(101)][8cfa7401-3419-4760-a6af-c334bf1e4ecd] - Node 8cfa7401-3419-4760-a6af-c334bf1e4ecd[8cfa7401-3419-4760-a6af-c334bf1e4ecd] monitor closed 
[INFO ] 2024-09-11 11:34:32.693 - [任务 2(101)][8cfa7401-3419-4760-a6af-c334bf1e4ecd] - Node 8cfa7401-3419-4760-a6af-c334bf1e4ecd[8cfa7401-3419-4760-a6af-c334bf1e4ecd] close complete, cost 17 ms 
[INFO ] 2024-09-11 11:34:32.697 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-21afb7d3-99db-48c4-8f53-bce1171578d5 
[INFO ] 2024-09-11 11:34:32.698 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-21afb7d3-99db-48c4-8f53-bce1171578d5 
[INFO ] 2024-09-11 11:34:32.698 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:34:32.700 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:34:32.700 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:34:32.711 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 26 ms 
[INFO ] 2024-09-11 11:34:32.711 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:34:32.711 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:34:32.711 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:34:32.712 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 1007ms 
[INFO ] 2024-09-11 11:34:57.881 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:34:57.882 - [任务 2(101)][48a3c3eb-7214-4e08-9947-0fbf90f83d9c] - Node 48a3c3eb-7214-4e08-9947-0fbf90f83d9c[48a3c3eb-7214-4e08-9947-0fbf90f83d9c] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:34:57.882 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:57.883 - [任务 2(101)][48a3c3eb-7214-4e08-9947-0fbf90f83d9c] - Node 48a3c3eb-7214-4e08-9947-0fbf90f83d9c[48a3c3eb-7214-4e08-9947-0fbf90f83d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:34:57.883 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:34:57.931 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 40 ms 
[INFO ] 2024-09-11 11:34:57.932 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 39 ms 
[INFO ] 2024-09-11 11:34:58.137 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:34:58.149 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:34:58.158 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:58.159 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:34:58.159 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:34:58.159 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:34:58.160 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 17 ms 
[INFO ] 2024-09-11 11:34:58.525 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:34:58.526 - [任务 2(101)][48a3c3eb-7214-4e08-9947-0fbf90f83d9c] - Node 48a3c3eb-7214-4e08-9947-0fbf90f83d9c[48a3c3eb-7214-4e08-9947-0fbf90f83d9c] running status set to false 
[INFO ] 2024-09-11 11:34:58.526 - [任务 2(101)][48a3c3eb-7214-4e08-9947-0fbf90f83d9c] - Node 48a3c3eb-7214-4e08-9947-0fbf90f83d9c[48a3c3eb-7214-4e08-9947-0fbf90f83d9c] schema data cleaned 
[INFO ] 2024-09-11 11:34:58.526 - [任务 2(101)][48a3c3eb-7214-4e08-9947-0fbf90f83d9c] - Node 48a3c3eb-7214-4e08-9947-0fbf90f83d9c[48a3c3eb-7214-4e08-9947-0fbf90f83d9c] monitor closed 
[INFO ] 2024-09-11 11:34:58.526 - [任务 2(101)][48a3c3eb-7214-4e08-9947-0fbf90f83d9c] - Node 48a3c3eb-7214-4e08-9947-0fbf90f83d9c[48a3c3eb-7214-4e08-9947-0fbf90f83d9c] close complete, cost 7 ms 
[INFO ] 2024-09-11 11:34:58.535 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-b1135361-0ac5-4100-8a9b-71cfc5942e55 
[INFO ] 2024-09-11 11:34:58.535 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-b1135361-0ac5-4100-8a9b-71cfc5942e55 
[INFO ] 2024-09-11 11:34:58.535 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:34:58.537 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:34:58.539 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:34:58.539 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 28 ms 
[INFO ] 2024-09-11 11:34:58.542 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:34:58.543 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:34:58.543 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:34:58.543 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 814ms 
[INFO ] 2024-09-11 11:35:46.598 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:35:46.659 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:35:46.659 - [任务 2(101)][7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] - Node 7c95fe2f-4ec4-4161-8ebd-5f1195c577d6[7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:35:46.661 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:35:46.661 - [任务 2(101)][7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] - Node 7c95fe2f-4ec4-4161-8ebd-5f1195c577d6[7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:35:46.690 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 26 ms 
[INFO ] 2024-09-11 11:35:46.690 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 24 ms 
[INFO ] 2024-09-11 11:35:46.867 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:35:46.886 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:35:46.902 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:35:46.902 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:35:46.902 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:35:46.902 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:35:46.902 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 19 ms 
[INFO ] 2024-09-11 11:35:47.195 - [任务 2(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Dateltil is not defined 
[ERROR] 2024-09-11 11:35:47.401 - [任务 2(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Dateltil is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Dateltil is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: Dateltil is not defined
	<js>.process(<eval>:4)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Dateltil is not defined
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Dateltil is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: Dateltil is not defined
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-11 11:35:49.744 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:35:49.744 - [任务 2(101)][7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] - Node 7c95fe2f-4ec4-4161-8ebd-5f1195c577d6[7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] running status set to false 
[INFO ] 2024-09-11 11:35:49.744 - [任务 2(101)][7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] - Node 7c95fe2f-4ec4-4161-8ebd-5f1195c577d6[7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] schema data cleaned 
[INFO ] 2024-09-11 11:35:49.745 - [任务 2(101)][7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] - Node 7c95fe2f-4ec4-4161-8ebd-5f1195c577d6[7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] monitor closed 
[INFO ] 2024-09-11 11:35:49.746 - [任务 2(101)][7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] - Node 7c95fe2f-4ec4-4161-8ebd-5f1195c577d6[7c95fe2f-4ec4-4161-8ebd-5f1195c577d6] close complete, cost 6 ms 
[INFO ] 2024-09-11 11:35:49.750 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-1daca867-81fe-4cd0-90bd-f26b3e4d5f37 
[INFO ] 2024-09-11 11:35:49.750 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-1daca867-81fe-4cd0-90bd-f26b3e4d5f37 
[INFO ] 2024-09-11 11:35:49.754 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:35:49.758 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:35:49.758 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:35:49.758 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 18 ms 
[INFO ] 2024-09-11 11:35:49.763 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:35:49.763 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:35:49.763 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:35:49.763 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 3170ms 
[INFO ] 2024-09-11 11:35:56.070 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:35:56.271 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:35:56.273 - [任务 2(101)][e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] - Node e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196[e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:35:56.273 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:35:56.273 - [任务 2(101)][e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] - Node e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196[e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:35:56.300 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 28 ms 
[INFO ] 2024-09-11 11:35:56.302 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 32 ms 
[INFO ] 2024-09-11 11:35:56.516 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:35:56.568 - [任务 2(101)][AA_0530_T1] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[ERROR] 2024-09-11 11:35:56.771 - [任务 2(101)][AA_0530_T1] - start source runner failed: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d

<-- Simple Stack Trace -->
Caused by: io.DGG.entity.error.CoreException: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	io.DGG.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.DGG.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.DGG.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.DGG.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.DGG.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.DGG.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-09-11 11:35:59.138 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:35:59.138 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:35:59.139 - [任务 2(101)][e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] - Node e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196[e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] running status set to false 
[INFO ] 2024-09-11 11:35:59.139 - [任务 2(101)][e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] - Node e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196[e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] schema data cleaned 
[INFO ] 2024-09-11 11:35:59.139 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:35:59.139 - [任务 2(101)][e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] - Node e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196[e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] monitor closed 
[INFO ] 2024-09-11 11:35:59.140 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:35:59.141 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 10 ms 
[INFO ] 2024-09-11 11:35:59.141 - [任务 2(101)][e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] - Node e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196[e222a8f6-9f59-4c4f-a0a8-dfcf9d2ff196] close complete, cost 9 ms 
[INFO ] 2024-09-11 11:35:59.141 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: null 
[INFO ] 2024-09-11 11:35:59.141 - [任务 2(101)][AA_0530_T1] - PDK connector node released: null 
[INFO ] 2024-09-11 11:35:59.141 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:35:59.141 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:35:59.142 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 11 ms 
[INFO ] 2024-09-11 11:35:59.151 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:35:59.151 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:35:59.151 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:35:59.151 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 3084ms 
[INFO ] 2024-09-11 11:36:07.312 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:36:07.384 - [任务 2(101)][4d46294f-4af6-4014-b127-3cf5d32722f1] - Node 4d46294f-4af6-4014-b127-3cf5d32722f1[4d46294f-4af6-4014-b127-3cf5d32722f1] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:36:07.386 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:36:07.386 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:36:07.387 - [任务 2(101)][4d46294f-4af6-4014-b127-3cf5d32722f1] - Node 4d46294f-4af6-4014-b127-3cf5d32722f1[4d46294f-4af6-4014-b127-3cf5d32722f1] preload schema finished, cost 1 ms 
[INFO ] 2024-09-11 11:36:07.402 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 23 ms 
[INFO ] 2024-09-11 11:36:07.402 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 23 ms 
[INFO ] 2024-09-11 11:36:07.570 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:36:07.674 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:36:07.683 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:36:07.684 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:36:07.685 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:36:07.685 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:36:07.891 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 14 ms 
[INFO ] 2024-09-11 11:36:08.136 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:36:08.138 - [任务 2(101)][4d46294f-4af6-4014-b127-3cf5d32722f1] - Node 4d46294f-4af6-4014-b127-3cf5d32722f1[4d46294f-4af6-4014-b127-3cf5d32722f1] running status set to false 
[INFO ] 2024-09-11 11:36:08.139 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-b0ae310e-5e83-416f-884c-88b13a83ea46 
[INFO ] 2024-09-11 11:36:08.139 - [任务 2(101)][4d46294f-4af6-4014-b127-3cf5d32722f1] - Node 4d46294f-4af6-4014-b127-3cf5d32722f1[4d46294f-4af6-4014-b127-3cf5d32722f1] schema data cleaned 
[INFO ] 2024-09-11 11:36:08.140 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-b0ae310e-5e83-416f-884c-88b13a83ea46 
[INFO ] 2024-09-11 11:36:08.140 - [任务 2(101)][4d46294f-4af6-4014-b127-3cf5d32722f1] - Node 4d46294f-4af6-4014-b127-3cf5d32722f1[4d46294f-4af6-4014-b127-3cf5d32722f1] monitor closed 
[INFO ] 2024-09-11 11:36:08.140 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:36:08.140 - [任务 2(101)][4d46294f-4af6-4014-b127-3cf5d32722f1] - Node 4d46294f-4af6-4014-b127-3cf5d32722f1[4d46294f-4af6-4014-b127-3cf5d32722f1] close complete, cost 25 ms 
[INFO ] 2024-09-11 11:36:08.142 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:36:08.142 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:36:08.155 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 76 ms 
[INFO ] 2024-09-11 11:36:08.155 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:36:08.155 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:36:08.155 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:36:08.155 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 935ms 
[INFO ] 2024-09-11 11:50:41.245 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:50:41.317 - [任务 2(101)][82ded8e8-de1f-43c9-a101-40a7826b006a] - Node 82ded8e8-de1f-43c9-a101-40a7826b006a[82ded8e8-de1f-43c9-a101-40a7826b006a] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:50:41.317 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:50:41.317 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:50:41.317 - [任务 2(101)][82ded8e8-de1f-43c9-a101-40a7826b006a] - Node 82ded8e8-de1f-43c9-a101-40a7826b006a[82ded8e8-de1f-43c9-a101-40a7826b006a] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:50:41.359 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 41 ms 
[INFO ] 2024-09-11 11:50:41.363 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 40 ms 
[INFO ] 2024-09-11 11:50:41.363 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[WARN ] 2024-09-11 11:50:41.692 - [任务 2(101)][AA_0530_T1] - Source table is empty, trying to mock data 
[INFO ] 2024-09-11 11:50:41.692 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:50:41.709 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:50:41.710 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:50:41.710 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:50:41.710 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:50:41.710 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 24 ms 
[INFO ] 2024-09-11 11:50:42.112 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:50:42.114 - [任务 2(101)][82ded8e8-de1f-43c9-a101-40a7826b006a] - Node 82ded8e8-de1f-43c9-a101-40a7826b006a[82ded8e8-de1f-43c9-a101-40a7826b006a] running status set to false 
[INFO ] 2024-09-11 11:50:42.114 - [任务 2(101)][82ded8e8-de1f-43c9-a101-40a7826b006a] - Node 82ded8e8-de1f-43c9-a101-40a7826b006a[82ded8e8-de1f-43c9-a101-40a7826b006a] schema data cleaned 
[INFO ] 2024-09-11 11:50:42.114 - [任务 2(101)][82ded8e8-de1f-43c9-a101-40a7826b006a] - Node 82ded8e8-de1f-43c9-a101-40a7826b006a[82ded8e8-de1f-43c9-a101-40a7826b006a] monitor closed 
[INFO ] 2024-09-11 11:50:42.114 - [任务 2(101)][82ded8e8-de1f-43c9-a101-40a7826b006a] - Node 82ded8e8-de1f-43c9-a101-40a7826b006a[82ded8e8-de1f-43c9-a101-40a7826b006a] close complete, cost 0 ms 
[INFO ] 2024-09-11 11:50:42.115 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-ff9972a8-b3f3-417b-854e-000d0f647a60 
[INFO ] 2024-09-11 11:50:42.115 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-ff9972a8-b3f3-417b-854e-000d0f647a60 
[INFO ] 2024-09-11 11:50:42.115 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:50:42.115 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:50:42.115 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:50:42.115 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 8 ms 
[INFO ] 2024-09-11 11:50:42.141 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:50:42.141 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:50:42.141 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:50:42.141 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 907ms 
[INFO ] 2024-09-11 11:51:32.716 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:51:32.856 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:51:32.856 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:51:32.859 - [任务 2(101)][05fb1955-3f60-4179-b943-6628bbe79503] - Node 05fb1955-3f60-4179-b943-6628bbe79503[05fb1955-3f60-4179-b943-6628bbe79503] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:51:32.859 - [任务 2(101)][05fb1955-3f60-4179-b943-6628bbe79503] - Node 05fb1955-3f60-4179-b943-6628bbe79503[05fb1955-3f60-4179-b943-6628bbe79503] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:51:32.929 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 53 ms 
[INFO ] 2024-09-11 11:51:32.929 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 53 ms 
[INFO ] 2024-09-11 11:51:32.929 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[INFO ] 2024-09-11 11:51:33.228 - [任务 2(101)][AA_0530_T1] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[ERROR] 2024-09-11 11:51:33.274 - [任务 2(101)][AA_0530_T1] - start source runner failed: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d

<-- Simple Stack Trace -->
Caused by: io.DGG.entity.error.CoreException: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	io.DGG.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.DGG.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.DGG.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.DGG-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d
	at io.DGG.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.DGG.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.DGG.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.DGG.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.DGG.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][05fb1955-3f60-4179-b943-6628bbe79503] - Node 05fb1955-3f60-4179-b943-6628bbe79503[05fb1955-3f60-4179-b943-6628bbe79503] running status set to false 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][05fb1955-3f60-4179-b943-6628bbe79503] - Node 05fb1955-3f60-4179-b943-6628bbe79503[05fb1955-3f60-4179-b943-6628bbe79503] schema data cleaned 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][05fb1955-3f60-4179-b943-6628bbe79503] - Node 05fb1955-3f60-4179-b943-6628bbe79503[05fb1955-3f60-4179-b943-6628bbe79503] monitor closed 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][05fb1955-3f60-4179-b943-6628bbe79503] - Node 05fb1955-3f60-4179-b943-6628bbe79503[05fb1955-3f60-4179-b943-6628bbe79503] close complete, cost 3 ms 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 4 ms 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: null 
[INFO ] 2024-09-11 11:51:35.780 - [任务 2(101)][AA_0530_T1] - PDK connector node released: null 
[INFO ] 2024-09-11 11:51:35.781 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:51:35.781 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:51:35.781 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 7 ms 
[INFO ] 2024-09-11 11:51:35.783 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:51:35.783 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:51:35.783 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:51:35.783 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 3101ms 
[INFO ] 2024-09-11 11:52:00.258 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:52:00.346 - [任务 2(101)][079d6835-b455-45bc-be0f-587b14ac81fc] - Node 079d6835-b455-45bc-be0f-587b14ac81fc[079d6835-b455-45bc-be0f-587b14ac81fc] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:52:00.346 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:52:00.346 - [任务 2(101)][079d6835-b455-45bc-be0f-587b14ac81fc] - Node 079d6835-b455-45bc-be0f-587b14ac81fc[079d6835-b455-45bc-be0f-587b14ac81fc] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:52:00.346 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:52:00.350 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 20 ms 
[INFO ] 2024-09-11 11:52:00.350 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 20 ms 
[INFO ] 2024-09-11 11:52:00.379 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[WARN ] 2024-09-11 11:52:00.717 - [任务 2(101)][AA_0530_T1] - Source table is empty, trying to mock data 
[INFO ] 2024-09-11 11:52:00.762 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:52:00.767 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:52:00.767 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:52:00.768 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:52:00.768 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:52:00.768 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 24 ms 
[INFO ] 2024-09-11 11:52:01.025 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:52:01.026 - [任务 2(101)][079d6835-b455-45bc-be0f-587b14ac81fc] - Node 079d6835-b455-45bc-be0f-587b14ac81fc[079d6835-b455-45bc-be0f-587b14ac81fc] running status set to false 
[INFO ] 2024-09-11 11:52:01.026 - [任务 2(101)][079d6835-b455-45bc-be0f-587b14ac81fc] - Node 079d6835-b455-45bc-be0f-587b14ac81fc[079d6835-b455-45bc-be0f-587b14ac81fc] schema data cleaned 
[INFO ] 2024-09-11 11:52:01.040 - [任务 2(101)][079d6835-b455-45bc-be0f-587b14ac81fc] - Node 079d6835-b455-45bc-be0f-587b14ac81fc[079d6835-b455-45bc-be0f-587b14ac81fc] monitor closed 
[INFO ] 2024-09-11 11:52:01.055 - [任务 2(101)][079d6835-b455-45bc-be0f-587b14ac81fc] - Node 079d6835-b455-45bc-be0f-587b14ac81fc[079d6835-b455-45bc-be0f-587b14ac81fc] close complete, cost 0 ms 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-065a2982-7593-4e98-a6d2-8e4d6815531b 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-065a2982-7593-4e98-a6d2-8e4d6815531b 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 9 ms 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:52:01.070 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 889ms 
[INFO ] 2024-09-11 11:52:44.803 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:52:44.877 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:52:44.877 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:52:44.877 - [任务 2(101)][5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] - Node 5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c[5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:52:44.877 - [任务 2(101)][5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] - Node 5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c[5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:52:44.916 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 27 ms 
[INFO ] 2024-09-11 11:52:44.916 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 27 ms 
[INFO ] 2024-09-11 11:52:44.916 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[WARN ] 2024-09-11 11:52:45.230 - [任务 2(101)][AA_0530_T1] - Source table is empty, trying to mock data 
[INFO ] 2024-09-11 11:52:45.230 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:52:45.254 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:52:45.255 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:52:45.256 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:52:45.256 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:52:45.257 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 35 ms 
[INFO ] 2024-09-11 11:52:45.679 - [任务 2(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:45 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)]) 
[ERROR] 2024-09-11 11:52:45.693 - [任务 2(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:45 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)]) <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:45 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:45 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	<js>.process(<eval>:6)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:45 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:45 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:45 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	at <js>.process(<eval>:6)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-11 11:52:48.226 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:52:48.235 - [任务 2(101)][5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] - Node 5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c[5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] running status set to false 
[INFO ] 2024-09-11 11:52:48.235 - [任务 2(101)][5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] - Node 5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c[5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] schema data cleaned 
[INFO ] 2024-09-11 11:52:48.235 - [任务 2(101)][5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] - Node 5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c[5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] monitor closed 
[INFO ] 2024-09-11 11:52:48.235 - [任务 2(101)][5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] - Node 5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c[5a0cd2c7-96d2-4355-9b5e-5a2683c15f7c] close complete, cost 0 ms 
[INFO ] 2024-09-11 11:52:48.240 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-356b163c-9394-47c3-9ce2-138093d5d4e0 
[INFO ] 2024-09-11 11:52:48.240 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-356b163c-9394-47c3-9ce2-138093d5d4e0 
[INFO ] 2024-09-11 11:52:48.240 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:52:48.240 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:52:48.243 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:52:48.243 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 5 ms 
[INFO ] 2024-09-11 11:52:48.245 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:52:48.245 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:52:48.246 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:52:48.246 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 3435ms 
[INFO ] 2024-09-11 11:52:54.654 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:52:54.809 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:52:54.809 - [任务 2(101)][a816d444-cf98-4fe0-bc25-e1c48301d173] - Node a816d444-cf98-4fe0-bc25-e1c48301d173[a816d444-cf98-4fe0-bc25-e1c48301d173] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:52:54.809 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:52:54.809 - [任务 2(101)][a816d444-cf98-4fe0-bc25-e1c48301d173] - Node a816d444-cf98-4fe0-bc25-e1c48301d173[a816d444-cf98-4fe0-bc25-e1c48301d173] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:52:54.826 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 24 ms 
[INFO ] 2024-09-11 11:52:54.826 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 24 ms 
[INFO ] 2024-09-11 11:52:54.828 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[WARN ] 2024-09-11 11:52:55.089 - [任务 2(101)][AA_0530_T1] - Source table is empty, trying to mock data 
[INFO ] 2024-09-11 11:52:55.101 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:52:55.114 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:52:55.114 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:52:55.118 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:52:55.118 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:52:55.118 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 19 ms 
[INFO ] 2024-09-11 11:52:55.487 - [任务 2(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:55 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)]) 
[ERROR] 2024-09-11 11:52:55.495 - [任务 2(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:55 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)]) <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:55 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:55 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	<js>.process(<eval>:6)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:55 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:55 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (timeStamp2Date) on com.DGG.constant.DateUtil failed due to: no applicable overload found (overloads: [Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(java.lang.String,java.lang.String)], Method[public static java.lang.String com.DGG.constant.DateUtil.timeStamp2Date(long,java.lang.String)]], arguments: [JavaObject[Wed Sep 11 19:52:55 CST 2024 (java.util.Date)] (HostObject), yyyy-MM-dd HH:mm:ss (String)])
	at <js>.process(<eval>:6)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-11 11:52:58.024 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:52:58.024 - [任务 2(101)][a816d444-cf98-4fe0-bc25-e1c48301d173] - Node a816d444-cf98-4fe0-bc25-e1c48301d173[a816d444-cf98-4fe0-bc25-e1c48301d173] running status set to false 
[INFO ] 2024-09-11 11:52:58.024 - [任务 2(101)][a816d444-cf98-4fe0-bc25-e1c48301d173] - Node a816d444-cf98-4fe0-bc25-e1c48301d173[a816d444-cf98-4fe0-bc25-e1c48301d173] schema data cleaned 
[INFO ] 2024-09-11 11:52:58.024 - [任务 2(101)][a816d444-cf98-4fe0-bc25-e1c48301d173] - Node a816d444-cf98-4fe0-bc25-e1c48301d173[a816d444-cf98-4fe0-bc25-e1c48301d173] monitor closed 
[INFO ] 2024-09-11 11:52:58.024 - [任务 2(101)][a816d444-cf98-4fe0-bc25-e1c48301d173] - Node a816d444-cf98-4fe0-bc25-e1c48301d173[a816d444-cf98-4fe0-bc25-e1c48301d173] close complete, cost 0 ms 
[INFO ] 2024-09-11 11:52:58.025 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-9e515cc2-cc66-478b-a6b5-2d6a330190ea 
[INFO ] 2024-09-11 11:52:58.028 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-9e515cc2-cc66-478b-a6b5-2d6a330190ea 
[INFO ] 2024-09-11 11:52:58.028 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:52:58.028 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:52:58.028 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:52:58.028 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 5 ms 
[INFO ] 2024-09-11 11:52:58.042 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:52:58.043 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:52:58.043 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:52:58.043 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 3382ms 
[INFO ] 2024-09-11 11:53:19.686 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:53:19.767 - [任务 2(101)][e0a28c16-427e-47ef-84c5-128195878171] - Node e0a28c16-427e-47ef-84c5-128195878171[e0a28c16-427e-47ef-84c5-128195878171] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:53:19.767 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:53:19.767 - [任务 2(101)][e0a28c16-427e-47ef-84c5-128195878171] - Node e0a28c16-427e-47ef-84c5-128195878171[e0a28c16-427e-47ef-84c5-128195878171] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:53:19.767 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:53:19.790 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 20 ms 
[INFO ] 2024-09-11 11:53:19.790 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 21 ms 
[INFO ] 2024-09-11 11:53:19.790 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[WARN ] 2024-09-11 11:53:20.121 - [任务 2(101)][AA_0530_T1] - Source table is empty, trying to mock data 
[INFO ] 2024-09-11 11:53:20.129 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:53:20.148 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:53:20.149 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:53:20.149 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:53:20.149 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:53:20.150 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 24 ms 
[INFO ] 2024-09-11 11:53:20.503 - [任务 2(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: str2 is not defined 
[ERROR] 2024-09-11 11:53:20.515 - [任务 2(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: str2 is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: str2 is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: str2 is not defined
	<js>.process(<eval>:8)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: str2 is not defined
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: str2 is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: str2 is not defined
	at <js>.process(<eval>:8)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-11 11:53:23.034 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:53:23.035 - [任务 2(101)][e0a28c16-427e-47ef-84c5-128195878171] - Node e0a28c16-427e-47ef-84c5-128195878171[e0a28c16-427e-47ef-84c5-128195878171] running status set to false 
[INFO ] 2024-09-11 11:53:23.035 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-058d5e05-6fac-49c8-ab32-ff36c4e69911 
[INFO ] 2024-09-11 11:53:23.035 - [任务 2(101)][e0a28c16-427e-47ef-84c5-128195878171] - Node e0a28c16-427e-47ef-84c5-128195878171[e0a28c16-427e-47ef-84c5-128195878171] schema data cleaned 
[INFO ] 2024-09-11 11:53:23.035 - [任务 2(101)][e0a28c16-427e-47ef-84c5-128195878171] - Node e0a28c16-427e-47ef-84c5-128195878171[e0a28c16-427e-47ef-84c5-128195878171] monitor closed 
[INFO ] 2024-09-11 11:53:23.035 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-058d5e05-6fac-49c8-ab32-ff36c4e69911 
[INFO ] 2024-09-11 11:53:23.035 - [任务 2(101)][e0a28c16-427e-47ef-84c5-128195878171] - Node e0a28c16-427e-47ef-84c5-128195878171[e0a28c16-427e-47ef-84c5-128195878171] close complete, cost 1 ms 
[INFO ] 2024-09-11 11:53:23.035 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:53:23.036 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:53:23.036 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:53:23.036 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 5 ms 
[INFO ] 2024-09-11 11:53:23.037 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:53:23.037 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:53:23.037 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:53:23.037 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 3358ms 
[INFO ] 2024-09-11 11:53:33.996 - [任务 2(101)] - 66e0e46ceedc3276e7d58def task start 
[INFO ] 2024-09-11 11:53:34.064 - [任务 2(101)][240f4ed7-e15d-4c50-929e-6616a1937e05] - Node 240f4ed7-e15d-4c50-929e-6616a1937e05[240f4ed7-e15d-4c50-929e-6616a1937e05] start preload schema,table counts: 0 
[INFO ] 2024-09-11 11:53:34.067 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:53:34.067 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] start preload schema,table counts: 1 
[INFO ] 2024-09-11 11:53:34.067 - [任务 2(101)][240f4ed7-e15d-4c50-929e-6616a1937e05] - Node 240f4ed7-e15d-4c50-929e-6616a1937e05[240f4ed7-e15d-4c50-929e-6616a1937e05] preload schema finished, cost 0 ms 
[INFO ] 2024-09-11 11:53:34.107 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] preload schema finished, cost 23 ms 
[INFO ] 2024-09-11 11:53:34.107 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] preload schema finished, cost 23 ms 
[INFO ] 2024-09-11 11:53:34.108 - [任务 2(101)][增强JS] - Node js_processor(增强JS: f5875733-d156-4625-94db-bec02f1f361c) enable batch process 
[WARN ] 2024-09-11 11:53:34.455 - [任务 2(101)][AA_0530_T1] - Source table is empty, trying to mock data 
[INFO ] 2024-09-11 11:53:34.467 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] running status set to false 
[INFO ] 2024-09-11 11:53:34.472 - [任务 2(101)][AA_0530_T1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:53:34.472 - [任务 2(101)][AA_0530_T1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3ce7daa4-4571-4290-a128-055fff0c133d 
[INFO ] 2024-09-11 11:53:34.472 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] schema data cleaned 
[INFO ] 2024-09-11 11:53:34.472 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] monitor closed 
[INFO ] 2024-09-11 11:53:34.473 - [任务 2(101)][AA_0530_T1] - Node AA_0530_T1[3ce7daa4-4571-4290-a128-055fff0c133d] close complete, cost 10 ms 
[INFO ] 2024-09-11 11:53:34.779 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] running status set to false 
[INFO ] 2024-09-11 11:53:34.781 - [任务 2(101)][240f4ed7-e15d-4c50-929e-6616a1937e05] - Node 240f4ed7-e15d-4c50-929e-6616a1937e05[240f4ed7-e15d-4c50-929e-6616a1937e05] running status set to false 
[INFO ] 2024-09-11 11:53:34.785 - [任务 2(101)][240f4ed7-e15d-4c50-929e-6616a1937e05] - Node 240f4ed7-e15d-4c50-929e-6616a1937e05[240f4ed7-e15d-4c50-929e-6616a1937e05] schema data cleaned 
[INFO ] 2024-09-11 11:53:34.786 - [任务 2(101)][240f4ed7-e15d-4c50-929e-6616a1937e05] - Node 240f4ed7-e15d-4c50-929e-6616a1937e05[240f4ed7-e15d-4c50-929e-6616a1937e05] monitor closed 
[INFO ] 2024-09-11 11:53:34.786 - [任务 2(101)][240f4ed7-e15d-4c50-929e-6616a1937e05] - Node 240f4ed7-e15d-4c50-929e-6616a1937e05[240f4ed7-e15d-4c50-929e-6616a1937e05] close complete, cost 0 ms 
[INFO ] 2024-09-11 11:53:34.786 - [任务 2(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-175108a6-d305-42ba-881a-34a9ac127588 
[INFO ] 2024-09-11 11:53:34.790 - [任务 2(101)][增强JS] - PDK connector node released: ScriptExecutor-Mongo-175108a6-d305-42ba-881a-34a9ac127588 
[INFO ] 2024-09-11 11:53:34.791 - [任务 2(101)][增强JS] - [ScriptExecutorsManager-66e0e46ceedc3276e7d58def-f5875733-d156-4625-94db-bec02f1f361c-66dfc2d7c6deef4c73230834] schema data cleaned 
[INFO ] 2024-09-11 11:53:34.791 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] schema data cleaned 
[INFO ] 2024-09-11 11:53:34.791 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] monitor closed 
[INFO ] 2024-09-11 11:53:34.791 - [任务 2(101)][增强JS] - Node 增强JS[f5875733-d156-4625-94db-bec02f1f361c] close complete, cost 5 ms 
[INFO ] 2024-09-11 11:53:34.808 - [任务 2(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-11 11:53:34.808 - [任务 2(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-11 11:53:34.808 - [任务 2(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-11 11:53:34.808 - [任务 2(101)] - test run task 66e0e46ceedc3276e7d58def complete, cost 854ms 
