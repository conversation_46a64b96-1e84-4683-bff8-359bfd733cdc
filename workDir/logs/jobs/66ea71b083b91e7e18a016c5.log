[INFO ] 2024-09-20 13:30:57.250 - [任务 7] - Start task milestones: 66ea71b083b91e7e18a016c5(任务 7) 
[INFO ] 2024-09-20 13:30:57.271 - [任务 7] - Task initialization... 
[INFO ] 2024-09-20 13:30:57.445 - [任务 7] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-20 13:30:57.484 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-20 13:30:57.618 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:30:57.619 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:30:57.619 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:30:57.619 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:30:59.116 - [任务 7][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-20 13:30:59.117 - [任务 7][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-20 13:30:59.118 - [任务 7][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-20 13:30:59.118 - [任务 7][Mysql3306] - batch offset found: {"BMSQL_CONFIG":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"6e109575-f4c5-4a8d-882d-c560a6c4912c","offset":{"{\"server\":\"6e109575-f4c5-4a8d-882d-c560a6c4912c\"}":"{\"ts_sec\":1726640717,\"file\":\"binlog.000035\",\"pos\":377538015,\"server_id\":1}"}} 
[INFO ] 2024-09-20 13:30:59.144 - [任务 7][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-20 13:30:59.159 - [任务 7][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-20 13:30:59.159 - [任务 7][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-20 13:30:59.189 - [任务 7][Mysql3306] - Starting stream read, table list: [BMSQL_CONFIG], offset: {"name":"6e109575-f4c5-4a8d-882d-c560a6c4912c","offset":{"{\"server\":\"6e109575-f4c5-4a8d-882d-c560a6c4912c\"}":"{\"ts_sec\":1726640717,\"file\":\"binlog.000035\",\"pos\":377538015,\"server_id\":1}"}} 
[INFO ] 2024-09-20 13:30:59.191 - [任务 7][Mysql3306] - Starting mysql cdc, server name: 6e109575-f4c5-4a8d-882d-c560a6c4912c 
[INFO ] 2024-09-20 13:30:59.259 - [任务 7][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 421975024
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6e109575-f4c5-4a8d-882d-c560a6c4912c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6e109575-f4c5-4a8d-882d-c560a6c4912c
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: 6e109575-f4c5-4a8d-882d-c560a6c4912c
  pdk.offset.string: {"name":"6e109575-f4c5-4a8d-882d-c560a6c4912c","offset":{"{\"server\":\"6e109575-f4c5-4a8d-882d-c560a6c4912c\"}":"{\"ts_sec\":1726640717,\"file\":\"binlog.000035\",\"pos\":377538015,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-20 13:30:59.259 - [任务 7][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_CONFIG], data change syncing 
[INFO ] 2024-09-20 13:30:59.265 - [任务 7][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-20 13:30:59.265 - [任务 7][DummyTarget] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-20 13:51:55.493 - [任务 7] - Stop task milestones: 66ea71b083b91e7e18a016c5(任务 7)  
[INFO ] 2024-09-20 13:51:55.860 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] running status set to false 
[INFO ] 2024-09-20 13:51:55.959 - [任务 7][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-20 13:51:55.959 - [任务 7][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-09-20 13:51:55.972 - [任务 7][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-c4b175d0-5b79-4e57-98f6-96f3204f236e 
[INFO ] 2024-09-20 13:51:55.973 - [任务 7][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-c4b175d0-5b79-4e57-98f6-96f3204f236e 
[INFO ] 2024-09-20 13:51:55.973 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] schema data cleaned 
[INFO ] 2024-09-20 13:51:55.973 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] monitor closed 
[INFO ] 2024-09-20 13:51:55.974 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] close complete, cost 117 ms 
[INFO ] 2024-09-20 13:51:55.974 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] running status set to false 
[INFO ] 2024-09-20 13:51:55.983 - [任务 7][DummyTarget] - Stop connector 
[INFO ] 2024-09-20 13:51:55.986 - [任务 7][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-f561adf5-c235-4b72-b4ba-51db27f20d81 
[INFO ] 2024-09-20 13:51:55.986 - [任务 7][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-f561adf5-c235-4b72-b4ba-51db27f20d81 
[INFO ] 2024-09-20 13:51:55.987 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] schema data cleaned 
[INFO ] 2024-09-20 13:51:55.987 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] monitor closed 
[INFO ] 2024-09-20 13:51:56.189 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] close complete, cost 13 ms 
[INFO ] 2024-09-20 13:52:00.717 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-20 13:52:00.717 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-09-20 13:52:00.718 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-09-20 13:52:00.740 - [任务 7] - Remove memory task client succeed, task: 任务 7[66ea71b083b91e7e18a016c5] 
[INFO ] 2024-09-20 13:52:00.740 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[66ea71b083b91e7e18a016c5] 
[INFO ] 2024-09-20 13:52:04.542 - [任务 7] - Start task milestones: 66ea71b083b91e7e18a016c5(任务 7) 
[INFO ] 2024-09-20 13:52:04.543 - [任务 7] - Task initialization... 
[INFO ] 2024-09-20 13:52:04.654 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-20 13:52:04.654 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-20 13:52:04.686 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:52:04.686 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:52:04.686 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:52:04.686 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:52:47.934 - [任务 7][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-20 13:52:47.935 - [任务 7][DummyTarget] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-20 13:52:48.235 - [任务 7][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-20 13:52:48.236 - [任务 7][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-20 13:52:48.236 - [任务 7][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-20 13:52:48.238 - [任务 7][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000035","position":377543847,"gtidSet":""} 
[INFO ] 2024-09-20 13:52:48.238 - [任务 7][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-20 13:52:48.289 - [任务 7][Mysql3306] - Initial sync started 
[INFO ] 2024-09-20 13:52:48.289 - [任务 7][Mysql3306] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-09-20 13:52:48.311 - [任务 7][Mysql3306] - Table BMSQL_CONFIG is going to be initial synced 
[WARN ] 2024-09-20 13:52:48.311 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:52:48.513 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:53:48.375 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:53:48.376 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:54:48.426 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:54:48.427 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:55:48.463 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:55:48.664 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:56:48.486 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:56:48.486 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:57:48.525 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:57:48.525 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:58:48.576 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:58:48.577 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:59:48.645 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:59:48.645 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:00:48.699 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:00:48.699 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:01:48.754 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:01:48.754 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:02:48.789 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:02:48.991 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:03:48.912 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:03:48.913 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:04:48.993 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:04:48.993 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:05:49.059 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:05:49.060 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:06:49.112 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:06:49.113 - [任务 7][Mysql3306] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 14:07:49.164 - [任务 7][Mysql3306] - Query 'BMSQL_CONFIG' snapshot row size failed: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'BMSQL_CONFIG'  count failed: Table 'test.bmsql_config' doesn't exist
java.util.concurrent.CompletionException: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'BMSQL_CONFIG'  count failed: Table 'test.bmsql_config' doesn't exist
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1643)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1632)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'BMSQL_CONFIG'  count failed: Table 'test.bmsql_config' doesn't exist
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doBatchCountFunction(HazelcastSourcePdkDataNode.java:1301)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doAsyncTableCount$39(HazelcastSourcePdkBaseNode.java:1274)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	... 7 more
Caused by: io.tapdata.exception.NodeException: Query table 'BMSQL_CONFIG'  count failed: Table 'test.bmsql_config' doesn't exist
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doBatchCountFunction$56(HazelcastSourcePdkDataNode.java:1304)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 11 more
Caused by: io.tapdata.exception.NodeException: Query table 'BMSQL_CONFIG'  count failed: Table 'test.bmsql_config' doesn't exist
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$55(HazelcastSourcePdkDataNode.java:1331)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.common.CommonDbConnector.batchCount(CommonDbConnector.java:360)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$55(HazelcastSourcePdkDataNode.java:1323)
	... 20 more
 
[INFO ] 2024-09-20 14:07:49.164 - [任务 7][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-20 14:07:49.165 - [任务 7][Mysql3306] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CFG_NAME`, `CFG_VALUE` FROM `test`.`BMSQL_CONFIG`, code: 42S02(1146), error: Table 'test.bmsql_config' doesn't exist 
[ERROR] 2024-09-20 14:07:49.175 - [任务 7][Mysql3306] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CFG_NAME`, `CFG_VALUE` FROM `test`.`BMSQL_CONFIG`, code: 42S02(1146), error: Table 'test.bmsql_config' doesn't exist <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CFG_NAME`, `CFG_VALUE` FROM `test`.`BMSQL_CONFIG`, code: 42S02(1146), error: Table 'test.bmsql_config' doesn't exist

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CFG_NAME`, `CFG_VALUE` FROM `test`.`BMSQL_CONFIG`, code: 42S02(1146), error: Table 'test.bmsql_config' doesn't exist
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:256)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:257)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:178)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CFG_NAME`, `CFG_VALUE` FROM `test`.`BMSQL_CONFIG`, code: 42S02(1146), error: Table 'test.bmsql_config' doesn't exist
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:340)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `CFG_NAME`, `CFG_VALUE` FROM `test`.`BMSQL_CONFIG`, code: 42S02(1146), error: Table 'test.bmsql_config' doesn't exist
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$12(HazelcastSourcePdkDataNode.java:348)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `CFG_NAME`, `CFG_VALUE` FROM `test`.`BMSQL_CONFIG`, code: 42S02(1146), error: Table 'test.bmsql_config' doesn't exist
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:610)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:424)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 20 more
Caused by: java.sql.SQLSyntaxErrorException: Table 'test.bmsql_config' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:206)
	... 24 more

[INFO ] 2024-09-20 14:07:49.378 - [任务 7][Mysql3306] - Job suspend in error handle 
[INFO ] 2024-09-20 14:07:52.609 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] running status set to false 
[INFO ] 2024-09-20 14:07:52.636 - [任务 7][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-c4b175d0-5b79-4e57-98f6-96f3204f236e 
[INFO ] 2024-09-20 14:07:52.639 - [任务 7][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-c4b175d0-5b79-4e57-98f6-96f3204f236e 
[INFO ] 2024-09-20 14:07:52.639 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] schema data cleaned 
[INFO ] 2024-09-20 14:07:52.639 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] monitor closed 
[INFO ] 2024-09-20 14:07:52.641 - [任务 7][Mysql3306] - Node Mysql3306[c4b175d0-5b79-4e57-98f6-96f3204f236e] close complete, cost 2972 ms 
[INFO ] 2024-09-20 14:07:52.644 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] running status set to false 
[INFO ] 2024-09-20 14:07:52.644 - [任务 7][DummyTarget] - Stop connector 
[INFO ] 2024-09-20 14:07:52.645 - [任务 7][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-f561adf5-c235-4b72-b4ba-51db27f20d81 
[INFO ] 2024-09-20 14:07:52.645 - [任务 7][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-f561adf5-c235-4b72-b4ba-51db27f20d81 
[INFO ] 2024-09-20 14:07:52.645 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] schema data cleaned 
[INFO ] 2024-09-20 14:07:52.645 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] monitor closed 
[INFO ] 2024-09-20 14:07:52.656 - [任务 7][DummyTarget] - Node DummyTarget[f561adf5-c235-4b72-b4ba-51db27f20d81] close complete, cost 4 ms 
[INFO ] 2024-09-20 14:07:53.485 - [任务 7] - Task [任务 7] cannot retry, reason: Sync progress is empty 
[INFO ] 2024-09-20 14:07:53.485 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-20 14:07:53.538 - [任务 7] - Stop task milestones: 66ea71b083b91e7e18a016c5(任务 7)  
[INFO ] 2024-09-20 14:07:53.538 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-09-20 14:07:53.538 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-09-20 14:07:53.557 - [任务 7] - Remove memory task client succeed, task: 任务 7[66ea71b083b91e7e18a016c5] 
[INFO ] 2024-09-20 14:07:53.557 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[66ea71b083b91e7e18a016c5] 
