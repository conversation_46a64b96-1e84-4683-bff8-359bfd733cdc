[INFO ] 2024-07-17 18:21:57.779 - [任务 6] - Start task milestones: 66979b1bb92eda1a86f52074(任务 6) 
[INFO ] 2024-07-17 18:21:57.786 - [任务 6] - Task initialization... 
[INFO ] 2024-07-17 18:21:58.055 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:21:58.203 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 18:21:58.231 - [任务 6][POLICY] - Node POLICY[375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8] start preload schema,table counts: 1 
[INFO ] 2024-07-17 18:21:58.231 - [任务 6][POLICY] - Node POLICY[375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 18:21:58.231 - [任务 6][TEST1] - Node TEST1[b15ec1ad-be32-460b-b055-3fe5e9180ae3] start preload schema,table counts: 1 
[INFO ] 2024-07-17 18:21:58.231 - [任务 6][TEST1] - Node TEST1[b15ec1ad-be32-460b-b055-3fe5e9180ae3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 18:21:58.711 - [任务 6][TEST1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 18:21:58.795 - [任务 6][TEST1] - Table "test.TEST1" exists, skip auto create table 
[INFO ] 2024-07-17 18:21:58.795 - [任务 6][TEST1] - The table TEST1 has already exist. 
[INFO ] 2024-07-17 18:21:58.795 - [任务 6][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-17 18:21:58.795 - [任务 6][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-17 18:21:58.795 - [任务 6][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 18:21:59.023 - [任务 6][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721211708,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 18:21:59.045 - [任务 6][POLICY] - Initial sync started 
[INFO ] 2024-07-17 18:21:59.045 - [任务 6][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-17 18:21:59.065 - [任务 6][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-17 18:21:59.065 - [任务 6][POLICY] - Query table 'POLICY' counts: 695 
[WARN ] 2024-07-17 18:21:59.268 - [任务 6][TEST1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 1054, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'CAR_MODEL' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-17 18:22:52.261 - [任务 6][POLICY] - Node POLICY[375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8] running status set to false 
[INFO ] 2024-07-17 18:22:52.274 - [任务 6][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 18:22:52.274 - [任务 6][POLICY] - Initial sync completed 
[INFO ] 2024-07-17 18:22:52.275 - [任务 6][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-17 18:22:52.275 - [任务 6][POLICY] - Incremental sync completed 
[INFO ] 2024-07-17 18:22:52.275 - [任务 6][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8 
[INFO ] 2024-07-17 18:22:52.276 - [任务 6][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8 
[INFO ] 2024-07-17 18:22:52.277 - [任务 6][POLICY] - Node POLICY[375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8] schema data cleaned 
[INFO ] 2024-07-17 18:22:52.277 - [任务 6][POLICY] - Node POLICY[375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8] monitor closed 
[INFO ] 2024-07-17 18:22:52.281 - [任务 6][POLICY] - Node POLICY[375bbb73-fbf4-4b59-aaa1-c4df6aaf3bd8] close complete, cost 19 ms 
[INFO ] 2024-07-17 18:22:52.290 - [任务 6][TEST1] - Node TEST1[b15ec1ad-be32-460b-b055-3fe5e9180ae3] running status set to false 
[INFO ] 2024-07-17 18:22:52.306 - [任务 6][TEST1] - PDK connector node stopped: HazelcastTargetPdkDataNode-b15ec1ad-be32-460b-b055-3fe5e9180ae3 
[INFO ] 2024-07-17 18:22:52.306 - [任务 6][TEST1] - PDK connector node released: HazelcastTargetPdkDataNode-b15ec1ad-be32-460b-b055-3fe5e9180ae3 
[INFO ] 2024-07-17 18:22:52.306 - [任务 6][TEST1] - Node TEST1[b15ec1ad-be32-460b-b055-3fe5e9180ae3] schema data cleaned 
[INFO ] 2024-07-17 18:22:52.306 - [任务 6][TEST1] - Node TEST1[b15ec1ad-be32-460b-b055-3fe5e9180ae3] monitor closed 
[INFO ] 2024-07-17 18:22:52.308 - [任务 6][TEST1] - Node TEST1[b15ec1ad-be32-460b-b055-3fe5e9180ae3] close complete, cost 28 ms 
[INFO ] 2024-07-17 18:22:52.386 - [任务 6][TEST1] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record 
[ERROR] 2024-07-17 18:22:52.387 - [任务 6][TEST1] - java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record <-- Error Message -->
java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:809)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:572)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:528)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 11 more
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 18 more
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:809)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 26 more

[INFO ] 2024-07-17 18:22:55.282 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 18:22:55.282 - [任务 6] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@35fabbef 
[INFO ] 2024-07-17 18:22:55.432 - [任务 6] - Stop task milestones: 66979b1bb92eda1a86f52074(任务 6)  
[INFO ] 2024-07-17 18:22:55.432 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-07-17 18:22:55.432 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 18:22:55.496 - [任务 6] - Remove memory task client succeed, task: 任务 6[66979b1bb92eda1a86f52074] 
[INFO ] 2024-07-17 18:22:55.496 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[66979b1bb92eda1a86f52074] 
