[INFO ] 2024-07-29 03:53:22.233 - [TestCK FLOAT32~FLOAT64] - Task initialization... 
[INFO ] 2024-07-29 03:53:22.234 - [TestCK FLOAT32~FLOAT64] - Start task milestones: 66a6a18666c385580759bc0a(TestCK FLOAT32~FLOAT64) 
[INFO ] 2024-07-29 03:53:22.412 - [TestCK FLOAT32~FLOAT64] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 03:53:22.412 - [TestCK FLOAT32~FLOAT64] - The engine receives TestCK FLOAT32~FLOAT64 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 03:53:22.446 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:53:22.447 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:53:22.448 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:53:22.448 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:53:23.276 - [TestCK FLOAT32~FLOAT64][testFloat] - Source node "testFloat" read batch size: 100 
[INFO ] 2024-07-29 03:53:23.277 - [TestCK FLOAT32~FLOAT64][testFloat] - Source node "testFloat" event queue capacity: 200 
[INFO ] 2024-07-29 03:53:23.277 - [TestCK FLOAT32~FLOAT64][testFloat] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 03:53:23.278 - [TestCK FLOAT32~FLOAT64][testFloat] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 03:53:23.279 - [TestCK FLOAT32~FLOAT64][testFloat] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 03:53:23.327 - [TestCK FLOAT32~FLOAT64][testFloat] - Initial sync started 
[INFO ] 2024-07-29 03:53:23.331 - [TestCK FLOAT32~FLOAT64][testFloat] - Starting batch read, table name: testFloat, offset: null 
[INFO ] 2024-07-29 03:53:23.350 - [TestCK FLOAT32~FLOAT64][testFloat] - Table testFloat is going to be initial synced 
[INFO ] 2024-07-29 03:53:23.350 - [TestCK FLOAT32~FLOAT64][testFloat] - Table [testFloat] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 03:53:23.361 - [TestCK FLOAT32~FLOAT64][testFloat] - Query table 'testFloat' counts: 1 
[INFO ] 2024-07-29 03:53:23.362 - [TestCK FLOAT32~FLOAT64][testFloat] - Initial sync completed 
[INFO ] 2024-07-29 03:53:23.563 - [TestCK FLOAT32~FLOAT64][TFloat] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 03:53:24.378 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] running status set to false 
[INFO ] 2024-07-29 03:53:24.381 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] running status set to false 
[INFO ] 2024-07-29 03:53:24.404 - [TestCK FLOAT32~FLOAT64][testFloat] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 03:53:24.404 - [TestCK FLOAT32~FLOAT64][TFloat] - PDK connector node stopped: HazelcastTargetPdkDataNode-a2576e7b-87c0-402f-bbac-1c9fb8cc26ce 
[INFO ] 2024-07-29 03:53:24.405 - [TestCK FLOAT32~FLOAT64][TFloat] - PDK connector node released: HazelcastTargetPdkDataNode-a2576e7b-87c0-402f-bbac-1c9fb8cc26ce 
[INFO ] 2024-07-29 03:53:24.405 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] schema data cleaned 
[INFO ] 2024-07-29 03:53:24.406 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] monitor closed 
[INFO ] 2024-07-29 03:53:24.535 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] close complete, cost 30 ms 
[INFO ] 2024-07-29 03:53:24.535 - [TestCK FLOAT32~FLOAT64][testFloat] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 03:53:24.555 - [TestCK FLOAT32~FLOAT64][testFloat] - PDK connector node stopped: HazelcastSourcePdkDataNode-42d33292-1afb-4c74-9fe6-b0ad119d992e 
[INFO ] 2024-07-29 03:53:24.555 - [TestCK FLOAT32~FLOAT64][testFloat] - PDK connector node released: HazelcastSourcePdkDataNode-42d33292-1afb-4c74-9fe6-b0ad119d992e 
[INFO ] 2024-07-29 03:53:24.555 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] schema data cleaned 
[INFO ] 2024-07-29 03:53:24.555 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] monitor closed 
[INFO ] 2024-07-29 03:53:24.761 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] close complete, cost 182 ms 
[INFO ] 2024-07-29 03:53:26.845 - [TestCK FLOAT32~FLOAT64] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 03:53:26.845 - [TestCK FLOAT32~FLOAT64] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@47d52af1 
[INFO ] 2024-07-29 03:53:26.964 - [TestCK FLOAT32~FLOAT64] - Stop task milestones: 66a6a18666c385580759bc0a(TestCK FLOAT32~FLOAT64)  
[INFO ] 2024-07-29 03:53:26.975 - [TestCK FLOAT32~FLOAT64] - Stopped task aspect(s) 
[INFO ] 2024-07-29 03:53:26.979 - [TestCK FLOAT32~FLOAT64] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 03:53:26.997 - [TestCK FLOAT32~FLOAT64] - Remove memory task client succeed, task: TestCK FLOAT32~FLOAT64[66a6a18666c385580759bc0a] 
[INFO ] 2024-07-29 03:53:26.998 - [TestCK FLOAT32~FLOAT64] - Destroy memory task client cache succeed, task: TestCK FLOAT32~FLOAT64[66a6a18666c385580759bc0a] 
[INFO ] 2024-07-29 03:54:14.376 - [TestCK FLOAT32~FLOAT64] - Task initialization... 
[INFO ] 2024-07-29 03:54:14.400 - [TestCK FLOAT32~FLOAT64] - Start task milestones: 66a6a18666c385580759bc0a(TestCK FLOAT32~FLOAT64) 
[INFO ] 2024-07-29 03:54:14.542 - [TestCK FLOAT32~FLOAT64] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 03:54:14.542 - [TestCK FLOAT32~FLOAT64] - The engine receives TestCK FLOAT32~FLOAT64 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 03:54:14.586 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:54:14.590 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:54:14.590 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 03:54:14.590 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:54:15.419 - [TestCK FLOAT32~FLOAT64][testFloat] - Source node "testFloat" read batch size: 100 
[INFO ] 2024-07-29 03:54:15.420 - [TestCK FLOAT32~FLOAT64][testFloat] - Source node "testFloat" event queue capacity: 200 
[INFO ] 2024-07-29 03:54:15.420 - [TestCK FLOAT32~FLOAT64][testFloat] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 03:54:15.420 - [TestCK FLOAT32~FLOAT64][testFloat] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 03:54:15.420 - [TestCK FLOAT32~FLOAT64][testFloat] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 03:54:15.491 - [TestCK FLOAT32~FLOAT64][testFloat] - Initial sync started 
[INFO ] 2024-07-29 03:54:15.491 - [TestCK FLOAT32~FLOAT64][testFloat] - Starting batch read, table name: testFloat, offset: null 
[INFO ] 2024-07-29 03:54:15.492 - [TestCK FLOAT32~FLOAT64][testFloat] - Table testFloat is going to be initial synced 
[INFO ] 2024-07-29 03:54:15.516 - [TestCK FLOAT32~FLOAT64][testFloat] - Table [testFloat] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 03:54:15.524 - [TestCK FLOAT32~FLOAT64][testFloat] - Query table 'testFloat' counts: 1 
[INFO ] 2024-07-29 03:54:15.524 - [TestCK FLOAT32~FLOAT64][testFloat] - Initial sync completed 
[INFO ] 2024-07-29 03:54:15.648 - [TestCK FLOAT32~FLOAT64][TFloat] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 03:54:15.649 - [TestCK FLOAT32~FLOAT64][TFloat] - Table "test.TFloat" exists, skip auto create table 
[INFO ] 2024-07-29 03:54:15.649 - [TestCK FLOAT32~FLOAT64][TFloat] - The table TFloat has already exist. 
[INFO ] 2024-07-29 03:54:33.289 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] running status set to false 
[INFO ] 2024-07-29 03:54:33.290 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] running status set to false 
[INFO ] 2024-07-29 03:54:33.290 - [TestCK FLOAT32~FLOAT64][testFloat] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 03:54:33.318 - [TestCK FLOAT32~FLOAT64][TFloat] - PDK connector node stopped: HazelcastTargetPdkDataNode-a2576e7b-87c0-402f-bbac-1c9fb8cc26ce 
[INFO ] 2024-07-29 03:54:33.319 - [TestCK FLOAT32~FLOAT64][TFloat] - PDK connector node released: HazelcastTargetPdkDataNode-a2576e7b-87c0-402f-bbac-1c9fb8cc26ce 
[INFO ] 2024-07-29 03:54:33.319 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] schema data cleaned 
[INFO ] 2024-07-29 03:54:33.319 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] monitor closed 
[INFO ] 2024-07-29 03:54:33.330 - [TestCK FLOAT32~FLOAT64][TFloat] - Node TFloat[a2576e7b-87c0-402f-bbac-1c9fb8cc26ce] close complete, cost 33 ms 
[INFO ] 2024-07-29 03:54:33.330 - [TestCK FLOAT32~FLOAT64][testFloat] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 03:54:33.354 - [TestCK FLOAT32~FLOAT64][testFloat] - PDK connector node stopped: HazelcastSourcePdkDataNode-42d33292-1afb-4c74-9fe6-b0ad119d992e 
[INFO ] 2024-07-29 03:54:33.354 - [TestCK FLOAT32~FLOAT64][testFloat] - PDK connector node released: HazelcastSourcePdkDataNode-42d33292-1afb-4c74-9fe6-b0ad119d992e 
[INFO ] 2024-07-29 03:54:33.355 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] schema data cleaned 
[INFO ] 2024-07-29 03:54:33.356 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] monitor closed 
[INFO ] 2024-07-29 03:54:33.356 - [TestCK FLOAT32~FLOAT64][testFloat] - Node testFloat[42d33292-1afb-4c74-9fe6-b0ad119d992e] close complete, cost 74 ms 
[INFO ] 2024-07-29 03:54:37.871 - [TestCK FLOAT32~FLOAT64] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 03:54:37.990 - [TestCK FLOAT32~FLOAT64] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c6928ce 
[INFO ] 2024-07-29 03:54:37.990 - [TestCK FLOAT32~FLOAT64] - Stop task milestones: 66a6a18666c385580759bc0a(TestCK FLOAT32~FLOAT64)  
[INFO ] 2024-07-29 03:54:37.996 - [TestCK FLOAT32~FLOAT64] - Stopped task aspect(s) 
[INFO ] 2024-07-29 03:54:37.996 - [TestCK FLOAT32~FLOAT64] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 03:54:38.013 - [TestCK FLOAT32~FLOAT64] - Remove memory task client succeed, task: TestCK FLOAT32~FLOAT64[66a6a18666c385580759bc0a] 
[INFO ] 2024-07-29 03:54:38.013 - [TestCK FLOAT32~FLOAT64] - Destroy memory task client cache succeed, task: TestCK FLOAT32~FLOAT64[66a6a18666c385580759bc0a] 
