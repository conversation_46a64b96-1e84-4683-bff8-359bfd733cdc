[TRACE] 2025-02-27 16:17:09.090 - [任务 28] - Task initialization... 
[TRACE] 2025-02-27 16:17:09.093 - [任务 28] - Start task milestones: 67c01f3703459062b550c360(任务 28) 
[INFO ] 2025-02-27 16:17:09.297 - [任务 28] - Loading table structure completed 
[TRACE] 2025-02-27 16:17:09.349 - [任务 28] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-02-27 16:17:09.350 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-27 16:17:09.406 - [任务 28] - Task started 
[TRACE] 2025-02-27 16:17:09.410 - [任务 28][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:17:09.410 - [任务 28][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:17:09.411 - [任务 28][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] preload schema finished, cost 0 ms 
[TRACE] 2025-02-27 16:17:09.411 - [任务 28][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] preload schema finished, cost 0 ms 
[INFO ] 2025-02-27 16:17:10.346 - [任务 28][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-02-27 16:17:10.349 - [任务 28][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-27 16:17:10.350 - [任务 28][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-27 16:17:10.351 - [任务 28][Pg] - Apply table structure to target database 
[INFO ] 2025-02-27 16:17:10.354 - [任务 28][SqlServer] - Source connector(SqlServer) initialization completed 
[TRACE] 2025-02-27 16:17:10.354 - [任务 28][SqlServer] - Source node "SqlServer" read batch size: 100 
[TRACE] 2025-02-27 16:17:10.354 - [任务 28][SqlServer] - Source node "SqlServer" event queue capacity: 200 
[TRACE] 2025-02-27 16:17:10.354 - [任务 28][SqlServer] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-27 16:17:11.455 - [任务 28][SqlServer] - Use existing stream offset: {"currentStartLSN":"000037CD000015300001","tablesOffset":{},"ddlOffset":null} 
[TRACE] 2025-02-27 16:17:11.456 - [任务 28][SqlServer] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-27 16:17:11.532 - [任务 28][SqlServer] - Starting batch read from 1 tables 
[TRACE] 2025-02-27 16:17:11.534 - [任务 28][SqlServer] - Initial sync started 
[INFO ] 2025-02-27 16:17:11.534 - [任务 28][SqlServer] - Starting batch read from table: SJM_Test 
[TRACE] 2025-02-27 16:17:11.535 - [任务 28][SqlServer] - Table SJM_Test is going to be initial synced 
[TRACE] 2025-02-27 16:17:11.615 - [任务 28][SqlServer] - Query snapshot row size completed: SqlServer(2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89) 
[INFO ] 2025-02-27 16:17:11.615 - [任务 28][SqlServer] - Table SJM_Test has been completed batch read 
[TRACE] 2025-02-27 16:17:11.617 - [任务 28][SqlServer] - Initial sync completed 
[INFO ] 2025-02-27 16:17:11.617 - [任务 28][SqlServer] - Batch read completed. 
[TRACE] 2025-02-27 16:17:11.619 - [任务 28][SqlServer] - Incremental sync starting... 
[TRACE] 2025-02-27 16:17:11.619 - [任务 28][SqlServer] - Initial sync completed 
[TRACE] 2025-02-27 16:17:11.619 - [任务 28][SqlServer] - Starting stream read, table list: [SJM_Test], offset: {"currentStartLSN":"000037CD000015300001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-02-27 16:17:11.620 - [任务 28][SqlServer] - Starting incremental sync using database log parser 
[INFO ] 2025-02-27 16:17:12.637 - [任务 28][SqlServer] - opened cdc tables: [big, test_mysql__time_old, normal_table, p_01, Customer, test_doristime_old, gavin_1_partition_f24_normal, partition_f24_normal, Product, kkk, testXml2, partition_f22_normal, IdentityServerIdentityResourceProperties, partition_f23_normal, partition_f25_normal, t_rpt_filter, dummy, gavin_1_partition_f22, gavin_1_partition_f24, sss, test_sqlserver_time_old, gavin_1_partition_f23, qa_auto_test_one_many_89_4_1719311434714_5505, sjmincremental, testXML, Category1, testdate2, TimeTest, copyParentTable, testMytable, qa_auto_test_one_many_89_4_1719311113843_1539, gavin_1_partition_f25, AggTest1, from_oracle, sjmincremental1, gavin_supplierinfo, my_table, pppp%'g", qa_auto_test_one_many_89_4_1719311669478_7322, TestClaim, p1_low, gavin_qa_auto_test_one_many_89_4_1719312081494_7428, SJM_Test, ppppp, category0710, test_oracletime_cloud, gavin_1_partition_f22_normal, p1_mid, testuuu, test_table1, IdentityServerIdentityResources, orders, copyChildTable, test_dbtime_new, a_test, gavin_qa_auto_test_one_many_89_4_1719309446193_748, test001_dummy_test, dummy612, test1, pg_all_type, hash_from_mysql, BMSQL_DISTRICT, BMSQL_CONFIG, testTable, Supplier, fact_temperature_per_minute, _tapdata_heartbeat_table, gavin_t_approval_req_data_202407, umer_category, gavin_qa_auto_test_one_many_89_4_1719311113843_1539, ParentTable, test4, ppp, test5, test2, check_test, test3, test8, test9, gavin_1_partition_f23_normal, test6, test_ck_time_cloud, test7, test_pgtime_old, t_approval_req_data_202407, gavin_user_202403, gavin_user_202404, testImage, test_mysql__time_new, test_oracletime_old, testUniqueIndex, test_doristime_new, POCTest4, mssql_all_type, POCTest1, test_test9, test_TEST_LENGTH, test_test8, sffix_user, test_test7, TEST_DDL2345, test_test6, TEST_20240621001, test_test5, test_test4, test_test3, BMSQL_CUSTOMER, test_TEST_DDL_001, test_test2, supplier_umer, test_test1, PartitionTable, reimbursement_approval, p1_high, sffix_player, test_oracletime_old2, test, mysql-from-oracle, tttt't, gavin_product, test_dmtime_new, bls_test, FDM_dummy_test_steven_01, t_9_1_3, ppml, t_9_1_1, test_cktime_old, user_202403, user_202402, player_2024_1, Category1_test, player_2024_2, mysql_all_type, partition_f25, partition_f26, test-cktime-new, partition_f23, partition_f24, user_202406, test001_dummy_test_Increment, partition_f22, user_202404, Category0620, Product-0711, Category_test, test_mysql__time_cloud, _tap_double_active, Category, ChildTable, test_pgtime_cloud, sjm2, TEST_LENGTH, FDM_dummy_test_steven, qa_auto_test_one_many_89_4_1719309446193_748, qa_auto_test_one_many_89_4_1719312081494_7428, sjm1, CLAIM, test_sqlsever_time_cloud, qa_auto_test_one_many_89_4_1719313302800_4957, orderitem, mssql_all_type1, partition_f16, SupplierInfo, hh_user, partition_f15, player, TEST_DDL_001, gg, MyTable, testImage2, PocTest2, sffix_normal_table, testdate, test-dummy, oracle_all_type, testDate3, test_pgtime_new, AA_0601, AA_0729, gavin_1_partition_f15, pl, AA_0607, user, AA_0609, gavin_1_partition_f16] 
[INFO ] 2025-02-27 16:17:12.681 - [任务 28][SqlServer] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-02-27 16:17:12.681 - [任务 28][SqlServer] - Connector SQL Server incremental start succeed, tables: [SJM_Test], data change syncing 
[TRACE] 2025-02-27 16:29:57.820 - [任务 28][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] running status set to false 
[TRACE] 2025-02-27 16:29:58.023 - [任务 28][SqlServer] - Incremental sync completed 
[TRACE] 2025-02-27 16:30:00.878 - [任务 28][SqlServer] - PDK connector node stopped: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740644230158 
[TRACE] 2025-02-27 16:30:00.879 - [任务 28][SqlServer] - PDK connector node released: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740644230158 
[TRACE] 2025-02-27 16:30:00.880 - [任务 28][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] schema data cleaned 
[TRACE] 2025-02-27 16:30:00.881 - [任务 28][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] monitor closed 
[TRACE] 2025-02-27 16:30:00.883 - [任务 28][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] close complete, cost 3079 ms 
[TRACE] 2025-02-27 16:30:00.883 - [任务 28][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] running status set to false 
[TRACE] 2025-02-27 16:30:00.905 - [任务 28][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740644230038 
[TRACE] 2025-02-27 16:30:00.905 - [任务 28][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740644230038 
[TRACE] 2025-02-27 16:30:00.905 - [任务 28][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] schema data cleaned 
[TRACE] 2025-02-27 16:30:00.906 - [任务 28][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] monitor closed 
[TRACE] 2025-02-27 16:30:00.906 - [任务 28][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] close complete, cost 22 ms 
[TRACE] 2025-02-27 16:30:05.504 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-27 16:30:05.504 - [任务 28] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6eb7250f 
[TRACE] 2025-02-27 16:30:05.663 - [任务 28] - Stop task milestones: 67c01f3703459062b550c360(任务 28)  
[TRACE] 2025-02-27 16:30:05.663 - [任务 28] - Stopped task aspect(s) 
[TRACE] 2025-02-27 16:30:05.663 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2025-02-27 16:30:05.663 - [任务 28] - Task stopped. 
[TRACE] 2025-02-27 16:30:05.688 - [任务 28] - Remove memory task client succeed, task: 任务 28[67c01f3703459062b550c360] 
[TRACE] 2025-02-27 16:30:05.690 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[67c01f3703459062b550c360] 
[TRACE] 2025-02-27 16:30:28.722 - [SqlServer~PG] - Task initialization... 
[TRACE] 2025-02-27 16:30:28.844 - [SqlServer~PG] - Start task milestones: 67c01f3703459062b550c360(SqlServer~PG) 
[INFO ] 2025-02-27 16:30:28.844 - [SqlServer~PG] - Loading table structure completed 
[TRACE] 2025-02-27 16:30:28.921 - [SqlServer~PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-27 16:30:28.921 - [SqlServer~PG] - The engine receives SqlServer~PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-27 16:30:28.981 - [SqlServer~PG] - Task started 
[TRACE] 2025-02-27 16:30:28.982 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:30:28.982 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:30:28.982 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] preload schema finished, cost 0 ms 
[TRACE] 2025-02-27 16:30:28.982 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] preload schema finished, cost 0 ms 
[INFO ] 2025-02-27 16:30:29.390 - [SqlServer~PG][SqlServer] - Source connector(SqlServer) initialization completed 
[TRACE] 2025-02-27 16:30:29.392 - [SqlServer~PG][SqlServer] - Source node "SqlServer" read batch size: 100 
[TRACE] 2025-02-27 16:30:29.393 - [SqlServer~PG][SqlServer] - Source node "SqlServer" event queue capacity: 200 
[INFO ] 2025-02-27 16:30:29.412 - [SqlServer~PG][SqlServer] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-02-27 16:30:29.412 - [SqlServer~PG][SqlServer] - Use existing batch read offset: {"SJM_Test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"000037CD000018380004\",\"ddlOffset\":\"AAA3zQAAFTAAAQ==\",\"tablesOffset\":{\"SJM_Test\":\"000037CD000018380004\"}}" 
[TRACE] 2025-02-27 16:30:29.413 - [SqlServer~PG][SqlServer] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-27 16:30:29.431 - [SqlServer~PG][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-02-27 16:30:29.431 - [SqlServer~PG][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-27 16:30:29.431 - [SqlServer~PG][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-27 16:30:29.436 - [SqlServer~PG][Pg] - Apply table structure to target database 
[INFO ] 2025-02-27 16:30:29.459 - [SqlServer~PG][SqlServer] - Batch read completed. 
[TRACE] 2025-02-27 16:30:29.460 - [SqlServer~PG][SqlServer] - Incremental sync starting... 
[TRACE] 2025-02-27 16:30:29.460 - [SqlServer~PG][SqlServer] - Initial sync completed 
[TRACE] 2025-02-27 16:30:29.464 - [SqlServer~PG][SqlServer] - Starting stream read, table list: [SJM_Test], offset: "{\"currentStartLSN\":\"000037CD000018380004\",\"ddlOffset\":\"AAA3zQAAFTAAAQ==\",\"tablesOffset\":{\"SJM_Test\":\"000037CD000018380004\"}}" 
[INFO ] 2025-02-27 16:30:29.465 - [SqlServer~PG][SqlServer] - Starting incremental sync using database log parser 
[INFO ] 2025-02-27 16:30:30.388 - [SqlServer~PG][SqlServer] - opened cdc tables: [big, test_mysql__time_old, normal_table, p_01, Customer, test_doristime_old, gavin_1_partition_f24_normal, partition_f24_normal, Product, kkk, testXml2, partition_f22_normal, IdentityServerIdentityResourceProperties, partition_f23_normal, partition_f25_normal, t_rpt_filter, dummy, gavin_1_partition_f22, gavin_1_partition_f24, sss, test_sqlserver_time_old, gavin_1_partition_f23, qa_auto_test_one_many_89_4_1719311434714_5505, sjmincremental, testXML, Category1, testdate2, TimeTest, copyParentTable, testMytable, qa_auto_test_one_many_89_4_1719311113843_1539, gavin_1_partition_f25, AggTest1, from_oracle, sjmincremental1, gavin_supplierinfo, my_table, pppp%'g", qa_auto_test_one_many_89_4_1719311669478_7322, TestClaim, p1_low, gavin_qa_auto_test_one_many_89_4_1719312081494_7428, SJM_Test, ppppp, category0710, test_oracletime_cloud, gavin_1_partition_f22_normal, p1_mid, testuuu, test_table1, IdentityServerIdentityResources, orders, copyChildTable, test_dbtime_new, a_test, gavin_qa_auto_test_one_many_89_4_1719309446193_748, test001_dummy_test, dummy612, test1, pg_all_type, hash_from_mysql, BMSQL_DISTRICT, BMSQL_CONFIG, testTable, Supplier, fact_temperature_per_minute, _tapdata_heartbeat_table, gavin_t_approval_req_data_202407, umer_category, gavin_qa_auto_test_one_many_89_4_1719311113843_1539, ParentTable, test4, ppp, test5, test2, check_test, test3, test8, test9, gavin_1_partition_f23_normal, test6, test_ck_time_cloud, test7, test_pgtime_old, t_approval_req_data_202407, gavin_user_202403, gavin_user_202404, testImage, test_mysql__time_new, test_oracletime_old, testUniqueIndex, test_doristime_new, POCTest4, mssql_all_type, POCTest1, test_test9, test_TEST_LENGTH, test_test8, sffix_user, test_test7, TEST_DDL2345, test_test6, TEST_20240621001, test_test5, test_test4, test_test3, BMSQL_CUSTOMER, test_TEST_DDL_001, test_test2, supplier_umer, test_test1, PartitionTable, reimbursement_approval, p1_high, sffix_player, test_oracletime_old2, test, mysql-from-oracle, tttt't, gavin_product, test_dmtime_new, bls_test, FDM_dummy_test_steven_01, t_9_1_3, ppml, t_9_1_1, test_cktime_old, user_202403, user_202402, player_2024_1, Category1_test, player_2024_2, mysql_all_type, partition_f25, partition_f26, test-cktime-new, partition_f23, partition_f24, user_202406, test001_dummy_test_Increment, partition_f22, user_202404, Category0620, Product-0711, Category_test, test_mysql__time_cloud, _tap_double_active, Category, ChildTable, test_pgtime_cloud, sjm2, TEST_LENGTH, FDM_dummy_test_steven, qa_auto_test_one_many_89_4_1719309446193_748, qa_auto_test_one_many_89_4_1719312081494_7428, sjm1, CLAIM, test_sqlsever_time_cloud, qa_auto_test_one_many_89_4_1719313302800_4957, orderitem, mssql_all_type1, partition_f16, SupplierInfo, hh_user, partition_f15, player, TEST_DDL_001, gg, MyTable, testImage2, PocTest2, sffix_normal_table, testdate, test-dummy, oracle_all_type, testDate3, test_pgtime_new, AA_0601, AA_0729, gavin_1_partition_f15, pl, AA_0607, user, AA_0609, gavin_1_partition_f16] 
[INFO ] 2025-02-27 16:30:30.539 - [SqlServer~PG][SqlServer] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-02-27 16:30:30.747 - [SqlServer~PG][SqlServer] - Connector SQL Server incremental start succeed, tables: [SJM_Test], data change syncing 
[TRACE] 2025-02-27 16:32:42.255 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] running status set to false 
[TRACE] 2025-02-27 16:32:42.868 - [SqlServer~PG][SqlServer] - Incremental sync completed 
[TRACE] 2025-02-27 16:32:45.154 - [SqlServer~PG][SqlServer] - PDK connector node stopped: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740645029147 
[TRACE] 2025-02-27 16:32:45.156 - [SqlServer~PG][SqlServer] - PDK connector node released: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740645029147 
[TRACE] 2025-02-27 16:32:45.156 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] schema data cleaned 
[TRACE] 2025-02-27 16:32:45.161 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] monitor closed 
[TRACE] 2025-02-27 16:32:45.162 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] close complete, cost 3086 ms 
[TRACE] 2025-02-27 16:32:45.192 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] running status set to false 
[TRACE] 2025-02-27 16:32:45.192 - [SqlServer~PG][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740645029089 
[TRACE] 2025-02-27 16:32:45.192 - [SqlServer~PG][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740645029089 
[TRACE] 2025-02-27 16:32:45.192 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] schema data cleaned 
[TRACE] 2025-02-27 16:32:45.194 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] monitor closed 
[TRACE] 2025-02-27 16:32:45.194 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] close complete, cost 31 ms 
[TRACE] 2025-02-27 16:32:45.863 - [SqlServer~PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-27 16:32:45.864 - [SqlServer~PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4556c38 
[TRACE] 2025-02-27 16:32:46.010 - [SqlServer~PG] - Stop task milestones: 67c01f3703459062b550c360(SqlServer~PG)  
[TRACE] 2025-02-27 16:32:46.010 - [SqlServer~PG] - Stopped task aspect(s) 
[TRACE] 2025-02-27 16:32:46.010 - [SqlServer~PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-27 16:32:46.011 - [SqlServer~PG] - Task stopped. 
[TRACE] 2025-02-27 16:32:46.047 - [SqlServer~PG] - Remove memory task client succeed, task: SqlServer~PG[67c01f3703459062b550c360] 
[TRACE] 2025-02-27 16:32:46.048 - [SqlServer~PG] - Destroy memory task client cache succeed, task: SqlServer~PG[67c01f3703459062b550c360] 
[TRACE] 2025-02-27 16:32:58.097 - [SqlServer~PG] - Task initialization... 
[TRACE] 2025-02-27 16:32:58.249 - [SqlServer~PG] - Start task milestones: 67c01f3703459062b550c360(SqlServer~PG) 
[INFO ] 2025-02-27 16:32:58.251 - [SqlServer~PG] - Loading table structure completed 
[TRACE] 2025-02-27 16:32:58.315 - [SqlServer~PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-27 16:32:58.391 - [SqlServer~PG] - The engine receives SqlServer~PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-27 16:32:58.391 - [SqlServer~PG] - Task started 
[TRACE] 2025-02-27 16:32:58.430 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:32:58.444 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:32:58.445 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] preload schema finished, cost 0 ms 
[TRACE] 2025-02-27 16:32:58.446 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] preload schema finished, cost 0 ms 
[INFO ] 2025-02-27 16:32:59.345 - [SqlServer~PG][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-02-27 16:32:59.345 - [SqlServer~PG][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-27 16:32:59.346 - [SqlServer~PG][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-27 16:32:59.346 - [SqlServer~PG][SqlServer] - Source connector(SqlServer) initialization completed 
[TRACE] 2025-02-27 16:32:59.346 - [SqlServer~PG][SqlServer] - Source node "SqlServer" read batch size: 100 
[TRACE] 2025-02-27 16:32:59.346 - [SqlServer~PG][SqlServer] - Source node "SqlServer" event queue capacity: 200 
[TRACE] 2025-02-27 16:32:59.346 - [SqlServer~PG][SqlServer] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-27 16:32:59.356 - [SqlServer~PG][Pg] - Apply table structure to target database 
[TRACE] 2025-02-27 16:32:59.402 - [SqlServer~PG][Pg] - The table SJM_Test has already exist. 
[INFO ] 2025-02-27 16:33:00.267 - [SqlServer~PG][SqlServer] - Use existing stream offset: {"currentStartLSN":"000037C900012DF80001","tablesOffset":{},"ddlOffset":null} 
[TRACE] 2025-02-27 16:33:00.268 - [SqlServer~PG][SqlServer] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-27 16:33:00.379 - [SqlServer~PG][SqlServer] - Batch read completed. 
[TRACE] 2025-02-27 16:33:00.379 - [SqlServer~PG][SqlServer] - Incremental sync starting... 
[TRACE] 2025-02-27 16:33:00.381 - [SqlServer~PG][SqlServer] - Initial sync completed 
[TRACE] 2025-02-27 16:33:00.389 - [SqlServer~PG][SqlServer] - Starting stream read, table list: [SJM_Test], offset: {"currentStartLSN":"000037C900012DF80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-02-27 16:33:00.394 - [SqlServer~PG][SqlServer] - Starting incremental sync using database log parser 
[INFO ] 2025-02-27 16:33:01.423 - [SqlServer~PG][SqlServer] - opened cdc tables: [big, test_mysql__time_old, normal_table, p_01, Customer, test_doristime_old, gavin_1_partition_f24_normal, partition_f24_normal, Product, kkk, testXml2, partition_f22_normal, IdentityServerIdentityResourceProperties, partition_f23_normal, partition_f25_normal, t_rpt_filter, dummy, gavin_1_partition_f22, gavin_1_partition_f24, sss, test_sqlserver_time_old, gavin_1_partition_f23, qa_auto_test_one_many_89_4_1719311434714_5505, sjmincremental, testXML, Category1, testdate2, TimeTest, copyParentTable, testMytable, qa_auto_test_one_many_89_4_1719311113843_1539, gavin_1_partition_f25, AggTest1, from_oracle, sjmincremental1, gavin_supplierinfo, my_table, pppp%'g", qa_auto_test_one_many_89_4_1719311669478_7322, TestClaim, p1_low, gavin_qa_auto_test_one_many_89_4_1719312081494_7428, SJM_Test, ppppp, category0710, test_oracletime_cloud, gavin_1_partition_f22_normal, p1_mid, testuuu, test_table1, IdentityServerIdentityResources, orders, copyChildTable, test_dbtime_new, a_test, gavin_qa_auto_test_one_many_89_4_1719309446193_748, test001_dummy_test, dummy612, test1, pg_all_type, hash_from_mysql, BMSQL_DISTRICT, BMSQL_CONFIG, testTable, Supplier, fact_temperature_per_minute, _tapdata_heartbeat_table, gavin_t_approval_req_data_202407, umer_category, gavin_qa_auto_test_one_many_89_4_1719311113843_1539, ParentTable, test4, ppp, test5, test2, check_test, test3, test8, test9, gavin_1_partition_f23_normal, test6, test_ck_time_cloud, test7, test_pgtime_old, t_approval_req_data_202407, gavin_user_202403, gavin_user_202404, testImage, test_mysql__time_new, test_oracletime_old, testUniqueIndex, test_doristime_new, POCTest4, mssql_all_type, POCTest1, test_test9, test_TEST_LENGTH, test_test8, sffix_user, test_test7, TEST_DDL2345, test_test6, TEST_20240621001, test_test5, test_test4, test_test3, BMSQL_CUSTOMER, test_TEST_DDL_001, test_test2, supplier_umer, test_test1, PartitionTable, reimbursement_approval, p1_high, sffix_player, test_oracletime_old2, test, mysql-from-oracle, tttt't, gavin_product, test_dmtime_new, bls_test, FDM_dummy_test_steven_01, t_9_1_3, ppml, t_9_1_1, test_cktime_old, user_202403, user_202402, player_2024_1, Category1_test, player_2024_2, mysql_all_type, partition_f25, partition_f26, test-cktime-new, partition_f23, partition_f24, user_202406, test001_dummy_test_Increment, partition_f22, user_202404, Category0620, Product-0711, Category_test, test_mysql__time_cloud, _tap_double_active, Category, ChildTable, test_pgtime_cloud, sjm2, TEST_LENGTH, FDM_dummy_test_steven, qa_auto_test_one_many_89_4_1719309446193_748, qa_auto_test_one_many_89_4_1719312081494_7428, sjm1, CLAIM, test_sqlsever_time_cloud, qa_auto_test_one_many_89_4_1719313302800_4957, orderitem, mssql_all_type1, partition_f16, SupplierInfo, hh_user, partition_f15, player, TEST_DDL_001, gg, MyTable, testImage2, PocTest2, sffix_normal_table, testdate, test-dummy, oracle_all_type, testDate3, test_pgtime_new, AA_0601, AA_0729, gavin_1_partition_f15, pl, AA_0607, user, AA_0609, gavin_1_partition_f16] 
[INFO ] 2025-02-27 16:33:01.559 - [SqlServer~PG][SqlServer] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-02-27 16:33:01.560 - [SqlServer~PG][SqlServer] - Connector SQL Server incremental start succeed, tables: [SJM_Test], data change syncing 
[TRACE] 2025-02-27 16:33:15.301 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] running status set to false 
[TRACE] 2025-02-27 16:33:15.514 - [SqlServer~PG][SqlServer] - Incremental sync completed 
[TRACE] 2025-02-27 16:33:18.360 - [SqlServer~PG][SqlServer] - PDK connector node stopped: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740645179142 
[TRACE] 2025-02-27 16:33:18.361 - [SqlServer~PG][SqlServer] - PDK connector node released: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740645179142 
[TRACE] 2025-02-27 16:33:18.361 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] schema data cleaned 
[TRACE] 2025-02-27 16:33:18.368 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] monitor closed 
[TRACE] 2025-02-27 16:33:18.368 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] close complete, cost 3084 ms 
[TRACE] 2025-02-27 16:33:18.368 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] running status set to false 
[TRACE] 2025-02-27 16:33:18.387 - [SqlServer~PG][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740645179029 
[TRACE] 2025-02-27 16:33:18.387 - [SqlServer~PG][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740645179029 
[TRACE] 2025-02-27 16:33:18.387 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] schema data cleaned 
[TRACE] 2025-02-27 16:33:18.390 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] monitor closed 
[TRACE] 2025-02-27 16:33:18.390 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] close complete, cost 21 ms 
[TRACE] 2025-02-27 16:33:21.109 - [SqlServer~PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-27 16:33:21.110 - [SqlServer~PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2ba2e0f0 
[TRACE] 2025-02-27 16:33:21.247 - [SqlServer~PG] - Stop task milestones: 67c01f3703459062b550c360(SqlServer~PG)  
[TRACE] 2025-02-27 16:33:21.247 - [SqlServer~PG] - Stopped task aspect(s) 
[TRACE] 2025-02-27 16:33:21.247 - [SqlServer~PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-27 16:33:21.247 - [SqlServer~PG] - Task stopped. 
[TRACE] 2025-02-27 16:33:21.277 - [SqlServer~PG] - Remove memory task client succeed, task: SqlServer~PG[67c01f3703459062b550c360] 
[TRACE] 2025-02-27 16:33:21.277 - [SqlServer~PG] - Destroy memory task client cache succeed, task: SqlServer~PG[67c01f3703459062b550c360] 
[TRACE] 2025-02-27 16:33:45.570 - [SqlServer~PG] - Task initialization... 
[TRACE] 2025-02-27 16:33:45.571 - [SqlServer~PG] - Start task milestones: 67c01f3703459062b550c360(SqlServer~PG) 
[INFO ] 2025-02-27 16:33:45.738 - [SqlServer~PG] - Loading table structure completed 
[TRACE] 2025-02-27 16:33:45.738 - [SqlServer~PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-27 16:33:45.805 - [SqlServer~PG] - The engine receives SqlServer~PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-27 16:33:45.805 - [SqlServer~PG] - Task started 
[TRACE] 2025-02-27 16:33:45.831 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:33:45.831 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] start preload schema,table counts: 1 
[TRACE] 2025-02-27 16:33:45.831 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] preload schema finished, cost 0 ms 
[TRACE] 2025-02-27 16:33:45.831 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] preload schema finished, cost 0 ms 
[INFO ] 2025-02-27 16:33:48.795 - [SqlServer~PG][SqlServer] - Source connector(SqlServer) initialization completed 
[INFO ] 2025-02-27 16:33:48.819 - [SqlServer~PG][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-02-27 16:33:48.820 - [SqlServer~PG][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-27 16:33:48.820 - [SqlServer~PG][Pg] - Write batch size: 100, max wait ms per batch: 500 
[TRACE] 2025-02-27 16:33:48.820 - [SqlServer~PG][SqlServer] - Source node "SqlServer" read batch size: 100 
[TRACE] 2025-02-27 16:33:48.820 - [SqlServer~PG][SqlServer] - Source node "SqlServer" event queue capacity: 200 
[TRACE] 2025-02-27 16:33:48.820 - [SqlServer~PG][SqlServer] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-27 16:33:48.824 - [SqlServer~PG][Pg] - Apply table structure to target database 
[TRACE] 2025-02-27 16:33:49.026 - [SqlServer~PG][Pg] - The table SJM_Test has already exist. 
[INFO ] 2025-02-27 16:35:10.993 - [SqlServer~PG][SqlServer] - Use existing stream offset: {"currentStartLSN":"000037C900012DF80001","tablesOffset":{},"ddlOffset":null} 
[TRACE] 2025-02-27 16:35:10.996 - [SqlServer~PG][SqlServer] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-27 16:35:11.041 - [SqlServer~PG][SqlServer] - Batch read completed. 
[TRACE] 2025-02-27 16:35:11.041 - [SqlServer~PG][SqlServer] - Incremental sync starting... 
[TRACE] 2025-02-27 16:35:11.044 - [SqlServer~PG][SqlServer] - Initial sync completed 
[TRACE] 2025-02-27 16:35:11.044 - [SqlServer~PG][SqlServer] - Starting stream read, table list: [SJM_Test], offset: {"currentStartLSN":"000037C900012DF80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-02-27 16:35:11.044 - [SqlServer~PG][SqlServer] - Starting incremental sync using database log parser 
[INFO ] 2025-02-27 16:35:12.057 - [SqlServer~PG][SqlServer] - opened cdc tables: [big, test_mysql__time_old, normal_table, p_01, Customer, test_doristime_old, gavin_1_partition_f24_normal, partition_f24_normal, Product, kkk, testXml2, partition_f22_normal, IdentityServerIdentityResourceProperties, partition_f23_normal, partition_f25_normal, t_rpt_filter, dummy, gavin_1_partition_f22, gavin_1_partition_f24, sss, test_sqlserver_time_old, gavin_1_partition_f23, qa_auto_test_one_many_89_4_1719311434714_5505, sjmincremental, testXML, Category1, testdate2, TimeTest, copyParentTable, testMytable, qa_auto_test_one_many_89_4_1719311113843_1539, gavin_1_partition_f25, AggTest1, from_oracle, sjmincremental1, gavin_supplierinfo, my_table, pppp%'g", qa_auto_test_one_many_89_4_1719311669478_7322, TestClaim, p1_low, gavin_qa_auto_test_one_many_89_4_1719312081494_7428, SJM_Test, ppppp, category0710, test_oracletime_cloud, gavin_1_partition_f22_normal, p1_mid, testuuu, test_table1, IdentityServerIdentityResources, orders, copyChildTable, test_dbtime_new, a_test, gavin_qa_auto_test_one_many_89_4_1719309446193_748, test001_dummy_test, dummy612, test1, pg_all_type, hash_from_mysql, BMSQL_DISTRICT, BMSQL_CONFIG, testTable, Supplier, fact_temperature_per_minute, _tapdata_heartbeat_table, gavin_t_approval_req_data_202407, umer_category, gavin_qa_auto_test_one_many_89_4_1719311113843_1539, ParentTable, test4, ppp, test5, test2, check_test, test3, test8, test9, gavin_1_partition_f23_normal, test6, test_ck_time_cloud, test7, test_pgtime_old, t_approval_req_data_202407, gavin_user_202403, gavin_user_202404, testImage, test_mysql__time_new, test_oracletime_old, testUniqueIndex, test_doristime_new, POCTest4, mssql_all_type, POCTest1, test_test9, test_TEST_LENGTH, test_test8, sffix_user, test_test7, TEST_DDL2345, test_test6, TEST_20240621001, test_test5, test_test4, test_test3, BMSQL_CUSTOMER, test_TEST_DDL_001, test_test2, supplier_umer, test_test1, PartitionTable, reimbursement_approval, p1_high, sffix_player, test_oracletime_old2, test, mysql-from-oracle, tttt't, gavin_product, test_dmtime_new, bls_test, FDM_dummy_test_steven_01, t_9_1_3, ppml, t_9_1_1, test_cktime_old, user_202403, user_202402, player_2024_1, Category1_test, player_2024_2, mysql_all_type, partition_f25, partition_f26, test-cktime-new, partition_f23, partition_f24, user_202406, test001_dummy_test_Increment, partition_f22, user_202404, Category0620, Product-0711, Category_test, test_mysql__time_cloud, _tap_double_active, Category, ChildTable, test_pgtime_cloud, sjm2, TEST_LENGTH, FDM_dummy_test_steven, qa_auto_test_one_many_89_4_1719309446193_748, qa_auto_test_one_many_89_4_1719312081494_7428, sjm1, CLAIM, test_sqlsever_time_cloud, qa_auto_test_one_many_89_4_1719313302800_4957, orderitem, mssql_all_type1, partition_f16, SupplierInfo, hh_user, partition_f15, player, TEST_DDL_001, gg, MyTable, testImage2, PocTest2, sffix_normal_table, testdate, test-dummy, oracle_all_type, testDate3, test_pgtime_new, AA_0601, AA_0729, gavin_1_partition_f15, pl, AA_0607, user, AA_0609, gavin_1_partition_f16] 
[INFO ] 2025-02-27 16:35:12.206 - [SqlServer~PG][SqlServer] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-02-27 16:35:12.206 - [SqlServer~PG][SqlServer] - Connector SQL Server incremental start succeed, tables: [SJM_Test], data change syncing 
[TRACE] 2025-02-27 18:22:06.965 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] running status set to false 
[TRACE] 2025-02-27 18:22:07.002 - [SqlServer~PG][SqlServer] - Incremental sync completed 
[TRACE] 2025-02-27 18:22:10.029 - [SqlServer~PG][SqlServer] - PDK connector node stopped: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740645226558 
[TRACE] 2025-02-27 18:22:10.030 - [SqlServer~PG][SqlServer] - PDK connector node released: HazelcastSourcePdkDataNode_2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89_1740645226558 
[TRACE] 2025-02-27 18:22:10.030 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] schema data cleaned 
[TRACE] 2025-02-27 18:22:10.031 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] monitor closed 
[TRACE] 2025-02-27 18:22:10.035 - [SqlServer~PG][SqlServer] - Node SqlServer[2dfc2f4b-89fc-4fd5-bf75-b6a88de29a89] close complete, cost 3087 ms 
[TRACE] 2025-02-27 18:22:10.035 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] running status set to false 
[TRACE] 2025-02-27 18:22:10.057 - [SqlServer~PG][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740645226465 
[TRACE] 2025-02-27 18:22:10.057 - [SqlServer~PG][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50_1740645226465 
[TRACE] 2025-02-27 18:22:10.057 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] schema data cleaned 
[TRACE] 2025-02-27 18:22:10.058 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] monitor closed 
[TRACE] 2025-02-27 18:22:10.058 - [SqlServer~PG][Pg] - Node Pg[dfbe3ddf-b5ea-421c-b9bd-77b6a8259d50] close complete, cost 22 ms 
[TRACE] 2025-02-27 18:22:14.095 - [SqlServer~PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-27 18:22:14.098 - [SqlServer~PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@759b6080 
[TRACE] 2025-02-27 18:22:14.098 - [SqlServer~PG] - Stop task milestones: 67c01f3703459062b550c360(SqlServer~PG)  
[TRACE] 2025-02-27 18:22:14.260 - [SqlServer~PG] - Stopped task aspect(s) 
[TRACE] 2025-02-27 18:22:14.260 - [SqlServer~PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-27 18:22:14.285 - [SqlServer~PG] - Task stopped. 
[TRACE] 2025-02-27 18:22:14.286 - [SqlServer~PG] - Remove memory task client succeed, task: SqlServer~PG[67c01f3703459062b550c360] 
[TRACE] 2025-02-27 18:22:14.286 - [SqlServer~PG] - Destroy memory task client cache succeed, task: SqlServer~PG[67c01f3703459062b550c360] 
