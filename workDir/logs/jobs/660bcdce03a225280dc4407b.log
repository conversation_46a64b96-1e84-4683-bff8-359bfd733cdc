[INFO ] 2024-04-02 21:55:09.399 - [任务 43(101)] - 660bcdce03a225280dc4407b task start 
[INFO ] 2024-04-02 21:55:09.525 - [任务 43(101)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] start preload schema,table counts: 1 
[INFO ] 2024-04-02 21:55:09.525 - [任务 43(101)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] start preload schema,table counts: 1 
[INFO ] 2024-04-02 21:55:09.526 - [任务 43(101)][8964e4ae-f3cc-4140-8d97-6acad0d72112] - Node 8964e4ae-f3cc-4140-8d97-6acad0d72112[8964e4ae-f3cc-4140-8d97-6acad0d72112] start preload schema,table counts: 0 
[INFO ] 2024-04-02 21:55:09.528 - [任务 43(101)][8964e4ae-f3cc-4140-8d97-6acad0d72112] - Node 8964e4ae-f3cc-4140-8d97-6acad0d72112[8964e4ae-f3cc-4140-8d97-6acad0d72112] preload schema finished, cost 0 ms 
[ERROR] 2024-04-02 21:55:09.564 - [任务 43(101)][增强JS] - java.lang.RuntimeException: Find schema failed, message: Table name "de3cff20-f533-4269-86be-122c4a0476c4" not exists, qualified name: PN_de3cff20-f533-4269-86be-122c4a0476c4 <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "de3cff20-f533-4269-86be-122c4a0476c4" not exists, qualified name: PN_de3cff20-f533-4269-86be-122c4a0476c4

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "de3cff20-f533-4269-86be-122c4a0476c4" not exists, qualified name: PN_de3cff20-f533-4269-86be-122c4a0476c4
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "de3cff20-f533-4269-86be-122c4a0476c4" not exists, qualified name: PN_de3cff20-f533-4269-86be-122c4a0476c4
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:211)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "de3cff20-f533-4269-86be-122c4a0476c4" not exists, qualified name: PN_de3cff20-f533-4269-86be-122c4a0476c4
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:203)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "de3cff20-f533-4269-86be-122c4a0476c4" not exists, qualified name: PN_de3cff20-f533-4269-86be-122c4a0476c4
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-04-02 21:55:09.700 - [任务 43(101)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] preload schema finished, cost 96 ms 
[INFO ] 2024-04-02 21:55:10.047 - [任务 43(101)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] running status set to false 
[INFO ] 2024-04-02 21:55:10.047 - [任务 43(101)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8fcdda3b-8438-45a8-b53f-28a6c47ade37 
[INFO ] 2024-04-02 21:55:10.047 - [任务 43(101)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8fcdda3b-8438-45a8-b53f-28a6c47ade37 
[INFO ] 2024-04-02 21:55:10.048 - [任务 43(101)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] schema data cleaned 
[INFO ] 2024-04-02 21:55:10.048 - [任务 43(101)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] monitor closed 
[INFO ] 2024-04-02 21:55:10.050 - [任务 43(101)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] close complete, cost 20 ms 
[INFO ] 2024-04-02 21:55:12.169 - [任务 43(101)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] running status set to false 
[INFO ] 2024-04-02 21:55:12.170 - [任务 43(101)][8964e4ae-f3cc-4140-8d97-6acad0d72112] - Node 8964e4ae-f3cc-4140-8d97-6acad0d72112[8964e4ae-f3cc-4140-8d97-6acad0d72112] running status set to false 
[INFO ] 2024-04-02 21:55:12.181 - [任务 43(101)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] schema data cleaned 
[INFO ] 2024-04-02 21:55:12.181 - [任务 43(101)][8964e4ae-f3cc-4140-8d97-6acad0d72112] - Node 8964e4ae-f3cc-4140-8d97-6acad0d72112[8964e4ae-f3cc-4140-8d97-6acad0d72112] schema data cleaned 
[INFO ] 2024-04-02 21:55:12.181 - [任务 43(101)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] monitor closed 
[INFO ] 2024-04-02 21:55:12.181 - [任务 43(101)][8964e4ae-f3cc-4140-8d97-6acad0d72112] - Node 8964e4ae-f3cc-4140-8d97-6acad0d72112[8964e4ae-f3cc-4140-8d97-6acad0d72112] monitor closed 
[INFO ] 2024-04-02 21:55:12.182 - [任务 43(101)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] close complete, cost 23 ms 
[INFO ] 2024-04-02 21:55:12.183 - [任务 43(101)][8964e4ae-f3cc-4140-8d97-6acad0d72112] - Node 8964e4ae-f3cc-4140-8d97-6acad0d72112[8964e4ae-f3cc-4140-8d97-6acad0d72112] close complete, cost 20 ms 
[INFO ] 2024-04-02 21:55:12.184 - [任务 43(101)] - test run task 660bcdce03a225280dc4407b complete, cost 2841ms 
