[INFO ] 2024-07-24 11:59:13.897 - [任务 23] - Start task milestones: 66a07bdcf604e81d788d00f2(任务 23) 
[INFO ] 2024-07-24 11:59:13.899 - [任务 23] - Task initialization... 
[INFO ] 2024-07-24 11:59:14.045 - [任务 23] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 11:59:14.091 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 11:59:14.165 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] start preload schema,table counts: 1 
[INFO ] 2024-07-24 11:59:14.171 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 11:59:14.178 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 11:59:14.184 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 11:59:15.345 - [任务 23][Inventory2] - Source node "Inventory2" read batch size: 100 
[INFO ] 2024-07-24 11:59:15.345 - [任务 23][Inventory2] - Source node "Inventory2" event queue capacity: 200 
[INFO ] 2024-07-24 11:59:15.345 - [任务 23][Inventory2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 11:59:15.365 - [任务 23][Inventory2] - batch offset found: {},stream offset found: {"currentStartLSN":"000001CB000102700001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 11:59:15.365 - [任务 23][Inventory2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 11:59:15.441 - [任务 23][Inventory2] - Initial sync started 
[INFO ] 2024-07-24 11:59:15.442 - [任务 23][Inventory2] - Starting batch read, table name: Inventory2, offset: null 
[INFO ] 2024-07-24 11:59:15.472 - [任务 23][Inventory2] - Table Inventory2 is going to be initial synced 
[INFO ] 2024-07-24 11:59:15.558 - [任务 23][Inventory2] - Table [Inventory2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 11:59:15.567 - [任务 23][Inventory2] - Query table 'Inventory2' counts: 1 
[INFO ] 2024-07-24 11:59:15.569 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 11:59:15.575 - [任务 23][Inventory2] - Incremental sync starting... 
[INFO ] 2024-07-24 11:59:15.577 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 11:59:15.577 - [任务 23][Inventory2] - Starting stream read, table list: [Inventory2, _tapdata_heartbeat_table], offset: {"currentStartLSN":"000001CB000102700001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 11:59:15.660 - [任务 23][Inventory2] - opened cdc tables: [Inventory2, Inventory] 
[INFO ] 2024-07-24 11:59:15.661 - [任务 23][Inventory2] - building CT table for table _tapdata_heartbeat_table 
[INFO ] 2024-07-24 11:59:15.866 - [任务 23][wimT1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 11:59:16.252 - [任务 23][Inventory2] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 11:59:16.457 - [任务 23][Inventory2] - Connector SQL Server incremental start succeed, tables: [Inventory2, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 12:06:18.544 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] running status set to false 
[INFO ] 2024-07-24 12:06:19.158 - [任务 23][Inventory2] - Incremental sync completed 
[INFO ] 2024-07-24 12:06:21.554 - [任务 23][Inventory2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 12:06:21.556 - [任务 23][Inventory2] - PDK connector node released: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 12:06:21.559 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] schema data cleaned 
[INFO ] 2024-07-24 12:06:21.559 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] monitor closed 
[INFO ] 2024-07-24 12:06:21.559 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] close complete, cost 3068 ms 
[INFO ] 2024-07-24 12:06:21.586 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] running status set to false 
[INFO ] 2024-07-24 12:06:21.586 - [任务 23][wimT1] - PDK connector node stopped: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 12:06:21.586 - [任务 23][wimT1] - PDK connector node released: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 12:06:21.586 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] schema data cleaned 
[INFO ] 2024-07-24 12:06:21.586 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] monitor closed 
[INFO ] 2024-07-24 12:06:21.789 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] close complete, cost 30 ms 
[INFO ] 2024-07-24 12:06:25.841 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 12:06:25.844 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@734450b7 
[INFO ] 2024-07-24 12:06:25.845 - [任务 23] - Stop task milestones: 66a07bdcf604e81d788d00f2(任务 23)  
[INFO ] 2024-07-24 12:06:25.972 - [任务 23] - Stopped task aspect(s) 
[INFO ] 2024-07-24 12:06:25.973 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 12:06:25.995 - [任务 23] - Remove memory task client succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
[INFO ] 2024-07-24 12:06:25.996 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
[INFO ] 2024-07-24 12:06:43.770 - [任务 23] - Start task milestones: 66a07bdcf604e81d788d00f2(任务 23) 
[INFO ] 2024-07-24 12:06:43.868 - [任务 23] - Task initialization... 
[INFO ] 2024-07-24 12:06:43.899 - [任务 23] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 12:06:43.975 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 12:06:43.976 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:06:43.976 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:06:43.981 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:06:43.981 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:06:44.293 - [任务 23][Inventory2] - Source node "Inventory2" read batch size: 100 
[INFO ] 2024-07-24 12:06:44.293 - [任务 23][Inventory2] - Source node "Inventory2" event queue capacity: 200 
[INFO ] 2024-07-24 12:06:44.293 - [任务 23][Inventory2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-24 12:06:44.295 - [任务 23][Inventory2] - batch offset found: {"Inventory2":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: "{\"currentStartLSN\":\"000001CB00012E600006\",\"ddlOffset\":\"AAABywABAnAAAQ==\",\"tablesOffset\":{\"Inventory2\":\"000001CB000102700001\",\"_tapdata_heartbeat_table\":\"000001CB00012E600006\"}}" 
[INFO ] 2024-07-24 12:06:44.295 - [任务 23][Inventory2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 12:06:44.373 - [任务 23][wimT1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 12:06:44.373 - [任务 23][Inventory2] - Incremental sync starting... 
[INFO ] 2024-07-24 12:06:44.373 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 12:06:44.375 - [任务 23][Inventory2] - Starting stream read, table list: [Inventory2, _tapdata_heartbeat_table], offset: "{\"currentStartLSN\":\"000001CB00012E600006\",\"ddlOffset\":\"AAABywABAnAAAQ==\",\"tablesOffset\":{\"Inventory2\":\"000001CB000102700001\",\"_tapdata_heartbeat_table\":\"000001CB00012E600006\"}}" 
[INFO ] 2024-07-24 12:06:44.577 - [任务 23][Inventory2] - opened cdc tables: [_tapdata_heartbeat_table, Inventory2, Inventory] 
[INFO ] 2024-07-24 12:06:44.798 - [任务 23][Inventory2] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 12:06:44.800 - [任务 23][Inventory2] - Connector SQL Server incremental start succeed, tables: [Inventory2, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 12:15:46.750 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] running status set to false 
[INFO ] 2024-07-24 12:17:54.034 - [任务 23] - Task initialization... 
[INFO ] 2024-07-24 12:17:54.244 - [任务 23] - Start task milestones: 66a07bdcf604e81d788d00f2(任务 23) 
[INFO ] 2024-07-24 12:17:54.740 - [任务 23] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 12:17:54.740 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 12:17:55.601 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:17:55.603 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:17:55.606 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:17:55.615 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:17:56.598 - [任务 23][Inventory2] - Source node "Inventory2" read batch size: 100 
[INFO ] 2024-07-24 12:17:56.603 - [任务 23][Inventory2] - Source node "Inventory2" event queue capacity: 200 
[INFO ] 2024-07-24 12:17:56.628 - [任务 23][Inventory2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-24 12:17:56.636 - [任务 23][wimT1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 12:17:56.643 - [任务 23][Inventory2] - batch offset found: {},stream offset found: "{\"currentStartLSN\":\"000001CB000146D00003\",\"ddlOffset\":\"AAABywABAnAAAQ==\",\"tablesOffset\":{\"Inventory2\":\"000001CB000102700001\",\"_tapdata_heartbeat_table\":\"000001CB000146D00003\"}}" 
[INFO ] 2024-07-24 12:17:56.646 - [任务 23][Inventory2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 12:17:56.717 - [任务 23][Inventory2] - Incremental sync starting... 
[INFO ] 2024-07-24 12:17:56.718 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 12:17:56.729 - [任务 23][Inventory2] - Starting stream read, table list: [Inventory2, _tapdata_heartbeat_table], offset: "{\"currentStartLSN\":\"000001CB000146D00003\",\"ddlOffset\":\"AAABywABAnAAAQ==\",\"tablesOffset\":{\"Inventory2\":\"000001CB000102700001\",\"_tapdata_heartbeat_table\":\"000001CB000146D00003\"}}" 
[INFO ] 2024-07-24 12:17:56.937 - [任务 23][Inventory2] - opened cdc tables: [_tapdata_heartbeat_table, Inventory2, Inventory] 
[INFO ] 2024-07-24 12:17:57.077 - [任务 23][Inventory2] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 12:17:57.077 - [任务 23][Inventory2] - Connector SQL Server incremental start succeed, tables: [Inventory2, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 12:22:37.269 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] running status set to false 
[INFO ] 2024-07-24 12:22:37.886 - [任务 23][Inventory2] - Incremental sync completed 
[INFO ] 2024-07-24 12:22:40.301 - [任务 23][Inventory2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 12:22:40.305 - [任务 23][Inventory2] - PDK connector node released: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 12:22:40.305 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] schema data cleaned 
[INFO ] 2024-07-24 12:22:40.311 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] monitor closed 
[INFO ] 2024-07-24 12:22:40.312 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] close complete, cost 3113 ms 
[INFO ] 2024-07-24 12:22:40.313 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] running status set to false 
[INFO ] 2024-07-24 12:22:40.378 - [任务 23][wimT1] - PDK connector node stopped: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 12:22:40.379 - [任务 23][wimT1] - PDK connector node released: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 12:22:40.379 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] schema data cleaned 
[INFO ] 2024-07-24 12:22:40.380 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] monitor closed 
[INFO ] 2024-07-24 12:22:40.587 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] close complete, cost 73 ms 
[INFO ] 2024-07-24 12:22:41.196 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 12:22:41.198 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7aed4841 
[INFO ] 2024-07-24 12:22:41.335 - [任务 23] - Stop task milestones: 66a07bdcf604e81d788d00f2(任务 23)  
[INFO ] 2024-07-24 12:22:41.357 - [任务 23] - Stopped task aspect(s) 
[INFO ] 2024-07-24 12:22:41.357 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 12:22:41.380 - [任务 23] - Remove memory task client succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
[INFO ] 2024-07-24 12:22:41.389 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
[INFO ] 2024-07-24 12:23:28.080 - [任务 23] - Task initialization... 
[INFO ] 2024-07-24 12:23:28.148 - [任务 23] - Start task milestones: 66a07bdcf604e81d788d00f2(任务 23) 
[INFO ] 2024-07-24 12:23:28.225 - [任务 23] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 12:23:28.293 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 12:23:28.356 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:23:28.361 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:23:28.362 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:23:28.362 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:23:29.259 - [任务 23][wimT1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 12:23:29.260 - [任务 23][Inventory2] - Source node "Inventory2" read batch size: 100 
[INFO ] 2024-07-24 12:23:29.260 - [任务 23][Inventory2] - Source node "Inventory2" event queue capacity: 200 
[INFO ] 2024-07-24 12:23:29.261 - [任务 23][Inventory2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 12:23:29.261 - [任务 23][wimT1] - Table "test.wimT1" exists, skip auto create table 
[INFO ] 2024-07-24 12:23:29.261 - [任务 23][wimT1] - The table wimT1 has already exist. 
[INFO ] 2024-07-24 12:23:29.299 - [任务 23][Inventory2] - batch offset found: {},stream offset found: {"currentStartLSN":"000001CB000151380003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 12:23:29.301 - [任务 23][Inventory2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 12:23:29.347 - [任务 23][Inventory2] - Initial sync started 
[INFO ] 2024-07-24 12:23:29.352 - [任务 23][Inventory2] - Starting batch read, table name: Inventory2, offset: null 
[INFO ] 2024-07-24 12:23:29.409 - [任务 23][Inventory2] - Table Inventory2 is going to be initial synced 
[INFO ] 2024-07-24 12:23:29.409 - [任务 23][Inventory2] - Table [Inventory2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 12:23:29.447 - [任务 23][Inventory2] - Query table 'Inventory2' counts: 1 
[INFO ] 2024-07-24 12:23:29.448 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 12:23:29.448 - [任务 23][Inventory2] - Incremental sync starting... 
[INFO ] 2024-07-24 12:23:29.449 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 12:23:29.500 - [任务 23][Inventory2] - Starting stream read, table list: [Inventory2, _tapdata_heartbeat_table], offset: {"currentStartLSN":"000001CB000151380003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 12:23:29.504 - [任务 23][Inventory2] - opened cdc tables: [_tapdata_heartbeat_table, Inventory2, Inventory] 
[INFO ] 2024-07-24 12:23:29.652 - [任务 23][Inventory2] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 12:23:29.654 - [任务 23][Inventory2] - Connector SQL Server incremental start succeed, tables: [Inventory2, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 15:17:54.669 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] running status set to false 
[INFO ] 2024-07-24 15:17:55.240 - [任务 23][Inventory2] - Incremental sync completed 
[INFO ] 2024-07-24 15:17:57.730 - [任务 23][Inventory2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 15:17:57.733 - [任务 23][Inventory2] - PDK connector node released: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 15:17:57.733 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] schema data cleaned 
[INFO ] 2024-07-24 15:17:57.739 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] monitor closed 
[INFO ] 2024-07-24 15:17:57.740 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] close complete, cost 3090 ms 
[INFO ] 2024-07-24 15:17:57.740 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] running status set to false 
[INFO ] 2024-07-24 15:17:57.768 - [任务 23][wimT1] - PDK connector node stopped: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 15:17:57.769 - [任务 23][wimT1] - PDK connector node released: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 15:17:57.769 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] schema data cleaned 
[INFO ] 2024-07-24 15:17:57.771 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] monitor closed 
[INFO ] 2024-07-24 15:17:57.771 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] close complete, cost 31 ms 
[INFO ] 2024-07-24 15:18:01.431 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 15:18:01.432 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1aacd1a8 
[INFO ] 2024-07-24 15:18:01.609 - [任务 23] - Stop task milestones: 66a07bdcf604e81d788d00f2(任务 23)  
[INFO ] 2024-07-24 15:18:01.610 - [任务 23] - Stopped task aspect(s) 
[INFO ] 2024-07-24 15:18:01.633 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 15:18:01.634 - [任务 23] - Remove memory task client succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
[INFO ] 2024-07-24 15:18:01.634 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
[INFO ] 2024-07-24 15:55:00.461 - [任务 23] - Task initialization... 
[INFO ] 2024-07-24 15:55:00.461 - [任务 23] - Start task milestones: 66a07bdcf604e81d788d00f2(任务 23) 
[INFO ] 2024-07-24 15:55:00.637 - [任务 23] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 15:55:00.692 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 15:55:00.692 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:55:00.692 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:55:00.692 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:55:00.693 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:55:01.392 - [任务 23][wimT1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 15:55:01.415 - [任务 23][wimT1] - Table "test.wimT1" exists, skip auto create table 
[INFO ] 2024-07-24 15:55:01.424 - [任务 23][wimT1] - The table wimT1 has already exist. 
[INFO ] 2024-07-24 15:55:01.599 - [任务 23][Inventory2] - Source node "Inventory2" read batch size: 100 
[INFO ] 2024-07-24 15:55:01.601 - [任务 23][Inventory2] - Source node "Inventory2" event queue capacity: 200 
[INFO ] 2024-07-24 15:55:01.601 - [任务 23][Inventory2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 15:55:01.624 - [任务 23][Inventory2] - batch offset found: {},stream offset found: {"currentStartLSN":"000001CE00004DA00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 15:55:01.624 - [任务 23][Inventory2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 15:55:01.670 - [任务 23][Inventory2] - Initial sync started 
[INFO ] 2024-07-24 15:55:01.670 - [任务 23][Inventory2] - Starting batch read, table name: Inventory2, offset: null 
[INFO ] 2024-07-24 15:55:06.331 - [任务 23][Inventory2] - Table Inventory2 is going to be initial synced 
[INFO ] 2024-07-24 15:55:08.927 - [任务 23][Inventory2] - Table [Inventory2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 15:55:08.928 - [任务 23][Inventory2] - Query table 'Inventory2' counts: 1 
[INFO ] 2024-07-24 15:55:08.928 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 15:55:08.929 - [任务 23][Inventory2] - Incremental sync starting... 
[INFO ] 2024-07-24 15:55:08.929 - [任务 23][Inventory2] - Initial sync completed 
[INFO ] 2024-07-24 15:55:08.991 - [任务 23][Inventory2] - Starting stream read, table list: [Inventory2, _tapdata_heartbeat_table], offset: {"currentStartLSN":"000001CE00004DA00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 15:55:08.993 - [任务 23][Inventory2] - opened cdc tables: [_tapdata_heartbeat_table, Inventory2, Inventory] 
[INFO ] 2024-07-24 15:55:12.490 - [任务 23][Inventory2] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 15:55:12.492 - [任务 23][Inventory2] - Connector SQL Server incremental start succeed, tables: [Inventory2, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 19:05:26.566 - [任务 23][Inventory2] - Incremental sync completed 
[INFO ] 2024-07-24 19:05:26.569 - [任务 23][Inventory2] - Exception skipping - The current exception does not match the skip exception strategy, message: The client connection was terminated by the sqlserver server 
[ERROR] 2024-07-24 19:05:26.581 - [任务 23][Inventory2] - com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。 <-- Error Message -->
com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。

<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:613)
	...

<-- Full Stack Trace -->
com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。
	at io.tapdata.connector.mssql.exception.MssqlExceptionCollector.collectTerminateByServer(MssqlExceptionCollector.java:27)
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.startCdcRunner(MssqlCdcRunner.java:436)
	at io.tapdata.connector.mssql.MssqlConnector.streamRead(MssqlConnector.java:382)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:613)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:537)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeQuery(SQLServerPreparedStatement.java:456)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.startCdcRunner(MssqlCdcRunner.java:282)
	... 19 more

[INFO ] 2024-07-24 19:05:26.740 - [任务 23][Inventory2] - Job suspend in error handle 
[INFO ] 2024-07-24 19:05:26.741 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] running status set to false 
[INFO ] 2024-07-24 19:05:26.856 - [任务 23] - Task [任务 23] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:27.491 - [任务 23][Inventory2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 19:12:27.492 - [任务 23][Inventory2] - PDK connector node released: HazelcastSourcePdkDataNode-e042917a-c7f2-4504-8cc6-ebb088882b2c 
[INFO ] 2024-07-24 19:12:27.492 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] schema data cleaned 
[INFO ] 2024-07-24 19:12:27.492 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] monitor closed 
[INFO ] 2024-07-24 19:12:27.498 - [任务 23][Inventory2] - Node Inventory2[e042917a-c7f2-4504-8cc6-ebb088882b2c] close complete, cost 3030 ms 
[INFO ] 2024-07-24 19:12:27.498 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] running status set to false 
[INFO ] 2024-07-24 19:12:27.528 - [任务 23][wimT1] - PDK connector node stopped: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 19:12:27.528 - [任务 23][wimT1] - PDK connector node released: HazelcastTargetPdkDataNode-a7584fbc-9e48-40df-8c4c-0302828ed40b 
[INFO ] 2024-07-24 19:12:27.529 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] schema data cleaned 
[INFO ] 2024-07-24 19:12:27.529 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] monitor closed 
[INFO ] 2024-07-24 19:12:27.530 - [任务 23][wimT1] - Node wimT1[a7584fbc-9e48-40df-8c4c-0302828ed40b] close complete, cost 32 ms 
[INFO ] 2024-07-24 19:12:29.606 - [任务 23] - Task [任务 23] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:29.606 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 19:12:29.727 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@670aff8e 
[INFO ] 2024-07-24 19:12:29.733 - [任务 23] - Stop task milestones: 66a07bdcf604e81d788d00f2(任务 23)  
[INFO ] 2024-07-24 19:12:29.740 - [任务 23] - Stopped task aspect(s) 
[INFO ] 2024-07-24 19:12:29.740 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 19:12:29.761 - [任务 23] - Remove memory task client succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
[INFO ] 2024-07-24 19:12:29.761 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[66a07bdcf604e81d788d00f2] 
