[INFO ] 2024-07-18 17:02:17.513 - [Heartbeat-LocalMongo] - Start task milestones: 66979bd8b92eda1a86f521f4(Heartbeat-LocalMongo) 
[INFO ] 2024-07-18 17:02:17.684 - [Heartbeat-LocalMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 17:02:17.855 - [Heartbeat-LocalMongo] - The engine receives Heartbeat-LocalMongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 17:02:17.855 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:02:17.855 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:02:17.855 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 17:02:17.855 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 17:02:18.477 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 17:02:18.477 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 17:02:18.477 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 17:02:18.477 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 17:02:18.479 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721293338477,"lastTimes":1721293338477,"lastTN":0,"tableStats":{}} 
[WARN ] 2024-07-18 17:02:18.602 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721293331, "i": 1}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721293331, "i": 1}}, "signature": {"hash": {"$binary": {"base64": "4FVwM/IU5cjDMlDvfXL9seeLcUw=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-18 17:02:18.602 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-18 17:02:18.629 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 17:02:18.632 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 17:02:18.632 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 17:02:18.638 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-18 17:02:18.638 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721293338477,"lastTimes":1721293338477,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 17:02:18.639 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 17:02:18.639 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 17:27:27.770 - [Heartbeat-LocalMongo] - Start task milestones: 66979bd8b92eda1a86f521f4(Heartbeat-LocalMongo) 
[INFO ] 2024-07-18 17:27:29.993 - [Heartbeat-LocalMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 17:27:30.348 - [Heartbeat-LocalMongo] - The engine receives Heartbeat-LocalMongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 17:27:30.849 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:27:30.876 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:27:30.884 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 17:27:30.885 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 17:27:31.876 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 17:27:31.876 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 17:27:31.907 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 17:27:31.908 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721293338603,"lastTimes":1721293338605,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721293338477,"lastTimes":1721294544001,"lastTN":1101,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1100,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-18 17:27:32.108 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721293338477,"lastTimes":1721294544001,"lastTN":1101,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1100,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-18 17:27:32.125 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 17:27:32.140 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 17:27:32.332 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 17:30:25.552 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] running status set to false 
[INFO ] 2024-07-18 17:30:25.558 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-18 17:30:25.563 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e16b14a9-f910-4e30-ac8e-075be39aad0f 
[INFO ] 2024-07-18 17:30:25.565 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-e16b14a9-f910-4e30-ac8e-075be39aad0f 
[INFO ] 2024-07-18 17:30:25.572 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] schema data cleaned 
[INFO ] 2024-07-18 17:30:25.573 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] monitor closed 
[INFO ] 2024-07-18 17:30:25.581 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] close complete, cost 92 ms 
[INFO ] 2024-07-18 17:30:25.581 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] running status set to false 
[WARN ] 2024-07-18 17:30:25.634 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-c9c14f06-b566-4c20-ba03-6aa6bfb83433 
[INFO ] 2024-07-18 17:30:25.635 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-c9c14f06-b566-4c20-ba03-6aa6bfb83433 
[INFO ] 2024-07-18 17:30:25.635 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] schema data cleaned 
[INFO ] 2024-07-18 17:30:25.635 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] monitor closed 
[INFO ] 2024-07-18 17:30:25.637 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] close complete, cost 56 ms 
[INFO ] 2024-07-18 18:04:14.180 - [Heartbeat-LocalMongo] - Start task milestones: 66979bd8b92eda1a86f521f4(Heartbeat-LocalMongo) 
[INFO ] 2024-07-18 18:04:16.467 - [Heartbeat-LocalMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 18:04:16.536 - [Heartbeat-LocalMongo] - The engine receives Heartbeat-LocalMongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 18:04:17.444 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:04:17.473 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:04:17.487 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:04:17.487 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:04:18.768 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 18:04:18.769 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 18:04:18.828 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 18:04:18.828 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721293338477,"lastTimes":1721295024865,"lastTN":173,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1273,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-18 18:04:19.008 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721293338477,"lastTimes":1721295024865,"lastTN":173,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1273,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-18 18:04:19.022 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 18:04:19.033 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 18:04:19.285 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 18:09:04.443 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] running status set to false 
[INFO ] 2024-07-18 18:09:04.445 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-18 18:09:04.549 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e16b14a9-f910-4e30-ac8e-075be39aad0f 
[INFO ] 2024-07-18 18:48:24.361 - [Heartbeat-LocalMongo] - Start task milestones: 66979bd8b92eda1a86f521f4(Heartbeat-LocalMongo) 
[INFO ] 2024-07-18 18:48:31.917 - [Heartbeat-LocalMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 18:48:32.302 - [Heartbeat-LocalMongo] - The engine receives Heartbeat-LocalMongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 18:48:33.271 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:48:33.272 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:48:33.273 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:48:33.289 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:48:34.776 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 18:48:34.777 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 18:48:34.810 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 18:48:37.439 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721293338477,"lastTimes":1721297339895,"lastTN":281,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1554,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-18 18:48:37.665 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721293338477,"lastTimes":1721297339895,"lastTN":281,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1554,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-18 18:48:37.731 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 18:48:37.767 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 18:48:37.982 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 18:54:30.011 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] running status set to false 
[INFO ] 2024-07-18 18:54:30.013 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-18 18:54:30.031 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e16b14a9-f910-4e30-ac8e-075be39aad0f 
[INFO ] 2024-07-18 18:54:30.032 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-e16b14a9-f910-4e30-ac8e-075be39aad0f 
[INFO ] 2024-07-18 18:54:30.034 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] schema data cleaned 
[INFO ] 2024-07-18 18:54:30.034 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] monitor closed 
[INFO ] 2024-07-18 18:54:30.044 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e16b14a9-f910-4e30-ac8e-075be39aad0f] close complete, cost 51 ms 
[INFO ] 2024-07-18 18:54:30.044 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] running status set to false 
[WARN ] 2024-07-18 18:54:30.100 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-c9c14f06-b566-4c20-ba03-6aa6bfb83433 
[INFO ] 2024-07-18 18:54:30.100 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-c9c14f06-b566-4c20-ba03-6aa6bfb83433 
[INFO ] 2024-07-18 18:54:30.101 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] schema data cleaned 
[INFO ] 2024-07-18 18:54:30.101 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] monitor closed 
[INFO ] 2024-07-18 18:54:30.148 - [Heartbeat-LocalMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c9c14f06-b566-4c20-ba03-6aa6bfb83433] close complete, cost 59 ms 
