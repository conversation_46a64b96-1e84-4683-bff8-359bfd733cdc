[INFO ] 2024-09-27 17:21:10.636 - [任务 9] - Task initialization... 
[INFO ] 2024-09-27 17:21:10.748 - [任务 9] - Start task milestones: 66f678f4d320cf0fc1621d62(任务 9) 
[INFO ] 2024-09-27 17:21:10.748 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:21:10.846 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:21:10.939 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:21:10.940 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:21:10.940 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:21:10.941 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:21:11.887 - [任务 9][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 17:21:11.887 - [任务 9][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 17:21:11.887 - [任务 9][local3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 17:21:11.898 - [任务 9][local3307] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 17:21:11.901 - [任务 9][local3307] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-27 17:21:11.960 - [任务 9][local3307] - Initial sync started 
[INFO ] 2024-09-27 17:21:11.962 - [任务 9][local3307] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-09-27 17:21:12.075 - [任务 9][local3307] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-09-27 17:21:12.089 - [任务 9][local3307] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 17:21:12.096 - [任务 9][local3307] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-09-27 17:21:12.096 - [任务 9][local3307] - Initial sync completed 
[INFO ] 2024-09-27 17:21:12.097 - [任务 9][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 17:21:12.097 - [任务 9][local3307] - Initial sync completed 
[INFO ] 2024-09-27 17:21:12.097 - [任务 9][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 17:21:12.173 - [任务 9][local3307] - Starting mysql cdc, server name: eff59ff8-b080-4764-918b-f382f7911818 
[INFO ] 2024-09-27 17:21:12.174 - [任务 9][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"eff59ff8-b080-4764-918b-f382f7911818","offset":{"{\"server\":\"eff59ff8-b080-4764-918b-f382f7911818\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 222198137
  time.precision.mode: adaptive_time_microseconds
  database.server.name: eff59ff8-b080-4764-918b-f382f7911818
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-eff59ff8-b080-4764-918b-f382f7911818
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: eff59ff8-b080-4764-918b-f382f7911818
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[WARN ] 2024-09-27 17:21:12.184 - [任务 9][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727428871998} and {hostPort=localhost:43306, time=1727400071996} 
[INFO ] 2024-09-27 17:21:12.184 - [任务 9][LocalhostMaster] - Node(LocalhostMaster) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 17:21:12.184 - [任务 9][LocalhostMaster] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 17:21:12.335 - [任务 9][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[WARN ] 2024-09-27 17:21:13.337 - [任务 9][LocalhostMaster] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-27 17:24:57.026 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] running status set to false 
[INFO ] 2024-09-27 17:24:57.135 - [任务 9][local3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 17:24:57.135 - [任务 9][local3307] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 17:24:57.161 - [任务 9][local3307] - Incremental sync completed 
[INFO ] 2024-09-27 17:24:57.161 - [任务 9][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2 
[INFO ] 2024-09-27 17:24:57.161 - [任务 9][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2 
[INFO ] 2024-09-27 17:24:57.161 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] schema data cleaned 
[INFO ] 2024-09-27 17:24:57.161 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] monitor closed 
[INFO ] 2024-09-27 17:24:57.162 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] close complete, cost 137 ms 
[INFO ] 2024-09-27 17:24:57.202 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] running status set to false 
[INFO ] 2024-09-27 17:24:57.202 - [任务 9][LocalhostMaster] - PDK connector node stopped: HazelcastTargetPdkDataNode-7340f236-33f2-4396-af36-52977118b996 
[INFO ] 2024-09-27 17:24:57.202 - [任务 9][LocalhostMaster] - PDK connector node released: HazelcastTargetPdkDataNode-7340f236-33f2-4396-af36-52977118b996 
[INFO ] 2024-09-27 17:24:57.203 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] schema data cleaned 
[INFO ] 2024-09-27 17:24:57.204 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] monitor closed 
[INFO ] 2024-09-27 17:24:57.204 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] close complete, cost 40 ms 
[INFO ] 2024-09-27 17:25:01.599 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 17:25:01.599 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b61c2d3 
[INFO ] 2024-09-27 17:25:01.711 - [任务 9] - Stop task milestones: 66f678f4d320cf0fc1621d62(任务 9)  
[INFO ] 2024-09-27 17:25:01.720 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-09-27 17:25:01.720 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 17:25:01.751 - [任务 9] - Remove memory task client succeed, task: 任务 9[66f678f4d320cf0fc1621d62] 
[INFO ] 2024-09-27 17:25:01.752 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66f678f4d320cf0fc1621d62] 
[INFO ] 2024-09-27 17:25:17.587 - [任务 9] - Task initialization... 
[INFO ] 2024-09-27 17:25:17.587 - [任务 9] - Start task milestones: 66f678f4d320cf0fc1621d62(任务 9) 
[INFO ] 2024-09-27 17:25:17.781 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:25:17.781 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:25:17.838 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:25:17.838 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:25:17.838 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:25:17.838 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:25:42.261 - [任务 9][local3307] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2827/963530769@e893d2c failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:232)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:226)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } }, "signature" : { "hash" : { "$binary" : "NdyLnmLZ4jNEppDnPmqSjNhO7/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 
[ERROR] 2024-09-27 17:25:42.371 - [任务 9][local3307] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2827/963530769@e893d2c failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:232)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:226)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } }, "signature" : { "hash" : { "$binary" : "NdyLnmLZ4jNEppDnPmqSjNhO7/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2827/963530769@e893d2c failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:232)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:226)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } }, "signature" : { "hash" : { "$binary" : "NdyLnmLZ4jNEppDnPmqSjNhO7/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } }, "signature" : { "hash" : { "$binary" : "NdyLnmLZ4jNEppDnPmqSjNhO7/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2827/963530769@e893d2c failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:232)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:226)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } }, "signature" : { "hash" : { "$binary" : "NdyLnmLZ4jNEppDnPmqSjNhO7/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2827/963530769@e893d2c failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:232)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:226)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } }, "signature" : { "hash" : { "$binary" : "NdyLnmLZ4jNEppDnPmqSjNhO7/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:232)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:226)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } }, "signature" : { "hash" : { "$binary" : "NdyLnmLZ4jNEppDnPmqSjNhO7/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727429142, "i" : 37 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

[INFO ] 2024-09-27 17:25:42.385 - [任务 9][local3307] - Job suspend in error handle 
[INFO ] 2024-09-27 17:25:42.385 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] running status set to false 
[INFO ] 2024-09-27 17:25:42.385 - [任务 9][local3307] - PDK connector node stopped: null 
[INFO ] 2024-09-27 17:25:42.385 - [任务 9][local3307] - PDK connector node released: null 
[INFO ] 2024-09-27 17:25:42.385 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] schema data cleaned 
[INFO ] 2024-09-27 17:25:42.386 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] monitor closed 
[INFO ] 2024-09-27 17:25:42.405 - [任务 9][local3307] - Node local3307[741e3fa8-e5ce-4ee7-94d7-f90922ba3ef2] close complete, cost 15 ms 
[INFO ] 2024-09-27 17:25:42.405 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] running status set to false 
[INFO ] 2024-09-27 17:25:42.446 - [任务 9][LocalhostMaster] - PDK connector node stopped: HazelcastTargetPdkDataNode-7340f236-33f2-4396-af36-52977118b996 
[INFO ] 2024-09-27 17:25:42.447 - [任务 9][LocalhostMaster] - PDK connector node released: HazelcastTargetPdkDataNode-7340f236-33f2-4396-af36-52977118b996 
[INFO ] 2024-09-27 17:25:42.447 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] schema data cleaned 
[INFO ] 2024-09-27 17:25:42.447 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] monitor closed 
[INFO ] 2024-09-27 17:25:42.449 - [任务 9][LocalhostMaster] - Node LocalhostMaster[7340f236-33f2-4396-af36-52977118b996] close complete, cost 51 ms 
[INFO ] 2024-09-27 17:25:42.549 - [任务 9][LocalhostMaster] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed. 
[ERROR] 2024-09-27 17:25:42.549 - [任务 9][LocalhostMaster] - Unknown PDK exception occur, java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed. <-- Error Message -->
Unknown PDK exception occur, java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:105)
	io.tapdata.connector.mysql.MysqlJdbcContextV2.querySlaveStatus(MysqlJdbcContextV2.java:160)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:453)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:185)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	at io.tapdata.connector.mysql.util.MysqlUtil.buildMasterNode(MysqlUtil.java:231)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:104)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:105)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.querySlaveStatus(MysqlJdbcContextV2.java:160)
	at io.tapdata.connector.mysql.util.MysqlUtil.buildMasterNode(MysqlUtil.java:229)
	... 21 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 25 more

[INFO ] 2024-09-27 17:25:46.410 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 17:25:46.410 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3bc71400 
[INFO ] 2024-09-27 17:25:46.531 - [任务 9] - Stop task milestones: 66f678f4d320cf0fc1621d62(任务 9)  
[INFO ] 2024-09-27 17:25:46.554 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-09-27 17:25:46.555 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 17:25:46.600 - [任务 9] - Remove memory task client succeed, task: 任务 9[66f678f4d320cf0fc1621d62] 
[INFO ] 2024-09-27 17:25:46.604 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66f678f4d320cf0fc1621d62] 
