[TRACE] 2025-03-02 14:02:57.434 - [任务 33] - Task initialization... 
[TRACE] 2025-03-02 14:02:57.436 - [任务 33] - Start task milestones: 67c3f4511b7f486ff6b4e56a(任务 33) 
[INFO ] 2025-03-02 14:02:57.566 - [任务 33] - Loading table structure completed 
[TRACE] 2025-03-02 14:02:57.647 - [任务 33] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-03-02 14:02:57.647 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 14:02:57.686 - [任务 33] - Task started 
[TRACE] 2025-03-02 14:02:57.686 - [任务 33][Pg] - Node Pg[b36619bc-ab0f-459a-8faa-1a0e2a872d8f] start preload schema,table counts: 3 
[TRACE] 2025-03-02 14:02:57.686 - [任务 33][Sybase-Wim] - Node Sybase-Wim[c4583692-5d5e-451a-9888-ef0754cb5984] start preload schema,table counts: 3 
[TRACE] 2025-03-02 14:02:57.686 - [任务 33][Sybase-Wim] - Node Sybase-Wim[c4583692-5d5e-451a-9888-ef0754cb5984] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 14:02:57.686 - [任务 33][Pg] - Node Pg[b36619bc-ab0f-459a-8faa-1a0e2a872d8f] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 14:02:57.893 - [任务 33][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 14:02:58.417 - [任务 33][Sybase-Wim] - Sink connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-02 14:02:58.418 - [任务 33][Sybase-Wim] - Node(Sybase-Wim) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 14:02:58.419 - [任务 33][Sybase-Wim] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 14:02:58.622 - [任务 33][Sybase-Wim] - Apply table structure to target database 
[TRACE] 2025-03-02 14:02:58.739 - [任务 33][Sybase-Wim] - The table testTimeStampTwoWithNoIndex has already exist. 
[INFO ] 2025-03-02 14:02:58.815 - [任务 33][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 14:02:58.816 - [任务 33][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 14:02:58.816 - [任务 33][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 14:02:58.816 - [任务 33][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 14:02:59.629 - [任务 33][Sybase-Wim] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_ithNoIndex0e4334c51a7d  on  lisTest.dbo.testTimeStampTwoWithNoIndex ( _no_pk_hash  asc)] 
[TRACE] 2025-03-02 14:02:59.717 - [任务 33][Sybase-Wim] - The table testTimeStampTwoWithIndex has already exist. 
[WARN ] 2025-03-02 14:02:59.924 - [任务 33][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 14:03:00.368 - [任务 33][Pg] - new logical replication slot created, slotName:tapdata_cdc_29f63bcd_45bf_4aef_8cb9_b35a5ca62234 
[INFO ] 2025-03-02 14:03:00.369 - [任务 33][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 14:03:00.369 - [任务 33][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[WARN ] 2025-03-02 14:03:00.443 - [任务 33][Sybase-Wim] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_oWithIndexf8e53a6c60d1  on  lisTest.dbo.testTimeStampTwoWithIndex ( _no_pk_hash  asc)] 
[INFO ] 2025-03-02 14:03:00.443 - [任务 33][Pg] - Batch read completed. 
[TRACE] 2025-03-02 14:03:00.444 - [任务 33][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 14:03:00.444 - [任务 33][Pg] - Initial sync completed 
[TRACE] 2025-03-02 14:03:00.445 - [任务 33][Pg] - Starting stream read, table list: [testTimeStampTwoWithNoIndex, testTimeStampTwoWithIndex, testTimeStamp], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 14:03:00.445 - [任务 33][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 14:03:00.512 - [任务 33][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 14:03:00.512 - [任务 33][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_29f63bcd_45bf_4aef_8cb9_b35a5ca62234 
[TRACE] 2025-03-02 14:03:00.662 - [任务 33][Sybase-Wim] - The table testTimeStamp has already exist. 
[TRACE] 2025-03-02 14:03:02.407 - [任务 33][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampTwoWithNoIndex, testTimeStampTwoWithIndex, testTimeStamp], data change syncing 
[TRACE] 2025-03-02 14:48:36.385 - [任务 33][Pg] - Node Pg[b36619bc-ab0f-459a-8faa-1a0e2a872d8f] running status set to false 
[TRACE] 2025-03-02 14:48:36.788 - [任务 33][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 14:48:36.802 - [任务 33][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_b36619bc-ab0f-459a-8faa-1a0e2a872d8f_1740895378236 
[TRACE] 2025-03-02 14:48:36.802 - [任务 33][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_b36619bc-ab0f-459a-8faa-1a0e2a872d8f_1740895378236 
[TRACE] 2025-03-02 14:48:36.802 - [任务 33][Pg] - Node Pg[b36619bc-ab0f-459a-8faa-1a0e2a872d8f] schema data cleaned 
[TRACE] 2025-03-02 14:48:36.802 - [任务 33][Pg] - Node Pg[b36619bc-ab0f-459a-8faa-1a0e2a872d8f] monitor closed 
[TRACE] 2025-03-02 14:48:36.803 - [任务 33][Pg] - Node Pg[b36619bc-ab0f-459a-8faa-1a0e2a872d8f] close complete, cost 440 ms 
[TRACE] 2025-03-02 14:48:36.803 - [任务 33][Sybase-Wim] - Node Sybase-Wim[c4583692-5d5e-451a-9888-ef0754cb5984] running status set to false 
[TRACE] 2025-03-02 14:48:37.082 - [任务 33][Sybase-Wim] - PDK connector node stopped: HazelcastTargetPdkDataNode_c4583692-5d5e-451a-9888-ef0754cb5984_1740895378321 
[TRACE] 2025-03-02 14:48:37.082 - [任务 33][Sybase-Wim] - PDK connector node released: HazelcastTargetPdkDataNode_c4583692-5d5e-451a-9888-ef0754cb5984_1740895378321 
[TRACE] 2025-03-02 14:48:37.083 - [任务 33][Sybase-Wim] - Node Sybase-Wim[c4583692-5d5e-451a-9888-ef0754cb5984] schema data cleaned 
[TRACE] 2025-03-02 14:48:37.084 - [任务 33][Sybase-Wim] - Node Sybase-Wim[c4583692-5d5e-451a-9888-ef0754cb5984] monitor closed 
[TRACE] 2025-03-02 14:48:37.289 - [任务 33][Sybase-Wim] - Node Sybase-Wim[c4583692-5d5e-451a-9888-ef0754cb5984] close complete, cost 280 ms 
[TRACE] 2025-03-02 14:48:40.120 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 14:48:40.124 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a8c03c3 
[TRACE] 2025-03-02 14:48:40.125 - [任务 33] - Stop task milestones: 67c3f4511b7f486ff6b4e56a(任务 33)  
[TRACE] 2025-03-02 14:48:40.303 - [任务 33] - Stopped task aspect(s) 
[TRACE] 2025-03-02 14:48:40.303 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 14:48:40.382 - [任务 33] - Task stopped. 
[TRACE] 2025-03-02 14:48:40.383 - [任务 33] - Remove memory task client succeed, task: 任务 33[67c3f4511b7f486ff6b4e56a] 
[TRACE] 2025-03-02 14:48:40.383 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67c3f4511b7f486ff6b4e56a] 
