[INFO ] 2024-07-26 19:44:51.421 - [任务 1] - Start task milestones: 66a38bd4183a6022d03baa76(任务 1) 
[INFO ] 2024-07-26 19:44:51.518 - [任务 1] - Task initialization... 
[INFO ] 2024-07-26 19:44:52.333 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:44:52.345 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:44:52.769 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:44:52.769 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:44:52.770 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 19:44:52.770 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:44:54.069 - [任务 1][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 19:44:54.069 - [任务 1][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 19:44:54.069 - [任务 1][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:44:54.224 - [任务 1][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721994293,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:44:54.289 - [任务 1][Testabc] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 19:44:54.328 - [任务 1][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 19:44:54.328 - [任务 1][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 19:44:54.425 - [任务 1][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 19:44:54.425 - [任务 1][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 19:44:54.497 - [任务 1][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:44:54.501 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:44:54.501 - [任务 1][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 19:44:54.516 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:44:54.517 - [任务 1][CUSTOMER] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"cdcOffset":1721994293,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:44:54.719 - [任务 1][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 19:46:42.482 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] running status set to false 
[INFO ] 2024-07-26 19:46:42.482 - [任务 1][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-729b4bb2-bd17-47a6-8896-6d9a56b5182f 
[INFO ] 2024-07-26 19:46:42.484 - [任务 1][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-729b4bb2-bd17-47a6-8896-6d9a56b5182f 
[INFO ] 2024-07-26 19:46:42.484 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] schema data cleaned 
[INFO ] 2024-07-26 19:46:42.495 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] monitor closed 
[INFO ] 2024-07-26 19:46:42.496 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] close complete, cost 54 ms 
[INFO ] 2024-07-26 19:46:42.496 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] running status set to false 
[INFO ] 2024-07-26 19:46:42.529 - [任务 1][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 19:46:42.552 - [任务 1][Testabc] - PDK connector node stopped: HazelcastTargetPdkDataNode-ff97d93f-c194-4480-b499-ac3ca4d6f39d 
[INFO ] 2024-07-26 19:46:42.552 - [任务 1][Testabc] - PDK connector node released: HazelcastTargetPdkDataNode-ff97d93f-c194-4480-b499-ac3ca4d6f39d 
[INFO ] 2024-07-26 19:46:42.552 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] schema data cleaned 
[INFO ] 2024-07-26 19:46:42.553 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] monitor closed 
[INFO ] 2024-07-26 19:46:42.761 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] close complete, cost 58 ms 
[INFO ] 2024-07-26 19:46:45.387 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:46:45.388 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@19df1e7d 
[INFO ] 2024-07-26 19:46:45.391 - [任务 1] - Stop task milestones: 66a38bd4183a6022d03baa76(任务 1)  
[INFO ] 2024-07-26 19:46:45.513 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:46:45.514 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:46:45.557 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a38bd4183a6022d03baa76] 
[INFO ] 2024-07-26 19:46:45.557 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a38bd4183a6022d03baa76] 
[INFO ] 2024-07-26 19:47:02.245 - [任务 1] - Start task milestones: 66a38bd4183a6022d03baa76(任务 1) 
[INFO ] 2024-07-26 19:47:02.410 - [任务 1] - Task initialization... 
[INFO ] 2024-07-26 19:47:37.114 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:47:37.195 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:47:37.196 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:47:37.200 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:47:37.202 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:47:37.202 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:47:38.178 - [任务 1][Testabc] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 19:47:38.178 - [任务 1][Testabc] - Table "test.Testabc" exists, skip auto create table 
[INFO ] 2024-07-26 19:47:38.178 - [任务 1][Testabc] - The table Testabc has already exist. 
[INFO ] 2024-07-26 19:47:38.244 - [任务 1][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 19:47:38.248 - [任务 1][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 19:47:38.248 - [任务 1][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:47:38.396 - [任务 1][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721994458,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:47:38.397 - [任务 1][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 19:47:38.404 - [任务 1][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 19:47:38.407 - [任务 1][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 19:47:38.445 - [任务 1][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 19:47:38.445 - [任务 1][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:47:38.445 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:47:38.445 - [任务 1][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 19:47:38.447 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:47:38.461 - [任务 1][CUSTOMER] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"cdcOffset":1721994458,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:47:38.461 - [任务 1][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 19:48:16.316 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] running status set to false 
[INFO ] 2024-07-26 19:48:16.317 - [任务 1][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-729b4bb2-bd17-47a6-8896-6d9a56b5182f 
[INFO ] 2024-07-26 19:48:16.319 - [任务 1][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-729b4bb2-bd17-47a6-8896-6d9a56b5182f 
[INFO ] 2024-07-26 19:48:16.319 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] schema data cleaned 
[INFO ] 2024-07-26 19:48:16.321 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] monitor closed 
[INFO ] 2024-07-26 19:48:16.325 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] close complete, cost 51 ms 
[INFO ] 2024-07-26 19:48:16.325 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] running status set to false 
[INFO ] 2024-07-26 19:48:16.344 - [任务 1][Testabc] - PDK connector node stopped: HazelcastTargetPdkDataNode-ff97d93f-c194-4480-b499-ac3ca4d6f39d 
[INFO ] 2024-07-26 19:48:16.344 - [任务 1][Testabc] - PDK connector node released: HazelcastTargetPdkDataNode-ff97d93f-c194-4480-b499-ac3ca4d6f39d 
[INFO ] 2024-07-26 19:48:16.345 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] schema data cleaned 
[INFO ] 2024-07-26 19:48:16.345 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] monitor closed 
[INFO ] 2024-07-26 19:48:16.553 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] close complete, cost 23 ms 
[INFO ] 2024-07-26 19:48:16.805 - [任务 1][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 19:48:16.805 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:48:16.809 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2a61910e 
[INFO ] 2024-07-26 19:48:16.809 - [任务 1] - Stop task milestones: 66a38bd4183a6022d03baa76(任务 1)  
[INFO ] 2024-07-26 19:48:16.938 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:48:16.938 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:48:16.969 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a38bd4183a6022d03baa76] 
[INFO ] 2024-07-26 19:48:17.176 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a38bd4183a6022d03baa76] 
[INFO ] 2024-07-26 19:48:40.990 - [任务 1] - Start task milestones: 66a38bd4183a6022d03baa76(任务 1) 
[INFO ] 2024-07-26 19:48:41.072 - [任务 1] - Task initialization... 
[INFO ] 2024-07-26 19:48:41.191 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:48:41.260 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:48:41.261 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:48:41.261 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:48:41.463 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:48:41.464 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:48:42.245 - [任务 1][Testabc] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 19:48:42.265 - [任务 1][Testabc] - Table "test.Testabc" exists, skip auto create table 
[INFO ] 2024-07-26 19:48:42.266 - [任务 1][Testabc] - The table Testabc has already exist. 
[INFO ] 2024-07-26 19:48:42.310 - [任务 1][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 19:48:42.311 - [任务 1][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 19:48:42.311 - [任务 1][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:48:42.478 - [任务 1][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721994522,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:48:42.543 - [任务 1][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 19:48:42.563 - [任务 1][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 19:48:42.568 - [任务 1][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 19:48:42.601 - [任务 1][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 19:48:42.601 - [任务 1][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:48:42.601 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:48:42.602 - [任务 1][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 19:48:42.606 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:48:42.606 - [任务 1][CUSTOMER] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"cdcOffset":1721994522,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:48:42.810 - [任务 1][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 19:49:07.425 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] running status set to false 
[INFO ] 2024-07-26 19:49:07.445 - [任务 1][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-729b4bb2-bd17-47a6-8896-6d9a56b5182f 
[INFO ] 2024-07-26 19:49:07.445 - [任务 1][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-729b4bb2-bd17-47a6-8896-6d9a56b5182f 
[INFO ] 2024-07-26 19:49:07.446 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] schema data cleaned 
[INFO ] 2024-07-26 19:49:07.446 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] monitor closed 
[INFO ] 2024-07-26 19:49:07.448 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] close complete, cost 54 ms 
[INFO ] 2024-07-26 19:49:07.448 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] running status set to false 
[INFO ] 2024-07-26 19:49:07.470 - [任务 1][Testabc] - PDK connector node stopped: HazelcastTargetPdkDataNode-ff97d93f-c194-4480-b499-ac3ca4d6f39d 
[INFO ] 2024-07-26 19:49:07.470 - [任务 1][Testabc] - PDK connector node released: HazelcastTargetPdkDataNode-ff97d93f-c194-4480-b499-ac3ca4d6f39d 
[INFO ] 2024-07-26 19:49:07.472 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] schema data cleaned 
[INFO ] 2024-07-26 19:49:07.472 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] monitor closed 
[INFO ] 2024-07-26 19:49:07.680 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] close complete, cost 24 ms 
[INFO ] 2024-07-26 19:49:08.088 - [任务 1][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 19:49:12.050 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:49:12.051 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@f820a78 
[INFO ] 2024-07-26 19:49:12.051 - [任务 1] - Stop task milestones: 66a38bd4183a6022d03baa76(任务 1)  
[INFO ] 2024-07-26 19:49:12.168 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:49:12.171 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:49:12.193 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a38bd4183a6022d03baa76] 
[INFO ] 2024-07-26 19:49:12.196 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a38bd4183a6022d03baa76] 
[INFO ] 2024-07-26 19:50:25.721 - [任务 1] - Start task milestones: 66a38bd4183a6022d03baa76(任务 1) 
[INFO ] 2024-07-26 19:50:25.793 - [任务 1] - Task initialization... 
[INFO ] 2024-07-26 19:50:25.928 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:50:25.934 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:50:25.999 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:50:25.999 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:50:25.999 - [任务 1][Testabc] - Node Testabc[ff97d93f-c194-4480-b499-ac3ca4d6f39d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:50:25.999 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:50:26.904 - [任务 1][Testabc] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 19:50:26.904 - [任务 1][Testabc] - Table "test.Testabc" exists, skip auto create table 
[INFO ] 2024-07-26 19:50:26.904 - [任务 1][Testabc] - The table Testabc has already exist. 
[INFO ] 2024-07-26 19:50:26.993 - [任务 1][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 19:50:26.993 - [任务 1][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 19:50:26.993 - [任务 1][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:50:27.091 - [任务 1][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721994626,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:50:27.156 - [任务 1][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 19:50:27.156 - [任务 1][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 19:50:27.186 - [任务 1][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 19:50:27.186 - [任务 1][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 19:50:27.238 - [任务 1][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:50:27.238 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:50:27.242 - [任务 1][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 19:50:27.242 - [任务 1][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 19:50:27.258 - [任务 1][CUSTOMER] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"cdcOffset":1721994626,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:50:27.258 - [任务 1][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 20:00:19.375 - [任务 1][CUSTOMER] - Node CUSTOMER[729b4bb2-bd17-47a6-8896-6d9a56b5182f] running status set to false 
