[INFO ] 2024-07-24 15:33:05.951 - [TestBlob] - Task initialization... 
[INFO ] 2024-07-24 15:33:05.953 - [TestBlob] - Start task milestones: 66a0ad6cf604e81d788d048c(TestBlob) 
[INFO ] 2024-07-24 15:33:06.059 - [TestBlob] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-24 15:33:06.103 - [TestBlob] - The engine receives TestBlob task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 15:33:06.177 - [TestBlob][testBlob] - Node testBlob[e50b5761-0a6a-4e5f-b577-81550abea90d] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:33:06.177 - [TestBlob][Testblob] - Node Testblob[5d54bd87-9fd4-403d-8dff-9962286461d4] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:33:06.177 - [TestBlob][testBlob] - Node testBlob[e50b5761-0a6a-4e5f-b577-81550abea90d] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 15:33:06.178 - [TestBlob][Testblob] - Node Testblob[5d54bd87-9fd4-403d-8dff-9962286461d4] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 15:33:07.164 - [TestBlob][testBlob] - Source node "testBlob" read batch size: 100 
[INFO ] 2024-07-24 15:33:07.165 - [TestBlob][testBlob] - Source node "testBlob" event queue capacity: 200 
[INFO ] 2024-07-24 15:33:07.165 - [TestBlob][testBlob] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 15:33:07.195 - [TestBlob][testBlob] - batch offset found: {},stream offset found: {"filename":"binlog.000033","position":48029103,"gtidSet":""} 
[INFO ] 2024-07-24 15:33:07.260 - [TestBlob][testBlob] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 15:33:07.261 - [TestBlob][Testblob] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 15:33:07.272 - [TestBlob][testBlob] - Initial sync started 
[INFO ] 2024-07-24 15:33:07.273 - [TestBlob][testBlob] - Starting batch read, table name: testBlob, offset: null 
[INFO ] 2024-07-24 15:33:07.317 - [TestBlob][testBlob] - Table testBlob is going to be initial synced 
[INFO ] 2024-07-24 15:33:07.322 - [TestBlob][testBlob] - Table [testBlob] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 15:33:07.333 - [TestBlob][testBlob] - Query table 'testBlob' counts: 2 
[INFO ] 2024-07-24 15:33:07.333 - [TestBlob][testBlob] - Initial sync completed 
[INFO ] 2024-07-24 15:33:07.334 - [TestBlob][testBlob] - Incremental sync starting... 
[INFO ] 2024-07-24 15:33:07.334 - [TestBlob][testBlob] - Initial sync completed 
[INFO ] 2024-07-24 15:33:07.387 - [TestBlob][testBlob] - Starting stream read, table list: [testBlob, _tapdata_heartbeat_table], offset: {"filename":"binlog.000033","position":48029103,"gtidSet":""} 
[INFO ] 2024-07-24 15:33:07.388 - [TestBlob][testBlob] - Starting mysql cdc, server name: dad54617-b6b5-4bc8-9401-b3ec556795fb 
[INFO ] 2024-07-24 15:33:07.591 - [TestBlob][testBlob] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1838261880
  time.precision.mode: adaptive_time_microseconds
  database.server.name: dad54617-b6b5-4bc8-9401-b3ec556795fb
  database.port: 3306
  threadName: Debezium-Mysql-Connector-dad54617-b6b5-4bc8-9401-b3ec556795fb
  database.hostname: localhost
  database.password: ********
  name: dad54617-b6b5-4bc8-9401-b3ec556795fb
  pdk.offset.string: {"name":"dad54617-b6b5-4bc8-9401-b3ec556795fb","offset":{"{\"server\":\"dad54617-b6b5-4bc8-9401-b3ec556795fb\"}":"{\"file\":\"binlog.000033\",\"pos\":48029103,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testBlob,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-24 15:33:07.753 - [TestBlob][testBlob] - Connector Mysql incremental start succeed, tables: [testBlob, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 15:54:32.774 - [TestBlob][testBlob] - Node testBlob[e50b5761-0a6a-4e5f-b577-81550abea90d] running status set to false 
[INFO ] 2024-07-24 15:54:32.824 - [TestBlob][testBlob] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-24 15:54:32.825 - [TestBlob][testBlob] - Mysql binlog reader stopped 
[INFO ] 2024-07-24 15:54:32.835 - [TestBlob][testBlob] - Incremental sync completed 
[INFO ] 2024-07-24 15:54:32.836 - [TestBlob][testBlob] - PDK connector node stopped: HazelcastSourcePdkDataNode-e50b5761-0a6a-4e5f-b577-81550abea90d 
[INFO ] 2024-07-24 15:54:32.836 - [TestBlob][testBlob] - PDK connector node released: HazelcastSourcePdkDataNode-e50b5761-0a6a-4e5f-b577-81550abea90d 
[INFO ] 2024-07-24 15:54:32.837 - [TestBlob][testBlob] - Node testBlob[e50b5761-0a6a-4e5f-b577-81550abea90d] schema data cleaned 
[INFO ] 2024-07-24 15:54:32.839 - [TestBlob][testBlob] - Node testBlob[e50b5761-0a6a-4e5f-b577-81550abea90d] monitor closed 
[INFO ] 2024-07-24 15:54:32.839 - [TestBlob][testBlob] - Node testBlob[e50b5761-0a6a-4e5f-b577-81550abea90d] close complete, cost 93 ms 
[INFO ] 2024-07-24 15:54:32.863 - [TestBlob][Testblob] - Node Testblob[5d54bd87-9fd4-403d-8dff-9962286461d4] running status set to false 
[INFO ] 2024-07-24 15:54:32.863 - [TestBlob][Testblob] - PDK connector node stopped: HazelcastTargetPdkDataNode-5d54bd87-9fd4-403d-8dff-9962286461d4 
[INFO ] 2024-07-24 15:54:32.863 - [TestBlob][Testblob] - PDK connector node released: HazelcastTargetPdkDataNode-5d54bd87-9fd4-403d-8dff-9962286461d4 
[INFO ] 2024-07-24 15:54:32.863 - [TestBlob][Testblob] - Node Testblob[5d54bd87-9fd4-403d-8dff-9962286461d4] schema data cleaned 
[INFO ] 2024-07-24 15:54:32.863 - [TestBlob][Testblob] - Node Testblob[5d54bd87-9fd4-403d-8dff-9962286461d4] monitor closed 
[INFO ] 2024-07-24 15:54:32.866 - [TestBlob][Testblob] - Node Testblob[5d54bd87-9fd4-403d-8dff-9962286461d4] close complete, cost 26 ms 
[INFO ] 2024-07-24 15:54:37.564 - [TestBlob] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 15:54:37.565 - [TestBlob] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6a6af39f 
[INFO ] 2024-07-24 15:54:37.690 - [TestBlob] - Stop task milestones: 66a0ad6cf604e81d788d048c(TestBlob)  
[INFO ] 2024-07-24 15:54:37.690 - [TestBlob] - Stopped task aspect(s) 
[INFO ] 2024-07-24 15:54:37.690 - [TestBlob] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 15:54:37.724 - [TestBlob] - Remove memory task client succeed, task: TestBlob[66a0ad6cf604e81d788d048c] 
[INFO ] 2024-07-24 15:54:37.726 - [TestBlob] - Destroy memory task client cache succeed, task: TestBlob[66a0ad6cf604e81d788d048c] 
