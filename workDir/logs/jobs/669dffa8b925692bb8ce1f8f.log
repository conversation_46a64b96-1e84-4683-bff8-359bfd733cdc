[INFO ] 2024-07-22 14:44:32.902 - [任务 6] - Start task milestones: 669dffa8b925692bb8ce1f8f(任务 6) 
[INFO ] 2024-07-22 14:44:33.109 - [任务 6] - Task initialization... 
[INFO ] 2024-07-22 14:44:33.156 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-22 14:44:33.237 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-22 14:44:33.237 - [任务 6][testBlob] - Node testBlob[69b62b9a-b9a1-4181-99f6-caee5861947c] start preload schema,table counts: 1 
[INFO ] 2024-07-22 14:44:33.238 - [任务 6][testBlob] - Node testBlob[c4cfebe6-98c9-4313-ae4a-91b7dcef3418] start preload schema,table counts: 1 
[INFO ] 2024-07-22 14:44:33.238 - [任务 6][testBlob] - Node testBlob[c4cfebe6-98c9-4313-ae4a-91b7dcef3418] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 14:44:33.441 - [任务 6][testBlob] - Node testBlob[69b62b9a-b9a1-4181-99f6-caee5861947c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 14:44:34.051 - [任务 6][testBlob] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-22 14:44:34.074 - [任务 6][testBlob] - Source node "testBlob" read batch size: 100 
[INFO ] 2024-07-22 14:44:34.074 - [任务 6][testBlob] - Source node "testBlob" event queue capacity: 200 
[INFO ] 2024-07-22 14:44:34.076 - [任务 6][testBlob] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-22 14:44:34.076 - [任务 6][testBlob] - batch offset found: {},stream offset found: {"filename":"binlog.000033","position":34096324,"gtidSet":""} 
[INFO ] 2024-07-22 14:44:34.146 - [任务 6][testBlob] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-22 14:44:34.146 - [任务 6][testBlob] - Initial sync started 
[INFO ] 2024-07-22 14:44:34.148 - [任务 6][testBlob] - Starting batch read, table name: testBlob, offset: null 
[INFO ] 2024-07-22 14:44:34.149 - [任务 6][testBlob] - Table testBlob is going to be initial synced 
[INFO ] 2024-07-22 14:44:34.189 - [任务 6][testBlob] - Table [testBlob] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-22 14:44:34.190 - [任务 6][testBlob] - Query table 'testBlob' counts: 1 
[INFO ] 2024-07-22 14:44:34.190 - [任务 6][testBlob] - Initial sync completed 
[INFO ] 2024-07-22 14:44:34.190 - [任务 6][testBlob] - Incremental sync starting... 
[INFO ] 2024-07-22 14:44:34.191 - [任务 6][testBlob] - Initial sync completed 
[INFO ] 2024-07-22 14:44:34.221 - [任务 6][testBlob] - Starting stream read, table list: [testBlob, _tapdata_heartbeat_table], offset: {"filename":"binlog.000033","position":34096324,"gtidSet":""} 
[INFO ] 2024-07-22 14:44:34.221 - [任务 6][testBlob] - Starting mysql cdc, server name: 6ff0674c-8d75-4d6f-95ed-aa2d042afc15 
[INFO ] 2024-07-22 14:44:34.291 - [任务 6][testBlob] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 488081838
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6ff0674c-8d75-4d6f-95ed-aa2d042afc15
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6ff0674c-8d75-4d6f-95ed-aa2d042afc15
  database.hostname: localhost
  database.password: ********
  name: 6ff0674c-8d75-4d6f-95ed-aa2d042afc15
  pdk.offset.string: {"name":"6ff0674c-8d75-4d6f-95ed-aa2d042afc15","offset":{"{\"server\":\"6ff0674c-8d75-4d6f-95ed-aa2d042afc15\"}":"{\"file\":\"binlog.000033\",\"pos\":34096324,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testBlob,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-22 14:44:34.292 - [任务 6][testBlob] - Connector Mysql incremental start succeed, tables: [testBlob, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-22 15:41:50.808 - [任务 6][testBlob] - Node testBlob[c4cfebe6-98c9-4313-ae4a-91b7dcef3418] running status set to false 
[INFO ] 2024-07-22 15:41:50.857 - [任务 6][testBlob] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-22 15:41:50.858 - [任务 6][testBlob] - Mysql binlog reader stopped 
[INFO ] 2024-07-22 15:41:50.860 - [任务 6][testBlob] - PDK connector node stopped: HazelcastSourcePdkDataNode-c4cfebe6-98c9-4313-ae4a-91b7dcef3418 
[INFO ] 2024-07-22 15:41:50.860 - [任务 6][testBlob] - Incremental sync completed 
[INFO ] 2024-07-22 15:41:50.863 - [任务 6][testBlob] - PDK connector node released: HazelcastSourcePdkDataNode-c4cfebe6-98c9-4313-ae4a-91b7dcef3418 
[INFO ] 2024-07-22 15:41:50.863 - [任务 6][testBlob] - Node testBlob[c4cfebe6-98c9-4313-ae4a-91b7dcef3418] schema data cleaned 
[INFO ] 2024-07-22 15:41:50.863 - [任务 6][testBlob] - Node testBlob[c4cfebe6-98c9-4313-ae4a-91b7dcef3418] monitor closed 
[INFO ] 2024-07-22 15:41:50.865 - [任务 6][testBlob] - Node testBlob[c4cfebe6-98c9-4313-ae4a-91b7dcef3418] close complete, cost 71 ms 
[INFO ] 2024-07-22 15:41:50.876 - [任务 6][testBlob] - Node testBlob[69b62b9a-b9a1-4181-99f6-caee5861947c] running status set to false 
[INFO ] 2024-07-22 15:41:50.876 - [任务 6][testBlob] - Clickhouse Optimize Table start, tables: ["testBlob"] 
[WARN ] 2024-07-22 15:41:51.447 - [任务 6][testBlob] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-22 15:41:51.447 - [任务 6][testBlob] - PDK connector node stopped: HazelcastTargetPdkDataNode-69b62b9a-b9a1-4181-99f6-caee5861947c 
[INFO ] 2024-07-22 15:41:51.447 - [任务 6][testBlob] - PDK connector node released: HazelcastTargetPdkDataNode-69b62b9a-b9a1-4181-99f6-caee5861947c 
[INFO ] 2024-07-22 15:41:51.448 - [任务 6][testBlob] - Node testBlob[69b62b9a-b9a1-4181-99f6-caee5861947c] schema data cleaned 
[INFO ] 2024-07-22 15:41:51.448 - [任务 6][testBlob] - Node testBlob[69b62b9a-b9a1-4181-99f6-caee5861947c] monitor closed 
[INFO ] 2024-07-22 15:41:51.649 - [任务 6][testBlob] - Node testBlob[69b62b9a-b9a1-4181-99f6-caee5861947c] close complete, cost 584 ms 
[INFO ] 2024-07-22 15:41:55.553 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-22 15:41:55.558 - [任务 6] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49907e9d 
[INFO ] 2024-07-22 15:41:55.559 - [任务 6] - Stop task milestones: 669dffa8b925692bb8ce1f8f(任务 6)  
[INFO ] 2024-07-22 15:41:55.689 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-07-22 15:41:55.690 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-07-22 15:41:55.731 - [任务 6] - Remove memory task client succeed, task: 任务 6[669dffa8b925692bb8ce1f8f] 
[INFO ] 2024-07-22 15:41:55.732 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[669dffa8b925692bb8ce1f8f] 
