[INFO ] 2024-07-05 12:03:24.102 - [Heartbeat-TAPDATA2Oracle] - Start task milestones: 6687708bed8812650a8a33f0(Heartbeat-TAPDATA2Oracle) 
[INFO ] 2024-07-05 12:03:24.249 - [Heartbeat-TAPDATA2Oracle] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-05 12:03:24.341 - [Heartbeat-TAPDATA2Oracle] - The engine receives Heartbeat-TAPDATA2Oracle task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 12:03:24.372 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7abe052e-6388-4e4b-af23-f72eb2e79ff7] start preload schema,table counts: 1 
[INFO ] 2024-07-05 12:03:24.372 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[be7d9626-ddf1-46b6-8fbd-625861f6497a] start preload schema,table counts: 1 
[INFO ] 2024-07-05 12:03:24.372 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7abe052e-6388-4e4b-af23-f72eb2e79ff7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 12:03:24.372 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[be7d9626-ddf1-46b6-8fbd-625861f6497a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 12:03:26.125 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-05 12:03:26.125 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-05 12:03:26.125 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 12:03:26.126 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1720152206124,"lastTimes":1720152206124,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-05 12:03:26.175 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-05 12:03:26.186 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-05 12:03:26.193 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-05 12:03:26.193 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-05 12:03:26.194 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-05 12:03:26.195 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1720152206124,"lastTimes":1720152206124,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-05 12:03:26.195 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-05 12:03:26.195 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-05 12:03:27.150 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 12:08:12.780 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[be7d9626-ddf1-46b6-8fbd-625861f6497a] running status set to false 
[INFO ] 2024-07-05 12:08:12.796 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-05 12:08:12.796 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-be7d9626-ddf1-46b6-8fbd-625861f6497a 
[INFO ] 2024-07-05 12:08:12.796 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-be7d9626-ddf1-46b6-8fbd-625861f6497a 
[INFO ] 2024-07-05 12:08:12.796 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[be7d9626-ddf1-46b6-8fbd-625861f6497a] schema data cleaned 
[INFO ] 2024-07-05 12:08:12.798 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[be7d9626-ddf1-46b6-8fbd-625861f6497a] monitor closed 
[INFO ] 2024-07-05 12:08:12.798 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[be7d9626-ddf1-46b6-8fbd-625861f6497a] close complete, cost 23 ms 
[INFO ] 2024-07-05 12:08:12.798 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7abe052e-6388-4e4b-af23-f72eb2e79ff7] running status set to false 
[INFO ] 2024-07-05 12:08:12.834 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-7abe052e-6388-4e4b-af23-f72eb2e79ff7 
[INFO ] 2024-07-05 12:08:12.834 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-7abe052e-6388-4e4b-af23-f72eb2e79ff7 
[INFO ] 2024-07-05 12:08:12.834 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7abe052e-6388-4e4b-af23-f72eb2e79ff7] schema data cleaned 
[INFO ] 2024-07-05 12:08:12.836 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7abe052e-6388-4e4b-af23-f72eb2e79ff7] monitor closed 
[INFO ] 2024-07-05 12:08:12.836 - [Heartbeat-TAPDATA2Oracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7abe052e-6388-4e4b-af23-f72eb2e79ff7] close complete, cost 38 ms 
[INFO ] 2024-07-05 12:08:16.504 - [Heartbeat-TAPDATA2Oracle] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:08:16.504 - [Heartbeat-TAPDATA2Oracle] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2645ae2e 
[INFO ] 2024-07-05 12:08:16.504 - [Heartbeat-TAPDATA2Oracle] - Stop task milestones: 6687708bed8812650a8a33f0(Heartbeat-TAPDATA2Oracle)  
[INFO ] 2024-07-05 12:08:16.625 - [Heartbeat-TAPDATA2Oracle] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:08:16.625 - [Heartbeat-TAPDATA2Oracle] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:08:16.651 - [Heartbeat-TAPDATA2Oracle] - Remove memory task client succeed, task: Heartbeat-TAPDATA2Oracle[6687708bed8812650a8a33f0] 
[INFO ] 2024-07-05 12:08:16.651 - [Heartbeat-TAPDATA2Oracle] - Destroy memory task client cache succeed, task: Heartbeat-TAPDATA2Oracle[6687708bed8812650a8a33f0] 
