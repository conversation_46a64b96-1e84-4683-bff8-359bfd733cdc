[INFO ] 2024-04-01 16:20:54.887 - [任务 37] - Start task milestones: 660a6e336839b51220137cf6(任务 37) 
[INFO ] 2024-04-01 16:20:54.888 - [任务 37] - Task initialization... 
[INFO ] 2024-04-01 16:20:54.912 - [任务 37] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-01 16:20:55.115 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-01 16:20:55.208 - [任务 37][test8] - Node test8[65e3e351-982b-4afb-8e64-5ce64ef075cb] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:20:55.209 - [任务 37][CLAIM] - Node CLAIM[0a9d4b6f-297f-452d-ba47-76d592166088] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:20:55.209 - [任务 37][test8] - Node test8[65e3e351-982b-4afb-8e64-5ce64ef075cb] preload schema finished, cost 75 ms 
[INFO ] 2024-04-01 16:20:55.209 - [任务 37][CLAIM] - Node CLAIM[0a9d4b6f-297f-452d-ba47-76d592166088] preload schema finished, cost 75 ms 
[INFO ] 2024-04-01 16:20:56.230 - [任务 37][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-01 16:20:56.247 - [任务 37][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-01 16:20:56.247 - [任务 37][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-01 16:20:56.247 - [任务 37][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-01 16:20:56.269 - [任务 37][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145948132,"gtidSet":""} 
[INFO ] 2024-04-01 16:20:56.335 - [任务 37][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-04-01 16:20:56.335 - [任务 37][CLAIM] - Initial sync started 
[INFO ] 2024-04-01 16:20:56.339 - [任务 37][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-01 16:20:56.344 - [任务 37][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-01 16:20:56.459 - [任务 37][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-01 16:20:56.460 - [任务 37][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 16:20:56.460 - [任务 37][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-01 16:20:56.460 - [任务 37][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 16:20:56.467 - [任务 37][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145948132,"gtidSet":""} 
[INFO ] 2024-04-01 16:20:56.574 - [任务 37][CLAIM] - Starting mysql cdc, server name: 4ce24953-c511-4163-8cf0-ec043beb0fd6 
[INFO ] 2024-04-01 16:20:56.574 - [任务 37][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2140164627
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4ce24953-c511-4163-8cf0-ec043beb0fd6
  database.port: 3306
  threadName: Debezium-Mysql-Connector-4ce24953-c511-4163-8cf0-ec043beb0fd6
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4ce24953-c511-4163-8cf0-ec043beb0fd6
  pdk.offset.string: {"name":"4ce24953-c511-4163-8cf0-ec043beb0fd6","offset":{"{\"server\":\"4ce24953-c511-4163-8cf0-ec043beb0fd6\"}":"{\"file\":\"binlog.000020\",\"pos\":145948132,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-01 16:20:56.765 - [任务 37][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-01 16:21:15.022 - [任务 37] - Stop task milestones: 660a6e336839b51220137cf6(任务 37)  
[INFO ] 2024-04-01 16:21:15.110 - [任务 37][CLAIM] - Node CLAIM[0a9d4b6f-297f-452d-ba47-76d592166088] running status set to false 
[INFO ] 2024-04-01 16:21:15.115 - [任务 37][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-01 16:21:15.126 - [任务 37][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-01 16:21:15.126 - [任务 37][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0a9d4b6f-297f-452d-ba47-76d592166088 
[INFO ] 2024-04-01 16:21:15.126 - [任务 37][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-0a9d4b6f-297f-452d-ba47-76d592166088 
[INFO ] 2024-04-01 16:21:15.127 - [任务 37][CLAIM] - Node CLAIM[0a9d4b6f-297f-452d-ba47-76d592166088] schema data cleaned 
[INFO ] 2024-04-01 16:21:15.127 - [任务 37][CLAIM] - Node CLAIM[0a9d4b6f-297f-452d-ba47-76d592166088] monitor closed 
[INFO ] 2024-04-01 16:21:15.127 - [任务 37][CLAIM] - Node CLAIM[0a9d4b6f-297f-452d-ba47-76d592166088] close complete, cost 60 ms 
[INFO ] 2024-04-01 16:21:15.127 - [任务 37][test8] - Node test8[65e3e351-982b-4afb-8e64-5ce64ef075cb] running status set to false 
[INFO ] 2024-04-01 16:21:15.151 - [任务 37][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-65e3e351-982b-4afb-8e64-5ce64ef075cb 
[INFO ] 2024-04-01 16:21:15.151 - [任务 37][test8] - PDK connector node released: HazelcastTargetPdkDataNode-65e3e351-982b-4afb-8e64-5ce64ef075cb 
[INFO ] 2024-04-01 16:21:15.151 - [任务 37][test8] - Node test8[65e3e351-982b-4afb-8e64-5ce64ef075cb] schema data cleaned 
[INFO ] 2024-04-01 16:21:15.151 - [任务 37][test8] - Node test8[65e3e351-982b-4afb-8e64-5ce64ef075cb] monitor closed 
[INFO ] 2024-04-01 16:21:15.151 - [任务 37][test8] - Node test8[65e3e351-982b-4afb-8e64-5ce64ef075cb] close complete, cost 23 ms 
[INFO ] 2024-04-01 16:21:17.601 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-01 16:21:17.613 - [任务 37] - Stopped task aspect(s) 
[INFO ] 2024-04-01 16:21:17.613 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2024-04-01 16:21:17.655 - [任务 37] - Remove memory task client succeed, task: 任务 37[660a6e336839b51220137cf6] 
[INFO ] 2024-04-01 16:21:17.656 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[660a6e336839b51220137cf6] 
