[INFO ] 2024-05-14 07:09:00.213 - [任务 5] - Task initialization... 
[INFO ] 2024-05-14 07:09:00.215 - [任务 5] - Start task milestones: 66430dedd96c7030f5d24861(任务 5) 
[INFO ] 2024-05-14 07:09:00.405 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-14 07:09:00.406 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-14 07:09:00.500 - [任务 5][TestDate] - Node TestDate[fc04ab31-0c74-4949-b3a3-739eb12d4aa2] start preload schema,table counts: 1 
[INFO ] 2024-05-14 07:09:00.501 - [任务 5][OracleTest2] - Node OracleTest2[0dcef2f8-3097-48ea-aff8-33af2e312af8] start preload schema,table counts: 1 
[INFO ] 2024-05-14 07:09:00.552 - [任务 5][OracleTest2] - Node OracleTest2[0dcef2f8-3097-48ea-aff8-33af2e312af8] preload schema finished, cost 47 ms 
[INFO ] 2024-05-14 07:09:00.553 - [任务 5][TestDate] - Node TestDate[fc04ab31-0c74-4949-b3a3-739eb12d4aa2] preload schema finished, cost 48 ms 
[INFO ] 2024-05-14 07:09:01.644 - [任务 5][TestDate] - Source node "TestDate" read batch size: 100 
[INFO ] 2024-05-14 07:09:01.648 - [任务 5][TestDate] - Source node "TestDate" event queue capacity: 200 
[INFO ] 2024-05-14 07:09:01.649 - [任务 5][TestDate] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-14 07:09:01.877 - [任务 5][TestDate] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":721132455,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-14 07:09:01.879 - [任务 5][TestDate] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-14 07:09:02.023 - [任务 5][TestDate] - Initial sync started 
[INFO ] 2024-05-14 07:09:02.026 - [任务 5][TestDate] - Starting batch read, table name: TestDate, offset: null 
[INFO ] 2024-05-14 07:09:02.026 - [任务 5][TestDate] - Table TestDate is going to be initial synced 
[INFO ] 2024-05-14 07:09:02.149 - [任务 5][TestDate] - Query table 'TestDate' counts: 6 
[INFO ] 2024-05-14 07:09:02.150 - [任务 5][TestDate] - Initial sync completed 
[INFO ] 2024-05-14 07:09:02.150 - [任务 5][TestDate] - Incremental sync starting... 
[INFO ] 2024-05-14 07:09:02.151 - [任务 5][TestDate] - Initial sync completed 
[INFO ] 2024-05-14 07:09:02.354 - [任务 5][TestDate] - Starting stream read, table list: [TestDate], offset: {"sortString":null,"offsetValue":null,"lastScn":721132455,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-14 07:09:02.451 - [任务 5][TestDate] - Checking whether archived log exists... 
[INFO ] 2024-05-14 07:09:02.452 - [任务 5][TestDate] - building new log file... 
[INFO ] 2024-05-14 07:09:04.879 - [任务 5][TestDate] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 07:09:07.108 - [任务 5][OracleTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-14 07:09:58.024 - [任务 5][TestDate] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 07:09:58.026 - [任务 5][TestDate] - Connector Oracle incremental start succeed, tables: [TestDate], data change syncing 
[INFO ] 2024-05-14 08:06:44.906 - [任务 5][TestDate] - Log Miner is shutting down... 
[INFO ] 2024-05-14 08:06:44.906 - [任务 5][TestDate] - Log Miner has been closed! 
[INFO ] 2024-05-14 08:06:45.084 - [任务 5][TestDate] - Checking whether archived log exists... 
[INFO ] 2024-05-14 08:06:45.184 - [任务 5][TestDate] - building new log file... 
[INFO ] 2024-05-14 08:06:51.299 - [任务 5][TestDate] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 08:08:14.603 - [任务 5][TestDate] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 08:08:32.090 - [任务 5][TestDate] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-14 09:42:57.280 - [任务 5][TestDate] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-14 09:45:37.369 - [任务 5][TestDate] - Log Miner is shutting down... 
[INFO ] 2024-05-14 09:45:37.369 - [任务 5][TestDate] - Log Miner has been closed! 
[INFO ] 2024-05-14 09:45:47.395 - [任务 5][TestDate] - Checking whether archived log exists... 
[INFO ] 2024-05-14 09:45:47.518 - [任务 5][TestDate] - building new log file... 
[INFO ] 2024-05-14 09:45:50.605 - [任务 5][TestDate] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 09:47:15.950 - [任务 5][TestDate] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 09:47:33.421 - [任务 5][TestDate] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-14 10:40:04.851 - [任务 5] - Stop task milestones: 66430dedd96c7030f5d24861(任务 5)  
[INFO ] 2024-05-14 10:40:04.867 - [任务 5][TestDate] - Node TestDate[fc04ab31-0c74-4949-b3a3-739eb12d4aa2] running status set to false 
[INFO ] 2024-05-14 10:40:04.867 - [任务 5][TestDate] - Log Miner is shutting down... 
[INFO ] 2024-05-14 10:40:04.876 - [任务 5][TestDate] - Log Miner has been closed! 
[INFO ] 2024-05-14 10:40:04.881 - [任务 5][TestDate] - Auto redo oracle log miner result set closed 
[INFO ] 2024-05-14 10:40:04.907 - [任务 5][TestDate] - PDK connector node stopped: HazelcastSourcePdkDataNode-fc04ab31-0c74-4949-b3a3-739eb12d4aa2 
[INFO ] 2024-05-14 10:40:04.908 - [任务 5][TestDate] - PDK connector node released: HazelcastSourcePdkDataNode-fc04ab31-0c74-4949-b3a3-739eb12d4aa2 
[INFO ] 2024-05-14 10:40:04.908 - [任务 5][TestDate] - Node TestDate[fc04ab31-0c74-4949-b3a3-739eb12d4aa2] schema data cleaned 
[INFO ] 2024-05-14 10:40:04.908 - [任务 5][TestDate] - Node TestDate[fc04ab31-0c74-4949-b3a3-739eb12d4aa2] monitor closed 
[INFO ] 2024-05-14 10:40:04.909 - [任务 5][TestDate] - Node TestDate[fc04ab31-0c74-4949-b3a3-739eb12d4aa2] close complete, cost 48 ms 
[INFO ] 2024-05-14 10:40:04.909 - [任务 5][OracleTest2] - Node OracleTest2[0dcef2f8-3097-48ea-aff8-33af2e312af8] running status set to false 
[INFO ] 2024-05-14 10:40:05.032 - [任务 5][OracleTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-0dcef2f8-3097-48ea-aff8-33af2e312af8 
[INFO ] 2024-05-14 10:40:05.032 - [任务 5][OracleTest2] - PDK connector node released: HazelcastTargetPdkDataNode-0dcef2f8-3097-48ea-aff8-33af2e312af8 
[INFO ] 2024-05-14 10:40:05.033 - [任务 5][OracleTest2] - Node OracleTest2[0dcef2f8-3097-48ea-aff8-33af2e312af8] schema data cleaned 
[INFO ] 2024-05-14 10:40:05.033 - [任务 5][OracleTest2] - Node OracleTest2[0dcef2f8-3097-48ea-aff8-33af2e312af8] monitor closed 
[INFO ] 2024-05-14 10:40:05.033 - [任务 5][OracleTest2] - Node OracleTest2[0dcef2f8-3097-48ea-aff8-33af2e312af8] close complete, cost 124 ms 
[INFO ] 2024-05-14 10:40:05.419 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-14 10:40:05.419 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-14 10:40:05.419 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-14 10:40:05.464 - [任务 5] - Remove memory task client succeed, task: 任务 5[66430dedd96c7030f5d24861] 
[INFO ] 2024-05-14 10:40:05.464 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66430dedd96c7030f5d24861] 
