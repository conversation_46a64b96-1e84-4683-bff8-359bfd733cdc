[INFO ] 2024-09-27 15:58:40.003 - [任务 3] - Start task milestones: 66f6659ce239ae1b0e7cb2b1(任务 3) 
[INFO ] 2024-09-27 15:58:40.026 - [任务 3] - Task initialization... 
[INFO ] 2024-09-27 15:58:40.592 - [任务 3] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-27 15:58:40.658 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 15:58:40.746 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] start preload schema,table counts: 1 
[INFO ] 2024-09-27 15:58:40.746 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] start preload schema,table counts: 1 
[INFO ] 2024-09-27 15:58:40.746 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 15:58:40.746 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 15:58:53.677 - [任务 3][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 15:58:53.677 - [任务 3][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 15:58:53.909 - [任务 3][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727423932996} and {hostPort=localhost:43306, time=1727395132993} 
[INFO ] 2024-09-27 15:58:53.939 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 15:58:53.940 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 15:58:53.940 - [任务 3][LocalhostMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 15:58:53.958 - [任务 3][LocalhostMaster] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 15:58:53.997 - [任务 3][LocalhostMaster] - Initial sync started 
[INFO ] 2024-09-27 15:58:53.998 - [任务 3][LocalhostMaster] - Starting batch read, table name: t3 
[INFO ] 2024-09-27 15:58:54.017 - [任务 3][LocalhostMaster] - Table t3 is going to be initial synced 
[INFO ] 2024-09-27 15:58:54.291 - [任务 3][LocalhostMaster] - Query table 't3' counts: 2178000 
[INFO ] 2024-09-27 16:11:08.988 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] running status set to false 
[WARN ] 2024-09-27 16:11:09.000 - [任务 3][LocalhostMaster] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 16:11:09.001 - [任务 3][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 16:11:09.001 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] schema data cleaned 
[INFO ] 2024-09-27 16:11:09.001 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] monitor closed 
[INFO ] 2024-09-27 16:11:09.014 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] close complete, cost 34 ms 
[INFO ] 2024-09-27 16:11:09.020 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] running status set to false 
[WARN ] 2024-09-27 16:11:09.178 - [任务 3][SourceMongo] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-03059925-e1f9-4f18-9697-7c82389e2558 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-03059925-e1f9-4f18-9697-7c82389e2558 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] schema data cleaned 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] monitor closed 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] close complete, cost 163 ms 
[INFO ] 2024-09-27 16:23:22.453 - [任务 3] - Task initialization... 
[INFO ] 2024-09-27 16:23:22.601 - [任务 3] - Start task milestones: 66f6659ce239ae1b0e7cb2b1(任务 3) 
[INFO ] 2024-09-27 16:23:24.071 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:23:24.228 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:23:25.016 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.018 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:23:25.019 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.020 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:23:26.608 - [任务 3][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:23:26.617 - [任务 3][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 16:23:27.308 - [任务 3][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727425406982} and {hostPort=localhost:43306, time=1727396606975} 
[INFO ] 2024-09-27 16:23:27.342 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 16:23:27.347 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 16:23:27.348 - [任务 3][LocalhostMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:23:27.378 - [任务 3][LocalhostMaster] - batch offset found: {"t3":{"batch_read_connector_status":"RUNNING"}},stream offset found: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 16:23:27.442 - [任务 3][LocalhostMaster] - Initial sync started 
[INFO ] 2024-09-27 16:23:27.443 - [任务 3][LocalhostMaster] - Starting batch read, table name: t3 
[INFO ] 2024-09-27 16:23:27.465 - [任务 3][LocalhostMaster] - Table t3 is going to be initial synced 
[INFO ] 2024-09-27 16:23:28.105 - [任务 3][LocalhostMaster] - Query table 't3' counts: 2178000 
[INFO ] 2024-09-27 16:25:22.281 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] running status set to false 
[INFO ] 2024-09-27 16:25:22.417 - [任务 3][LocalhostMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 16:25:22.417 - [任务 3][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 16:25:22.418 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] schema data cleaned 
[INFO ] 2024-09-27 16:25:22.419 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] monitor closed 
[INFO ] 2024-09-27 16:25:22.420 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] close complete, cost 142 ms 
[INFO ] 2024-09-27 16:25:22.424 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] running status set to false 
[INFO ] 2024-09-27 16:25:22.424 - [任务 3][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 16:25:22.497 - [任务 3][LocalhostMaster] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t3`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-03059925-e1f9-4f18-9697-7c82389e2558 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-03059925-e1f9-4f18-9697-7c82389e2558 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] schema data cleaned 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] monitor closed 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] close complete, cost 32 ms 
[ERROR] 2024-09-27 16:25:22.507 - [任务 3][LocalhostMaster] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t3`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t3`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Socket is closed.
	com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t3`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t3`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t3`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t3`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 25 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 28 more

[INFO ] 2024-09-27 16:25:23.723 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 16:25:23.724 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@50672706 
[INFO ] 2024-09-27 16:25:23.843 - [任务 3] - Stop task milestones: 66f6659ce239ae1b0e7cb2b1(任务 3)  
[INFO ] 2024-09-27 16:25:23.854 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-09-27 16:25:23.854 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 16:25:23.868 - [任务 3] - Remove memory task client succeed, task: 任务 3[66f6659ce239ae1b0e7cb2b1] 
[INFO ] 2024-09-27 16:25:23.871 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66f6659ce239ae1b0e7cb2b1] 
[INFO ] 2024-09-27 16:26:18.645 - [任务 3] - Task initialization... 
[INFO ] 2024-09-27 16:26:18.727 - [任务 3] - Start task milestones: 66f6659ce239ae1b0e7cb2b1(任务 3) 
[INFO ] 2024-09-27 16:26:18.997 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:26:19.027 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:26:19.153 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:26:19.153 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:26:19.153 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:26:19.154 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:26:19.959 - [任务 3][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:26:19.959 - [任务 3][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 16:26:20.147 - [任务 3][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727425579992} and {hostPort=localhost:43306, time=1727396779987} 
[INFO ] 2024-09-27 16:26:20.147 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 16:26:20.147 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 16:26:20.147 - [任务 3][LocalhostMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:26:20.147 - [任务 3][LocalhostMaster] - batch offset found: {"t3":{"batch_read_connector_status":"RUNNING"}},stream offset found: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 16:26:20.251 - [任务 3][LocalhostMaster] - Initial sync started 
[INFO ] 2024-09-27 16:26:20.251 - [任务 3][LocalhostMaster] - Starting batch read, table name: t3 
[INFO ] 2024-09-27 16:26:20.276 - [任务 3][LocalhostMaster] - Table t3 is going to be initial synced 
[INFO ] 2024-09-27 16:26:20.636 - [任务 3][LocalhostMaster] - Query table 't3' counts: 2178000 
[INFO ] 2024-09-27 16:50:47.924 - [任务 3][LocalhostMaster] - Table [t3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 16:50:47.925 - [任务 3][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 16:50:47.925 - [任务 3][LocalhostMaster] - Incremental sync starting... 
[INFO ] 2024-09-27 16:50:47.925 - [任务 3][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 16:50:47.925 - [任务 3][LocalhostMaster] - Starting stream read, table list: [t3], offset: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 16:50:47.963 - [任务 3][LocalhostMaster] - Starting mysql cdc, server name: 9475e890-bf8c-4745-b14b-4429f14097b2 
[INFO ] 2024-09-27 16:50:47.964 - [任务 3][LocalhostMaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"9475e890-bf8c-4745-b14b-4429f14097b2","offset":{"{\"server\":\"9475e890-bf8c-4745-b14b-4429f14097b2\"}":"{\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 208511424
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9475e890-bf8c-4745-b14b-4429f14097b2
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 33306
  threadName: Debezium-Mysql-Connector-9475e890-bf8c-4745-b14b-4429f14097b2
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 9475e890-bf8c-4745-b14b-4429f14097b2
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.t3
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:50:48.166 - [任务 3][LocalhostMaster] - Connector Mysql incremental start succeed, tables: [t3], data change syncing 
[INFO ] 2024-09-27 17:13:33.036 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] running status set to false 
[INFO ] 2024-09-27 17:13:33.280 - [任务 3][LocalhostMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 17:13:33.283 - [任务 3][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 17:13:33.283 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.283 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] monitor closed 
[INFO ] 2024-09-27 17:13:33.286 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] close complete, cost 268 ms 
[INFO ] 2024-09-27 17:13:33.287 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] running status set to false 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-03059925-e1f9-4f18-9697-7c82389e2558 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-03059925-e1f9-4f18-9697-7c82389e2558 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] monitor closed 
[INFO ] 2024-09-27 17:13:33.486 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] close complete, cost 60 ms 
[INFO ] 2024-09-27 17:13:36.935 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 17:13:36.935 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@c83ec6 
[INFO ] 2024-09-27 17:13:37.044 - [任务 3] - Stop task milestones: 66f6659ce239ae1b0e7cb2b1(任务 3)  
[INFO ] 2024-09-27 17:13:37.061 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-09-27 17:13:37.061 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 17:13:37.082 - [任务 3] - Remove memory task client succeed, task: 任务 3[66f6659ce239ae1b0e7cb2b1] 
[INFO ] 2024-09-27 17:13:37.083 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66f6659ce239ae1b0e7cb2b1] 
[INFO ] 2024-09-27 17:16:08.211 - [任务 3] - Task initialization... 
[INFO ] 2024-09-27 17:16:08.436 - [任务 3] - Start task milestones: 66f6659ce239ae1b0e7cb2b1(任务 3) 
[INFO ] 2024-09-27 17:16:08.833 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:16:09.138 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:16:09.370 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.371 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:16:09.374 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.374 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:16:10.091 - [任务 3][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 17:16:10.091 - [任务 3][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 17:16:12.203 - [任务 3][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727428571996} and {hostPort=localhost:43306, time=1727399771993} 
[INFO ] 2024-09-27 17:16:12.207 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 17:16:12.208 - [任务 3][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 17:16:12.214 - [任务 3][LocalhostMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 17:16:12.214 - [任务 3][LocalhostMaster] - batch offset found: {"t3":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"9475e890-bf8c-4745-b14b-4429f14097b2","offset":{"{\"server\":\"9475e890-bf8c-4745-b14b-4429f14097b2\"}":"{\"ts_sec\":1727427048,\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:12.333 - [任务 3][LocalhostMaster] - Incremental sync starting... 
[INFO ] 2024-09-27 17:16:12.333 - [任务 3][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 17:16:12.334 - [任务 3][LocalhostMaster] - Starting stream read, table list: [t3], offset: {"name":"9475e890-bf8c-4745-b14b-4429f14097b2","offset":{"{\"server\":\"9475e890-bf8c-4745-b14b-4429f14097b2\"}":"{\"ts_sec\":1727427048,\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:20.293 - [任务 3][LocalhostMaster] - Starting mysql cdc, server name: 9475e890-bf8c-4745-b14b-4429f14097b2 
[INFO ] 2024-09-27 17:16:20.437 - [任务 3][LocalhostMaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"9475e890-bf8c-4745-b14b-4429f14097b2","offset":{"{\"server\":\"9475e890-bf8c-4745-b14b-4429f14097b2\"}":"{\"file\":\"mysql-bin.000043\",\"pos\":333483890,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1636870339
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9475e890-bf8c-4745-b14b-4429f14097b2
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 33306
  threadName: Debezium-Mysql-Connector-9475e890-bf8c-4745-b14b-4429f14097b2
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 9475e890-bf8c-4745-b14b-4429f14097b2
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.t3
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 17:16:20.438 - [任务 3][LocalhostMaster] - Connector Mysql incremental start succeed, tables: [t3], data change syncing 
[INFO ] 2024-09-27 18:53:28.743 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] running status set to false 
[WARN ] 2024-09-27 18:53:28.752 - [任务 3][LocalhostMaster] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 18:53:28.752 - [任务 3][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-4a60fdef-d8f7-4e12-9ff2-f0a054a57909 
[INFO ] 2024-09-27 18:53:28.752 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] schema data cleaned 
[INFO ] 2024-09-27 18:53:28.753 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] monitor closed 
[INFO ] 2024-09-27 18:53:28.754 - [任务 3][LocalhostMaster] - Node LocalhostMaster[4a60fdef-d8f7-4e12-9ff2-f0a054a57909] close complete, cost 23 ms 
[INFO ] 2024-09-27 18:53:28.755 - [任务 3][SourceMongo] - Node SourceMongo[03059925-e1f9-4f18-9697-7c82389e2558] running status set to false 
