[INFO ] 2024-07-26 17:42:34.079 - [Heartbeat-DamengSource] - Start task milestones: 66a36f898191787d1e5af467(Heartbeat-DamengSource) 
[INFO ] 2024-07-26 17:42:34.243 - [Heartbeat-DamengSource] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 17:42:34.314 - [Heartbeat-DamengSource] - The engine receives Heartbeat-DamengSource task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 17:42:34.480 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c4f12bd4-fc23-4c5d-9169-7ce6242cf177] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:42:34.481 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c4f12bd4-fc23-4c5d-9169-7ce6242cf177] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:42:34.485 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d3378730-cc3f-43a2-b0d9-c3e1da1947a9] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:42:34.485 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d3378730-cc3f-43a2-b0d9-c3e1da1947a9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:42:35.610 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-26 17:42:35.785 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 17:42:35.785 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 17:42:35.785 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 17:42:35.786 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721986955785,"lastTimes":1721986955785,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 17:42:35.786 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 17:42:35.872 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 17:42:35.872 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 17:42:35.890 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 17:42:35.890 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 17:42:35.890 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 17:42:35.893 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721986955785,"lastTimes":1721986955785,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 17:42:35.894 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 17:42:35.943 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:00:00.825 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c4f12bd4-fc23-4c5d-9169-7ce6242cf177] running status set to false 
[INFO ] 2024-07-26 18:00:00.825 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:00:00.826 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-c4f12bd4-fc23-4c5d-9169-7ce6242cf177 
[INFO ] 2024-07-26 18:00:00.826 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-c4f12bd4-fc23-4c5d-9169-7ce6242cf177 
[INFO ] 2024-07-26 18:00:00.826 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c4f12bd4-fc23-4c5d-9169-7ce6242cf177] schema data cleaned 
[INFO ] 2024-07-26 18:00:00.826 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c4f12bd4-fc23-4c5d-9169-7ce6242cf177] monitor closed 
[INFO ] 2024-07-26 18:00:00.826 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c4f12bd4-fc23-4c5d-9169-7ce6242cf177] close complete, cost 3 ms 
[INFO ] 2024-07-26 18:00:00.826 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d3378730-cc3f-43a2-b0d9-c3e1da1947a9] running status set to false 
[INFO ] 2024-07-26 18:00:00.835 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-d3378730-cc3f-43a2-b0d9-c3e1da1947a9 
[INFO ] 2024-07-26 18:00:00.835 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-d3378730-cc3f-43a2-b0d9-c3e1da1947a9 
[INFO ] 2024-07-26 18:00:00.836 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d3378730-cc3f-43a2-b0d9-c3e1da1947a9] schema data cleaned 
[INFO ] 2024-07-26 18:00:00.836 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d3378730-cc3f-43a2-b0d9-c3e1da1947a9] monitor closed 
[INFO ] 2024-07-26 18:00:00.836 - [Heartbeat-DamengSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d3378730-cc3f-43a2-b0d9-c3e1da1947a9] close complete, cost 9 ms 
[INFO ] 2024-07-26 18:00:01.699 - [Heartbeat-DamengSource] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:00:01.699 - [Heartbeat-DamengSource] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@73de2a87 
[INFO ] 2024-07-26 18:00:01.699 - [Heartbeat-DamengSource] - Stop task milestones: 66a36f898191787d1e5af467(Heartbeat-DamengSource)  
[INFO ] 2024-07-26 18:00:01.814 - [Heartbeat-DamengSource] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:00:01.814 - [Heartbeat-DamengSource] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:00:01.833 - [Heartbeat-DamengSource] - Remove memory task client succeed, task: Heartbeat-DamengSource[66a36f898191787d1e5af467] 
[INFO ] 2024-07-26 18:00:01.836 - [Heartbeat-DamengSource] - Destroy memory task client cache succeed, task: Heartbeat-DamengSource[66a36f898191787d1e5af467] 
