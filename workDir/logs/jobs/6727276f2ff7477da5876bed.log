[INFO ] 2024-11-04 04:35:10.910 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 04:35:10.937 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 04:35:11.626 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 04:35:11.832 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 04:35:12.063 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 04:35:12.064 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 04:35:12.066 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 04:35:12.067 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 04:35:12.936 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 04:35:12.943 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 04:35:12.944 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 04:35:12.961 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":18558,"gtidSet":""} 
[INFO ] 2024-11-04 04:35:12.966 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 04:35:13.139 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 04:35:13.141 - [测试DDL][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-11-04 04:35:13.142 - [测试DDL][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-11-04 04:35:13.142 - [测试DDL][BMSQL_ITEM] - Query snapshot row size completed: BMSQL_ITEM(a4776644-0fce-49e6-bac5-27d95b47845a) 
[INFO ] 2024-11-04 04:35:13.149 - [测试DDL][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-11-04 04:35:13.910 - [测试DDL][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 04:35:13.910 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 04:35:13.911 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 04:35:13.911 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 04:35:13.969 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000037","position":18558,"gtidSet":""} 
[INFO ] 2024-11-04 04:35:13.969 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5 
[INFO ] 2024-11-04 04:35:14.171 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5","offset":{"{\"server\":\"0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5\"}":"{\"file\":\"binlog.000037\",\"pos\":18558,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 870262482
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 04:35:14.382 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 04:36:08.248 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add `I_ITEM3` varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 04:36:08.253 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5', offset={{"server":"0d6f63d8-eaa3-4c1e-aa8b-d9463a43b8e5"}={"ts_sec":1730666167,"file":"binlog.000037","pos":18813,"server_id":1}}} 
[INFO ] 2024-11-04 04:36:08.306 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4794cdae: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM3","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add `I_ITEM3` varchar(20)","referenceTime":1730666167864,"tableId":"BMSQL_ITEM","time":1730666168231,"type":209} 
[INFO ] 2024-11-04 04:36:08.308 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 04:36:08.513 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 04:36:09.290 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4794cdae: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM3","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add `I_ITEM3` varchar(20)","referenceTime":1730666167864,"tableId":"BMSQL_ITEM","time":1730666168231,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 04:38:58.622 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4794cdae: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM3","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add `I_ITEM3` varchar(20)","referenceTime":1730666167864,"tableId":"BMSQL_ITEM","time":1730666168231,"type":209}) 
[INFO ] 2024-11-04 05:10:09.476 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 05:10:09.583 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 05:10:09.592 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 05:10:09.593 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 05:10:09.593 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 05:10:09.595 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 05:10:09.596 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 05:10:09.609 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 05:10:09.609 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 149 ms 
[INFO ] 2024-11-04 05:10:09.610 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 05:10:09.623 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 05:10:09.623 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 05:10:09.624 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 05:10:09.625 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 05:10:09.833 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 20 ms 
[INFO ] 2024-11-04 05:10:10.156 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 05:10:10.156 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4ea462b8 
[INFO ] 2024-11-04 05:10:10.170 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 05:10:10.320 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 05:10:10.322 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 05:10:10.391 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 05:10:10.392 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 05:11:45.495 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 05:11:45.497 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 05:11:45.843 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 05:11:45.845 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 05:11:45.929 - [测试DDL][字段改名] - Node 字段改名[1c3c484c-4062-480e-b9ff-d1a7c2283874] start preload schema,table counts: 1 
[INFO ] 2024-11-04 05:11:45.929 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 05:11:45.931 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 05:11:45.931 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 05:11:45.938 - [测试DDL][字段改名] - Node 字段改名[1c3c484c-4062-480e-b9ff-d1a7c2283874] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 05:11:45.944 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 05:11:45.944 - [测试DDL][字段改名] - Node field_rename_processor(字段改名: 1c3c484c-4062-480e-b9ff-d1a7c2283874) enable batch process 
[INFO ] 2024-11-04 05:11:46.815 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 05:11:46.816 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 05:11:46.817 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 05:11:46.822 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":18813,"gtidSet":""} 
[INFO ] 2024-11-04 05:11:46.822 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 05:11:46.909 - [测试DDL][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-11-04 05:11:46.909 - [测试DDL][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-11-04 05:11:46.910 - [测试DDL][BMSQL_ITEM] - Query snapshot row size completed: BMSQL_ITEM(a4776644-0fce-49e6-bac5-27d95b47845a) 
[INFO ] 2024-11-04 05:11:46.910 - [测试DDL][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-11-04 05:11:47.117 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 05:11:47.564 - [测试DDL][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 05:11:47.568 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 05:11:47.569 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 05:11:47.569 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 05:11:47.573 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000037","position":18813,"gtidSet":""} 
[INFO ] 2024-11-04 05:11:47.614 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 74bac71e-9ab9-4883-8e8d-40f278d67f88 
[INFO ] 2024-11-04 05:11:47.615 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"74bac71e-9ab9-4883-8e8d-40f278d67f88","offset":{"{\"server\":\"74bac71e-9ab9-4883-8e8d-40f278d67f88\"}":"{\"file\":\"binlog.000037\",\"pos\":18813,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 196939588
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 74bac71e-9ab9-4883-8e8d-40f278d67f88
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-74bac71e-9ab9-4883-8e8d-40f278d67f88
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 74bac71e-9ab9-4883-8e8d-40f278d67f88
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 05:11:47.817 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 05:13:25.686 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM4 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 05:13:25.687 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='74bac71e-9ab9-4883-8e8d-40f278d67f88', offset={{"server":"74bac71e-9ab9-4883-8e8d-40f278d67f88"}={"ts_sec":1730668405,"file":"binlog.000037","pos":19073,"server_id":1}}} 
[INFO ] 2024-11-04 05:13:25.693 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2f16035a: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":9,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730668405485,"tableId":"BMSQL_ITEM","time":1730668405670,"type":209} 
[INFO ] 2024-11-04 05:13:25.693 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 05:13:25.894 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 05:14:52.340 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 05:14:52.427 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 05:14:52.427 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 05:14:52.434 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 05:14:52.441 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 05:14:52.441 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 05:14:52.441 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 05:14:52.441 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 05:14:52.445 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 120 ms 
[INFO ] 2024-11-04 05:14:52.446 - [测试DDL][字段改名] - Node 字段改名[1c3c484c-4062-480e-b9ff-d1a7c2283874] running status set to false 
[INFO ] 2024-11-04 05:14:52.584 - [测试DDL][字段改名] - Node 字段改名[1c3c484c-4062-480e-b9ff-d1a7c2283874] schema data cleaned 
[INFO ] 2024-11-04 05:14:52.587 - [测试DDL][字段改名] - Node 字段改名[1c3c484c-4062-480e-b9ff-d1a7c2283874] monitor closed 
[INFO ] 2024-11-04 05:14:52.591 - [测试DDL][字段改名] - Node 字段改名[1c3c484c-4062-480e-b9ff-d1a7c2283874] close complete, cost 145 ms 
[INFO ] 2024-11-04 05:14:52.592 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 05:14:52.619 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 05:14:52.619 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 05:14:52.620 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 05:14:52.620 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 05:14:52.826 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 30 ms 
[INFO ] 2024-11-04 05:14:55.643 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 05:14:55.653 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@31720ffa 
[INFO ] 2024-11-04 05:14:55.653 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 05:14:55.780 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 05:14:55.780 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 05:14:55.807 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 05:14:56.013 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 05:16:51.936 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 05:16:52.052 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 05:16:52.053 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 05:16:52.160 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 05:16:52.203 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 05:16:52.203 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 05:16:52.203 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 05:16:52.409 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 05:16:53.018 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 05:16:53.165 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 05:16:53.165 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 05:16:53.170 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 05:16:53.171 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":19073,"gtidSet":""} 
[INFO ] 2024-11-04 05:16:53.245 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 05:16:53.246 - [测试DDL][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-11-04 05:16:53.246 - [测试DDL][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-11-04 05:16:53.246 - [测试DDL][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-11-04 05:16:53.246 - [测试DDL][BMSQL_ITEM] - Query snapshot row size completed: BMSQL_ITEM(a4776644-0fce-49e6-bac5-27d95b47845a) 
[INFO ] 2024-11-04 05:16:53.640 - [测试DDL][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 05:16:53.640 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 05:16:53.640 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 05:16:53.640 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 05:16:53.671 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000037","position":19073,"gtidSet":""} 
[INFO ] 2024-11-04 05:16:53.676 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 48eb871d-d9d3-4e20-873b-8060b177cb54 
[INFO ] 2024-11-04 05:16:53.728 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"48eb871d-d9d3-4e20-873b-8060b177cb54","offset":{"{\"server\":\"48eb871d-d9d3-4e20-873b-8060b177cb54\"}":"{\"file\":\"binlog.000037\",\"pos\":19073,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 787929768
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 48eb871d-d9d3-4e20-873b-8060b177cb54
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-48eb871d-d9d3-4e20-873b-8060b177cb54
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 48eb871d-d9d3-4e20-873b-8060b177cb54
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 05:16:53.728 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 05:49:33.740 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 05:49:33.769 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 05:49:33.769 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 05:49:33.778 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 05:49:33.785 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 05:49:33.785 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 05:49:33.786 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 05:49:33.790 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 05:49:33.790 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 85 ms 
[INFO ] 2024-11-04 05:49:33.791 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 05:49:33.809 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 05:49:33.810 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 05:49:33.810 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 05:49:33.810 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 05:49:33.811 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 21 ms 
[INFO ] 2024-11-04 05:49:38.103 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 05:49:38.105 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@bb5e4a5 
[INFO ] 2024-11-04 05:49:38.106 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 05:49:38.228 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 05:49:38.228 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 05:49:38.259 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 05:49:38.464 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:45:20.194 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 07:45:20.194 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 07:45:20.534 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 07:45:20.534 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 07:45:20.574 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:45:20.574 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:45:20.575 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 07:45:20.575 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:45:21.453 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 07:45:21.455 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Table "test.BMSQL_ITEM_TEST_DDL" exists, skip auto create table 
[INFO ] 2024-11-04 07:45:21.455 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The table BMSQL_ITEM_TEST_DDL has already exist. 
[INFO ] 2024-11-04 07:45:21.588 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 07:45:21.589 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 07:45:21.590 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 07:45:21.596 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":19073,"gtidSet":""} 
[INFO ] 2024-11-04 07:45:21.663 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 07:45:21.664 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 07:45:21.664 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 07:45:21.664 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000037","position":19073,"gtidSet":""} 
[INFO ] 2024-11-04 07:45:21.707 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: d625bd9d-b3b7-4a61-baf8-bc3b13748e1e 
[INFO ] 2024-11-04 07:45:21.708 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"d625bd9d-b3b7-4a61-baf8-bc3b13748e1e","offset":{"{\"server\":\"d625bd9d-b3b7-4a61-baf8-bc3b13748e1e\"}":"{\"file\":\"binlog.000037\",\"pos\":19073,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 620009342
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d625bd9d-b3b7-4a61-baf8-bc3b13748e1e
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d625bd9d-b3b7-4a61-baf8-bc3b13748e1e
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: d625bd9d-b3b7-4a61-baf8-bc3b13748e1e
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 07:45:21.910 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 07:45:45.760 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM5 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:45:45.762 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='d625bd9d-b3b7-4a61-baf8-bc3b13748e1e', offset={{"server":"d625bd9d-b3b7-4a61-baf8-bc3b13748e1e"}={"ts_sec":1730677545,"file":"binlog.000037","pos":19333,"server_id":1}}} 
[INFO ] 2024-11-04 07:45:45.764 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5e65c1f: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":10,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677545397,"tableId":"BMSQL_ITEM","time":1730677545747,"type":209} 
[INFO ] 2024-11-04 07:45:45.883 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:45:45.883 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:45:46.126 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5e65c1f: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":10,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677545397,"tableId":"BMSQL_ITEM","time":1730677545747,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:45:46.126 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5e65c1f: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":10,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677545397,"tableId":"BMSQL_ITEM","time":1730677545747,"type":209}) 
[INFO ] 2024-11-04 07:45:57.273 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 07:45:57.294 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 07:45:57.296 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 07:45:57.305 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 07:45:57.305 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:45:57.306 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:45:57.306 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 07:45:57.308 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 07:45:57.308 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 66 ms 
[INFO ] 2024-11-04 07:45:57.328 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 07:45:57.328 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:45:57.328 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:45:57.328 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 07:45:57.333 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 07:45:57.333 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 23 ms 
[INFO ] 2024-11-04 07:45:59.738 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 07:45:59.741 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5bf60611 
[INFO ] 2024-11-04 07:45:59.741 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 07:45:59.877 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 07:45:59.877 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 07:45:59.910 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:45:59.910 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:46:42.418 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 07:46:42.647 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 07:46:42.788 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 07:46:42.789 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 07:46:42.831 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:46:42.832 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:46:42.832 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:46:43.037 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:46:43.640 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 07:46:43.640 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 07:46:43.641 - [测试DDL][BMSQL_ITEM] - Sync progress not exists, will run task as first time 
[INFO ] 2024-11-04 07:46:43.642 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 07:46:43.644 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: 1730677200000 
[INFO ] 2024-11-04 07:46:43.644 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 07:46:43.696 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 07:46:43.702 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 07:46:43.703 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: 1730677200000 
[INFO ] 2024-11-04 07:46:43.847 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1 
[INFO ] 2024-11-04 07:46:43.852 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 07:46:43.857 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Sync progress not exists, will run task as first time 
[INFO ] 2024-11-04 07:46:43.857 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1","offset":{"{\"server\":\"009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1\"}":"{\"file\":\"binlog.000037\",\"pos\":18813,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1345617551
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 07:46:43.883 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Table "test.BMSQL_ITEM_TEST_DDL" exists, skip auto create table 
[INFO ] 2024-11-04 07:46:43.884 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The table BMSQL_ITEM_TEST_DDL has already exist. 
[INFO ] 2024-11-04 07:46:44.094 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 07:46:44.433 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM4 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:46:44.437 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1', offset={{"server":"009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1"}={"ts_sec":1730677603,"file":"binlog.000037","pos":19073,"server_id":1}}} 
[INFO ] 2024-11-04 07:46:44.438 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM5 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:46:44.440 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1', offset={{"server":"009c1dfc-ee7b-4fa1-87fd-e8a65e2798c1"}={"ts_sec":1730677603,"file":"binlog.000037","pos":19333,"server_id":1}}} 
[INFO ] 2024-11-04 07:46:44.441 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@725b721: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677603976,"tableId":"BMSQL_ITEM","time":1730677604423,"type":209} 
[INFO ] 2024-11-04 07:46:44.501 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:46:44.501 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:46:44.511 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7da42ab1: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677603980,"tableId":"BMSQL_ITEM","time":1730677604437,"type":209} 
[INFO ] 2024-11-04 07:46:44.511 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:46:44.711 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:46:44.871 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@725b721: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677603976,"tableId":"BMSQL_ITEM","time":1730677604423,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:46:45.398 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@725b721: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677603976,"tableId":"BMSQL_ITEM","time":1730677604423,"type":209}) 
[INFO ] 2024-11-04 07:46:45.399 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7da42ab1: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677603980,"tableId":"BMSQL_ITEM","time":1730677604437,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:46:45.952 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4' 
[ERROR] 2024-11-04 07:46:45.954 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4' <-- Error Message -->
Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeNewFieldFunction$33(HazelcastTargetPdkDataNode.java:597)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:592)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:552)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$29(HazelcastTargetPdkDataNode.java:538)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:525)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:787)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:715)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:674)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:627)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:596)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:648)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:595)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:442)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$32(HazelcastTargetPdkDataNode.java:598)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 29 more

[INFO ] 2024-11-04 07:46:46.159 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Job suspend in error handle 
[INFO ] 2024-11-04 07:46:46.233 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 07:46:46.381 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 07:46:46.394 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 07:46:46.394 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 07:46:46.399 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:46:46.400 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:46:46.401 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 07:46:46.401 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 07:46:46.408 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7da42ab1: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677603980,"tableId":"BMSQL_ITEM","time":1730677604437,"type":209}) 
[INFO ] 2024-11-04 07:46:46.408 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 190 ms 
[INFO ] 2024-11-04 07:46:46.409 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 07:46:46.453 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:46:46.462 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:46:46.462 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 07:46:46.462 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 07:46:46.463 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 43 ms 
[INFO ] 2024-11-04 07:46:50.002 - [测试DDL] - Task [测试DDL] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 07:46:50.017 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 07:46:50.019 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@14afd62c 
[INFO ] 2024-11-04 07:46:50.019 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 07:46:50.162 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 07:46:50.163 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 07:46:50.198 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:46:50.198 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:48:13.907 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 07:48:13.910 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 07:48:14.057 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 07:48:14.058 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 07:48:14.098 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:48:14.099 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:48:14.099 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:48:14.099 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:48:14.993 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 07:48:14.993 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Table "test.BMSQL_ITEM_TEST_DDL" exists, skip auto create table 
[INFO ] 2024-11-04 07:48:14.993 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The table BMSQL_ITEM_TEST_DDL has already exist. 
[INFO ] 2024-11-04 07:48:15.092 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 07:48:15.093 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 07:48:15.093 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 07:48:15.093 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: 1730677200000 
[INFO ] 2024-11-04 07:48:15.093 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 07:48:15.141 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 07:48:15.141 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 07:48:15.145 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: 1730677200000 
[INFO ] 2024-11-04 07:48:15.244 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 7b9332cd-31e1-4e96-b3e2-0a44c7785a5a 
[INFO ] 2024-11-04 07:48:15.244 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"7b9332cd-31e1-4e96-b3e2-0a44c7785a5a","offset":{"{\"server\":\"7b9332cd-31e1-4e96-b3e2-0a44c7785a5a\"}":"{\"file\":\"binlog.000037\",\"pos\":18813,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1147002429
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 7b9332cd-31e1-4e96-b3e2-0a44c7785a5a
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-7b9332cd-31e1-4e96-b3e2-0a44c7785a5a
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 7b9332cd-31e1-4e96-b3e2-0a44c7785a5a
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 07:48:15.449 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 07:48:15.803 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM4 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:48:15.804 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='7b9332cd-31e1-4e96-b3e2-0a44c7785a5a', offset={{"server":"7b9332cd-31e1-4e96-b3e2-0a44c7785a5a"}={"ts_sec":1730677695,"file":"binlog.000037","pos":19073,"server_id":1}}} 
[INFO ] 2024-11-04 07:48:15.804 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM5 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:48:15.806 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='7b9332cd-31e1-4e96-b3e2-0a44c7785a5a', offset={{"server":"7b9332cd-31e1-4e96-b3e2-0a44c7785a5a"}={"ts_sec":1730677695,"file":"binlog.000037","pos":19333,"server_id":1}}} 
[INFO ] 2024-11-04 07:48:15.831 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@47b21125: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677695357,"tableId":"BMSQL_ITEM","time":1730677695797,"type":209} 
[INFO ] 2024-11-04 07:48:15.831 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:48:15.872 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:48:15.873 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5519f251: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677695362,"tableId":"BMSQL_ITEM","time":1730677695804,"type":209} 
[INFO ] 2024-11-04 07:48:15.898 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:48:15.898 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:48:19.888 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@47b21125: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677695357,"tableId":"BMSQL_ITEM","time":1730677695797,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:48:19.918 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@47b21125: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677695357,"tableId":"BMSQL_ITEM","time":1730677695797,"type":209}) 
[INFO ] 2024-11-04 07:48:19.918 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5519f251: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677695362,"tableId":"BMSQL_ITEM","time":1730677695804,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:48:37.974 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4' 
[ERROR] 2024-11-04 07:48:37.976 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4' <-- Error Message -->
Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeNewFieldFunction$33(HazelcastTargetPdkDataNode.java:597)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:592)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:552)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$29(HazelcastTargetPdkDataNode.java:538)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:525)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:787)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:715)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:674)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:627)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:596)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:648)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:595)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:442)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$32(HazelcastTargetPdkDataNode.java:598)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 29 more

[INFO ] 2024-11-04 07:48:38.194 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Job suspend in error handle 
[INFO ] 2024-11-04 07:48:38.490 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 07:48:38.492 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 07:48:38.492 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 07:48:38.504 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 07:48:38.505 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:48:38.505 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:48:38.505 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 07:48:38.512 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 07:48:38.512 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 106 ms 
[INFO ] 2024-11-04 07:48:38.512 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 07:48:38.622 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:48:38.626 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:48:38.626 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 07:48:38.626 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 07:48:38.627 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 113 ms 
[INFO ] 2024-11-04 07:48:42.958 - [测试DDL] - Task [测试DDL] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 07:48:42.958 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 07:48:42.965 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@66e716e2 
[INFO ] 2024-11-04 07:48:42.969 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 07:48:43.134 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 07:48:43.134 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 07:48:43.149 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:48:43.151 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:48:49.948 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 07:48:49.948 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 07:48:50.059 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 07:48:50.140 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 07:48:50.140 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:48:50.140 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:48:50.140 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:48:50.140 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:48:50.963 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 07:48:50.996 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Table "test.BMSQL_ITEM_TEST_DDL" exists, skip auto create table 
[INFO ] 2024-11-04 07:48:50.997 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The table BMSQL_ITEM_TEST_DDL has already exist. 
[INFO ] 2024-11-04 07:48:51.132 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 07:48:51.133 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 07:48:51.133 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 07:48:51.133 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: 1730677200000 
[INFO ] 2024-11-04 07:48:51.133 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 07:48:51.181 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 07:48:51.181 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 07:48:51.258 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: 1730677200000 
[INFO ] 2024-11-04 07:48:51.260 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: e131f194-1fd0-4578-ac92-ad12676bfee3 
[INFO ] 2024-11-04 07:48:51.296 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"e131f194-1fd0-4578-ac92-ad12676bfee3","offset":{"{\"server\":\"e131f194-1fd0-4578-ac92-ad12676bfee3\"}":"{\"file\":\"binlog.000037\",\"pos\":18813,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1384996822
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e131f194-1fd0-4578-ac92-ad12676bfee3
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e131f194-1fd0-4578-ac92-ad12676bfee3
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: e131f194-1fd0-4578-ac92-ad12676bfee3
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 07:48:51.296 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 07:48:51.817 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM4 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:48:51.818 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='e131f194-1fd0-4578-ac92-ad12676bfee3', offset={{"server":"e131f194-1fd0-4578-ac92-ad12676bfee3"}={"ts_sec":1730677731,"file":"binlog.000037","pos":19073,"server_id":1}}} 
[INFO ] 2024-11-04 07:48:51.820 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM5 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:48:51.820 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='e131f194-1fd0-4578-ac92-ad12676bfee3', offset={{"server":"e131f194-1fd0-4578-ac92-ad12676bfee3"}={"ts_sec":1730677731,"file":"binlog.000037","pos":19333,"server_id":1}}} 
[INFO ] 2024-11-04 07:48:51.844 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@26a4b25e: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677731355,"tableId":"BMSQL_ITEM","time":1730677731806,"type":209} 
[INFO ] 2024-11-04 07:48:51.844 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:48:51.912 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:48:51.912 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@3f4f4df0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677731367,"tableId":"BMSQL_ITEM","time":1730677731818,"type":209} 
[INFO ] 2024-11-04 07:48:51.921 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:48:52.125 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:48:52.482 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@26a4b25e: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677731355,"tableId":"BMSQL_ITEM","time":1730677731806,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:48:53.007 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@26a4b25e: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677731355,"tableId":"BMSQL_ITEM","time":1730677731806,"type":209}) 
[INFO ] 2024-11-04 07:48:53.212 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@3f4f4df0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677731367,"tableId":"BMSQL_ITEM","time":1730677731818,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:48:59.987 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4' 
[ERROR] 2024-11-04 07:49:00.046 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4' <-- Error Message -->
Unknown PDK exception occur, when operate table: BMSQL_ITEM_TEST_DDL, java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeNewFieldFunction$33(HazelcastTargetPdkDataNode.java:597)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:592)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:552)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$29(HazelcastTargetPdkDataNode.java:538)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:525)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:787)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:715)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:674)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:627)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:596)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:648)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:595)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:442)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$32(HazelcastTargetPdkDataNode.java:598)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 29 more

[INFO ] 2024-11-04 07:49:00.047 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Job suspend in error handle 
[INFO ] 2024-11-04 07:49:00.450 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 07:49:00.557 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 07:49:00.557 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 07:49:00.564 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 07:49:00.564 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:49:00.564 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:49:00.564 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 07:49:00.569 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 07:49:00.569 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 126 ms 
[INFO ] 2024-11-04 07:49:00.569 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 07:49:00.594 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:49:00.595 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:49:00.595 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 07:49:00.595 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 07:49:00.596 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 27 ms 
[INFO ] 2024-11-04 07:49:50.002 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 07:49:50.003 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 07:49:50.718 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 07:49:50.925 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 07:49:51.255 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:49:51.257 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 07:49:51.258 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 07:49:51.258 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 07:49:52.387 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 07:49:52.396 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 07:49:52.398 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 07:49:52.404 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 07:49:52.410 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: 1730677200000 
[INFO ] 2024-11-04 07:49:52.411 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 07:49:52.475 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Table "test.BMSQL_ITEM_TEST_DDL" exists, skip auto create table 
[INFO ] 2024-11-04 07:49:52.476 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The table BMSQL_ITEM_TEST_DDL has already exist. 
[INFO ] 2024-11-04 07:49:52.487 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 07:49:52.488 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 07:49:52.492 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: 1730677200000 
[INFO ] 2024-11-04 07:49:52.682 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 31dc817f-7fd4-4df7-ac4b-dbe95c330aaf 
[INFO ] 2024-11-04 07:49:52.684 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"31dc817f-7fd4-4df7-ac4b-dbe95c330aaf","offset":{"{\"server\":\"31dc817f-7fd4-4df7-ac4b-dbe95c330aaf\"}":"{\"file\":\"binlog.000037\",\"pos\":18813,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 249396570
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 31dc817f-7fd4-4df7-ac4b-dbe95c330aaf
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-31dc817f-7fd4-4df7-ac4b-dbe95c330aaf
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 31dc817f-7fd4-4df7-ac4b-dbe95c330aaf
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 07:49:53.097 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 07:49:53.946 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM4 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:49:53.947 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='31dc817f-7fd4-4df7-ac4b-dbe95c330aaf', offset={{"server":"31dc817f-7fd4-4df7-ac4b-dbe95c330aaf"}={"ts_sec":1730677793,"file":"binlog.000037","pos":19073,"server_id":1}}} 
[INFO ] 2024-11-04 07:49:53.948 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM5 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 07:49:53.949 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='31dc817f-7fd4-4df7-ac4b-dbe95c330aaf', offset={{"server":"31dc817f-7fd4-4df7-ac4b-dbe95c330aaf"}={"ts_sec":1730677793,"file":"binlog.000037","pos":19333,"server_id":1}}} 
[INFO ] 2024-11-04 07:49:53.995 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@447351c0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677793497,"tableId":"BMSQL_ITEM","time":1730677793937,"type":209} 
[INFO ] 2024-11-04 07:49:53.998 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:49:54.080 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:49:54.081 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@102d2030: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677793531,"tableId":"BMSQL_ITEM","time":1730677793947,"type":209} 
[INFO ] 2024-11-04 07:49:54.158 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 07:49:54.158 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 07:49:54.910 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@447351c0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677793497,"tableId":"BMSQL_ITEM","time":1730677793937,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:49:54.962 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@447351c0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677793497,"tableId":"BMSQL_ITEM","time":1730677793937,"type":209}) 
[INFO ] 2024-11-04 07:49:54.963 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@102d2030: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730677793531,"tableId":"BMSQL_ITEM","time":1730677793947,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 07:50:04.330 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: NEW_FIELDio.tapdata.entity.event.ddl.table.TapNewFieldEvent@447351c0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677793497,"tableId":"BMSQL_ITEM_TEST_DDL","time":1730677793937,"type":209}
 
[ERROR] 2024-11-04 07:50:04.332 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Execute PDK method: NEW_FIELDio.tapdata.entity.event.ddl.table.TapNewFieldEvent@447351c0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677793497,"tableId":"BMSQL_ITEM_TEST_DDL","time":1730677793937,"type":209}
 <-- Error Message -->
Execute PDK method: NEW_FIELDio.tapdata.entity.event.ddl.table.TapNewFieldEvent@447351c0: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730677793497,"tableId":"BMSQL_ITEM_TEST_DDL","time":1730677793937,"type":209}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Execute PDK method: NEW_FIELD
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:604)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:552)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$29(HazelcastTargetPdkDataNode.java:538)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:525)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:787)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:715)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:674)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:627)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:596)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:648)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:595)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:592)
	... 20 more
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeNewFieldFunction$33(HazelcastTargetPdkDataNode.java:597)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 22 more
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:442)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$32(HazelcastTargetPdkDataNode.java:598)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 29 more

[INFO ] 2024-11-04 07:50:04.538 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Job suspend in error handle 
[INFO ] 2024-11-04 07:50:04.743 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 07:50:04.774 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 07:50:04.787 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 07:50:04.799 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 07:50:04.805 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:50:04.806 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 07:50:04.810 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 07:50:04.810 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 07:50:04.816 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 158 ms 
[INFO ] 2024-11-04 07:50:04.816 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 07:50:04.838 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:50:04.839 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 07:50:04.839 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 07:50:04.842 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 07:50:05.044 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 27 ms 
[INFO ] 2024-11-04 07:50:09.176 - [测试DDL] - Task [测试DDL] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 07:50:09.219 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 07:50:09.227 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3643b40a 
[INFO ] 2024-11-04 07:50:09.231 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 07:50:09.351 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 07:50:09.383 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 07:50:09.383 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 07:50:09.383 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 08:49:08.983 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 08:49:08.985 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 08:49:09.254 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 08:49:09.256 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 08:49:09.309 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 08:49:09.309 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 08:49:09.310 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 08:49:09.310 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 08:49:10.160 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 08:49:10.187 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Table "test.BMSQL_ITEM_TEST_DDL" exists, skip auto create table 
[INFO ] 2024-11-04 08:49:10.192 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The table BMSQL_ITEM_TEST_DDL has already exist. 
[INFO ] 2024-11-04 08:49:10.322 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 08:49:10.322 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 08:49:10.324 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 08:49:10.325 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: 1730677200000 
[INFO ] 2024-11-04 08:49:10.327 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 08:49:10.381 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 08:49:10.381 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 08:49:10.451 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: 1730677200000 
[INFO ] 2024-11-04 08:49:10.452 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 9aea84c8-2c1a-415f-b08a-c0f1228a46a1 
[INFO ] 2024-11-04 08:49:10.491 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"9aea84c8-2c1a-415f-b08a-c0f1228a46a1","offset":{"{\"server\":\"9aea84c8-2c1a-415f-b08a-c0f1228a46a1\"}":"{\"file\":\"binlog.000037\",\"pos\":18813,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1476842685
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9aea84c8-2c1a-415f-b08a-c0f1228a46a1
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9aea84c8-2c1a-415f-b08a-c0f1228a46a1
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 9aea84c8-2c1a-415f-b08a-c0f1228a46a1
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 08:49:10.495 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 08:49:11.034 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM4 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 08:49:11.037 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='9aea84c8-2c1a-415f-b08a-c0f1228a46a1', offset={{"server":"9aea84c8-2c1a-415f-b08a-c0f1228a46a1"}={"ts_sec":1730681350,"file":"binlog.000037","pos":19073,"server_id":1}}} 
[INFO ] 2024-11-04 08:49:11.039 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM5 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 08:49:11.039 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='9aea84c8-2c1a-415f-b08a-c0f1228a46a1', offset={{"server":"9aea84c8-2c1a-415f-b08a-c0f1228a46a1"}={"ts_sec":1730681350,"file":"binlog.000037","pos":19333,"server_id":1}}} 
[INFO ] 2024-11-04 08:49:11.063 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2006fbd7: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730681350577,"tableId":"BMSQL_ITEM","time":1730681351018,"type":209} 
[INFO ] 2024-11-04 08:49:11.063 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 08:49:11.123 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 08:49:11.124 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@515fa606: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730681350586,"tableId":"BMSQL_ITEM","time":1730681351036,"type":209} 
[INFO ] 2024-11-04 08:49:11.157 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 08:49:11.157 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 08:49:11.766 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2006fbd7: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730681350577,"tableId":"BMSQL_ITEM","time":1730681351018,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 08:49:12.208 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2006fbd7: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730681350577,"tableId":"BMSQL_ITEM","time":1730681351018,"type":209}) 
[INFO ] 2024-11-04 08:49:12.210 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@515fa606: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM5","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM5 varchar(20)","referenceTime":1730681350586,"tableId":"BMSQL_ITEM","time":1730681351036,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 08:49:17.621 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: NEW_FIELDio.tapdata.entity.event.ddl.table.TapNewFieldEvent@2006fbd7: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730681350577,"tableId":"BMSQL_ITEM_TEST_DDL","time":1730681351018,"type":209}
 
[ERROR] 2024-11-04 08:49:17.648 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Execute PDK method: NEW_FIELDio.tapdata.entity.event.ddl.table.TapNewFieldEvent@2006fbd7: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730681350577,"tableId":"BMSQL_ITEM_TEST_DDL","time":1730681351018,"type":209}
 <-- Error Message -->
Execute PDK method: NEW_FIELDio.tapdata.entity.event.ddl.table.TapNewFieldEvent@2006fbd7: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM4","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM4 varchar(20)","referenceTime":1730681350577,"tableId":"BMSQL_ITEM_TEST_DDL","time":1730681351018,"type":209}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Execute PDK method: NEW_FIELD
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:604)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:552)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$29(HazelcastTargetPdkDataNode.java:538)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:525)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:787)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:715)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:674)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:627)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:596)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:648)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:595)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:592)
	... 20 more
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeNewFieldFunction$33(HazelcastTargetPdkDataNode.java:597)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 22 more
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'I_ITEM4'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:442)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$32(HazelcastTargetPdkDataNode.java:598)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 29 more

[INFO ] 2024-11-04 08:49:17.658 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Job suspend in error handle 
[INFO ] 2024-11-04 08:49:18.204 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 08:49:18.205 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 08:49:18.205 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 08:49:18.206 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 08:49:18.214 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 08:49:18.215 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 08:49:18.215 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 08:49:18.215 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 08:49:18.217 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 114 ms 
[INFO ] 2024-11-04 08:49:18.218 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 08:49:18.242 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 08:49:18.243 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 08:49:18.244 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 08:49:18.244 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 08:49:18.455 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 27 ms 
[INFO ] 2024-11-04 08:49:22.652 - [测试DDL] - Task [测试DDL] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 08:49:22.662 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 08:49:22.674 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@77e6fdd5 
[INFO ] 2024-11-04 08:49:22.675 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 08:49:22.804 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 08:49:22.805 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 08:49:22.827 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 08:49:22.831 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 08:49:40.887 - [测试DDL] - Start task milestones: 6727276f2ff7477da5876bed(测试DDL) 
[INFO ] 2024-11-04 08:49:40.888 - [测试DDL] - Task initialization... 
[INFO ] 2024-11-04 08:49:41.100 - [测试DDL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 08:49:41.172 - [测试DDL] - The engine receives 测试DDL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 08:49:41.172 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 08:49:41.182 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] start preload schema,table counts: 1 
[INFO ] 2024-11-04 08:49:41.183 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 08:49:41.184 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 08:49:42.144 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 08:49:42.304 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-11-04 08:49:42.305 - [测试DDL][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-11-04 08:49:42.305 - [测试DDL][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 08:49:42.319 - [测试DDL][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":19333,"gtidSet":""} 
[INFO ] 2024-11-04 08:49:42.320 - [测试DDL][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 08:49:42.418 - [测试DDL][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-11-04 08:49:42.420 - [测试DDL][BMSQL_ITEM] - Query snapshot row size completed: BMSQL_ITEM(a4776644-0fce-49e6-bac5-27d95b47845a) 
[INFO ] 2024-11-04 08:49:42.420 - [测试DDL][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-11-04 08:49:42.420 - [测试DDL][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-11-04 08:49:42.995 - [测试DDL][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 08:49:42.998 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 08:49:42.998 - [测试DDL][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-11-04 08:49:43.001 - [测试DDL][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-11-04 08:49:43.045 - [测试DDL][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000037","position":19333,"gtidSet":""} 
[INFO ] 2024-11-04 08:49:43.046 - [测试DDL][BMSQL_ITEM] - Starting mysql cdc, server name: 2a45a212-a2cd-413f-ab00-855a7311e5ce 
[INFO ] 2024-11-04 08:49:43.108 - [测试DDL][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"2a45a212-a2cd-413f-ab00-855a7311e5ce","offset":{"{\"server\":\"2a45a212-a2cd-413f-ab00-855a7311e5ce\"}":"{\"file\":\"binlog.000037\",\"pos\":19333,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1400329170
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2a45a212-a2cd-413f-ab00-855a7311e5ce
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-2a45a212-a2cd-413f-ab00-855a7311e5ce
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 2a45a212-a2cd-413f-ab00-855a7311e5ce
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 08:49:43.108 - [测试DDL][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-11-04 08:50:32.624 - [测试DDL][BMSQL_ITEM] - Read DDL: alter table BMSQL_ITEM add column I_ITEM6 varchar(20), about to be packaged as some event(s) 
[INFO ] 2024-11-04 08:50:32.624 - [测试DDL][BMSQL_ITEM] - DDL event  - Table: BMSQL_ITEM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='2a45a212-a2cd-413f-ab00-855a7311e5ce', offset={{"server":"2a45a212-a2cd-413f-ab00-855a7311e5ce"}={"ts_sec":1730681432,"file":"binlog.000037","pos":19593,"server_id":1}}} 
[INFO ] 2024-11-04 08:50:32.629 - [测试DDL][BMSQL_ITEM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1f773144: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM6","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM6 varchar(20)","referenceTime":1730681432215,"tableId":"BMSQL_ITEM","time":1730681432618,"type":209} 
[INFO ] 2024-11-04 08:50:32.629 - [测试DDL][BMSQL_ITEM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_ITEM_6720599e584d3301f1b298c8_6727276f2ff7477da5876bed 
[INFO ] 2024-11-04 08:50:32.830 - [测试DDL][BMSQL_ITEM] - Alter table schema transform finished 
[INFO ] 2024-11-04 08:50:33.379 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1f773144: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM6","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM6 varchar(20)","referenceTime":1730681432215,"tableId":"BMSQL_ITEM","time":1730681432618,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-11-04 08:50:33.562 - [测试DDL][BMSQL_ITEM_TEST_DDL] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1f773144: {"newFields":[{"autoInc":false,"dataType":"varchar(20)","name":"I_ITEM6","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}],"originDDL":"alter table BMSQL_ITEM add column I_ITEM6 varchar(20)","referenceTime":1730681432215,"tableId":"BMSQL_ITEM","time":1730681432618,"type":209}) 
[INFO ] 2024-11-04 08:50:51.834 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] running status set to false 
[INFO ] 2024-11-04 08:50:51.837 - [测试DDL][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 08:50:51.837 - [测试DDL][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 08:50:51.842 - [测试DDL][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-11-04 08:50:51.858 - [测试DDL][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 08:50:51.858 - [测试DDL][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-a4776644-0fce-49e6-bac5-27d95b47845a 
[INFO ] 2024-11-04 08:50:51.862 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] schema data cleaned 
[INFO ] 2024-11-04 08:50:51.863 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] monitor closed 
[INFO ] 2024-11-04 08:50:51.867 - [测试DDL][BMSQL_ITEM] - Node BMSQL_ITEM[a4776644-0fce-49e6-bac5-27d95b47845a] close complete, cost 175 ms 
[INFO ] 2024-11-04 08:50:51.867 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] running status set to false 
[INFO ] 2024-11-04 08:50:51.898 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node stopped: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 08:50:51.899 - [测试DDL][BMSQL_ITEM_TEST_DDL] - PDK connector node released: HazelcastTargetPdkDataNode-f2e228ce-f2e1-4f80-aa71-c6abfc4f7318 
[INFO ] 2024-11-04 08:50:51.899 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] schema data cleaned 
[INFO ] 2024-11-04 08:50:51.899 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] monitor closed 
[INFO ] 2024-11-04 08:50:51.906 - [测试DDL][BMSQL_ITEM_TEST_DDL] - Node BMSQL_ITEM_TEST_DDL[f2e228ce-f2e1-4f80-aa71-c6abfc4f7318] close complete, cost 35 ms 
[INFO ] 2024-11-04 08:50:52.953 - [测试DDL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 08:50:52.956 - [测试DDL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d521314 
[INFO ] 2024-11-04 08:50:52.956 - [测试DDL] - Stop task milestones: 6727276f2ff7477da5876bed(测试DDL)  
[INFO ] 2024-11-04 08:50:53.103 - [测试DDL] - Stopped task aspect(s) 
[INFO ] 2024-11-04 08:50:53.105 - [测试DDL] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 08:50:53.141 - [测试DDL] - Remove memory task client succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
[INFO ] 2024-11-04 08:50:53.142 - [测试DDL] - Destroy memory task client cache succeed, task: 测试DDL[6727276f2ff7477da5876bed] 
