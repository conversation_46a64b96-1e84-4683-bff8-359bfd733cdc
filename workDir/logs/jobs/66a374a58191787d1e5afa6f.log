[INFO ] 2024-07-26 18:05:25.080 - [任务 38] - Task initialization... 
[INFO ] 2024-07-26 18:05:25.080 - [任务 38] - Start task milestones: 66a374a58191787d1e5afa6f(任务 38) 
[INFO ] 2024-07-26 18:05:25.150 - [任务 38] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:05:25.177 - [任务 38] - The engine receives 任务 38 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:05:25.253 - [任务 38][Test] - Node Test[88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:05:25.253 - [任务 38][POLICY] - Node POLICY[be173d12-1bd8-4daf-876a-9bd9a491245a] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:05:25.253 - [任务 38][Test] - Node Test[88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:05:25.253 - [任务 38][POLICY] - Node POLICY[be173d12-1bd8-4daf-876a-9bd9a491245a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:05:26.201 - [任务 38][Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 18:05:26.305 - [任务 38][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-26 18:05:26.320 - [任务 38][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-26 18:05:26.320 - [任务 38][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 18:05:26.473 - [任务 38][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721988326,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 18:05:26.538 - [任务 38][POLICY] - Initial sync started 
[INFO ] 2024-07-26 18:05:26.538 - [任务 38][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-26 18:05:26.570 - [任务 38][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-26 18:05:26.570 - [任务 38][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-26 18:05:26.628 - [任务 38][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 18:05:26.628 - [任务 38][POLICY] - Initial sync completed 
[INFO ] 2024-07-26 18:05:26.628 - [任务 38][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-26 18:05:26.629 - [任务 38][POLICY] - Initial sync completed 
[INFO ] 2024-07-26 18:05:26.629 - [任务 38][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721988326,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 18:05:26.833 - [任务 38][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:05:50.532 - [任务 38][POLICY] - Node POLICY[be173d12-1bd8-4daf-876a-9bd9a491245a] running status set to false 
[INFO ] 2024-07-26 18:05:50.537 - [任务 38][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-be173d12-1bd8-4daf-876a-9bd9a491245a 
[INFO ] 2024-07-26 18:05:50.537 - [任务 38][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-be173d12-1bd8-4daf-876a-9bd9a491245a 
[INFO ] 2024-07-26 18:05:50.538 - [任务 38][POLICY] - Node POLICY[be173d12-1bd8-4daf-876a-9bd9a491245a] schema data cleaned 
[INFO ] 2024-07-26 18:05:50.539 - [任务 38][POLICY] - Node POLICY[be173d12-1bd8-4daf-876a-9bd9a491245a] monitor closed 
[INFO ] 2024-07-26 18:05:50.539 - [任务 38][POLICY] - Node POLICY[be173d12-1bd8-4daf-876a-9bd9a491245a] close complete, cost 7 ms 
[INFO ] 2024-07-26 18:05:50.573 - [任务 38][Test] - Node Test[88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8] running status set to false 
[INFO ] 2024-07-26 18:05:50.573 - [任务 38][Test] - PDK connector node stopped: HazelcastTargetPdkDataNode-88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8 
[INFO ] 2024-07-26 18:05:50.573 - [任务 38][Test] - PDK connector node released: HazelcastTargetPdkDataNode-88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8 
[INFO ] 2024-07-26 18:05:50.573 - [任务 38][Test] - Node Test[88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8] schema data cleaned 
[INFO ] 2024-07-26 18:05:50.573 - [任务 38][Test] - Node Test[88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8] monitor closed 
[INFO ] 2024-07-26 18:05:50.574 - [任务 38][Test] - Node Test[88fc0f0e-3ba8-49c4-85ea-c5fc29b676e8] close complete, cost 34 ms 
[INFO ] 2024-07-26 18:05:50.778 - [任务 38][POLICY] - Incremental sync completed 
[INFO ] 2024-07-26 18:05:53.494 - [任务 38] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:05:53.495 - [任务 38] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3bb5b4d8 
[INFO ] 2024-07-26 18:05:53.640 - [任务 38] - Stop task milestones: 66a374a58191787d1e5afa6f(任务 38)  
[INFO ] 2024-07-26 18:05:53.640 - [任务 38] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:05:53.640 - [任务 38] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:05:53.659 - [任务 38] - Remove memory task client succeed, task: 任务 38[66a374a58191787d1e5afa6f] 
[INFO ] 2024-07-26 18:05:53.663 - [任务 38] - Destroy memory task client cache succeed, task: 任务 38[66a374a58191787d1e5afa6f] 
