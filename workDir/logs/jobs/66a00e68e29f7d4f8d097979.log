[INFO ] 2024-07-24 04:12:33.434 - [任务 19] - Task initialization... 
[INFO ] 2024-07-24 04:12:33.507 - [任务 19] - Start task milestones: 66a00e68e29f7d4f8d097979(任务 19) 
[INFO ] 2024-07-24 04:12:33.507 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 04:12:33.570 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 04:12:33.570 - [任务 19][Inventory] - Node Inventory[088c3398-09e0-4260-97d7-adbe703f6372] start preload schema,table counts: 1 
[INFO ] 2024-07-24 04:12:33.570 - [任务 19][SqlServerTest2] - Node SqlServerTest2[76954c37-79ad-4130-8cf1-564336185b26] start preload schema,table counts: 1 
[INFO ] 2024-07-24 04:12:33.570 - [任务 19][Inventory] - Node Inventory[088c3398-09e0-4260-97d7-adbe703f6372] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 04:12:33.570 - [任务 19][SqlServerTest2] - Node SqlServerTest2[76954c37-79ad-4130-8cf1-564336185b26] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 04:12:34.273 - [任务 19][Inventory] - Source node "Inventory" read batch size: 100 
[INFO ] 2024-07-24 04:12:34.274 - [任务 19][Inventory] - Source node "Inventory" event queue capacity: 200 
[INFO ] 2024-07-24 04:12:34.274 - [任务 19][Inventory] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 04:12:34.296 - [任务 19][Inventory] - batch offset found: {},stream offset found: {"currentStartLSN":"0000002900001D300001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 04:12:34.296 - [任务 19][Inventory] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 04:12:34.385 - [任务 19][Inventory] - Initial sync started 
[INFO ] 2024-07-24 04:12:34.385 - [任务 19][Inventory] - Starting batch read, table name: Inventory 
[INFO ] 2024-07-24 04:12:34.385 - [任务 19][Inventory] - Table Inventory is going to be initial synced 
[INFO ] 2024-07-24 04:12:34.456 - [任务 19][Inventory] - Table [Inventory] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 04:12:34.481 - [任务 19][Inventory] - Query table 'Inventory' counts: 4 
[INFO ] 2024-07-24 04:12:34.482 - [任务 19][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 04:12:34.490 - [任务 19][Inventory] - Incremental sync starting... 
[INFO ] 2024-07-24 04:12:34.491 - [任务 19][Inventory] - Initial sync completed 
[INFO ] 2024-07-24 04:12:34.545 - [任务 19][Inventory] - Starting stream read, table list: [Inventory], offset: {"currentStartLSN":"0000002900001D300001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 04:12:34.545 - [任务 19][Inventory] - opened cdc tables: [] 
[INFO ] 2024-07-24 04:12:34.545 - [任务 19][Inventory] - building CT table for table Inventory 
[INFO ] 2024-07-24 04:12:34.571 - [任务 19][SqlServerTest2] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-24 04:12:34.572 - [任务 19][Inventory] - build CT table for table Inventory failed 
[INFO ] 2024-07-24 04:12:34.619 - [任务 19][Inventory] - Incremental sync completed 
[INFO ] 2024-07-24 04:12:34.619 - [任务 19][Inventory] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-24 04:12:34.632 - [任务 19][Inventory] - java.lang.RuntimeException: com.microsoft.sqlserver.jdbc.SQLServerException: Object does not exist or access is denied. <-- Error Message -->
java.lang.RuntimeException: com.microsoft.sqlserver.jdbc.SQLServerException: Object does not exist or access is denied.

<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Object does not exist or access is denied.
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1662)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:615)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:537)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: com.microsoft.sqlserver.jdbc.SQLServerException: Object does not exist or access is denied.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:762)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:752)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:641)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: com.microsoft.sqlserver.jdbc.SQLServerException: Object does not exist or access is denied.
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.startCdcRunner(MssqlCdcRunner.java:239)
	at io.tapdata.connector.mssql.MssqlConnector.streamRead(MssqlConnector.java:382)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:741)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Object does not exist or access is denied.
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1662)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:615)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:537)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeQuery(SQLServerPreparedStatement.java:456)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.mssql.cdc.SqlServerCdcHelper.allCaptureInstance(SqlServerCdcHelper.java:162)
	at io.tapdata.connector.mssql.cdc.SqlServerCdcHelper.getCaptureInstance(SqlServerCdcHelper.java:123)
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.changeCdcStatement(MssqlCdcRunner.java:563)
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.initCdcStatement(MssqlCdcRunner.java:534)
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.startCdcRunner(MssqlCdcRunner.java:236)
	... 19 more

[INFO ] 2024-07-24 04:12:34.632 - [任务 19][Inventory] - Job suspend in error handle 
[INFO ] 2024-07-24 04:12:35.038 - [任务 19][Inventory] - Node Inventory[088c3398-09e0-4260-97d7-adbe703f6372] running status set to false 
[INFO ] 2024-07-24 04:12:36.232 - [任务 19] - Task [任务 19] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 04:12:37.933 - [任务 19][Inventory] - PDK connector node stopped: HazelcastSourcePdkDataNode-088c3398-09e0-4260-97d7-adbe703f6372 
[INFO ] 2024-07-24 04:12:37.934 - [任务 19][Inventory] - PDK connector node released: HazelcastSourcePdkDataNode-088c3398-09e0-4260-97d7-adbe703f6372 
[INFO ] 2024-07-24 04:12:37.934 - [任务 19][Inventory] - Node Inventory[088c3398-09e0-4260-97d7-adbe703f6372] schema data cleaned 
[INFO ] 2024-07-24 04:12:37.934 - [任务 19][Inventory] - Node Inventory[088c3398-09e0-4260-97d7-adbe703f6372] monitor closed 
[INFO ] 2024-07-24 04:12:37.940 - [任务 19][Inventory] - Node Inventory[088c3398-09e0-4260-97d7-adbe703f6372] close complete, cost 3057 ms 
[INFO ] 2024-07-24 04:12:37.963 - [任务 19][SqlServerTest2] - Node SqlServerTest2[76954c37-79ad-4130-8cf1-564336185b26] running status set to false 
[INFO ] 2024-07-24 04:12:37.963 - [任务 19][SqlServerTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-76954c37-79ad-4130-8cf1-564336185b26 
[INFO ] 2024-07-24 04:12:37.963 - [任务 19][SqlServerTest2] - PDK connector node released: HazelcastTargetPdkDataNode-76954c37-79ad-4130-8cf1-564336185b26 
[INFO ] 2024-07-24 04:12:37.963 - [任务 19][SqlServerTest2] - Node SqlServerTest2[76954c37-79ad-4130-8cf1-564336185b26] schema data cleaned 
[INFO ] 2024-07-24 04:12:37.964 - [任务 19][SqlServerTest2] - Node SqlServerTest2[76954c37-79ad-4130-8cf1-564336185b26] monitor closed 
[INFO ] 2024-07-24 04:12:38.172 - [任务 19][SqlServerTest2] - Node SqlServerTest2[76954c37-79ad-4130-8cf1-564336185b26] close complete, cost 25 ms 
[INFO ] 2024-07-24 04:12:41.276 - [任务 19] - Task [任务 19] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 04:12:41.278 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 04:12:41.279 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1ffac795 
[INFO ] 2024-07-24 04:12:41.410 - [任务 19] - Stop task milestones: 66a00e68e29f7d4f8d097979(任务 19)  
[INFO ] 2024-07-24 04:12:41.424 - [任务 19] - Stopped task aspect(s) 
[INFO ] 2024-07-24 04:12:41.424 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 04:12:41.440 - [任务 19] - Remove memory task client succeed, task: 任务 19[66a00e68e29f7d4f8d097979] 
[INFO ] 2024-07-24 04:12:41.443 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[66a00e68e29f7d4f8d097979] 
