[INFO ] 2024-07-25 17:40:20.342 - [任务 31 - Copy] - Task initialization... 
[INFO ] 2024-07-25 17:40:20.551 - [任务 31 - Copy] - Start task milestones: 66a21d6b349bc63fe9d89d19(任务 31 - Co<PERSON>) 
[INFO ] 2024-07-25 17:40:20.648 - [任务 31 - Co<PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-25 17:40:20.786 - [任务 31 - Copy] - The engine receives 任务 31 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 17:40:20.786 - [任务 31 - Copy][表编辑] - Node 表编辑[91025f3d-25a2-4124-931a-499b52d125a3] start preload schema,table counts: 1 
[INFO ] 2024-07-25 17:40:20.786 - [任务 31 - Copy][Mysql3307] - Node Mysql3307[b90d2c50-dc33-40b9-9dd8-75b9fac7a710] start preload schema,table counts: 1 
[INFO ] 2024-07-25 17:40:20.787 - [任务 31 - Copy][Mysql] - Node Mysql[15bfe6dd-6de1-4084-89e8-28cb643b89a5] start preload schema,table counts: 1 
[INFO ] 2024-07-25 17:40:20.787 - [任务 31 - Copy][Mysql3307] - Node Mysql3307[b90d2c50-dc33-40b9-9dd8-75b9fac7a710] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 17:40:20.787 - [任务 31 - Copy][表编辑] - Node 表编辑[91025f3d-25a2-4124-931a-499b52d125a3] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 17:40:20.787 - [任务 31 - Copy][Mysql] - Node Mysql[15bfe6dd-6de1-4084-89e8-28cb643b89a5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 17:40:21.406 - [任务 31 - Copy][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 17:40:21.406 - [任务 31 - Copy][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 17:40:21.557 - [任务 31 - Copy][Mysql3307] - Source node "Mysql3307" read batch size: 100 
[INFO ] 2024-07-25 17:40:21.557 - [任务 31 - Copy][Mysql3307] - Source node "Mysql3307" event queue capacity: 200 
[INFO ] 2024-07-25 17:40:21.557 - [任务 31 - Copy][Mysql3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 17:40:21.560 - [任务 31 - Copy][Mysql3307] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":214548423,"gtidSet":""} 
[INFO ] 2024-07-25 17:40:21.560 - [任务 31 - Copy][Mysql3307] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 17:40:21.605 - [任务 31 - Copy][Mysql3307] - Initial sync started 
[INFO ] 2024-07-25 17:40:21.610 - [任务 31 - Copy][Mysql3307] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 17:40:21.610 - [任务 31 - Copy][Mysql3307] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 17:40:21.652 - [任务 31 - Copy][Mysql3307] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 17:40:21.652 - [任务 31 - Copy][Mysql3307] - Query table 'bmsql_customer_test' counts: 4 
[INFO ] 2024-07-25 17:40:21.653 - [任务 31 - Copy][Mysql3307] - Initial sync completed 
[INFO ] 2024-07-25 17:40:21.653 - [任务 31 - Copy][Mysql3307] - Incremental sync starting... 
[INFO ] 2024-07-25 17:40:21.653 - [任务 31 - Copy][Mysql3307] - Initial sync completed 
[INFO ] 2024-07-25 17:40:21.654 - [任务 31 - Copy][Mysql3307] - Starting stream read, table list: [bmsql_customer_test], offset: {"filename":"binlog.000020","position":214548423,"gtidSet":""} 
[INFO ] 2024-07-25 17:40:21.680 - [任务 31 - Copy][Mysql3307] - Starting mysql cdc, server name: d3d544b9-e801-4717-ab5b-aa3ac436c886 
[INFO ] 2024-07-25 17:40:21.680 - [任务 31 - Copy][Mysql3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1770131767
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d3d544b9-e801-4717-ab5b-aa3ac436c886
  database.port: 3307
  threadName: Debezium-Mysql-Connector-d3d544b9-e801-4717-ab5b-aa3ac436c886
  database.hostname: localhost
  database.password: ********
  name: d3d544b9-e801-4717-ab5b-aa3ac436c886
  pdk.offset.string: {"name":"d3d544b9-e801-4717-ab5b-aa3ac436c886","offset":{"{\"server\":\"d3d544b9-e801-4717-ab5b-aa3ac436c886\"}":"{\"file\":\"binlog.000020\",\"pos\":214548423,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.bmsql_customer_test
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-25 17:40:21.887 - [任务 31 - Copy][Mysql3307] - Connector Mysql incremental start succeed, tables: [bmsql_customer_test], data change syncing 
[INFO ] 2024-07-25 17:42:17.606 - [任务 31 - Copy][Mysql3307] - Node Mysql3307[b90d2c50-dc33-40b9-9dd8-75b9fac7a710] running status set to false 
[INFO ] 2024-07-25 17:42:17.649 - [任务 31 - Copy][Mysql3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-25 17:42:17.649 - [任务 31 - Copy][Mysql3307] - Mysql binlog reader stopped 
[INFO ] 2024-07-25 17:42:17.650 - [任务 31 - Copy][Mysql3307] - Incremental sync completed 
[INFO ] 2024-07-25 17:42:17.659 - [任务 31 - Copy][Mysql3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-b90d2c50-dc33-40b9-9dd8-75b9fac7a710 
[INFO ] 2024-07-25 17:42:17.659 - [任务 31 - Copy][Mysql3307] - PDK connector node released: HazelcastSourcePdkDataNode-b90d2c50-dc33-40b9-9dd8-75b9fac7a710 
[INFO ] 2024-07-25 17:42:17.660 - [任务 31 - Copy][Mysql3307] - Node Mysql3307[b90d2c50-dc33-40b9-9dd8-75b9fac7a710] schema data cleaned 
[INFO ] 2024-07-25 17:42:17.660 - [任务 31 - Copy][Mysql3307] - Node Mysql3307[b90d2c50-dc33-40b9-9dd8-75b9fac7a710] monitor closed 
[INFO ] 2024-07-25 17:42:17.662 - [任务 31 - Copy][Mysql3307] - Node Mysql3307[b90d2c50-dc33-40b9-9dd8-75b9fac7a710] close complete, cost 79 ms 
[INFO ] 2024-07-25 17:42:17.662 - [任务 31 - Copy][表编辑] - Node 表编辑[91025f3d-25a2-4124-931a-499b52d125a3] running status set to false 
[INFO ] 2024-07-25 17:42:17.671 - [任务 31 - Copy][表编辑] - Node 表编辑[91025f3d-25a2-4124-931a-499b52d125a3] schema data cleaned 
[INFO ] 2024-07-25 17:42:17.672 - [任务 31 - Copy][表编辑] - Node 表编辑[91025f3d-25a2-4124-931a-499b52d125a3] monitor closed 
[INFO ] 2024-07-25 17:42:17.675 - [任务 31 - Copy][表编辑] - Node 表编辑[91025f3d-25a2-4124-931a-499b52d125a3] close complete, cost 13 ms 
[INFO ] 2024-07-25 17:42:17.675 - [任务 31 - Copy][Mysql] - Node Mysql[15bfe6dd-6de1-4084-89e8-28cb643b89a5] running status set to false 
[INFO ] 2024-07-25 17:42:17.691 - [任务 31 - Copy][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-15bfe6dd-6de1-4084-89e8-28cb643b89a5 
[INFO ] 2024-07-25 17:42:17.691 - [任务 31 - Copy][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-15bfe6dd-6de1-4084-89e8-28cb643b89a5 
[INFO ] 2024-07-25 17:42:17.692 - [任务 31 - Copy][Mysql] - Node Mysql[15bfe6dd-6de1-4084-89e8-28cb643b89a5] schema data cleaned 
[INFO ] 2024-07-25 17:42:17.694 - [任务 31 - Copy][Mysql] - Node Mysql[15bfe6dd-6de1-4084-89e8-28cb643b89a5] monitor closed 
[INFO ] 2024-07-25 17:42:17.694 - [任务 31 - Copy][Mysql] - Node Mysql[15bfe6dd-6de1-4084-89e8-28cb643b89a5] close complete, cost 18 ms 
[INFO ] 2024-07-25 17:42:18.829 - [任务 31 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 17:42:18.944 - [任务 31 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1bd44c32 
[INFO ] 2024-07-25 17:42:18.944 - [任务 31 - Copy] - Stop task milestones: 66a21d6b349bc63fe9d89d19(任务 31 - Copy)  
[INFO ] 2024-07-25 17:42:18.955 - [任务 31 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-25 17:42:18.955 - [任务 31 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 17:42:18.972 - [任务 31 - Copy] - Remove memory task client succeed, task: 任务 31 - Copy[66a21d6b349bc63fe9d89d19] 
[INFO ] 2024-07-25 17:42:19.172 - [任务 31 - Copy] - Destroy memory task client cache succeed, task: 任务 31 - Copy[66a21d6b349bc63fe9d89d19] 
