[INFO ] 2024-10-14 07:59:47.670 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 07:59:47.690 - [测试主从合并内嵌数组没有关联键(100)][fa098545-06de-45e6-a411-e44d96ce4c79] - Node fa098545-06de-45e6-a411-e44d96ce4c79[fa098545-06de-45e6-a411-e44d96ce4c79] start preload schema,table counts: 0 
[INFO ] 2024-10-14 07:59:47.690 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 07:59:47.690 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 07:59:47.691 - [测试主从合并内嵌数组没有关联键(100)][8188fcc4-3749-4556-9589-604a6b038209] - Node 8188fcc4-3749-4556-9589-604a6b038209[8188fcc4-3749-4556-9589-604a6b038209] start preload schema,table counts: 0 
[INFO ] 2024-10-14 07:59:47.698 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 07:59:47.699 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:47.701 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:47.701 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:47.702 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:47.702 - [测试主从合并内嵌数组没有关联键(100)][fa098545-06de-45e6-a411-e44d96ce4c79] - Node fa098545-06de-45e6-a411-e44d96ce4c79[fa098545-06de-45e6-a411-e44d96ce4c79] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:47.702 - [测试主从合并内嵌数组没有关联键(100)][8188fcc4-3749-4556-9589-604a6b038209] - Node 8188fcc4-3749-4556-9589-604a6b038209[8188fcc4-3749-4556-9589-604a6b038209] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:47.702 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 07:59:47.702 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 07:59:53.500 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 07:59:53.586 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 07:59:53.588 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 07:59:53.591 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 07:59:53.592 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 07:59:53.594 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 123 ms 
[INFO ] 2024-10-14 07:59:58.625 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 07:59:58.625 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 07:59:58.625 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 07:59:58.629 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 07:59:58.631 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 07:59:58.632 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 64 ms 
[INFO ] 2024-10-14 07:59:59.397 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 07:59:59.413 - [测试主从合并内嵌数组没有关联键(100)][8188fcc4-3749-4556-9589-604a6b038209] - Node 8188fcc4-3749-4556-9589-604a6b038209[8188fcc4-3749-4556-9589-604a6b038209] running status set to false 
[INFO ] 2024-10-14 07:59:59.419 - [测试主从合并内嵌数组没有关联键(100)][8188fcc4-3749-4556-9589-604a6b038209] - Node 8188fcc4-3749-4556-9589-604a6b038209[8188fcc4-3749-4556-9589-604a6b038209] schema data cleaned 
[INFO ] 2024-10-14 07:59:59.423 - [测试主从合并内嵌数组没有关联键(100)][8188fcc4-3749-4556-9589-604a6b038209] - Node 8188fcc4-3749-4556-9589-604a6b038209[8188fcc4-3749-4556-9589-604a6b038209] monitor closed 
[INFO ] 2024-10-14 07:59:59.424 - [测试主从合并内嵌数组没有关联键(100)][8188fcc4-3749-4556-9589-604a6b038209] - Node 8188fcc4-3749-4556-9589-604a6b038209[8188fcc4-3749-4556-9589-604a6b038209] close complete, cost 5 ms 
[INFO ] 2024-10-14 07:59:59.439 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-63015364-f77a-4d1a-8ed2-f9884519970c 
[INFO ] 2024-10-14 07:59:59.442 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-63015364-f77a-4d1a-8ed2-f9884519970c 
[INFO ] 2024-10-14 07:59:59.442 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 07:59:59.453 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 07:59:59.453 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 07:59:59.463 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 67 ms 
[INFO ] 2024-10-14 07:59:59.482 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 07:59:59.482 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 07:59:59.482 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 07:59:59.684 - [测试主从合并内嵌数组没有关联键(100)][d84b2211-2ebd-4724-8573-5e4a4622805f] - Node d84b2211-2ebd-4724-8573-5e4a4622805f[d84b2211-2ebd-4724-8573-5e4a4622805f] start preload schema,table counts: 0 
[INFO ] 2024-10-14 07:59:59.685 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 07:59:59.685 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 07:59:59.685 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:59.686 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:59.686 - [测试主从合并内嵌数组没有关联键(100)][d84b2211-2ebd-4724-8573-5e4a4622805f] - Node d84b2211-2ebd-4724-8573-5e4a4622805f[d84b2211-2ebd-4724-8573-5e4a4622805f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 07:59:59.686 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:00:04.826 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:00:04.832 - [测试主从合并内嵌数组没有关联键(100)][fa098545-06de-45e6-a411-e44d96ce4c79] - Node fa098545-06de-45e6-a411-e44d96ce4c79[fa098545-06de-45e6-a411-e44d96ce4c79] running status set to false 
[INFO ] 2024-10-14 08:00:04.841 - [测试主从合并内嵌数组没有关联键(100)][fa098545-06de-45e6-a411-e44d96ce4c79] - Node fa098545-06de-45e6-a411-e44d96ce4c79[fa098545-06de-45e6-a411-e44d96ce4c79] schema data cleaned 
[INFO ] 2024-10-14 08:00:04.841 - [测试主从合并内嵌数组没有关联键(100)][fa098545-06de-45e6-a411-e44d96ce4c79] - Node fa098545-06de-45e6-a411-e44d96ce4c79[fa098545-06de-45e6-a411-e44d96ce4c79] monitor closed 
[INFO ] 2024-10-14 08:00:04.842 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-af113e90-5422-4ed6-9a5f-3aa439b0b4a6 
[INFO ] 2024-10-14 08:00:04.842 - [测试主从合并内嵌数组没有关联键(100)][fa098545-06de-45e6-a411-e44d96ce4c79] - Node fa098545-06de-45e6-a411-e44d96ce4c79[fa098545-06de-45e6-a411-e44d96ce4c79] close complete, cost 70 ms 
[INFO ] 2024-10-14 08:00:04.843 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-af113e90-5422-4ed6-9a5f-3aa439b0b4a6 
[INFO ] 2024-10-14 08:00:04.843 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:00:04.846 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:00:04.846 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:00:04.849 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 82 ms 
[INFO ] 2024-10-14 08:00:04.850 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:04.850 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:04.922 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:04.936 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:00:04.950 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:04.950 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:04.950 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:00:04.950 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:00:05.036 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 43 ms 
[INFO ] 2024-10-14 08:00:05.036 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:05.036 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:05.037 - [测试主从合并内嵌数组没有关联键(100)][65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] - Node 65f95c7e-5459-4bf5-ae06-e5b9d85a32cd[65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:05.037 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:05.037 - [测试主从合并内嵌数组没有关联键(100)][65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] - Node 65f95c7e-5459-4bf5-ae06-e5b9d85a32cd[65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:05.037 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:05.041 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:00:10.582 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:00:10.624 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:10.624 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:10.624 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:00:10.624 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:00:10.829 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 49 ms 
[INFO ] 2024-10-14 08:00:11.411 - [测试主从合并内嵌数组没有关联键(100)][0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] - Node 0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a[0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:11.411 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:11.412 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:11.412 - [测试主从合并内嵌数组没有关联键(100)][0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] - Node 0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a[0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:11.412 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:11.412 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:11.412 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:11.905 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:11.906 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:11.906 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:11.906 - [测试主从合并内嵌数组没有关联键(100)][138a6906-2a65-4132-bd8b-14b33e601f79] - Node 138a6906-2a65-4132-bd8b-14b33e601f79[138a6906-2a65-4132-bd8b-14b33e601f79] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:11.906 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:11.906 - [测试主从合并内嵌数组没有关联键(100)][138a6906-2a65-4132-bd8b-14b33e601f79] - Node 138a6906-2a65-4132-bd8b-14b33e601f79[138a6906-2a65-4132-bd8b-14b33e601f79] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:11.907 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:13.450 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:13.451 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:13.451 - [测试主从合并内嵌数组没有关联键(100)][03b8e532-1966-4a94-977c-8bdad29897c2] - Node 03b8e532-1966-4a94-977c-8bdad29897c2[03b8e532-1966-4a94-977c-8bdad29897c2] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:13.451 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:13.451 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:13.451 - [测试主从合并内嵌数组没有关联键(100)][03b8e532-1966-4a94-977c-8bdad29897c2] - Node 03b8e532-1966-4a94-977c-8bdad29897c2[03b8e532-1966-4a94-977c-8bdad29897c2] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:13.451 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:15.797 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:00:15.799 - [测试主从合并内嵌数组没有关联键(100)][d84b2211-2ebd-4724-8573-5e4a4622805f] - Node d84b2211-2ebd-4724-8573-5e4a4622805f[d84b2211-2ebd-4724-8573-5e4a4622805f] running status set to false 
[INFO ] 2024-10-14 08:00:15.799 - [测试主从合并内嵌数组没有关联键(100)][d84b2211-2ebd-4724-8573-5e4a4622805f] - Node d84b2211-2ebd-4724-8573-5e4a4622805f[d84b2211-2ebd-4724-8573-5e4a4622805f] schema data cleaned 
[INFO ] 2024-10-14 08:00:15.799 - [测试主从合并内嵌数组没有关联键(100)][d84b2211-2ebd-4724-8573-5e4a4622805f] - Node d84b2211-2ebd-4724-8573-5e4a4622805f[d84b2211-2ebd-4724-8573-5e4a4622805f] monitor closed 
[INFO ] 2024-10-14 08:00:15.799 - [测试主从合并内嵌数组没有关联键(100)][d84b2211-2ebd-4724-8573-5e4a4622805f] - Node d84b2211-2ebd-4724-8573-5e4a4622805f[d84b2211-2ebd-4724-8573-5e4a4622805f] close complete, cost 6 ms 
[INFO ] 2024-10-14 08:00:15.841 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-1379a3ac-8f71-46df-a64e-658acb885cd7 
[INFO ] 2024-10-14 08:00:15.844 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-1379a3ac-8f71-46df-a64e-658acb885cd7 
[INFO ] 2024-10-14 08:00:15.844 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:00:15.848 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:00:15.852 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:00:15.852 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 62 ms 
[INFO ] 2024-10-14 08:00:15.854 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:15.855 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:15.855 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:17.229 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:17.230 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:17.230 - [测试主从合并内嵌数组没有关联键(100)][d489f0f5-7548-4ef5-8973-373cd0ca9a69] - Node d489f0f5-7548-4ef5-8973-373cd0ca9a69[d489f0f5-7548-4ef5-8973-373cd0ca9a69] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:17.230 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:17.230 - [测试主从合并内嵌数组没有关联键(100)][d489f0f5-7548-4ef5-8973-373cd0ca9a69] - Node d489f0f5-7548-4ef5-8973-373cd0ca9a69[d489f0f5-7548-4ef5-8973-373cd0ca9a69] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:17.230 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:17.231 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:17.437 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:17.438 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:17.438 - [测试主从合并内嵌数组没有关联键(100)][96b7e2a5-9cda-4669-8bdd-c60a43f79817] - Node 96b7e2a5-9cda-4669-8bdd-c60a43f79817[96b7e2a5-9cda-4669-8bdd-c60a43f79817] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:17.440 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:17.441 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:17.442 - [测试主从合并内嵌数组没有关联键(100)][96b7e2a5-9cda-4669-8bdd-c60a43f79817] - Node 96b7e2a5-9cda-4669-8bdd-c60a43f79817[96b7e2a5-9cda-4669-8bdd-c60a43f79817] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:17.442 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:17.974 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:00:17.974 - [测试主从合并内嵌数组没有关联键(100)][65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] - Node 65f95c7e-5459-4bf5-ae06-e5b9d85a32cd[65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] running status set to false 
[INFO ] 2024-10-14 08:00:17.974 - [测试主从合并内嵌数组没有关联键(100)][65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] - Node 65f95c7e-5459-4bf5-ae06-e5b9d85a32cd[65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] schema data cleaned 
[INFO ] 2024-10-14 08:00:17.974 - [测试主从合并内嵌数组没有关联键(100)][65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] - Node 65f95c7e-5459-4bf5-ae06-e5b9d85a32cd[65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] monitor closed 
[INFO ] 2024-10-14 08:00:17.998 - [测试主从合并内嵌数组没有关联键(100)][65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] - Node 65f95c7e-5459-4bf5-ae06-e5b9d85a32cd[65f95c7e-5459-4bf5-ae06-e5b9d85a32cd] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:00:17.998 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-a2df8ef3-aa5e-4cd8-933f-d62edab42d36 
[INFO ] 2024-10-14 08:00:18.000 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-a2df8ef3-aa5e-4cd8-933f-d62edab42d36 
[INFO ] 2024-10-14 08:00:18.000 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:00:18.003 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:00:18.003 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:00:18.006 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 31 ms 
[INFO ] 2024-10-14 08:00:18.006 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:18.006 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:18.219 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:23.092 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:23.123 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:23.123 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:23.123 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:23.123 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:23.329 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 57 ms 
[INFO ] 2024-10-14 08:00:24.218 - [测试主从合并内嵌数组没有关联键(100)][d057f36c-e828-4790-aafb-b1dfbe606764] - Node d057f36c-e828-4790-aafb-b1dfbe606764[d057f36c-e828-4790-aafb-b1dfbe606764] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:24.218 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:24.218 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:24.218 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:24.218 - [测试主从合并内嵌数组没有关联键(100)][d057f36c-e828-4790-aafb-b1dfbe606764] - Node d057f36c-e828-4790-aafb-b1dfbe606764[d057f36c-e828-4790-aafb-b1dfbe606764] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:24.218 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:24.219 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:28.477 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:28.478 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:28.478 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:28.478 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:28.479 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:28.498 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 83 ms 
[INFO ] 2024-10-14 08:00:28.499 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:00:28.499 - [测试主从合并内嵌数组没有关联键(100)][96b7e2a5-9cda-4669-8bdd-c60a43f79817] - Node 96b7e2a5-9cda-4669-8bdd-c60a43f79817[96b7e2a5-9cda-4669-8bdd-c60a43f79817] running status set to false 
[INFO ] 2024-10-14 08:00:28.499 - [测试主从合并内嵌数组没有关联键(100)][96b7e2a5-9cda-4669-8bdd-c60a43f79817] - Node 96b7e2a5-9cda-4669-8bdd-c60a43f79817[96b7e2a5-9cda-4669-8bdd-c60a43f79817] schema data cleaned 
[INFO ] 2024-10-14 08:00:28.499 - [测试主从合并内嵌数组没有关联键(100)][96b7e2a5-9cda-4669-8bdd-c60a43f79817] - Node 96b7e2a5-9cda-4669-8bdd-c60a43f79817[96b7e2a5-9cda-4669-8bdd-c60a43f79817] monitor closed 
[INFO ] 2024-10-14 08:00:28.499 - [测试主从合并内嵌数组没有关联键(100)][96b7e2a5-9cda-4669-8bdd-c60a43f79817] - Node 96b7e2a5-9cda-4669-8bdd-c60a43f79817[96b7e2a5-9cda-4669-8bdd-c60a43f79817] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:00:28.531 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-affdbf09-ef69-447f-9763-d6ec977db8e7 
[INFO ] 2024-10-14 08:00:28.531 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-affdbf09-ef69-447f-9763-d6ec977db8e7 
[INFO ] 2024-10-14 08:00:28.532 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:00:28.533 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:00:28.533 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:00:28.543 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 37 ms 
[INFO ] 2024-10-14 08:00:28.543 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:28.544 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:28.544 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:28.702 - [测试主从合并内嵌数组没有关联键(100)][b8061123-a490-4bb4-8822-5299fc37c35f] - Node b8061123-a490-4bb4-8822-5299fc37c35f[b8061123-a490-4bb4-8822-5299fc37c35f] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:28.703 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:28.703 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:28.703 - [测试主从合并内嵌数组没有关联键(100)][b8061123-a490-4bb4-8822-5299fc37c35f] - Node b8061123-a490-4bb4-8822-5299fc37c35f[b8061123-a490-4bb4-8822-5299fc37c35f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:28.703 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:28.703 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:28.704 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:00:33.499 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:33.499 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:33.503 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:33.503 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:33.503 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:33.513 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 59 ms 
[INFO ] 2024-10-14 08:00:34.253 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:00:34.254 - [测试主从合并内嵌数组没有关联键(100)][d489f0f5-7548-4ef5-8973-373cd0ca9a69] - Node d489f0f5-7548-4ef5-8973-373cd0ca9a69[d489f0f5-7548-4ef5-8973-373cd0ca9a69] running status set to false 
[INFO ] 2024-10-14 08:00:34.254 - [测试主从合并内嵌数组没有关联键(100)][d489f0f5-7548-4ef5-8973-373cd0ca9a69] - Node d489f0f5-7548-4ef5-8973-373cd0ca9a69[d489f0f5-7548-4ef5-8973-373cd0ca9a69] schema data cleaned 
[INFO ] 2024-10-14 08:00:34.254 - [测试主从合并内嵌数组没有关联键(100)][d489f0f5-7548-4ef5-8973-373cd0ca9a69] - Node d489f0f5-7548-4ef5-8973-373cd0ca9a69[d489f0f5-7548-4ef5-8973-373cd0ca9a69] monitor closed 
[INFO ] 2024-10-14 08:00:34.254 - [测试主从合并内嵌数组没有关联键(100)][d489f0f5-7548-4ef5-8973-373cd0ca9a69] - Node d489f0f5-7548-4ef5-8973-373cd0ca9a69[d489f0f5-7548-4ef5-8973-373cd0ca9a69] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:00:34.306 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-93580ff9-dfc3-43a0-9f94-ca835c03ea1c 
[INFO ] 2024-10-14 08:00:34.307 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-93580ff9-dfc3-43a0-9f94-ca835c03ea1c 
[INFO ] 2024-10-14 08:00:34.307 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:00:34.309 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:00:34.310 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:00:34.312 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 64 ms 
[INFO ] 2024-10-14 08:00:34.313 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:34.313 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:34.480 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:34.482 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:34.484 - [测试主从合并内嵌数组没有关联键(100)][ac1fd637-cb5e-48b5-901d-03c902559187] - Node ac1fd637-cb5e-48b5-901d-03c902559187[ac1fd637-cb5e-48b5-901d-03c902559187] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:34.485 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:34.485 - [测试主从合并内嵌数组没有关联键(100)][ac1fd637-cb5e-48b5-901d-03c902559187] - Node ac1fd637-cb5e-48b5-901d-03c902559187[ac1fd637-cb5e-48b5-901d-03c902559187] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:34.485 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:34.485 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:00:34.485 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:00:35.684 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.IllegalAccessError: tried to access class com.zaxxer.hikari.pool.PoolBase$ConnectionSetupException from class com.zaxxer.hikari.pool.HikariPool 
[ERROR] 2024-10-14 08:00:35.686 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - start source runner failed: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.IllegalAccessError: tried to access class com.zaxxer.hikari.pool.PoolBase$ConnectionSetupException from class com.zaxxer.hikari.pool.HikariPool <-- Error Message -->
start source runner failed: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.IllegalAccessError: tried to access class com.zaxxer.hikari.pool.PoolBase$ConnectionSetupException from class com.zaxxer.hikari.pool.HikariPool

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalAccessError: tried to access class com.zaxxer.hikari.pool.PoolBase$ConnectionSetupException from class com.zaxxer.hikari.pool.HikariPool
	com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	...

<-- Full Stack Trace -->
java.lang.IllegalAccessError: tried to access class com.zaxxer.hikari.pool.PoolBase$ConnectionSetupException from class com.zaxxer.hikari.pool.HikariPool
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalAccessError: tried to access class com.zaxxer.hikari.pool.PoolBase$ConnectionSetupException from class com.zaxxer.hikari.pool.HikariPool
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:117)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 23 more

[INFO ] 2024-10-14 08:00:37.603 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:37.604 - [测试主从合并内嵌数组没有关联键(100)][a9c329b8-0a29-48e5-be19-4ddd4d4d4514] - Node a9c329b8-0a29-48e5-be19-4ddd4d4d4514[a9c329b8-0a29-48e5-be19-4ddd4d4d4514] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:37.604 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:37.604 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:37.604 - [测试主从合并内嵌数组没有关联键(100)][a9c329b8-0a29-48e5-be19-4ddd4d4d4514] - Node a9c329b8-0a29-48e5-be19-4ddd4d4d4514[a9c329b8-0a29-48e5-be19-4ddd4d4d4514] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:37.604 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:37.604 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:37.912 - [测试主从合并内嵌数组没有关联键(100)][4870771a-a217-4497-9c52-cf94a1f10fc8] - Node 4870771a-a217-4497-9c52-cf94a1f10fc8[4870771a-a217-4497-9c52-cf94a1f10fc8] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:37.913 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:37.913 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:37.913 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:37.913 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:00:37.913 - [测试主从合并内嵌数组没有关联键(100)][4870771a-a217-4497-9c52-cf94a1f10fc8] - Node 4870771a-a217-4497-9c52-cf94a1f10fc8[4870771a-a217-4497-9c52-cf94a1f10fc8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:00:37.913 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:00:38.220 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:38.220 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:00:38.221 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:00:38.222 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:00:38.222 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:00:38.223 - [测试主从合并内嵌数组没有关联键(100)][03b8e532-1966-4a94-977c-8bdad29897c2] - Node 03b8e532-1966-4a94-977c-8bdad29897c2[03b8e532-1966-4a94-977c-8bdad29897c2] running status set to false 
[INFO ] 2024-10-14 08:00:38.223 - [测试主从合并内嵌数组没有关联键(100)][03b8e532-1966-4a94-977c-8bdad29897c2] - Node 03b8e532-1966-4a94-977c-8bdad29897c2[03b8e532-1966-4a94-977c-8bdad29897c2] schema data cleaned 
[INFO ] 2024-10-14 08:00:38.223 - [测试主从合并内嵌数组没有关联键(100)][03b8e532-1966-4a94-977c-8bdad29897c2] - Node 03b8e532-1966-4a94-977c-8bdad29897c2[03b8e532-1966-4a94-977c-8bdad29897c2] monitor closed 
[INFO ] 2024-10-14 08:00:38.223 - [测试主从合并内嵌数组没有关联键(100)][03b8e532-1966-4a94-977c-8bdad29897c2] - Node 03b8e532-1966-4a94-977c-8bdad29897c2[03b8e532-1966-4a94-977c-8bdad29897c2] close complete, cost 0 ms 
[WARN ] 2024-10-14 08:00:38.223 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Stop PDK connector node failed: Unknown PDK exception occur, java.lang.BootstrapMethodError: java.lang.NoClassDefFoundError: io/tapdata/connector/mysql/MysqlReader | Associate id: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:38.223 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:38.223 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:38.224 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:38.224 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 5 ms 
[INFO ] 2024-10-14 08:00:38.225 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:38.225 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:38.225 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:38.273 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:38.273 - [测试主从合并内嵌数组没有关联键(100)][6069e845-de78-4e63-ad98-7a6b5ab1373c] - Node 6069e845-de78-4e63-ad98-7a6b5ab1373c[6069e845-de78-4e63-ad98-7a6b5ab1373c] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:38.274 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:38.274 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:38.274 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:38.274 - [测试主从合并内嵌数组没有关联键(100)][6069e845-de78-4e63-ad98-7a6b5ab1373c] - Node 6069e845-de78-4e63-ad98-7a6b5ab1373c[6069e845-de78-4e63-ad98-7a6b5ab1373c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:38.478 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:00:39.932 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:00:39.984 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:39.984 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:39.985 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:00:39.985 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:00:40.195 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 55 ms 
[INFO ] 2024-10-14 08:00:40.785 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:40.840 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:40.841 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:40.841 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:40.842 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:40.842 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 69 ms 
[INFO ] 2024-10-14 08:00:40.991 - [测试主从合并内嵌数组没有关联键(100)][d057f36c-e828-4790-aafb-b1dfbe606764] - Node d057f36c-e828-4790-aafb-b1dfbe606764[d057f36c-e828-4790-aafb-b1dfbe606764] running status set to false 
[INFO ] 2024-10-14 08:00:40.991 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:00:40.992 - [测试主从合并内嵌数组没有关联键(100)][d057f36c-e828-4790-aafb-b1dfbe606764] - Node d057f36c-e828-4790-aafb-b1dfbe606764[d057f36c-e828-4790-aafb-b1dfbe606764] schema data cleaned 
[INFO ] 2024-10-14 08:00:40.992 - [测试主从合并内嵌数组没有关联键(100)][d057f36c-e828-4790-aafb-b1dfbe606764] - Node d057f36c-e828-4790-aafb-b1dfbe606764[d057f36c-e828-4790-aafb-b1dfbe606764] monitor closed 
[INFO ] 2024-10-14 08:00:40.992 - [测试主从合并内嵌数组没有关联键(100)][d057f36c-e828-4790-aafb-b1dfbe606764] - Node d057f36c-e828-4790-aafb-b1dfbe606764[d057f36c-e828-4790-aafb-b1dfbe606764] close complete, cost 4 ms 
[INFO ] 2024-10-14 08:00:41.027 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-b9afcaf7-f7ee-4584-89cb-2ca802c19f25 
[INFO ] 2024-10-14 08:00:41.027 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-b9afcaf7-f7ee-4584-89cb-2ca802c19f25 
[INFO ] 2024-10-14 08:00:41.028 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:00:41.033 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:00:41.033 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:00:41.033 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 47 ms 
[INFO ] 2024-10-14 08:00:41.035 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:41.035 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:41.220 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:41.221 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:41.221 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:41.222 - [测试主从合并内嵌数组没有关联键(100)][ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] - Node ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73[ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:41.222 - [测试主从合并内嵌数组没有关联键(100)][ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] - Node ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73[ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:41.223 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:00:41.223 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:00:41.224 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:00:45.399 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:00:45.400 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:00:45.400 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:45.400 - [测试主从合并内嵌数组没有关联键(100)][ac1fd637-cb5e-48b5-901d-03c902559187] - Node ac1fd637-cb5e-48b5-901d-03c902559187[ac1fd637-cb5e-48b5-901d-03c902559187] running status set to false 
[INFO ] 2024-10-14 08:00:45.400 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:45.400 - [测试主从合并内嵌数组没有关联键(100)][ac1fd637-cb5e-48b5-901d-03c902559187] - Node ac1fd637-cb5e-48b5-901d-03c902559187[ac1fd637-cb5e-48b5-901d-03c902559187] schema data cleaned 
[INFO ] 2024-10-14 08:00:45.400 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:00:45.420 - [测试主从合并内嵌数组没有关联键(100)][ac1fd637-cb5e-48b5-901d-03c902559187] - Node ac1fd637-cb5e-48b5-901d-03c902559187[ac1fd637-cb5e-48b5-901d-03c902559187] monitor closed 
[INFO ] 2024-10-14 08:00:45.421 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:00:45.421 - [测试主从合并内嵌数组没有关联键(100)][ac1fd637-cb5e-48b5-901d-03c902559187] - Node ac1fd637-cb5e-48b5-901d-03c902559187[ac1fd637-cb5e-48b5-901d-03c902559187] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:00:45.421 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 126 ms 
[INFO ] 2024-10-14 08:00:45.440 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-08adbb9b-85a9-4e87-826d-87fc1dad0887 
[INFO ] 2024-10-14 08:00:45.440 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-08adbb9b-85a9-4e87-826d-87fc1dad0887 
[INFO ] 2024-10-14 08:00:45.441 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:00:45.443 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:00:45.444 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:00:45.448 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 54 ms 
[INFO ] 2024-10-14 08:00:45.448 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:45.448 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:45.449 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:45.653 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:45.660 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:45.660 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:45.661 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:45.661 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:45.866 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 24 ms 
[INFO ] 2024-10-14 08:00:47.958 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:00:47.960 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:47.971 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:47.971 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:00:47.971 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:00:48.176 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 64 ms 
[INFO ] 2024-10-14 08:00:49.610 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:00:49.611 - [测试主从合并内嵌数组没有关联键(100)][6069e845-de78-4e63-ad98-7a6b5ab1373c] - Node 6069e845-de78-4e63-ad98-7a6b5ab1373c[6069e845-de78-4e63-ad98-7a6b5ab1373c] running status set to false 
[INFO ] 2024-10-14 08:00:49.611 - [测试主从合并内嵌数组没有关联键(100)][6069e845-de78-4e63-ad98-7a6b5ab1373c] - Node 6069e845-de78-4e63-ad98-7a6b5ab1373c[6069e845-de78-4e63-ad98-7a6b5ab1373c] schema data cleaned 
[INFO ] 2024-10-14 08:00:49.611 - [测试主从合并内嵌数组没有关联键(100)][6069e845-de78-4e63-ad98-7a6b5ab1373c] - Node 6069e845-de78-4e63-ad98-7a6b5ab1373c[6069e845-de78-4e63-ad98-7a6b5ab1373c] monitor closed 
[INFO ] 2024-10-14 08:00:49.650 - [测试主从合并内嵌数组没有关联键(100)][6069e845-de78-4e63-ad98-7a6b5ab1373c] - Node 6069e845-de78-4e63-ad98-7a6b5ab1373c[6069e845-de78-4e63-ad98-7a6b5ab1373c] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:00:49.650 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-85f1768e-c6d1-4af5-947c-4aa63b1c5a46 
[INFO ] 2024-10-14 08:00:49.651 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-85f1768e-c6d1-4af5-947c-4aa63b1c5a46 
[INFO ] 2024-10-14 08:00:49.651 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:00:49.653 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:00:49.653 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:00:49.655 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 48 ms 
[INFO ] 2024-10-14 08:00:49.656 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:49.656 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:49.862 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:50.632 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:50.632 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:50.632 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:50.632 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:50.633 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:50.633 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 59 ms 
[INFO ] 2024-10-14 08:00:52.418 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:00:52.444 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:52.444 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:00:52.445 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:00:52.446 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:00:52.447 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 46 ms 
[INFO ] 2024-10-14 08:00:55.267 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:00:55.270 - [测试主从合并内嵌数组没有关联键(100)][b8061123-a490-4bb4-8822-5299fc37c35f] - Node b8061123-a490-4bb4-8822-5299fc37c35f[b8061123-a490-4bb4-8822-5299fc37c35f] running status set to false 
[INFO ] 2024-10-14 08:00:55.270 - [测试主从合并内嵌数组没有关联键(100)][b8061123-a490-4bb4-8822-5299fc37c35f] - Node b8061123-a490-4bb4-8822-5299fc37c35f[b8061123-a490-4bb4-8822-5299fc37c35f] schema data cleaned 
[INFO ] 2024-10-14 08:00:55.294 - [测试主从合并内嵌数组没有关联键(100)][b8061123-a490-4bb4-8822-5299fc37c35f] - Node b8061123-a490-4bb4-8822-5299fc37c35f[b8061123-a490-4bb4-8822-5299fc37c35f] monitor closed 
[INFO ] 2024-10-14 08:00:55.294 - [测试主从合并内嵌数组没有关联键(100)][b8061123-a490-4bb4-8822-5299fc37c35f] - Node b8061123-a490-4bb4-8822-5299fc37c35f[b8061123-a490-4bb4-8822-5299fc37c35f] close complete, cost 12 ms 
[INFO ] 2024-10-14 08:00:55.337 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-c2f21518-006b-4e33-a6aa-2b6c6ead2550 
[INFO ] 2024-10-14 08:00:55.337 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-c2f21518-006b-4e33-a6aa-2b6c6ead2550 
[INFO ] 2024-10-14 08:00:55.343 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:00:55.343 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:00:55.343 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:00:55.343 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 143 ms 
[INFO ] 2024-10-14 08:00:55.348 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:55.348 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:55.348 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:55.634 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:00:55.652 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:55.652 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:00:55.652 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:00:55.652 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:00:55.654 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 29 ms 
[INFO ] 2024-10-14 08:00:58.088 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:00:58.089 - [测试主从合并内嵌数组没有关联键(100)][4870771a-a217-4497-9c52-cf94a1f10fc8] - Node 4870771a-a217-4497-9c52-cf94a1f10fc8[4870771a-a217-4497-9c52-cf94a1f10fc8] running status set to false 
[INFO ] 2024-10-14 08:00:58.090 - [测试主从合并内嵌数组没有关联键(100)][4870771a-a217-4497-9c52-cf94a1f10fc8] - Node 4870771a-a217-4497-9c52-cf94a1f10fc8[4870771a-a217-4497-9c52-cf94a1f10fc8] schema data cleaned 
[INFO ] 2024-10-14 08:00:58.090 - [测试主从合并内嵌数组没有关联键(100)][4870771a-a217-4497-9c52-cf94a1f10fc8] - Node 4870771a-a217-4497-9c52-cf94a1f10fc8[4870771a-a217-4497-9c52-cf94a1f10fc8] monitor closed 
[INFO ] 2024-10-14 08:00:58.090 - [测试主从合并内嵌数组没有关联键(100)][4870771a-a217-4497-9c52-cf94a1f10fc8] - Node 4870771a-a217-4497-9c52-cf94a1f10fc8[4870771a-a217-4497-9c52-cf94a1f10fc8] close complete, cost 7 ms 
[INFO ] 2024-10-14 08:00:58.139 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-b7163c40-ef03-495b-84f8-f4bead9bcaf1 
[INFO ] 2024-10-14 08:00:58.139 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-b7163c40-ef03-495b-84f8-f4bead9bcaf1 
[INFO ] 2024-10-14 08:00:58.139 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:00:58.142 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:00:58.142 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:00:58.143 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 71 ms 
[INFO ] 2024-10-14 08:00:58.147 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:58.147 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:58.359 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:58.365 - [测试主从合并内嵌数组没有关联键(100)][7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] - Node 7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d[7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:58.365 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:58.367 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:58.368 - [测试主从合并内嵌数组没有关联键(100)][7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] - Node 7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d[7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:58.368 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:58.368 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:58.573 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:00:59.584 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:00:59.585 - [测试主从合并内嵌数组没有关联键(100)][138a6906-2a65-4132-bd8b-14b33e601f79] - Node 138a6906-2a65-4132-bd8b-14b33e601f79[138a6906-2a65-4132-bd8b-14b33e601f79] running status set to false 
[INFO ] 2024-10-14 08:00:59.585 - [测试主从合并内嵌数组没有关联键(100)][138a6906-2a65-4132-bd8b-14b33e601f79] - Node 138a6906-2a65-4132-bd8b-14b33e601f79[138a6906-2a65-4132-bd8b-14b33e601f79] schema data cleaned 
[INFO ] 2024-10-14 08:00:59.585 - [测试主从合并内嵌数组没有关联键(100)][138a6906-2a65-4132-bd8b-14b33e601f79] - Node 138a6906-2a65-4132-bd8b-14b33e601f79[138a6906-2a65-4132-bd8b-14b33e601f79] monitor closed 
[INFO ] 2024-10-14 08:00:59.585 - [测试主从合并内嵌数组没有关联键(100)][138a6906-2a65-4132-bd8b-14b33e601f79] - Node 138a6906-2a65-4132-bd8b-14b33e601f79[138a6906-2a65-4132-bd8b-14b33e601f79] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:00:59.598 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-5a5718d9-0a40-43c8-99e6-1a666b20372f 
[INFO ] 2024-10-14 08:00:59.598 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-5a5718d9-0a40-43c8-99e6-1a666b20372f 
[INFO ] 2024-10-14 08:00:59.598 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:00:59.601 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:00:59.602 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:00:59.602 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 23 ms 
[INFO ] 2024-10-14 08:00:59.604 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:00:59.604 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:00:59.742 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:00:59.743 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:59.743 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:00:59.744 - [测试主从合并内嵌数组没有关联键(100)][d20a9161-3f4d-46e5-827c-c76f8dee70cb] - Node d20a9161-3f4d-46e5-827c-c76f8dee70cb[d20a9161-3f4d-46e5-827c-c76f8dee70cb] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:00:59.744 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:59.744 - [测试主从合并内嵌数组没有关联键(100)][d20a9161-3f4d-46e5-827c-c76f8dee70cb] - Node d20a9161-3f4d-46e5-827c-c76f8dee70cb[d20a9161-3f4d-46e5-827c-c76f8dee70cb] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:59.744 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:00:59.744 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:01:00.262 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:01:00.263 - [测试主从合并内嵌数组没有关联键(100)][0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] - Node 0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a[0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] running status set to false 
[INFO ] 2024-10-14 08:01:00.263 - [测试主从合并内嵌数组没有关联键(100)][0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] - Node 0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a[0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] schema data cleaned 
[INFO ] 2024-10-14 08:01:00.263 - [测试主从合并内嵌数组没有关联键(100)][0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] - Node 0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a[0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] monitor closed 
[INFO ] 2024-10-14 08:01:00.263 - [测试主从合并内嵌数组没有关联键(100)][0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] - Node 0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a[0f2eb1c0-eebb-49f2-9af5-ec5795adfa6a] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:01:00.276 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-5e96f646-0d5a-419e-b6c2-33df45ebf4d0 
[INFO ] 2024-10-14 08:01:00.276 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-5e96f646-0d5a-419e-b6c2-33df45ebf4d0 
[INFO ] 2024-10-14 08:01:00.276 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:01:00.278 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:01:00.278 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:01:00.279 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 28 ms 
[INFO ] 2024-10-14 08:01:00.280 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:00.280 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:00.354 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:00.354 - [测试主从合并内嵌数组没有关联键(100)][9a296bc4-83d6-4bbe-bc96-f9125603b2bb] - Node 9a296bc4-83d6-4bbe-bc96-f9125603b2bb[9a296bc4-83d6-4bbe-bc96-f9125603b2bb] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:01:00.354 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:00.355 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:00.355 - [测试主从合并内嵌数组没有关联键(100)][9a296bc4-83d6-4bbe-bc96-f9125603b2bb] - Node 9a296bc4-83d6-4bbe-bc96-f9125603b2bb[9a296bc4-83d6-4bbe-bc96-f9125603b2bb] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:00.355 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:00.355 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:00.356 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:01:04.075 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:04.075 - [测试主从合并内嵌数组没有关联键(100)][15af01fd-deb8-4477-a4e4-7ecebe1e1f06] - Node 15af01fd-deb8-4477-a4e4-7ecebe1e1f06[15af01fd-deb8-4477-a4e4-7ecebe1e1f06] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:01:04.076 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:04.076 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:04.076 - [测试主从合并内嵌数组没有关联键(100)][15af01fd-deb8-4477-a4e4-7ecebe1e1f06] - Node 15af01fd-deb8-4477-a4e4-7ecebe1e1f06[15af01fd-deb8-4477-a4e4-7ecebe1e1f06] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:04.076 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:04.076 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:01:04.329 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:01:04.363 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:04.363 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:04.363 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:01:04.364 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:01:04.566 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 36 ms 
[INFO ] 2024-10-14 08:01:05.349 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:01:05.351 - [测试主从合并内嵌数组没有关联键(100)][a9c329b8-0a29-48e5-be19-4ddd4d4d4514] - Node a9c329b8-0a29-48e5-be19-4ddd4d4d4514[a9c329b8-0a29-48e5-be19-4ddd4d4d4514] running status set to false 
[INFO ] 2024-10-14 08:01:05.374 - [测试主从合并内嵌数组没有关联键(100)][a9c329b8-0a29-48e5-be19-4ddd4d4d4514] - Node a9c329b8-0a29-48e5-be19-4ddd4d4d4514[a9c329b8-0a29-48e5-be19-4ddd4d4d4514] schema data cleaned 
[INFO ] 2024-10-14 08:01:05.384 - [测试主从合并内嵌数组没有关联键(100)][a9c329b8-0a29-48e5-be19-4ddd4d4d4514] - Node a9c329b8-0a29-48e5-be19-4ddd4d4d4514[a9c329b8-0a29-48e5-be19-4ddd4d4d4514] monitor closed 
[INFO ] 2024-10-14 08:01:05.384 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a94d3b7d-6717-4884-b53d-c9f3c1502f3f 
[INFO ] 2024-10-14 08:01:05.384 - [测试主从合并内嵌数组没有关联键(100)][a9c329b8-0a29-48e5-be19-4ddd4d4d4514] - Node a9c329b8-0a29-48e5-be19-4ddd4d4d4514[a9c329b8-0a29-48e5-be19-4ddd4d4d4514] close complete, cost 41 ms 
[INFO ] 2024-10-14 08:01:05.385 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a94d3b7d-6717-4884-b53d-c9f3c1502f3f 
[INFO ] 2024-10-14 08:01:05.385 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:01:05.388 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:01:05.388 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:01:05.392 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 52 ms 
[INFO ] 2024-10-14 08:01:05.392 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:05.392 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:05.523 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:05.525 - [测试主从合并内嵌数组没有关联键(100)][ce8bdc03-147f-4d5a-8173-591241815d7e] - Node ce8bdc03-147f-4d5a-8173-591241815d7e[ce8bdc03-147f-4d5a-8173-591241815d7e] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:01:05.525 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:05.525 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:05.525 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:05.525 - [测试主从合并内嵌数组没有关联键(100)][ce8bdc03-147f-4d5a-8173-591241815d7e] - Node ce8bdc03-147f-4d5a-8173-591241815d7e[ce8bdc03-147f-4d5a-8173-591241815d7e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:05.525 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:01:05.734 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:01:10.195 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:01:10.227 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:01:10.227 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:01:10.227 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:01:10.227 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:01:10.228 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 58 ms 
[INFO ] 2024-10-14 08:01:10.265 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:01:10.265 - [测试主从合并内嵌数组没有关联键(100)][7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] - Node 7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d[7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] running status set to false 
[INFO ] 2024-10-14 08:01:10.265 - [测试主从合并内嵌数组没有关联键(100)][7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] - Node 7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d[7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] schema data cleaned 
[INFO ] 2024-10-14 08:01:10.265 - [测试主从合并内嵌数组没有关联键(100)][7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] - Node 7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d[7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] monitor closed 
[INFO ] 2024-10-14 08:01:10.265 - [测试主从合并内嵌数组没有关联键(100)][7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] - Node 7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d[7e30b1ea-a9d1-4181-97a8-27ec1d4f2b2d] close complete, cost 6 ms 
[INFO ] 2024-10-14 08:01:10.294 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-5dc71260-f3f5-4861-9a36-f8ca9233c6ec 
[INFO ] 2024-10-14 08:01:10.295 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-5dc71260-f3f5-4861-9a36-f8ca9233c6ec 
[INFO ] 2024-10-14 08:01:10.295 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:01:10.296 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:01:10.296 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:01:10.299 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 39 ms 
[INFO ] 2024-10-14 08:01:10.300 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:10.300 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:10.506 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:11.066 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:01:11.068 - [测试主从合并内嵌数组没有关联键(100)][ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] - Node ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73[ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] running status set to false 
[INFO ] 2024-10-14 08:01:11.068 - [测试主从合并内嵌数组没有关联键(100)][ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] - Node ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73[ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] schema data cleaned 
[INFO ] 2024-10-14 08:01:11.068 - [测试主从合并内嵌数组没有关联键(100)][ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] - Node ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73[ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] monitor closed 
[INFO ] 2024-10-14 08:01:11.068 - [测试主从合并内嵌数组没有关联键(100)][ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] - Node ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73[ddc5f1e4-1757-4a2e-b1fb-7b1835a13a73] close complete, cost 7 ms 
[INFO ] 2024-10-14 08:01:11.091 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-216519d3-50a6-4b72-86aa-6fbec9e7998e 
[INFO ] 2024-10-14 08:01:11.091 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-216519d3-50a6-4b72-86aa-6fbec9e7998e 
[INFO ] 2024-10-14 08:01:11.091 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:01:11.095 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:01:11.095 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:01:11.095 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 40 ms 
[INFO ] 2024-10-14 08:01:11.099 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:11.099 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:11.307 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:15.351 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:01:15.390 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:15.391 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:15.391 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:01:15.391 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:01:15.594 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 49 ms 
[INFO ] 2024-10-14 08:01:17.305 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:01:17.308 - [测试主从合并内嵌数组没有关联键(100)][15af01fd-deb8-4477-a4e4-7ecebe1e1f06] - Node 15af01fd-deb8-4477-a4e4-7ecebe1e1f06[15af01fd-deb8-4477-a4e4-7ecebe1e1f06] running status set to false 
[INFO ] 2024-10-14 08:01:17.308 - [测试主从合并内嵌数组没有关联键(100)][15af01fd-deb8-4477-a4e4-7ecebe1e1f06] - Node 15af01fd-deb8-4477-a4e4-7ecebe1e1f06[15af01fd-deb8-4477-a4e4-7ecebe1e1f06] schema data cleaned 
[INFO ] 2024-10-14 08:01:17.308 - [测试主从合并内嵌数组没有关联键(100)][15af01fd-deb8-4477-a4e4-7ecebe1e1f06] - Node 15af01fd-deb8-4477-a4e4-7ecebe1e1f06[15af01fd-deb8-4477-a4e4-7ecebe1e1f06] monitor closed 
[INFO ] 2024-10-14 08:01:17.308 - [测试主从合并内嵌数组没有关联键(100)][15af01fd-deb8-4477-a4e4-7ecebe1e1f06] - Node 15af01fd-deb8-4477-a4e4-7ecebe1e1f06[15af01fd-deb8-4477-a4e4-7ecebe1e1f06] close complete, cost 7 ms 
[INFO ] 2024-10-14 08:01:17.361 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-e4e428ed-935a-4cfc-b0c1-35a6f31567f2 
[INFO ] 2024-10-14 08:01:17.361 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-e4e428ed-935a-4cfc-b0c1-35a6f31567f2 
[INFO ] 2024-10-14 08:01:17.364 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:01:17.364 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:01:17.364 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:01:17.365 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 72 ms 
[INFO ] 2024-10-14 08:01:17.368 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:17.368 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:17.369 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:17.679 - [测试主从合并内嵌数组没有关联键(100)][45981878-306c-44bc-b87e-1a1527ff1602] - Node 45981878-306c-44bc-b87e-1a1527ff1602[45981878-306c-44bc-b87e-1a1527ff1602] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:01:17.680 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:17.680 - [测试主从合并内嵌数组没有关联键(100)][45981878-306c-44bc-b87e-1a1527ff1602] - Node 45981878-306c-44bc-b87e-1a1527ff1602[45981878-306c-44bc-b87e-1a1527ff1602] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:17.680 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:17.682 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:01:17.682 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:01:17.886 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:01:20.268 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:01:20.318 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:20.325 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:20.325 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:01:20.325 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:01:20.537 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 80 ms 
[INFO ] 2024-10-14 08:01:21.146 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:01:21.181 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:21.181 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:21.181 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:01:21.182 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:01:21.182 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 46 ms 
[INFO ] 2024-10-14 08:01:23.797 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:01:23.840 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:23.841 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:01:23.841 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:01:23.842 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:01:23.843 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 71 ms 
[INFO ] 2024-10-14 08:01:24.806 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:01:24.806 - [测试主从合并内嵌数组没有关联键(100)][9a296bc4-83d6-4bbe-bc96-f9125603b2bb] - Node 9a296bc4-83d6-4bbe-bc96-f9125603b2bb[9a296bc4-83d6-4bbe-bc96-f9125603b2bb] running status set to false 
[INFO ] 2024-10-14 08:01:24.806 - [测试主从合并内嵌数组没有关联键(100)][9a296bc4-83d6-4bbe-bc96-f9125603b2bb] - Node 9a296bc4-83d6-4bbe-bc96-f9125603b2bb[9a296bc4-83d6-4bbe-bc96-f9125603b2bb] schema data cleaned 
[INFO ] 2024-10-14 08:01:24.806 - [测试主从合并内嵌数组没有关联键(100)][9a296bc4-83d6-4bbe-bc96-f9125603b2bb] - Node 9a296bc4-83d6-4bbe-bc96-f9125603b2bb[9a296bc4-83d6-4bbe-bc96-f9125603b2bb] monitor closed 
[INFO ] 2024-10-14 08:01:24.806 - [测试主从合并内嵌数组没有关联键(100)][9a296bc4-83d6-4bbe-bc96-f9125603b2bb] - Node 9a296bc4-83d6-4bbe-bc96-f9125603b2bb[9a296bc4-83d6-4bbe-bc96-f9125603b2bb] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:01:24.841 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-9f855e9d-c45f-481a-a1c7-fc1c235b8725 
[INFO ] 2024-10-14 08:01:24.841 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-9f855e9d-c45f-481a-a1c7-fc1c235b8725 
[INFO ] 2024-10-14 08:01:24.843 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:01:24.843 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:01:24.843 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:01:24.844 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 43 ms 
[INFO ] 2024-10-14 08:01:24.846 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:24.846 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:24.847 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:28.816 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:01:28.817 - [测试主从合并内嵌数组没有关联键(100)][d20a9161-3f4d-46e5-827c-c76f8dee70cb] - Node d20a9161-3f4d-46e5-827c-c76f8dee70cb[d20a9161-3f4d-46e5-827c-c76f8dee70cb] running status set to false 
[INFO ] 2024-10-14 08:01:28.817 - [测试主从合并内嵌数组没有关联键(100)][d20a9161-3f4d-46e5-827c-c76f8dee70cb] - Node d20a9161-3f4d-46e5-827c-c76f8dee70cb[d20a9161-3f4d-46e5-827c-c76f8dee70cb] schema data cleaned 
[INFO ] 2024-10-14 08:01:28.818 - [测试主从合并内嵌数组没有关联键(100)][d20a9161-3f4d-46e5-827c-c76f8dee70cb] - Node d20a9161-3f4d-46e5-827c-c76f8dee70cb[d20a9161-3f4d-46e5-827c-c76f8dee70cb] monitor closed 
[INFO ] 2024-10-14 08:01:28.818 - [测试主从合并内嵌数组没有关联键(100)][d20a9161-3f4d-46e5-827c-c76f8dee70cb] - Node d20a9161-3f4d-46e5-827c-c76f8dee70cb[d20a9161-3f4d-46e5-827c-c76f8dee70cb] close complete, cost 7 ms 
[INFO ] 2024-10-14 08:01:28.835 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-1a5bb9db-a0fd-4e81-bd16-6d48567cb1cf 
[INFO ] 2024-10-14 08:01:28.835 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-1a5bb9db-a0fd-4e81-bd16-6d48567cb1cf 
[INFO ] 2024-10-14 08:01:28.841 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:01:28.841 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:01:28.841 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:01:28.844 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 35 ms 
[INFO ] 2024-10-14 08:01:28.845 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:28.845 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:29.055 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:31.018 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:01:31.033 - [测试主从合并内嵌数组没有关联键(100)][45981878-306c-44bc-b87e-1a1527ff1602] - Node 45981878-306c-44bc-b87e-1a1527ff1602[45981878-306c-44bc-b87e-1a1527ff1602] running status set to false 
[INFO ] 2024-10-14 08:01:31.034 - [测试主从合并内嵌数组没有关联键(100)][45981878-306c-44bc-b87e-1a1527ff1602] - Node 45981878-306c-44bc-b87e-1a1527ff1602[45981878-306c-44bc-b87e-1a1527ff1602] schema data cleaned 
[INFO ] 2024-10-14 08:01:31.043 - [测试主从合并内嵌数组没有关联键(100)][45981878-306c-44bc-b87e-1a1527ff1602] - Node 45981878-306c-44bc-b87e-1a1527ff1602[45981878-306c-44bc-b87e-1a1527ff1602] monitor closed 
[INFO ] 2024-10-14 08:01:31.043 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-e9b30a62-9d88-4e5a-b3d7-9ee52e3d78d0 
[INFO ] 2024-10-14 08:01:31.043 - [测试主从合并内嵌数组没有关联键(100)][45981878-306c-44bc-b87e-1a1527ff1602] - Node 45981878-306c-44bc-b87e-1a1527ff1602[45981878-306c-44bc-b87e-1a1527ff1602] close complete, cost 39 ms 
[INFO ] 2024-10-14 08:01:31.044 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-e9b30a62-9d88-4e5a-b3d7-9ee52e3d78d0 
[INFO ] 2024-10-14 08:01:31.044 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:01:31.046 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:01:31.047 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:01:31.047 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 49 ms 
[INFO ] 2024-10-14 08:01:31.050 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:31.050 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:31.258 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:01:34.009 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:01:34.010 - [测试主从合并内嵌数组没有关联键(100)][ce8bdc03-147f-4d5a-8173-591241815d7e] - Node ce8bdc03-147f-4d5a-8173-591241815d7e[ce8bdc03-147f-4d5a-8173-591241815d7e] running status set to false 
[INFO ] 2024-10-14 08:01:34.010 - [测试主从合并内嵌数组没有关联键(100)][ce8bdc03-147f-4d5a-8173-591241815d7e] - Node ce8bdc03-147f-4d5a-8173-591241815d7e[ce8bdc03-147f-4d5a-8173-591241815d7e] schema data cleaned 
[INFO ] 2024-10-14 08:01:34.010 - [测试主从合并内嵌数组没有关联键(100)][ce8bdc03-147f-4d5a-8173-591241815d7e] - Node ce8bdc03-147f-4d5a-8173-591241815d7e[ce8bdc03-147f-4d5a-8173-591241815d7e] monitor closed 
[INFO ] 2024-10-14 08:01:34.010 - [测试主从合并内嵌数组没有关联键(100)][ce8bdc03-147f-4d5a-8173-591241815d7e] - Node ce8bdc03-147f-4d5a-8173-591241815d7e[ce8bdc03-147f-4d5a-8173-591241815d7e] close complete, cost 1 ms 
[INFO ] 2024-10-14 08:01:34.025 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-5040da0a-c775-4b3a-a934-910ab56441bf 
[INFO ] 2024-10-14 08:01:34.025 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-5040da0a-c775-4b3a-a934-910ab56441bf 
[INFO ] 2024-10-14 08:01:34.025 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:01:34.030 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:01:34.030 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:01:34.033 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 31 ms 
[INFO ] 2024-10-14 08:01:34.034 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:01:34.034 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:01:34.239 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:04:51.316 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:04:51.318 - [测试主从合并内嵌数组没有关联键(100)][a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] - Node a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee[a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:04:51.318 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:04:51.318 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:04:51.318 - [测试主从合并内嵌数组没有关联键(100)][a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] - Node a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee[a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:04:51.318 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:04:51.318 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:04:53.348 - [测试主从合并内嵌数组没有关联键(100)][ed6e9a8b-a110-41b9-bc36-5ab71b65d342] - Node ed6e9a8b-a110-41b9-bc36-5ab71b65d342[ed6e9a8b-a110-41b9-bc36-5ab71b65d342] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:04:53.348 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:04:53.348 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:04:53.348 - [测试主从合并内嵌数组没有关联键(100)][ed6e9a8b-a110-41b9-bc36-5ab71b65d342] - Node ed6e9a8b-a110-41b9-bc36-5ab71b65d342[ed6e9a8b-a110-41b9-bc36-5ab71b65d342] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:04:53.348 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:04:53.348 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:04:53.553 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:04:56.646 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:04:56.681 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:04:56.681 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:04:56.681 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:04:56.681 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:04:56.889 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 37 ms 
[INFO ] 2024-10-14 08:05:01.914 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:05:01.938 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:05:01.938 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:05:01.938 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:05:01.940 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:05:01.943 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 38 ms 
[INFO ] 2024-10-14 08:05:01.983 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:05:01.984 - [测试主从合并内嵌数组没有关联键(100)][a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] - Node a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee[a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] running status set to false 
[INFO ] 2024-10-14 08:05:01.985 - [测试主从合并内嵌数组没有关联键(100)][a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] - Node a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee[a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] schema data cleaned 
[INFO ] 2024-10-14 08:05:01.985 - [测试主从合并内嵌数组没有关联键(100)][a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] - Node a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee[a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] monitor closed 
[INFO ] 2024-10-14 08:05:01.994 - [测试主从合并内嵌数组没有关联键(100)][a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] - Node a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee[a2f123a8-2b84-4b3f-9e80-6c3ae317f0ee] close complete, cost 14 ms 
[INFO ] 2024-10-14 08:05:01.994 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-b4317fcc-a218-4ce4-9941-0c345f048c67 
[INFO ] 2024-10-14 08:05:01.995 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-b4317fcc-a218-4ce4-9941-0c345f048c67 
[INFO ] 2024-10-14 08:05:01.995 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:05:01.998 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:05:01.998 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:05:02.001 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 30 ms 
[INFO ] 2024-10-14 08:05:02.001 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:05:02.001 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:05:02.002 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:05:02.236 - [测试主从合并内嵌数组没有关联键(100)][775e0a08-ee5b-4e2a-b69b-e6685c281fd5] - Node 775e0a08-ee5b-4e2a-b69b-e6685c281fd5[775e0a08-ee5b-4e2a-b69b-e6685c281fd5] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:05:02.239 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:05:02.239 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:05:02.239 - [测试主从合并内嵌数组没有关联键(100)][775e0a08-ee5b-4e2a-b69b-e6685c281fd5] - Node 775e0a08-ee5b-4e2a-b69b-e6685c281fd5[775e0a08-ee5b-4e2a-b69b-e6685c281fd5] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:05:02.239 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:05:02.239 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:05:02.455 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:05:07.966 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:05:07.967 - [测试主从合并内嵌数组没有关联键(100)][ed6e9a8b-a110-41b9-bc36-5ab71b65d342] - Node ed6e9a8b-a110-41b9-bc36-5ab71b65d342[ed6e9a8b-a110-41b9-bc36-5ab71b65d342] running status set to false 
[INFO ] 2024-10-14 08:05:07.968 - [测试主从合并内嵌数组没有关联键(100)][ed6e9a8b-a110-41b9-bc36-5ab71b65d342] - Node ed6e9a8b-a110-41b9-bc36-5ab71b65d342[ed6e9a8b-a110-41b9-bc36-5ab71b65d342] schema data cleaned 
[INFO ] 2024-10-14 08:05:07.968 - [测试主从合并内嵌数组没有关联键(100)][ed6e9a8b-a110-41b9-bc36-5ab71b65d342] - Node ed6e9a8b-a110-41b9-bc36-5ab71b65d342[ed6e9a8b-a110-41b9-bc36-5ab71b65d342] monitor closed 
[INFO ] 2024-10-14 08:05:07.968 - [测试主从合并内嵌数组没有关联键(100)][ed6e9a8b-a110-41b9-bc36-5ab71b65d342] - Node ed6e9a8b-a110-41b9-bc36-5ab71b65d342[ed6e9a8b-a110-41b9-bc36-5ab71b65d342] close complete, cost 4 ms 
[INFO ] 2024-10-14 08:05:07.989 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-1e9406e0-9532-4308-8937-9bd0ef460f53 
[INFO ] 2024-10-14 08:05:07.989 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-1e9406e0-9532-4308-8937-9bd0ef460f53 
[INFO ] 2024-10-14 08:05:07.992 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:05:07.992 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:05:07.993 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:05:07.993 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 35 ms 
[INFO ] 2024-10-14 08:05:07.995 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:05:07.996 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:05:07.996 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:05:08.223 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:05:08.223 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:05:08.225 - [测试主从合并内嵌数组没有关联键(100)][0abcc8ce-8954-404b-a0bd-498f88e74cf9] - Node 0abcc8ce-8954-404b-a0bd-498f88e74cf9[0abcc8ce-8954-404b-a0bd-498f88e74cf9] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:05:08.225 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:05:08.225 - [测试主从合并内嵌数组没有关联键(100)][0abcc8ce-8954-404b-a0bd-498f88e74cf9] - Node 0abcc8ce-8954-404b-a0bd-498f88e74cf9[0abcc8ce-8954-404b-a0bd-498f88e74cf9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:05:08.225 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:05:08.225 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:05:13.236 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:05:13.237 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:05:13.237 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:05:13.237 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:05:13.237 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:05:13.385 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 39 ms 
[INFO ] 2024-10-14 08:05:13.385 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:05:13.405 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:05:13.406 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:05:13.406 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:05:13.406 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:05:13.408 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 27 ms 
[INFO ] 2024-10-14 08:05:18.832 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:05:18.841 - [测试主从合并内嵌数组没有关联键(100)][775e0a08-ee5b-4e2a-b69b-e6685c281fd5] - Node 775e0a08-ee5b-4e2a-b69b-e6685c281fd5[775e0a08-ee5b-4e2a-b69b-e6685c281fd5] running status set to false 
[INFO ] 2024-10-14 08:05:18.852 - [测试主从合并内嵌数组没有关联键(100)][775e0a08-ee5b-4e2a-b69b-e6685c281fd5] - Node 775e0a08-ee5b-4e2a-b69b-e6685c281fd5[775e0a08-ee5b-4e2a-b69b-e6685c281fd5] schema data cleaned 
[INFO ] 2024-10-14 08:05:18.853 - [测试主从合并内嵌数组没有关联键(100)][775e0a08-ee5b-4e2a-b69b-e6685c281fd5] - Node 775e0a08-ee5b-4e2a-b69b-e6685c281fd5[775e0a08-ee5b-4e2a-b69b-e6685c281fd5] monitor closed 
[INFO ] 2024-10-14 08:05:18.853 - [测试主从合并内嵌数组没有关联键(100)][775e0a08-ee5b-4e2a-b69b-e6685c281fd5] - Node 775e0a08-ee5b-4e2a-b69b-e6685c281fd5[775e0a08-ee5b-4e2a-b69b-e6685c281fd5] close complete, cost 4 ms 
[INFO ] 2024-10-14 08:05:18.863 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-d98a260c-b36e-414d-9d70-4e9252315f44 
[INFO ] 2024-10-14 08:05:18.863 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-d98a260c-b36e-414d-9d70-4e9252315f44 
[INFO ] 2024-10-14 08:05:18.863 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:05:18.867 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:05:18.867 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:05:18.870 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 47 ms 
[INFO ] 2024-10-14 08:05:18.871 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:05:18.871 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:05:19.078 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:05:23.999 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:05:24.031 - [测试主从合并内嵌数组没有关联键(100)][0abcc8ce-8954-404b-a0bd-498f88e74cf9] - Node 0abcc8ce-8954-404b-a0bd-498f88e74cf9[0abcc8ce-8954-404b-a0bd-498f88e74cf9] running status set to false 
[INFO ] 2024-10-14 08:05:24.032 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-f4d7c15a-3ac0-4291-b361-b2342adf1094 
[INFO ] 2024-10-14 08:05:24.032 - [测试主从合并内嵌数组没有关联键(100)][0abcc8ce-8954-404b-a0bd-498f88e74cf9] - Node 0abcc8ce-8954-404b-a0bd-498f88e74cf9[0abcc8ce-8954-404b-a0bd-498f88e74cf9] schema data cleaned 
[INFO ] 2024-10-14 08:05:24.032 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-f4d7c15a-3ac0-4291-b361-b2342adf1094 
[INFO ] 2024-10-14 08:05:24.032 - [测试主从合并内嵌数组没有关联键(100)][0abcc8ce-8954-404b-a0bd-498f88e74cf9] - Node 0abcc8ce-8954-404b-a0bd-498f88e74cf9[0abcc8ce-8954-404b-a0bd-498f88e74cf9] monitor closed 
[INFO ] 2024-10-14 08:05:24.033 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:05:24.033 - [测试主从合并内嵌数组没有关联键(100)][0abcc8ce-8954-404b-a0bd-498f88e74cf9] - Node 0abcc8ce-8954-404b-a0bd-498f88e74cf9[0abcc8ce-8954-404b-a0bd-498f88e74cf9] close complete, cost 42 ms 
[INFO ] 2024-10-14 08:05:24.036 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:05:24.036 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:05:24.036 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 52 ms 
[INFO ] 2024-10-14 08:05:24.039 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:05:24.039 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:05:24.039 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:09:32.398 - [测试主从合并内嵌数组没有关联键(100)][96ba2f59-5474-40ed-997a-4e0cc02da5b8] - Node 96ba2f59-5474-40ed-997a-4e0cc02da5b8[96ba2f59-5474-40ed-997a-4e0cc02da5b8] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:09:32.399 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:32.399 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:32.399 - [测试主从合并内嵌数组没有关联键(100)][96ba2f59-5474-40ed-997a-4e0cc02da5b8] - Node 96ba2f59-5474-40ed-997a-4e0cc02da5b8[96ba2f59-5474-40ed-997a-4e0cc02da5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:09:32.399 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:09:32.400 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:09:32.400 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:09:32.436 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:32.436 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:32.436 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:09:32.436 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:09:32.436 - [测试主从合并内嵌数组没有关联键(100)][6de30cfd-d602-43fa-a031-7d3d6f32ae59] - Node 6de30cfd-d602-43fa-a031-7d3d6f32ae59[6de30cfd-d602-43fa-a031-7d3d6f32ae59] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:09:32.436 - [测试主从合并内嵌数组没有关联键(100)][6de30cfd-d602-43fa-a031-7d3d6f32ae59] - Node 6de30cfd-d602-43fa-a031-7d3d6f32ae59[6de30cfd-d602-43fa-a031-7d3d6f32ae59] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:09:32.637 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:09:37.733 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:09:37.767 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:09:37.767 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:09:37.768 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:09:37.768 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:09:37.768 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 35 ms 
[INFO ] 2024-10-14 08:09:43.232 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:09:43.255 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:09:43.255 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:09:43.255 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:09:43.255 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:09:43.308 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 33 ms 
[INFO ] 2024-10-14 08:09:43.308 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:09:43.309 - [测试主从合并内嵌数组没有关联键(100)][96ba2f59-5474-40ed-997a-4e0cc02da5b8] - Node 96ba2f59-5474-40ed-997a-4e0cc02da5b8[96ba2f59-5474-40ed-997a-4e0cc02da5b8] running status set to false 
[INFO ] 2024-10-14 08:09:43.309 - [测试主从合并内嵌数组没有关联键(100)][96ba2f59-5474-40ed-997a-4e0cc02da5b8] - Node 96ba2f59-5474-40ed-997a-4e0cc02da5b8[96ba2f59-5474-40ed-997a-4e0cc02da5b8] schema data cleaned 
[INFO ] 2024-10-14 08:09:43.309 - [测试主从合并内嵌数组没有关联键(100)][96ba2f59-5474-40ed-997a-4e0cc02da5b8] - Node 96ba2f59-5474-40ed-997a-4e0cc02da5b8[96ba2f59-5474-40ed-997a-4e0cc02da5b8] monitor closed 
[INFO ] 2024-10-14 08:09:43.326 - [测试主从合并内嵌数组没有关联键(100)][96ba2f59-5474-40ed-997a-4e0cc02da5b8] - Node 96ba2f59-5474-40ed-997a-4e0cc02da5b8[96ba2f59-5474-40ed-997a-4e0cc02da5b8] close complete, cost 0 ms 
[INFO ] 2024-10-14 08:09:43.327 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-99dd3c13-9443-43b3-bd1a-913dad864ce3 
[INFO ] 2024-10-14 08:09:43.327 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-99dd3c13-9443-43b3-bd1a-913dad864ce3 
[INFO ] 2024-10-14 08:09:43.329 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:09:43.330 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:09:43.330 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:09:43.330 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 25 ms 
[INFO ] 2024-10-14 08:09:43.333 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:09:43.333 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:09:43.481 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:09:43.484 - [测试主从合并内嵌数组没有关联键(100)][237a7591-5c29-4510-a759-844ac37a5e6a] - Node 237a7591-5c29-4510-a759-844ac37a5e6a[237a7591-5c29-4510-a759-844ac37a5e6a] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:09:43.484 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:43.484 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:43.484 - [测试主从合并内嵌数组没有关联键(100)][237a7591-5c29-4510-a759-844ac37a5e6a] - Node 237a7591-5c29-4510-a759-844ac37a5e6a[237a7591-5c29-4510-a759-844ac37a5e6a] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:09:43.484 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:09:43.484 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:09:43.689 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:09:48.896 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:09:48.898 - [测试主从合并内嵌数组没有关联键(100)][6de30cfd-d602-43fa-a031-7d3d6f32ae59] - Node 6de30cfd-d602-43fa-a031-7d3d6f32ae59[6de30cfd-d602-43fa-a031-7d3d6f32ae59] running status set to false 
[INFO ] 2024-10-14 08:09:48.898 - [测试主从合并内嵌数组没有关联键(100)][6de30cfd-d602-43fa-a031-7d3d6f32ae59] - Node 6de30cfd-d602-43fa-a031-7d3d6f32ae59[6de30cfd-d602-43fa-a031-7d3d6f32ae59] schema data cleaned 
[INFO ] 2024-10-14 08:09:48.898 - [测试主从合并内嵌数组没有关联键(100)][6de30cfd-d602-43fa-a031-7d3d6f32ae59] - Node 6de30cfd-d602-43fa-a031-7d3d6f32ae59[6de30cfd-d602-43fa-a031-7d3d6f32ae59] monitor closed 
[INFO ] 2024-10-14 08:09:48.898 - [测试主从合并内嵌数组没有关联键(100)][6de30cfd-d602-43fa-a031-7d3d6f32ae59] - Node 6de30cfd-d602-43fa-a031-7d3d6f32ae59[6de30cfd-d602-43fa-a031-7d3d6f32ae59] close complete, cost 6 ms 
[INFO ] 2024-10-14 08:09:48.921 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-d45ac893-ab99-4ea8-8ea7-f2fd7f2e2d5f 
[INFO ] 2024-10-14 08:09:48.921 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-d45ac893-ab99-4ea8-8ea7-f2fd7f2e2d5f 
[INFO ] 2024-10-14 08:09:48.921 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:09:48.923 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:09:48.925 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:09:48.925 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 37 ms 
[INFO ] 2024-10-14 08:09:48.928 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:09:48.928 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:09:49.077 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:09:49.081 - [测试主从合并内嵌数组没有关联键(100)][4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] - Node 4a26f0c7-335b-4e02-ad4d-c40cd5f913aa[4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:09:49.081 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:49.081 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:09:49.081 - [测试主从合并内嵌数组没有关联键(100)][4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] - Node 4a26f0c7-335b-4e02-ad4d-c40cd5f913aa[4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:09:49.081 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:09:49.081 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:09:49.081 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:09:54.115 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:09:54.164 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:09:54.164 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:09:54.164 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:09:54.164 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:09:54.165 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 62 ms 
[INFO ] 2024-10-14 08:09:54.253 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:09:54.272 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:09:54.272 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:09:54.272 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:09:54.272 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:09:54.272 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 21 ms 
[INFO ] 2024-10-14 08:09:59.967 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:09:59.969 - [测试主从合并内嵌数组没有关联键(100)][237a7591-5c29-4510-a759-844ac37a5e6a] - Node 237a7591-5c29-4510-a759-844ac37a5e6a[237a7591-5c29-4510-a759-844ac37a5e6a] running status set to false 
[INFO ] 2024-10-14 08:09:59.969 - [测试主从合并内嵌数组没有关联键(100)][237a7591-5c29-4510-a759-844ac37a5e6a] - Node 237a7591-5c29-4510-a759-844ac37a5e6a[237a7591-5c29-4510-a759-844ac37a5e6a] schema data cleaned 
[INFO ] 2024-10-14 08:09:59.969 - [测试主从合并内嵌数组没有关联键(100)][237a7591-5c29-4510-a759-844ac37a5e6a] - Node 237a7591-5c29-4510-a759-844ac37a5e6a[237a7591-5c29-4510-a759-844ac37a5e6a] monitor closed 
[INFO ] 2024-10-14 08:09:59.969 - [测试主从合并内嵌数组没有关联键(100)][237a7591-5c29-4510-a759-844ac37a5e6a] - Node 237a7591-5c29-4510-a759-844ac37a5e6a[237a7591-5c29-4510-a759-844ac37a5e6a] close complete, cost 8 ms 
[INFO ] 2024-10-14 08:10:00.001 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-1aa987ac-bea3-49d3-b82d-924c89802e81 
[INFO ] 2024-10-14 08:10:00.002 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-1aa987ac-bea3-49d3-b82d-924c89802e81 
[INFO ] 2024-10-14 08:10:00.002 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:10:00.002 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:10:00.002 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:10:00.002 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 48 ms 
[INFO ] 2024-10-14 08:10:00.004 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:10:00.004 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:10:00.004 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:10:05.085 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:10:05.086 - [测试主从合并内嵌数组没有关联键(100)][4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] - Node 4a26f0c7-335b-4e02-ad4d-c40cd5f913aa[4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] running status set to false 
[INFO ] 2024-10-14 08:10:05.086 - [测试主从合并内嵌数组没有关联键(100)][4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] - Node 4a26f0c7-335b-4e02-ad4d-c40cd5f913aa[4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] schema data cleaned 
[INFO ] 2024-10-14 08:10:05.086 - [测试主从合并内嵌数组没有关联键(100)][4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] - Node 4a26f0c7-335b-4e02-ad4d-c40cd5f913aa[4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] monitor closed 
[INFO ] 2024-10-14 08:10:05.086 - [测试主从合并内嵌数组没有关联键(100)][4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] - Node 4a26f0c7-335b-4e02-ad4d-c40cd5f913aa[4a26f0c7-335b-4e02-ad4d-c40cd5f913aa] close complete, cost 4 ms 
[INFO ] 2024-10-14 08:10:05.109 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-6e0674cf-f94c-4357-8d13-465fd4c13f9d 
[INFO ] 2024-10-14 08:10:05.109 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-6e0674cf-f94c-4357-8d13-465fd4c13f9d 
[INFO ] 2024-10-14 08:10:05.109 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:10:05.112 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:10:05.113 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:10:05.113 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 38 ms 
[INFO ] 2024-10-14 08:10:05.115 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:10:05.116 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:10:05.116 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:23:48.690 - [测试主从合并内嵌数组没有关联键(100)][41e464fc-4e49-40ef-96f8-f49971c530ee] - Node 41e464fc-4e49-40ef-96f8-f49971c530ee[41e464fc-4e49-40ef-96f8-f49971c530ee] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:23:48.691 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:23:48.691 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:23:48.691 - [测试主从合并内嵌数组没有关联键(100)][41e464fc-4e49-40ef-96f8-f49971c530ee] - Node 41e464fc-4e49-40ef-96f8-f49971c530ee[41e464fc-4e49-40ef-96f8-f49971c530ee] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:48.692 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:48.692 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:48.694 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:23:48.839 - [测试主从合并内嵌数组没有关联键(100)][ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] - Node ad8fe1a7-824b-4e31-b6f2-121915c8f8fa[ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:23:48.839 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:23:48.839 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:23:48.839 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:48.839 - [测试主从合并内嵌数组没有关联键(100)][ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] - Node ad8fe1a7-824b-4e31-b6f2-121915c8f8fa[ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:48.839 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:48.839 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:23:54.196 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:23:54.196 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:23:54.198 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:23:54.198 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:23:54.198 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:23:54.291 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 69 ms 
[INFO ] 2024-10-14 08:23:54.291 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:23:54.291 - [测试主从合并内嵌数组没有关联键(100)][3c4db084-24b3-473b-a004-cdce9ed97fd5] - Node 3c4db084-24b3-473b-a004-cdce9ed97fd5[3c4db084-24b3-473b-a004-cdce9ed97fd5] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:23:54.292 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:23:54.292 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:23:54.292 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:54.292 - [测试主从合并内嵌数组没有关联键(100)][3c4db084-24b3-473b-a004-cdce9ed97fd5] - Node 3c4db084-24b3-473b-a004-cdce9ed97fd5[3c4db084-24b3-473b-a004-cdce9ed97fd5] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:23:54.292 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:23:59.487 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:23:59.488 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:23:59.514 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:23:59.514 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:23:59.514 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:23:59.514 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:23:59.515 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 42 ms 
[ERROR] 2024-10-14 08:23:59.717 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-10-14 08:24:02.165 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:24:02.165 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: null 
[INFO ] 2024-10-14 08:24:02.165 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: null 
[INFO ] 2024-10-14 08:24:02.165 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:24:02.165 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:24:02.165 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:24:02.168 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:24:02.168 - [测试主从合并内嵌数组没有关联键(100)][3c4db084-24b3-473b-a004-cdce9ed97fd5] - Node 3c4db084-24b3-473b-a004-cdce9ed97fd5[3c4db084-24b3-473b-a004-cdce9ed97fd5] running status set to false 
[INFO ] 2024-10-14 08:24:02.168 - [测试主从合并内嵌数组没有关联键(100)][3c4db084-24b3-473b-a004-cdce9ed97fd5] - Node 3c4db084-24b3-473b-a004-cdce9ed97fd5[3c4db084-24b3-473b-a004-cdce9ed97fd5] schema data cleaned 
[INFO ] 2024-10-14 08:24:02.168 - [测试主从合并内嵌数组没有关联键(100)][3c4db084-24b3-473b-a004-cdce9ed97fd5] - Node 3c4db084-24b3-473b-a004-cdce9ed97fd5[3c4db084-24b3-473b-a004-cdce9ed97fd5] monitor closed 
[INFO ] 2024-10-14 08:24:02.168 - [测试主从合并内嵌数组没有关联键(100)][3c4db084-24b3-473b-a004-cdce9ed97fd5] - Node 3c4db084-24b3-473b-a004-cdce9ed97fd5[3c4db084-24b3-473b-a004-cdce9ed97fd5] close complete, cost 0 ms 
[INFO ] 2024-10-14 08:24:02.168 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:24:02.168 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:24:02.169 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 0 ms 
[INFO ] 2024-10-14 08:24:02.170 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:24:02.170 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:24:02.171 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:24:02.319 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:24:02.320 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:24:02.320 - [测试主从合并内嵌数组没有关联键(100)][34552b80-2cdb-4e30-afb8-9af5120520ed] - Node 34552b80-2cdb-4e30-afb8-9af5120520ed[34552b80-2cdb-4e30-afb8-9af5120520ed] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:24:02.320 - [测试主从合并内嵌数组没有关联键(100)][34552b80-2cdb-4e30-afb8-9af5120520ed] - Node 34552b80-2cdb-4e30-afb8-9af5120520ed[34552b80-2cdb-4e30-afb8-9af5120520ed] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:24:02.320 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:24:02.320 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:24:02.321 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:24:05.667 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:24:05.668 - [测试主从合并内嵌数组没有关联键(100)][41e464fc-4e49-40ef-96f8-f49971c530ee] - Node 41e464fc-4e49-40ef-96f8-f49971c530ee[41e464fc-4e49-40ef-96f8-f49971c530ee] running status set to false 
[INFO ] 2024-10-14 08:24:05.668 - [测试主从合并内嵌数组没有关联键(100)][41e464fc-4e49-40ef-96f8-f49971c530ee] - Node 41e464fc-4e49-40ef-96f8-f49971c530ee[41e464fc-4e49-40ef-96f8-f49971c530ee] schema data cleaned 
[INFO ] 2024-10-14 08:24:05.668 - [测试主从合并内嵌数组没有关联键(100)][41e464fc-4e49-40ef-96f8-f49971c530ee] - Node 41e464fc-4e49-40ef-96f8-f49971c530ee[41e464fc-4e49-40ef-96f8-f49971c530ee] monitor closed 
[INFO ] 2024-10-14 08:24:05.668 - [测试主从合并内嵌数组没有关联键(100)][41e464fc-4e49-40ef-96f8-f49971c530ee] - Node 41e464fc-4e49-40ef-96f8-f49971c530ee[41e464fc-4e49-40ef-96f8-f49971c530ee] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:24:05.694 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-8e72a458-e763-4192-a052-00e48ac2c656 
[INFO ] 2024-10-14 08:24:05.694 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-8e72a458-e763-4192-a052-00e48ac2c656 
[INFO ] 2024-10-14 08:24:05.694 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:24:05.696 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:24:05.696 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:24:05.698 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 34 ms 
[INFO ] 2024-10-14 08:24:05.699 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:24:05.699 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:24:05.829 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:24:05.829 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:24:05.829 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:24:05.830 - [测试主从合并内嵌数组没有关联键(100)][0802b20a-babf-4e49-b0f2-9a517867c6eb] - Node 0802b20a-babf-4e49-b0f2-9a517867c6eb[0802b20a-babf-4e49-b0f2-9a517867c6eb] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:24:05.830 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:24:05.830 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:24:05.830 - [测试主从合并内嵌数组没有关联键(100)][0802b20a-babf-4e49-b0f2-9a517867c6eb] - Node 0802b20a-babf-4e49-b0f2-9a517867c6eb[0802b20a-babf-4e49-b0f2-9a517867c6eb] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:24:05.835 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:24:07.945 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:24:07.946 - [测试主从合并内嵌数组没有关联键(100)][ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] - Node ad8fe1a7-824b-4e31-b6f2-121915c8f8fa[ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] running status set to false 
[INFO ] 2024-10-14 08:24:07.946 - [测试主从合并内嵌数组没有关联键(100)][ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] - Node ad8fe1a7-824b-4e31-b6f2-121915c8f8fa[ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] schema data cleaned 
[INFO ] 2024-10-14 08:24:07.946 - [测试主从合并内嵌数组没有关联键(100)][ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] - Node ad8fe1a7-824b-4e31-b6f2-121915c8f8fa[ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] monitor closed 
[INFO ] 2024-10-14 08:24:07.946 - [测试主从合并内嵌数组没有关联键(100)][ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] - Node ad8fe1a7-824b-4e31-b6f2-121915c8f8fa[ad8fe1a7-824b-4e31-b6f2-121915c8f8fa] close complete, cost 7 ms 
[INFO ] 2024-10-14 08:24:07.971 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-312e6d0f-d3d7-4419-9369-bdcb5fd12aa8 
[INFO ] 2024-10-14 08:24:07.971 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-312e6d0f-d3d7-4419-9369-bdcb5fd12aa8 
[INFO ] 2024-10-14 08:24:07.972 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:24:07.975 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:24:07.976 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:24:07.977 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 41 ms 
[INFO ] 2024-10-14 08:24:07.981 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:24:07.981 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:24:08.137 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:24:08.138 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:24:08.138 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:24:08.138 - [测试主从合并内嵌数组没有关联键(100)][6fd421eb-52c2-41a7-abbc-07400234ee9d] - Node 6fd421eb-52c2-41a7-abbc-07400234ee9d[6fd421eb-52c2-41a7-abbc-07400234ee9d] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:24:08.138 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:24:08.138 - [测试主从合并内嵌数组没有关联键(100)][6fd421eb-52c2-41a7-abbc-07400234ee9d] - Node 6fd421eb-52c2-41a7-abbc-07400234ee9d[6fd421eb-52c2-41a7-abbc-07400234ee9d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:24:08.139 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:24:08.139 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:24:13.173 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:24:13.174 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:24:13.174 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:24:13.174 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:24:13.174 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:24:13.174 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 46 ms 
[INFO ] 2024-10-14 08:24:13.412 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:24:13.412 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:24:13.412 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:24:13.412 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:24:13.412 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:24:13.413 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 29 ms 
[INFO ] 2024-10-14 08:24:18.529 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:24:18.557 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:24:18.559 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:24:18.559 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:24:18.559 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:24:18.559 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 40 ms 
[INFO ] 2024-10-14 08:24:18.645 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:24:18.645 - [测试主从合并内嵌数组没有关联键(100)][6fd421eb-52c2-41a7-abbc-07400234ee9d] - Node 6fd421eb-52c2-41a7-abbc-07400234ee9d[6fd421eb-52c2-41a7-abbc-07400234ee9d] running status set to false 
[INFO ] 2024-10-14 08:24:18.645 - [测试主从合并内嵌数组没有关联键(100)][6fd421eb-52c2-41a7-abbc-07400234ee9d] - Node 6fd421eb-52c2-41a7-abbc-07400234ee9d[6fd421eb-52c2-41a7-abbc-07400234ee9d] schema data cleaned 
[INFO ] 2024-10-14 08:24:18.645 - [测试主从合并内嵌数组没有关联键(100)][6fd421eb-52c2-41a7-abbc-07400234ee9d] - Node 6fd421eb-52c2-41a7-abbc-07400234ee9d[6fd421eb-52c2-41a7-abbc-07400234ee9d] monitor closed 
[INFO ] 2024-10-14 08:24:18.645 - [测试主从合并内嵌数组没有关联键(100)][6fd421eb-52c2-41a7-abbc-07400234ee9d] - Node 6fd421eb-52c2-41a7-abbc-07400234ee9d[6fd421eb-52c2-41a7-abbc-07400234ee9d] close complete, cost 7 ms 
[INFO ] 2024-10-14 08:24:18.652 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-a39e6346-24d7-4273-bf95-34fe88f44824 
[INFO ] 2024-10-14 08:24:18.653 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-a39e6346-24d7-4273-bf95-34fe88f44824 
[INFO ] 2024-10-14 08:24:18.653 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:24:18.656 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:24:18.656 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:24:18.657 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 30 ms 
[INFO ] 2024-10-14 08:24:18.662 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:24:18.662 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:24:18.867 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:24:20.825 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:24:20.828 - [测试主从合并内嵌数组没有关联键(100)][0802b20a-babf-4e49-b0f2-9a517867c6eb] - Node 0802b20a-babf-4e49-b0f2-9a517867c6eb[0802b20a-babf-4e49-b0f2-9a517867c6eb] running status set to false 
[INFO ] 2024-10-14 08:24:20.828 - [测试主从合并内嵌数组没有关联键(100)][0802b20a-babf-4e49-b0f2-9a517867c6eb] - Node 0802b20a-babf-4e49-b0f2-9a517867c6eb[0802b20a-babf-4e49-b0f2-9a517867c6eb] schema data cleaned 
[INFO ] 2024-10-14 08:24:20.828 - [测试主从合并内嵌数组没有关联键(100)][0802b20a-babf-4e49-b0f2-9a517867c6eb] - Node 0802b20a-babf-4e49-b0f2-9a517867c6eb[0802b20a-babf-4e49-b0f2-9a517867c6eb] monitor closed 
[INFO ] 2024-10-14 08:24:20.828 - [测试主从合并内嵌数组没有关联键(100)][0802b20a-babf-4e49-b0f2-9a517867c6eb] - Node 0802b20a-babf-4e49-b0f2-9a517867c6eb[0802b20a-babf-4e49-b0f2-9a517867c6eb] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:24:20.856 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-d36cbc3d-a6ae-4902-9089-336aa18c92c0 
[INFO ] 2024-10-14 08:24:20.856 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-d36cbc3d-a6ae-4902-9089-336aa18c92c0 
[INFO ] 2024-10-14 08:24:20.856 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:24:20.858 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:24:20.859 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:24:20.859 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 45 ms 
[INFO ] 2024-10-14 08:24:20.862 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:24:20.863 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:24:20.863 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:24:22.590 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:24:22.592 - [测试主从合并内嵌数组没有关联键(100)][34552b80-2cdb-4e30-afb8-9af5120520ed] - Node 34552b80-2cdb-4e30-afb8-9af5120520ed[34552b80-2cdb-4e30-afb8-9af5120520ed] running status set to false 
[INFO ] 2024-10-14 08:24:22.592 - [测试主从合并内嵌数组没有关联键(100)][34552b80-2cdb-4e30-afb8-9af5120520ed] - Node 34552b80-2cdb-4e30-afb8-9af5120520ed[34552b80-2cdb-4e30-afb8-9af5120520ed] schema data cleaned 
[INFO ] 2024-10-14 08:24:22.592 - [测试主从合并内嵌数组没有关联键(100)][34552b80-2cdb-4e30-afb8-9af5120520ed] - Node 34552b80-2cdb-4e30-afb8-9af5120520ed[34552b80-2cdb-4e30-afb8-9af5120520ed] monitor closed 
[INFO ] 2024-10-14 08:24:22.592 - [测试主从合并内嵌数组没有关联键(100)][34552b80-2cdb-4e30-afb8-9af5120520ed] - Node 34552b80-2cdb-4e30-afb8-9af5120520ed[34552b80-2cdb-4e30-afb8-9af5120520ed] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:24:22.605 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-cc0a2663-d4c9-4a1e-b7fb-7d8a1f895215 
[INFO ] 2024-10-14 08:24:22.605 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-cc0a2663-d4c9-4a1e-b7fb-7d8a1f895215 
[INFO ] 2024-10-14 08:24:22.605 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:24:22.606 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:24:22.606 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:24:22.608 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 26 ms 
[INFO ] 2024-10-14 08:24:22.608 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:24:22.608 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:24:22.608 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:45:17.229 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:45:17.230 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:45:17.231 - [测试主从合并内嵌数组没有关联键(100)][f0761b93-7217-48fc-b8f2-978298b1a230] - Node f0761b93-7217-48fc-b8f2-978298b1a230[f0761b93-7217-48fc-b8f2-978298b1a230] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:45:17.231 - [测试主从合并内嵌数组没有关联键(100)][f0761b93-7217-48fc-b8f2-978298b1a230] - Node f0761b93-7217-48fc-b8f2-978298b1a230[f0761b93-7217-48fc-b8f2-978298b1a230] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:45:17.231 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:45:17.231 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:45:17.231 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:45:22.687 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:45:22.687 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:45:22.688 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:45:22.688 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:45:22.688 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:45:22.892 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 21 ms 
[INFO ] 2024-10-14 08:45:28.252 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:45:28.254 - [测试主从合并内嵌数组没有关联键(100)][f0761b93-7217-48fc-b8f2-978298b1a230] - Node f0761b93-7217-48fc-b8f2-978298b1a230[f0761b93-7217-48fc-b8f2-978298b1a230] running status set to false 
[INFO ] 2024-10-14 08:45:28.254 - [测试主从合并内嵌数组没有关联键(100)][f0761b93-7217-48fc-b8f2-978298b1a230] - Node f0761b93-7217-48fc-b8f2-978298b1a230[f0761b93-7217-48fc-b8f2-978298b1a230] schema data cleaned 
[INFO ] 2024-10-14 08:45:28.254 - [测试主从合并内嵌数组没有关联键(100)][f0761b93-7217-48fc-b8f2-978298b1a230] - Node f0761b93-7217-48fc-b8f2-978298b1a230[f0761b93-7217-48fc-b8f2-978298b1a230] monitor closed 
[INFO ] 2024-10-14 08:45:28.254 - [测试主从合并内嵌数组没有关联键(100)][f0761b93-7217-48fc-b8f2-978298b1a230] - Node f0761b93-7217-48fc-b8f2-978298b1a230[f0761b93-7217-48fc-b8f2-978298b1a230] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:45:28.280 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-3f68b420-7a82-4b0e-b80d-47f6a6213a89 
[INFO ] 2024-10-14 08:45:28.280 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-3f68b420-7a82-4b0e-b80d-47f6a6213a89 
[INFO ] 2024-10-14 08:45:28.280 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:45:28.283 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:45:28.283 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:45:28.286 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 43 ms 
[INFO ] 2024-10-14 08:45:28.287 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:45:28.287 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:45:28.288 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:45:28.419 - [测试主从合并内嵌数组没有关联键(100)][311b1c4d-08be-4d1e-9346-ea34a248d875] - Node 311b1c4d-08be-4d1e-9346-ea34a248d875[311b1c4d-08be-4d1e-9346-ea34a248d875] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:45:28.420 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:45:28.420 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:45:28.420 - [测试主从合并内嵌数组没有关联键(100)][311b1c4d-08be-4d1e-9346-ea34a248d875] - Node 311b1c4d-08be-4d1e-9346-ea34a248d875[311b1c4d-08be-4d1e-9346-ea34a248d875] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:45:28.420 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:45:28.421 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:45:28.425 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:45:33.854 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:45:33.883 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:45:33.883 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:45:33.884 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:45:33.884 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:45:34.091 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 39 ms 
[INFO ] 2024-10-14 08:45:39.686 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:45:39.688 - [测试主从合并内嵌数组没有关联键(100)][311b1c4d-08be-4d1e-9346-ea34a248d875] - Node 311b1c4d-08be-4d1e-9346-ea34a248d875[311b1c4d-08be-4d1e-9346-ea34a248d875] running status set to false 
[INFO ] 2024-10-14 08:45:39.688 - [测试主从合并内嵌数组没有关联键(100)][311b1c4d-08be-4d1e-9346-ea34a248d875] - Node 311b1c4d-08be-4d1e-9346-ea34a248d875[311b1c4d-08be-4d1e-9346-ea34a248d875] schema data cleaned 
[INFO ] 2024-10-14 08:45:39.688 - [测试主从合并内嵌数组没有关联键(100)][311b1c4d-08be-4d1e-9346-ea34a248d875] - Node 311b1c4d-08be-4d1e-9346-ea34a248d875[311b1c4d-08be-4d1e-9346-ea34a248d875] monitor closed 
[INFO ] 2024-10-14 08:45:39.688 - [测试主从合并内嵌数组没有关联键(100)][311b1c4d-08be-4d1e-9346-ea34a248d875] - Node 311b1c4d-08be-4d1e-9346-ea34a248d875[311b1c4d-08be-4d1e-9346-ea34a248d875] close complete, cost 3 ms 
[INFO ] 2024-10-14 08:45:39.716 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-c39e3524-8cec-40f2-a7c4-c56663660bd3 
[INFO ] 2024-10-14 08:45:39.716 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-c39e3524-8cec-40f2-a7c4-c56663660bd3 
[INFO ] 2024-10-14 08:45:39.717 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:45:39.720 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:45:39.720 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:45:39.723 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 45 ms 
[INFO ] 2024-10-14 08:45:39.723 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:45:39.723 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:45:39.933 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:45:49.349 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:45:49.350 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:45:49.351 - [测试主从合并内嵌数组没有关联键(100)][30684553-16aa-4324-989b-d9ed9dad9f30] - Node 30684553-16aa-4324-989b-d9ed9dad9f30[30684553-16aa-4324-989b-d9ed9dad9f30] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:45:49.351 - [测试主从合并内嵌数组没有关联键(100)][30684553-16aa-4324-989b-d9ed9dad9f30] - Node 30684553-16aa-4324-989b-d9ed9dad9f30[30684553-16aa-4324-989b-d9ed9dad9f30] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:45:49.351 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:45:49.351 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:45:49.351 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 08:45:54.808 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 08:45:54.808 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:45:54.809 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 08:45:54.809 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 08:45:54.812 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 08:45:55.019 - [测试主从合并内嵌数组没有关联键(100)][master_not_arr_index] - Node master_not_arr_index[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 29 ms 
[INFO ] 2024-10-14 08:46:00.396 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 08:46:00.398 - [测试主从合并内嵌数组没有关联键(100)][30684553-16aa-4324-989b-d9ed9dad9f30] - Node 30684553-16aa-4324-989b-d9ed9dad9f30[30684553-16aa-4324-989b-d9ed9dad9f30] running status set to false 
[INFO ] 2024-10-14 08:46:00.405 - [测试主从合并内嵌数组没有关联键(100)][30684553-16aa-4324-989b-d9ed9dad9f30] - Node 30684553-16aa-4324-989b-d9ed9dad9f30[30684553-16aa-4324-989b-d9ed9dad9f30] schema data cleaned 
[INFO ] 2024-10-14 08:46:00.405 - [测试主从合并内嵌数组没有关联键(100)][30684553-16aa-4324-989b-d9ed9dad9f30] - Node 30684553-16aa-4324-989b-d9ed9dad9f30[30684553-16aa-4324-989b-d9ed9dad9f30] monitor closed 
[INFO ] 2024-10-14 08:46:00.405 - [测试主从合并内嵌数组没有关联键(100)][30684553-16aa-4324-989b-d9ed9dad9f30] - Node 30684553-16aa-4324-989b-d9ed9dad9f30[30684553-16aa-4324-989b-d9ed9dad9f30] close complete, cost 2 ms 
[INFO ] 2024-10-14 08:46:00.424 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-48a07d25-fe78-473e-be54-5c299868a857 
[INFO ] 2024-10-14 08:46:00.424 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-48a07d25-fe78-473e-be54-5c299868a857 
[INFO ] 2024-10-14 08:46:00.425 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 08:46:00.428 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 08:46:00.428 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 08:46:00.431 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 45 ms 
[INFO ] 2024-10-14 08:46:00.431 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:46:00.431 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:46:00.561 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 08:46:00.563 - [测试主从合并内嵌数组没有关联键(100)][df1fc7a4-56c7-4a57-9250-c52aca5f5169] - Node df1fc7a4-56c7-4a57-9250-c52aca5f5169[df1fc7a4-56c7-4a57-9250-c52aca5f5169] start preload schema,table counts: 0 
[INFO ] 2024-10-14 08:46:00.563 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:46:00.563 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 08:46:00.563 - [测试主从合并内嵌数组没有关联键(100)][df1fc7a4-56c7-4a57-9250-c52aca5f5169] - Node df1fc7a4-56c7-4a57-9250-c52aca5f5169[df1fc7a4-56c7-4a57-9250-c52aca5f5169] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 08:46:00.563 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:46:00.563 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 08:46:00.566 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 08:46:05.989 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 08:46:06.017 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:46:06.017 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 08:46:06.018 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 08:46:06.019 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 08:46:06.019 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 43 ms 
[INFO ] 2024-10-14 08:46:11.541 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 08:46:11.542 - [测试主从合并内嵌数组没有关联键(100)][df1fc7a4-56c7-4a57-9250-c52aca5f5169] - Node df1fc7a4-56c7-4a57-9250-c52aca5f5169[df1fc7a4-56c7-4a57-9250-c52aca5f5169] running status set to false 
[INFO ] 2024-10-14 08:46:11.542 - [测试主从合并内嵌数组没有关联键(100)][df1fc7a4-56c7-4a57-9250-c52aca5f5169] - Node df1fc7a4-56c7-4a57-9250-c52aca5f5169[df1fc7a4-56c7-4a57-9250-c52aca5f5169] schema data cleaned 
[INFO ] 2024-10-14 08:46:11.542 - [测试主从合并内嵌数组没有关联键(100)][df1fc7a4-56c7-4a57-9250-c52aca5f5169] - Node df1fc7a4-56c7-4a57-9250-c52aca5f5169[df1fc7a4-56c7-4a57-9250-c52aca5f5169] monitor closed 
[INFO ] 2024-10-14 08:46:11.542 - [测试主从合并内嵌数组没有关联键(100)][df1fc7a4-56c7-4a57-9250-c52aca5f5169] - Node df1fc7a4-56c7-4a57-9250-c52aca5f5169[df1fc7a4-56c7-4a57-9250-c52aca5f5169] close complete, cost 6 ms 
[INFO ] 2024-10-14 08:46:11.569 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-b5799ecd-74ff-467e-876e-a566bf29dd26 
[INFO ] 2024-10-14 08:46:11.569 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-b5799ecd-74ff-467e-876e-a566bf29dd26 
[INFO ] 2024-10-14 08:46:11.569 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 08:46:11.572 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 08:46:11.572 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 08:46:11.572 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 43 ms 
[INFO ] 2024-10-14 08:46:11.575 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 08:46:11.575 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 08:46:11.576 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:08.332 - [测试主从合并内嵌数组没有关联键(100)][32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] - Node 32c88864-4574-4fbe-a38a-b0c6dd9fdc1c[32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:08.333 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:08.333 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:08.333 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:08.333 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:27:08.333 - [测试主从合并内嵌数组没有关联键(100)][32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] - Node 32c88864-4574-4fbe-a38a-b0c6dd9fdc1c[32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:08.333 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:27:13.873 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:27:13.922 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:13.922 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:13.923 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:27:13.923 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:27:14.007 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 59 ms 
[INFO ] 2024-10-14 15:27:14.008 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:14.008 - [测试主从合并内嵌数组没有关联键(100)][74efb049-8196-4813-ab42-ca1c51072562] - Node 74efb049-8196-4813-ab42-ca1c51072562[74efb049-8196-4813-ab42-ca1c51072562] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:14.008 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:14.008 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:14.008 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:14.008 - [测试主从合并内嵌数组没有关联键(100)][74efb049-8196-4813-ab42-ca1c51072562] - Node 74efb049-8196-4813-ab42-ca1c51072562[74efb049-8196-4813-ab42-ca1c51072562] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:14.219 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:27:16.000 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:16.002 - [测试主从合并内嵌数组没有关联键(100)][0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] - Node 0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c[0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:16.003 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:16.003 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:16.003 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:16.003 - [测试主从合并内嵌数组没有关联键(100)][0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] - Node 0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c[0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:16.003 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:27:18.564 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:18.564 - [测试主从合并内嵌数组没有关联键(100)][f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] - Node f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e[f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:18.564 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:18.565 - [测试主从合并内嵌数组没有关联键(100)][f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] - Node f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e[f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:18.565 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:18.565 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:18.565 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:27:19.708 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:27:19.709 - [测试主从合并内嵌数组没有关联键(100)][32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] - Node 32c88864-4574-4fbe-a38a-b0c6dd9fdc1c[32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] running status set to false 
[INFO ] 2024-10-14 15:27:19.709 - [测试主从合并内嵌数组没有关联键(100)][32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] - Node 32c88864-4574-4fbe-a38a-b0c6dd9fdc1c[32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] schema data cleaned 
[INFO ] 2024-10-14 15:27:19.709 - [测试主从合并内嵌数组没有关联键(100)][32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] - Node 32c88864-4574-4fbe-a38a-b0c6dd9fdc1c[32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] monitor closed 
[INFO ] 2024-10-14 15:27:19.709 - [测试主从合并内嵌数组没有关联键(100)][32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] - Node 32c88864-4574-4fbe-a38a-b0c6dd9fdc1c[32c88864-4574-4fbe-a38a-b0c6dd9fdc1c] close complete, cost 1 ms 
[INFO ] 2024-10-14 15:27:19.720 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-b0e6df26-47c5-47b1-84ec-55d4b6d0e554 
[INFO ] 2024-10-14 15:27:19.720 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-b0e6df26-47c5-47b1-84ec-55d4b6d0e554 
[INFO ] 2024-10-14 15:27:19.722 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:27:19.722 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:27:19.722 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:27:19.722 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 30 ms 
[INFO ] 2024-10-14 15:27:19.724 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:19.724 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:19.724 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:19.807 - [测试主从合并内嵌数组没有关联键(100)][a0f4b4e5-c682-4a8a-a791-801f01e49eef] - Node a0f4b4e5-c682-4a8a-a791-801f01e49eef[a0f4b4e5-c682-4a8a-a791-801f01e49eef] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:19.807 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:19.807 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:19.807 - [测试主从合并内嵌数组没有关联键(100)][a0f4b4e5-c682-4a8a-a791-801f01e49eef] - Node a0f4b4e5-c682-4a8a-a791-801f01e49eef[a0f4b4e5-c682-4a8a-a791-801f01e49eef] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:19.808 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:19.808 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:19.808 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:27:24.909 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:27:24.943 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:24.946 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:24.946 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:27:24.946 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:27:25.150 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 72 ms 
[INFO ] 2024-10-14 15:27:27.186 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:27:27.187 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:27.188 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:27.188 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:27:27.188 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:27:27.393 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 32 ms 
[INFO ] 2024-10-14 15:27:30.218 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:27:30.219 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:30.219 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:30.219 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:27:30.219 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:27:30.219 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 21 ms 
[INFO ] 2024-10-14 15:27:32.584 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:27:32.587 - [测试主从合并内嵌数组没有关联键(100)][f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] - Node f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e[f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] running status set to false 
[INFO ] 2024-10-14 15:27:32.587 - [测试主从合并内嵌数组没有关联键(100)][f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] - Node f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e[f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] schema data cleaned 
[INFO ] 2024-10-14 15:27:32.587 - [测试主从合并内嵌数组没有关联键(100)][f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] - Node f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e[f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] monitor closed 
[INFO ] 2024-10-14 15:27:32.587 - [测试主从合并内嵌数组没有关联键(100)][f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] - Node f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e[f2fe0630-3db5-4c21-b58e-5b2eb2efdd0e] close complete, cost 5 ms 
[INFO ] 2024-10-14 15:27:32.619 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-c4c30512-b361-436e-8f36-3f32eaa111c1 
[INFO ] 2024-10-14 15:27:32.619 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-c4c30512-b361-436e-8f36-3f32eaa111c1 
[INFO ] 2024-10-14 15:27:32.619 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:27:32.621 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:27:32.621 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:27:32.624 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 54 ms 
[INFO ] 2024-10-14 15:27:32.624 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:32.624 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:32.625 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:32.696 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:27:32.699 - [测试主从合并内嵌数组没有关联键(100)][a0f4b4e5-c682-4a8a-a791-801f01e49eef] - Node a0f4b4e5-c682-4a8a-a791-801f01e49eef[a0f4b4e5-c682-4a8a-a791-801f01e49eef] running status set to false 
[INFO ] 2024-10-14 15:27:32.699 - [测试主从合并内嵌数组没有关联键(100)][a0f4b4e5-c682-4a8a-a791-801f01e49eef] - Node a0f4b4e5-c682-4a8a-a791-801f01e49eef[a0f4b4e5-c682-4a8a-a791-801f01e49eef] schema data cleaned 
[INFO ] 2024-10-14 15:27:32.703 - [测试主从合并内嵌数组没有关联键(100)][a0f4b4e5-c682-4a8a-a791-801f01e49eef] - Node a0f4b4e5-c682-4a8a-a791-801f01e49eef[a0f4b4e5-c682-4a8a-a791-801f01e49eef] monitor closed 
[INFO ] 2024-10-14 15:27:32.716 - [测试主从合并内嵌数组没有关联键(100)][a0f4b4e5-c682-4a8a-a791-801f01e49eef] - Node a0f4b4e5-c682-4a8a-a791-801f01e49eef[a0f4b4e5-c682-4a8a-a791-801f01e49eef] close complete, cost 18 ms 
[INFO ] 2024-10-14 15:27:32.717 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-b8197358-176d-47ad-8c81-85666cd60a5c 
[INFO ] 2024-10-14 15:27:32.717 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-b8197358-176d-47ad-8c81-85666cd60a5c 
[INFO ] 2024-10-14 15:27:32.717 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:27:32.722 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:27:32.722 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:27:32.724 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 38 ms 
[INFO ] 2024-10-14 15:27:32.724 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:32.724 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:32.818 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:32.819 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:32.819 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:32.819 - [测试主从合并内嵌数组没有关联键(100)][45a311b8-43c9-4e1b-8ab5-b617f495448d] - Node 45a311b8-43c9-4e1b-8ab5-b617f495448d[45a311b8-43c9-4e1b-8ab5-b617f495448d] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:32.819 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:32.819 - [测试主从合并内嵌数组没有关联键(100)][45a311b8-43c9-4e1b-8ab5-b617f495448d] - Node 45a311b8-43c9-4e1b-8ab5-b617f495448d[45a311b8-43c9-4e1b-8ab5-b617f495448d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:32.819 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:32.821 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:27:35.731 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:27:35.757 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:35.757 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:27:35.758 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:27:35.758 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:27:35.959 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 39 ms 
[INFO ] 2024-10-14 15:27:37.834 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Jar file is not a file or not exists, /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__670c8f0c2e4f8c5b0dc89dd5__.jario.tapdata.entity.event.dml.TapInsertRecordEvent@37cbd0b9: {"after":{"name":"name2","id":1,"childId":1},"containsIllegalDate":false,"tableId":"master_not_arr_index","time":1728890833858,"type":300}
 
[ERROR] 2024-10-14 15:27:37.834 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Jar file is not a file or not exists, /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__670c8f0c2e4f8c5b0dc89dd5__.jario.tapdata.entity.event.dml.TapInsertRecordEvent@37cbd0b9: {"after":{"name":"name2","id":1,"childId":1},"containsIllegalDate":false,"tableId":"master_not_arr_index","time":1728890833858,"type":300}
 <-- Error Message -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Jar file is not a file or not exists, /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__670c8f0c2e4f8c5b0dc89dd5__.jario.tapdata.entity.event.dml.TapInsertRecordEvent@37cbd0b9: {"after":{"name":"name2","id":1,"childId":1},"containsIllegalDate":false,"tableId":"master_not_arr_index","time":1728890833858,"type":300}


<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Jar file is not a file or not exists, /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__670c8f0c2e4f8c5b0dc89dd5__.jar
	io.tapdata.pdk.core.classloader.ExternalJarManager.loadJars(ExternalJarManager.java:149)
	io.tapdata.pdk.core.connector.TapConnectorManager.refreshJars(TapConnectorManager.java:187)
	io.tapdata.pdk.core.api.PDKIntegration.refreshJars(PDKIntegration.java:460)
	io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:95)
	io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Jar file is not a file or not exists, /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__670c8f0c2e4f8c5b0dc89dd5__.jar
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:264)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Jar file is not a file or not exists, /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__670c8f0c2e4f8c5b0dc89dd5__.jar
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:165)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:152)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:148)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:197)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$null$0(HazelcastJavaScriptProcessorNode.java:173)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:173)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	... 18 more
Caused by: code: 60007 | message: Jar file is not a file or not exists, /Users/<USER>/IdeaProjects/tapdata-oss/dist/mysql-connector-v1.0-SNAPSHOT__670c8f0c2e4f8c5b0dc89dd5__.jar
	at io.tapdata.pdk.core.classloader.ExternalJarManager.loadJars(ExternalJarManager.java:149)
	at io.tapdata.pdk.core.connector.TapConnectorManager.refreshJars(TapConnectorManager.java:187)
	at io.tapdata.pdk.core.api.PDKIntegration.refreshJars(PDKIntegration.java:460)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:95)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 34 more

[INFO ] 2024-10-14 15:27:40.241 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:27:40.261 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:40.263 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:40.263 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:27:40.264 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:27:40.264 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 23 ms 
[INFO ] 2024-10-14 15:27:40.374 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:27:40.376 - [测试主从合并内嵌数组没有关联键(100)][0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] - Node 0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c[0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] running status set to false 
[INFO ] 2024-10-14 15:27:40.376 - [测试主从合并内嵌数组没有关联键(100)][0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] - Node 0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c[0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] schema data cleaned 
[INFO ] 2024-10-14 15:27:40.376 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:27:40.377 - [测试主从合并内嵌数组没有关联键(100)][0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] - Node 0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c[0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] monitor closed 
[INFO ] 2024-10-14 15:27:40.377 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:27:40.377 - [测试主从合并内嵌数组没有关联键(100)][0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] - Node 0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c[0ea061e1-b5bb-4aba-bd98-be7fe1bdc44c] close complete, cost 1 ms 
[INFO ] 2024-10-14 15:27:40.377 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 5 ms 
[INFO ] 2024-10-14 15:27:40.381 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:40.381 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:40.518 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:40.518 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:40.518 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:40.518 - [测试主从合并内嵌数组没有关联键(100)][38cf82c7-d97f-4848-8ac8-dedc16daf615] - Node 38cf82c7-d97f-4848-8ac8-dedc16daf615[38cf82c7-d97f-4848-8ac8-dedc16daf615] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:40.518 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:40.518 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:40.518 - [测试主从合并内嵌数组没有关联键(100)][38cf82c7-d97f-4848-8ac8-dedc16daf615] - Node 38cf82c7-d97f-4848-8ac8-dedc16daf615[38cf82c7-d97f-4848-8ac8-dedc16daf615] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:40.519 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:27:45.605 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Failed to load driver class com.mysql.cj.jdbc.Driver in either of HikariConfig class loader or Thread context classloader 
[ERROR] 2024-10-14 15:27:45.806 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Unknown PDK exception occur, java.lang.RuntimeException: Failed to load driver class com.mysql.cj.jdbc.Driver in either of HikariConfig class loader or Thread context classloader <-- Error Message -->
Unknown PDK exception occur, java.lang.RuntimeException: Failed to load driver class com.mysql.cj.jdbc.Driver in either of HikariConfig class loader or Thread context classloader

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Failed to load driver class com.mysql.cj.jdbc.Driver in either of HikariConfig class loader or Thread context classloader
	com.zaxxer.hikari.HikariConfig.setDriverClassName(HikariConfig.java:491)
	io.tapdata.common.JdbcContext$HikariConnection.getHikariDataSource(JdbcContext.java:269)
	io.tapdata.common.JdbcContext.<init>(JdbcContext.java:33)
	io.tapdata.connector.mysql.MysqlJdbcContextV2.<init>(MysqlJdbcContextV2.java:36)
	io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:109)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to load driver class com.mysql.cj.jdbc.Driver in either of HikariConfig class loader or Thread context classloader
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:160)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:197)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$null$0(HazelcastJavaScriptProcessorNode.java:173)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:173)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to load driver class com.mysql.cj.jdbc.Driver in either of HikariConfig class loader or Thread context classloader
	at com.zaxxer.hikari.HikariConfig.setDriverClassName(HikariConfig.java:491)
	at io.tapdata.common.JdbcContext$HikariConnection.getHikariDataSource(JdbcContext.java:269)
	at io.tapdata.common.JdbcContext.<init>(JdbcContext.java:33)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.<init>(MysqlJdbcContextV2.java:36)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:109)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 38 more

[INFO ] 2024-10-14 15:27:48.155 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:27:48.155 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:27:48.156 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:27:48.156 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 2 ms 
[INFO ] 2024-10-14 15:27:48.156 - [测试主从合并内嵌数组没有关联键(100)][45a311b8-43c9-4e1b-8ab5-b617f495448d] - Node 45a311b8-43c9-4e1b-8ab5-b617f495448d[45a311b8-43c9-4e1b-8ab5-b617f495448d] running status set to false 
[INFO ] 2024-10-14 15:27:48.156 - [测试主从合并内嵌数组没有关联键(100)][45a311b8-43c9-4e1b-8ab5-b617f495448d] - Node 45a311b8-43c9-4e1b-8ab5-b617f495448d[45a311b8-43c9-4e1b-8ab5-b617f495448d] schema data cleaned 
[INFO ] 2024-10-14 15:27:48.156 - [测试主从合并内嵌数组没有关联键(100)][45a311b8-43c9-4e1b-8ab5-b617f495448d] - Node 45a311b8-43c9-4e1b-8ab5-b617f495448d[45a311b8-43c9-4e1b-8ab5-b617f495448d] monitor closed 
[INFO ] 2024-10-14 15:27:48.158 - [测试主从合并内嵌数组没有关联键(100)][45a311b8-43c9-4e1b-8ab5-b617f495448d] - Node 45a311b8-43c9-4e1b-8ab5-b617f495448d[45a311b8-43c9-4e1b-8ab5-b617f495448d] close complete, cost 0 ms 
[INFO ] 2024-10-14 15:27:48.158 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:48.158 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:48.359 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:49.501 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:27:49.514 - [测试主从合并内嵌数组没有关联键(100)][74efb049-8196-4813-ab42-ca1c51072562] - Node 74efb049-8196-4813-ab42-ca1c51072562[74efb049-8196-4813-ab42-ca1c51072562] running status set to false 
[INFO ] 2024-10-14 15:27:49.514 - [测试主从合并内嵌数组没有关联键(100)][74efb049-8196-4813-ab42-ca1c51072562] - Node 74efb049-8196-4813-ab42-ca1c51072562[74efb049-8196-4813-ab42-ca1c51072562] schema data cleaned 
[INFO ] 2024-10-14 15:27:49.514 - [测试主从合并内嵌数组没有关联键(100)][74efb049-8196-4813-ab42-ca1c51072562] - Node 74efb049-8196-4813-ab42-ca1c51072562[74efb049-8196-4813-ab42-ca1c51072562] monitor closed 
[INFO ] 2024-10-14 15:27:49.526 - [测试主从合并内嵌数组没有关联键(100)][74efb049-8196-4813-ab42-ca1c51072562] - Node 74efb049-8196-4813-ab42-ca1c51072562[74efb049-8196-4813-ab42-ca1c51072562] close complete, cost 5 ms 
[INFO ] 2024-10-14 15:27:49.526 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a192b51f-a80d-4c35-842b-831f6983d1ed 
[INFO ] 2024-10-14 15:27:49.526 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a192b51f-a80d-4c35-842b-831f6983d1ed 
[INFO ] 2024-10-14 15:27:49.526 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:27:49.528 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:27:49.529 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:27:49.529 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 27 ms 
[INFO ] 2024-10-14 15:27:49.529 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:49.529 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:49.530 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:49.679 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:49.679 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:27:49.680 - [测试主从合并内嵌数组没有关联键(100)][74809691-5efc-430e-a124-476b4505bff9] - Node 74809691-5efc-430e-a124-476b4505bff9[74809691-5efc-430e-a124-476b4505bff9] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:27:49.680 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:49.680 - [测试主从合并内嵌数组没有关联键(100)][74809691-5efc-430e-a124-476b4505bff9] - Node 74809691-5efc-430e-a124-476b4505bff9[74809691-5efc-430e-a124-476b4505bff9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:27:49.682 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:27:49.883 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:27:50.491 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:27:50.501 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:50.502 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:50.502 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:27:50.502 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:27:50.710 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 32 ms 
[INFO ] 2024-10-14 15:27:54.455 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:27:54.470 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:54.470 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:27:54.471 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:27:54.472 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:27:54.473 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave_not_arr_index2[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 25 ms 
[INFO ] 2024-10-14 15:27:59.363 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:27:59.371 - [测试主从合并内嵌数组没有关联键(100)][38cf82c7-d97f-4848-8ac8-dedc16daf615] - Node 38cf82c7-d97f-4848-8ac8-dedc16daf615[38cf82c7-d97f-4848-8ac8-dedc16daf615] running status set to false 
[INFO ] 2024-10-14 15:27:59.371 - [测试主从合并内嵌数组没有关联键(100)][38cf82c7-d97f-4848-8ac8-dedc16daf615] - Node 38cf82c7-d97f-4848-8ac8-dedc16daf615[38cf82c7-d97f-4848-8ac8-dedc16daf615] schema data cleaned 
[INFO ] 2024-10-14 15:27:59.371 - [测试主从合并内嵌数组没有关联键(100)][38cf82c7-d97f-4848-8ac8-dedc16daf615] - Node 38cf82c7-d97f-4848-8ac8-dedc16daf615[38cf82c7-d97f-4848-8ac8-dedc16daf615] monitor closed 
[INFO ] 2024-10-14 15:27:59.372 - [测试主从合并内嵌数组没有关联键(100)][38cf82c7-d97f-4848-8ac8-dedc16daf615] - Node 38cf82c7-d97f-4848-8ac8-dedc16daf615[38cf82c7-d97f-4848-8ac8-dedc16daf615] close complete, cost 2 ms 
[INFO ] 2024-10-14 15:27:59.375 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-24b98077-4453-4411-b3c0-19eaa1bcfee6 
[INFO ] 2024-10-14 15:27:59.375 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-24b98077-4453-4411-b3c0-19eaa1bcfee6 
[INFO ] 2024-10-14 15:27:59.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:27:59.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:27:59.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:27:59.379 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 15 ms 
[INFO ] 2024-10-14 15:27:59.380 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:59.380 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:59.468 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:27:59.468 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:27:59.475 - [测试主从合并内嵌数组没有关联键(100)][74809691-5efc-430e-a124-476b4505bff9] - Node 74809691-5efc-430e-a124-476b4505bff9[74809691-5efc-430e-a124-476b4505bff9] running status set to false 
[INFO ] 2024-10-14 15:27:59.475 - [测试主从合并内嵌数组没有关联键(100)][74809691-5efc-430e-a124-476b4505bff9] - Node 74809691-5efc-430e-a124-476b4505bff9[74809691-5efc-430e-a124-476b4505bff9] schema data cleaned 
[INFO ] 2024-10-14 15:27:59.475 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-a625039a-525b-41d6-9fae-5d693aa42d12 
[INFO ] 2024-10-14 15:27:59.475 - [测试主从合并内嵌数组没有关联键(100)][74809691-5efc-430e-a124-476b4505bff9] - Node 74809691-5efc-430e-a124-476b4505bff9[74809691-5efc-430e-a124-476b4505bff9] monitor closed 
[INFO ] 2024-10-14 15:27:59.476 - [测试主从合并内嵌数组没有关联键(100)][74809691-5efc-430e-a124-476b4505bff9] - Node 74809691-5efc-430e-a124-476b4505bff9[74809691-5efc-430e-a124-476b4505bff9] close complete, cost 3 ms 
[INFO ] 2024-10-14 15:27:59.476 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-a625039a-525b-41d6-9fae-5d693aa42d12 
[INFO ] 2024-10-14 15:27:59.476 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:27:59.477 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:27:59.477 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:27:59.477 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 10 ms 
[INFO ] 2024-10-14 15:27:59.478 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:27:59.478 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:27:59.682 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:37:22.627 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:22.629 - [测试主从合并内嵌数组没有关联键(100)][a445f45b-d110-4efa-93b7-dd2fbf594ecf] - Node a445f45b-d110-4efa-93b7-dd2fbf594ecf[a445f45b-d110-4efa-93b7-dd2fbf594ecf] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:37:22.629 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:22.629 - [测试主从合并内嵌数组没有关联键(100)][a445f45b-d110-4efa-93b7-dd2fbf594ecf] - Node a445f45b-d110-4efa-93b7-dd2fbf594ecf[a445f45b-d110-4efa-93b7-dd2fbf594ecf] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:22.629 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:22.629 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:37:22.629 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:37:27.398 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:37:27.412 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:37:27.412 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:37:27.413 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:37:27.413 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:37:27.618 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 16 ms 
[INFO ] 2024-10-14 15:37:32.350 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:37:32.350 - [测试主从合并内嵌数组没有关联键(100)][a445f45b-d110-4efa-93b7-dd2fbf594ecf] - Node a445f45b-d110-4efa-93b7-dd2fbf594ecf[a445f45b-d110-4efa-93b7-dd2fbf594ecf] running status set to false 
[INFO ] 2024-10-14 15:37:32.350 - [测试主从合并内嵌数组没有关联键(100)][a445f45b-d110-4efa-93b7-dd2fbf594ecf] - Node a445f45b-d110-4efa-93b7-dd2fbf594ecf[a445f45b-d110-4efa-93b7-dd2fbf594ecf] schema data cleaned 
[INFO ] 2024-10-14 15:37:32.354 - [测试主从合并内嵌数组没有关联键(100)][a445f45b-d110-4efa-93b7-dd2fbf594ecf] - Node a445f45b-d110-4efa-93b7-dd2fbf594ecf[a445f45b-d110-4efa-93b7-dd2fbf594ecf] monitor closed 
[INFO ] 2024-10-14 15:37:32.354 - [测试主从合并内嵌数组没有关联键(100)][a445f45b-d110-4efa-93b7-dd2fbf594ecf] - Node a445f45b-d110-4efa-93b7-dd2fbf594ecf[a445f45b-d110-4efa-93b7-dd2fbf594ecf] close complete, cost 15 ms 
[INFO ] 2024-10-14 15:37:32.364 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-c507ee80-057c-498c-83ab-a816157ebf2c 
[INFO ] 2024-10-14 15:37:32.364 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-c507ee80-057c-498c-83ab-a816157ebf2c 
[INFO ] 2024-10-14 15:37:32.368 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:37:32.368 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:37:32.368 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:37:32.369 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 68 ms 
[INFO ] 2024-10-14 15:37:32.372 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:37:32.372 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)][b415544e-29d3-4d54-bb8a-5fcca10953e1] - Node b415544e-29d3-4d54-bb8a-5fcca10953e1[b415544e-29d3-4d54-bb8a-5fcca10953e1] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)][b415544e-29d3-4d54-bb8a-5fcca10953e1] - Node b415544e-29d3-4d54-bb8a-5fcca10953e1[b415544e-29d3-4d54-bb8a-5fcca10953e1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:32.466 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:37:37.258 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:37:37.271 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:37:37.271 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:37:37.271 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:37:37.271 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:37:37.271 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 16 ms 
[INFO ] 2024-10-14 15:37:42.203 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:37:42.204 - [测试主从合并内嵌数组没有关联键(100)][b415544e-29d3-4d54-bb8a-5fcca10953e1] - Node b415544e-29d3-4d54-bb8a-5fcca10953e1[b415544e-29d3-4d54-bb8a-5fcca10953e1] running status set to false 
[INFO ] 2024-10-14 15:37:42.204 - [测试主从合并内嵌数组没有关联键(100)][b415544e-29d3-4d54-bb8a-5fcca10953e1] - Node b415544e-29d3-4d54-bb8a-5fcca10953e1[b415544e-29d3-4d54-bb8a-5fcca10953e1] schema data cleaned 
[INFO ] 2024-10-14 15:37:42.205 - [测试主从合并内嵌数组没有关联键(100)][b415544e-29d3-4d54-bb8a-5fcca10953e1] - Node b415544e-29d3-4d54-bb8a-5fcca10953e1[b415544e-29d3-4d54-bb8a-5fcca10953e1] monitor closed 
[INFO ] 2024-10-14 15:37:42.206 - [测试主从合并内嵌数组没有关联键(100)][b415544e-29d3-4d54-bb8a-5fcca10953e1] - Node b415544e-29d3-4d54-bb8a-5fcca10953e1[b415544e-29d3-4d54-bb8a-5fcca10953e1] close complete, cost 3 ms 
[INFO ] 2024-10-14 15:37:42.227 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-605efcf1-8d1e-4a51-84d6-5984ac33979f 
[INFO ] 2024-10-14 15:37:42.227 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-605efcf1-8d1e-4a51-84d6-5984ac33979f 
[INFO ] 2024-10-14 15:37:42.228 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:37:42.229 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:37:42.229 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:37:42.231 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 42 ms 
[INFO ] 2024-10-14 15:37:42.231 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:37:42.231 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:37:42.436 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:37:42.924 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:42.924 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:42.924 - [测试主从合并内嵌数组没有关联键(100)][0dae5fc1-07f1-4802-8dd8-7e4e85470009] - Node 0dae5fc1-07f1-4802-8dd8-7e4e85470009[0dae5fc1-07f1-4802-8dd8-7e4e85470009] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:37:42.924 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:42.924 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:42.924 - [测试主从合并内嵌数组没有关联键(100)][0dae5fc1-07f1-4802-8dd8-7e4e85470009] - Node 0dae5fc1-07f1-4802-8dd8-7e4e85470009[0dae5fc1-07f1-4802-8dd8-7e4e85470009] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:37:43.125 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:37:47.745 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:37:47.761 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:37:47.761 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:37:47.762 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:37:47.762 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:37:47.967 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 21 ms 
[INFO ] 2024-10-14 15:37:52.857 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:37:52.858 - [测试主从合并内嵌数组没有关联键(100)][0dae5fc1-07f1-4802-8dd8-7e4e85470009] - Node 0dae5fc1-07f1-4802-8dd8-7e4e85470009[0dae5fc1-07f1-4802-8dd8-7e4e85470009] running status set to false 
[INFO ] 2024-10-14 15:37:52.858 - [测试主从合并内嵌数组没有关联键(100)][0dae5fc1-07f1-4802-8dd8-7e4e85470009] - Node 0dae5fc1-07f1-4802-8dd8-7e4e85470009[0dae5fc1-07f1-4802-8dd8-7e4e85470009] schema data cleaned 
[INFO ] 2024-10-14 15:37:52.858 - [测试主从合并内嵌数组没有关联键(100)][0dae5fc1-07f1-4802-8dd8-7e4e85470009] - Node 0dae5fc1-07f1-4802-8dd8-7e4e85470009[0dae5fc1-07f1-4802-8dd8-7e4e85470009] monitor closed 
[INFO ] 2024-10-14 15:37:52.859 - [测试主从合并内嵌数组没有关联键(100)][0dae5fc1-07f1-4802-8dd8-7e4e85470009] - Node 0dae5fc1-07f1-4802-8dd8-7e4e85470009[0dae5fc1-07f1-4802-8dd8-7e4e85470009] close complete, cost 4 ms 
[INFO ] 2024-10-14 15:37:52.865 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-2663eb29-7a6d-44c2-88d2-02e1b0e1a339 
[INFO ] 2024-10-14 15:37:52.865 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-2663eb29-7a6d-44c2-88d2-02e1b0e1a339 
[INFO ] 2024-10-14 15:37:52.865 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:37:52.867 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:37:52.867 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:37:52.868 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 20 ms 
[INFO ] 2024-10-14 15:37:52.868 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:37:52.868 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:37:52.972 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:37:52.972 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:52.973 - [测试主从合并内嵌数组没有关联键(100)][d6220f2a-acdc-4e81-8e86-b08c62cc42f6] - Node d6220f2a-acdc-4e81-8e86-b08c62cc42f6[d6220f2a-acdc-4e81-8e86-b08c62cc42f6] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:37:52.973 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:37:52.973 - [测试主从合并内嵌数组没有关联键(100)][d6220f2a-acdc-4e81-8e86-b08c62cc42f6] - Node d6220f2a-acdc-4e81-8e86-b08c62cc42f6[d6220f2a-acdc-4e81-8e86-b08c62cc42f6] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:37:52.973 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:37:52.973 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 3 ms 
[INFO ] 2024-10-14 15:37:52.974 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:37:57.758 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:37:57.758 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:37:57.759 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:37:57.759 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:37:57.759 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:37:57.760 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 18 ms 
[INFO ] 2024-10-14 15:38:02.610 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:38:02.610 - [测试主从合并内嵌数组没有关联键(100)][d6220f2a-acdc-4e81-8e86-b08c62cc42f6] - Node d6220f2a-acdc-4e81-8e86-b08c62cc42f6[d6220f2a-acdc-4e81-8e86-b08c62cc42f6] running status set to false 
[INFO ] 2024-10-14 15:38:02.610 - [测试主从合并内嵌数组没有关联键(100)][d6220f2a-acdc-4e81-8e86-b08c62cc42f6] - Node d6220f2a-acdc-4e81-8e86-b08c62cc42f6[d6220f2a-acdc-4e81-8e86-b08c62cc42f6] schema data cleaned 
[INFO ] 2024-10-14 15:38:02.610 - [测试主从合并内嵌数组没有关联键(100)][d6220f2a-acdc-4e81-8e86-b08c62cc42f6] - Node d6220f2a-acdc-4e81-8e86-b08c62cc42f6[d6220f2a-acdc-4e81-8e86-b08c62cc42f6] monitor closed 
[INFO ] 2024-10-14 15:38:02.610 - [测试主从合并内嵌数组没有关联键(100)][d6220f2a-acdc-4e81-8e86-b08c62cc42f6] - Node d6220f2a-acdc-4e81-8e86-b08c62cc42f6[d6220f2a-acdc-4e81-8e86-b08c62cc42f6] close complete, cost 0 ms 
[INFO ] 2024-10-14 15:38:02.612 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-60a53921-33fd-4388-852b-454f230c24d8 
[INFO ] 2024-10-14 15:38:02.613 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-60a53921-33fd-4388-852b-454f230c24d8 
[INFO ] 2024-10-14 15:38:02.613 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:38:02.614 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:38:02.614 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:38:02.615 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 12 ms 
[INFO ] 2024-10-14 15:38:02.615 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:38:02.615 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:38:02.820 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:38:23.547 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:23.547 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:23.547 - [测试主从合并内嵌数组没有关联键(100)][b075f901-8c8a-4052-83a1-65cff504ea12] - Node b075f901-8c8a-4052-83a1-65cff504ea12[b075f901-8c8a-4052-83a1-65cff504ea12] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:38:23.548 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:23.548 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:23.548 - [测试主从合并内嵌数组没有关联键(100)][b075f901-8c8a-4052-83a1-65cff504ea12] - Node b075f901-8c8a-4052-83a1-65cff504ea12[b075f901-8c8a-4052-83a1-65cff504ea12] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:23.599 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:38:23.599 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:23.599 - [测试主从合并内嵌数组没有关联键(100)][f83daa95-413c-4f66-b7ef-ccab8a8fb75a] - Node f83daa95-413c-4f66-b7ef-ccab8a8fb75a[f83daa95-413c-4f66-b7ef-ccab8a8fb75a] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:38:23.599 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:23.599 - [测试主从合并内嵌数组没有关联键(100)][f83daa95-413c-4f66-b7ef-ccab8a8fb75a] - Node f83daa95-413c-4f66-b7ef-ccab8a8fb75a[f83daa95-413c-4f66-b7ef-ccab8a8fb75a] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:23.599 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:23.599 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:23.804 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:38:28.342 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:38:28.343 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:38:28.343 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:38:28.343 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:38:28.343 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:38:28.343 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 13 ms 
[INFO ] 2024-10-14 15:38:33.183 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:38:33.183 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:38:33.183 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:38:33.184 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:38:33.184 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:38:33.305 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 33 ms 
[INFO ] 2024-10-14 15:38:33.305 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:38:33.328 - [测试主从合并内嵌数组没有关联键(100)][b075f901-8c8a-4052-83a1-65cff504ea12] - Node b075f901-8c8a-4052-83a1-65cff504ea12[b075f901-8c8a-4052-83a1-65cff504ea12] running status set to false 
[INFO ] 2024-10-14 15:38:33.328 - [测试主从合并内嵌数组没有关联键(100)][b075f901-8c8a-4052-83a1-65cff504ea12] - Node b075f901-8c8a-4052-83a1-65cff504ea12[b075f901-8c8a-4052-83a1-65cff504ea12] schema data cleaned 
[INFO ] 2024-10-14 15:38:33.329 - [测试主从合并内嵌数组没有关联键(100)][b075f901-8c8a-4052-83a1-65cff504ea12] - Node b075f901-8c8a-4052-83a1-65cff504ea12[b075f901-8c8a-4052-83a1-65cff504ea12] monitor closed 
[INFO ] 2024-10-14 15:38:33.329 - [测试主从合并内嵌数组没有关联键(100)][b075f901-8c8a-4052-83a1-65cff504ea12] - Node b075f901-8c8a-4052-83a1-65cff504ea12[b075f901-8c8a-4052-83a1-65cff504ea12] close complete, cost 14 ms 
[INFO ] 2024-10-14 15:38:33.333 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-ceed00e9-b8ab-47b0-bf83-b1eebc6d1ff2 
[INFO ] 2024-10-14 15:38:33.333 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-ceed00e9-b8ab-47b0-bf83-b1eebc6d1ff2 
[INFO ] 2024-10-14 15:38:33.333 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:38:33.336 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:38:33.336 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:38:33.337 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 33 ms 
[INFO ] 2024-10-14 15:38:33.338 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:38:33.338 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:38:33.338 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:38:33.420 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:33.420 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:33.420 - [测试主从合并内嵌数组没有关联键(100)][368961eb-e1c0-489a-8282-5b415b4b3035] - Node 368961eb-e1c0-489a-8282-5b415b4b3035[368961eb-e1c0-489a-8282-5b415b4b3035] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:38:33.421 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:33.421 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:33.421 - [测试主从合并内嵌数组没有关联键(100)][368961eb-e1c0-489a-8282-5b415b4b3035] - Node 368961eb-e1c0-489a-8282-5b415b4b3035[368961eb-e1c0-489a-8282-5b415b4b3035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:33.626 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:38:34.644 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:34.644 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:34.645 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:34.645 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:34.645 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:38:34.645 - [测试主从合并内嵌数组没有关联键(100)][ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] - Node ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11[ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:38:34.645 - [测试主从合并内嵌数组没有关联键(100)][ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] - Node ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11[ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:38.202 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:38:38.202 - [测试主从合并内嵌数组没有关联键(100)][f83daa95-413c-4f66-b7ef-ccab8a8fb75a] - Node f83daa95-413c-4f66-b7ef-ccab8a8fb75a[f83daa95-413c-4f66-b7ef-ccab8a8fb75a] running status set to false 
[INFO ] 2024-10-14 15:38:38.202 - [测试主从合并内嵌数组没有关联键(100)][f83daa95-413c-4f66-b7ef-ccab8a8fb75a] - Node f83daa95-413c-4f66-b7ef-ccab8a8fb75a[f83daa95-413c-4f66-b7ef-ccab8a8fb75a] schema data cleaned 
[INFO ] 2024-10-14 15:38:38.219 - [测试主从合并内嵌数组没有关联键(100)][f83daa95-413c-4f66-b7ef-ccab8a8fb75a] - Node f83daa95-413c-4f66-b7ef-ccab8a8fb75a[f83daa95-413c-4f66-b7ef-ccab8a8fb75a] monitor closed 
[INFO ] 2024-10-14 15:38:38.221 - [测试主从合并内嵌数组没有关联键(100)][f83daa95-413c-4f66-b7ef-ccab8a8fb75a] - Node f83daa95-413c-4f66-b7ef-ccab8a8fb75a[f83daa95-413c-4f66-b7ef-ccab8a8fb75a] close complete, cost 3 ms 
[INFO ] 2024-10-14 15:38:38.226 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-30075478-99cc-47d4-a7dc-d0260c772cd2 
[INFO ] 2024-10-14 15:38:38.226 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-30075478-99cc-47d4-a7dc-d0260c772cd2 
[INFO ] 2024-10-14 15:38:38.227 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:38:38.231 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:38:38.231 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:38:38.231 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 29 ms 
[INFO ] 2024-10-14 15:38:38.233 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:38:38.233 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:38:38.234 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:38:38.374 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:38.374 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:38.375 - [测试主从合并内嵌数组没有关联键(100)][da34f37e-374e-4571-b063-9b2f24d82de5] - Node da34f37e-374e-4571-b063-9b2f24d82de5[da34f37e-374e-4571-b063-9b2f24d82de5] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:38:38.375 - [测试主从合并内嵌数组没有关联键(100)][da34f37e-374e-4571-b063-9b2f24d82de5] - Node da34f37e-374e-4571-b063-9b2f24d82de5[da34f37e-374e-4571-b063-9b2f24d82de5] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:38.375 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:38.375 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 2 ms 
[INFO ] 2024-10-14 15:38:38.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:38:38.440 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:38:38.440 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:38:38.440 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:38:38.440 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:38:38.440 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:38:38.441 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 28 ms 
[INFO ] 2024-10-14 15:38:43.350 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:38:43.350 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:38:43.350 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:38:43.350 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:38:43.350 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:38:43.553 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 33 ms 
[INFO ] 2024-10-14 15:38:45.372 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:38:45.386 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:38:45.387 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:38:45.387 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:38:45.387 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:38:45.387 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 15 ms 
[INFO ] 2024-10-14 15:38:50.284 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:38:50.294 - [测试主从合并内嵌数组没有关联键(100)][ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] - Node ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11[ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] running status set to false 
[INFO ] 2024-10-14 15:38:50.300 - [测试主从合并内嵌数组没有关联键(100)][ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] - Node ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11[ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] schema data cleaned 
[INFO ] 2024-10-14 15:38:50.300 - [测试主从合并内嵌数组没有关联键(100)][ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] - Node ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11[ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] monitor closed 
[INFO ] 2024-10-14 15:38:50.302 - [测试主从合并内嵌数组没有关联键(100)][ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] - Node ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11[ff2250a8-d4d5-4eb0-ac2c-0d0ca307df11] close complete, cost 17 ms 
[INFO ] 2024-10-14 15:38:50.307 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-241c06b7-cb7b-420d-9d31-f5d127df30b9 
[INFO ] 2024-10-14 15:38:50.307 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-241c06b7-cb7b-420d-9d31-f5d127df30b9 
[INFO ] 2024-10-14 15:38:50.307 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:38:50.310 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:38:50.310 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:38:50.310 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 31 ms 
[INFO ] 2024-10-14 15:38:50.315 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:38:50.315 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:38:50.315 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:38:50.356 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:38:50.357 - [测试主从合并内嵌数组没有关联键(100)][da34f37e-374e-4571-b063-9b2f24d82de5] - Node da34f37e-374e-4571-b063-9b2f24d82de5[da34f37e-374e-4571-b063-9b2f24d82de5] running status set to false 
[INFO ] 2024-10-14 15:38:50.357 - [测试主从合并内嵌数组没有关联键(100)][da34f37e-374e-4571-b063-9b2f24d82de5] - Node da34f37e-374e-4571-b063-9b2f24d82de5[da34f37e-374e-4571-b063-9b2f24d82de5] schema data cleaned 
[INFO ] 2024-10-14 15:38:50.357 - [测试主从合并内嵌数组没有关联键(100)][da34f37e-374e-4571-b063-9b2f24d82de5] - Node da34f37e-374e-4571-b063-9b2f24d82de5[da34f37e-374e-4571-b063-9b2f24d82de5] monitor closed 
[INFO ] 2024-10-14 15:38:50.357 - [测试主从合并内嵌数组没有关联键(100)][da34f37e-374e-4571-b063-9b2f24d82de5] - Node da34f37e-374e-4571-b063-9b2f24d82de5[da34f37e-374e-4571-b063-9b2f24d82de5] close complete, cost 1 ms 
[INFO ] 2024-10-14 15:38:50.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-cea30ed7-8aba-44b7-b958-2496ee14f5fc 
[INFO ] 2024-10-14 15:38:50.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-cea30ed7-8aba-44b7-b958-2496ee14f5fc 
[INFO ] 2024-10-14 15:38:50.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:38:50.380 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:38:50.381 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:38:50.381 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 29 ms 
[INFO ] 2024-10-14 15:38:50.383 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:38:50.383 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:38:50.383 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:38:50.487 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:50.487 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:38:50.487 - [测试主从合并内嵌数组没有关联键(100)][7d8547a1-e130-4dcf-ace9-0a36580182f9] - Node 7d8547a1-e130-4dcf-ace9-0a36580182f9[7d8547a1-e130-4dcf-ace9-0a36580182f9] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:38:50.487 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:50.488 - [测试主从合并内嵌数组没有关联键(100)][7d8547a1-e130-4dcf-ace9-0a36580182f9] - Node 7d8547a1-e130-4dcf-ace9-0a36580182f9[7d8547a1-e130-4dcf-ace9-0a36580182f9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:50.488 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:38:50.489 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:38:55.150 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:38:55.167 - [测试主从合并内嵌数组没有关联键(100)][368961eb-e1c0-489a-8282-5b415b4b3035] - Node 368961eb-e1c0-489a-8282-5b415b4b3035[368961eb-e1c0-489a-8282-5b415b4b3035] running status set to false 
[INFO ] 2024-10-14 15:38:55.168 - [测试主从合并内嵌数组没有关联键(100)][368961eb-e1c0-489a-8282-5b415b4b3035] - Node 368961eb-e1c0-489a-8282-5b415b4b3035[368961eb-e1c0-489a-8282-5b415b4b3035] schema data cleaned 
[INFO ] 2024-10-14 15:38:55.168 - [测试主从合并内嵌数组没有关联键(100)][368961eb-e1c0-489a-8282-5b415b4b3035] - Node 368961eb-e1c0-489a-8282-5b415b4b3035[368961eb-e1c0-489a-8282-5b415b4b3035] monitor closed 
[INFO ] 2024-10-14 15:38:55.172 - [测试主从合并内嵌数组没有关联键(100)][368961eb-e1c0-489a-8282-5b415b4b3035] - Node 368961eb-e1c0-489a-8282-5b415b4b3035[368961eb-e1c0-489a-8282-5b415b4b3035] close complete, cost 25 ms 
[INFO ] 2024-10-14 15:38:55.172 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-9d2e06cc-adf8-4beb-a034-26100b9100fa 
[INFO ] 2024-10-14 15:38:55.172 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-9d2e06cc-adf8-4beb-a034-26100b9100fa 
[INFO ] 2024-10-14 15:38:55.172 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:38:55.175 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:38:55.176 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:38:55.177 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 35 ms 
[INFO ] 2024-10-14 15:38:55.180 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:38:55.180 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:38:55.181 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:38:55.411 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:38:55.432 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:38:55.432 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:38:55.432 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:38:55.433 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:38:55.434 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 29 ms 
[INFO ] 2024-10-14 15:39:00.357 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:39:00.357 - [测试主从合并内嵌数组没有关联键(100)][7d8547a1-e130-4dcf-ace9-0a36580182f9] - Node 7d8547a1-e130-4dcf-ace9-0a36580182f9[7d8547a1-e130-4dcf-ace9-0a36580182f9] running status set to false 
[INFO ] 2024-10-14 15:39:00.357 - [测试主从合并内嵌数组没有关联键(100)][7d8547a1-e130-4dcf-ace9-0a36580182f9] - Node 7d8547a1-e130-4dcf-ace9-0a36580182f9[7d8547a1-e130-4dcf-ace9-0a36580182f9] schema data cleaned 
[INFO ] 2024-10-14 15:39:00.357 - [测试主从合并内嵌数组没有关联键(100)][7d8547a1-e130-4dcf-ace9-0a36580182f9] - Node 7d8547a1-e130-4dcf-ace9-0a36580182f9[7d8547a1-e130-4dcf-ace9-0a36580182f9] monitor closed 
[INFO ] 2024-10-14 15:39:00.358 - [测试主从合并内嵌数组没有关联键(100)][7d8547a1-e130-4dcf-ace9-0a36580182f9] - Node 7d8547a1-e130-4dcf-ace9-0a36580182f9[7d8547a1-e130-4dcf-ace9-0a36580182f9] close complete, cost 3 ms 
[INFO ] 2024-10-14 15:39:00.360 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-bb6dcd96-b526-45ac-b730-cc96a3e67481 
[INFO ] 2024-10-14 15:39:00.360 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-bb6dcd96-b526-45ac-b730-cc96a3e67481 
[INFO ] 2024-10-14 15:39:00.361 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:39:00.361 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:39:00.361 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:39:00.363 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 16 ms 
[INFO ] 2024-10-14 15:39:00.363 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:39:00.363 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:39:00.568 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:42:53.429 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:42:53.429 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][fbf65730-0752-4b1c-a125-c5fabb089a1d] - Node fbf65730-0752-4b1c-a125-c5fabb089a1d[fbf65730-0752-4b1c-a125-c5fabb089a1d] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][fbf65730-0752-4b1c-a125-c5fabb089a1d] - Node fbf65730-0752-4b1c-a125-c5fabb089a1d[fbf65730-0752-4b1c-a125-c5fabb089a1d] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][93509dac-3c4a-4089-9954-7d0de58817d9] - Node 93509dac-3c4a-4089-9954-7d0de58817d9[93509dac-3c4a-4089-9954-7d0de58817d9] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][93509dac-3c4a-4089-9954-7d0de58817d9] - Node 93509dac-3c4a-4089-9954-7d0de58817d9[93509dac-3c4a-4089-9954-7d0de58817d9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:42:53.431 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:42:58.303 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:42:58.323 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:42:58.324 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:42:58.324 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:42:58.325 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:42:58.325 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 26 ms 
[INFO ] 2024-10-14 15:43:02.675 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:02.676 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:02.676 - [测试主从合并内嵌数组没有关联键(100)][ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] - Node ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e[ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:43:02.676 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:02.676 - [测试主从合并内嵌数组没有关联键(100)][ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] - Node ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e[ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:02.676 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:02.677 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:43:03.189 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:43:03.189 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:43:03.190 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:43:03.190 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:43:03.190 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:43:03.190 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 20 ms 
[INFO ] 2024-10-14 15:43:03.413 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:43:03.421 - [测试主从合并内嵌数组没有关联键(100)][fbf65730-0752-4b1c-a125-c5fabb089a1d] - Node fbf65730-0752-4b1c-a125-c5fabb089a1d[fbf65730-0752-4b1c-a125-c5fabb089a1d] running status set to false 
[INFO ] 2024-10-14 15:43:03.421 - [测试主从合并内嵌数组没有关联键(100)][fbf65730-0752-4b1c-a125-c5fabb089a1d] - Node fbf65730-0752-4b1c-a125-c5fabb089a1d[fbf65730-0752-4b1c-a125-c5fabb089a1d] schema data cleaned 
[INFO ] 2024-10-14 15:43:03.421 - [测试主从合并内嵌数组没有关联键(100)][fbf65730-0752-4b1c-a125-c5fabb089a1d] - Node fbf65730-0752-4b1c-a125-c5fabb089a1d[fbf65730-0752-4b1c-a125-c5fabb089a1d] monitor closed 
[INFO ] 2024-10-14 15:43:03.422 - [测试主从合并内嵌数组没有关联键(100)][fbf65730-0752-4b1c-a125-c5fabb089a1d] - Node fbf65730-0752-4b1c-a125-c5fabb089a1d[fbf65730-0752-4b1c-a125-c5fabb089a1d] close complete, cost 1 ms 
[INFO ] 2024-10-14 15:43:03.424 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a0a269c2-ddc5-46e0-928a-ffbf96fab033 
[INFO ] 2024-10-14 15:43:03.424 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a0a269c2-ddc5-46e0-928a-ffbf96fab033 
[INFO ] 2024-10-14 15:43:03.425 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:43:03.426 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:43:03.426 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:43:03.428 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 13 ms 
[INFO ] 2024-10-14 15:43:03.428 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:03.428 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:03.428 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:43:03.514 - [测试主从合并内嵌数组没有关联键(100)][6d12e8e1-c674-4071-af68-226d561d9234] - Node 6d12e8e1-c674-4071-af68-226d561d9234[6d12e8e1-c674-4071-af68-226d561d9234] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:43:03.514 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:03.514 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:03.514 - [测试主从合并内嵌数组没有关联键(100)][6d12e8e1-c674-4071-af68-226d561d9234] - Node 6d12e8e1-c674-4071-af68-226d561d9234[6d12e8e1-c674-4071-af68-226d561d9234] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:03.514 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:03.514 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:03.718 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:43:03.891 - [测试主从合并内嵌数组没有关联键(100)][25c432ab-010a-4593-9e7a-c5eb695d9a67] - Node 25c432ab-010a-4593-9e7a-c5eb695d9a67[25c432ab-010a-4593-9e7a-c5eb695d9a67] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:43:03.892 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:03.892 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:03.892 - [测试主从合并内嵌数组没有关联键(100)][25c432ab-010a-4593-9e7a-c5eb695d9a67] - Node 25c432ab-010a-4593-9e7a-c5eb695d9a67[25c432ab-010a-4593-9e7a-c5eb695d9a67] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:03.892 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:03.892 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 15:43:03.892 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:43:08.416 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:43:08.417 - [测试主从合并内嵌数组没有关联键(100)][93509dac-3c4a-4089-9954-7d0de58817d9] - Node 93509dac-3c4a-4089-9954-7d0de58817d9[93509dac-3c4a-4089-9954-7d0de58817d9] running status set to false 
[INFO ] 2024-10-14 15:43:08.417 - [测试主从合并内嵌数组没有关联键(100)][93509dac-3c4a-4089-9954-7d0de58817d9] - Node 93509dac-3c4a-4089-9954-7d0de58817d9[93509dac-3c4a-4089-9954-7d0de58817d9] schema data cleaned 
[INFO ] 2024-10-14 15:43:08.417 - [测试主从合并内嵌数组没有关联键(100)][93509dac-3c4a-4089-9954-7d0de58817d9] - Node 93509dac-3c4a-4089-9954-7d0de58817d9[93509dac-3c4a-4089-9954-7d0de58817d9] monitor closed 
[INFO ] 2024-10-14 15:43:08.417 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-82954f17-fb57-4c01-bbc5-0dde06c69787 
[INFO ] 2024-10-14 15:43:08.418 - [测试主从合并内嵌数组没有关联键(100)][93509dac-3c4a-4089-9954-7d0de58817d9] - Node 93509dac-3c4a-4089-9954-7d0de58817d9[93509dac-3c4a-4089-9954-7d0de58817d9] close complete, cost 2 ms 
[INFO ] 2024-10-14 15:43:08.418 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-82954f17-fb57-4c01-bbc5-0dde06c69787 
[INFO ] 2024-10-14 15:43:08.418 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:43:08.418 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:43:08.418 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:43:08.419 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 16 ms 
[INFO ] 2024-10-14 15:43:08.419 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:08.419 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:08.511 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:43:08.511 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:08.512 - [测试主从合并内嵌数组没有关联键(100)][9b0d9647-0b24-43fb-ba5c-170e22926ddc] - Node 9b0d9647-0b24-43fb-ba5c-170e22926ddc[9b0d9647-0b24-43fb-ba5c-170e22926ddc] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:43:08.512 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:08.512 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:08.512 - [测试主从合并内嵌数组没有关联键(100)][9b0d9647-0b24-43fb-ba5c-170e22926ddc] - Node 9b0d9647-0b24-43fb-ba5c-170e22926ddc[9b0d9647-0b24-43fb-ba5c-170e22926ddc] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:08.512 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:08.560 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:43:08.571 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:43:08.575 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:08.575 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:08.575 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:43:08.575 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:43:08.783 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 22 ms 
[INFO ] 2024-10-14 15:43:13.344 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:43:13.344 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:43:13.345 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:43:13.345 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:43:13.345 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:43:13.517 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 35 ms 
[INFO ] 2024-10-14 15:43:13.517 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:43:13.537 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:13.537 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:13.537 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:43:13.537 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:43:13.537 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 24 ms 
[INFO ] 2024-10-14 15:43:13.707 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:43:13.708 - [测试主从合并内嵌数组没有关联键(100)][6d12e8e1-c674-4071-af68-226d561d9234] - Node 6d12e8e1-c674-4071-af68-226d561d9234[6d12e8e1-c674-4071-af68-226d561d9234] running status set to false 
[INFO ] 2024-10-14 15:43:13.709 - [测试主从合并内嵌数组没有关联键(100)][6d12e8e1-c674-4071-af68-226d561d9234] - Node 6d12e8e1-c674-4071-af68-226d561d9234[6d12e8e1-c674-4071-af68-226d561d9234] schema data cleaned 
[INFO ] 2024-10-14 15:43:13.709 - [测试主从合并内嵌数组没有关联键(100)][6d12e8e1-c674-4071-af68-226d561d9234] - Node 6d12e8e1-c674-4071-af68-226d561d9234[6d12e8e1-c674-4071-af68-226d561d9234] monitor closed 
[INFO ] 2024-10-14 15:43:13.709 - [测试主从合并内嵌数组没有关联键(100)][6d12e8e1-c674-4071-af68-226d561d9234] - Node 6d12e8e1-c674-4071-af68-226d561d9234[6d12e8e1-c674-4071-af68-226d561d9234] close complete, cost 5 ms 
[INFO ] 2024-10-14 15:43:13.712 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-0b01557e-b7af-422f-9349-b2f6cd74cfc2 
[INFO ] 2024-10-14 15:43:13.712 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-0b01557e-b7af-422f-9349-b2f6cd74cfc2 
[INFO ] 2024-10-14 15:43:13.714 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:43:13.714 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:43:13.714 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:43:13.714 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 20 ms 
[INFO ] 2024-10-14 15:43:13.717 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:13.717 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:13.717 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:43:18.436 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:43:18.450 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:43:18.450 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:43:18.450 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:43:18.450 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:43:18.451 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 15 ms 
[INFO ] 2024-10-14 15:43:18.647 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:43:18.647 - [测试主从合并内嵌数组没有关联键(100)][9b0d9647-0b24-43fb-ba5c-170e22926ddc] - Node 9b0d9647-0b24-43fb-ba5c-170e22926ddc[9b0d9647-0b24-43fb-ba5c-170e22926ddc] running status set to false 
[INFO ] 2024-10-14 15:43:18.674 - [测试主从合并内嵌数组没有关联键(100)][9b0d9647-0b24-43fb-ba5c-170e22926ddc] - Node 9b0d9647-0b24-43fb-ba5c-170e22926ddc[9b0d9647-0b24-43fb-ba5c-170e22926ddc] schema data cleaned 
[INFO ] 2024-10-14 15:43:18.674 - [测试主从合并内嵌数组没有关联键(100)][9b0d9647-0b24-43fb-ba5c-170e22926ddc] - Node 9b0d9647-0b24-43fb-ba5c-170e22926ddc[9b0d9647-0b24-43fb-ba5c-170e22926ddc] monitor closed 
[INFO ] 2024-10-14 15:43:18.674 - [测试主从合并内嵌数组没有关联键(100)][9b0d9647-0b24-43fb-ba5c-170e22926ddc] - Node 9b0d9647-0b24-43fb-ba5c-170e22926ddc[9b0d9647-0b24-43fb-ba5c-170e22926ddc] close complete, cost 2 ms 
[INFO ] 2024-10-14 15:43:18.674 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-b19d307b-c6e4-4740-8658-01822698c0aa 
[INFO ] 2024-10-14 15:43:18.674 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-b19d307b-c6e4-4740-8658-01822698c0aa 
[INFO ] 2024-10-14 15:43:18.675 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:43:18.677 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:43:18.677 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:43:18.677 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 36 ms 
[INFO ] 2024-10-14 15:43:18.680 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:18.680 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:18.680 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:43:23.763 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:43:23.764 - [测试主从合并内嵌数组没有关联键(100)][25c432ab-010a-4593-9e7a-c5eb695d9a67] - Node 25c432ab-010a-4593-9e7a-c5eb695d9a67[25c432ab-010a-4593-9e7a-c5eb695d9a67] running status set to false 
[INFO ] 2024-10-14 15:43:23.764 - [测试主从合并内嵌数组没有关联键(100)][25c432ab-010a-4593-9e7a-c5eb695d9a67] - Node 25c432ab-010a-4593-9e7a-c5eb695d9a67[25c432ab-010a-4593-9e7a-c5eb695d9a67] schema data cleaned 
[INFO ] 2024-10-14 15:43:23.764 - [测试主从合并内嵌数组没有关联键(100)][25c432ab-010a-4593-9e7a-c5eb695d9a67] - Node 25c432ab-010a-4593-9e7a-c5eb695d9a67[25c432ab-010a-4593-9e7a-c5eb695d9a67] monitor closed 
[INFO ] 2024-10-14 15:43:23.765 - [测试主从合并内嵌数组没有关联键(100)][25c432ab-010a-4593-9e7a-c5eb695d9a67] - Node 25c432ab-010a-4593-9e7a-c5eb695d9a67[25c432ab-010a-4593-9e7a-c5eb695d9a67] close complete, cost 3 ms 
[INFO ] 2024-10-14 15:43:23.780 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-eeed779d-77ad-4a2f-9323-4b5e00c799b9 
[INFO ] 2024-10-14 15:43:23.780 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-eeed779d-77ad-4a2f-9323-4b5e00c799b9 
[INFO ] 2024-10-14 15:43:23.780 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:43:23.781 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:43:23.782 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:43:23.783 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 21 ms 
[INFO ] 2024-10-14 15:43:23.784 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:23.784 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:23.784 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:43:23.856 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:43:23.859 - [测试主从合并内嵌数组没有关联键(100)][ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] - Node ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e[ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] running status set to false 
[INFO ] 2024-10-14 15:43:23.860 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-18f0dfd7-5921-45a2-b7b4-3a5851e6f7ef 
[INFO ] 2024-10-14 15:43:23.860 - [测试主从合并内嵌数组没有关联键(100)][ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] - Node ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e[ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] schema data cleaned 
[INFO ] 2024-10-14 15:43:23.862 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-18f0dfd7-5921-45a2-b7b4-3a5851e6f7ef 
[INFO ] 2024-10-14 15:43:23.862 - [测试主从合并内嵌数组没有关联键(100)][ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] - Node ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e[ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] monitor closed 
[INFO ] 2024-10-14 15:43:23.862 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:43:23.865 - [测试主从合并内嵌数组没有关联键(100)][ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] - Node ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e[ea8d5f06-0d27-49f0-86cf-0761f7bf1d9e] close complete, cost 27 ms 
[INFO ] 2024-10-14 15:43:23.865 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:43:23.865 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:43:23.865 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 31 ms 
[INFO ] 2024-10-14 15:43:23.869 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:23.869 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:23.923 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:43:23.923 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:23.923 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:23.924 - [测试主从合并内嵌数组没有关联键(100)][1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] - Node 1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2[1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:43:23.924 - [测试主从合并内嵌数组没有关联键(100)][1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] - Node 1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2[1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:23.924 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:23.924 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:23.924 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:43:23.961 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:23.962 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:43:23.963 - [测试主从合并内嵌数组没有关联键(100)][0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] - Node 0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3[0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:43:23.963 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:23.963 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:23.963 - [测试主从合并内嵌数组没有关联键(100)][0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] - Node 0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3[0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:43:24.166 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:43:28.958 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:43:28.959 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:28.959 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:28.959 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:43:28.959 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:43:29.164 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 26 ms 
[INFO ] 2024-10-14 15:43:33.700 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:43:33.713 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:33.713 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:43:33.713 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:43:33.714 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:43:33.714 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 14 ms 
[INFO ] 2024-10-14 15:43:33.901 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:43:33.901 - [测试主从合并内嵌数组没有关联键(100)][1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] - Node 1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2[1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] running status set to false 
[INFO ] 2024-10-14 15:43:33.901 - [测试主从合并内嵌数组没有关联键(100)][1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] - Node 1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2[1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] schema data cleaned 
[INFO ] 2024-10-14 15:43:33.902 - [测试主从合并内嵌数组没有关联键(100)][1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] - Node 1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2[1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] monitor closed 
[INFO ] 2024-10-14 15:43:33.902 - [测试主从合并内嵌数组没有关联键(100)][1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] - Node 1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2[1b0aec21-2e95-4b2e-8e3b-38f067e8a9e2] close complete, cost 0 ms 
[INFO ] 2024-10-14 15:43:33.905 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-38efd2ac-6613-4a39-91cc-91e24b68ca7c 
[INFO ] 2024-10-14 15:43:33.905 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-38efd2ac-6613-4a39-91cc-91e24b68ca7c 
[INFO ] 2024-10-14 15:43:33.905 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:43:33.906 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:43:33.906 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:43:33.908 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 16 ms 
[INFO ] 2024-10-14 15:43:33.908 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:33.908 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:34.117 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:43:38.677 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:43:38.688 - [测试主从合并内嵌数组没有关联键(100)][0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] - Node 0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3[0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] running status set to false 
[INFO ] 2024-10-14 15:43:38.691 - [测试主从合并内嵌数组没有关联键(100)][0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] - Node 0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3[0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] schema data cleaned 
[INFO ] 2024-10-14 15:43:38.691 - [测试主从合并内嵌数组没有关联键(100)][0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] - Node 0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3[0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] monitor closed 
[INFO ] 2024-10-14 15:43:38.691 - [测试主从合并内嵌数组没有关联键(100)][0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] - Node 0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3[0a0d320b-0e32-4225-9e0d-7b0a9f71d4f3] close complete, cost 0 ms 
[INFO ] 2024-10-14 15:43:38.697 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-9da99660-722b-4372-beeb-18aa2eed1048 
[INFO ] 2024-10-14 15:43:38.697 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-9da99660-722b-4372-beeb-18aa2eed1048 
[INFO ] 2024-10-14 15:43:38.697 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:43:38.699 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:43:38.699 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:43:38.700 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 28 ms 
[INFO ] 2024-10-14 15:43:38.702 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:43:38.702 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:43:38.702 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:48:10.594 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:10.594 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:10.595 - [测试主从合并内嵌数组没有关联键(100)][573fd9be-e9df-40c2-a0d5-3305d8be1da1] - Node 573fd9be-e9df-40c2-a0d5-3305d8be1da1[573fd9be-e9df-40c2-a0d5-3305d8be1da1] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:48:10.595 - [测试主从合并内嵌数组没有关联键(100)][573fd9be-e9df-40c2-a0d5-3305d8be1da1] - Node 573fd9be-e9df-40c2-a0d5-3305d8be1da1[573fd9be-e9df-40c2-a0d5-3305d8be1da1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:10.595 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:10.595 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:10.595 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:48:15.252 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:15.252 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:15.252 - [测试主从合并内嵌数组没有关联键(100)][fba83935-2b7e-477d-9989-f9bd71e88216] - Node fba83935-2b7e-477d-9989-f9bd71e88216[fba83935-2b7e-477d-9989-f9bd71e88216] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:48:15.252 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:15.253 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:15.253 - [测试主从合并内嵌数组没有关联键(100)][fba83935-2b7e-477d-9989-f9bd71e88216] - Node fba83935-2b7e-477d-9989-f9bd71e88216[fba83935-2b7e-477d-9989-f9bd71e88216] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:15.253 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:48:15.487 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:48:15.487 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:48:15.487 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:48:15.487 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:48:15.487 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:48:15.488 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 20 ms 
[INFO ] 2024-10-14 15:48:18.635 - [测试主从合并内嵌数组没有关联键(100)][bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] - Node bcf1cb50-0f96-495e-8b0c-9bd185b0c51f[bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:48:18.635 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:18.635 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:18.635 - [测试主从合并内嵌数组没有关联键(100)][bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] - Node bcf1cb50-0f96-495e-8b0c-9bd185b0c51f[bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:18.635 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:18.636 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:18.636 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 15:48:20.375 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:48:20.375 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:48:20.375 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:48:20.375 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:48:20.375 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:48:20.376 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 21 ms 
[INFO ] 2024-10-14 15:48:20.523 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:48:20.525 - [测试主从合并内嵌数组没有关联键(100)][573fd9be-e9df-40c2-a0d5-3305d8be1da1] - Node 573fd9be-e9df-40c2-a0d5-3305d8be1da1[573fd9be-e9df-40c2-a0d5-3305d8be1da1] running status set to false 
[INFO ] 2024-10-14 15:48:20.525 - [测试主从合并内嵌数组没有关联键(100)][573fd9be-e9df-40c2-a0d5-3305d8be1da1] - Node 573fd9be-e9df-40c2-a0d5-3305d8be1da1[573fd9be-e9df-40c2-a0d5-3305d8be1da1] schema data cleaned 
[INFO ] 2024-10-14 15:48:20.525 - [测试主从合并内嵌数组没有关联键(100)][573fd9be-e9df-40c2-a0d5-3305d8be1da1] - Node 573fd9be-e9df-40c2-a0d5-3305d8be1da1[573fd9be-e9df-40c2-a0d5-3305d8be1da1] monitor closed 
[INFO ] 2024-10-14 15:48:20.525 - [测试主从合并内嵌数组没有关联键(100)][573fd9be-e9df-40c2-a0d5-3305d8be1da1] - Node 573fd9be-e9df-40c2-a0d5-3305d8be1da1[573fd9be-e9df-40c2-a0d5-3305d8be1da1] close complete, cost 3 ms 
[INFO ] 2024-10-14 15:48:20.550 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-094786ba-d701-40de-a3ff-652f7d38cb8c 
[INFO ] 2024-10-14 15:48:20.551 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-094786ba-d701-40de-a3ff-652f7d38cb8c 
[INFO ] 2024-10-14 15:48:20.551 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:48:20.554 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:48:20.555 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:48:20.555 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 36 ms 
[INFO ] 2024-10-14 15:48:20.557 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:48:20.557 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:48:20.658 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:48:20.658 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:20.659 - [测试主从合并内嵌数组没有关联键(100)][2f479890-077c-4d9d-bfc8-1bf8116ce20d] - Node 2f479890-077c-4d9d-bfc8-1bf8116ce20d[2f479890-077c-4d9d-bfc8-1bf8116ce20d] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:48:20.659 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:20.659 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:20.659 - [测试主从合并内嵌数组没有关联键(100)][2f479890-077c-4d9d-bfc8-1bf8116ce20d] - Node 2f479890-077c-4d9d-bfc8-1bf8116ce20d[2f479890-077c-4d9d-bfc8-1bf8116ce20d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:20.659 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:20.659 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:48:25.365 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 15:48:25.383 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:48:25.383 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 15:48:25.383 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 15:48:25.384 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 15:48:25.386 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 23 ms 
[INFO ] 2024-10-14 15:48:27.405 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:48:27.408 - [测试主从合并内嵌数组没有关联键(100)][fba83935-2b7e-477d-9989-f9bd71e88216] - Node fba83935-2b7e-477d-9989-f9bd71e88216[fba83935-2b7e-477d-9989-f9bd71e88216] running status set to false 
[INFO ] 2024-10-14 15:48:27.408 - [测试主从合并内嵌数组没有关联键(100)][fba83935-2b7e-477d-9989-f9bd71e88216] - Node fba83935-2b7e-477d-9989-f9bd71e88216[fba83935-2b7e-477d-9989-f9bd71e88216] schema data cleaned 
[INFO ] 2024-10-14 15:48:27.408 - [测试主从合并内嵌数组没有关联键(100)][fba83935-2b7e-477d-9989-f9bd71e88216] - Node fba83935-2b7e-477d-9989-f9bd71e88216[fba83935-2b7e-477d-9989-f9bd71e88216] monitor closed 
[INFO ] 2024-10-14 15:48:27.408 - [测试主从合并内嵌数组没有关联键(100)][fba83935-2b7e-477d-9989-f9bd71e88216] - Node fba83935-2b7e-477d-9989-f9bd71e88216[fba83935-2b7e-477d-9989-f9bd71e88216] close complete, cost 0 ms 
[INFO ] 2024-10-14 15:48:27.412 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a3b98fc7-3258-4969-b0eb-cdadb91e66b0 
[INFO ] 2024-10-14 15:48:27.414 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a3b98fc7-3258-4969-b0eb-cdadb91e66b0 
[INFO ] 2024-10-14 15:48:27.414 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:48:27.418 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:48:27.418 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:48:27.418 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 22 ms 
[INFO ] 2024-10-14 15:48:27.419 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:48:27.423 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:48:27.423 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:48:27.629 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:27.629 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:27.630 - [测试主从合并内嵌数组没有关联键(100)][6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] - Node 6c2a9a9b-f3eb-4c5a-8099-8f59deddca23[6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:48:27.630 - [测试主从合并内嵌数组没有关联键(100)][6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] - Node 6c2a9a9b-f3eb-4c5a-8099-8f59deddca23[6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:27.630 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:27.630 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:27.630 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:48:32.242 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 15:48:32.242 - [测试主从合并内嵌数组没有关联键(100)][bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] - Node bcf1cb50-0f96-495e-8b0c-9bd185b0c51f[bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] running status set to false 
[INFO ] 2024-10-14 15:48:32.243 - [测试主从合并内嵌数组没有关联键(100)][bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] - Node bcf1cb50-0f96-495e-8b0c-9bd185b0c51f[bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] schema data cleaned 
[INFO ] 2024-10-14 15:48:32.243 - [测试主从合并内嵌数组没有关联键(100)][bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] - Node bcf1cb50-0f96-495e-8b0c-9bd185b0c51f[bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] monitor closed 
[INFO ] 2024-10-14 15:48:32.243 - [测试主从合并内嵌数组没有关联键(100)][bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] - Node bcf1cb50-0f96-495e-8b0c-9bd185b0c51f[bcf1cb50-0f96-495e-8b0c-9bd185b0c51f] close complete, cost 1 ms 
[INFO ] 2024-10-14 15:48:32.259 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-1808dd12-b239-482b-aaed-369cafedcd16 
[INFO ] 2024-10-14 15:48:32.260 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-1808dd12-b239-482b-aaed-369cafedcd16 
[INFO ] 2024-10-14 15:48:32.260 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 15:48:32.262 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 15:48:32.262 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 15:48:32.264 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 21 ms 
[INFO ] 2024-10-14 15:48:32.265 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:48:32.265 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:48:32.265 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:48:32.367 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:32.368 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:32.368 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 15:48:32.368 - [测试主从合并内嵌数组没有关联键(100)][27097070-82d6-46dd-9a7f-3f5450b4fd82] - Node 27097070-82d6-46dd-9a7f-3f5450b4fd82[27097070-82d6-46dd-9a7f-3f5450b4fd82] start preload schema,table counts: 0 
[INFO ] 2024-10-14 15:48:32.368 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 15:48:32.368 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:32.368 - [测试主从合并内嵌数组没有关联键(100)][27097070-82d6-46dd-9a7f-3f5450b4fd82] - Node 27097070-82d6-46dd-9a7f-3f5450b4fd82[27097070-82d6-46dd-9a7f-3f5450b4fd82] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 15:48:32.543 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:48:32.543 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:48:32.543 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:48:32.543 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:48:32.543 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:48:32.544 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 26 ms 
[INFO ] 2024-10-14 15:48:37.099 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:48:37.099 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:48:37.099 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:48:37.099 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:48:37.099 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:48:37.100 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 13 ms 
[INFO ] 2024-10-14 15:48:37.383 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 15:48:37.398 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:48:37.398 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 15:48:37.398 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 15:48:37.398 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 15:48:37.399 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 19 ms 
[INFO ] 2024-10-14 15:48:37.562 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:48:37.564 - [测试主从合并内嵌数组没有关联键(100)][6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] - Node 6c2a9a9b-f3eb-4c5a-8099-8f59deddca23[6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] running status set to false 
[INFO ] 2024-10-14 15:48:37.564 - [测试主从合并内嵌数组没有关联键(100)][6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] - Node 6c2a9a9b-f3eb-4c5a-8099-8f59deddca23[6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] schema data cleaned 
[INFO ] 2024-10-14 15:48:37.564 - [测试主从合并内嵌数组没有关联键(100)][6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] - Node 6c2a9a9b-f3eb-4c5a-8099-8f59deddca23[6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] monitor closed 
[INFO ] 2024-10-14 15:48:37.564 - [测试主从合并内嵌数组没有关联键(100)][6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] - Node 6c2a9a9b-f3eb-4c5a-8099-8f59deddca23[6c2a9a9b-f3eb-4c5a-8099-8f59deddca23] close complete, cost 3 ms 
[INFO ] 2024-10-14 15:48:37.589 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-9a6ed32a-df8c-4605-a093-89fbdb696b89 
[INFO ] 2024-10-14 15:48:37.589 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-9a6ed32a-df8c-4605-a093-89fbdb696b89 
[INFO ] 2024-10-14 15:48:37.590 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:48:37.590 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:48:37.590 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:48:37.590 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 36 ms 
[INFO ] 2024-10-14 15:48:37.599 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:48:37.599 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:48:37.599 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:48:42.312 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:48:42.325 - [测试主从合并内嵌数组没有关联键(100)][27097070-82d6-46dd-9a7f-3f5450b4fd82] - Node 27097070-82d6-46dd-9a7f-3f5450b4fd82[27097070-82d6-46dd-9a7f-3f5450b4fd82] running status set to false 
[INFO ] 2024-10-14 15:48:42.325 - [测试主从合并内嵌数组没有关联键(100)][27097070-82d6-46dd-9a7f-3f5450b4fd82] - Node 27097070-82d6-46dd-9a7f-3f5450b4fd82[27097070-82d6-46dd-9a7f-3f5450b4fd82] schema data cleaned 
[INFO ] 2024-10-14 15:48:42.325 - [测试主从合并内嵌数组没有关联键(100)][27097070-82d6-46dd-9a7f-3f5450b4fd82] - Node 27097070-82d6-46dd-9a7f-3f5450b4fd82[27097070-82d6-46dd-9a7f-3f5450b4fd82] monitor closed 
[INFO ] 2024-10-14 15:48:42.325 - [测试主从合并内嵌数组没有关联键(100)][27097070-82d6-46dd-9a7f-3f5450b4fd82] - Node 27097070-82d6-46dd-9a7f-3f5450b4fd82[27097070-82d6-46dd-9a7f-3f5450b4fd82] close complete, cost 2 ms 
[INFO ] 2024-10-14 15:48:42.329 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-91dc0b2d-348e-4430-b881-f0dd7f4809e7 
[INFO ] 2024-10-14 15:48:42.330 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-91dc0b2d-348e-4430-b881-f0dd7f4809e7 
[INFO ] 2024-10-14 15:48:42.330 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:48:42.332 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:48:42.332 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:48:42.332 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 27 ms 
[INFO ] 2024-10-14 15:48:42.333 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:48:42.333 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:48:42.333 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 15:48:47.114 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 15:48:47.115 - [测试主从合并内嵌数组没有关联键(100)][2f479890-077c-4d9d-bfc8-1bf8116ce20d] - Node 2f479890-077c-4d9d-bfc8-1bf8116ce20d[2f479890-077c-4d9d-bfc8-1bf8116ce20d] running status set to false 
[INFO ] 2024-10-14 15:48:47.115 - [测试主从合并内嵌数组没有关联键(100)][2f479890-077c-4d9d-bfc8-1bf8116ce20d] - Node 2f479890-077c-4d9d-bfc8-1bf8116ce20d[2f479890-077c-4d9d-bfc8-1bf8116ce20d] schema data cleaned 
[INFO ] 2024-10-14 15:48:47.115 - [测试主从合并内嵌数组没有关联键(100)][2f479890-077c-4d9d-bfc8-1bf8116ce20d] - Node 2f479890-077c-4d9d-bfc8-1bf8116ce20d[2f479890-077c-4d9d-bfc8-1bf8116ce20d] monitor closed 
[INFO ] 2024-10-14 15:48:47.118 - [测试主从合并内嵌数组没有关联键(100)][2f479890-077c-4d9d-bfc8-1bf8116ce20d] - Node 2f479890-077c-4d9d-bfc8-1bf8116ce20d[2f479890-077c-4d9d-bfc8-1bf8116ce20d] close complete, cost 2 ms 
[INFO ] 2024-10-14 15:48:47.124 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-c0d224b2-c0c0-4e8e-a8dd-344ae846556d 
[INFO ] 2024-10-14 15:48:47.124 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-c0d224b2-c0c0-4e8e-a8dd-344ae846556d 
[INFO ] 2024-10-14 15:48:47.124 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 15:48:47.125 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 15:48:47.125 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 15:48:47.127 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 13 ms 
[INFO ] 2024-10-14 15:48:47.127 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 15:48:47.127 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 15:48:47.127 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:05:55.532 - [测试主从合并内嵌数组没有关联键(100)][5cfc883f-cb42-4540-8461-2155b1b202e6] - Node 5cfc883f-cb42-4540-8461-2155b1b202e6[5cfc883f-cb42-4540-8461-2155b1b202e6] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:05:55.532 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:05:55.533 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:05:55.533 - [测试主从合并内嵌数组没有关联键(100)][5cfc883f-cb42-4540-8461-2155b1b202e6] - Node 5cfc883f-cb42-4540-8461-2155b1b202e6[5cfc883f-cb42-4540-8461-2155b1b202e6] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 16:05:55.533 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 2 ms 
[INFO ] 2024-10-14 16:05:55.534 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 3 ms 
[INFO ] 2024-10-14 16:05:55.536 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 16:06:00.433 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 16:06:00.434 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:06:00.434 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:06:00.434 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 16:06:00.434 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 16:06:00.640 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 25 ms 
[INFO ] 2024-10-14 16:06:00.720 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:06:00.720 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:06:00.720 - [测试主从合并内嵌数组没有关联键(100)][42b151b5-4591-4255-b6a9-0a91df02fc54] - Node 42b151b5-4591-4255-b6a9-0a91df02fc54[42b151b5-4591-4255-b6a9-0a91df02fc54] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:06:00.720 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:00.720 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:00.720 - [测试主从合并内嵌数组没有关联键(100)][42b151b5-4591-4255-b6a9-0a91df02fc54] - Node 42b151b5-4591-4255-b6a9-0a91df02fc54[42b151b5-4591-4255-b6a9-0a91df02fc54] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:00.720 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 16:06:05.471 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 16:06:05.471 - [测试主从合并内嵌数组没有关联键(100)][5cfc883f-cb42-4540-8461-2155b1b202e6] - Node 5cfc883f-cb42-4540-8461-2155b1b202e6[5cfc883f-cb42-4540-8461-2155b1b202e6] running status set to false 
[INFO ] 2024-10-14 16:06:05.473 - [测试主从合并内嵌数组没有关联键(100)][5cfc883f-cb42-4540-8461-2155b1b202e6] - Node 5cfc883f-cb42-4540-8461-2155b1b202e6[5cfc883f-cb42-4540-8461-2155b1b202e6] schema data cleaned 
[INFO ] 2024-10-14 16:06:05.473 - [测试主从合并内嵌数组没有关联键(100)][5cfc883f-cb42-4540-8461-2155b1b202e6] - Node 5cfc883f-cb42-4540-8461-2155b1b202e6[5cfc883f-cb42-4540-8461-2155b1b202e6] monitor closed 
[INFO ] 2024-10-14 16:06:05.476 - [测试主从合并内嵌数组没有关联键(100)][5cfc883f-cb42-4540-8461-2155b1b202e6] - Node 5cfc883f-cb42-4540-8461-2155b1b202e6[5cfc883f-cb42-4540-8461-2155b1b202e6] close complete, cost 11 ms 
[INFO ] 2024-10-14 16:06:05.476 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-503163a1-d59b-4f7e-9c05-885879bfbd20 
[INFO ] 2024-10-14 16:06:05.476 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-503163a1-d59b-4f7e-9c05-885879bfbd20 
[INFO ] 2024-10-14 16:06:05.476 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 16:06:05.477 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 16:06:05.477 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 16:06:05.477 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 20 ms 
[INFO ] 2024-10-14 16:06:05.478 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:06:05.478 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:06:05.557 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:06:05.557 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:06:05.557 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:06:05.557 - [测试主从合并内嵌数组没有关联键(100)][6a3a0db6-ceae-48f4-b5f8-2de167b03878] - Node 6a3a0db6-ceae-48f4-b5f8-2de167b03878[6a3a0db6-ceae-48f4-b5f8-2de167b03878] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:06:05.558 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:05.558 - [测试主从合并内嵌数组没有关联键(100)][6a3a0db6-ceae-48f4-b5f8-2de167b03878] - Node 6a3a0db6-ceae-48f4-b5f8-2de167b03878[6a3a0db6-ceae-48f4-b5f8-2de167b03878] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:05.558 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:05.761 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 16:06:10.404 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 16:06:10.405 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:06:10.405 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:06:10.405 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 16:06:10.405 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 16:06:10.561 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 21 ms 
[INFO ] 2024-10-14 16:06:10.561 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 16:06:10.585 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:06:10.585 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:06:10.585 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 16:06:10.585 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 16:06:10.586 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 28 ms 
[INFO ] 2024-10-14 16:06:15.464 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 16:06:15.469 - [测试主从合并内嵌数组没有关联键(100)][42b151b5-4591-4255-b6a9-0a91df02fc54] - Node 42b151b5-4591-4255-b6a9-0a91df02fc54[42b151b5-4591-4255-b6a9-0a91df02fc54] running status set to false 
[INFO ] 2024-10-14 16:06:15.475 - [测试主从合并内嵌数组没有关联键(100)][42b151b5-4591-4255-b6a9-0a91df02fc54] - Node 42b151b5-4591-4255-b6a9-0a91df02fc54[42b151b5-4591-4255-b6a9-0a91df02fc54] schema data cleaned 
[INFO ] 2024-10-14 16:06:15.476 - [测试主从合并内嵌数组没有关联键(100)][42b151b5-4591-4255-b6a9-0a91df02fc54] - Node 42b151b5-4591-4255-b6a9-0a91df02fc54[42b151b5-4591-4255-b6a9-0a91df02fc54] monitor closed 
[INFO ] 2024-10-14 16:06:15.478 - [测试主从合并内嵌数组没有关联键(100)][42b151b5-4591-4255-b6a9-0a91df02fc54] - Node 42b151b5-4591-4255-b6a9-0a91df02fc54[42b151b5-4591-4255-b6a9-0a91df02fc54] close complete, cost 23 ms 
[INFO ] 2024-10-14 16:06:15.484 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-c4fff92b-23bb-41d9-b1b7-13d11eaf6fab 
[INFO ] 2024-10-14 16:06:15.484 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-c4fff92b-23bb-41d9-b1b7-13d11eaf6fab 
[INFO ] 2024-10-14 16:06:15.487 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 16:06:15.487 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 16:06:15.487 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 16:06:15.487 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 42 ms 
[INFO ] 2024-10-14 16:06:15.490 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:06:15.490 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:06:15.490 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:06:15.592 - [测试主从合并内嵌数组没有关联键(100)][43c4ef9f-a66e-4231-a219-06ecb1984718] - Node 43c4ef9f-a66e-4231-a219-06ecb1984718[43c4ef9f-a66e-4231-a219-06ecb1984718] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:06:15.592 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:06:15.592 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:06:15.592 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:15.592 - [测试主从合并内嵌数组没有关联键(100)][43c4ef9f-a66e-4231-a219-06ecb1984718] - Node 43c4ef9f-a66e-4231-a219-06ecb1984718[43c4ef9f-a66e-4231-a219-06ecb1984718] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 16:06:15.592 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:06:15.593 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 16:06:20.377 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 16:06:20.378 - [测试主从合并内嵌数组没有关联键(100)][6a3a0db6-ceae-48f4-b5f8-2de167b03878] - Node 6a3a0db6-ceae-48f4-b5f8-2de167b03878[6a3a0db6-ceae-48f4-b5f8-2de167b03878] running status set to false 
[INFO ] 2024-10-14 16:06:20.378 - [测试主从合并内嵌数组没有关联键(100)][6a3a0db6-ceae-48f4-b5f8-2de167b03878] - Node 6a3a0db6-ceae-48f4-b5f8-2de167b03878[6a3a0db6-ceae-48f4-b5f8-2de167b03878] schema data cleaned 
[INFO ] 2024-10-14 16:06:20.393 - [测试主从合并内嵌数组没有关联键(100)][6a3a0db6-ceae-48f4-b5f8-2de167b03878] - Node 6a3a0db6-ceae-48f4-b5f8-2de167b03878[6a3a0db6-ceae-48f4-b5f8-2de167b03878] monitor closed 
[INFO ] 2024-10-14 16:06:20.402 - [测试主从合并内嵌数组没有关联键(100)][6a3a0db6-ceae-48f4-b5f8-2de167b03878] - Node 6a3a0db6-ceae-48f4-b5f8-2de167b03878[6a3a0db6-ceae-48f4-b5f8-2de167b03878] close complete, cost 12 ms 
[INFO ] 2024-10-14 16:06:20.402 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-a9110ed5-67db-4739-8b6a-e37b9b7d94bb 
[INFO ] 2024-10-14 16:06:20.402 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-a9110ed5-67db-4739-8b6a-e37b9b7d94bb 
[INFO ] 2024-10-14 16:06:20.402 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 16:06:20.403 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 16:06:20.403 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 16:06:20.404 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 27 ms 
[INFO ] 2024-10-14 16:06:20.404 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:06:20.404 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:06:20.572 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:06:20.572 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 16:06:20.589 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:06:20.589 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:06:20.589 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 16:06:20.589 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 16:06:20.799 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 17 ms 
[INFO ] 2024-10-14 16:06:25.697 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 16:06:25.710 - [测试主从合并内嵌数组没有关联键(100)][43c4ef9f-a66e-4231-a219-06ecb1984718] - Node 43c4ef9f-a66e-4231-a219-06ecb1984718[43c4ef9f-a66e-4231-a219-06ecb1984718] running status set to false 
[INFO ] 2024-10-14 16:06:25.712 - [测试主从合并内嵌数组没有关联键(100)][43c4ef9f-a66e-4231-a219-06ecb1984718] - Node 43c4ef9f-a66e-4231-a219-06ecb1984718[43c4ef9f-a66e-4231-a219-06ecb1984718] schema data cleaned 
[INFO ] 2024-10-14 16:06:25.712 - [测试主从合并内嵌数组没有关联键(100)][43c4ef9f-a66e-4231-a219-06ecb1984718] - Node 43c4ef9f-a66e-4231-a219-06ecb1984718[43c4ef9f-a66e-4231-a219-06ecb1984718] monitor closed 
[INFO ] 2024-10-14 16:06:25.712 - [测试主从合并内嵌数组没有关联键(100)][43c4ef9f-a66e-4231-a219-06ecb1984718] - Node 43c4ef9f-a66e-4231-a219-06ecb1984718[43c4ef9f-a66e-4231-a219-06ecb1984718] close complete, cost 4 ms 
[INFO ] 2024-10-14 16:06:25.719 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-7bb81a36-a41b-485e-8bd6-d013c4382621 
[INFO ] 2024-10-14 16:06:25.720 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-7bb81a36-a41b-485e-8bd6-d013c4382621 
[INFO ] 2024-10-14 16:06:25.720 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 16:06:25.722 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 16:06:25.722 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 16:06:25.723 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 37 ms 
[INFO ] 2024-10-14 16:06:25.724 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:06:25.724 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:06:25.724 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:07:10.899 - [测试主从合并内嵌数组没有关联键(100)][b23c8538-e9ed-401a-b754-169b668866d1] - Node b23c8538-e9ed-401a-b754-169b668866d1[b23c8538-e9ed-401a-b754-169b668866d1] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:07:10.899 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:10.899 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:10.899 - [测试主从合并内嵌数组没有关联键(100)][b23c8538-e9ed-401a-b754-169b668866d1] - Node b23c8538-e9ed-401a-b754-169b668866d1[b23c8538-e9ed-401a-b754-169b668866d1] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 16:07:10.899 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:07:10.899 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:07:10.903 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 16:07:13.908 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:13.908 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:13.908 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:07:13.909 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 16:07:13.910 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node js_processor(master增强JS: 95ca8e4b-34cb-4332-b86c-84f6fb71b63e) enable batch process 
[INFO ] 2024-10-14 16:07:13.911 - [测试主从合并内嵌数组没有关联键(100)][409c053e-e469-4f95-a99f-2aeb587685f1] - Node 409c053e-e469-4f95-a99f-2aeb587685f1[409c053e-e469-4f95-a99f-2aeb587685f1] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:07:13.912 - [测试主从合并内嵌数组没有关联键(100)][409c053e-e469-4f95-a99f-2aeb587685f1] - Node 409c053e-e469-4f95-a99f-2aeb587685f1[409c053e-e469-4f95-a99f-2aeb587685f1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:07:15.737 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 16:07:15.753 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:07:15.753 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:07:15.753 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 16:07:15.754 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 16:07:15.754 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 17 ms 
[INFO ] 2024-10-14 16:07:20.481 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 16:07:20.492 - [测试主从合并内嵌数组没有关联键(100)][b23c8538-e9ed-401a-b754-169b668866d1] - Node b23c8538-e9ed-401a-b754-169b668866d1[b23c8538-e9ed-401a-b754-169b668866d1] running status set to false 
[INFO ] 2024-10-14 16:07:20.492 - [测试主从合并内嵌数组没有关联键(100)][b23c8538-e9ed-401a-b754-169b668866d1] - Node b23c8538-e9ed-401a-b754-169b668866d1[b23c8538-e9ed-401a-b754-169b668866d1] schema data cleaned 
[INFO ] 2024-10-14 16:07:20.492 - [测试主从合并内嵌数组没有关联键(100)][b23c8538-e9ed-401a-b754-169b668866d1] - Node b23c8538-e9ed-401a-b754-169b668866d1[b23c8538-e9ed-401a-b754-169b668866d1] monitor closed 
[INFO ] 2024-10-14 16:07:20.497 - [测试主从合并内嵌数组没有关联键(100)][b23c8538-e9ed-401a-b754-169b668866d1] - Node b23c8538-e9ed-401a-b754-169b668866d1[b23c8538-e9ed-401a-b754-169b668866d1] close complete, cost 2 ms 
[INFO ] 2024-10-14 16:07:20.497 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-aa38b730-4d4c-41b9-9497-e38664acc9de 
[INFO ] 2024-10-14 16:07:20.497 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-aa38b730-4d4c-41b9-9497-e38664acc9de 
[INFO ] 2024-10-14 16:07:20.497 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 16:07:20.500 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 16:07:20.500 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 16:07:20.502 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 19 ms 
[INFO ] 2024-10-14 16:07:20.503 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:07:20.503 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:07:20.711 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:07:20.713 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:20.714 - [测试主从合并内嵌数组没有关联键(100)][7658b792-e6d7-4041-8205-ab94fc529e57] - Node 7658b792-e6d7-4041-8205-ab94fc529e57[7658b792-e6d7-4041-8205-ab94fc529e57] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:07:20.715 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:20.715 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 16:07:20.715 - [测试主从合并内嵌数组没有关联键(100)][7658b792-e6d7-4041-8205-ab94fc529e57] - Node 7658b792-e6d7-4041-8205-ab94fc529e57[7658b792-e6d7-4041-8205-ab94fc529e57] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 16:07:20.715 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:07:20.717 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 16:07:22.692 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] running status set to false 
[INFO ] 2024-10-14 16:07:22.693 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:07:22.693 - [测试主从合并内嵌数组没有关联键(100)][master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0258048b-b589-4d8b-98b8-0562ce079035 
[INFO ] 2024-10-14 16:07:22.693 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] schema data cleaned 
[INFO ] 2024-10-14 16:07:22.693 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] monitor closed 
[INFO ] 2024-10-14 16:07:22.693 - [测试主从合并内嵌数组没有关联键(100)][master] - Node master[0258048b-b589-4d8b-98b8-0562ce079035] close complete, cost 30 ms 
[INFO ] 2024-10-14 16:07:25.640 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 16:07:25.640 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:07:25.640 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:07:25.641 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 16:07:25.641 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 16:07:25.846 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 18 ms 
[INFO ] 2024-10-14 16:07:30.416 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] running status set to false 
[INFO ] 2024-10-14 16:07:30.425 - [测试主从合并内嵌数组没有关联键(100)][409c053e-e469-4f95-a99f-2aeb587685f1] - Node 409c053e-e469-4f95-a99f-2aeb587685f1[409c053e-e469-4f95-a99f-2aeb587685f1] running status set to false 
[INFO ] 2024-10-14 16:07:30.425 - [测试主从合并内嵌数组没有关联键(100)][409c053e-e469-4f95-a99f-2aeb587685f1] - Node 409c053e-e469-4f95-a99f-2aeb587685f1[409c053e-e469-4f95-a99f-2aeb587685f1] schema data cleaned 
[INFO ] 2024-10-14 16:07:30.425 - [测试主从合并内嵌数组没有关联键(100)][409c053e-e469-4f95-a99f-2aeb587685f1] - Node 409c053e-e469-4f95-a99f-2aeb587685f1[409c053e-e469-4f95-a99f-2aeb587685f1] monitor closed 
[INFO ] 2024-10-14 16:07:30.425 - [测试主从合并内嵌数组没有关联键(100)][409c053e-e469-4f95-a99f-2aeb587685f1] - Node 409c053e-e469-4f95-a99f-2aeb587685f1[409c053e-e469-4f95-a99f-2aeb587685f1] close complete, cost 0 ms 
[INFO ] 2024-10-14 16:07:30.427 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-f7c9632f-f8e0-4e71-ac46-45db6956b090 
[INFO ] 2024-10-14 16:07:30.427 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - PDK connector node released: ScriptExecutor-mysql3306-f7c9632f-f8e0-4e71-ac46-45db6956b090 
[INFO ] 2024-10-14 16:07:30.429 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-95ca8e4b-34cb-4332-b86c-84f6fb71b63e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-14 16:07:30.429 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] schema data cleaned 
[INFO ] 2024-10-14 16:07:30.429 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] monitor closed 
[INFO ] 2024-10-14 16:07:30.429 - [测试主从合并内嵌数组没有关联键(100)][master增强JS] - Node master增强JS[95ca8e4b-34cb-4332-b86c-84f6fb71b63e] close complete, cost 14 ms 
[INFO ] 2024-10-14 16:07:30.431 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:07:30.431 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:07:30.431 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:07:30.582 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:30.582 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] start preload schema,table counts: 1 
[INFO ] 2024-10-14 16:07:30.585 - [测试主从合并内嵌数组没有关联键(100)][6e85a4c3-da30-4aae-b1ec-763445b7e144] - Node 6e85a4c3-da30-4aae-b1ec-763445b7e144[6e85a4c3-da30-4aae-b1ec-763445b7e144] start preload schema,table counts: 0 
[INFO ] 2024-10-14 16:07:30.585 - [测试主从合并内嵌数组没有关联键(100)][6e85a4c3-da30-4aae-b1ec-763445b7e144] - Node 6e85a4c3-da30-4aae-b1ec-763445b7e144[6e85a4c3-da30-4aae-b1ec-763445b7e144] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 16:07:30.585 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] preload schema finished, cost 2 ms 
[INFO ] 2024-10-14 16:07:30.585 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] preload schema finished, cost 2 ms 
[INFO ] 2024-10-14 16:07:30.586 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node js_processor(slave增强JS: 2f7dbe11-fed7-4333-94be-fe8e03309c68) enable batch process 
[INFO ] 2024-10-14 16:07:30.600 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 16:07:30.600 - [测试主从合并内嵌数组没有关联键(100)][7658b792-e6d7-4041-8205-ab94fc529e57] - Node 7658b792-e6d7-4041-8205-ab94fc529e57[7658b792-e6d7-4041-8205-ab94fc529e57] running status set to false 
[INFO ] 2024-10-14 16:07:30.601 - [测试主从合并内嵌数组没有关联键(100)][7658b792-e6d7-4041-8205-ab94fc529e57] - Node 7658b792-e6d7-4041-8205-ab94fc529e57[7658b792-e6d7-4041-8205-ab94fc529e57] schema data cleaned 
[INFO ] 2024-10-14 16:07:30.601 - [测试主从合并内嵌数组没有关联键(100)][7658b792-e6d7-4041-8205-ab94fc529e57] - Node 7658b792-e6d7-4041-8205-ab94fc529e57[7658b792-e6d7-4041-8205-ab94fc529e57] monitor closed 
[INFO ] 2024-10-14 16:07:30.601 - [测试主从合并内嵌数组没有关联键(100)][7658b792-e6d7-4041-8205-ab94fc529e57] - Node 7658b792-e6d7-4041-8205-ab94fc529e57[7658b792-e6d7-4041-8205-ab94fc529e57] close complete, cost 1 ms 
[INFO ] 2024-10-14 16:07:30.620 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-3a86e01d-5157-47bf-b813-0257a34b225d 
[INFO ] 2024-10-14 16:07:30.620 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-3a86e01d-5157-47bf-b813-0257a34b225d 
[INFO ] 2024-10-14 16:07:30.620 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 16:07:30.623 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 16:07:30.624 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 16:07:30.624 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 24 ms 
[INFO ] 2024-10-14 16:07:30.625 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:07:30.625 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:07:30.625 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-14 16:07:35.479 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] running status set to false 
[INFO ] 2024-10-14 16:07:35.485 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:07:35.485 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3 
[INFO ] 2024-10-14 16:07:35.486 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] schema data cleaned 
[INFO ] 2024-10-14 16:07:35.486 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] monitor closed 
[INFO ] 2024-10-14 16:07:35.689 - [测试主从合并内嵌数组没有关联键(100)][slave_not_arr_index2] - Node slave[11d82b86-70d5-4d15-9a9a-3f7bcfd3b8f3] close complete, cost 43 ms 
[INFO ] 2024-10-14 16:07:40.444 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] running status set to false 
[INFO ] 2024-10-14 16:07:40.444 - [测试主从合并内嵌数组没有关联键(100)][6e85a4c3-da30-4aae-b1ec-763445b7e144] - Node 6e85a4c3-da30-4aae-b1ec-763445b7e144[6e85a4c3-da30-4aae-b1ec-763445b7e144] running status set to false 
[INFO ] 2024-10-14 16:07:40.444 - [测试主从合并内嵌数组没有关联键(100)][6e85a4c3-da30-4aae-b1ec-763445b7e144] - Node 6e85a4c3-da30-4aae-b1ec-763445b7e144[6e85a4c3-da30-4aae-b1ec-763445b7e144] schema data cleaned 
[INFO ] 2024-10-14 16:07:40.445 - [测试主从合并内嵌数组没有关联键(100)][6e85a4c3-da30-4aae-b1ec-763445b7e144] - Node 6e85a4c3-da30-4aae-b1ec-763445b7e144[6e85a4c3-da30-4aae-b1ec-763445b7e144] monitor closed 
[INFO ] 2024-10-14 16:07:40.445 - [测试主从合并内嵌数组没有关联键(100)][6e85a4c3-da30-4aae-b1ec-763445b7e144] - Node 6e85a4c3-da30-4aae-b1ec-763445b7e144[6e85a4c3-da30-4aae-b1ec-763445b7e144] close complete, cost 1 ms 
[INFO ] 2024-10-14 16:07:40.447 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-5f9a9880-52d9-4651-8d9b-dbb986fcc297 
[INFO ] 2024-10-14 16:07:40.447 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - PDK connector node released: ScriptExecutor-mysql3307-5f9a9880-52d9-4651-8d9b-dbb986fcc297 
[INFO ] 2024-10-14 16:07:40.447 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - [ScriptExecutorsManager-6708c1f582af0a589c4e61f9-2f7dbe11-fed7-4333-94be-fe8e03309c68-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-14 16:07:40.448 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] schema data cleaned 
[INFO ] 2024-10-14 16:07:40.448 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] monitor closed 
[INFO ] 2024-10-14 16:07:40.449 - [测试主从合并内嵌数组没有关联键(100)][slave增强JS] - Node slave增强JS[2f7dbe11-fed7-4333-94be-fe8e03309c68] close complete, cost 13 ms 
[INFO ] 2024-10-14 16:07:40.450 - [测试主从合并内嵌数组没有关联键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-14 16:07:40.450 - [测试主从合并内嵌数组没有关联键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-14 16:07:40.655 - [测试主从合并内嵌数组没有关联键(100)] - Stopped task aspect(s) 
