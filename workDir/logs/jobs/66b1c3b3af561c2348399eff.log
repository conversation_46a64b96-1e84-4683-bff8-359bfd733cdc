[INFO ] 2024-08-07 18:29:31.941 - [任务 2] - Task initialization... 
[INFO ] 2024-08-07 18:29:31.970 - [任务 2] - Start task milestones: 66b1c3b3af561c2348399eff(任务 2) 
[INFO ] 2024-08-07 18:29:32.286 - [任务 2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-07 18:29:32.288 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-07 18:29:32.408 - [任务 2][Customer_back] - Node Customer_back[123a332e-f8b1-4b2a-b6d4-8fdea07e567d] start preload schema,table counts: 1 
[INFO ] 2024-08-07 18:29:32.409 - [任务 2][TestCustomer] - Node TestCustomer[d962f3ee-5ee6-4928-b872-abd55d2ff287] start preload schema,table counts: 1 
[INFO ] 2024-08-07 18:29:32.409 - [任务 2][TestCustomer] - Node TestCustomer[d962f3ee-5ee6-4928-b872-abd55d2ff287] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 18:29:32.409 - [任务 2][Customer_back] - Node Customer_back[123a332e-f8b1-4b2a-b6d4-8fdea07e567d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 18:29:33.237 - [任务 2][TestCustomer] - Source node "TestCustomer" read batch size: 100 
[INFO ] 2024-08-07 18:29:33.238 - [任务 2][TestCustomer] - Source node "TestCustomer" event queue capacity: 200 
[INFO ] 2024-08-07 18:29:33.239 - [任务 2][TestCustomer] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-07 18:29:33.251 - [任务 2][TestCustomer] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":306945425,"gtidSet":""} 
[INFO ] 2024-08-07 18:29:33.312 - [任务 2][TestCustomer] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-07 18:29:33.312 - [任务 2][TestCustomer] - Initial sync started 
[INFO ] 2024-08-07 18:29:33.312 - [任务 2][TestCustomer] - Starting batch read, table name: TestCustomer 
[INFO ] 2024-08-07 18:29:33.317 - [任务 2][TestCustomer] - Table TestCustomer is going to be initial synced 
[WARN ] 2024-08-07 18:29:33.368 - [任务 2][TestCustomer] - Query 'TestCustomer' snapshot row size failed: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'TestCustomer'  count failed: Table 'testerp.testcustomer' doesn't exist
java.util.concurrent.CompletionException: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'TestCustomer'  count failed: Table 'testerp.testcustomer' doesn't exist
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1643)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1632)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'TestCustomer'  count failed: Table 'testerp.testcustomer' doesn't exist
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doBatchCountFunction(HazelcastSourcePdkDataNode.java:1307)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doAsyncTableCount$42(HazelcastSourcePdkBaseNode.java:1359)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	... 7 more
Caused by: io.tapdata.exception.NodeException: Query table 'TestCustomer'  count failed: Table 'testerp.testcustomer' doesn't exist
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doBatchCountFunction$56(HazelcastSourcePdkDataNode.java:1310)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 11 more
Caused by: io.tapdata.exception.NodeException: Query table 'TestCustomer'  count failed: Table 'testerp.testcustomer' doesn't exist
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$55(HazelcastSourcePdkDataNode.java:1337)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 17 more
Caused by: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.common.CommonDbConnector.batchCount(CommonDbConnector.java:360)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$55(HazelcastSourcePdkDataNode.java:1326)
	... 18 more
 
[INFO ] 2024-08-07 18:29:33.369 - [任务 2][TestCustomer] - Initial sync completed 
[INFO ] 2024-08-07 18:29:33.411 - [任务 2][TestCustomer] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist 
[ERROR] 2024-08-07 18:29:33.412 - [任务 2][TestCustomer] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:444)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:257)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:258)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:179)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:340)
	... 10 more
Caused by: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$12(HazelcastSourcePdkDataNode.java:348)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Table 'testerp.testcustomer' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:568)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:422)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[INFO ] 2024-08-07 18:29:33.444 - [任务 2][TestCustomer] - Job suspend in error handle 
[INFO ] 2024-08-07 18:29:33.449 - [任务 2][Customer_back] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-07 18:29:33.846 - [任务 2][TestCustomer] - Node TestCustomer[d962f3ee-5ee6-4928-b872-abd55d2ff287] running status set to false 
[INFO ] 2024-08-07 18:29:33.846 - [任务 2][TestCustomer] - PDK connector node stopped: HazelcastSourcePdkDataNode-d962f3ee-5ee6-4928-b872-abd55d2ff287 
[INFO ] 2024-08-07 18:29:33.847 - [任务 2][TestCustomer] - PDK connector node released: HazelcastSourcePdkDataNode-d962f3ee-5ee6-4928-b872-abd55d2ff287 
[INFO ] 2024-08-07 18:29:33.847 - [任务 2][TestCustomer] - Node TestCustomer[d962f3ee-5ee6-4928-b872-abd55d2ff287] schema data cleaned 
[INFO ] 2024-08-07 18:29:33.851 - [任务 2][TestCustomer] - Node TestCustomer[d962f3ee-5ee6-4928-b872-abd55d2ff287] monitor closed 
[INFO ] 2024-08-07 18:29:33.851 - [任务 2][TestCustomer] - Node TestCustomer[d962f3ee-5ee6-4928-b872-abd55d2ff287] close complete, cost 35 ms 
[INFO ] 2024-08-07 18:29:33.862 - [任务 2][Customer_back] - Node Customer_back[123a332e-f8b1-4b2a-b6d4-8fdea07e567d] running status set to false 
[INFO ] 2024-08-07 18:29:33.863 - [任务 2][Customer_back] - PDK connector node stopped: HazelcastTargetPdkDataNode-123a332e-f8b1-4b2a-b6d4-8fdea07e567d 
[INFO ] 2024-08-07 18:29:33.863 - [任务 2][Customer_back] - PDK connector node released: HazelcastTargetPdkDataNode-123a332e-f8b1-4b2a-b6d4-8fdea07e567d 
[INFO ] 2024-08-07 18:29:33.863 - [任务 2][Customer_back] - Node Customer_back[123a332e-f8b1-4b2a-b6d4-8fdea07e567d] schema data cleaned 
[INFO ] 2024-08-07 18:29:33.864 - [任务 2][Customer_back] - Node Customer_back[123a332e-f8b1-4b2a-b6d4-8fdea07e567d] monitor closed 
[INFO ] 2024-08-07 18:29:33.864 - [任务 2][Customer_back] - Node Customer_back[123a332e-f8b1-4b2a-b6d4-8fdea07e567d] close complete, cost 12 ms 
[INFO ] 2024-08-07 18:29:34.308 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-07 18:29:34.323 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-07 18:29:34.323 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@32f3dedf 
[INFO ] 2024-08-07 18:29:34.464 - [任务 2] - Stop task milestones: 66b1c3b3af561c2348399eff(任务 2)  
[INFO ] 2024-08-07 18:29:34.481 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-08-07 18:29:34.482 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-08-07 18:29:34.527 - [任务 2] - Remove memory task client succeed, task: 任务 2[66b1c3b3af561c2348399eff] 
[INFO ] 2024-08-07 18:29:34.530 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66b1c3b3af561c2348399eff] 
