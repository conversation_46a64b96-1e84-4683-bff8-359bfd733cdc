[WARN ] 2024-07-30 16:10:47.772 - [任务 1] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=5ece3e71a4b54657ac105d31e33554f0b7937ddf42c04cf78e2b151895606424&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a6fbd3f48b00590f3f5108%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@5a39c2e6, CDC=io.tapdata.milestone.entity.MilestoneEntity@698a79af, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@79ccd452, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@63b9d403, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@77831460, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@4d7e4c3e}, attrs.nodeMilestones={502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@520f6f87, NODE=io.tapdata.milestone.entity.MilestoneEntity@7f3bb009, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@10f48b9a, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@2f543a06}, 15fe52d3-9923-4b60-822d-05fc72a2d5cd={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@60e1b3c, NODE=io.tapdata.milestone.entity.MilestoneEntity@2417580c, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@108bbf4f, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@1e812352}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=1bc6490c-f3d5-48ba-be51-45dded02f150}: 无效参数: task not found 
[INFO ] 2024-07-30 16:10:48.069 - [任务 1][TESTPO] - Node TESTPO[502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2] running status set to false 
[INFO ] 2024-07-30 16:10:48.165 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-30 16:10:48.171 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2 
[INFO ] 2024-07-30 16:10:48.173 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2 
[INFO ] 2024-07-30 16:10:48.174 - [任务 1][TESTPO] - Node TESTPO[502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2] schema data cleaned 
[INFO ] 2024-07-30 16:10:48.178 - [任务 1][TESTPO] - Node TESTPO[502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2] monitor closed 
[INFO ] 2024-07-30 16:10:48.191 - [任务 1][TESTPO] - Node TESTPO[502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2] close complete, cost 138 ms 
[INFO ] 2024-07-30 16:10:48.193 - [任务 1][TestPO] - Node TestPO[15fe52d3-9923-4b60-822d-05fc72a2d5cd] running status set to false 
[INFO ] 2024-07-30 16:10:48.433 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-15fe52d3-9923-4b60-822d-05fc72a2d5cd 
[INFO ] 2024-07-30 16:10:48.434 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-15fe52d3-9923-4b60-822d-05fc72a2d5cd 
[INFO ] 2024-07-30 16:10:48.434 - [任务 1][TestPO] - Node TestPO[15fe52d3-9923-4b60-822d-05fc72a2d5cd] schema data cleaned 
[INFO ] 2024-07-30 16:10:48.435 - [任务 1][TestPO] - Node TestPO[15fe52d3-9923-4b60-822d-05fc72a2d5cd] monitor closed 
[INFO ] 2024-07-30 16:10:48.642 - [任务 1][TestPO] - Node TestPO[15fe52d3-9923-4b60-822d-05fc72a2d5cd] close complete, cost 247 ms 
[WARN ] 2024-07-30 16:10:52.788 - [任务 1] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=f7bfafdcdbab440f81829972faabe0b8e6a0052419c04707a34c2087eafe5f1f&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a6fbd3f48b00590f3f5108%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@5a39c2e6, CDC=io.tapdata.milestone.entity.MilestoneEntity@698a79af, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@79ccd452, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@63b9d403, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@77831460, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@4d7e4c3e}, attrs.nodeMilestones={502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@520f6f87, NODE=io.tapdata.milestone.entity.MilestoneEntity@7f3bb009, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@10f48b9a, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@2f543a06}, 15fe52d3-9923-4b60-822d-05fc72a2d5cd={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@60e1b3c, NODE=io.tapdata.milestone.entity.MilestoneEntity@2417580c, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@108bbf4f, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@1e812352}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=cf8bc351-5ab4-4296-81db-09b003581e91}: 无效参数: task not found 
[INFO ] 2024-07-30 16:10:52.800 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 16:10:52.816 - [任务 1] - Stop task milestones: 66a6fbd3f48b00590f3f5108(任务 1)  
[WARN ] 2024-07-30 16:10:52.842 - [任务 1] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=f7bfafdcdbab440f81829972faabe0b8e6a0052419c04707a34c2087eafe5f1f&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a6fbd3f48b00590f3f5108%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@5a39c2e6, CDC=io.tapdata.milestone.entity.MilestoneEntity@698a79af, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@79ccd452, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@63b9d403, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@77831460, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@4d7e4c3e}, attrs.nodeMilestones={502b67c9-5f7c-4ed1-86b3-0a13fc9f7ad2={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@520f6f87, NODE=io.tapdata.milestone.entity.MilestoneEntity@7f3bb009, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@10f48b9a, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@2f543a06}, 15fe52d3-9923-4b60-822d-05fc72a2d5cd={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@60e1b3c, NODE=io.tapdata.milestone.entity.MilestoneEntity@2417580c, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@108bbf4f, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@1e812352}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=70ab511f-c799-4523-89bc-d1537a01a3d9}: 无效参数: task not found 
[INFO ] 2024-07-30 16:10:52.966 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 16:10:52.966 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-30 16:10:53.327 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 16:10:53.327 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@799c0ffa 
[INFO ] 2024-07-30 16:10:53.331 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 16:10:53.331 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a6fbd3f48b00590f3f5108] 
[INFO ] 2024-07-30 16:10:53.331 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a6fbd3f48b00590f3f5108] 
