[INFO ] 2024-04-05 14:11:41.710 - [任务 50] - Start task milestones: 660d05473d62860f2c687aa3(任务 50) 
[INFO ] 2024-04-05 14:11:41.712 - [任务 50] - Task initialization... 
[INFO ] 2024-04-05 14:11:41.712 - [任务 50] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-05 14:11:41.712 - [任务 50] - The engine receives 任务 50 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-05 14:11:41.928 - [任务 50][test1] - Node test1[5df975f1-d915-463a-9a99-80a360ac7efc] start preload schema,table counts: 1 
[INFO ] 2024-04-05 14:11:41.929 - [任务 50][test21] - Node test21[691806d1-a5cf-4ea0-8cbf-9173e1abfe9e] start preload schema,table counts: 1 
[INFO ] 2024-04-05 14:11:42.181 - [任务 50][test1] - Node test1[5df975f1-d915-463a-9a99-80a360ac7efc] preload schema finished, cost 238 ms 
[INFO ] 2024-04-05 14:11:42.183 - [任务 50][test21] - Node test21[691806d1-a5cf-4ea0-8cbf-9173e1abfe9e] preload schema finished, cost 237 ms 
[INFO ] 2024-04-05 14:11:45.683 - [任务 50][test21] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-05 14:11:45.684 - [任务 50][test21] - Table "test.test21" exists, skip auto create table 
[INFO ] 2024-04-05 14:11:45.887 - [任务 50][test21] - The table test21 has already exist. 
[INFO ] 2024-04-05 14:11:51.030 - [任务 50][test1] - Source node "test1" read batch size: 100 
[INFO ] 2024-04-05 14:11:51.033 - [任务 50][test1] - Source node "test1" event queue capacity: 200 
[INFO ] 2024-04-05 14:11:51.033 - [任务 50][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-05 14:11:51.046 - [任务 50][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-05 14:11:51.249 - [任务 50][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-05 14:11:51.256 - [任务 50][test1] - Initial sync started 
[INFO ] 2024-04-05 14:11:51.257 - [任务 50][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-04-05 14:11:51.469 - [任务 50][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-04-05 14:11:57.799 - [任务 50][test1] - Initial sync completed 
[INFO ] 2024-04-05 14:11:57.803 - [任务 50][test1] - Incremental sync starting... 
[INFO ] 2024-04-05 14:11:57.805 - [任务 50][test1] - Initial sync completed 
[INFO ] 2024-04-05 14:11:57.805 - [任务 50][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-04-05 14:14:26.411 - [任务 50] - Stop task milestones: 660d05473d62860f2c687aa3(任务 50)  
[INFO ] 2024-04-05 14:14:26.853 - [任务 50][test1] - Node test1[5df975f1-d915-463a-9a99-80a360ac7efc] running status set to false 
[ERROR] 2024-04-05 14:14:26.854 - [任务 50][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:326)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:693)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	... 18 more

[INFO ] 2024-04-05 14:14:26.870 - [任务 50][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df975f1-d915-463a-9a99-80a360ac7efc 
[INFO ] 2024-04-05 14:14:26.870 - [任务 50][test1] - PDK connector node released: HazelcastSourcePdkDataNode-5df975f1-d915-463a-9a99-80a360ac7efc 
[INFO ] 2024-04-05 14:14:26.872 - [任务 50][test1] - Node test1[5df975f1-d915-463a-9a99-80a360ac7efc] schema data cleaned 
[INFO ] 2024-04-05 14:14:26.872 - [任务 50][test1] - Node test1[5df975f1-d915-463a-9a99-80a360ac7efc] monitor closed 
[INFO ] 2024-04-05 14:14:26.879 - [任务 50][test1] - Node test1[5df975f1-d915-463a-9a99-80a360ac7efc] close complete, cost 58 ms 
[INFO ] 2024-04-05 14:14:26.879 - [任务 50][test21] - Node test21[691806d1-a5cf-4ea0-8cbf-9173e1abfe9e] running status set to false 
[INFO ] 2024-04-05 14:14:26.984 - [任务 50][test21] - PDK connector node stopped: HazelcastTargetPdkDataNode-691806d1-a5cf-4ea0-8cbf-9173e1abfe9e 
[INFO ] 2024-04-05 14:14:26.984 - [任务 50][test21] - PDK connector node released: HazelcastTargetPdkDataNode-691806d1-a5cf-4ea0-8cbf-9173e1abfe9e 
[INFO ] 2024-04-05 14:14:26.984 - [任务 50][test21] - Node test21[691806d1-a5cf-4ea0-8cbf-9173e1abfe9e] schema data cleaned 
[INFO ] 2024-04-05 14:14:26.986 - [任务 50][test21] - Node test21[691806d1-a5cf-4ea0-8cbf-9173e1abfe9e] monitor closed 
[INFO ] 2024-04-05 14:14:26.986 - [任务 50][test21] - Node test21[691806d1-a5cf-4ea0-8cbf-9173e1abfe9e] close complete, cost 109 ms 
[INFO ] 2024-04-05 14:14:30.999 - [任务 50] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-05 14:14:31.000 - [任务 50] - Stopped task aspect(s) 
[INFO ] 2024-04-05 14:14:31.000 - [任务 50] - Snapshot order controller have been removed 
[INFO ] 2024-04-05 14:14:31.075 - [任务 50] - Remove memory task client succeed, task: 任务 50[660d05473d62860f2c687aa3] 
[INFO ] 2024-04-05 14:14:31.076 - [任务 50] - Destroy memory task client cache succeed, task: 任务 50[660d05473d62860f2c687aa3] 
