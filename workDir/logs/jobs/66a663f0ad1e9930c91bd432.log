[INFO ] 2024-07-28 23:31:39.975 - [Heartbeat-TargetMongo] - Start task milestones: 66a663f0ad1e9930c91bd432(Heartbeat-TargetMongo) 
[INFO ] 2024-07-28 23:31:40.904 - [Heartbeat-TargetMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-28 23:31:40.911 - [Heartbeat-TargetMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 23:31:40.913 - [Heartbeat-TargetMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@b83847e 
[INFO ] 2024-07-28 23:31:41.025 - [Heartbeat-TargetMongo] - Stop task milestones: 66a663f0ad1e9930c91bd432(Heartbeat-TargetMongo)  
[INFO ] 2024-07-28 23:31:41.042 - [Heartbeat-TargetMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 23:31:41.043 - [Heartbeat-TargetMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 23:31:41.069 - [Heartbeat-TargetMongo] - Node [id 1e87c0cf-d6f3-4950-9e42-4caa8a84ba64, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 1e87c0cf-d6f3-4950-9e42-4caa8a84ba64, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 23:36:12.250 - [Heartbeat-TargetMongo] - Start task milestones: 66a663f0ad1e9930c91bd432(Heartbeat-TargetMongo) 
[INFO ] 2024-07-28 23:36:12.383 - [Heartbeat-TargetMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 23:36:12.383 - [Heartbeat-TargetMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 23:36:12.460 - [Heartbeat-TargetMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@22859db7 
[INFO ] 2024-07-28 23:36:12.521 - [Heartbeat-TargetMongo] - Stop task milestones: 66a663f0ad1e9930c91bd432(Heartbeat-TargetMongo)  
[INFO ] 2024-07-28 23:36:12.521 - [Heartbeat-TargetMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 23:36:12.521 - [Heartbeat-TargetMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 23:36:12.553 - [Heartbeat-TargetMongo] - Node [id 1e87c0cf-d6f3-4950-9e42-4caa8a84ba64, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 1e87c0cf-d6f3-4950-9e42-4caa8a84ba64, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

