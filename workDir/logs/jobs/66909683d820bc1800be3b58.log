[INFO ] 2024-07-15 03:35:34.300 - [Heartbeat-SouceMysql] - Start task milestones: 66909683d820bc1800be3b58(Heartbeat-SouceMysql) 
[INFO ] 2024-07-15 03:35:34.581 - [Heartbeat-SouceMysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-15 03:35:34.680 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 03:35:34.775 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e480cd18-1fb1-4c7a-9d96-bb436989b2d1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 03:35:34.775 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2596742c-85eb-4322-a27c-4bbecd84d956] start preload schema,table counts: 1 
[INFO ] 2024-07-15 03:35:34.775 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e480cd18-1fb1-4c7a-9d96-bb436989b2d1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 03:35:34.775 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2596742c-85eb-4322-a27c-4bbecd84d956] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 03:35:34.957 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 03:35:34.957 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 03:35:34.958 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 03:35:34.958 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1720751749347,"lastTimes":1720985424045,"lastTN":358,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":24779,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 03:35:35.150 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1720751749347,"lastTimes":1720985424045,"lastTN":358,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":24779,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 03:35:35.150 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 03:35:35.158 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 03:35:36.039 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 03:39:13.932 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2596742c-85eb-4322-a27c-4bbecd84d956] running status set to false 
[INFO ] 2024-07-15 03:39:13.944 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-15 03:39:13.959 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-2596742c-85eb-4322-a27c-4bbecd84d956 
[INFO ] 2024-07-15 03:39:13.960 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-2596742c-85eb-4322-a27c-4bbecd84d956 
[INFO ] 2024-07-15 03:39:13.961 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2596742c-85eb-4322-a27c-4bbecd84d956] schema data cleaned 
[INFO ] 2024-07-15 03:39:13.961 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2596742c-85eb-4322-a27c-4bbecd84d956] monitor closed 
[INFO ] 2024-07-15 03:39:13.966 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2596742c-85eb-4322-a27c-4bbecd84d956] close complete, cost 39 ms 
[INFO ] 2024-07-15 03:39:13.996 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e480cd18-1fb1-4c7a-9d96-bb436989b2d1] running status set to false 
