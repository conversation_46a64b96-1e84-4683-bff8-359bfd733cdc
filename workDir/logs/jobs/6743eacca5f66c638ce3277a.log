[INFO ] 2024-11-25 11:26:24.332 - [任务 21] - Task initialization... 
[INFO ] 2024-11-25 11:26:24.338 - [任务 21] - Start task milestones: 6743eacca5f66c638ce3277a(任务 21) 
[INFO ] 2024-11-25 11:26:24.945 - [任务 21] - No<PERSON> performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-11-25 11:26:26.102 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-25 11:26:26.103 - [任务 21][testMerge] - Node testMerge[274e48db-edfd-4bc1-a8f4-ca471f0eecba] start preload schema,table counts: 1 
[INFO ] 2024-11-25 11:26:26.103 - [任务 21][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] start preload schema,table counts: 3 
[INFO ] 2024-11-25 11:26:26.104 - [任务 21][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] start preload schema,table counts: 1 
[INFO ] 2024-11-25 11:26:26.106 - [任务 21][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] start preload schema,table counts: 1 
[INFO ] 2024-11-25 11:26:26.106 - [任务 21][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 11:26:26.113 - [任务 21][testMerge] - Node testMerge[274e48db-edfd-4bc1-a8f4-ca471f0eecba] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 11:26:26.121 - [任务 21][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 11:26:26.123 - [任务 21][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 11:26:26.127 - [任务 21][主从合并] - Node merge_table_processor(主从合并: cc5201ab-41bd-4f88-9898-4be94f2121c7) enable batch process 
[INFO ] 2024-11-25 11:26:26.331 - [任务 21][主从合并] - 
Merge lookup relation{
  test_cache_parent(9b0a9ec4-5bc4-47dd-976b-17307ca32e37)
    ->test_cache_child(3cc67342-2d6e-4c4a-b083-ccf77e2f58d9)
} 
[INFO ] 2024-11-25 11:26:27.083 - [任务 21][testMerge] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-25 11:26:27.163 - [任务 21][主从合并] - Create merge cache, node id: 3cc67342-2d6e-4c4a-b083-ccf77e2f58d9, imap name: 2068302776, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-25 11:26:27.252 - [任务 21][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: - Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert 
[ERROR] 2024-11-25 11:26:27.255 - [任务 21][主从合并] - - Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert <-- Error Message -->
- Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:252)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:169)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	...

<-- Full Stack Trace -->
- Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:252)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-11-25 11:26:27.270 - [任务 21][主从合并] - Job suspend in error handle 
[INFO ] 2024-11-25 11:26:27.270 - [任务 21][test_merge_no_primary_key_parent] - Source node "test_merge_no_primary_key_parent" read batch size: 100 
[INFO ] 2024-11-25 11:26:27.270 - [任务 21][test_merge_no_primary_key_parent] - Source node "test_merge_no_primary_key_parent" event queue capacity: 200 
[INFO ] 2024-11-25 11:26:27.270 - [任务 21][test_merge_no_primary_key_parent] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-25 11:26:27.364 - [任务 21][test_merge_no_primary_key_parent] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":987212,"gtidSet":""} 
[INFO ] 2024-11-25 11:26:27.366 - [任务 21][testMerge] - Table: testMerge already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2024-11-25 11:26:27.560 - [任务 21] - Node[test_merge_no_primary_key_parent] is waiting for running 
[INFO ] 2024-11-25 11:26:27.560 - [任务 21][test_merge_no_primary_key_child] - Source node "test_merge_no_primary_key_child" read batch size: 100 
[INFO ] 2024-11-25 11:26:27.560 - [任务 21][test_merge_no_primary_key_child] - Source node "test_merge_no_primary_key_child" event queue capacity: 200 
[INFO ] 2024-11-25 11:26:27.560 - [任务 21][test_merge_no_primary_key_child] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-25 11:26:27.561 - [任务 21][test_merge_no_primary_key_child] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":987212,"gtidSet":""} 
[INFO ] 2024-11-25 11:26:27.673 - [任务 21][test_merge_no_primary_key_child] - Initial sync started 
[INFO ] 2024-11-25 11:26:27.673 - [任务 21][test_merge_no_primary_key_child] - Starting batch read, table name: test_merge_no_primary_key_child 
[INFO ] 2024-11-25 11:26:27.673 - [任务 21][test_merge_no_primary_key_child] - Query snapshot row size completed: test_merge_no_primary_key_child(9b0a9ec4-5bc4-47dd-976b-17307ca32e37) 
[INFO ] 2024-11-25 11:26:27.674 - [任务 21][test_merge_no_primary_key_child] - Table test_merge_no_primary_key_child is going to be initial synced 
[INFO ] 2024-11-25 11:26:27.722 - [任务 21][test_merge_no_primary_key_child] - Initial sync completed 
[INFO ] 2024-11-25 11:26:27.722 - [任务 21][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] running status set to false 
[INFO ] 2024-11-25 11:26:27.750 - [任务 21][test_merge_no_primary_key_parent] - Initial sync started 
[INFO ] 2024-11-25 11:26:27.753 - [任务 21][test_merge_no_primary_key_parent] - Query snapshot row size completed: test_merge_no_primary_key_parent(3cc67342-2d6e-4c4a-b083-ccf77e2f58d9) 
[INFO ] 2024-11-25 11:26:27.754 - [任务 21][test_merge_no_primary_key_parent] - Initial sync completed 
[INFO ] 2024-11-25 11:26:27.762 - [任务 21][test_merge_no_primary_key_parent] - Incremental sync starting... 
[INFO ] 2024-11-25 11:26:27.768 - [任务 21][test_merge_no_primary_key_parent] - Incremental sync completed 
[INFO ] 2024-11-25 11:26:27.783 - [任务 21][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSourcePdkDataNode_3cc67342-2d6e-4c4a-b083-ccf77e2f58d9_1732505187039 
[INFO ] 2024-11-25 11:26:27.784 - [任务 21][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSourcePdkDataNode_3cc67342-2d6e-4c4a-b083-ccf77e2f58d9_1732505187039 
[INFO ] 2024-11-25 11:26:27.790 - [任务 21][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] schema data cleaned 
[INFO ] 2024-11-25 11:26:27.790 - [任务 21][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] monitor closed 
[INFO ] 2024-11-25 11:26:27.805 - [任务 21][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] close complete, cost 72 ms 
[INFO ] 2024-11-25 11:26:27.806 - [任务 21][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] running status set to false 
[INFO ] 2024-11-25 11:26:27.826 - [任务 21][test_merge_no_primary_key_child] - Incremental sync starting... 
[INFO ] 2024-11-25 11:26:27.838 - [任务 21][test_merge_no_primary_key_child] - Incremental sync completed 
[INFO ] 2024-11-25 11:26:27.845 - [任务 21][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSourcePdkDataNode_9b0a9ec4-5bc4-47dd-976b-17307ca32e37_1732505187342 
[INFO ] 2024-11-25 11:26:27.846 - [任务 21][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSourcePdkDataNode_9b0a9ec4-5bc4-47dd-976b-17307ca32e37_1732505187342 
[INFO ] 2024-11-25 11:26:27.846 - [任务 21][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] schema data cleaned 
[INFO ] 2024-11-25 11:26:27.847 - [任务 21][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] monitor closed 
[INFO ] 2024-11-25 11:26:27.850 - [任务 21][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] close complete, cost 44 ms 
[INFO ] 2024-11-25 11:26:27.850 - [任务 21][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] running status set to false 
[INFO ] 2024-11-25 11:26:27.861 - [任务 21][主从合并] - Destroy merge cache resource: 2068302776 
[INFO ] 2024-11-25 11:26:27.861 - [任务 21][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] schema data cleaned 
[INFO ] 2024-11-25 11:26:27.861 - [任务 21][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] monitor closed 
[INFO ] 2024-11-25 11:26:27.863 - [任务 21][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] close complete, cost 13 ms 
[INFO ] 2024-11-25 11:26:27.863 - [任务 21][testMerge] - Node testMerge[274e48db-edfd-4bc1-a8f4-ca471f0eecba] running status set to false 
[INFO ] 2024-11-25 11:26:27.886 - [任务 21] - Task [任务 21] cannot retry, reason: Task retry service not start 
[INFO ] 2024-11-25 11:26:27.886 - [任务 21][testMerge] - PDK connector node stopped: HazelcastTargetPdkDataNode_274e48db-edfd-4bc1-a8f4-ca471f0eecba_1732505186887 
[INFO ] 2024-11-25 11:26:27.887 - [任务 21][testMerge] - PDK connector node released: HazelcastTargetPdkDataNode_274e48db-edfd-4bc1-a8f4-ca471f0eecba_1732505186887 
[INFO ] 2024-11-25 11:26:27.887 - [任务 21][testMerge] - Node testMerge[274e48db-edfd-4bc1-a8f4-ca471f0eecba] schema data cleaned 
[INFO ] 2024-11-25 11:26:27.888 - [任务 21][testMerge] - Node testMerge[274e48db-edfd-4bc1-a8f4-ca471f0eecba] monitor closed 
[INFO ] 2024-11-25 11:26:28.095 - [任务 21][testMerge] - Node testMerge[274e48db-edfd-4bc1-a8f4-ca471f0eecba] close complete, cost 24 ms 
[INFO ] 2024-11-25 11:26:32.928 - [任务 21] - Task [任务 21] cannot retry, reason: Task retry service not start 
[INFO ] 2024-11-25 11:26:32.933 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-25 11:26:32.937 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@16a34e53 
[INFO ] 2024-11-25 11:26:33.054 - [任务 21] - Stop task milestones: 6743eacca5f66c638ce3277a(任务 21)  
[INFO ] 2024-11-25 11:26:33.061 - [任务 21] - Stopped task aspect(s) 
[INFO ] 2024-11-25 11:26:33.061 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2024-11-25 11:26:33.088 - [任务 21] - Remove memory task client succeed, task: 任务 21[6743eacca5f66c638ce3277a] 
[INFO ] 2024-11-25 11:26:33.090 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[6743eacca5f66c638ce3277a] 
[INFO ] 2024-11-25 15:24:30.321 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-e1ee736f-33ce-4fd9-8904-5082b782dbf4 complete, cost 1116ms 
[INFO ] 2024-11-25 15:24:35.995 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-319e3d90-eee2-4dba-8270-0540c42a97bb complete, cost 692ms 
[INFO ] 2024-11-25 15:24:42.024 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-3c4730c5-2875-460b-a6ff-505e705d828e complete, cost 639ms 
[INFO ] 2024-11-25 15:25:00.208 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-ba38a665-a791-47ee-ac9a-68e3c0730aba complete, cost 744ms 
[INFO ] 2024-11-25 15:25:01.047 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-b8a9728f-b389-47e7-bd5b-95488a9188b2 complete, cost 887ms 
[INFO ] 2024-11-25 15:25:05.612 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-3cd0f529-74aa-4ae1-bdb2-1ace4de40892 complete, cost 1023ms 
[INFO ] 2024-11-25 15:25:05.865 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-bc78bf9a-c42c-4630-94f0-35d444869e12 complete, cost 1192ms 
[INFO ] 2024-11-25 15:25:07.814 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-e49c51a1-5d23-4bc8-8a2b-1889c155c433 complete, cost 604ms 
[INFO ] 2024-11-25 15:25:16.619 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-7d99f01d-bb99-4cb2-a6ae-4139e8427e75 complete, cost 587ms 
[INFO ] 2024-11-25 15:25:22.489 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-688c5847-abf2-4f84-91e3-37db5160466e complete, cost 661ms 
[INFO ] 2024-11-25 15:25:24.283 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-28c8f597-89a6-4cf8-a8e4-deb5a2ef9d1b complete, cost 661ms 
[INFO ] 2024-11-25 15:25:26.203 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-40dac60a-6cc8-4bcd-802a-b17e86ccbea7 complete, cost 659ms 
[INFO ] 2024-11-25 15:25:34.644 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-926a0a01-d9d9-4f5a-a2ff-336f152baf80 complete, cost 606ms 
[INFO ] 2024-11-25 15:25:37.033 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-0ffb6a26-3d50-4643-9a7a-6a70eb4797b2 complete, cost 697ms 
[INFO ] 2024-11-25 15:25:38.081 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-79ed4630-dc72-4b32-a025-77b24743d5c3 complete, cost 641ms 
[INFO ] 2024-11-25 15:25:40.095 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-a798a858-477f-4c2b-a846-ebcb4823f09d complete, cost 679ms 
[INFO ] 2024-11-25 15:25:41.386 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-747e8c38-7ebd-4d0b-94f1-1249e89ef446 complete, cost 661ms 
[INFO ] 2024-11-25 15:25:42.968 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-281e5e50-fad5-4b24-af53-dae21604c33a complete, cost 622ms 
[INFO ] 2024-11-25 15:25:45.711 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-2747b00d-06fa-4ce8-b786-b411afdf863c complete, cost 646ms 
[INFO ] 2024-11-25 15:25:49.925 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-8b6df768-354d-43fd-823d-e15e25f9bf27 complete, cost 762ms 
[INFO ] 2024-11-25 15:25:50.035 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-f045792f-6ed1-4e6d-89e8-da37e74b0d01 complete, cost 628ms 
[INFO ] 2024-11-25 15:25:50.586 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-3cdacac5-e740-498e-adff-d840a40c8af6 complete, cost 1095ms 
[INFO ] 2024-11-25 15:25:50.831 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-36614728-56d9-40fe-8f64-09ce856773b1 complete, cost 1145ms 
[INFO ] 2024-11-25 15:25:50.961 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-22cf3c94-6642-468e-ad19-dc07baa0aea2 complete, cost 1174ms 
[INFO ] 2024-11-25 15:25:52.029 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-257f1ffd-9227-48f7-aef5-c3d9c72d4431 complete, cost 629ms 
[INFO ] 2024-11-25 15:26:03.354 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-cc6e8522-a70d-4d70-9ada-b198a2faeaca complete, cost 595ms 
[INFO ] 2024-11-25 15:26:07.332 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-ac45a80a-ad38-44ec-9ab1-8d754f5e4e0d complete, cost 660ms 
[INFO ] 2024-11-25 15:26:31.054 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-ad482e21-fb10-4347-978b-80fd44e7c473 complete, cost 595ms 
[INFO ] 2024-11-25 15:26:37.064 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-ec10463e-2a3e-4fca-b86f-80714fe8707e complete, cost 644ms 
[INFO ] 2024-11-25 15:26:39.274 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-5d3c1203-15f0-44f2-b03e-31f7199f1b0b complete, cost 734ms 
[INFO ] 2024-11-25 15:26:42.100 - [测试主从合并没主键] - Task initialization... 
[INFO ] 2024-11-25 15:26:42.258 - [测试主从合并没主键] - Start task milestones: 6743eacca5f66c638ce3277a(测试主从合并没主键) 
[INFO ] 2024-11-25 15:26:42.875 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-d980ea58-bdd0-4a64-985b-391b13077118 complete, cost 682ms 
[INFO ] 2024-11-25 15:26:43.081 - [测试主从合并没主键] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-11-25 15:26:44.098 - [测试主从合并没主键] - The engine receives 测试主从合并没主键 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-25 15:26:44.105 - [测试主从合并没主键][testMerge6] - Node testMerge6[274e48db-edfd-4bc1-a8f4-ca471f0eecba] start preload schema,table counts: 1 
[INFO ] 2024-11-25 15:26:44.106 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] start preload schema,table counts: 1 
[INFO ] 2024-11-25 15:26:44.106 - [测试主从合并没主键][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] start preload schema,table counts: 3 
[INFO ] 2024-11-25 15:26:44.106 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 15:26:44.106 - [测试主从合并没主键][testMerge6] - Node testMerge6[274e48db-edfd-4bc1-a8f4-ca471f0eecba] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 15:26:44.106 - [测试主从合并没主键][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 15:26:44.106 - [测试主从合并没主键][主从合并] - Node merge_table_processor(主从合并: cc5201ab-41bd-4f88-9898-4be94f2121c7) enable batch process 
[INFO ] 2024-11-25 15:26:44.107 - [测试主从合并没主键][主从合并] - 
Merge lookup relation{
  增强JS(aa5b53f1-d1f7-4357-a07e-54accdfd7bf2)
    ->test_cache_parent(9b0a9ec4-5bc4-47dd-976b-17307ca32e37)
} 
[INFO ] 2024-11-25 15:26:44.164 - [测试主从合并没主键][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] start preload schema,table counts: 1 
[INFO ] 2024-11-25 15:26:44.167 - [测试主从合并没主键][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 15:26:44.167 - [测试主从合并没主键][增强JS] - Node 增强JS[aa5b53f1-d1f7-4357-a07e-54accdfd7bf2] start preload schema,table counts: 1 
[INFO ] 2024-11-25 15:26:44.168 - [测试主从合并没主键][增强JS] - Node 增强JS[aa5b53f1-d1f7-4357-a07e-54accdfd7bf2] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 15:26:44.168 - [测试主从合并没主键][增强JS] - Node js_processor(增强JS: aa5b53f1-d1f7-4357-a07e-54accdfd7bf2) enable batch process 
[INFO ] 2024-11-25 15:26:44.586 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Source node "test_merge_no_primary_key_parent" read batch size: 100 
[INFO ] 2024-11-25 15:26:44.586 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Source node "test_merge_no_primary_key_parent" event queue capacity: 200 
[INFO ] 2024-11-25 15:26:44.586 - [测试主从合并没主键][test_merge_no_primary_key_parent] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-25 15:26:44.695 - [测试主从合并没主键][test_merge_no_primary_key_parent] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":8983421,"gtidSet":""} 
[INFO ] 2024-11-25 15:26:44.695 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Initial sync started 
[INFO ] 2024-11-25 15:26:44.704 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Starting batch read, table name: test_merge_no_primary_key_parent 
[INFO ] 2024-11-25 15:26:44.707 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Table test_merge_no_primary_key_parent is going to be initial synced 
[INFO ] 2024-11-25 15:26:44.707 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Query snapshot row size completed: test_merge_no_primary_key_parent(3cc67342-2d6e-4c4a-b083-ccf77e2f58d9) 
[INFO ] 2024-11-25 15:26:44.709 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Table [test_merge_no_primary_key_parent] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 15:26:44.711 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Initial sync completed 
[INFO ] 2024-11-25 15:26:44.808 - [测试主从合并没主键][主从合并] - Create merge cache, node id: 9b0a9ec4-5bc4-47dd-976b-17307ca32e37, imap name: -2047127170, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-25 15:26:44.812 - [测试主从合并没主键][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: - Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert 
[ERROR] 2024-11-25 15:26:44.817 - [测试主从合并没主键][主从合并] - - Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert <-- Error Message -->
- Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:936)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:252)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:169)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	...

<-- Full Stack Trace -->
- Table name: test_merge_no_primary_key_child
- Node name: test_merge_no_primary_key_child
- Merge operation: updateOrInsert
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:936)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:252)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-11-25 15:26:44.827 - [测试主从合并没主键][主从合并] - Job suspend in error handle 
[INFO ] 2024-11-25 15:26:44.855 - [测试主从合并没主键][test_merge_no_primary_key_child] - Source node "test_merge_no_primary_key_child" read batch size: 100 
[INFO ] 2024-11-25 15:26:44.855 - [测试主从合并没主键][test_merge_no_primary_key_child] - Source node "test_merge_no_primary_key_child" event queue capacity: 200 
[INFO ] 2024-11-25 15:26:44.855 - [测试主从合并没主键][test_merge_no_primary_key_child] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-25 15:26:44.903 - [测试主从合并没主键][test_merge_no_primary_key_child] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":8983421,"gtidSet":""} 
[INFO ] 2024-11-25 15:26:44.905 - [测试主从合并没主键] - Node[test_merge_no_primary_key_child] is waiting for running 
[INFO ] 2024-11-25 15:26:45.110 - [测试主从合并没主键][testMerge6] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-25 15:26:45.243 - [测试主从合并没主键][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] running status set to false 
[INFO ] 2024-11-25 15:26:45.251 - [测试主从合并没主键][test_merge_no_primary_key_child] - Initial sync started 
[INFO ] 2024-11-25 15:26:45.252 - [测试主从合并没主键][test_merge_no_primary_key_child] - Initial sync completed 
[INFO ] 2024-11-25 15:26:45.266 - [测试主从合并没主键][test_merge_no_primary_key_child] - Query snapshot row size completed: test_merge_no_primary_key_child(9b0a9ec4-5bc4-47dd-976b-17307ca32e37) 
[INFO ] 2024-11-25 15:26:45.277 - [测试主从合并没主键][test_merge_no_primary_key_child] - Incremental sync starting... 
[INFO ] 2024-11-25 15:26:45.277 - [测试主从合并没主键][test_merge_no_primary_key_child] - Incremental sync completed 
[INFO ] 2024-11-25 15:26:45.277 - [测试主从合并没主键][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSourcePdkDataNode_9b0a9ec4-5bc4-47dd-976b-17307ca32e37_1732519604660 
[INFO ] 2024-11-25 15:26:45.277 - [测试主从合并没主键][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSourcePdkDataNode_9b0a9ec4-5bc4-47dd-976b-17307ca32e37_1732519604660 
[INFO ] 2024-11-25 15:26:45.278 - [测试主从合并没主键][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] schema data cleaned 
[INFO ] 2024-11-25 15:26:45.278 - [测试主从合并没主键][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] monitor closed 
[INFO ] 2024-11-25 15:26:45.278 - [测试主从合并没主键][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[9b0a9ec4-5bc4-47dd-976b-17307ca32e37] close complete, cost 35 ms 
[INFO ] 2024-11-25 15:26:45.278 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] running status set to false 
[INFO ] 2024-11-25 15:26:45.288 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Incremental sync starting... 
[INFO ] 2024-11-25 15:26:45.289 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Incremental sync completed 
[INFO ] 2024-11-25 15:26:45.289 - [测试主从合并没主键][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSourcePdkDataNode_3cc67342-2d6e-4c4a-b083-ccf77e2f58d9_1732519604367 
[INFO ] 2024-11-25 15:26:45.289 - [测试主从合并没主键][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSourcePdkDataNode_3cc67342-2d6e-4c4a-b083-ccf77e2f58d9_1732519604367 
[INFO ] 2024-11-25 15:26:45.289 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] schema data cleaned 
[INFO ] 2024-11-25 15:26:45.289 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] monitor closed 
[INFO ] 2024-11-25 15:26:45.291 - [测试主从合并没主键][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[3cc67342-2d6e-4c4a-b083-ccf77e2f58d9] close complete, cost 12 ms 
[INFO ] 2024-11-25 15:26:45.291 - [测试主从合并没主键][增强JS] - Node 增强JS[aa5b53f1-d1f7-4357-a07e-54accdfd7bf2] running status set to false 
[INFO ] 2024-11-25 15:26:45.291 - [测试主从合并没主键][增强JS] - Node 增强JS[aa5b53f1-d1f7-4357-a07e-54accdfd7bf2] schema data cleaned 
[INFO ] 2024-11-25 15:26:45.291 - [测试主从合并没主键][增强JS] - Node 增强JS[aa5b53f1-d1f7-4357-a07e-54accdfd7bf2] monitor closed 
[INFO ] 2024-11-25 15:26:45.292 - [测试主从合并没主键][增强JS] - Node 增强JS[aa5b53f1-d1f7-4357-a07e-54accdfd7bf2] close complete, cost 1 ms 
[INFO ] 2024-11-25 15:26:45.299 - [测试主从合并没主键][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] running status set to false 
[INFO ] 2024-11-25 15:26:45.300 - [测试主从合并没主键][主从合并] - Destroy merge cache resource: -2047127170 
[INFO ] 2024-11-25 15:26:45.301 - [测试主从合并没主键][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] schema data cleaned 
[INFO ] 2024-11-25 15:26:45.302 - [测试主从合并没主键][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] monitor closed 
[INFO ] 2024-11-25 15:26:45.302 - [测试主从合并没主键][主从合并] - Node 主从合并[cc5201ab-41bd-4f88-9898-4be94f2121c7] close complete, cost 9 ms 
[INFO ] 2024-11-25 15:26:45.303 - [测试主从合并没主键][testMerge6] - Node testMerge6[274e48db-edfd-4bc1-a8f4-ca471f0eecba] running status set to false 
[INFO ] 2024-11-25 15:26:45.308 - [测试主从合并没主键][testMerge6] - PDK connector node stopped: HazelcastTargetPdkDataNode_274e48db-edfd-4bc1-a8f4-ca471f0eecba_1732519604915 
[INFO ] 2024-11-25 15:26:45.309 - [测试主从合并没主键][testMerge6] - PDK connector node released: HazelcastTargetPdkDataNode_274e48db-edfd-4bc1-a8f4-ca471f0eecba_1732519604915 
[INFO ] 2024-11-25 15:26:45.309 - [测试主从合并没主键][testMerge6] - Node testMerge6[274e48db-edfd-4bc1-a8f4-ca471f0eecba] schema data cleaned 
[INFO ] 2024-11-25 15:26:45.309 - [测试主从合并没主键][testMerge6] - Node testMerge6[274e48db-edfd-4bc1-a8f4-ca471f0eecba] monitor closed 
[INFO ] 2024-11-25 15:26:45.309 - [测试主从合并没主键][testMerge6] - Node testMerge6[274e48db-edfd-4bc1-a8f4-ca471f0eecba] close complete, cost 6 ms 
[INFO ] 2024-11-25 15:26:45.427 - [测试主从合并没主键] - Task [测试主从合并没主键] cannot retry, reason: Task retry service not start 
[INFO ] 2024-11-25 15:26:45.427 - [测试主从合并没主键] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-25 15:26:45.427 - [测试主从合并没主键] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@58bae369 
[INFO ] 2024-11-25 15:26:45.544 - [测试主从合并没主键] - Stop task milestones: 6743eacca5f66c638ce3277a(测试主从合并没主键)  
[INFO ] 2024-11-25 15:26:45.573 - [测试主从合并没主键] - Stopped task aspect(s) 
[INFO ] 2024-11-25 15:26:45.573 - [测试主从合并没主键] - Snapshot order controller have been removed 
[INFO ] 2024-11-25 15:26:45.590 - [测试主从合并没主键] - Remove memory task client succeed, task: 测试主从合并没主键[6743eacca5f66c638ce3277a] 
[INFO ] 2024-11-25 15:26:45.591 - [测试主从合并没主键] - Destroy memory task client cache succeed, task: 测试主从合并没主键[6743eacca5f66c638ce3277a] 
[INFO ] 2024-11-25 15:32:05.851 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-bca5da47-2249-4413-870a-70df6c646cab complete, cost 673ms 
[INFO ] 2024-11-25 15:42:18.198 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-d5e09459-211a-4c1d-8c1f-69a8ee98e508 complete, cost 765ms 
[INFO ] 2024-11-25 15:42:21.865 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-de47ee56-189c-4219-8f02-7a8325abbbe9 complete, cost 714ms 
[INFO ] 2024-11-25 15:42:30.627 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-d555370f-0efa-4b02-acc0-5efc962a9940 complete, cost 624ms 
[INFO ] 2024-11-25 15:42:53.665 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-01e7240c-3d80-4793-b247-9ee312ffbdce complete, cost 652ms 
[INFO ] 2024-11-25 15:43:18.193 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-3f15fa0d-000f-4e5e-add3-96fd4073cec5 complete, cost 642ms 
[INFO ] 2024-11-25 15:43:20.080 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-ada0f00a-a7a0-433a-a8cd-4b46a17eafe5 complete, cost 634ms 
[INFO ] 2024-11-25 16:17:03.829 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-732c5257-2eab-45cf-b9d4-ff3a6594bbd0 complete, cost 659ms 
[INFO ] 2024-11-25 16:17:04.058 - [测试主从合并没主键] - load tapTable task 6743eacca5f66c638ce32779-e21884d4-d1ff-4fe3-aa81-c4addca5fa0e complete, cost 1038ms 
