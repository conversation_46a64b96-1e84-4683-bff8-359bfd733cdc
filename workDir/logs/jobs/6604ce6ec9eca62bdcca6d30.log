[INFO ] 2024-03-28 09:57:42.868 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:42.870 - [任务 32(100)][69dbe77c-0784-412a-a1a6-c46ce2ca4479] - Node 69dbe77c-0784-412a-a1a6-c46ce2ca4479[69dbe77c-0784-412a-a1a6-c46ce2ca4479] start preload schema,table counts: 0 
[INFO ] 2024-03-28 09:57:42.870 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:42.870 - [任务 32(100)][69dbe77c-0784-412a-a1a6-c46ce2ca4479] - Node 69dbe77c-0784-412a-a1a6-c46ce2ca4479[69dbe77c-0784-412a-a1a6-c46ce2ca4479] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:42.870 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:42.871 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:43.151 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:43.151 - [任务 32(100)][eda1446a-b678-4e7d-9712-76e9bdec7345] - Node eda1446a-b678-4e7d-9712-76e9bdec7345[eda1446a-b678-4e7d-9712-76e9bdec7345] start preload schema,table counts: 0 
[INFO ] 2024-03-28 09:57:43.151 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:43.151 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:43.151 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:43.151 - [任务 32(100)][eda1446a-b678-4e7d-9712-76e9bdec7345] - Node eda1446a-b678-4e7d-9712-76e9bdec7345[eda1446a-b678-4e7d-9712-76e9bdec7345] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:44.099 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] running status set to false 
[INFO ] 2024-03-28 09:57:44.138 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] running status set to false 
[INFO ] 2024-03-28 09:57:44.139 - [任务 32(100)][eda1446a-b678-4e7d-9712-76e9bdec7345] - Node eda1446a-b678-4e7d-9712-76e9bdec7345[eda1446a-b678-4e7d-9712-76e9bdec7345] running status set to false 
[INFO ] 2024-03-28 09:57:44.139 - [任务 32(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:44.139 - [任务 32(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:44.150 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.150 - [任务 32(100)][eda1446a-b678-4e7d-9712-76e9bdec7345] - Node eda1446a-b678-4e7d-9712-76e9bdec7345[eda1446a-b678-4e7d-9712-76e9bdec7345] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.150 - [任务 32(100)][eda1446a-b678-4e7d-9712-76e9bdec7345] - Node eda1446a-b678-4e7d-9712-76e9bdec7345[eda1446a-b678-4e7d-9712-76e9bdec7345] monitor closed 
[INFO ] 2024-03-28 09:57:44.150 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] monitor closed 
[INFO ] 2024-03-28 09:57:44.208 - [任务 32(100)][eda1446a-b678-4e7d-9712-76e9bdec7345] - Node eda1446a-b678-4e7d-9712-76e9bdec7345[eda1446a-b678-4e7d-9712-76e9bdec7345] close complete, cost 66 ms 
[INFO ] 2024-03-28 09:57:44.211 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] close complete, cost 116 ms 
[INFO ] 2024-03-28 09:57:44.226 - [任务 32(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ac5378af-2f35-4b25-8605-de00d7f32da2 
[INFO ] 2024-03-28 09:57:44.226 - [任务 32(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ac5378af-2f35-4b25-8605-de00d7f32da2 
[INFO ] 2024-03-28 09:57:44.227 - [任务 32(100)][增强JS] - [ScriptExecutorsManager-6604ce6ec9eca62bdcca6d30-46c737e6-8ca6-401a-a3e6-270a383fb8a1-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.227 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.227 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] monitor closed 
[INFO ] 2024-03-28 09:57:44.227 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] close complete, cost 90 ms 
[INFO ] 2024-03-28 09:57:44.227 - [任务 32(100)] - load tapTable task 6604ce6ec9eca62bdcca6d30-eda1446a-b678-4e7d-9712-76e9bdec7345 complete, cost 1120ms 
[INFO ] 2024-03-28 09:57:44.435 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] running status set to false 
[INFO ] 2024-03-28 09:57:44.446 - [任务 32(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:44.446 - [任务 32(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:44.446 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.446 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] monitor closed 
[INFO ] 2024-03-28 09:57:44.454 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] close complete, cost 38 ms 
[INFO ] 2024-03-28 09:57:44.454 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] running status set to false 
[INFO ] 2024-03-28 09:57:44.456 - [任务 32(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-63f03b03-85f4-4e7e-a9e4-9c1d89bc6833 
[INFO ] 2024-03-28 09:57:44.457 - [任务 32(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-63f03b03-85f4-4e7e-a9e4-9c1d89bc6833 
[INFO ] 2024-03-28 09:57:44.457 - [任务 32(100)][增强JS] - [ScriptExecutorsManager-6604ce6ec9eca62bdcca6d30-46c737e6-8ca6-401a-a3e6-270a383fb8a1-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.459 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.459 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] monitor closed 
[INFO ] 2024-03-28 09:57:44.459 - [任务 32(100)][69dbe77c-0784-412a-a1a6-c46ce2ca4479] - Node 69dbe77c-0784-412a-a1a6-c46ce2ca4479[69dbe77c-0784-412a-a1a6-c46ce2ca4479] running status set to false 
[INFO ] 2024-03-28 09:57:44.459 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] close complete, cost 6 ms 
[INFO ] 2024-03-28 09:57:44.459 - [任务 32(100)][69dbe77c-0784-412a-a1a6-c46ce2ca4479] - Node 69dbe77c-0784-412a-a1a6-c46ce2ca4479[69dbe77c-0784-412a-a1a6-c46ce2ca4479] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.460 - [任务 32(100)][69dbe77c-0784-412a-a1a6-c46ce2ca4479] - Node 69dbe77c-0784-412a-a1a6-c46ce2ca4479[69dbe77c-0784-412a-a1a6-c46ce2ca4479] monitor closed 
[INFO ] 2024-03-28 09:57:44.460 - [任务 32(100)][69dbe77c-0784-412a-a1a6-c46ce2ca4479] - Node 69dbe77c-0784-412a-a1a6-c46ce2ca4479[69dbe77c-0784-412a-a1a6-c46ce2ca4479] close complete, cost 0 ms 
[INFO ] 2024-03-28 09:57:44.505 - [任务 32(100)] - load tapTable task 6604ce6ec9eca62bdcca6d30-69dbe77c-0784-412a-a1a6-c46ce2ca4479 complete, cost 1675ms 
[INFO ] 2024-03-28 09:57:44.505 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:44.506 - [任务 32(100)][f0a37271-cbab-4178-9050-eefd82d45eb5] - Node f0a37271-cbab-4178-9050-eefd82d45eb5[f0a37271-cbab-4178-9050-eefd82d45eb5] start preload schema,table counts: 0 
[INFO ] 2024-03-28 09:57:44.506 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:44.506 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:44.506 - [任务 32(100)][f0a37271-cbab-4178-9050-eefd82d45eb5] - Node f0a37271-cbab-4178-9050-eefd82d45eb5[f0a37271-cbab-4178-9050-eefd82d45eb5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:44.506 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 09:57:44.792 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] running status set to false 
[INFO ] 2024-03-28 09:57:44.799 - [任务 32(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:44.800 - [任务 32(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:44.800 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] schema data cleaned 
[INFO ] 2024-03-28 09:57:44.800 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] monitor closed 
[INFO ] 2024-03-28 09:57:44.801 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] close complete, cost 19 ms 
[INFO ] 2024-03-28 09:57:45.016 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] running status set to false 
[INFO ] 2024-03-28 09:57:45.016 - [任务 32(100)][f0a37271-cbab-4178-9050-eefd82d45eb5] - Node f0a37271-cbab-4178-9050-eefd82d45eb5[f0a37271-cbab-4178-9050-eefd82d45eb5] running status set to false 
[INFO ] 2024-03-28 09:57:45.016 - [任务 32(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-8091f165-8476-42b7-b921-036817196cd7 
[INFO ] 2024-03-28 09:57:45.016 - [任务 32(100)][f0a37271-cbab-4178-9050-eefd82d45eb5] - Node f0a37271-cbab-4178-9050-eefd82d45eb5[f0a37271-cbab-4178-9050-eefd82d45eb5] schema data cleaned 
[INFO ] 2024-03-28 09:57:45.016 - [任务 32(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-8091f165-8476-42b7-b921-036817196cd7 
[INFO ] 2024-03-28 09:57:45.016 - [任务 32(100)][f0a37271-cbab-4178-9050-eefd82d45eb5] - Node f0a37271-cbab-4178-9050-eefd82d45eb5[f0a37271-cbab-4178-9050-eefd82d45eb5] monitor closed 
[INFO ] 2024-03-28 09:57:45.017 - [任务 32(100)][增强JS] - [ScriptExecutorsManager-6604ce6ec9eca62bdcca6d30-46c737e6-8ca6-401a-a3e6-270a383fb8a1-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:57:45.017 - [任务 32(100)][f0a37271-cbab-4178-9050-eefd82d45eb5] - Node f0a37271-cbab-4178-9050-eefd82d45eb5[f0a37271-cbab-4178-9050-eefd82d45eb5] close complete, cost 0 ms 
[INFO ] 2024-03-28 09:57:45.017 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] schema data cleaned 
[INFO ] 2024-03-28 09:57:45.018 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] monitor closed 
[INFO ] 2024-03-28 09:57:45.018 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] close complete, cost 6 ms 
[INFO ] 2024-03-28 09:57:45.018 - [任务 32(100)] - load tapTable task 6604ce6ec9eca62bdcca6d30-f0a37271-cbab-4178-9050-eefd82d45eb5 complete, cost 648ms 
[INFO ] 2024-03-28 09:57:46.953 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:46.953 - [任务 32(100)][8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] - Node 8db9f0ea-158a-49fb-a1e0-f6f9b41667d8[8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] start preload schema,table counts: 0 
[INFO ] 2024-03-28 09:57:46.953 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:57:46.954 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:46.954 - [任务 32(100)][8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] - Node 8db9f0ea-158a-49fb-a1e0-f6f9b41667d8[8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:46.954 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:57:47.215 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] running status set to false 
[INFO ] 2024-03-28 09:57:47.216 - [任务 32(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:47.216 - [任务 32(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:57:47.216 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] schema data cleaned 
[INFO ] 2024-03-28 09:57:47.217 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] monitor closed 
[INFO ] 2024-03-28 09:57:47.218 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] close complete, cost 12 ms 
[INFO ] 2024-03-28 09:57:47.452 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] running status set to false 
[INFO ] 2024-03-28 09:57:47.453 - [任务 32(100)][8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] - Node 8db9f0ea-158a-49fb-a1e0-f6f9b41667d8[8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] running status set to false 
[INFO ] 2024-03-28 09:57:47.453 - [任务 32(100)][8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] - Node 8db9f0ea-158a-49fb-a1e0-f6f9b41667d8[8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] schema data cleaned 
[INFO ] 2024-03-28 09:57:47.453 - [任务 32(100)][8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] - Node 8db9f0ea-158a-49fb-a1e0-f6f9b41667d8[8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] monitor closed 
[INFO ] 2024-03-28 09:57:47.453 - [任务 32(100)][8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] - Node 8db9f0ea-158a-49fb-a1e0-f6f9b41667d8[8db9f0ea-158a-49fb-a1e0-f6f9b41667d8] close complete, cost 2 ms 
[INFO ] 2024-03-28 09:57:47.458 - [任务 32(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-bf7ad9b2-2f9e-454d-9f75-280240a21603 
[INFO ] 2024-03-28 09:57:47.458 - [任务 32(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-bf7ad9b2-2f9e-454d-9f75-280240a21603 
[INFO ] 2024-03-28 09:57:47.459 - [任务 32(100)][增强JS] - [ScriptExecutorsManager-6604ce6ec9eca62bdcca6d30-46c737e6-8ca6-401a-a3e6-270a383fb8a1-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:57:47.460 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] schema data cleaned 
[INFO ] 2024-03-28 09:57:47.460 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] monitor closed 
[INFO ] 2024-03-28 09:57:47.460 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] close complete, cost 11 ms 
[INFO ] 2024-03-28 09:57:47.668 - [任务 32(100)] - load tapTable task 6604ce6ec9eca62bdcca6d30-8db9f0ea-158a-49fb-a1e0-f6f9b41667d8 complete, cost 557ms 
[INFO ] 2024-03-28 09:58:01.480 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:58:01.480 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:58:01.486 - [任务 32(100)][204fd989-b421-48ef-98c6-21283bb3feb5] - Node 204fd989-b421-48ef-98c6-21283bb3feb5[204fd989-b421-48ef-98c6-21283bb3feb5] start preload schema,table counts: 0 
[INFO ] 2024-03-28 09:58:01.487 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:01.487 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:01.693 - [任务 32(100)][204fd989-b421-48ef-98c6-21283bb3feb5] - Node 204fd989-b421-48ef-98c6-21283bb3feb5[204fd989-b421-48ef-98c6-21283bb3feb5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:01.765 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] running status set to false 
[INFO ] 2024-03-28 09:58:01.767 - [任务 32(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:58:01.767 - [任务 32(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:58:01.767 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] schema data cleaned 
[INFO ] 2024-03-28 09:58:01.767 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] monitor closed 
[INFO ] 2024-03-28 09:58:01.767 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] close complete, cost 12 ms 
[INFO ] 2024-03-28 09:58:02.017 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] running status set to false 
[INFO ] 2024-03-28 09:58:02.017 - [任务 32(100)][204fd989-b421-48ef-98c6-21283bb3feb5] - Node 204fd989-b421-48ef-98c6-21283bb3feb5[204fd989-b421-48ef-98c6-21283bb3feb5] running status set to false 
[INFO ] 2024-03-28 09:58:02.017 - [任务 32(100)][204fd989-b421-48ef-98c6-21283bb3feb5] - Node 204fd989-b421-48ef-98c6-21283bb3feb5[204fd989-b421-48ef-98c6-21283bb3feb5] schema data cleaned 
[INFO ] 2024-03-28 09:58:02.017 - [任务 32(100)][204fd989-b421-48ef-98c6-21283bb3feb5] - Node 204fd989-b421-48ef-98c6-21283bb3feb5[204fd989-b421-48ef-98c6-21283bb3feb5] monitor closed 
[INFO ] 2024-03-28 09:58:02.017 - [任务 32(100)][204fd989-b421-48ef-98c6-21283bb3feb5] - Node 204fd989-b421-48ef-98c6-21283bb3feb5[204fd989-b421-48ef-98c6-21283bb3feb5] close complete, cost 1 ms 
[INFO ] 2024-03-28 09:58:02.025 - [任务 32(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-151770ec-7f33-4ad2-9134-722b45203d1d 
[INFO ] 2024-03-28 09:58:02.025 - [任务 32(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-151770ec-7f33-4ad2-9134-722b45203d1d 
[INFO ] 2024-03-28 09:58:02.027 - [任务 32(100)][增强JS] - [ScriptExecutorsManager-6604ce6ec9eca62bdcca6d30-46c737e6-8ca6-401a-a3e6-270a383fb8a1-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:58:02.027 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] schema data cleaned 
[INFO ] 2024-03-28 09:58:02.027 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] monitor closed 
[INFO ] 2024-03-28 09:58:02.028 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] close complete, cost 12 ms 
[INFO ] 2024-03-28 09:58:02.235 - [任务 32(100)] - load tapTable task 6604ce6ec9eca62bdcca6d30-204fd989-b421-48ef-98c6-21283bb3feb5 complete, cost 622ms 
[INFO ] 2024-03-28 09:58:02.942 - [任务 32(100)][f6264ae2-c49a-4037-b878-f50c0c261e6f] - Node f6264ae2-c49a-4037-b878-f50c0c261e6f[f6264ae2-c49a-4037-b878-f50c0c261e6f] start preload schema,table counts: 0 
[INFO ] 2024-03-28 09:58:02.942 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:58:02.942 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:58:02.942 - [任务 32(100)][f6264ae2-c49a-4037-b878-f50c0c261e6f] - Node f6264ae2-c49a-4037-b878-f50c0c261e6f[f6264ae2-c49a-4037-b878-f50c0c261e6f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:02.943 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:02.943 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 09:58:03.276 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] running status set to false 
[INFO ] 2024-03-28 09:58:03.276 - [任务 32(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:58:03.279 - [任务 32(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:58:03.279 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][82b690d8-04c4-4cbb-b7a6-3858b80da78c] - Node 82b690d8-04c4-4cbb-b7a6-3858b80da78c[82b690d8-04c4-4cbb-b7a6-3858b80da78c] start preload schema,table counts: 0 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][82b690d8-04c4-4cbb-b7a6-3858b80da78c] - Node 82b690d8-04c4-4cbb-b7a6-3858b80da78c[82b690d8-04c4-4cbb-b7a6-3858b80da78c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] monitor closed 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 09:58:03.280 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] close complete, cost 32 ms 
[INFO ] 2024-03-28 09:58:03.516 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] running status set to false 
[INFO ] 2024-03-28 09:58:03.516 - [任务 32(100)][f6264ae2-c49a-4037-b878-f50c0c261e6f] - Node f6264ae2-c49a-4037-b878-f50c0c261e6f[f6264ae2-c49a-4037-b878-f50c0c261e6f] running status set to false 
[INFO ] 2024-03-28 09:58:03.516 - [任务 32(100)][f6264ae2-c49a-4037-b878-f50c0c261e6f] - Node f6264ae2-c49a-4037-b878-f50c0c261e6f[f6264ae2-c49a-4037-b878-f50c0c261e6f] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.516 - [任务 32(100)][f6264ae2-c49a-4037-b878-f50c0c261e6f] - Node f6264ae2-c49a-4037-b878-f50c0c261e6f[f6264ae2-c49a-4037-b878-f50c0c261e6f] monitor closed 
[INFO ] 2024-03-28 09:58:03.516 - [任务 32(100)][f6264ae2-c49a-4037-b878-f50c0c261e6f] - Node f6264ae2-c49a-4037-b878-f50c0c261e6f[f6264ae2-c49a-4037-b878-f50c0c261e6f] close complete, cost 0 ms 
[INFO ] 2024-03-28 09:58:03.518 - [任务 32(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5e8a77fe-699c-4d16-aa72-d6ba7b6f2858 
[INFO ] 2024-03-28 09:58:03.519 - [任务 32(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-5e8a77fe-699c-4d16-aa72-d6ba7b6f2858 
[INFO ] 2024-03-28 09:58:03.519 - [任务 32(100)][增强JS] - [ScriptExecutorsManager-6604ce6ec9eca62bdcca6d30-46c737e6-8ca6-401a-a3e6-270a383fb8a1-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.520 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.520 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] monitor closed 
[INFO ] 2024-03-28 09:58:03.521 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] close complete, cost 7 ms 
[INFO ] 2024-03-28 09:58:03.521 - [任务 32(100)] - load tapTable task 6604ce6ec9eca62bdcca6d30-f6264ae2-c49a-4037-b878-f50c0c261e6f complete, cost 665ms 
[INFO ] 2024-03-28 09:58:03.582 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] running status set to false 
[INFO ] 2024-03-28 09:58:03.582 - [任务 32(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:58:03.582 - [任务 32(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f669551a-4554-4ace-876e-b2cfdf86257f 
[INFO ] 2024-03-28 09:58:03.582 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.582 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] monitor closed 
[INFO ] 2024-03-28 09:58:03.582 - [任务 32(100)][test2] - Node test2[f669551a-4554-4ace-876e-b2cfdf86257f] close complete, cost 6 ms 
[INFO ] 2024-03-28 09:58:03.800 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] running status set to false 
[INFO ] 2024-03-28 09:58:03.802 - [任务 32(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-44c835c5-9631-4085-8302-b4d190798938 
[INFO ] 2024-03-28 09:58:03.802 - [任务 32(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-44c835c5-9631-4085-8302-b4d190798938 
[INFO ] 2024-03-28 09:58:03.802 - [任务 32(100)][增强JS] - [ScriptExecutorsManager-6604ce6ec9eca62bdcca6d30-46c737e6-8ca6-401a-a3e6-270a383fb8a1-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.805 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.805 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] monitor closed 
[INFO ] 2024-03-28 09:58:03.805 - [任务 32(100)][增强JS] - Node 增强JS[46c737e6-8ca6-401a-a3e6-270a383fb8a1] close complete, cost 6 ms 
[INFO ] 2024-03-28 09:58:03.805 - [任务 32(100)][82b690d8-04c4-4cbb-b7a6-3858b80da78c] - Node 82b690d8-04c4-4cbb-b7a6-3858b80da78c[82b690d8-04c4-4cbb-b7a6-3858b80da78c] running status set to false 
[INFO ] 2024-03-28 09:58:03.805 - [任务 32(100)][82b690d8-04c4-4cbb-b7a6-3858b80da78c] - Node 82b690d8-04c4-4cbb-b7a6-3858b80da78c[82b690d8-04c4-4cbb-b7a6-3858b80da78c] schema data cleaned 
[INFO ] 2024-03-28 09:58:03.806 - [任务 32(100)][82b690d8-04c4-4cbb-b7a6-3858b80da78c] - Node 82b690d8-04c4-4cbb-b7a6-3858b80da78c[82b690d8-04c4-4cbb-b7a6-3858b80da78c] monitor closed 
[INFO ] 2024-03-28 09:58:03.806 - [任务 32(100)][82b690d8-04c4-4cbb-b7a6-3858b80da78c] - Node 82b690d8-04c4-4cbb-b7a6-3858b80da78c[82b690d8-04c4-4cbb-b7a6-3858b80da78c] close complete, cost 0 ms 
[INFO ] 2024-03-28 09:58:04.015 - [任务 32(100)] - load tapTable task 6604ce6ec9eca62bdcca6d30-82b690d8-04c4-4cbb-b7a6-3858b80da78c complete, cost 599ms 
