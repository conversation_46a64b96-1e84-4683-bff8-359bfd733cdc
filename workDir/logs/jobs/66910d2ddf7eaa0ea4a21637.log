[INFO ] 2024-07-12 19:02:44.904 - [任务 60] - Task initialization... 
[INFO ] 2024-07-12 19:02:44.905 - [任务 60] - Start task milestones: 66910d2ddf7eaa0ea4a21637(任务 60) 
[INFO ] 2024-07-12 19:02:45.116 - [任务 60] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 19:02:45.155 - [任务 60] - The engine receives 任务 60 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 19:02:45.200 - [任务 60][SourceMongo] - Node SourceMongo[6ff3cc7e-1444-4540-91d5-49e5c3ef426b] start preload schema,table counts: 1 
[INFO ] 2024-07-12 19:02:45.200 - [任务 60][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[c09b8ec5-2e72-4499-9a09-7a12da6b1e9c] start preload schema,table counts: 1 
[INFO ] 2024-07-12 19:02:45.200 - [任务 60][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[c09b8ec5-2e72-4499-9a09-7a12da6b1e9c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 19:02:45.200 - [任务 60][SourceMongo] - Node SourceMongo[6ff3cc7e-1444-4540-91d5-49e5c3ef426b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 19:02:45.856 - [任务 60][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-12 19:02:45.859 - [任务 60][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-12 19:02:45.860 - [任务 60][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 19:02:45.863 - [任务 60][SourceMysqlTestHeartBeat] - Node(SourceMysqlTestHeartBeat) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-12 19:02:45.864 - [任务 60][SourceMysqlTestHeartBeat] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 19:02:46.062 - [任务 60][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1720782165,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-12 19:02:46.062 - [任务 60][SourceMongo] - Initial sync started 
[INFO ] 2024-07-12 19:02:46.073 - [任务 60][SourceMongo] - Starting batch read, table name: repliceTime, offset: null 
[INFO ] 2024-07-12 19:02:46.073 - [任务 60][SourceMongo] - Table repliceTime is going to be initial synced 
[INFO ] 2024-07-12 19:02:46.118 - [任务 60][SourceMongo] - Table [repliceTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 19:02:46.119 - [任务 60][SourceMongo] - Query table 'repliceTime' counts: 1 
[INFO ] 2024-07-12 19:02:46.120 - [任务 60][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-12 19:02:46.120 - [任务 60][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-12 19:02:46.123 - [任务 60][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-12 19:02:46.123 - [任务 60][SourceMongo] - Starting stream read, table list: [repliceTime, _tapdata_heartbeat_table], offset: {"cdcOffset":1720782165,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-12 19:02:46.128 - [任务 60][SourceMongo] - Connector MongoDB incremental start succeed, tables: [repliceTime, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 19:04:52.090 - [任务 60][SourceMongo] - Node SourceMongo[6ff3cc7e-1444-4540-91d5-49e5c3ef426b] running status set to false 
[INFO ] 2024-07-12 19:04:52.097 - [任务 60][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-6ff3cc7e-1444-4540-91d5-49e5c3ef426b 
[INFO ] 2024-07-12 19:04:52.098 - [任务 60][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-6ff3cc7e-1444-4540-91d5-49e5c3ef426b 
[INFO ] 2024-07-12 19:04:52.098 - [任务 60][SourceMongo] - Node SourceMongo[6ff3cc7e-1444-4540-91d5-49e5c3ef426b] schema data cleaned 
[INFO ] 2024-07-12 19:04:52.104 - [任务 60][SourceMongo] - Node SourceMongo[6ff3cc7e-1444-4540-91d5-49e5c3ef426b] monitor closed 
[INFO ] 2024-07-12 19:04:52.104 - [任务 60][SourceMongo] - Node SourceMongo[6ff3cc7e-1444-4540-91d5-49e5c3ef426b] close complete, cost 43 ms 
[INFO ] 2024-07-12 19:04:52.152 - [任务 60][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[c09b8ec5-2e72-4499-9a09-7a12da6b1e9c] running status set to false 
[INFO ] 2024-07-12 19:04:52.152 - [任务 60][SourceMysqlTestHeartBeat] - PDK connector node stopped: HazelcastTargetPdkDataNode-c09b8ec5-2e72-4499-9a09-7a12da6b1e9c 
[INFO ] 2024-07-12 19:04:52.152 - [任务 60][SourceMysqlTestHeartBeat] - PDK connector node released: HazelcastTargetPdkDataNode-c09b8ec5-2e72-4499-9a09-7a12da6b1e9c 
[INFO ] 2024-07-12 19:04:52.153 - [任务 60][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[c09b8ec5-2e72-4499-9a09-7a12da6b1e9c] schema data cleaned 
[INFO ] 2024-07-12 19:04:52.153 - [任务 60][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[c09b8ec5-2e72-4499-9a09-7a12da6b1e9c] monitor closed 
[INFO ] 2024-07-12 19:04:52.153 - [任务 60][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[c09b8ec5-2e72-4499-9a09-7a12da6b1e9c] close complete, cost 49 ms 
[INFO ] 2024-07-12 19:04:53.166 - [任务 60][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-12 19:04:56.201 - [任务 60] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 19:04:56.215 - [任务 60] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1f58b96e 
[INFO ] 2024-07-12 19:04:56.215 - [任务 60] - Stop task milestones: 66910d2ddf7eaa0ea4a21637(任务 60)  
[INFO ] 2024-07-12 19:04:56.368 - [任务 60] - Stopped task aspect(s) 
[INFO ] 2024-07-12 19:04:56.369 - [任务 60] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 19:04:56.461 - [任务 60] - Remove memory task client succeed, task: 任务 60[66910d2ddf7eaa0ea4a21637] 
[INFO ] 2024-07-12 19:04:56.461 - [任务 60] - Destroy memory task client cache succeed, task: 任务 60[66910d2ddf7eaa0ea4a21637] 
