[INFO ] 2024-03-19 10:28:31.683 - [任务 4] - Start task milestones: 65f8f8377f674158a05b19f7(任务 4) 
[INFO ] 2024-03-19 10:28:31.699 - [任务 4] - Task initialization... 
[INFO ] 2024-03-19 10:28:31.706 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-19 10:28:31.767 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-19 10:28:31.809 - [任务 4][BMSQL_CONFIG] - Node BMSQL_CONFIG[0dff7c34-8e86-4108-bf33-5befb55d669c] start preload schema,table counts: 1 
[INFO ] 2024-03-19 10:28:31.810 - [任务 4][BMSQL_CONFIG_BACK] - Node BMSQL_CONFIG_BACK[f3ea339b-e187-44d5-a96e-ecb7934f2582] start preload schema,table counts: 1 
[INFO ] 2024-03-19 10:28:31.830 - [任务 4][BMSQL_CONFIG] - Node BMSQL_CONFIG[0dff7c34-8e86-4108-bf33-5befb55d669c] preload schema finished, cost 20 ms 
[INFO ] 2024-03-19 10:28:31.830 - [任务 4][BMSQL_CONFIG_BACK] - Node BMSQL_CONFIG_BACK[f3ea339b-e187-44d5-a96e-ecb7934f2582] preload schema finished, cost 20 ms 
[INFO ] 2024-03-19 10:28:32.742 - [任务 4][BMSQL_CONFIG_BACK] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-19 10:28:32.896 - [任务 4][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" read batch size: 100 
[INFO ] 2024-03-19 10:28:32.896 - [任务 4][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" event queue capacity: 200 
[INFO ] 2024-03-19 10:28:32.900 - [任务 4][BMSQL_CONFIG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-19 10:28:33.312 - [任务 4][BMSQL_CONFIG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":22696683,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-03-19 10:28:33.312 - [任务 4][BMSQL_CONFIG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-03-19 10:28:33.374 - [任务 4][BMSQL_CONFIG] - Initial sync started 
[INFO ] 2024-03-19 10:28:33.375 - [任务 4][BMSQL_CONFIG] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-03-19 10:28:33.380 - [任务 4][BMSQL_CONFIG] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-03-19 10:28:33.437 - [任务 4][BMSQL_CONFIG] - Query table 'BMSQL_CONFIG' counts: 4 
[INFO ] 2024-03-19 10:28:33.437 - [任务 4][BMSQL_CONFIG] - Initial sync completed 
[INFO ] 2024-03-19 10:28:33.437 - [任务 4][BMSQL_CONFIG] - Incremental sync starting... 
[INFO ] 2024-03-19 10:28:33.437 - [任务 4][BMSQL_CONFIG] - Initial sync completed 
[INFO ] 2024-03-19 10:28:33.439 - [任务 4][BMSQL_CONFIG] - Starting stream read, table list: [BMSQL_CONFIG], offset: {"sortString":null,"offsetValue":null,"lastScn":22696683,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-03-19 10:28:33.603 - [任务 4][BMSQL_CONFIG] - total start mining scn: 22696683 
[INFO ] 2024-03-19 10:28:34.747 - [任务 4][BMSQL_CONFIG] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[WARN ] 2024-03-19 10:58:11.862 - [任务 4][BMSQL_CONFIG] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-19 10:59:11.875 - [任务 4][BMSQL_CONFIG] - Log Miner is shutting down... 
[INFO ] 2024-03-19 10:59:11.875 - [任务 4][BMSQL_CONFIG] - Log Miner has been closed! 
[INFO ] 2024-03-19 10:59:13.242 - [任务 4][BMSQL_CONFIG] - total start mining scn: 22696683 
[INFO ] 2024-03-19 10:59:14.358 - [任务 4][BMSQL_CONFIG] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-03-19 11:00:30.136 - [任务 4] - Stop task milestones: 65f8f8377f674158a05b19f7(任务 4)  
[INFO ] 2024-03-19 11:00:30.359 - [任务 4][BMSQL_CONFIG] - Node BMSQL_CONFIG[0dff7c34-8e86-4108-bf33-5befb55d669c] running status set to false 
[INFO ] 2024-03-19 11:00:30.372 - [任务 4][BMSQL_CONFIG] - Log Miner is shutting down... 
[INFO ] 2024-03-19 11:00:30.388 - [任务 4][BMSQL_CONFIG] - Log Miner has been closed! 
[ERROR] 2024-03-19 11:00:30.412 - [任务 4][BMSQL_CONFIG] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:391)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:712)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:66)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:733)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:723)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:615)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:204)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-03-19 11:00:30.709 - [任务 4][BMSQL_CONFIG] - PDK connector node stopped: HazelcastSourcePdkDataNode-0dff7c34-8e86-4108-bf33-5befb55d669c 
[INFO ] 2024-03-19 11:00:30.714 - [任务 4][BMSQL_CONFIG] - PDK connector node released: HazelcastSourcePdkDataNode-0dff7c34-8e86-4108-bf33-5befb55d669c 
[INFO ] 2024-03-19 11:00:30.714 - [任务 4][BMSQL_CONFIG] - Node BMSQL_CONFIG[0dff7c34-8e86-4108-bf33-5befb55d669c] schema data cleaned 
[INFO ] 2024-03-19 11:00:30.714 - [任务 4][BMSQL_CONFIG] - Node BMSQL_CONFIG[0dff7c34-8e86-4108-bf33-5befb55d669c] monitor closed 
[INFO ] 2024-03-19 11:00:30.714 - [任务 4][BMSQL_CONFIG] - Node BMSQL_CONFIG[0dff7c34-8e86-4108-bf33-5befb55d669c] close complete, cost 355 ms 
[INFO ] 2024-03-19 11:00:30.714 - [任务 4][BMSQL_CONFIG_BACK] - Node BMSQL_CONFIG_BACK[f3ea339b-e187-44d5-a96e-ecb7934f2582] running status set to false 
[INFO ] 2024-03-19 11:00:31.313 - [任务 4][BMSQL_CONFIG_BACK] - PDK connector node stopped: HazelcastTargetPdkDataNode-f3ea339b-e187-44d5-a96e-ecb7934f2582 
[INFO ] 2024-03-19 11:00:31.321 - [任务 4][BMSQL_CONFIG_BACK] - PDK connector node released: HazelcastTargetPdkDataNode-f3ea339b-e187-44d5-a96e-ecb7934f2582 
[INFO ] 2024-03-19 11:00:31.321 - [任务 4][BMSQL_CONFIG_BACK] - Node BMSQL_CONFIG_BACK[f3ea339b-e187-44d5-a96e-ecb7934f2582] schema data cleaned 
[INFO ] 2024-03-19 11:00:31.321 - [任务 4][BMSQL_CONFIG_BACK] - Node BMSQL_CONFIG_BACK[f3ea339b-e187-44d5-a96e-ecb7934f2582] monitor closed 
[INFO ] 2024-03-19 11:00:31.322 - [任务 4][BMSQL_CONFIG_BACK] - Node BMSQL_CONFIG_BACK[f3ea339b-e187-44d5-a96e-ecb7934f2582] close complete, cost 602 ms 
[INFO ] 2024-03-19 11:00:32.122 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-19 11:00:32.123 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-03-19 11:00:32.124 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-03-19 11:00:32.172 - [任务 4] - Remove memory task client succeed, task: 任务 4[65f8f8377f674158a05b19f7] 
[INFO ] 2024-03-19 11:00:32.172 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[65f8f8377f674158a05b19f7] 
