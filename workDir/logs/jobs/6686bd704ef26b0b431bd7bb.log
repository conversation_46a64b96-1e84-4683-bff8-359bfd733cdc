[INFO ] 2024-07-15 03:35:34.284 - [Heartbeat-SourceMongo] - Start task milestones: 6686bd704ef26b0b431bd7bb(Heartbeat-SourceMongo) 
[INFO ] 2024-07-15 03:35:34.581 - [Heartbeat-SourceMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-15 03:35:34.681 - [Heartbeat-SourceMongo] - The engine receives Heartbeat-SourceMongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 03:35:34.775 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[57d6f160-7e11-444a-b8b3-8c59c3f4bf77] start preload schema,table counts: 1 
[INFO ] 2024-07-15 03:35:34.775 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[57d6f160-7e11-444a-b8b3-8c59c3f4bf77] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 03:35:34.793 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a4577117-de11-45fe-ac74-1349fe3d18bc] start preload schema,table counts: 1 
[INFO ] 2024-07-15 03:35:34.793 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a4577117-de11-45fe-ac74-1349fe3d18bc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 03:35:34.950 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 03:35:34.950 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 03:35:34.954 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 03:35:34.954 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1720106353855,"lastTimes":1720985430175,"lastTN":433,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":179992,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 03:35:35.104 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1720106353855,"lastTimes":1720985430175,"lastTN":433,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":179992,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 03:35:35.104 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 03:35:35.123 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 03:35:35.298 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 03:39:04.343 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a4577117-de11-45fe-ac74-1349fe3d18bc] running status set to false 
[INFO ] 2024-07-15 03:39:04.345 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 03:39:04.351 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4577117-de11-45fe-ac74-1349fe3d18bc 
[INFO ] 2024-07-15 03:39:04.351 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-a4577117-de11-45fe-ac74-1349fe3d18bc 
[INFO ] 2024-07-15 03:39:04.352 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a4577117-de11-45fe-ac74-1349fe3d18bc] schema data cleaned 
[INFO ] 2024-07-15 03:39:04.352 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a4577117-de11-45fe-ac74-1349fe3d18bc] monitor closed 
[INFO ] 2024-07-15 03:39:04.354 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a4577117-de11-45fe-ac74-1349fe3d18bc] close complete, cost 23 ms 
[INFO ] 2024-07-15 03:39:04.354 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[57d6f160-7e11-444a-b8b3-8c59c3f4bf77] running status set to false 
[INFO ] 2024-07-15 03:39:04.376 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-57d6f160-7e11-444a-b8b3-8c59c3f4bf77 
[INFO ] 2024-07-15 03:39:04.376 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-57d6f160-7e11-444a-b8b3-8c59c3f4bf77 
[INFO ] 2024-07-15 03:39:04.376 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[57d6f160-7e11-444a-b8b3-8c59c3f4bf77] schema data cleaned 
[INFO ] 2024-07-15 03:39:04.376 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[57d6f160-7e11-444a-b8b3-8c59c3f4bf77] monitor closed 
[INFO ] 2024-07-15 03:39:04.377 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[57d6f160-7e11-444a-b8b3-8c59c3f4bf77] close complete, cost 22 ms 
[INFO ] 2024-07-15 03:39:06.132 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 03:39:06.133 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@18171651 
[INFO ] 2024-07-15 03:39:06.270 - [Heartbeat-SourceMongo] - Stop task milestones: 6686bd704ef26b0b431bd7bb(Heartbeat-SourceMongo)  
[INFO ] 2024-07-15 03:39:06.270 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 03:39:06.299 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 03:39:06.299 - [Heartbeat-SourceMongo] - Remove memory task client succeed, task: Heartbeat-SourceMongo[6686bd704ef26b0b431bd7bb] 
[INFO ] 2024-07-15 03:39:06.500 - [Heartbeat-SourceMongo] - Destroy memory task client cache succeed, task: Heartbeat-SourceMongo[6686bd704ef26b0b431bd7bb] 
