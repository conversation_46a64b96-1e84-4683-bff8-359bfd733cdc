[INFO ] 2024-07-15 12:04:02.773 - [任务 2] - Task initialization... 
[INFO ] 2024-07-15 12:04:02.802 - [任务 2] - Start task milestones: 66949f941df4b966216a4ee0(任务 2) 
[INFO ] 2024-07-15 12:04:03.032 - [任务 2] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:04:03.033 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:04:03.077 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:04:03.078 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:04:03.081 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 12:04:03.082 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:04:04.541 - [任务 2][TESTPolicy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 12:04:04.579 - [任务 2][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 12:04:04.583 - [任务 2][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 12:04:04.583 - [任务 2][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:04:04.801 - [任务 2][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721016244,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 12:04:04.895 - [任务 2][POLICY] - Initial sync started 
[INFO ] 2024-07-15 12:04:04.895 - [任务 2][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 12:04:04.897 - [任务 2][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 12:04:04.984 - [任务 2][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 12:04:04.984 - [任务 2][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 12:04:04.984 - [任务 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:04:04.984 - [任务 2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 12:04:04.984 - [任务 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:04:05.033 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 12:04:05.033 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mongo enable share cdc: true 
[INFO ] 2024-07-15 12:04:05.033 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 2 enable share cdc: true 
[INFO ] 2024-07-15 12:04:05.053 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mongo的共享挖掘任务 
[INFO ] 2024-07-15 12:04:05.067 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 12:04:05.067 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 12:04:05.153 - [任务 2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:04:05.153 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 12:04:05.153 - [任务 2][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 12:04:05.153 - [任务 2][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 12:04:05.153 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 12:04:05.154 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 12:04:05.168 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 12:04:05.168 - [任务 2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:04:05.169 - [任务 2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2, external storage name: ExternalStorage_SHARE_CDC_-1603875826 
[INFO ] 2024-07-15 12:04:05.169 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 12:04:05.174 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T04:04:04.579Z): 1 
[INFO ] 2024-07-15 12:04:05.174 - [任务 2][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 12:04:05.174 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 12:04:05.174 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 12:06:09.374 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] running status set to false 
[INFO ] 2024-07-15 12:06:09.392 - [任务 2][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 12:06:09.392 - [任务 2][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 12:06:09.392 - [任务 2][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 12:06:09.392 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] schema data cleaned 
[INFO ] 2024-07-15 12:06:09.393 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] monitor closed 
[INFO ] 2024-07-15 12:06:09.395 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] close complete, cost 49 ms 
[INFO ] 2024-07-15 12:06:09.395 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] running status set to false 
[INFO ] 2024-07-15 12:06:09.411 - [任务 2][TESTPolicy] - PDK connector node stopped: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 12:06:09.411 - [任务 2][TESTPolicy] - PDK connector node released: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 12:06:09.411 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] schema data cleaned 
[INFO ] 2024-07-15 12:06:09.411 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] monitor closed 
[INFO ] 2024-07-15 12:06:09.412 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] close complete, cost 16 ms 
[INFO ] 2024-07-15 12:06:11.937 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:06:11.938 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@50b427fb 
[INFO ] 2024-07-15 12:06:12.075 - [任务 2] - Stop task milestones: 66949f941df4b966216a4ee0(任务 2)  
[INFO ] 2024-07-15 12:06:12.075 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:06:12.076 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:06:12.091 - [任务 2] - Remove memory task client succeed, task: 任务 2[66949f941df4b966216a4ee0] 
[INFO ] 2024-07-15 12:06:12.095 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66949f941df4b966216a4ee0] 
[INFO ] 2024-07-15 17:59:31.897 - [任务 2] - Task initialization... 
[INFO ] 2024-07-15 17:59:31.898 - [任务 2] - Start task milestones: 66949f941df4b966216a4ee0(任务 2) 
[INFO ] 2024-07-15 17:59:32.096 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 17:59:32.141 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 17:59:32.207 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:59:32.207 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:59:32.207 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:59:32.207 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 17:59:32.539 - [任务 2][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 17:59:32.540 - [任务 2][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 17:59:32.540 - [任务 2][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 17:59:32.545 - [任务 2][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1721016244,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 17:59:32.634 - [任务 2][TESTPolicy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 17:59:32.645 - [任务 2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 17:59:32.654 - [任务 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 17:59:32.780 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 17:59:32.780 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mongo enable share cdc: true 
[INFO ] 2024-07-15 17:59:32.813 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 2 enable share cdc: true 
[INFO ] 2024-07-15 17:59:32.813 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mongo的共享挖掘任务 
[INFO ] 2024-07-15 17:59:32.813 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 17:59:32.908 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 17:59:32.908 - [任务 2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 17:59:32.982 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 17:59:32.982 - [任务 2][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 17:59:32.982 - [任务 2][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 17:59:32.982 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 17:59:32.991 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 17:59:32.991 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 17:59:32.996 - [任务 2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 17:59:32.996 - [任务 2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2, external storage name: ExternalStorage_SHARE_CDC_-1603875826 
[INFO ] 2024-07-15 17:59:33.003 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 17:59:33.003 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T04:04:04.579Z): 1 
[INFO ] 2024-07-15 17:59:33.005 - [任务 2][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 17:59:33.005 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 17:59:33.206 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 19:02:43.828 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] running status set to false 
[INFO ] 2024-07-15 19:02:43.836 - [任务 2][POLICY] - Incremental sync completed 
[WARN ] 2024-07-15 19:02:43.847 - [任务 2][POLICY] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 19:02:43.851 - [任务 2][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 19:02:43.851 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] schema data cleaned 
[INFO ] 2024-07-15 19:02:43.852 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] monitor closed 
[INFO ] 2024-07-15 19:02:43.860 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] close complete, cost 45 ms 
[INFO ] 2024-07-15 19:02:43.860 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] running status set to false 
[WARN ] 2024-07-15 19:02:43.911 - [任务 2][TESTPolicy] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 19:02:43.911 - [任务 2][TESTPolicy] - PDK connector node released: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 19:02:43.911 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] schema data cleaned 
[INFO ] 2024-07-15 19:02:43.911 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] monitor closed 
[INFO ] 2024-07-15 19:02:43.919 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] close complete, cost 57 ms 
[INFO ] 2024-07-15 19:07:45.816 - [任务 2] - Start task milestones: 66949f941df4b966216a4ee0(任务 2) 
[INFO ] 2024-07-15 19:07:46.009 - [任务 2] - Task initialization... 
[INFO ] 2024-07-15 19:07:46.009 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:07:46.052 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:07:46.106 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:07:46.107 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:07:46.107 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:07:46.107 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:07:46.403 - [任务 2][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 19:07:46.406 - [任务 2][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 19:07:46.421 - [任务 2][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:07:46.422 - [任务 2][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1721016244,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:07:46.517 - [任务 2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 19:07:46.517 - [任务 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 19:07:46.601 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 19:07:46.601 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mongo enable share cdc: true 
[INFO ] 2024-07-15 19:07:46.643 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 2 enable share cdc: true 
[INFO ] 2024-07-15 19:07:46.644 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mongo的共享挖掘任务 
[INFO ] 2024-07-15 19:07:46.718 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 19:07:46.719 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 19:07:46.908 - [任务 2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 19:07:46.910 - [任务 2][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 19:07:46.923 - [任务 2][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 19:07:46.925 - [任务 2][POLICY] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.getConstruct(ShareCdcPDKTaskReader.java:303)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.checkTableStartPointValid(ShareCdcPDKTaskReader.java:224)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.canShareCdc(ShareCdcPDKTaskReader.java:214)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.init(ShareCdcPDKTaskReader.java:146)
	io.tapdata.flow.engine.V2.sharecdc.impl.ShareCdcFactory.shareCdcReader(ShareCdcFactory.java:36)
	...

<-- Full Stack Trace -->
io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: An internal error occurred when init share cdc reader; Error: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doShareCdc$47(HazelcastSourcePdkDataNode.java:911)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doShareCdc(HazelcastSourcePdkDataNode.java:894)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: An internal error occurred when init share cdc reader; Error: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.init(ShareCdcPDKTaskReader.java:153)
	at io.tapdata.flow.engine.V2.sharecdc.impl.ShareCdcFactory.shareCdcReader(ShareCdcFactory.java:36)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$46(HazelcastSourcePdkDataNode.java:906)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 12 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.getConstruct(ShareCdcPDKTaskReader.java:303)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.checkTableStartPointValid(ShareCdcPDKTaskReader.java:224)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.canShareCdc(ShareCdcPDKTaskReader.java:214)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.init(ShareCdcPDKTaskReader.java:146)
	... 15 more

[INFO ] 2024-07-15 19:07:47.011 - [任务 2][POLICY] - Job suspend in error handle 
[INFO ] 2024-07-15 19:07:47.012 - [任务 2][TESTPolicy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 19:07:47.037 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] running status set to false 
[INFO ] 2024-07-15 19:07:47.037 - [任务 2][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 19:07:47.037 - [任务 2][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 19:07:47.038 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] schema data cleaned 
[INFO ] 2024-07-15 19:07:47.043 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] monitor closed 
[INFO ] 2024-07-15 19:07:47.043 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] close complete, cost 18 ms 
[INFO ] 2024-07-15 19:07:47.043 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] running status set to false 
[INFO ] 2024-07-15 19:07:47.076 - [任务 2][TESTPolicy] - PDK connector node stopped: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 19:07:47.076 - [任务 2][TESTPolicy] - PDK connector node released: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 19:07:47.077 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] schema data cleaned 
[INFO ] 2024-07-15 19:07:47.077 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] monitor closed 
[INFO ] 2024-07-15 19:07:47.286 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] close complete, cost 34 ms 
[INFO ] 2024-07-15 19:07:50.925 - [任务 2] - Task [任务 2] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 19:07:50.933 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:07:50.941 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@50caec70 
[INFO ] 2024-07-15 19:07:50.941 - [任务 2] - Stop task milestones: 66949f941df4b966216a4ee0(任务 2)  
[INFO ] 2024-07-15 19:07:51.061 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:07:51.061 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:07:51.083 - [任务 2] - Remove memory task client succeed, task: 任务 2[66949f941df4b966216a4ee0] 
[INFO ] 2024-07-15 19:07:51.083 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66949f941df4b966216a4ee0] 
[INFO ] 2024-07-15 20:00:41.783 - [任务 2] - Start task milestones: 66949f941df4b966216a4ee0(任务 2) 
[INFO ] 2024-07-15 20:00:41.786 - [任务 2] - Task initialization... 
[INFO ] 2024-07-15 20:00:43.373 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:00:43.533 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 20:00:43.835 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] start preload schema,table counts: 1 
[INFO ] 2024-07-15 20:00:43.848 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:00:43.874 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] start preload schema,table counts: 1 
[INFO ] 2024-07-15 20:00:43.876 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:00:57.094 - [任务 2][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 20:00:57.094 - [任务 2][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 20:00:57.127 - [任务 2][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 20:00:57.157 - [任务 2][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1721016244,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 20:00:57.440 - [任务 2][TESTPolicy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 20:00:57.449 - [任务 2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 20:00:57.449 - [任务 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 20:00:57.546 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 20:00:57.548 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mongo enable share cdc: true 
[INFO ] 2024-07-15 20:00:57.548 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 2 enable share cdc: true 
[INFO ] 2024-07-15 20:00:57.672 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mongo的共享挖掘任务 
[INFO ] 2024-07-15 20:00:57.747 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 20:00:57.748 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:00:57.940 - [任务 2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:00:57.942 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66950f6866ab5ede8a947bb9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1931322122, shareCdcTaskId=66949fb11df4b966216a4f28, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:00:58.030 - [任务 2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1931322122', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:00:58.030 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 20:00:58.034 - [任务 2][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 20:00:58.039 - [任务 2][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 20:00:58.039 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 20:00:58.039 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-15 20:00:58.060 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:00:58.061 - [任务 2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:00:58.064 - [任务 2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 2, external storage name: ExternalStorage_SHARE_CDC_-1603875826 
[INFO ] 2024-07-15 20:00:58.065 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 20:00:58.075 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66950f6866ab5ede8a947bb9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1931322122, shareCdcTaskId=66949fb11df4b966216a4f28, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:00:58.076 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T04:04:04.579Z): 1 
[INFO ] 2024-07-15 20:00:58.077 - [任务 2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table_任务 2', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1931322122', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:00:58.077 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 20:00:58.077 - [任务 2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table_任务 2, external storage name: ExternalStorage_SHARE_CDC_-1931322122 
[INFO ] 2024-07-15 20:00:58.087 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-15 20:00:58.089 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 20:00:58.093 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(_tapdata_heartbeat_table) by timestamp(2024-07-15T04:04:04.579Z): 0 
[INFO ] 2024-07-15 20:00:58.094 - [任务 2][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 20:00:58.097 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 0 
[INFO ] 2024-07-15 20:00:58.309 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=0} 
[INFO ] 2024-07-15 20:00:59.722 - [任务 2][POLICY] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1721043782000, date=Mon Jul 15 19:43:02 CST 2024, before=Document{{}}, after=Document{{_id=66949fb566ab5ede8a857995, id=669497151df4b966216a48aa, ts=Mon Jul 15 19:53:08 CST 2024}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2OTUwQjQ2MDAwMDAwMTQyQjAyMkMwMTAwMjk2RTVBMTAwNDEx
NDM0QkVCRDBCMzRDMUNCOTBEQTM2QjQ5MEYxQ0U1NDY2NDVGNjk2NDAwNjQ2Njk0OUZCNTY2QUI1
RURFOEE4NTc5OTUwMDA0In2o
, type=DATA, connectionId=669497151df4b966216a48aa, isReplaceEvent=false, _ts=1721044858}} 
[INFO ] 2024-07-15 20:22:55.690 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] running status set to false 
[INFO ] 2024-07-15 20:22:55.721 - [任务 2][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 20:22:55.726 - [任务 2][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 20:22:55.727 - [任务 2][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-a0afcf32-f4ad-4dee-adf7-697dd92dd25d 
[INFO ] 2024-07-15 20:22:55.727 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] schema data cleaned 
[INFO ] 2024-07-15 20:22:55.728 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] monitor closed 
[INFO ] 2024-07-15 20:22:55.731 - [任务 2][POLICY] - Node POLICY[a0afcf32-f4ad-4dee-adf7-697dd92dd25d] close complete, cost 88 ms 
[INFO ] 2024-07-15 20:22:55.732 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] running status set to false 
[INFO ] 2024-07-15 20:22:55.892 - [任务 2][TESTPolicy] - PDK connector node stopped: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 20:22:55.892 - [任务 2][TESTPolicy] - PDK connector node released: HazelcastTargetPdkDataNode-5ee381a7-dc8c-4960-ac31-8dfa0711b0da 
[INFO ] 2024-07-15 20:22:55.893 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] schema data cleaned 
[INFO ] 2024-07-15 20:22:55.893 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] monitor closed 
[INFO ] 2024-07-15 20:22:55.894 - [任务 2][TESTPolicy] - Node TESTPolicy[5ee381a7-dc8c-4960-ac31-8dfa0711b0da] close complete, cost 162 ms 
[INFO ] 2024-07-15 20:23:00.187 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:23:00.203 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@15d0e5cd 
[INFO ] 2024-07-15 20:23:00.204 - [任务 2] - Stop task milestones: 66949f941df4b966216a4ee0(任务 2)  
[INFO ] 2024-07-15 20:23:00.369 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:23:00.369 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 20:23:00.397 - [任务 2] - Remove memory task client succeed, task: 任务 2[66949f941df4b966216a4ee0] 
[INFO ] 2024-07-15 20:23:00.397 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66949f941df4b966216a4ee0] 
