[INFO ] 2024-07-18 10:59:38.813 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Task initialization... 
[INFO ] 2024-07-18 10:59:39.017 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Start task milestones: 6698850f8315b25db9f53ae3(t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567) 
[INFO ] 2024-07-18 10:59:39.052 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:59:39.246 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - The engine receives t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:59:39.286 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[24e2f89c-0281-47c3-a92e-4bc386d55153] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:59:39.287 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[2aab9f34-1f9c-4be8-88a6-e38e5c4952a8] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:59:39.287 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[24e2f89c-0281-47c3-a92e-4bc386d55153] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:59:39.287 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[2aab9f34-1f9c-4be8-88a6-e38e5c4952a8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:59:39.666 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:59:39.756 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:59:39.756 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:59:39.756 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:59:39.756 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-18 10:59:39.955 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:59:39.958 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Starting batch read, table name: source_100w, offset: null 
[INFO ] 2024-07-18 10:59:39.958 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Table source_100w is going to be initial synced 
[INFO ] 2024-07-18 10:59:40.159 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Query table 'source_100w' counts: 1000000 
[INFO ] 2024-07-18 11:00:45.469 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Table [source_100w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:00:45.469 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:00:52.137 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[2aab9f34-1f9c-4be8-88a6-e38e5c4952a8] running status set to false 
[INFO ] 2024-07-18 11:00:52.137 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[24e2f89c-0281-47c3-a92e-4bc386d55153] running status set to false 
[INFO ] 2024-07-18 11:00:52.137 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-24e2f89c-0281-47c3-a92e-4bc386d55153 
[INFO ] 2024-07-18 11:00:52.137 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-2aab9f34-1f9c-4be8-88a6-e38e5c4952a8 
[INFO ] 2024-07-18 11:00:52.137 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-2aab9f34-1f9c-4be8-88a6-e38e5c4952a8 
[INFO ] 2024-07-18 11:00:52.137 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-24e2f89c-0281-47c3-a92e-4bc386d55153 
[INFO ] 2024-07-18 11:00:52.138 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[2aab9f34-1f9c-4be8-88a6-e38e5c4952a8] schema data cleaned 
[INFO ] 2024-07-18 11:00:52.138 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[24e2f89c-0281-47c3-a92e-4bc386d55153] schema data cleaned 
[INFO ] 2024-07-18 11:00:52.138 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[2aab9f34-1f9c-4be8-88a6-e38e5c4952a8] monitor closed 
[INFO ] 2024-07-18 11:00:52.138 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[24e2f89c-0281-47c3-a92e-4bc386d55153] monitor closed 
[INFO ] 2024-07-18 11:00:52.139 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[2aab9f34-1f9c-4be8-88a6-e38e5c4952a8] close complete, cost 78 ms 
[INFO ] 2024-07-18 11:00:52.139 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[24e2f89c-0281-47c3-a92e-4bc386d55153] close complete, cost 73 ms 
[INFO ] 2024-07-18 11:00:56.441 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:00:56.441 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@542f8bd5 
[INFO ] 2024-07-18 11:00:56.598 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Stop task milestones: 6698850f8315b25db9f53ae3(t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567)  
[INFO ] 2024-07-18 11:00:56.599 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:00:56.599 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:00:56.641 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Remove memory task client succeed, task: t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567[6698850f8315b25db9f53ae3] 
[INFO ] 2024-07-18 11:00:56.642 - [t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567] - Destroy memory task client cache succeed, task: t_2.4-2-mdb_to_mdb_full_1717403468657_3537-1721271567[6698850f8315b25db9f53ae3] 
