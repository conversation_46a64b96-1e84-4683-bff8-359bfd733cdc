[INFO ] 2024-07-01 12:18:37.725 - [任务 37] - Start task milestones: 66822d258946d747566381a9(任务 37) 
[INFO ] 2024-07-01 12:18:37.934 - [任务 37] - Task initialization... 
[INFO ] 2024-07-01 12:18:38.639 - [任务 37] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 12:18:38.842 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 12:18:39.027 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] start preload schema,table counts: 2 
[INFO ] 2024-07-01 12:18:39.028 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] start preload schema,table counts: 2 
[INFO ] 2024-07-01 12:18:39.030 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 12:18:39.280 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 12:18:40.218 - [任务 37][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 12:18:40.219 - [任务 37][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 12:18:40.219 - [任务 37][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 12:18:40.265 - [任务 37][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 12:18:40.267 - [任务 37][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 12:18:40.373 - [任务 37][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719807520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 12:18:40.464 - [任务 37][SourceMongo] - Initial sync started 
[INFO ] 2024-07-01 12:18:40.479 - [任务 37][SourceMongo] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-01 12:18:40.495 - [任务 37][SourceMongo] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-01 12:18:40.495 - [任务 37][SourceMongo] - Query table 'CUSTOMER' counts: 674 
[INFO ] 2024-07-01 12:18:40.641 - [任务 37][SourceMongo] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 12:18:40.642 - [任务 37][SourceMongo] - Starting batch read, table name: Custom_CLAIM, offset: null 
[INFO ] 2024-07-01 12:18:40.642 - [任务 37][SourceMongo] - Table Custom_CLAIM is going to be initial synced 
[INFO ] 2024-07-01 12:18:40.688 - [任务 37][SourceMongo] - Query table 'Custom_CLAIM' counts: 1130 
[INFO ] 2024-07-01 12:18:40.810 - [任务 37][SourceMongo] - Table [Custom_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 12:18:40.810 - [任务 37][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 12:18:40.815 - [任务 37][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 12:18:40.816 - [任务 37][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 12:18:40.822 - [任务 37][SourceMongo] - Starting stream read, table list: [CUSTOMER, Custom_CLAIM], offset: {"cdcOffset":1719807520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 12:18:40.853 - [任务 37][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, Custom_CLAIM], data change syncing 
[INFO ] 2024-07-01 12:18:54.926 - [任务 37] - Stop task milestones: 66822d258946d747566381a9(任务 37)  
[INFO ] 2024-07-01 12:18:55.106 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] running status set to false 
[INFO ] 2024-07-01 12:18:55.153 - [任务 37][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-232ebd48-980d-4910-b1fa-c5c484805e80 
[INFO ] 2024-07-01 12:18:55.154 - [任务 37][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-232ebd48-980d-4910-b1fa-c5c484805e80 
[INFO ] 2024-07-01 12:18:55.159 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] schema data cleaned 
[INFO ] 2024-07-01 12:18:55.160 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] monitor closed 
[INFO ] 2024-07-01 12:18:55.171 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] close complete, cost 56 ms 
[INFO ] 2024-07-01 12:18:55.172 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] running status set to false 
[INFO ] 2024-07-01 12:18:55.224 - [任务 37][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-97d7a685-c851-4edc-9065-7ca963353c08 
[INFO ] 2024-07-01 12:18:55.224 - [任务 37][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-97d7a685-c851-4edc-9065-7ca963353c08 
[INFO ] 2024-07-01 12:18:55.226 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] schema data cleaned 
[INFO ] 2024-07-01 12:18:55.227 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] monitor closed 
[INFO ] 2024-07-01 12:18:55.227 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] close complete, cost 56 ms 
[INFO ] 2024-07-01 12:18:58.626 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 12:18:58.628 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4edd4d54 
[INFO ] 2024-07-01 12:18:58.628 - [任务 37] - Stopped task aspect(s) 
[INFO ] 2024-07-01 12:18:58.628 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 12:18:58.689 - [任务 37] - Remove memory task client succeed, task: 任务 37[66822d258946d747566381a9] 
[INFO ] 2024-07-01 12:18:58.690 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[66822d258946d747566381a9] 
[INFO ] 2024-07-01 12:20:11.305 - [任务 37] - Start task milestones: 66822d258946d747566381a9(任务 37) 
[INFO ] 2024-07-01 12:20:11.310 - [任务 37] - Task initialization... 
[INFO ] 2024-07-01 12:20:11.703 - [任务 37] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 12:20:11.705 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 12:20:11.761 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] start preload schema,table counts: 3 
[INFO ] 2024-07-01 12:20:11.761 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] start preload schema,table counts: 3 
[INFO ] 2024-07-01 12:20:11.762 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 12:20:11.762 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 12:20:11.980 - [任务 37][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 12:20:11.980 - [任务 37][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 12:20:12.002 - [任务 37][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-01 12:20:12.103 - [任务 37][SourceMongo] - batch offset found: {"Custom_CLAIM":{"batch_read_connector_offset":{"sortKey":"_id","value":"657baffa62a1bacd962c870f","objectId":null},"batch_read_connector_status":"RUNNING"},"CUSTOMER":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1719807520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 12:20:12.104 - [任务 37][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 12:20:12.104 - [任务 37][SourceMongo] - Initial sync started 
[INFO ] 2024-07-01 12:20:12.104 - [任务 37][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 12:20:12.109 - [任务 37][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-01 12:20:12.211 - [任务 37][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-01 12:20:12.212 - [任务 37][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-01 12:20:12.229 - [任务 37][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 12:20:12.230 - [任务 37][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 12:20:12.234 - [任务 37][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 12:20:12.235 - [任务 37][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 12:20:12.235 - [任务 37][SourceMongo] - Starting stream read, table list: [CUSTOMER, POLICY, Custom_CLAIM], offset: {"cdcOffset":1719807520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 12:20:12.437 - [任务 37][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, POLICY, Custom_CLAIM], data change syncing 
[INFO ] 2024-07-01 14:10:34.608 - [任务 37] - Start task milestones: 66822d258946d747566381a9(任务 37) 
[INFO ] 2024-07-01 14:10:34.614 - [任务 37] - Task initialization... 
[INFO ] 2024-07-01 14:10:36.552 - [任务 37] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 14:10:37.163 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 14:10:37.662 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] start preload schema,table counts: 3 
[INFO ] 2024-07-01 14:10:37.663 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] start preload schema,table counts: 3 
[INFO ] 2024-07-01 14:10:37.664 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 14:10:37.665 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 14:10:38.563 - [任务 37][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 14:10:38.566 - [任务 37][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 14:10:38.607 - [任务 37][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-01 14:10:38.815 - [任务 37][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":true},"batch_read_connector_status":"RUNNING"},"Custom_CLAIM":{"batch_read_connector_offset":{"sortKey":"_id","value":"657baffa62a1bacd962c870f","objectId":null},"batch_read_connector_status":"RUNNING"},"CUSTOMER":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1719807520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:10:38.816 - [任务 37][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 14:10:38.817 - [任务 37][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 14:10:38.873 - [任务 37][SourceMongo] - Starting stream read, table list: [CUSTOMER, POLICY, Custom_CLAIM], offset: {"cdcOffset":1719807520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:10:38.874 - [任务 37][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 14:10:38.874 - [任务 37][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 14:10:38.952 - [任务 37][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, POLICY, Custom_CLAIM], data change syncing 
[INFO ] 2024-07-01 14:15:01.902 - [任务 37] - Stop task milestones: 66822d258946d747566381a9(任务 37)  
[INFO ] 2024-07-01 14:15:02.037 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] running status set to false 
[INFO ] 2024-07-01 14:15:02.087 - [任务 37][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-232ebd48-980d-4910-b1fa-c5c484805e80 
[INFO ] 2024-07-01 14:15:02.089 - [任务 37][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-232ebd48-980d-4910-b1fa-c5c484805e80 
[INFO ] 2024-07-01 14:15:02.089 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] schema data cleaned 
[INFO ] 2024-07-01 14:15:02.089 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] monitor closed 
[INFO ] 2024-07-01 14:15:02.095 - [任务 37][SourceMongo] - Node SourceMongo[232ebd48-980d-4910-b1fa-c5c484805e80] close complete, cost 58 ms 
[INFO ] 2024-07-01 14:15:02.099 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] running status set to false 
[INFO ] 2024-07-01 14:15:02.113 - [任务 37][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-97d7a685-c851-4edc-9065-7ca963353c08 
[INFO ] 2024-07-01 14:15:02.113 - [任务 37][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-97d7a685-c851-4edc-9065-7ca963353c08 
[INFO ] 2024-07-01 14:15:02.113 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] schema data cleaned 
[INFO ] 2024-07-01 14:15:02.114 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] monitor closed 
[INFO ] 2024-07-01 14:15:02.318 - [任务 37][SouceMysql] - Node SouceMysql[97d7a685-c851-4edc-9065-7ca963353c08] close complete, cost 19 ms 
[INFO ] 2024-07-01 14:15:06.734 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 14:15:06.737 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1cc903c7 
[INFO ] 2024-07-01 14:15:06.744 - [任务 37] - Stopped task aspect(s) 
[INFO ] 2024-07-01 14:15:06.745 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 14:15:06.832 - [任务 37] - Remove memory task client succeed, task: 任务 37[66822d258946d747566381a9] 
[INFO ] 2024-07-01 14:15:06.832 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[66822d258946d747566381a9] 
