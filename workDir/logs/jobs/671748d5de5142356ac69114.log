[INFO ] 2024-10-22 14:41:09.607 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:09.612 - [任务 2(100)][b40f320f-e17d-45ab-8d9a-2f7df3a7973e] - Node b40f320f-e17d-45ab-8d9a-2f7df3a7973e[b40f320f-e17d-45ab-8d9a-2f7df3a7973e] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:41:09.622 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:09.623 - [任务 2(100)][b40f320f-e17d-45ab-8d9a-2f7df3a7973e] - Node b40f320f-e17d-45ab-8d9a-2f7df3a7973e[b40f320f-e17d-45ab-8d9a-2f7df3a7973e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:09.624 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 2 ms 
[INFO ] 2024-10-22 14:41:09.626 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:14.147 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:41:14.150 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:14.157 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:14.158 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:41:14.168 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:41:14.168 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 46 ms 
[INFO ] 2024-10-22 14:41:14.218 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:41:14.218 - [任务 2(100)][b40f320f-e17d-45ab-8d9a-2f7df3a7973e] - Node b40f320f-e17d-45ab-8d9a-2f7df3a7973e[b40f320f-e17d-45ab-8d9a-2f7df3a7973e] running status set to false 
[INFO ] 2024-10-22 14:41:14.219 - [任务 2(100)][b40f320f-e17d-45ab-8d9a-2f7df3a7973e] - Node b40f320f-e17d-45ab-8d9a-2f7df3a7973e[b40f320f-e17d-45ab-8d9a-2f7df3a7973e] schema data cleaned 
[INFO ] 2024-10-22 14:41:14.219 - [任务 2(100)][b40f320f-e17d-45ab-8d9a-2f7df3a7973e] - Node b40f320f-e17d-45ab-8d9a-2f7df3a7973e[b40f320f-e17d-45ab-8d9a-2f7df3a7973e] monitor closed 
[INFO ] 2024-10-22 14:41:14.229 - [任务 2(100)][b40f320f-e17d-45ab-8d9a-2f7df3a7973e] - Node b40f320f-e17d-45ab-8d9a-2f7df3a7973e[b40f320f-e17d-45ab-8d9a-2f7df3a7973e] close complete, cost 5 ms 
[INFO ] 2024-10-22 14:41:14.229 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-a0e58f36-fa35-4e1f-a27e-f366f69ceaf0 
[INFO ] 2024-10-22 14:41:14.230 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-a0e58f36-fa35-4e1f-a27e-f366f69ceaf0 
[INFO ] 2024-10-22 14:41:14.231 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:41:14.238 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:41:14.238 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:41:14.238 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 49 ms 
[INFO ] 2024-10-22 14:41:14.248 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:41:14.248 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:41:14.249 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:41:31.009 - [任务 2(100)][495bb749-c580-47cc-b6d3-7357bd39d39c] - Node 495bb749-c580-47cc-b6d3-7357bd39d39c[495bb749-c580-47cc-b6d3-7357bd39d39c] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:41:31.010 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:31.011 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:31.026 - [任务 2(100)][495bb749-c580-47cc-b6d3-7357bd39d39c] - Node 495bb749-c580-47cc-b6d3-7357bd39d39c[495bb749-c580-47cc-b6d3-7357bd39d39c] preload schema finished, cost 1 ms 
[INFO ] 2024-10-22 14:41:31.029 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:31.029 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:31.202 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:31.202 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:31.203 - [任务 2(100)][1a1976c2-a707-47f0-9e04-768f67391a50] - Node 1a1976c2-a707-47f0-9e04-768f67391a50[1a1976c2-a707-47f0-9e04-768f67391a50] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:41:31.203 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:31.203 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:31.203 - [任务 2(100)][1a1976c2-a707-47f0-9e04-768f67391a50] - Node 1a1976c2-a707-47f0-9e04-768f67391a50[1a1976c2-a707-47f0-9e04-768f67391a50] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:31.370 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:41:31.371 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:31.373 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:31.373 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.374 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:41:31.375 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 29 ms 
[INFO ] 2024-10-22 14:41:31.587 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:41:31.610 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:31.614 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:41:31.616 - [任务 2(100)][1a1976c2-a707-47f0-9e04-768f67391a50] - Node 1a1976c2-a707-47f0-9e04-768f67391a50[1a1976c2-a707-47f0-9e04-768f67391a50] running status set to false 
[INFO ] 2024-10-22 14:41:31.617 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:31.617 - [任务 2(100)][1a1976c2-a707-47f0-9e04-768f67391a50] - Node 1a1976c2-a707-47f0-9e04-768f67391a50[1a1976c2-a707-47f0-9e04-768f67391a50] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.619 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.619 - [任务 2(100)][1a1976c2-a707-47f0-9e04-768f67391a50] - Node 1a1976c2-a707-47f0-9e04-768f67391a50[1a1976c2-a707-47f0-9e04-768f67391a50] monitor closed 
[INFO ] 2024-10-22 14:41:31.619 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:41:31.620 - [任务 2(100)][1a1976c2-a707-47f0-9e04-768f67391a50] - Node 1a1976c2-a707-47f0-9e04-768f67391a50[1a1976c2-a707-47f0-9e04-768f67391a50] close complete, cost 10 ms 
[INFO ] 2024-10-22 14:41:31.621 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 44 ms 
[INFO ] 2024-10-22 14:41:31.642 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-f06f2cc4-bb1d-4f5a-a98b-3e73e9bd661f 
[INFO ] 2024-10-22 14:41:31.643 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-f06f2cc4-bb1d-4f5a-a98b-3e73e9bd661f 
[INFO ] 2024-10-22 14:41:31.643 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.646 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.647 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:41:31.650 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 42 ms 
[INFO ] 2024-10-22 14:41:31.652 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:41:31.653 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:41:31.659 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:41:31.812 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:41:31.813 - [任务 2(100)][495bb749-c580-47cc-b6d3-7357bd39d39c] - Node 495bb749-c580-47cc-b6d3-7357bd39d39c[495bb749-c580-47cc-b6d3-7357bd39d39c] running status set to false 
[INFO ] 2024-10-22 14:41:31.813 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-2d433028-165e-4db8-afe1-96b162c6cec7 
[INFO ] 2024-10-22 14:41:31.813 - [任务 2(100)][495bb749-c580-47cc-b6d3-7357bd39d39c] - Node 495bb749-c580-47cc-b6d3-7357bd39d39c[495bb749-c580-47cc-b6d3-7357bd39d39c] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.814 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-2d433028-165e-4db8-afe1-96b162c6cec7 
[INFO ] 2024-10-22 14:41:31.814 - [任务 2(100)][495bb749-c580-47cc-b6d3-7357bd39d39c] - Node 495bb749-c580-47cc-b6d3-7357bd39d39c[495bb749-c580-47cc-b6d3-7357bd39d39c] monitor closed 
[INFO ] 2024-10-22 14:41:31.815 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.815 - [任务 2(100)][495bb749-c580-47cc-b6d3-7357bd39d39c] - Node 495bb749-c580-47cc-b6d3-7357bd39d39c[495bb749-c580-47cc-b6d3-7357bd39d39c] close complete, cost 7 ms 
[INFO ] 2024-10-22 14:41:31.816 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:41:31.817 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:41:31.820 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 34 ms 
[INFO ] 2024-10-22 14:41:31.820 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:41:31.821 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:41:32.025 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:41:35.834 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:35.840 - [任务 2(100)][2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] - Node 2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600[2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:41:35.840 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:41:35.842 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:35.842 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:36.052 - [任务 2(100)][2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] - Node 2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600[2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:41:36.162 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:41:36.163 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:36.163 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:41:36.163 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:41:36.164 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:41:36.164 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 26 ms 
[INFO ] 2024-10-22 14:41:36.350 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:41:36.352 - [任务 2(100)][2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] - Node 2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600[2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] running status set to false 
[INFO ] 2024-10-22 14:41:36.357 - [任务 2(100)][2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] - Node 2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600[2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] schema data cleaned 
[INFO ] 2024-10-22 14:41:36.377 - [任务 2(100)][2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] - Node 2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600[2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] monitor closed 
[INFO ] 2024-10-22 14:41:36.378 - [任务 2(100)][2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] - Node 2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600[2d68cc06-4ee2-4ab0-8dd7-d8c0c3c21600] close complete, cost 33 ms 
[INFO ] 2024-10-22 14:41:36.400 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-cf299d83-072b-45c6-81ad-2a672a66a170 
[INFO ] 2024-10-22 14:41:36.400 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-cf299d83-072b-45c6-81ad-2a672a66a170 
[INFO ] 2024-10-22 14:41:36.407 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:41:36.408 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:41:36.408 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:41:36.409 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 75 ms 
[INFO ] 2024-10-22 14:41:36.411 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:41:36.411 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:41:36.616 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:42:26.185 - [任务 2(100)][3b8f149c-7bc1-4884-b04b-b3184b007a11] - Node 3b8f149c-7bc1-4884-b04b-b3184b007a11[3b8f149c-7bc1-4884-b04b-b3184b007a11] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:42:26.185 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:42:26.185 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:42:26.185 - [任务 2(100)][3b8f149c-7bc1-4884-b04b-b3184b007a11] - Node 3b8f149c-7bc1-4884-b04b-b3184b007a11[3b8f149c-7bc1-4884-b04b-b3184b007a11] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:42:26.194 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:42:26.196 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:42:26.494 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:42:26.494 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:42:26.495 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:42:26.495 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:42:26.495 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:42:26.495 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 25 ms 
[INFO ] 2024-10-22 14:42:26.847 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check 
[ERROR] 2024-10-22 14:42:26.855 - [任务 2(100)][增强JS] - javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check <-- Error Message -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:251)
	... 1 more
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:124)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-10-22 14:42:29.462 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:42:29.464 - [任务 2(100)][3b8f149c-7bc1-4884-b04b-b3184b007a11] - Node 3b8f149c-7bc1-4884-b04b-b3184b007a11[3b8f149c-7bc1-4884-b04b-b3184b007a11] running status set to false 
[INFO ] 2024-10-22 14:42:29.468 - [任务 2(100)][3b8f149c-7bc1-4884-b04b-b3184b007a11] - Node 3b8f149c-7bc1-4884-b04b-b3184b007a11[3b8f149c-7bc1-4884-b04b-b3184b007a11] schema data cleaned 
[INFO ] 2024-10-22 14:42:29.468 - [任务 2(100)][3b8f149c-7bc1-4884-b04b-b3184b007a11] - Node 3b8f149c-7bc1-4884-b04b-b3184b007a11[3b8f149c-7bc1-4884-b04b-b3184b007a11] monitor closed 
[INFO ] 2024-10-22 14:42:29.481 - [任务 2(100)][3b8f149c-7bc1-4884-b04b-b3184b007a11] - Node 3b8f149c-7bc1-4884-b04b-b3184b007a11[3b8f149c-7bc1-4884-b04b-b3184b007a11] close complete, cost 19 ms 
[INFO ] 2024-10-22 14:42:29.482 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-4a81bc50-f4ac-4a22-a830-de9907e2d795 
[INFO ] 2024-10-22 14:42:29.483 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-4a81bc50-f4ac-4a22-a830-de9907e2d795 
[INFO ] 2024-10-22 14:42:29.483 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:42:29.486 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:42:29.486 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:42:29.488 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 41 ms 
[INFO ] 2024-10-22 14:42:29.490 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:42:29.490 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:42:29.491 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:42:38.965 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:42:38.966 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:42:38.966 - [任务 2(100)][b1c69d89-7372-4c77-b66f-68f056e92364] - Node b1c69d89-7372-4c77-b66f-68f056e92364[b1c69d89-7372-4c77-b66f-68f056e92364] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:42:38.966 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:42:38.966 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:42:38.966 - [任务 2(100)][b1c69d89-7372-4c77-b66f-68f056e92364] - Node b1c69d89-7372-4c77-b66f-68f056e92364[b1c69d89-7372-4c77-b66f-68f056e92364] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:42:39.261 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:42:39.276 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:42:39.276 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:42:39.276 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:42:39.277 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:42:39.278 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 20 ms 
[INFO ] 2024-10-22 14:42:39.487 - [任务 2(100)][增强JS] - create script executor for Mysql 
[INFO ] 2024-10-22 14:42:39.682 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:42:39.695 - [任务 2(100)][b1c69d89-7372-4c77-b66f-68f056e92364] - Node b1c69d89-7372-4c77-b66f-68f056e92364[b1c69d89-7372-4c77-b66f-68f056e92364] running status set to false 
[INFO ] 2024-10-22 14:42:39.696 - [任务 2(100)][b1c69d89-7372-4c77-b66f-68f056e92364] - Node b1c69d89-7372-4c77-b66f-68f056e92364[b1c69d89-7372-4c77-b66f-68f056e92364] schema data cleaned 
[INFO ] 2024-10-22 14:42:39.697 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-e8231855-0177-4944-9295-7250b69da54f 
[INFO ] 2024-10-22 14:42:39.697 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-e8231855-0177-4944-9295-7250b69da54f 
[INFO ] 2024-10-22 14:42:39.697 - [任务 2(100)][b1c69d89-7372-4c77-b66f-68f056e92364] - Node b1c69d89-7372-4c77-b66f-68f056e92364[b1c69d89-7372-4c77-b66f-68f056e92364] monitor closed 
[INFO ] 2024-10-22 14:42:39.697 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:42:39.699 - [任务 2(100)][b1c69d89-7372-4c77-b66f-68f056e92364] - Node b1c69d89-7372-4c77-b66f-68f056e92364[b1c69d89-7372-4c77-b66f-68f056e92364] close complete, cost 8 ms 
[INFO ] 2024-10-22 14:42:39.702 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-44ec24cc-dd5b-4e9a-b094-842c31b4ef7b 
[INFO ] 2024-10-22 14:42:39.702 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-44ec24cc-dd5b-4e9a-b094-842c31b4ef7b 
[INFO ] 2024-10-22 14:42:39.705 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:42:39.705 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:42:39.706 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:42:39.706 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 24 ms 
[INFO ] 2024-10-22 14:42:39.710 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:42:39.710 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:42:39.710 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:43:13.855 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:13.855 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:13.856 - [任务 2(100)][d4651ccf-138f-487c-ab13-28a6c34a761e] - Node d4651ccf-138f-487c-ab13-28a6c34a761e[d4651ccf-138f-487c-ab13-28a6c34a761e] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:43:13.856 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:13.857 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:13.857 - [任务 2(100)][d4651ccf-138f-487c-ab13-28a6c34a761e] - Node d4651ccf-138f-487c-ab13-28a6c34a761e[d4651ccf-138f-487c-ab13-28a6c34a761e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:14.209 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:43:14.210 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:14.210 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:14.210 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:43:14.210 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:43:14.375 - [任务 2(100)][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 24 ms 
[INFO ] 2024-10-22 14:43:14.375 - [任务 2(100)][增强JS] - create script executor for Mysql 
[INFO ] 2024-10-22 14:43:14.620 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.RuntimeException: script execute error 
[ERROR] 2024-10-22 14:43:14.622 - [任务 2(100)][增强JS] - javax.script.ScriptException: java.lang.RuntimeException: script execute error <-- Error Message -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:251)
	... 1 more
Caused by: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.DefaultSqlExecutor.execute(DefaultSqlExecutor.java:49)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:76)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:64)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$6(MysqlConnector.java:212)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:238)
	... 55 more

[INFO ] 2024-10-22 14:43:16.433 - [任务 2(100)][32306e0b-f5de-48a8-9699-6a2081e8f3a0] - Node 32306e0b-f5de-48a8-9699-6a2081e8f3a0[32306e0b-f5de-48a8-9699-6a2081e8f3a0] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:43:16.436 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:16.440 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:16.440 - [任务 2(100)][32306e0b-f5de-48a8-9699-6a2081e8f3a0] - Node 32306e0b-f5de-48a8-9699-6a2081e8f3a0[32306e0b-f5de-48a8-9699-6a2081e8f3a0] preload schema finished, cost 1 ms 
[INFO ] 2024-10-22 14:43:16.440 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:16.441 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:16.849 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:43:16.865 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:16.866 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:16.866 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:43:16.866 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:43:16.866 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 31 ms 
[INFO ] 2024-10-22 14:43:16.877 - [任务 2(100)][增强JS] - create script executor for Mysql 
[INFO ] 2024-10-22 14:43:17.151 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.RuntimeException: script execute error 
[ERROR] 2024-10-22 14:43:17.152 - [任务 2(100)][增强JS] - javax.script.ScriptException: java.lang.RuntimeException: script execute error <-- Error Message -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:251)
	... 1 more
Caused by: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.DefaultSqlExecutor.execute(DefaultSqlExecutor.java:49)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:76)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:64)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$6(MysqlConnector.java:212)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:238)
	... 55 more

[INFO ] 2024-10-22 14:43:17.170 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:43:17.172 - [任务 2(100)][d4651ccf-138f-487c-ab13-28a6c34a761e] - Node d4651ccf-138f-487c-ab13-28a6c34a761e[d4651ccf-138f-487c-ab13-28a6c34a761e] running status set to false 
[INFO ] 2024-10-22 14:43:17.175 - [任务 2(100)][d4651ccf-138f-487c-ab13-28a6c34a761e] - Node d4651ccf-138f-487c-ab13-28a6c34a761e[d4651ccf-138f-487c-ab13-28a6c34a761e] schema data cleaned 
[INFO ] 2024-10-22 14:43:17.175 - [任务 2(100)][d4651ccf-138f-487c-ab13-28a6c34a761e] - Node d4651ccf-138f-487c-ab13-28a6c34a761e[d4651ccf-138f-487c-ab13-28a6c34a761e] monitor closed 
[INFO ] 2024-10-22 14:43:17.182 - [任务 2(100)][d4651ccf-138f-487c-ab13-28a6c34a761e] - Node d4651ccf-138f-487c-ab13-28a6c34a761e[d4651ccf-138f-487c-ab13-28a6c34a761e] close complete, cost 13 ms 
[INFO ] 2024-10-22 14:43:17.182 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-82baf17b-9ca2-459f-9719-a9bca2b8e009 
[INFO ] 2024-10-22 14:43:17.183 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-82baf17b-9ca2-459f-9719-a9bca2b8e009 
[INFO ] 2024-10-22 14:43:17.183 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:17.192 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-eaf0de53-cddf-4cb8-b542-af75a4e1519b 
[INFO ] 2024-10-22 14:43:17.192 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-eaf0de53-cddf-4cb8-b542-af75a4e1519b 
[INFO ] 2024-10-22 14:43:17.192 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:17.198 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:43:17.198 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:43:17.200 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 43 ms 
[INFO ] 2024-10-22 14:43:17.201 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:43:17.201 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:43:17.404 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:43:19.711 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:43:19.712 - [任务 2(100)][32306e0b-f5de-48a8-9699-6a2081e8f3a0] - Node 32306e0b-f5de-48a8-9699-6a2081e8f3a0[32306e0b-f5de-48a8-9699-6a2081e8f3a0] running status set to false 
[INFO ] 2024-10-22 14:43:19.737 - [任务 2(100)][32306e0b-f5de-48a8-9699-6a2081e8f3a0] - Node 32306e0b-f5de-48a8-9699-6a2081e8f3a0[32306e0b-f5de-48a8-9699-6a2081e8f3a0] schema data cleaned 
[INFO ] 2024-10-22 14:43:19.737 - [任务 2(100)][32306e0b-f5de-48a8-9699-6a2081e8f3a0] - Node 32306e0b-f5de-48a8-9699-6a2081e8f3a0[32306e0b-f5de-48a8-9699-6a2081e8f3a0] monitor closed 
[INFO ] 2024-10-22 14:43:19.739 - [任务 2(100)][32306e0b-f5de-48a8-9699-6a2081e8f3a0] - Node 32306e0b-f5de-48a8-9699-6a2081e8f3a0[32306e0b-f5de-48a8-9699-6a2081e8f3a0] close complete, cost 38 ms 
[INFO ] 2024-10-22 14:43:19.739 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-69683838-3bd2-4e78-acfa-9efbd41ea0c5 
[INFO ] 2024-10-22 14:43:19.740 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-69683838-3bd2-4e78-acfa-9efbd41ea0c5 
[INFO ] 2024-10-22 14:43:19.740 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:19.758 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-e6ccea16-9f08-4c83-9916-d9a84efdcfce 
[INFO ] 2024-10-22 14:43:19.759 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-e6ccea16-9f08-4c83-9916-d9a84efdcfce 
[INFO ] 2024-10-22 14:43:19.764 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:19.764 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:43:19.765 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:43:19.766 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 76 ms 
[INFO ] 2024-10-22 14:43:19.768 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:43:19.769 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:43:19.769 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:43:25.504 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:25.505 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:25.505 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:25.506 - [任务 2(100)][36abb80b-3892-48b0-ae22-bd1e4179e1ce] - Node 36abb80b-3892-48b0-ae22-bd1e4179e1ce[36abb80b-3892-48b0-ae22-bd1e4179e1ce] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:43:25.507 - [任务 2(100)][36abb80b-3892-48b0-ae22-bd1e4179e1ce] - Node 36abb80b-3892-48b0-ae22-bd1e4179e1ce[36abb80b-3892-48b0-ae22-bd1e4179e1ce] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:25.507 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:25.863 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:43:25.879 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:25.879 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:25.879 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:43:25.881 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:43:25.885 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 37 ms 
[INFO ] 2024-10-22 14:43:26.090 - [任务 2(100)][增强JS] - create script executor for Mysql 
[INFO ] 2024-10-22 14:43:26.474 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:43:26.485 - [任务 2(100)][36abb80b-3892-48b0-ae22-bd1e4179e1ce] - Node 36abb80b-3892-48b0-ae22-bd1e4179e1ce[36abb80b-3892-48b0-ae22-bd1e4179e1ce] running status set to false 
[INFO ] 2024-10-22 14:43:26.489 - [任务 2(100)][36abb80b-3892-48b0-ae22-bd1e4179e1ce] - Node 36abb80b-3892-48b0-ae22-bd1e4179e1ce[36abb80b-3892-48b0-ae22-bd1e4179e1ce] schema data cleaned 
[INFO ] 2024-10-22 14:43:26.491 - [任务 2(100)][36abb80b-3892-48b0-ae22-bd1e4179e1ce] - Node 36abb80b-3892-48b0-ae22-bd1e4179e1ce[36abb80b-3892-48b0-ae22-bd1e4179e1ce] monitor closed 
[INFO ] 2024-10-22 14:43:26.492 - [任务 2(100)][36abb80b-3892-48b0-ae22-bd1e4179e1ce] - Node 36abb80b-3892-48b0-ae22-bd1e4179e1ce[36abb80b-3892-48b0-ae22-bd1e4179e1ce] close complete, cost 21 ms 
[INFO ] 2024-10-22 14:43:26.498 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-13fc44b5-9cde-40ed-9ca9-6678d6ec18be 
[INFO ] 2024-10-22 14:43:26.498 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-13fc44b5-9cde-40ed-9ca9-6678d6ec18be 
[INFO ] 2024-10-22 14:43:26.505 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:26.505 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-bad6fa80-8462-4b56-8db0-98ec43b92c29 
[INFO ] 2024-10-22 14:43:26.505 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-bad6fa80-8462-4b56-8db0-98ec43b92c29 
[INFO ] 2024-10-22 14:43:26.510 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:26.511 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:43:26.511 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:43:26.512 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 47 ms 
[INFO ] 2024-10-22 14:43:26.515 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:43:26.516 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:43:26.516 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-22 14:43:38.508 - [任务 2(100)][4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] - Node 4f4e4fdd-964a-40dc-8f87-8afcc545ee0a[4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] start preload schema,table counts: 0 
[INFO ] 2024-10-22 14:43:38.509 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:38.509 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] start preload schema,table counts: 1 
[INFO ] 2024-10-22 14:43:38.509 - [任务 2(100)][4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] - Node 4f4e4fdd-964a-40dc-8f87-8afcc545ee0a[4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:38.510 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:38.510 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-22 14:43:38.870 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] running status set to false 
[INFO ] 2024-10-22 14:43:38.870 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:38.871 - [任务 2(100)][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8dd6123f-0455-4ad0-b94c-4b65e6558e13 
[INFO ] 2024-10-22 14:43:38.871 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] schema data cleaned 
[INFO ] 2024-10-22 14:43:38.871 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] monitor closed 
[INFO ] 2024-10-22 14:43:39.023 - [任务 2(100)][0620_CAR_CLAIM_M] - Node AA00PP[8dd6123f-0455-4ad0-b94c-4b65e6558e13] close complete, cost 19 ms 
[INFO ] 2024-10-22 14:43:39.023 - [任务 2(100)][增强JS] - create script executor for Mysql 
[INFO ] 2024-10-22 14:43:39.328 - [任务 2(100)][增强JS] - [{Table=AA00PP, Create Table=CREATE TABLE `AA00PP` (
  `A1` varchar(48) NOT NULL,
  `A2` datetime NOT NULL,
  `A3` decimal(20,0) DEFAULT NULL,
  `A4` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`A1`,`A2`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci}] 
[INFO ] 2024-10-22 14:43:39.345 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] running status set to false 
[INFO ] 2024-10-22 14:43:39.346 - [任务 2(100)][4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] - Node 4f4e4fdd-964a-40dc-8f87-8afcc545ee0a[4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] running status set to false 
[INFO ] 2024-10-22 14:43:39.365 - [任务 2(100)][4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] - Node 4f4e4fdd-964a-40dc-8f87-8afcc545ee0a[4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] schema data cleaned 
[INFO ] 2024-10-22 14:43:39.367 - [任务 2(100)][4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] - Node 4f4e4fdd-964a-40dc-8f87-8afcc545ee0a[4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] monitor closed 
[INFO ] 2024-10-22 14:43:39.367 - [任务 2(100)][4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] - Node 4f4e4fdd-964a-40dc-8f87-8afcc545ee0a[4f4e4fdd-964a-40dc-8f87-8afcc545ee0a] close complete, cost 1 ms 
[INFO ] 2024-10-22 14:43:39.367 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-47b69541-4040-4c08-b317-b1e63c01cb5a 
[INFO ] 2024-10-22 14:43:39.368 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-47b69541-4040-4c08-b317-b1e63c01cb5a 
[INFO ] 2024-10-22 14:43:39.368 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:39.376 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql-6850de7e-f9b7-406d-ada1-ec768741ac39 
[INFO ] 2024-10-22 14:43:39.376 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-Mysql-6850de7e-f9b7-406d-ada1-ec768741ac39 
[INFO ] 2024-10-22 14:43:39.388 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-671748d5de5142356ac69114-0c54dffa-370f-4f33-ba96-d6485cdfdf1e-6717487bde5142356ac690fb] schema data cleaned 
[INFO ] 2024-10-22 14:43:39.388 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] schema data cleaned 
[INFO ] 2024-10-22 14:43:39.388 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] monitor closed 
[INFO ] 2024-10-22 14:43:39.389 - [任务 2(100)][增强JS] - Node 增强JS[0c54dffa-370f-4f33-ba96-d6485cdfdf1e] close complete, cost 60 ms 
[INFO ] 2024-10-22 14:43:39.391 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-22 14:43:39.391 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-22 14:43:39.425 - [任务 2(100)] - Stopped task aspect(s) 
