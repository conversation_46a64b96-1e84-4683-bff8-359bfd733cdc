[INFO ] 2024-07-04 14:28:23.688 - [Task 4] - Start task milestones: 6682d405494d8d2a5c957f38(Task 4) 
[INFO ] 2024-07-04 14:28:23.692 - [Task 4] - Task initialization... 
[INFO ] 2024-07-04 14:28:24.741 - [Task 4] - load tapTable task 6682d405494d8d2a5c957f37-38572837-d4ad-4fd8-ba55-0d5c9c41c404 complete, cost 924ms 
[INFO ] 2024-07-04 14:28:24.879 - [Task 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-04 14:28:24.971 - [Task 4] - The engine receives Task 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 14:28:24.971 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:28:24.971 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:28:24.971 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:28:24.971 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:24.971 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:25.176 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:25.259 - [Task 4][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-04 14:28:25.262 - [Task 4][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-04 14:28:25.263 - [Task 4][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-04 14:28:25.479 - [Task 4][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1720074505,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-04 14:28:25.557 - [Task 4][POLICY] - Initial sync started 
[INFO ] 2024-07-04 14:28:25.558 - [Task 4][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-04 14:28:25.611 - [Task 4][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-04 14:28:25.615 - [Task 4][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-04 14:28:25.823 - [Task 4][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-04 14:28:26.400 - [Task 4][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-04 14:28:26.403 - [Task 4][POLICY] - Initial sync completed 
[INFO ] 2024-07-04 14:28:26.403 - [Task 4][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-04 14:28:26.403 - [Task 4][POLICY] - Initial sync completed 
[INFO ] 2024-07-04 14:28:26.405 - [Task 4][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1720074505,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-04 14:28:26.611 - [Task 4][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[WARN ] 2024-07-04 14:39:01.495 - [Task 4] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out. 
[INFO ] 2024-07-04 14:39:01.509 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] running status set to false 
[INFO ] 2024-07-04 14:39:01.527 - [Task 4][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 14:39:01.528 - [Task 4][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 14:39:01.528 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] schema data cleaned 
[INFO ] 2024-07-04 14:39:01.529 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] monitor closed 
[INFO ] 2024-07-04 14:39:01.531 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] close complete, cost 23 ms 
[INFO ] 2024-07-04 14:39:01.539 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] running status set to false 
[INFO ] 2024-07-04 14:39:01.539 - [Task 4][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-5f01c89b-8ced-4963-9a96-631249852c88 
[INFO ] 2024-07-04 14:39:01.539 - [Task 4][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-5f01c89b-8ced-4963-9a96-631249852c88 
[INFO ] 2024-07-04 14:39:01.540 - [Task 4][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f38-bca58ceb-88e7-4364-bfef-bde381562d81-6674feb868ca1e3afc2a0d99] schema data cleaned 
[INFO ] 2024-07-04 14:39:01.550 - [Task 4][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-78360fae-d889-4859-b7ae-bd27c58c8db4 
[INFO ] 2024-07-04 14:39:01.550 - [Task 4][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-78360fae-d889-4859-b7ae-bd27c58c8db4 
[INFO ] 2024-07-04 14:39:01.550 - [Task 4][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f38-bca58ceb-88e7-4364-bfef-bde381562d81-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-07-04 14:39:01.561 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] schema data cleaned 
[INFO ] 2024-07-04 14:39:01.568 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] monitor closed 
[INFO ] 2024-07-04 14:39:01.568 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] close complete, cost 36 ms 
[INFO ] 2024-07-04 14:39:01.667 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] running status set to false 
[INFO ] 2024-07-04 14:39:01.669 - [Task 4][POLICY] - Incremental sync completed 
[WARN ] 2024-07-04 14:39:26.645 - [Task 4][POLICY] - Save to snapshot failed, collection: Task/syncProgress/6682d405494d8d2a5c957f38, object: {32a9e251-e83a-4311-915f-f151503bb049,77275a98-e971-4916-a599-6440c35c2203=SyncProgress{eventSerialNo=297, syncStage='CDC', batchOffset='{POLICY={batch_read_connector_offset=MongoBatchOffset[sortKey='_id', value=6510f74ca270a1cf5533d1b0, objectId=null], batch_read_connector_status=RUNNING}}', streamOffset='{"_data": "826686427D000000022B022C0100296E5A10049FB68840A87A415DAE43B3753D2FBFC5463C5F6964003C363531306637346361323730613163663535333363656634000004"}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/6682d405494d8d2a5c957f38": Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out. 
[INFO ] 2024-07-04 14:40:07.570 - [Task 4][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-77275a98-e971-4916-a599-6440c35c2203 
[INFO ] 2024-07-04 14:40:07.570 - [Task 4][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-77275a98-e971-4916-a599-6440c35c2203 
[INFO ] 2024-07-04 14:40:07.570 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] schema data cleaned 
[INFO ] 2024-07-04 14:40:07.570 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] monitor closed 
[INFO ] 2024-07-04 14:40:07.575 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] close complete, cost 66001 ms 
[INFO ] 2024-07-04 14:44:38.829 - [Task 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[WARN ] 2024-07-04 14:44:38.829 - [Task 4] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-04 14:44:38.838 - [Task 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@594f3541 
[INFO ] 2024-07-04 14:44:38.839 - [Task 4] - Stop task milestones: 6682d405494d8d2a5c957f38(Task 4)  
[WARN ] 2024-07-04 14:49:03.070 - [Task 4] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-04 14:49:03.180 - [Task 4] - Stopped task aspect(s) 
[INFO ] 2024-07-04 14:49:03.180 - [Task 4] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 14:49:03.204 - [Task 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 14:49:03.205 - [Task 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@594f3541 
[INFO ] 2024-07-04 14:49:03.205 - [Task 4] - Stopped task aspect(s) 
[INFO ] 2024-07-04 14:49:03.206 - [Task 4] - Remove memory task client succeed, task: Task 4[6682d405494d8d2a5c957f38] 
[INFO ] 2024-07-04 14:49:03.206 - [Task 4] - Destroy memory task client cache succeed, task: Task 4[6682d405494d8d2a5c957f38] 
[INFO ] 2024-07-04 15:00:06.827 - [Task 4] - Start task milestones: 6682d405494d8d2a5c957f38(Task 4) 
[INFO ] 2024-07-04 15:00:06.827 - [Task 4] - Task initialization... 
[INFO ] 2024-07-04 15:00:07.259 - [Task 4] - load tapTable task 6682d405494d8d2a5c957f37-3f591d88-d010-45e0-b87c-7cf9e78556b1 complete, cost 344ms 
[INFO ] 2024-07-04 15:00:07.348 - [Task 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-04 15:00:07.506 - [Task 4] - The engine receives Task 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 15:00:07.506 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:00:07.506 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:00:07.507 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:00:07.507 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:00:07.507 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:00:07.507 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:00:07.759 - [Task 4][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-04 15:00:07.759 - [Task 4][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-04 15:00:07.760 - [Task 4][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 15:00:07.844 - [Task 4][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"_data":{"value":"826686427D000000022B022C0100296E5A10049FB68840A87A415DAE43B3753D2FBFC5463C5F6964003C363531306637346361323730613163663535333363656634000004","bsonType":"STRING","string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-04 15:00:07.844 - [Task 4][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-04 15:00:07.854 - [Task 4][POLICY] - Initial sync completed 
[INFO ] 2024-07-04 15:00:07.854 - [Task 4][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"826686427D000000022B022C0100296E5A10049FB68840A87A415DAE43B3753D2FBFC5463C5F6964003C363531306637346361323730613163663535333363656634000004","bsonType":"STRING","string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-04 15:00:07.928 - [Task 4][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-04 15:00:07.929 - [Task 4][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-04 15:02:12.810 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] running status set to false 
[WARN ] 2024-07-04 15:46:29.661 - [Task 4][POLICY] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 15:46:29.705 - [Task 4] - Task initialization... 
[INFO ] 2024-07-04 15:46:29.854 - [Task 4] - Start task milestones: 6682d405494d8d2a5c957f38(Task 4) 
[INFO ] 2024-07-04 15:46:32.687 - [Task 4] - load tapTable task 6682d405494d8d2a5c957f37-ac37d82d-56ff-466f-b673-13021d1f641e complete, cost 1933ms 
[INFO ] 2024-07-04 15:46:32.865 - [Task 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-04 15:46:32.954 - [Task 4] - The engine receives Task 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 15:46:33.061 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:46:33.086 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] preload schema finished, cost 1 ms 
[INFO ] 2024-07-04 15:46:33.086 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:46:33.086 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:46:33.086 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:46:33.086 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:46:33.416 - [Task 4][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-04 15:46:33.417 - [Task 4][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-04 15:46:33.419 - [Task 4][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 15:46:33.532 - [Task 4][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"_data":{"value":"826686427D000000022B022C0100296E5A10049FB68840A87A415DAE43B3753D2FBFC5463C5F6964003C363531306637346361323730613163663535333363656634000004","bsonType":"STRING","boolean":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-04 15:46:33.532 - [Task 4][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-04 15:46:33.532 - [Task 4][POLICY] - Initial sync completed 
[INFO ] 2024-07-04 15:46:33.534 - [Task 4][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"826686427D000000022B022C0100296E5A10049FB68840A87A415DAE43B3753D2FBFC5463C5F6964003C363531306637346361323730613163663535333363656634000004","bsonType":"STRING","boolean":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-04 15:46:33.649 - [Task 4][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-04 15:46:33.649 - [Task 4][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-04 16:00:42.935 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] running status set to false 
[INFO ] 2024-07-04 16:00:42.954 - [Task 4][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 16:00:42.956 - [Task 4][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 16:00:42.956 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] schema data cleaned 
[INFO ] 2024-07-04 16:00:42.957 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] monitor closed 
[INFO ] 2024-07-04 16:00:42.964 - [Task 4][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] close complete, cost 53 ms 
[INFO ] 2024-07-04 16:00:42.974 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] running status set to false 
[INFO ] 2024-07-04 16:00:42.974 - [Task 4][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-08005c87-7653-4229-a417-7b13bfa2c13e 
[INFO ] 2024-07-04 16:00:42.974 - [Task 4][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-08005c87-7653-4229-a417-7b13bfa2c13e 
[INFO ] 2024-07-04 16:00:42.975 - [Task 4][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f38-bca58ceb-88e7-4364-bfef-bde381562d81-6674feb868ca1e3afc2a0d99] schema data cleaned 
[INFO ] 2024-07-04 16:00:42.986 - [Task 4][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-b185f64c-2c8c-4de6-803b-de7076a62236 
[INFO ] 2024-07-04 16:00:42.987 - [Task 4][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-b185f64c-2c8c-4de6-803b-de7076a62236 
[INFO ] 2024-07-04 16:00:42.987 - [Task 4][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f38-bca58ceb-88e7-4364-bfef-bde381562d81-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-07-04 16:00:43.004 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] schema data cleaned 
[INFO ] 2024-07-04 16:00:43.005 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] monitor closed 
[INFO ] 2024-07-04 16:00:43.007 - [Task 4][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] close complete, cost 42 ms 
[INFO ] 2024-07-04 16:00:43.037 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] running status set to false 
[INFO ] 2024-07-04 16:00:43.037 - [Task 4][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-77275a98-e971-4916-a599-6440c35c2203 
[INFO ] 2024-07-04 16:00:43.037 - [Task 4][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-77275a98-e971-4916-a599-6440c35c2203 
[INFO ] 2024-07-04 16:00:43.037 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] schema data cleaned 
[INFO ] 2024-07-04 16:00:43.037 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] monitor closed 
[INFO ] 2024-07-04 16:00:43.038 - [Task 4][POLICY] - Node POLICY[77275a98-e971-4916-a599-6440c35c2203] close complete, cost 30 ms 
[INFO ] 2024-07-04 16:00:43.529 - [Task 4][POLICY] - Incremental sync completed 
[INFO ] 2024-07-04 16:00:44.185 - [Task 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 16:00:44.192 - [Task 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6c8a1adb 
[INFO ] 2024-07-04 16:00:44.313 - [Task 4] - Stop task milestones: 6682d405494d8d2a5c957f38(Task 4)  
[INFO ] 2024-07-04 16:00:44.314 - [Task 4] - Stopped task aspect(s) 
[INFO ] 2024-07-04 16:00:44.348 - [Task 4] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 16:00:44.348 - [Task 4] - Remove memory task client succeed, task: Task 4[6682d405494d8d2a5c957f38] 
[INFO ] 2024-07-04 16:00:44.348 - [Task 4] - Destroy memory task client cache succeed, task: Task 4[6682d405494d8d2a5c957f38] 
