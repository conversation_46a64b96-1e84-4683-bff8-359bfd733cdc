[INFO ] 2024-08-28 10:41:45.620 - [任务 3][Mysql] - <PERSON>de <PERSON>[74f6c444-074f-4275-8487-ac365e8c864e] running status set to false 
[INFO ] 2024-08-28 10:41:45.659 - [任务 3][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-28 10:41:45.659 - [任务 3][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-28 10:41:45.659 - [任务 3][Mysql] - Incremental sync completed 
[INFO ] 2024-08-28 10:41:45.675 - [任务 3][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-74f6c444-074f-4275-8487-ac365e8c864e 
[INFO ] 2024-08-28 10:41:45.675 - [任务 3][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-74f6c444-074f-4275-8487-ac365e8c864e 
[INFO ] 2024-08-28 10:41:45.675 - [任务 3][Mysql] - Node Mysql[74f6c444-074f-4275-8487-ac365e8c864e] schema data cleaned 
[INFO ] 2024-08-28 10:41:45.680 - [任务 3][Mysql] - Node Mysql[74f6c444-074f-4275-8487-ac365e8c864e] monitor closed 
[INFO ] 2024-08-28 10:41:45.680 - [任务 3][Mysql] - Node Mysql[74f6c444-074f-4275-8487-ac365e8c864e] close complete, cost 106 ms 
[INFO ] 2024-08-28 10:41:45.680 - [任务 3][dummy] - Node dummy[b3e64a70-f20e-425d-82eb-e13ad4146b6e] running status set to false 
[INFO ] 2024-08-28 10:41:45.710 - [任务 3][dummy] - Stop connector 
[INFO ] 2024-08-28 10:41:45.716 - [任务 3][dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-b3e64a70-f20e-425d-82eb-e13ad4146b6e 
[INFO ] 2024-08-28 10:41:45.717 - [任务 3][dummy] - PDK connector node released: HazelcastTargetPdkDataNode-b3e64a70-f20e-425d-82eb-e13ad4146b6e 
[INFO ] 2024-08-28 10:41:45.717 - [任务 3][dummy] - Node dummy[b3e64a70-f20e-425d-82eb-e13ad4146b6e] schema data cleaned 
[INFO ] 2024-08-28 10:41:45.719 - [任务 3][dummy] - Node dummy[b3e64a70-f20e-425d-82eb-e13ad4146b6e] monitor closed 
[INFO ] 2024-08-28 10:41:45.719 - [任务 3][dummy] - Node dummy[b3e64a70-f20e-425d-82eb-e13ad4146b6e] close complete, cost 38 ms 
[INFO ] 2024-08-28 10:41:50.508 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-28 10:41:50.511 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@77dbe46d 
[INFO ] 2024-08-28 10:41:50.511 - [任务 3] - Stop task milestones: 66cdb5a1fb9f3a4fda09b63c(任务 3)  
[INFO ] 2024-08-28 10:41:50.663 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-08-28 10:41:50.663 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-08-28 10:41:50.699 - [任务 3] - Remove memory task client succeed, task: 任务 3[66cdb5a1fb9f3a4fda09b63c] 
[INFO ] 2024-08-28 10:41:50.700 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66cdb5a1fb9f3a4fda09b63c] 
