[INFO ] 2024-07-26 18:08:23.038 - [Heartbeat-TestMongoHeartBeat - Copy] - Start task milestones: 66a375968191787d1e5afca0(Heartbeat-TestMongoHeartBeat - Copy) 
[INFO ] 2024-07-26 18:08:23.267 - [Heartbeat-TestMongoHeartBeat - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:08:23.274 - [Heartbeat-TestMongoHeartBeat - Copy] - The engine receives Heartbeat-TestMongoHeartBeat - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:08:23.390 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:08:23.390 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:08:23.437 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6c038153-977b-4197-a0e0-4d9458eee2c3] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:08:23.441 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6c038153-977b-4197-a0e0-4d9458eee2c3] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 18:08:24.550 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 18:08:24.550 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 18:08:24.550 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 18:08:24.550 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721988504550,"lastTimes":1721988504550,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 18:08:24.599 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 18:08:24.610 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 18:08:24.610 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 18:08:24.611 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 18:08:24.612 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 18:08:24.613 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721988504550,"lastTimes":1721988504550,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 18:08:24.616 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 18:08:24.616 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:08:25.407 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-26 18:08:25.540 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721988505, "i": 49}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721988505, "i": 49}}, "signature": {"hash": {"$binary": {"base64": "pz2ad/yFssML7PdyuZ7m5BbY74s=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-26 18:10:24.533 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6c038153-977b-4197-a0e0-4d9458eee2c3] running status set to false 
[INFO ] 2024-07-26 18:10:24.551 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:10:24.551 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-6c038153-977b-4197-a0e0-4d9458eee2c3 
[INFO ] 2024-07-26 18:10:24.552 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-6c038153-977b-4197-a0e0-4d9458eee2c3 
[INFO ] 2024-07-26 18:10:24.552 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6c038153-977b-4197-a0e0-4d9458eee2c3] schema data cleaned 
[INFO ] 2024-07-26 18:10:24.552 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6c038153-977b-4197-a0e0-4d9458eee2c3] monitor closed 
[INFO ] 2024-07-26 18:10:24.553 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6c038153-977b-4197-a0e0-4d9458eee2c3] close complete, cost 23 ms 
[INFO ] 2024-07-26 18:10:24.570 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2] running status set to false 
[INFO ] 2024-07-26 18:10:24.570 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2 
[INFO ] 2024-07-26 18:10:24.570 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2 
[INFO ] 2024-07-26 18:10:24.570 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2] schema data cleaned 
[INFO ] 2024-07-26 18:10:24.570 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2] monitor closed 
[INFO ] 2024-07-26 18:10:24.776 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ffe6d8db-f4ad-4cb0-a3ea-4dc34bb59cf2] close complete, cost 17 ms 
[INFO ] 2024-07-26 18:10:26.229 - [Heartbeat-TestMongoHeartBeat - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:10:26.229 - [Heartbeat-TestMongoHeartBeat - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5fb62f4c 
[INFO ] 2024-07-26 18:10:26.230 - [Heartbeat-TestMongoHeartBeat - Copy] - Stop task milestones: 66a375968191787d1e5afca0(Heartbeat-TestMongoHeartBeat - Copy)  
[INFO ] 2024-07-26 18:10:26.356 - [Heartbeat-TestMongoHeartBeat - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:10:26.356 - [Heartbeat-TestMongoHeartBeat - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:10:26.394 - [Heartbeat-TestMongoHeartBeat - Copy] - Remove memory task client succeed, task: Heartbeat-TestMongoHeartBeat - Copy[66a375968191787d1e5afca0] 
[INFO ] 2024-07-26 18:10:26.396 - [Heartbeat-TestMongoHeartBeat - Copy] - Destroy memory task client cache succeed, task: Heartbeat-TestMongoHeartBeat - Copy[66a375968191787d1e5afca0] 
