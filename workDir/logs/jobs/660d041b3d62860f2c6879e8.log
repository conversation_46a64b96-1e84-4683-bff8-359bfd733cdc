[INFO ] 2024-04-06 20:50:58.204 - [任务 49] - Task initialization... 
[INFO ] 2024-04-06 20:50:58.215 - [任务 49] - Start task milestones: 660d041b3d62860f2c6879e8(任务 49) 
[INFO ] 2024-04-06 20:50:58.215 - [任务 49] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-06 20:50:58.215 - [任务 49] - The engine receives 任务 49 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 20:50:58.543 - [任务 49][test1] - Node test1[40dcd2d2-45f9-420d-a615-bf4c6a964f72] start preload schema,table counts: 1 
[INFO ] 2024-04-06 20:50:58.544 - [任务 49][CLAIM] - Node CLAIM[53bec195-0c00-4ed6-8404-8fd657c191a5] start preload schema,table counts: 1 
[INFO ] 2024-04-06 20:50:58.656 - [任务 49][CLAIM] - Node CLAIM[53bec195-0c00-4ed6-8404-8fd657c191a5] preload schema finished, cost 111 ms 
[INFO ] 2024-04-06 20:50:58.728 - [任务 49][test1] - Node test1[40dcd2d2-45f9-420d-a615-bf4c6a964f72] preload schema finished, cost 112 ms 
[ERROR] 2024-04-06 20:50:58.730 - [任务 49][CLAIM] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@4b85c8e8 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@4b85c8e8 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@4b85c8e8 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@4b85c8e8 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:129)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_53bec195-0c00-4ed6-8404-8fd657c191a5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

[ERROR] 2024-04-06 20:50:58.733 - [任务 49][test1] - Map name: PdkStateMap_40dcd2d2-45f9-420d-a615-bf4c6a964f72 <-- Error Message -->
Map name: PdkStateMap_40dcd2d2-45f9-420d-a615-bf4c6a964f72

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_40dcd2d2-45f9-420d-a615-bf4c6a964f72
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407858, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "ajxMKAp/MocXUkDiQu/ROdhPg/s=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[INFO ] 2024-04-06 20:50:58.770 - [任务 49][test1] - Job suspend in error handle 
[INFO ] 2024-04-06 20:50:58.771 - [任务 49][CLAIM] - Job suspend in error handle 
[INFO ] 2024-04-06 20:50:58.816 - [任务 49][CLAIM] - Node CLAIM[53bec195-0c00-4ed6-8404-8fd657c191a5] running status set to false 
[INFO ] 2024-04-06 20:50:58.819 - [任务 49][CLAIM] - PDK connector node stopped: null 
[INFO ] 2024-04-06 20:50:58.819 - [任务 49][CLAIM] - PDK connector node released: null 
[INFO ] 2024-04-06 20:50:58.820 - [任务 49][CLAIM] - Node CLAIM[53bec195-0c00-4ed6-8404-8fd657c191a5] schema data cleaned 
[INFO ] 2024-04-06 20:50:58.835 - [任务 49][CLAIM] - Node CLAIM[53bec195-0c00-4ed6-8404-8fd657c191a5] monitor closed 
[INFO ] 2024-04-06 20:50:58.835 - [任务 49][CLAIM] - Node CLAIM[53bec195-0c00-4ed6-8404-8fd657c191a5] close complete, cost 17 ms 
[INFO ] 2024-04-06 20:50:58.835 - [任务 49][test1] - Node test1[40dcd2d2-45f9-420d-a615-bf4c6a964f72] running status set to false 
[INFO ] 2024-04-06 20:50:58.838 - [任务 49][test1] - PDK connector node stopped: null 
[INFO ] 2024-04-06 20:50:58.839 - [任务 49][test1] - PDK connector node released: null 
[INFO ] 2024-04-06 20:50:58.839 - [任务 49][test1] - Node test1[40dcd2d2-45f9-420d-a615-bf4c6a964f72] schema data cleaned 
[INFO ] 2024-04-06 20:50:58.841 - [任务 49][test1] - Node test1[40dcd2d2-45f9-420d-a615-bf4c6a964f72] monitor closed 
[INFO ] 2024-04-06 20:50:59.049 - [任务 49][test1] - Node test1[40dcd2d2-45f9-420d-a615-bf4c6a964f72] close complete, cost 6 ms 
[INFO ] 2024-04-06 20:51:03.481 - [任务 49] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-06 20:51:03.485 - [任务 49] - Stop task milestones: 660d041b3d62860f2c6879e8(任务 49)  
[INFO ] 2024-04-06 20:51:03.518 - [任务 49] - Stopped task aspect(s) 
[INFO ] 2024-04-06 20:51:03.569 - [任务 49] - Snapshot order controller have been removed 
[INFO ] 2024-04-06 20:51:03.570 - [任务 49] - Remove memory task client succeed, task: 任务 49[660d041b3d62860f2c6879e8] 
[INFO ] 2024-04-06 20:51:03.570 - [任务 49] - Destroy memory task client cache succeed, task: 任务 49[660d041b3d62860f2c6879e8] 
