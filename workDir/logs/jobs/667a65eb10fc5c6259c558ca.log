[INFO ] 2024-06-25 14:44:29.267 - [任务 27] - Start task milestones: 667a65eb10fc5c6259c558ca(任务 27) 
[INFO ] 2024-06-25 14:44:29.268 - [任务 27] - Task initialization... 
[INFO ] 2024-06-25 14:44:29.560 - [任务 27] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:44:29.761 - [任务 27] - The engine receives 任务 27 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:44:29.763 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] start preload schema,table counts: 4 
[INFO ] 2024-06-25 14:44:29.764 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] start preload schema,table counts: 4 
[INFO ] 2024-06-25 14:44:29.767 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:44:29.767 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:44:30.441 - [任务 27][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 14:44:30.443 - [任务 27][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 14:44:30.446 - [任务 27][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 14:44:30.573 - [任务 27][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719297870,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:44:30.573 - [任务 27] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 14:44:30.678 - [任务 27][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 14:44:30.680 - [任务 27][SourceMongo] - Starting batch read, table name: wimtest1, offset: null 
[INFO ] 2024-06-25 14:44:30.737 - [任务 27][SourceMongo] - Table wimtest1 is going to be initial synced 
[INFO ] 2024-06-25 14:44:30.739 - [任务 27][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:44:30.739 - [任务 27][SourceMongo] - Query table 'wimtest1' counts: 1053 
[INFO ] 2024-06-25 14:44:30.740 - [任务 27][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:44:31.437 - [任务 27] - Stop task milestones: 667a65eb10fc5c6259c558ca(任务 27)  
[INFO ] 2024-06-25 14:44:31.440 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] running status set to false 
[INFO ] 2024-06-25 14:44:31.463 - [任务 27][SourceMongo] - Table [wimtest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:44:31.464 - [任务 27][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:44:31.529 - [任务 27][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-77051667-c216-4c46-9a39-0e900e44065c 
[INFO ] 2024-06-25 14:44:31.529 - [任务 27][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-77051667-c216-4c46-9a39-0e900e44065c 
[INFO ] 2024-06-25 14:44:31.533 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] schema data cleaned 
[INFO ] 2024-06-25 14:44:31.533 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] monitor closed 
[INFO ] 2024-06-25 14:44:31.539 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] close complete, cost 107 ms 
[INFO ] 2024-06-25 14:44:31.539 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] running status set to false 
[INFO ] 2024-06-25 14:44:31.570 - [任务 27][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-d04e4665-87c5-4ddf-b01c-2e5c884f7b83 
[INFO ] 2024-06-25 14:44:31.571 - [任务 27][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-d04e4665-87c5-4ddf-b01c-2e5c884f7b83 
[INFO ] 2024-06-25 14:44:31.572 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] schema data cleaned 
[INFO ] 2024-06-25 14:44:31.574 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] monitor closed 
[INFO ] 2024-06-25 14:44:31.575 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] close complete, cost 35 ms 
[INFO ] 2024-06-25 14:44:34.614 - [任务 27] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 14:44:34.615 - [任务 27] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@96ec520 
[INFO ] 2024-06-25 14:44:34.615 - [任务 27] - Stopped task aspect(s) 
[INFO ] 2024-06-25 14:44:34.616 - [任务 27] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 14:44:34.662 - [任务 27] - Remove memory task client succeed, task: 任务 27[667a65eb10fc5c6259c558ca] 
[INFO ] 2024-06-25 14:44:34.662 - [任务 27] - Destroy memory task client cache succeed, task: 任务 27[667a65eb10fc5c6259c558ca] 
[INFO ] 2024-06-25 14:45:11.326 - [任务 27] - Start task milestones: 667a65eb10fc5c6259c558ca(任务 27) 
[INFO ] 2024-06-25 14:45:11.532 - [任务 27] - Task initialization... 
[INFO ] 2024-06-25 14:45:11.685 - [任务 27] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:45:11.686 - [任务 27] - The engine receives 任务 27 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:45:11.814 - [任务 27][表编辑] - Node 表编辑[b86cb92c-6640-49ab-94b6-81ca620fad6c] start preload schema,table counts: 4 
[INFO ] 2024-06-25 14:45:11.816 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] start preload schema,table counts: 4 
[INFO ] 2024-06-25 14:45:11.816 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] start preload schema,table counts: 4 
[INFO ] 2024-06-25 14:45:11.818 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:45:11.818 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 14:45:11.818 - [任务 27][表编辑] - Node 表编辑[b86cb92c-6640-49ab-94b6-81ca620fad6c] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:45:12.459 - [任务 27][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 14:45:12.459 - [任务 27][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 14:45:12.459 - [任务 27][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 14:45:12.640 - [任务 27][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719297912,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:45:12.641 - [任务 27] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 14:45:12.783 - [任务 27][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 14:45:12.784 - [任务 27][SourceMongo] - Starting batch read, table name: wimtest1, offset: null 
[INFO ] 2024-06-25 14:45:12.784 - [任务 27][SourceMongo] - Table wimtest1 is going to be initial synced 
[INFO ] 2024-06-25 14:45:12.842 - [任务 27][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:45:12.852 - [任务 27][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:45:12.965 - [任务 27][SourceMongo] - Query table 'wimtest1' counts: 1053 
[INFO ] 2024-06-25 14:45:13.505 - [任务 27][SourceMongo] - Table [wimtest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:45:13.509 - [任务 27][SourceMongo] - Starting batch read, table name: wimtest2, offset: null 
[INFO ] 2024-06-25 14:45:13.509 - [任务 27][SourceMongo] - Table wimtest2 is going to be initial synced 
[INFO ] 2024-06-25 14:45:13.512 - [任务 27][SourceMongo] - Query table 'wimtest2' counts: 1075 
[INFO ] 2024-06-25 14:45:13.715 - [任务 27][SourceMongo] - Table [wimtest2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:45:13.720 - [任务 27][SourceMongo] - Starting batch read, table name: wimtest3, offset: null 
[INFO ] 2024-06-25 14:45:13.721 - [任务 27][SourceMongo] - Table wimtest3 is going to be initial synced 
[INFO ] 2024-06-25 14:45:13.731 - [任务 27][SourceMongo] - Table [wimtest3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:45:13.773 - [任务 27][SourceMongo] - Query table 'wimtest3' counts: 1 
[INFO ] 2024-06-25 14:45:13.779 - [任务 27][SourceMongo] - Starting batch read, table name: wimtest4, offset: null 
[INFO ] 2024-06-25 14:45:13.781 - [任务 27][SourceMongo] - Table wimtest4 is going to be initial synced 
[INFO ] 2024-06-25 14:45:13.796 - [任务 27][SourceMongo] - Query table 'wimtest4' counts: 1053 
[INFO ] 2024-06-25 14:45:14.039 - [任务 27][SourceMongo] - Table [wimtest4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:45:14.039 - [任务 27][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:45:14.039 - [任务 27][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 14:45:14.040 - [任务 27][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:45:14.106 - [任务 27][SourceMongo] - Starting stream read, table list: [wimtest1, wimtest2, wimtest3, wimtest4], offset: {"cdcOffset":1719297912,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:45:14.106 - [任务 27][SourceMongo] - Connector MongoDB incremental start succeed, tables: [wimtest1, wimtest2, wimtest3, wimtest4], data change syncing 
[INFO ] 2024-06-25 14:46:44.262 - [任务 27] - Stop task milestones: 667a65eb10fc5c6259c558ca(任务 27)  
[INFO ] 2024-06-25 14:46:44.262 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] running status set to false 
[INFO ] 2024-06-25 14:46:44.279 - [任务 27][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-77051667-c216-4c46-9a39-0e900e44065c 
[INFO ] 2024-06-25 14:46:44.279 - [任务 27][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-77051667-c216-4c46-9a39-0e900e44065c 
[INFO ] 2024-06-25 14:46:44.280 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] schema data cleaned 
[INFO ] 2024-06-25 14:46:44.281 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] monitor closed 
[INFO ] 2024-06-25 14:46:44.286 - [任务 27][SourceMongo] - Node SourceMongo[77051667-c216-4c46-9a39-0e900e44065c] close complete, cost 33 ms 
[INFO ] 2024-06-25 14:46:44.286 - [任务 27][表编辑] - Node 表编辑[b86cb92c-6640-49ab-94b6-81ca620fad6c] running status set to false 
[INFO ] 2024-06-25 14:46:44.293 - [任务 27][表编辑] - Node 表编辑[b86cb92c-6640-49ab-94b6-81ca620fad6c] schema data cleaned 
[INFO ] 2024-06-25 14:46:44.294 - [任务 27][表编辑] - Node 表编辑[b86cb92c-6640-49ab-94b6-81ca620fad6c] monitor closed 
[INFO ] 2024-06-25 14:46:44.295 - [任务 27][表编辑] - Node 表编辑[b86cb92c-6640-49ab-94b6-81ca620fad6c] close complete, cost 11 ms 
[INFO ] 2024-06-25 14:46:44.320 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] running status set to false 
[INFO ] 2024-06-25 14:46:44.321 - [任务 27][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-d04e4665-87c5-4ddf-b01c-2e5c884f7b83 
[INFO ] 2024-06-25 14:46:44.321 - [任务 27][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-d04e4665-87c5-4ddf-b01c-2e5c884f7b83 
[INFO ] 2024-06-25 14:46:44.321 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] schema data cleaned 
[INFO ] 2024-06-25 14:46:44.322 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] monitor closed 
[INFO ] 2024-06-25 14:46:44.322 - [任务 27][SouceMysql] - Node SouceMysql[d04e4665-87c5-4ddf-b01c-2e5c884f7b83] close complete, cost 26 ms 
[INFO ] 2024-06-25 14:46:44.795 - [任务 27] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 14:46:44.796 - [任务 27] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5a5e84d6 
[INFO ] 2024-06-25 14:46:44.796 - [任务 27] - Stopped task aspect(s) 
[INFO ] 2024-06-25 14:46:44.826 - [任务 27] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 14:46:44.826 - [任务 27] - Remove memory task client succeed, task: 任务 27[667a65eb10fc5c6259c558ca] 
[INFO ] 2024-06-25 14:46:44.826 - [任务 27] - Destroy memory task client cache succeed, task: 任务 27[667a65eb10fc5c6259c558ca] 
