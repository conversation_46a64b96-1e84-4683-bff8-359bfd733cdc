[INFO ] 2024-09-04 16:25:55.581 - [任务 1(101)] - 66d4d8b387d1c857bc300eb1 task start 
[INFO ] 2024-09-04 16:25:55.582 - [任务 1(101)][增强JS] - Node 增强JS[0465ef3c-57f4-452f-ad43-f2b6a9b5ce82] start preload schema,table counts: 1 
[INFO ] 2024-09-04 16:25:55.583 - [任务 1(101)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] start preload schema,table counts: 1 
[INFO ] 2024-09-04 16:25:55.583 - [任务 1(101)][fd4e8406-475e-47ba-a026-a18522540450] - Node fd4e8406-475e-47ba-a026-a18522540450[fd4e8406-475e-47ba-a026-a18522540450] start preload schema,table counts: 0 
[INFO ] 2024-09-04 16:25:55.584 - [任务 1(101)][fd4e8406-475e-47ba-a026-a18522540450] - Node fd4e8406-475e-47ba-a026-a18522540450[fd4e8406-475e-47ba-a026-a18522540450] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 16:25:55.599 - [任务 1(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Find schema failed, message: Table name "0465ef3c-57f4-452f-ad43-f2b6a9b5ce82" not exists, qualified name: PN_0465ef3c-57f4-452f-ad43-f2b6a9b5ce82 
[ERROR] 2024-09-04 16:25:55.626 - [任务 1(101)][增强JS] - java.lang.RuntimeException: Find schema failed, message: Table name "0465ef3c-57f4-452f-ad43-f2b6a9b5ce82" not exists, qualified name: PN_0465ef3c-57f4-452f-ad43-f2b6a9b5ce82 <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "0465ef3c-57f4-452f-ad43-f2b6a9b5ce82" not exists, qualified name: PN_0465ef3c-57f4-452f-ad43-f2b6a9b5ce82

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "0465ef3c-57f4-452f-ad43-f2b6a9b5ce82" not exists, qualified name: PN_0465ef3c-57f4-452f-ad43-f2b6a9b5ce82
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "0465ef3c-57f4-452f-ad43-f2b6a9b5ce82" not exists, qualified name: PN_0465ef3c-57f4-452f-ad43-f2b6a9b5ce82
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:220)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "0465ef3c-57f4-452f-ad43-f2b6a9b5ce82" not exists, qualified name: PN_0465ef3c-57f4-452f-ad43-f2b6a9b5ce82
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "0465ef3c-57f4-452f-ad43-f2b6a9b5ce82" not exists, qualified name: PN_0465ef3c-57f4-452f-ad43-f2b6a9b5ce82
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-09-04 16:25:55.629 - [任务 1(101)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] preload schema finished, cost 42 ms 
[INFO ] 2024-09-04 16:25:55.991 - [任务 1(101)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] running status set to false 
[INFO ] 2024-09-04 16:25:55.999 - [任务 1(101)][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7b3cf21e-c55e-40c4-b6d4-16216fbd800d 
[INFO ] 2024-09-04 16:25:55.999 - [任务 1(101)][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7b3cf21e-c55e-40c4-b6d4-16216fbd800d 
[INFO ] 2024-09-04 16:25:55.999 - [任务 1(101)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] schema data cleaned 
[INFO ] 2024-09-04 16:25:55.999 - [任务 1(101)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] monitor closed 
[INFO ] 2024-09-04 16:25:56.000 - [任务 1(101)][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] close complete, cost 9 ms 
[INFO ] 2024-09-04 16:25:58.140 - [任务 1(101)][增强JS] - Node 增强JS[0465ef3c-57f4-452f-ad43-f2b6a9b5ce82] running status set to false 
[INFO ] 2024-09-04 16:25:58.140 - [任务 1(101)][增强JS] - Node 增强JS[0465ef3c-57f4-452f-ad43-f2b6a9b5ce82] schema data cleaned 
[INFO ] 2024-09-04 16:25:58.140 - [任务 1(101)][增强JS] - Node 增强JS[0465ef3c-57f4-452f-ad43-f2b6a9b5ce82] monitor closed 
[INFO ] 2024-09-04 16:25:58.141 - [任务 1(101)][增强JS] - Node 增强JS[0465ef3c-57f4-452f-ad43-f2b6a9b5ce82] close complete, cost 1 ms 
[INFO ] 2024-09-04 16:25:58.144 - [任务 1(101)][fd4e8406-475e-47ba-a026-a18522540450] - Node fd4e8406-475e-47ba-a026-a18522540450[fd4e8406-475e-47ba-a026-a18522540450] running status set to false 
[INFO ] 2024-09-04 16:25:58.144 - [任务 1(101)][fd4e8406-475e-47ba-a026-a18522540450] - Node fd4e8406-475e-47ba-a026-a18522540450[fd4e8406-475e-47ba-a026-a18522540450] schema data cleaned 
[INFO ] 2024-09-04 16:25:58.144 - [任务 1(101)][fd4e8406-475e-47ba-a026-a18522540450] - Node fd4e8406-475e-47ba-a026-a18522540450[fd4e8406-475e-47ba-a026-a18522540450] monitor closed 
[INFO ] 2024-09-04 16:25:58.144 - [任务 1(101)][fd4e8406-475e-47ba-a026-a18522540450] - Node fd4e8406-475e-47ba-a026-a18522540450[fd4e8406-475e-47ba-a026-a18522540450] close complete, cost 1 ms 
[INFO ] 2024-09-04 16:25:58.155 - [任务 1(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-04 16:25:58.155 - [任务 1(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-04 16:25:58.156 - [任务 1(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-04 16:25:58.156 - [任务 1(101)] - test run task 66d4d8b387d1c857bc300eb1 complete, cost 2707ms 
