[INFO ] 2024-10-10 14:13:18.741 - [测试PDK 错误，不能写入Null值 - Copy] - load tapTable task 670770725fe35676bdffad9b-5ab7157a-4b94-4446-be5b-aa84cea4c9e1 complete, cost 12058ms 
[INFO ] 2024-10-10 14:14:02.184 - [测试PDK 错误，超过时间窗口] - Start task milestones: 670770725fe35676bdffad9c(测试PDK 错误，超过时间窗口) 
[INFO ] 2024-10-10 14:14:02.200 - [测试PDK 错误，超过时间窗口] - Task initialization... 
[INFO ] 2024-10-10 14:14:02.365 - [测试PDK 错误，超过时间窗口] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 14:14:02.367 - [测试PDK 错误，超过时间窗口] - The engine receives 测试PDK 错误，超过时间窗口 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 14:14:02.442 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:14:02.443 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:14:02.443 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:14:02.443 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:14:08.674 - [测试PDK 错误，超过时间窗口][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 14:14:08.677 - [测试PDK 错误，超过时间窗口][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 14:14:08.677 - [测试PDK 错误，超过时间窗口][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 14:14:08.678 - [测试PDK 错误，超过时间窗口][testNotNull] - batch offset found: {},stream offset found: 1719763200000 
[INFO ] 2024-10-10 14:14:08.678 - [测试PDK 错误，超过时间窗口][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 14:14:08.734 - [测试PDK 错误，超过时间窗口][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 14:14:08.734 - [测试PDK 错误，超过时间窗口][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 14:14:08.740 - [测试PDK 错误，超过时间窗口][testNotNull] - Starting stream read, table list: [testNotNull], offset: 1719763200000 
[INFO ] 2024-10-10 14:14:08.905 - [测试PDK 错误，超过时间窗口][testNotNull] - Starting mysql cdc, server name: e89363e4-0894-43a6-8358-0ad391176fbf 
[INFO ] 2024-10-10 14:14:09.106 - [测试PDK 错误，超过时间窗口][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"e89363e4-0894-43a6-8358-0ad391176fbf","offset":{"{\"server\":\"e89363e4-0894-43a6-8358-0ad391176fbf\"}":"{\"file\":\"binlog.000035\",\"pos\":0,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 656193714
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e89363e4-0894-43a6-8358-0ad391176fbf
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e89363e4-0894-43a6-8358-0ad391176fbf
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: e89363e4-0894-43a6-8358-0ad391176fbf
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 14:14:09.314 - [测试PDK 错误，超过时间窗口][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 14:14:13.393 - [测试PDK 错误，超过时间窗口][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 14:14:13.417 - [测试PDK 错误，超过时间窗口][testNotNull] - Table "test.testNotNull" exists, skip auto create table 
[INFO ] 2024-10-10 14:14:13.418 - [测试PDK 错误，超过时间窗口][testNotNull] - The table testNotNull has already exist. 
[INFO ] 2024-10-10 14:14:36.889 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] running status set to false 
[INFO ] 2024-10-10 14:14:36.897 - [测试PDK 错误，超过时间窗口][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 14:14:36.901 - [测试PDK 错误，超过时间窗口][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 14:14:36.901 - [测试PDK 错误，超过时间窗口][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 14:14:36.907 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-6108ab56-b8ce-4bd8-8195-18b21cede420 
[INFO ] 2024-10-10 14:14:36.914 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-6108ab56-b8ce-4bd8-8195-18b21cede420 
[INFO ] 2024-10-10 14:14:36.914 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] schema data cleaned 
[INFO ] 2024-10-10 14:14:36.914 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] monitor closed 
[INFO ] 2024-10-10 14:14:36.914 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] close complete, cost 243 ms 
[INFO ] 2024-10-10 14:14:36.914 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] running status set to false 
[INFO ] 2024-10-10 14:14:36.927 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a2395c-eee4-47cc-8a3d-124745d4d6ed 
[INFO ] 2024-10-10 14:14:36.935 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-85a2395c-eee4-47cc-8a3d-124745d4d6ed 
[INFO ] 2024-10-10 14:14:36.938 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] schema data cleaned 
[INFO ] 2024-10-10 14:14:36.943 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] monitor closed 
[INFO ] 2024-10-10 14:14:36.946 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] close complete, cost 16 ms 
[INFO ] 2024-10-10 14:14:37.222 - [测试PDK 错误，超过时间窗口] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 14:14:37.223 - [测试PDK 错误，超过时间窗口] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@69e91ac2 
[INFO ] 2024-10-10 14:14:37.334 - [测试PDK 错误，超过时间窗口] - Stop task milestones: 670770725fe35676bdffad9c(测试PDK 错误，超过时间窗口)  
[INFO ] 2024-10-10 14:14:37.353 - [测试PDK 错误，超过时间窗口] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:14:37.353 - [测试PDK 错误，超过时间窗口] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 14:14:37.428 - [测试PDK 错误，超过时间窗口] - Remove memory task client succeed, task: 测试PDK 错误，超过时间窗口[670770725fe35676bdffad9c] 
[INFO ] 2024-10-10 14:14:37.431 - [测试PDK 错误，超过时间窗口] - Destroy memory task client cache succeed, task: 测试PDK 错误，超过时间窗口[670770725fe35676bdffad9c] 
[INFO ] 2024-10-10 14:15:53.987 - [测试PDK 错误，超过时间窗口] - load tapTable task 670770725fe35676bdffad9b-9a0cd943-9945-4c36-826b-a97293cdf42b complete, cost 11481ms 
[INFO ] 2024-10-10 14:15:59.309 - [测试PDK 错误，超过时间窗口] - load tapTable task 670770725fe35676bdffad9b-8ca1c381-4fa8-4e33-995b-9ef6e1712fce complete, cost 14749ms 
[INFO ] 2024-10-10 14:16:06.993 - [测试PDK 错误，超过时间窗口] - load tapTable task 670770725fe35676bdffad9b-faa83903-42f7-4aa7-b4ee-e14800343e8b complete, cost 23701ms 
[INFO ] 2024-10-10 14:16:30.552 - [测试PDK 错误，超过时间窗口] - load tapTable task 670770725fe35676bdffad9b-32b8cfa8-157b-4d9d-a90f-0160c5ba2a91 complete, cost 11477ms 
[INFO ] 2024-10-10 14:16:36.110 - [测试PDK 错误，超过时间窗口] - load tapTable task 670770725fe35676bdffad9b-8a4affdf-5ece-4b17-acc7-e951a8993406 complete, cost 16448ms 
[INFO ] 2024-10-10 14:16:56.901 - [测试PDK 错误，超过时间窗口] - load tapTable task 670770725fe35676bdffad9b-cb42b06d-2afe-46fe-a53c-1c81ead6f4e5 complete, cost 11257ms 
[INFO ] 2024-10-10 14:16:59.999 - [测试PDK 错误，超过时间窗口] - Start task milestones: 670770725fe35676bdffad9c(测试PDK 错误，超过时间窗口) 
[INFO ] 2024-10-10 14:17:00.000 - [测试PDK 错误，超过时间窗口] - Task initialization... 
[INFO ] 2024-10-10 14:17:11.397 - [测试PDK 错误，超过时间窗口] - load tapTable task 670770725fe35676bdffad9b-21e40c21-bd10-4d97-841d-e8445e994444 complete, cost 11279ms 
[INFO ] 2024-10-10 14:17:11.452 - [测试PDK 错误，超过时间窗口] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 14:17:11.611 - [测试PDK 错误，超过时间窗口] - The engine receives 测试PDK 错误，超过时间窗口 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 14:17:11.612 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:17:11.612 - [测试PDK 错误，超过时间窗口][增强JS] - Node 增强JS[f90460d1-2b86-4a6f-8977-afb9f0ca0f8e] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:17:11.612 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:17:11.613 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:17:11.613 - [测试PDK 错误，超过时间窗口][增强JS] - Node 增强JS[f90460d1-2b86-4a6f-8977-afb9f0ca0f8e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:17:11.613 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:17:11.613 - [测试PDK 错误，超过时间窗口][增强JS] - Node js_processor(增强JS: f90460d1-2b86-4a6f-8977-afb9f0ca0f8e) enable batch process 
[INFO ] 2024-10-10 14:17:17.458 - [测试PDK 错误，超过时间窗口][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 14:17:17.462 - [测试PDK 错误，超过时间窗口][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 14:17:17.462 - [测试PDK 错误，超过时间窗口][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 14:17:17.478 - [测试PDK 错误，超过时间窗口][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":746,"gtidSet":""} 
[INFO ] 2024-10-10 14:17:17.478 - [测试PDK 错误，超过时间窗口][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 14:17:17.538 - [测试PDK 错误，超过时间窗口][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 14:17:17.547 - [测试PDK 错误，超过时间窗口][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 14:17:17.547 - [测试PDK 错误，超过时间窗口][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":746,"gtidSet":""} 
[INFO ] 2024-10-10 14:17:17.640 - [测试PDK 错误，超过时间窗口][testNotNull] - Starting mysql cdc, server name: de698480-6a5d-4127-aa34-258858a31718 
[INFO ] 2024-10-10 14:17:17.641 - [测试PDK 错误，超过时间窗口][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"de698480-6a5d-4127-aa34-258858a31718","offset":{"{\"server\":\"de698480-6a5d-4127-aa34-258858a31718\"}":"{\"file\":\"binlog.000036\",\"pos\":746,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 418484305
  time.precision.mode: adaptive_time_microseconds
  database.server.name: de698480-6a5d-4127-aa34-258858a31718
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-de698480-6a5d-4127-aa34-258858a31718
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: de698480-6a5d-4127-aa34-258858a31718
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 14:17:18.055 - [测试PDK 错误，超过时间窗口][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 14:17:22.055 - [测试PDK 错误，超过时间窗口][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 14:17:22.056 - [测试PDK 错误，超过时间窗口][testNotNull] - Table "test.testNotNull" exists, skip auto create table 
[INFO ] 2024-10-10 14:17:22.056 - [测试PDK 错误，超过时间窗口][testNotNull] - The table testNotNull has already exist. 
[INFO ] 2024-10-10 14:18:30.907 - [测试PDK 错误，超过时间窗口][testNotNull] - Table 'testNotNull' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-10 14:24:10.237 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] running status set to false 
[INFO ] 2024-10-10 14:24:10.240 - [测试PDK 错误，超过时间窗口][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 14:24:10.240 - [测试PDK 错误，超过时间窗口][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 14:24:10.252 - [测试PDK 错误，超过时间窗口][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 14:24:10.252 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-6108ab56-b8ce-4bd8-8195-18b21cede420 
[INFO ] 2024-10-10 14:24:10.252 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-6108ab56-b8ce-4bd8-8195-18b21cede420 
[INFO ] 2024-10-10 14:24:10.252 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] schema data cleaned 
[INFO ] 2024-10-10 14:24:10.253 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] monitor closed 
[INFO ] 2024-10-10 14:24:10.256 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[6108ab56-b8ce-4bd8-8195-18b21cede420] close complete, cost 67 ms 
[INFO ] 2024-10-10 14:24:10.256 - [测试PDK 错误，超过时间窗口][增强JS] - Node 增强JS[f90460d1-2b86-4a6f-8977-afb9f0ca0f8e] running status set to false 
[INFO ] 2024-10-10 14:24:10.266 - [测试PDK 错误，超过时间窗口][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-6f5ae57a-9daa-46d7-964b-6ab2c5c54eb1 
[INFO ] 2024-10-10 14:24:10.267 - [测试PDK 错误，超过时间窗口][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-6f5ae57a-9daa-46d7-964b-6ab2c5c54eb1 
[INFO ] 2024-10-10 14:24:10.267 - [测试PDK 错误，超过时间窗口][增强JS] - [ScriptExecutorsManager-670770725fe35676bdffad9c-f90460d1-2b86-4a6f-8977-afb9f0ca0f8e-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 14:24:10.274 - [测试PDK 错误，超过时间窗口][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-e6eabc5d-a021-453b-9b06-f32f972c7738 
[INFO ] 2024-10-10 14:24:10.274 - [测试PDK 错误，超过时间窗口][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-e6eabc5d-a021-453b-9b06-f32f972c7738 
[INFO ] 2024-10-10 14:24:10.274 - [测试PDK 错误，超过时间窗口][增强JS] - [ScriptExecutorsManager-670770725fe35676bdffad9c-f90460d1-2b86-4a6f-8977-afb9f0ca0f8e-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 14:24:10.284 - [测试PDK 错误，超过时间窗口][增强JS] - Node 增强JS[f90460d1-2b86-4a6f-8977-afb9f0ca0f8e] schema data cleaned 
[INFO ] 2024-10-10 14:24:10.284 - [测试PDK 错误，超过时间窗口][增强JS] - Node 增强JS[f90460d1-2b86-4a6f-8977-afb9f0ca0f8e] monitor closed 
[INFO ] 2024-10-10 14:24:10.284 - [测试PDK 错误，超过时间窗口][增强JS] - Node 增强JS[f90460d1-2b86-4a6f-8977-afb9f0ca0f8e] close complete, cost 30 ms 
[INFO ] 2024-10-10 14:24:10.284 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] running status set to false 
[INFO ] 2024-10-10 14:24:10.295 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a2395c-eee4-47cc-8a3d-124745d4d6ed 
[INFO ] 2024-10-10 14:24:10.303 - [测试PDK 错误，超过时间窗口][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-85a2395c-eee4-47cc-8a3d-124745d4d6ed 
[INFO ] 2024-10-10 14:24:10.304 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] schema data cleaned 
[INFO ] 2024-10-10 14:24:10.304 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] monitor closed 
[INFO ] 2024-10-10 14:24:10.304 - [测试PDK 错误，超过时间窗口][testNotNull] - Node testNotNull[85a2395c-eee4-47cc-8a3d-124745d4d6ed] close complete, cost 12 ms 
[INFO ] 2024-10-10 14:24:11.020 - [测试PDK 错误，超过时间窗口] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 14:24:11.020 - [测试PDK 错误，超过时间窗口] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b971da1 
[INFO ] 2024-10-10 14:24:11.131 - [测试PDK 错误，超过时间窗口] - Stop task milestones: 670770725fe35676bdffad9c(测试PDK 错误，超过时间窗口)  
[INFO ] 2024-10-10 14:24:11.159 - [测试PDK 错误，超过时间窗口] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:24:11.160 - [测试PDK 错误，超过时间窗口] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 14:24:11.188 - [测试PDK 错误，超过时间窗口] - Remove memory task client succeed, task: 测试PDK 错误，超过时间窗口[670770725fe35676bdffad9c] 
[INFO ] 2024-10-10 14:24:11.188 - [测试PDK 错误，超过时间窗口] - Destroy memory task client cache succeed, task: 测试PDK 错误，超过时间窗口[670770725fe35676bdffad9c] 
