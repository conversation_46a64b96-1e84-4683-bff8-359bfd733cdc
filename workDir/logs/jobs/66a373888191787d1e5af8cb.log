[INFO ] 2024-07-26 17:59:36.879 - [Heartbeat-TestMongoHeartBeat] - Start task milestones: 66a373888191787d1e5af8cb(Heartbeat-TestMongoHeartBeat) 
[INFO ] 2024-07-26 17:59:37.034 - [Heartbeat-TestMongoHeartBeat] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 17:59:37.079 - [Heartbeat-TestMongoHeartBeat] - The engine receives Heartbeat-TestMongoHeartBeat task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 17:59:37.172 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[10426884-e547-471d-8982-03f8dc0a1f10] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:59:37.172 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2f7e7c93-0537-4773-bb84-98219b36b45d] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:59:37.172 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[10426884-e547-471d-8982-03f8dc0a1f10] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:59:37.173 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2f7e7c93-0537-4773-bb84-98219b36b45d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:59:37.991 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-26 17:59:38.068 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721987977, "i": 67}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721987977, "i": 67}}, "signature": {"hash": {"$binary": {"base64": "rozNwi3v0+DZbGufbFzAX5VehiQ=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-26 17:59:38.232 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 17:59:38.232 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 17:59:38.233 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 17:59:38.233 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721987978232,"lastTimes":1721987978232,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 17:59:38.281 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 17:59:38.291 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 17:59:38.308 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 17:59:38.309 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 17:59:38.309 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 17:59:38.311 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721987978232,"lastTimes":1721987978232,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 17:59:38.311 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 17:59:38.338 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 17:59:49.866 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2f7e7c93-0537-4773-bb84-98219b36b45d] running status set to false 
[INFO ] 2024-07-26 17:59:49.869 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 17:59:49.871 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-2f7e7c93-0537-4773-bb84-98219b36b45d 
[INFO ] 2024-07-26 17:59:49.872 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-2f7e7c93-0537-4773-bb84-98219b36b45d 
[INFO ] 2024-07-26 17:59:49.872 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2f7e7c93-0537-4773-bb84-98219b36b45d] schema data cleaned 
[INFO ] 2024-07-26 17:59:49.872 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2f7e7c93-0537-4773-bb84-98219b36b45d] monitor closed 
[INFO ] 2024-07-26 17:59:49.873 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2f7e7c93-0537-4773-bb84-98219b36b45d] close complete, cost 13 ms 
[INFO ] 2024-07-26 17:59:49.873 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[10426884-e547-471d-8982-03f8dc0a1f10] running status set to false 
[INFO ] 2024-07-26 17:59:49.885 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-10426884-e547-471d-8982-03f8dc0a1f10 
[INFO ] 2024-07-26 17:59:49.886 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-10426884-e547-471d-8982-03f8dc0a1f10 
[INFO ] 2024-07-26 17:59:49.886 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[10426884-e547-471d-8982-03f8dc0a1f10] schema data cleaned 
[INFO ] 2024-07-26 17:59:49.886 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[10426884-e547-471d-8982-03f8dc0a1f10] monitor closed 
[INFO ] 2024-07-26 17:59:50.087 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[10426884-e547-471d-8982-03f8dc0a1f10] close complete, cost 13 ms 
[INFO ] 2024-07-26 17:59:50.958 - [Heartbeat-TestMongoHeartBeat] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 17:59:50.959 - [Heartbeat-TestMongoHeartBeat] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4fdea936 
[INFO ] 2024-07-26 17:59:50.959 - [Heartbeat-TestMongoHeartBeat] - Stop task milestones: 66a373888191787d1e5af8cb(Heartbeat-TestMongoHeartBeat)  
[INFO ] 2024-07-26 17:59:51.078 - [Heartbeat-TestMongoHeartBeat] - Stopped task aspect(s) 
[INFO ] 2024-07-26 17:59:51.078 - [Heartbeat-TestMongoHeartBeat] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 17:59:51.097 - [Heartbeat-TestMongoHeartBeat] - Remove memory task client succeed, task: Heartbeat-TestMongoHeartBeat[66a373888191787d1e5af8cb] 
[INFO ] 2024-07-26 17:59:51.099 - [Heartbeat-TestMongoHeartBeat] - Destroy memory task client cache succeed, task: Heartbeat-TestMongoHeartBeat[66a373888191787d1e5af8cb] 
