[INFO ] 2024-07-18 10:48:06.427 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Task initialization... 
[INFO ] 2024-07-18 10:48:06.637 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Start task milestones: 6698825a8315b25db9f538a8(t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873) 
[INFO ] 2024-07-18 10:48:06.865 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:48:07.268 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - The engine receives t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:48:07.283 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[7ace9b2e-877e-4979-94a8-52b212360864] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:48:07.288 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[23095607-7e42-4bcf-bd97-fbf382c4a446] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:48:07.288 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[23095607-7e42-4bcf-bd97-fbf382c4a446] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:48:07.288 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[7ace9b2e-877e-4979-94a8-52b212360864] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 10:48:07.649 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:48:07.649 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:48:07.649 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:48:07.650 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721270887649,"lastTimes":1721270887649,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:48:08.003 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:48:08.004 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Starting batch read, table name: mock_100w, offset: null 
[INFO ] 2024-07-18 10:48:08.004 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Table mock_100w is going to be initial synced 
[INFO ] 2024-07-18 10:48:08.065 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Start mock_100w batch read 
[INFO ] 2024-07-18 10:48:08.065 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Query table 'mock_100w' counts: 1000000 
[INFO ] 2024-07-18 10:48:16.385 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:49:46.285 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Compile mock_100w batch read 
[INFO ] 2024-07-18 10:49:46.286 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Table [mock_100w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:49:46.286 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:49:46.286 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 10:49:46.288 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:49:46.290 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Starting stream read, table list: [mock_100w], offset: {"syncStage":null,"beginTimes":1721270887649,"lastTimes":1721270887649,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:49:46.295 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Start [mock_100w] stream read 
[INFO ] 2024-07-18 10:49:46.295 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Connector Dummy incremental start succeed, tables: [mock_100w], data change syncing 
[INFO ] 2024-07-18 10:52:46.195 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[7ace9b2e-877e-4979-94a8-52b212360864] running status set to false 
[INFO ] 2024-07-18 10:52:46.196 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 10:52:46.214 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Compile [mock_100w] batch read 
[INFO ] 2024-07-18 10:52:46.217 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 10:52:46.223 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-7ace9b2e-877e-4979-94a8-52b212360864 
[INFO ] 2024-07-18 10:52:46.224 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-7ace9b2e-877e-4979-94a8-52b212360864 
[INFO ] 2024-07-18 10:52:46.224 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[7ace9b2e-877e-4979-94a8-52b212360864] schema data cleaned 
[INFO ] 2024-07-18 10:52:46.224 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[7ace9b2e-877e-4979-94a8-52b212360864] monitor closed 
[INFO ] 2024-07-18 10:52:46.226 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[7ace9b2e-877e-4979-94a8-52b212360864] close complete, cost 51 ms 
[INFO ] 2024-07-18 10:52:46.226 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[23095607-7e42-4bcf-bd97-fbf382c4a446] running status set to false 
[INFO ] 2024-07-18 10:52:46.302 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-23095607-7e42-4bcf-bd97-fbf382c4a446 
[INFO ] 2024-07-18 10:52:46.302 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-23095607-7e42-4bcf-bd97-fbf382c4a446 
[INFO ] 2024-07-18 10:52:46.302 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[23095607-7e42-4bcf-bd97-fbf382c4a446] schema data cleaned 
[INFO ] 2024-07-18 10:52:46.302 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[23095607-7e42-4bcf-bd97-fbf382c4a446] monitor closed 
[INFO ] 2024-07-18 10:52:46.304 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[23095607-7e42-4bcf-bd97-fbf382c4a446] close complete, cost 78 ms 
[INFO ] 2024-07-18 10:52:46.577 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6ece6b6c: {"after":{"rstring_3":"rDuCFCXp","rstring_2":"rDuCFCXp","rstring_1":"rDuCFCXp","created":"2024-07-18 02:50:07.499000","rstring_9":"rDuCFCXp","rstring_8":"rDuCFCXp","title":"rDuCFCXp","rstring_7":"rDuCFCXp","rint_1":6138.1161,"rstring_6":"rDuCFCXp","rstring_5":"rDuCFCXp","rstring_4":"rDuCFCXp","rint_3":6138.1161,"rint_2":6138.1161,"rstring_10":"rDuCFCXp","id":"3867af17-3004-4946-8119-1dc2cb0ff04a"},"containsIllegalDate":false,"referenceTime":1721271007499,"tableId":"mock_100w","time":1721271007499,"type":300}, nodeIds=[7ace9b2e-877e-4979-94a8-52b212360864], sourceTime=1721271007499, sourceSerialNo=null} 
[ERROR] 2024-07-18 10:52:46.661 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - target write record(s) failed <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:173)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:189)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	... 7 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 9 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:858)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: java.net.SocketException: Socket closed
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:128)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:455)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	... 25 more
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:156)
	at com.mysql.cj.NativeSession.queryServerVariable(NativeSession.java:589)
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1366)
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1359)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:677)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:93)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 27 more

[INFO ] 2024-07-18 10:52:47.029 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@57ca3e92: {"after":{"rstring_3":"rDuCFCXp","rstring_2":"rDuCFCXp","rstring_1":"rDuCFCXp","created":"2024-07-18 02:50:07.506000","rstring_9":"rDuCFCXp","rstring_8":"rDuCFCXp","title":"rDuCFCXp","rstring_7":"rDuCFCXp","rint_1":6138.1161,"rstring_6":"rDuCFCXp","rstring_5":"rDuCFCXp","rstring_4":"rDuCFCXp","rint_3":6138.1161,"rint_2":6138.1161,"rstring_10":"rDuCFCXp","id":"6cbb882c-61cb-4b80-bd91-5bba65454245"},"containsIllegalDate":false,"referenceTime":1721271007506,"tableId":"mock_100w","time":1721271007506,"type":300}, nodeIds=[7ace9b2e-877e-4979-94a8-52b212360864], sourceTime=1721271007506, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:52:47.061 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@1b077dbb: {"after":{"rstring_3":"rDuCFCXp","rstring_2":"rDuCFCXp","rstring_1":"rDuCFCXp","created":"2024-07-18 02:50:07.499000","rstring_9":"rDuCFCXp","rstring_8":"rDuCFCXp","title":"rDuCFCXp","rstring_7":"rDuCFCXp","rint_1":6138.1161,"rstring_6":"rDuCFCXp","rstring_5":"rDuCFCXp","rstring_4":"rDuCFCXp","rint_3":6138.1161,"rint_2":6138.1161,"rstring_10":"rDuCFCXp","id":"76b86e4e-1ecd-4159-8c8a-5223081c90fd"},"before":{"rstring_3":"rDuCFCXp","rstring_2":"rDuCFCXp","rstring_1":"rDuCFCXp","created":"2024-07-18 02:50:07.499000","rstring_9":"rDuCFCXp","rstring_8":"rDuCFCXp","title":"rDuCFCXp","rstring_7":"rDuCFCXp","rint_1":6138.1161,"rstring_6":"rDuCFCXp","rstring_5":"rDuCFCXp","rstring_4":"rDuCFCXp","rint_3":6138.1161,"rint_2":6138.1161,"rstring_10":"rDuCFCXp","id":"76b86e4e-1ecd-4159-8c8a-5223081c90fd"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271007499,"tableId":"mock_100w","time":1721271007499,"type":302}, nodeIds=[7ace9b2e-877e-4979-94a8-52b212360864], sourceTime=1721271007499, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:52:47.062 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@7552a1f4: {"after":{"rstring_3":"rDuCFCXp","rstring_2":"rDuCFCXp","rstring_1":"rDuCFCXp","created":"2024-07-18 02:50:07.499000","rstring_9":"rDuCFCXp","rstring_8":"rDuCFCXp","title":"rDuCFCXp","rstring_7":"rDuCFCXp","rint_1":6138.1161,"rstring_6":"rDuCFCXp","rstring_5":"rDuCFCXp","rstring_4":"rDuCFCXp","rint_3":6138.1161,"rint_2":6138.1161,"rstring_10":"rDuCFCXp","id":"2ea54879-f875-443a-beb7-366e0fd32754"},"before":{"rstring_3":"rDuCFCXp","rstring_2":"rDuCFCXp","rstring_1":"rDuCFCXp","created":"2024-07-18 02:50:07.499000","rstring_9":"rDuCFCXp","rstring_8":"rDuCFCXp","title":"rDuCFCXp","rstring_7":"rDuCFCXp","rint_1":6138.1161,"rstring_6":"rDuCFCXp","rstring_5":"rDuCFCXp","rstring_4":"rDuCFCXp","rint_3":6138.1161,"rint_2":6138.1161,"rstring_10":"rDuCFCXp","id":"2ea54879-f875-443a-beb7-366e0fd32754"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271007499,"tableId":"mock_100w","time":1721271007499,"type":302}, nodeIds=[7ace9b2e-877e-4979-94a8-52b212360864], sourceTime=1721271007499, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:52:49.712 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:52:49.712 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76bdbc23 
[INFO ] 2024-07-18 10:52:49.860 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Stop task milestones: 6698825a8315b25db9f538a8(t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873)  
[INFO ] 2024-07-18 10:52:49.925 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:52:49.925 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:52:50.007 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Remove memory task client succeed, task: t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873[6698825a8315b25db9f538a8] 
[INFO ] 2024-07-18 10:52:50.007 - [t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873] - Destroy memory task client cache succeed, task: t_2.2-1-mock_to_mysql_20fields_full/realtime_1717403468657_3537-1721270873[6698825a8315b25db9f538a8] 
