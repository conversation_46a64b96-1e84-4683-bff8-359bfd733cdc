[INFO ] 2024-07-26 19:44:51.417 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:44:52.198 - [Heartbeat-source] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:44:52.199 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:44:52.206 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@28e69502 
[INFO ] 2024-07-26 19:44:52.207 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:44:52.333 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:44:52.337 - [Heartbeat-source] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 19:44:52.338 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:46:58.047 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:47:36.947 - [Heartbeat-source] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:47:36.951 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:47:36.959 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@515f6a09 
[INFO ] 2024-07-26 19:47:36.959 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:47:37.114 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:47:37.115 - [Heartbeat-source] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 19:47:37.117 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:48:37.948 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:48:41.072 - [Heartbeat-source] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:48:41.072 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:48:41.074 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@61ca69ce 
[INFO ] 2024-07-26 19:48:41.074 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:48:41.191 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:48:41.193 - [Heartbeat-source] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 19:48:41.193 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:50:25.719 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:50:25.793 - [Heartbeat-source] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:50:25.796 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:50:25.797 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@74a19655 
[INFO ] 2024-07-26 19:50:25.797 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:50:25.933 - [Heartbeat-source] - Stopped task aspect(s) 
[ERROR] 2024-07-26 19:50:25.945 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:55:50.748 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:56:08.163 - [Heartbeat-source] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:56:08.164 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:56:08.166 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1f77f655 
[INFO ] 2024-07-26 19:56:08.166 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:56:08.298 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:56:08.301 - [Heartbeat-source] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 19:56:08.301 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:56:22.599 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:57:13.341 - [Heartbeat-source] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:57:13.342 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:57:13.343 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7c6fd823 
[INFO ] 2024-07-26 19:57:13.343 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:57:13.476 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:57:13.477 - [Heartbeat-source] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 19:57:13.478 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:57:53.502 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:58:40.291 - [Heartbeat-source] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:58:40.291 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:58:40.293 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3ce7aeb9 
[INFO ] 2024-07-26 19:58:40.293 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:58:40.429 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:58:40.431 - [Heartbeat-source] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 19:58:40.432 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:59:28.573 - [Heartbeat-source] - Start task milestones: 66a38c32183a6022d03baab5(Heartbeat-source) 
[INFO ] 2024-07-26 19:59:38.932 - [Heartbeat-source] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:59:38.934 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:59:38.935 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@500a6eb2 
[INFO ] 2024-07-26 19:59:38.935 - [Heartbeat-source] - Stop task milestones: 66a38c32183a6022d03baab5(Heartbeat-source)  
[INFO ] 2024-07-26 19:59:39.058 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:59:39.059 - [Heartbeat-source] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 19:59:39.261 - [Heartbeat-source] - Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id d8bec7d3-cf80-49cc-8db6-ca994edd0048, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

