[INFO ] 2024-04-07 01:01:44.654 - [Mysq<PERSON>-<PERSON><PERSON><PERSON>] - Task initialization... 
[INFO ] 2024-04-07 01:01:44.656 - [Mysql-Kafka] - Start task milestones: 66117f7043895b65e76af1b1(Mysql-Ka<PERSON><PERSON>) 
[INFO ] 2024-04-07 01:01:44.750 - [Mysq<PERSON>-<PERSON><PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:01:44.754 - [Mysql-Kafka] - The engine receives Mysql-Kafka task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:01:44.822 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:01:44.822 - [Mysql-Kafka][KafkaTest] - Node <PERSON>fkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:01:44.855 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] preload schema finished, cost 30 ms 
[INFO ] 2024-04-07 01:01:44.855 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] preload schema finished, cost 30 ms 
[INFO ] 2024-04-07 01:01:45.860 - [Mysql-Kafka][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:01:45.946 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" read batch size: 100 
[INFO ] 2024-04-07 01:01:45.948 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" event queue capacity: 200 
[INFO ] 2024-04-07 01:01:45.948 - [Mysql-Kafka][KafkaTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:01:46.027 - [Mysql-Kafka][KafkaTest] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":235832,"gtidSet":""} 
[INFO ] 2024-04-07 01:01:46.027 - [Mysql-Kafka][KafkaTest] - Initial sync started 
[INFO ] 2024-04-07 01:01:46.034 - [Mysql-Kafka][KafkaTest] - Starting batch read, table name: KafkaTest, offset: null 
[INFO ] 2024-04-07 01:01:46.034 - [Mysql-Kafka][KafkaTest] - Table KafkaTest is going to be initial synced 
[INFO ] 2024-04-07 01:01:46.237 - [Mysql-Kafka][KafkaTest] - Query table 'KafkaTest' counts: 3998 
[INFO ] 2024-04-07 01:01:46.457 - [Mysql-Kafka][KafkaTest] - Initial sync completed 
[INFO ] 2024-04-07 01:01:46.457 - [Mysql-Kafka][KafkaTest] - Incremental sync starting... 
[INFO ] 2024-04-07 01:01:46.461 - [Mysql-Kafka][KafkaTest] - Initial sync completed 
[INFO ] 2024-04-07 01:01:46.462 - [Mysql-Kafka][KafkaTest] - Starting stream read, table list: [KafkaTest], offset: {"filename":"binlog.000021","position":235832,"gtidSet":""} 
[INFO ] 2024-04-07 01:01:46.557 - [Mysql-Kafka][KafkaTest] - Starting mysql cdc, server name: 29685e41-7b2c-467f-8ca8-cfed121af7cb 
[INFO ] 2024-04-07 01:01:46.558 - [Mysql-Kafka][KafkaTest] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 573465219
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 29685e41-7b2c-467f-8ca8-cfed121af7cb
  database.port: 3306
  threadName: Debezium-Mysql-Connector-29685e41-7b2c-467f-8ca8-cfed121af7cb
  database.hostname: localhost
  database.password: ********
  name: 29685e41-7b2c-467f-8ca8-cfed121af7cb
  pdk.offset.string: {"name":"29685e41-7b2c-467f-8ca8-cfed121af7cb","offset":{"{\"server\":\"29685e41-7b2c-467f-8ca8-cfed121af7cb\"}":"{\"file\":\"binlog.000021\",\"pos\":235832,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.KafkaTest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-07 01:01:46.714 - [Mysql-Kafka][KafkaTest] - Connector Mysql incremental start succeed, tables: [KafkaTest], data change syncing 
[INFO ] 2024-04-07 01:03:12.764 - [Mysql-Kafka] - Stop task milestones: 66117f7043895b65e76af1b1(Mysql-Kafka)  
[INFO ] 2024-04-07 01:03:13.112 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] running status set to false 
[INFO ] 2024-04-07 01:03:13.113 - [Mysql-Kafka][KafkaTest] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-07 01:03:13.114 - [Mysql-Kafka][KafkaTest] - Mysql binlog reader stopped 
[INFO ] 2024-04-07 01:03:13.117 - [Mysql-Kafka][KafkaTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:03:13.118 - [Mysql-Kafka][KafkaTest] - PDK connector node released: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:03:13.118 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] schema data cleaned 
[INFO ] 2024-04-07 01:03:13.119 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] monitor closed 
[INFO ] 2024-04-07 01:03:13.119 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] close complete, cost 108 ms 
[INFO ] 2024-04-07 01:03:13.152 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] running status set to false 
[INFO ] 2024-04-07 01:03:13.152 - [Mysql-Kafka][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:03:13.153 - [Mysql-Kafka][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:03:13.153 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] schema data cleaned 
[INFO ] 2024-04-07 01:03:13.153 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] monitor closed 
[INFO ] 2024-04-07 01:03:13.360 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] close complete, cost 33 ms 
[INFO ] 2024-04-07 01:03:17.871 - [Mysql-Kafka] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:03:17.872 - [Mysql-Kafka] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:03:17.920 - [Mysql-Kafka] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:03:17.920 - [Mysql-Kafka] - Remove memory task client succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
[INFO ] 2024-04-07 01:03:17.920 - [Mysql-Kafka] - Destroy memory task client cache succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
[INFO ] 2024-04-07 01:07:06.649 - [Mysql-Kafka] - Task initialization... 
[INFO ] 2024-04-07 01:07:06.678 - [Mysql-Kafka] - Start task milestones: 66117f7043895b65e76af1b1(Mysql-Kafka) 
[INFO ] 2024-04-07 01:07:06.785 - [Mysql-Kafka] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:07:06.785 - [Mysql-Kafka] - The engine receives Mysql-Kafka task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:07:06.904 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:07:06.904 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:07:06.929 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] preload schema finished, cost 38 ms 
[INFO ] 2024-04-07 01:07:06.929 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] preload schema finished, cost 22 ms 
[INFO ] 2024-04-07 01:07:07.782 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" read batch size: 100 
[INFO ] 2024-04-07 01:07:07.782 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" event queue capacity: 200 
[INFO ] 2024-04-07 01:07:07.782 - [Mysql-Kafka][KafkaTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:07:07.785 - [Mysql-Kafka][KafkaTest] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":641482,"gtidSet":""} 
[INFO ] 2024-04-07 01:07:07.842 - [Mysql-Kafka][KafkaTest] - Initial sync started 
[INFO ] 2024-04-07 01:07:07.850 - [Mysql-Kafka][KafkaTest] - Starting batch read, table name: KafkaTest, offset: null 
[INFO ] 2024-04-07 01:07:07.886 - [Mysql-Kafka][KafkaTest] - Table KafkaTest is going to be initial synced 
[INFO ] 2024-04-07 01:07:07.887 - [Mysql-Kafka][KafkaTest] - Query table 'KafkaTest' counts: 10998 
[INFO ] 2024-04-07 01:07:08.088 - [Mysql-Kafka][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:07:09.791 - [Mysql-Kafka][KafkaTest] - Initial sync completed 
[INFO ] 2024-04-07 01:07:09.798 - [Mysql-Kafka][KafkaTest] - Incremental sync starting... 
[INFO ] 2024-04-07 01:07:09.803 - [Mysql-Kafka][KafkaTest] - Initial sync completed 
[INFO ] 2024-04-07 01:07:09.854 - [Mysql-Kafka][KafkaTest] - Starting stream read, table list: [KafkaTest], offset: {"filename":"binlog.000021","position":641482,"gtidSet":""} 
[INFO ] 2024-04-07 01:07:09.854 - [Mysql-Kafka][KafkaTest] - Starting mysql cdc, server name: 33859f36-5403-4205-8484-478a0894a904 
[INFO ] 2024-04-07 01:07:09.932 - [Mysql-Kafka][KafkaTest] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 36099044
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 33859f36-5403-4205-8484-478a0894a904
  database.port: 3306
  threadName: Debezium-Mysql-Connector-33859f36-5403-4205-8484-478a0894a904
  database.hostname: localhost
  database.password: ********
  name: 33859f36-5403-4205-8484-478a0894a904
  pdk.offset.string: {"name":"33859f36-5403-4205-8484-478a0894a904","offset":{"{\"server\":\"33859f36-5403-4205-8484-478a0894a904\"}":"{\"file\":\"binlog.000021\",\"pos\":641482,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.KafkaTest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-07 01:07:09.932 - [Mysql-Kafka][KafkaTest] - Connector Mysql incremental start succeed, tables: [KafkaTest], data change syncing 
[INFO ] 2024-04-07 01:14:44.727 - [Mysql-Kafka] - Stop task milestones: 66117f7043895b65e76af1b1(Mysql-Kafka)  
[INFO ] 2024-04-07 01:14:44.858 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] running status set to false 
[INFO ] 2024-04-07 01:14:44.860 - [Mysql-Kafka][KafkaTest] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-07 01:14:44.860 - [Mysql-Kafka][KafkaTest] - Mysql binlog reader stopped 
[INFO ] 2024-04-07 01:14:44.869 - [Mysql-Kafka][KafkaTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:14:44.869 - [Mysql-Kafka][KafkaTest] - PDK connector node released: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:14:44.869 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] schema data cleaned 
[INFO ] 2024-04-07 01:14:44.870 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] monitor closed 
[INFO ] 2024-04-07 01:14:44.870 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] close complete, cost 136 ms 
[INFO ] 2024-04-07 01:14:44.870 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] running status set to false 
[INFO ] 2024-04-07 01:14:44.910 - [Mysql-Kafka][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:14:44.910 - [Mysql-Kafka][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:14:44.911 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] schema data cleaned 
[INFO ] 2024-04-07 01:14:44.912 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] monitor closed 
[INFO ] 2024-04-07 01:14:44.913 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] close complete, cost 42 ms 
[INFO ] 2024-04-07 01:14:49.001 - [Mysql-Kafka] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:14:49.002 - [Mysql-Kafka] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:14:49.028 - [Mysql-Kafka] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:14:49.029 - [Mysql-Kafka] - Remove memory task client succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
[INFO ] 2024-04-07 01:14:49.029 - [Mysql-Kafka] - Destroy memory task client cache succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
[INFO ] 2024-04-07 01:16:44.415 - [Mysql-Kafka] - Task initialization... 
[INFO ] 2024-04-07 01:16:44.416 - [Mysql-Kafka] - Start task milestones: 66117f7043895b65e76af1b1(Mysql-Kafka) 
[INFO ] 2024-04-07 01:16:44.480 - [Mysql-Kafka] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:16:44.481 - [Mysql-Kafka] - The engine receives Mysql-Kafka task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:16:44.515 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:16:44.515 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:16:44.531 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] preload schema finished, cost 14 ms 
[INFO ] 2024-04-07 01:16:44.531 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] preload schema finished, cost 14 ms 
[INFO ] 2024-04-07 01:16:45.551 - [Mysql-Kafka][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:16:45.577 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" read batch size: 100 
[INFO ] 2024-04-07 01:16:45.577 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" event queue capacity: 200 
[INFO ] 2024-04-07 01:16:45.578 - [Mysql-Kafka][KafkaTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:16:45.580 - [Mysql-Kafka][KafkaTest] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":2039270,"gtidSet":""} 
[INFO ] 2024-04-07 01:16:45.626 - [Mysql-Kafka][KafkaTest] - Initial sync started 
[INFO ] 2024-04-07 01:16:45.627 - [Mysql-Kafka][KafkaTest] - Starting batch read, table name: KafkaTest, offset: null 
[INFO ] 2024-04-07 01:16:45.632 - [Mysql-Kafka][KafkaTest] - Table KafkaTest is going to be initial synced 
[INFO ] 2024-04-07 01:16:45.839 - [Mysql-Kafka][KafkaTest] - Query table 'KafkaTest' counts: 2999 
[INFO ] 2024-04-07 01:16:46.140 - [Mysql-Kafka][KafkaTest] - Initial sync completed 
[INFO ] 2024-04-07 01:16:46.141 - [Mysql-Kafka][KafkaTest] - Incremental sync starting... 
[INFO ] 2024-04-07 01:16:46.141 - [Mysql-Kafka][KafkaTest] - Initial sync completed 
[INFO ] 2024-04-07 01:16:46.144 - [Mysql-Kafka][KafkaTest] - Starting stream read, table list: [KafkaTest], offset: {"filename":"binlog.000021","position":2039270,"gtidSet":""} 
[INFO ] 2024-04-07 01:16:46.178 - [Mysql-Kafka][KafkaTest] - Starting mysql cdc, server name: e418b190-6917-45f5-87b5-8ff0a502c955 
[INFO ] 2024-04-07 01:16:46.249 - [Mysql-Kafka][KafkaTest] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1594988954
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e418b190-6917-45f5-87b5-8ff0a502c955
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e418b190-6917-45f5-87b5-8ff0a502c955
  database.hostname: localhost
  database.password: ********
  name: e418b190-6917-45f5-87b5-8ff0a502c955
  pdk.offset.string: {"name":"e418b190-6917-45f5-87b5-8ff0a502c955","offset":{"{\"server\":\"e418b190-6917-45f5-87b5-8ff0a502c955\"}":"{\"file\":\"binlog.000021\",\"pos\":2039270,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.KafkaTest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-07 01:16:46.249 - [Mysql-Kafka][KafkaTest] - Connector Mysql incremental start succeed, tables: [KafkaTest], data change syncing 
[INFO ] 2024-04-07 01:19:04.840 - [Mysql-Kafka][KafkaTest] - Read DDL: alter table KafkaTest add column firstName varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-04-07 01:19:04.849 - [Mysql-Kafka][KafkaTest] - DDL event  - Table: KafkaTest
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='e418b190-6917-45f5-87b5-8ff0a502c955', offset={{"server":"e418b190-6917-45f5-87b5-8ff0a502c955"}={"ts_sec":1712423944,"file":"binlog.000021","pos":2458486,"server_id":1}}} 
[INFO ] 2024-04-07 01:19:04.850 - [Mysql-Kafka][KafkaTest] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@75acc2a8: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"firstName","nullable":true,"partitionKey":false,"pos":4,"primaryKey":false}],"referenceTime":1712423944494,"tableId":"KafkaTest","time":1712423944792,"type":209} 
[INFO ] 2024-04-07 01:19:04.859 - [Mysql-Kafka][KafkaTest] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest_661147b205642634b1daa0d2_66117f7043895b65e76af1b1 
[INFO ] 2024-04-07 01:19:04.942 - [Mysql-Kafka][KafkaTest] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:22:46.352 - [Mysql-Kafka][KafkaTest] - Read DDL: alter table KafkaTest rename column `firstName` to `lastName`, about to be packaged as some event(s) 
[INFO ] 2024-04-07 01:22:46.352 - [Mysql-Kafka][KafkaTest] - DDL event  - Table: KafkaTest
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='e418b190-6917-45f5-87b5-8ff0a502c955', offset={{"server":"e418b190-6917-45f5-87b5-8ff0a502c955"}={"ts_sec":1712424165,"file":"binlog.000021","pos":3117768,"server_id":1}}} 
[INFO ] 2024-04-07 01:22:46.352 - [Mysql-Kafka][KafkaTest] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='KafkaTest', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@1d8aef81} 
[INFO ] 2024-04-07 01:22:46.352 - [Mysql-Kafka][KafkaTest] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest_661147b205642634b1daa0d2_66117f7043895b65e76af1b1 
[INFO ] 2024-04-07 01:22:46.352 - [Mysql-Kafka][KafkaTest] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:23:08.761 - [Mysql-Kafka][KafkaTest] - Read DDL: alter table KafkaTest drop column lastName, about to be packaged as some event(s) 
[INFO ] 2024-04-07 01:23:08.761 - [Mysql-Kafka][KafkaTest] - DDL event  - Table: KafkaTest
  - Event type: TapDropFieldEvent
  - Offset: MysqlStreamOffset{name='e418b190-6917-45f5-87b5-8ff0a502c955', offset={{"server":"e418b190-6917-45f5-87b5-8ff0a502c955"}={"ts_sec":1712424188,"file":"binlog.000021","pos":3177902,"server_id":1}}} 
[INFO ] 2024-04-07 01:23:08.762 - [Mysql-Kafka][KafkaTest] - Source node received an ddl event: TapDropFieldEvent{tableId='KafkaTest', fieldName='lastName'} 
[INFO ] 2024-04-07 01:23:08.788 - [Mysql-Kafka][KafkaTest] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest_661147b205642634b1daa0d2_66117f7043895b65e76af1b1 
[INFO ] 2024-04-07 01:23:08.788 - [Mysql-Kafka][KafkaTest] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:23:50.371 - [Mysql-Kafka] - Stop task milestones: 66117f7043895b65e76af1b1(Mysql-Kafka)  
[INFO ] 2024-04-07 01:23:50.775 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] running status set to false 
[INFO ] 2024-04-07 01:26:50.720 - [Mysql-Kafka][KafkaTest] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-07 01:26:50.720 - [Mysql-Kafka][KafkaTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:26:50.720 - [Mysql-Kafka][KafkaTest] - PDK connector node released: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:26:50.721 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] schema data cleaned 
[INFO ] 2024-04-07 01:26:50.721 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] monitor closed 
[INFO ] 2024-04-07 01:26:50.721 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] close complete, cost 180096 ms 
[INFO ] 2024-04-07 01:26:50.721 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] running status set to false 
[INFO ] 2024-04-07 01:26:50.745 - [Mysql-Kafka][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:26:50.745 - [Mysql-Kafka][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:26:50.746 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] schema data cleaned 
[INFO ] 2024-04-07 01:26:50.747 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] monitor closed 
[INFO ] 2024-04-07 01:26:50.747 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] close complete, cost 28 ms 
[INFO ] 2024-04-07 01:26:54.804 - [Mysql-Kafka] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:26:54.805 - [Mysql-Kafka] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:26:54.805 - [Mysql-Kafka] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:26:54.875 - [Mysql-Kafka] - Remove memory task client succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
[INFO ] 2024-04-07 01:26:54.875 - [Mysql-Kafka] - Destroy memory task client cache succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
[INFO ] 2024-04-07 01:34:39.565 - [Mysql-Kafka] - Task initialization... 
[INFO ] 2024-04-07 01:34:39.565 - [Mysql-Kafka] - Start task milestones: 66117f7043895b65e76af1b1(Mysql-Kafka) 
[INFO ] 2024-04-07 01:34:39.566 - [Mysql-Kafka] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:34:39.566 - [Mysql-Kafka] - The engine receives Mysql-Kafka task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:34:39.566 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:34:39.566 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:34:39.566 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] preload schema finished, cost 31 ms 
[INFO ] 2024-04-07 01:34:39.566 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] preload schema finished, cost 32 ms 
[INFO ] 2024-04-07 01:34:39.628 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" read batch size: 100 
[INFO ] 2024-04-07 01:34:39.630 - [Mysql-Kafka][KafkaTest] - Source node "KafkaTest" event queue capacity: 200 
[INFO ] 2024-04-07 01:34:39.631 - [Mysql-Kafka][KafkaTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-07 01:34:39.701 - [Mysql-Kafka][KafkaTest] - batch offset found: {"KafkaTest":{}},stream offset found: {"name":"e418b190-6917-45f5-87b5-8ff0a502c955","offset":{"{\"server\":\"e418b190-6917-45f5-87b5-8ff0a502c955\"}":"{\"ts_sec\":1712424210,\"file\":\"binlog.000021\",\"pos\":3291696,\"row\":100,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-04-07 01:34:39.703 - [Mysql-Kafka][KafkaTest] - Incremental sync starting... 
[INFO ] 2024-04-07 01:34:39.706 - [Mysql-Kafka][KafkaTest] - Initial sync completed 
[INFO ] 2024-04-07 01:34:39.731 - [Mysql-Kafka][KafkaTest] - Starting stream read, table list: [KafkaTest], offset: {"name":"e418b190-6917-45f5-87b5-8ff0a502c955","offset":{"{\"server\":\"e418b190-6917-45f5-87b5-8ff0a502c955\"}":"{\"ts_sec\":1712424210,\"file\":\"binlog.000021\",\"pos\":3291696,\"row\":100,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-04-07 01:34:39.732 - [Mysql-Kafka][KafkaTest] - Starting mysql cdc, server name: e418b190-6917-45f5-87b5-8ff0a502c955 
[INFO ] 2024-04-07 01:34:39.761 - [Mysql-Kafka][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:34:39.761 - [Mysql-Kafka][KafkaTest] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1234470781
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e418b190-6917-45f5-87b5-8ff0a502c955
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e418b190-6917-45f5-87b5-8ff0a502c955
  database.hostname: localhost
  database.password: ********
  name: e418b190-6917-45f5-87b5-8ff0a502c955
  pdk.offset.string: {"name":"e418b190-6917-45f5-87b5-8ff0a502c955","offset":{"{\"server\":\"e418b190-6917-45f5-87b5-8ff0a502c955\"}":"{\"ts_sec\":1712424210,\"file\":\"binlog.000021\",\"pos\":3291696,\"row\":100,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.KafkaTest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-07 01:34:39.963 - [Mysql-Kafka][KafkaTest] - Connector Mysql incremental start succeed, tables: [KafkaTest], data change syncing 
[INFO ] 2024-04-07 01:36:39.899 - [Mysql-Kafka][KafkaTest] - Read DDL: alter table KafkaTest add column firstName varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-04-07 01:36:39.901 - [Mysql-Kafka][KafkaTest] - DDL event  - Table: KafkaTest
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='e418b190-6917-45f5-87b5-8ff0a502c955', offset={{"server":"e418b190-6917-45f5-87b5-8ff0a502c955"}={"ts_sec":1712424999,"file":"binlog.000021","pos":3357718,"server_id":1}}} 
[INFO ] 2024-04-07 01:36:39.903 - [Mysql-Kafka][KafkaTest] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@31f71e0e: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"firstName","nullable":true,"partitionKey":false,"pos":4,"primaryKey":false}],"referenceTime":1712424999340,"tableId":"KafkaTest","time":1712424999827,"type":209} 
[INFO ] 2024-04-07 01:36:39.908 - [Mysql-Kafka][KafkaTest] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest_661147b205642634b1daa0d2_66117f7043895b65e76af1b1 
[INFO ] 2024-04-07 01:36:40.109 - [Mysql-Kafka][KafkaTest] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:37:02.834 - [Mysql-Kafka][KafkaTest] - Read DDL: alter table KafkaTest modify column firstName varchar(60), about to be packaged as some event(s) 
[INFO ] 2024-04-07 01:37:02.835 - [Mysql-Kafka][KafkaTest] - DDL event  - Table: KafkaTest
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='e418b190-6917-45f5-87b5-8ff0a502c955', offset={{"server":"e418b190-6917-45f5-87b5-8ff0a502c955"}={"ts_sec":1712425022,"file":"binlog.000021","pos":3357987,"server_id":1}}} 
[INFO ] 2024-04-07 01:37:02.838 - [Mysql-Kafka][KafkaTest] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='KafkaTest', fieldName='firstName', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@25390bc2, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=null, primaryChange=null} 
[INFO ] 2024-04-07 01:37:02.839 - [Mysql-Kafka][KafkaTest] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest_661147b205642634b1daa0d2_66117f7043895b65e76af1b1 
[INFO ] 2024-04-07 01:37:03.051 - [Mysql-Kafka][KafkaTest] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:37:16.995 - [Mysql-Kafka] - Stop task milestones: 66117f7043895b65e76af1b1(Mysql-Kafka)  
[INFO ] 2024-04-07 01:37:17.113 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] running status set to false 
[INFO ] 2024-04-07 01:37:17.159 - [Mysql-Kafka][KafkaTest] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-07 01:37:17.159 - [Mysql-Kafka][KafkaTest] - Mysql binlog reader stopped 
[INFO ] 2024-04-07 01:37:17.171 - [Mysql-Kafka][KafkaTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:37:17.171 - [Mysql-Kafka][KafkaTest] - PDK connector node released: HazelcastSourcePdkDataNode-c0acce9e-b3f3-4889-8457-3efbbb4020a6 
[INFO ] 2024-04-07 01:37:17.172 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] schema data cleaned 
[INFO ] 2024-04-07 01:37:17.172 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] monitor closed 
[INFO ] 2024-04-07 01:37:17.173 - [Mysql-Kafka][KafkaTest] - Node KafkaTest[c0acce9e-b3f3-4889-8457-3efbbb4020a6] close complete, cost 62 ms 
[INFO ] 2024-04-07 01:37:17.173 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] running status set to false 
[INFO ] 2024-04-07 01:37:17.192 - [Mysql-Kafka][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:37:17.193 - [Mysql-Kafka][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-9a153c89-0f5b-4660-a117-99a0539085eb 
[INFO ] 2024-04-07 01:37:17.193 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] schema data cleaned 
[INFO ] 2024-04-07 01:37:17.193 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] monitor closed 
[INFO ] 2024-04-07 01:37:17.193 - [Mysql-Kafka][KafkaTest2] - Node KafkaTest2[9a153c89-0f5b-4660-a117-99a0539085eb] close complete, cost 20 ms 
[INFO ] 2024-04-07 01:37:20.331 - [Mysql-Kafka] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:37:20.334 - [Mysql-Kafka] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:37:20.334 - [Mysql-Kafka] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:37:20.361 - [Mysql-Kafka] - Remove memory task client succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
[INFO ] 2024-04-07 01:37:20.363 - [Mysql-Kafka] - Destroy memory task client cache succeed, task: Mysql-Kafka[66117f7043895b65e76af1b1] 
