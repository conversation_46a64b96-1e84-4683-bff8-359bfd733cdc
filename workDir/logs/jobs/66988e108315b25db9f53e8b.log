[INFO ] 2024-07-18 11:38:02.879 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Task initialization... 
[INFO ] 2024-07-18 11:38:03.048 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Start task milestones: 66988e108315b25db9f53e8b(t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868) 
[INFO ] 2024-07-18 11:38:03.048 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:38:03.148 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - The engine receives t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:38:03.196 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][TableRename] - Node TableRename[7317049e-0330-49c3-ae5f-dc85730adcf7] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:38:03.197 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[02632558-478b-4b18-81d3-52ed2ea58016] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:38:03.197 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[644eb257-759a-43e6-8f33-c8a1cae5be6e] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:38:03.197 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[02632558-478b-4b18-81d3-52ed2ea58016] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:38:03.197 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][TableRename] - Node TableRename[7317049e-0330-49c3-ae5f-dc85730adcf7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:38:03.400 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[644eb257-759a-43e6-8f33-c8a1cae5be6e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:38:03.660 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Node(qa_mysql_repl_33306_t_1717403468657_3537) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-18 11:38:03.660 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:38:03.861 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:38:03.861 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:38:03.861 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:38:03.963 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000304","position":475485496,"gtidSet":""} 
[INFO ] 2024-07-18 11:38:03.963 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-18 11:38:04.073 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-18 11:38:04.073 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:38:04.073 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Starting batch read, table name: t_3_6_4_ddl, offset: null 
[INFO ] 2024-07-18 11:38:04.074 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Table t_3_6_4_ddl is going to be initial synced 
[INFO ] 2024-07-18 11:38:04.144 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Table [t_3_6_4_ddl] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:38:04.144 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Query table 't_3_6_4_ddl' counts: 1 
[INFO ] 2024-07-18 11:38:04.144 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:38:04.144 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:38:04.145 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:38:04.145 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Starting stream read, table list: [t_3_6_4_ddl, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000304","position":475485496,"gtidSet":""} 
[INFO ] 2024-07-18 11:38:04.242 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Starting mysql cdc, server name: 5a6952cf-3ab3-45fc-a0b8-0c25be2cf959 
[INFO ] 2024-07-18 11:38:04.242 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 819622844
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 5a6952cf-3ab3-45fc-a0b8-0c25be2cf959
  database.port: 3306
  threadName: Debezium-Mysql-Connector-5a6952cf-3ab3-45fc-a0b8-0c25be2cf959
  database.hostname: *************
  database.password: ********
  name: 5a6952cf-3ab3-45fc-a0b8-0c25be2cf959
  pdk.offset.string: {"name":"5a6952cf-3ab3-45fc-a0b8-0c25be2cf959","offset":{"{\"server\":\"5a6952cf-3ab3-45fc-a0b8-0c25be2cf959\"}":"{\"file\":\"mysql-bin.000304\",\"pos\":475485496,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: t0.t_3_6_4_ddl,t0._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: t0
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-18 11:38:04.447 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Connector Mysql incremental start succeed, tables: [t_3_6_4_ddl, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:38:19.354 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Read DDL: alter table t0.t_3_6_4_ddl change name name2 varchar(30), about to be packaged as some event(s) 
[INFO ] 2024-07-18 11:38:19.354 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Read DDL: alter table t0.t_3_6_4_ddl change name name2 varchar(30), about to be packaged as some event(s) 
[INFO ] 2024-07-18 11:38:19.354 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - DDL event  - Table: t_3_6_4_ddl
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='5a6952cf-3ab3-45fc-a0b8-0c25be2cf959', offset={{"server":"5a6952cf-3ab3-45fc-a0b8-0c25be2cf959"}={"ts_sec":1721273899,"file":"mysql-bin.000304","pos":475775206,"server_id":1121}}} 
[INFO ] 2024-07-18 11:38:19.354 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - DDL event  - Table: t_3_6_4_ddl
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='5a6952cf-3ab3-45fc-a0b8-0c25be2cf959', offset={{"server":"5a6952cf-3ab3-45fc-a0b8-0c25be2cf959"}={"ts_sec":1721273899,"file":"mysql-bin.000304","pos":475775206,"server_id":1121}}} 
[INFO ] 2024-07-18 11:38:19.355 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='t_3_6_4_ddl', nameChange=ValueChange{before=name, after=name2}} 
[INFO ] 2024-07-18 11:38:19.359 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_t_3_6_4_ddl_6697a4cfb92eda1a86f5244d_66988e108315b25db9f53e8b 
[INFO ] 2024-07-18 11:38:19.410 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Alter table schema transform finished 
[INFO ] 2024-07-18 11:38:19.410 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='t_3_6_4_ddl', fieldName='name2', dataTypeChange=ValueChange{before=null, after=varchar(30)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=null, primaryChange=null} 
[INFO ] 2024-07-18 11:38:19.472 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_t_3_6_4_ddl_6697a4cfb92eda1a86f5244d_66988e108315b25db9f53e8b 
[INFO ] 2024-07-18 11:38:19.472 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Alter table schema transform finished 
[INFO ] 2024-07-18 11:40:42.382 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[644eb257-759a-43e6-8f33-c8a1cae5be6e] running status set to false 
[INFO ] 2024-07-18 11:40:42.383 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-18 11:40:42.383 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Mysql binlog reader stopped 
[INFO ] 2024-07-18 11:40:42.383 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:40:42.390 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-644eb257-759a-43e6-8f33-c8a1cae5be6e 
[INFO ] 2024-07-18 11:40:42.390 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-644eb257-759a-43e6-8f33-c8a1cae5be6e 
[INFO ] 2024-07-18 11:40:42.391 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[644eb257-759a-43e6-8f33-c8a1cae5be6e] schema data cleaned 
[INFO ] 2024-07-18 11:40:42.391 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[644eb257-759a-43e6-8f33-c8a1cae5be6e] monitor closed 
[INFO ] 2024-07-18 11:40:42.392 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[644eb257-759a-43e6-8f33-c8a1cae5be6e] close complete, cost 46 ms 
[INFO ] 2024-07-18 11:40:42.392 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][TableRename] - Node TableRename[7317049e-0330-49c3-ae5f-dc85730adcf7] running status set to false 
[INFO ] 2024-07-18 11:40:42.392 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][TableRename] - Node TableRename[7317049e-0330-49c3-ae5f-dc85730adcf7] schema data cleaned 
[INFO ] 2024-07-18 11:40:42.392 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][TableRename] - Node TableRename[7317049e-0330-49c3-ae5f-dc85730adcf7] monitor closed 
[INFO ] 2024-07-18 11:40:42.392 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][TableRename] - Node TableRename[7317049e-0330-49c3-ae5f-dc85730adcf7] close complete, cost 0 ms 
[INFO ] 2024-07-18 11:40:42.392 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[02632558-478b-4b18-81d3-52ed2ea58016] running status set to false 
[INFO ] 2024-07-18 11:40:42.414 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-02632558-478b-4b18-81d3-52ed2ea58016 
[INFO ] 2024-07-18 11:40:42.414 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-02632558-478b-4b18-81d3-52ed2ea58016 
[INFO ] 2024-07-18 11:40:42.414 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[02632558-478b-4b18-81d3-52ed2ea58016] schema data cleaned 
[INFO ] 2024-07-18 11:40:42.414 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[02632558-478b-4b18-81d3-52ed2ea58016] monitor closed 
[INFO ] 2024-07-18 11:40:42.415 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[02632558-478b-4b18-81d3-52ed2ea58016] close complete, cost 22 ms 
[INFO ] 2024-07-18 11:40:44.657 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:40:44.770 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@331b742a 
[INFO ] 2024-07-18 11:40:44.770 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Stop task milestones: 66988e108315b25db9f53e8b(t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868)  
[INFO ] 2024-07-18 11:40:44.789 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:40:44.789 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:40:44.838 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Remove memory task client succeed, task: t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868[66988e108315b25db9f53e8b] 
[INFO ] 2024-07-18 11:40:44.839 - [t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868] - Destroy memory task client cache succeed, task: t_3.6.4-mysql_to_mysql_ddl_rename_1717403468657_3537-1721273868[66988e108315b25db9f53e8b] 
