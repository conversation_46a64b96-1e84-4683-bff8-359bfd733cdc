[INFO ] 2024-07-29 03:55:33.420 - [TestCK FLOAT32~FLOAT64 - Copy] - Task initialization... 
[INFO ] 2024-07-29 03:55:33.421 - [TestCK FLOAT32~FLOAT64 - Copy] - Start task milestones: 66a6a21266c385580759bcda(TestCK FLOAT32~FLOAT64 - Copy) 
[INFO ] 2024-07-29 03:55:33.508 - [TestCK FLOAT32~FLOAT64 - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 03:55:33.508 - [TestCK FLOAT32~FLOAT64 - Copy] - The engine receives TestCK FLOAT32~FLOAT64 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 03:55:33.588 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Node targetTFloat[684fd522-2140-42ce-b872-4931a78e63db] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:55:33.589 - [TestC<PERSON> FLOAT32~FLOAT64 - Copy][testFloat] - Node testFloat[88d1f2ee-5ee8-48f2-9062-734732522629] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:55:33.589 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Node targetTFloat[684fd522-2140-42ce-b872-4931a78e63db] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:55:33.589 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Node testFloat[88d1f2ee-5ee8-48f2-9062-734732522629] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:55:34.344 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Source node "testFloat" read batch size: 100 
[INFO ] 2024-07-29 03:55:34.345 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Source node "testFloat" event queue capacity: 200 
[INFO ] 2024-07-29 03:55:34.349 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 03:55:34.349 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 03:55:34.349 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 03:55:34.421 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Initial sync started 
[INFO ] 2024-07-29 03:55:34.425 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Starting batch read, table name: testFloat, offset: null 
[INFO ] 2024-07-29 03:55:34.425 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Table testFloat is going to be initial synced 
[INFO ] 2024-07-29 03:55:34.460 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Table [testFloat] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 03:55:34.460 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Query table 'testFloat' counts: 1 
[INFO ] 2024-07-29 03:55:34.548 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Initial sync completed 
[INFO ] 2024-07-29 03:55:34.548 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 03:55:35.492 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Node testFloat[88d1f2ee-5ee8-48f2-9062-734732522629] running status set to false 
[INFO ] 2024-07-29 03:55:35.494 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Node targetTFloat[684fd522-2140-42ce-b872-4931a78e63db] running status set to false 
[INFO ] 2024-07-29 03:55:35.494 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 03:55:35.501 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Clickhouse Optimize Table start, tables: ["targetTFloat"] 
[WARN ] 2024-07-29 03:55:35.529 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-29 03:55:35.536 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 03:55:35.555 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - PDK connector node stopped: HazelcastTargetPdkDataNode-684fd522-2140-42ce-b872-4931a78e63db 
[INFO ] 2024-07-29 03:55:35.555 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - PDK connector node stopped: HazelcastSourcePdkDataNode-88d1f2ee-5ee8-48f2-9062-734732522629 
[INFO ] 2024-07-29 03:55:35.556 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - PDK connector node released: HazelcastTargetPdkDataNode-684fd522-2140-42ce-b872-4931a78e63db 
[INFO ] 2024-07-29 03:55:35.556 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - PDK connector node released: HazelcastSourcePdkDataNode-88d1f2ee-5ee8-48f2-9062-734732522629 
[INFO ] 2024-07-29 03:55:35.557 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Node targetTFloat[684fd522-2140-42ce-b872-4931a78e63db] schema data cleaned 
[INFO ] 2024-07-29 03:55:35.557 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Node testFloat[88d1f2ee-5ee8-48f2-9062-734732522629] schema data cleaned 
[INFO ] 2024-07-29 03:55:35.557 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Node targetTFloat[684fd522-2140-42ce-b872-4931a78e63db] monitor closed 
[INFO ] 2024-07-29 03:55:35.557 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Node testFloat[88d1f2ee-5ee8-48f2-9062-734732522629] monitor closed 
[INFO ] 2024-07-29 03:55:35.558 - [TestCK FLOAT32~FLOAT64 - Copy][targetTFloat] - Node targetTFloat[684fd522-2140-42ce-b872-4931a78e63db] close complete, cost 74 ms 
[INFO ] 2024-07-29 03:55:35.558 - [TestCK FLOAT32~FLOAT64 - Copy][testFloat] - Node testFloat[88d1f2ee-5ee8-48f2-9062-734732522629] close complete, cost 80 ms 
[INFO ] 2024-07-29 03:55:38.097 - [TestCK FLOAT32~FLOAT64 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 03:55:38.097 - [TestCK FLOAT32~FLOAT64 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3822dd5b 
[INFO ] 2024-07-29 03:55:38.229 - [TestCK FLOAT32~FLOAT64 - Copy] - Stop task milestones: 66a6a21266c385580759bcda(TestCK FLOAT32~FLOAT64 - Copy)  
[INFO ] 2024-07-29 03:55:38.233 - [TestCK FLOAT32~FLOAT64 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-29 03:55:38.233 - [TestCK FLOAT32~FLOAT64 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 03:55:38.250 - [TestCK FLOAT32~FLOAT64 - Copy] - Remove memory task client succeed, task: TestCK FLOAT32~FLOAT64 - Copy[66a6a21266c385580759bcda] 
[INFO ] 2024-07-29 03:55:38.253 - [TestCK FLOAT32~FLOAT64 - Copy] - Destroy memory task client cache succeed, task: TestCK FLOAT32~FLOAT64 - Copy[66a6a21266c385580759bcda] 
