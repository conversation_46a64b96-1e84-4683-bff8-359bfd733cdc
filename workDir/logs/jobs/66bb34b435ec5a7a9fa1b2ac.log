[INFO ] 2024-08-14 00:20:51.166 - [任务 1 - Co<PERSON>][Mysql] - Starting mysql cdc, server name: 00b2ac46-a08d-47de-b5fe-2c284672edec 
[INFO ] 2024-08-14 00:20:51.187 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1527756176
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 00b2ac46-a08d-47de-b5fe-2c284672edec
  database.port: 3306
  threadName: Debezium-Mysql-Connector-00b2ac46-a08d-47de-b5fe-2c284672edec
  database.hostname: localhost
  database.password: ********
  name: 00b2ac46-a08d-47de-b5fe-2c284672edec
  pdk.offset.string: {"name":"00b2ac46-a08d-47de-b5fe-2c284672edec","offset":{"{\"server\":\"00b2ac46-a08d-47de-b5fe-2c284672edec\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 00:20:51.390 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 00:56:34.378 - [任务 1 - Copy][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-08-14 03:23:39.340 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-08-14 03:23:39.340 - [任务 1 - Copy][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	java.util.concurrent.FutureTask.run(FutureTask.java)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-14 03:56:15.817 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: 00b2ac46-a08d-47de-b5fe-2c284672edec 
[INFO ] 2024-08-14 03:56:15.913 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1877666797
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 00b2ac46-a08d-47de-b5fe-2c284672edec
  database.port: 3306
  threadName: Debezium-Mysql-Connector-00b2ac46-a08d-47de-b5fe-2c284672edec
  database.hostname: localhost
  database.password: ********
  name: 00b2ac46-a08d-47de-b5fe-2c284672edec
  pdk.offset.string: {"name":"00b2ac46-a08d-47de-b5fe-2c284672edec","offset":{"{\"server\":\"00b2ac46-a08d-47de-b5fe-2c284672edec\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 03:56:15.914 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 04:57:08.545 - [任务 1 - Copy][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-08-14 06:34:27.304 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 08:29:24.448 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 08:29:24.592 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 08:29:24.592 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 08:29:24.886 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 08:29:25.289 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 3 
[INFO ] 2024-08-14 08:29:25.495 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 3 
[INFO ] 2024-08-14 08:29:25.660 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 366 ms 
[INFO ] 2024-08-14 08:29:25.865 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 341 ms 
[INFO ] 2024-08-14 08:29:26.521 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 08:29:26.522 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 08:29:26.615 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 08:29:26.618 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 08:29:26.618 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 08:29:26.636 - [任务 1 - Copy][Mysql] - batch offset found: {"BMSQL_DISTRICT":{"offset":null,"status":"OVER"},"BMSQL_CONFIG":{"offset":{},"status":"RUNNING"},"BMSQL_CUSTOMER":{"offset":null,"status":"OVER"}},stream offset found: {"name":"00b2ac46-a08d-47de-b5fe-2c284672edec","offset":{"{\"server\":\"00b2ac46-a08d-47de-b5fe-2c284672edec\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-14 08:29:26.845 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 08:29:26.937 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 08:29:26.937 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 08:29:26.970 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"name":"00b2ac46-a08d-47de-b5fe-2c284672edec","offset":{"{\"server\":\"00b2ac46-a08d-47de-b5fe-2c284672edec\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-14 08:29:26.970 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: 00b2ac46-a08d-47de-b5fe-2c284672edec 
[INFO ] 2024-08-14 08:29:27.176 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 572890459
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 00b2ac46-a08d-47de-b5fe-2c284672edec
  database.port: 3306
  threadName: Debezium-Mysql-Connector-00b2ac46-a08d-47de-b5fe-2c284672edec
  database.hostname: localhost
  database.password: ********
  name: 00b2ac46-a08d-47de-b5fe-2c284672edec
  pdk.offset.string: {"name":"00b2ac46-a08d-47de-b5fe-2c284672edec","offset":{"{\"server\":\"00b2ac46-a08d-47de-b5fe-2c284672edec\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 08:29:27.682 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 08:30:23.502 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 08:30:23.858 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 08:30:24.005 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 08:30:24.005 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 08:30:24.037 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 08:30:24.037 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 08:30:24.039 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 08:30:24.047 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 08:30:24.047 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 187 ms 
[INFO ] 2024-08-14 08:30:24.107 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 08:30:24.108 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 08:30:24.108 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 08:30:24.110 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 08:30:24.111 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 08:30:24.112 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 67 ms 
[INFO ] 2024-08-14 08:30:28.595 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 08:30:28.596 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 08:30:28.683 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 08:30:28.683 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 08:30:28.683 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 11:51:47.154 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 11:51:47.213 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 11:51:47.213 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 11:51:47.426 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 11:51:47.736 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 3 
[INFO ] 2024-08-14 11:51:47.737 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 3 
[INFO ] 2024-08-14 11:51:48.021 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 269 ms 
[INFO ] 2024-08-14 11:51:48.023 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 269 ms 
[INFO ] 2024-08-14 11:51:49.000 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 11:51:49.003 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 11:51:49.089 - [任务 1 - Copy][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-14 11:51:49.091 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 11:51:49.095 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 11:51:49.111 - [任务 1 - Copy][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-14 11:51:49.112 - [任务 1 - Copy][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-14 11:51:49.113 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 11:51:49.217 - [任务 1 - Copy][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-14 11:51:49.367 - [任务 1 - Copy][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-14 11:51:49.367 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 11:51:49.387 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-14 11:51:49.388 - [任务 1 - Copy][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-14 11:51:49.448 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-14 11:51:49.449 - [任务 1 - Copy][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 11:51:52.795 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_DISTRICT 
[INFO ] 2024-08-14 11:51:52.826 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-14 11:51:52.839 - [任务 1 - Copy][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-14 11:51:52.893 - [任务 1 - Copy][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 11:51:52.893 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-14 11:51:52.893 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CUSTOMER 
[INFO ] 2024-08-14 11:51:52.894 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-14 11:51:52.896 - [任务 1 - Copy][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-14 11:51:52.901 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-14 11:51:52.907 - [任务 1 - Copy][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 11:51:52.910 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CONFIG 
[INFO ] 2024-08-14 11:51:52.910 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 11:51:52.911 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 11:51:52.919 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 11:51:52.920 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 11:51:52.983 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-14 11:51:52.984 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: bdb3e8b7-d851-437d-9e03-acb94ae3745d 
[INFO ] 2024-08-14 11:51:53.191 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1469126099
  time.precision.mode: adaptive_time_microseconds
  database.server.name: bdb3e8b7-d851-437d-9e03-acb94ae3745d
  database.port: 3306
  threadName: Debezium-Mysql-Connector-bdb3e8b7-d851-437d-9e03-acb94ae3745d
  database.hostname: localhost
  database.password: ********
  name: bdb3e8b7-d851-437d-9e03-acb94ae3745d
  pdk.offset.string: {"name":"bdb3e8b7-d851-437d-9e03-acb94ae3745d","offset":{"{\"server\":\"bdb3e8b7-d851-437d-9e03-acb94ae3745d\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 11:51:53.235 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 11:51:57.953 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_DISTRICT 
[INFO ] 2024-08-14 11:51:57.953 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CUSTOMER 
[INFO ] 2024-08-14 11:51:58.158 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CONFIG 
[INFO ] 2024-08-14 11:52:44.369 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 11:52:44.920 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 11:52:44.921 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 11:52:44.921 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 11:52:44.930 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 11:52:44.930 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 11:52:44.930 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 11:52:44.936 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 11:52:44.936 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 139 ms 
[INFO ] 2024-08-14 11:52:44.936 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 11:52:44.961 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 11:52:44.963 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 11:52:44.963 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 11:52:44.964 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 11:52:44.964 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 25 ms 
[INFO ] 2024-08-14 11:52:47.906 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 11:52:47.913 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 11:52:47.913 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 11:52:47.951 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 11:52:47.952 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 11:53:03.256 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 11:53:03.258 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 11:53:03.431 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 11:53:03.431 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 11:53:03.521 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 6 
[INFO ] 2024-08-14 11:53:03.521 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 6 
[INFO ] 2024-08-14 11:53:03.738 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 215 ms 
[INFO ] 2024-08-14 11:53:03.941 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 223 ms 
[INFO ] 2024-08-14 11:53:04.036 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 11:53:04.037 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 11:53:04.157 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 11:53:04.158 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 11:53:04.160 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 11:53:04.172 - [任务 1 - Copy][Mysql] - batch offset found: {"BMSQL_DISTRICT":{"offset":null,"status":"OVER"},"BMSQL_CONFIG":{"offset":{},"status":"RUNNING"},"BMSQL_CUSTOMER":{"offset":null,"status":"OVER"}},stream offset found: {"name":"bdb3e8b7-d851-437d-9e03-acb94ae3745d","offset":{"{\"server\":\"bdb3e8b7-d851-437d-9e03-acb94ae3745d\"}":"{\"ts_sec\":1723607513,\"file\":\"binlog.000034\",\"pos\":308235516}"}} 
[INFO ] 2024-08-14 11:53:04.173 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 11:53:04.318 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 11:53:04.326 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_STOCK, offset: null 
[INFO ] 2024-08-14 11:53:04.367 - [任务 1 - Copy][Mysql] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2024-08-14 11:53:04.367 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_STOCK' counts: 7899 
[INFO ] 2024-08-14 11:53:04.754 - [任务 1 - Copy][Mysql] - Table [BMSQL_STOCK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 11:53:04.759 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_STOCK 
[INFO ] 2024-08-14 11:53:04.761 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_ORDER_LINE, offset: null 
[INFO ] 2024-08-14 11:53:04.768 - [任务 1 - Copy][Mysql] - Table BMSQL_ORDER_LINE is going to be initial synced 
[INFO ] 2024-08-14 11:53:04.768 - [任务 1 - Copy][Mysql] - Table [BMSQL_ORDER_LINE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 11:53:04.771 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_ORDER_LINE' counts: 0 
[INFO ] 2024-08-14 11:53:04.771 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ORDER_LINE 
[INFO ] 2024-08-14 11:53:04.775 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER, offset: null 
[INFO ] 2024-08-14 11:53:04.775 - [任务 1 - Copy][Mysql] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-08-14 11:53:04.776 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-08-14 11:53:04.776 - [任务 1 - Copy][Mysql] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 11:53:04.780 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER 
[INFO ] 2024-08-14 11:53:04.780 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 11:53:04.783 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 11:53:04.784 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 11:53:04.784 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 11:53:04.785 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_STOCK, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"name":"bdb3e8b7-d851-437d-9e03-acb94ae3745d","offset":{"{\"server\":\"bdb3e8b7-d851-437d-9e03-acb94ae3745d\"}":"{\"ts_sec\":1723607513,\"file\":\"binlog.000034\",\"pos\":308235516}"}} 
[INFO ] 2024-08-14 11:53:04.813 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_STOCK 
[INFO ] 2024-08-14 11:53:04.813 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: bdb3e8b7-d851-437d-9e03-acb94ae3745d 
[INFO ] 2024-08-14 11:53:04.940 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1860411531
  time.precision.mode: adaptive_time_microseconds
  database.server.name: bdb3e8b7-d851-437d-9e03-acb94ae3745d
  database.port: 3306
  threadName: Debezium-Mysql-Connector-bdb3e8b7-d851-437d-9e03-acb94ae3745d
  database.hostname: localhost
  database.password: ********
  name: bdb3e8b7-d851-437d-9e03-acb94ae3745d
  pdk.offset.string: {"name":"bdb3e8b7-d851-437d-9e03-acb94ae3745d","offset":{"{\"server\":\"bdb3e8b7-d851-437d-9e03-acb94ae3745d\"}":"{\"ts_sec\":1723607513,\"file\":\"binlog.000034\",\"pos\":308235516}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_STOCK,test.BMSQL_ORDER_LINE,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 11:53:04.941 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_STOCK, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-14 11:53:09.833 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ORDER_LINE 
[INFO ] 2024-08-14 11:53:09.834 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_OORDER 
[INFO ] 2024-08-14 11:56:07.186 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 11:56:07.588 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 11:56:07.719 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 11:56:07.741 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 11:56:07.742 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 11:56:07.742 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 11:56:07.745 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 11:56:07.745 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 11:56:07.750 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 162 ms 
[INFO ] 2024-08-14 11:56:07.750 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 11:56:07.771 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 11:56:07.773 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 11:56:07.773 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 11:56:07.774 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 11:56:07.776 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 26 ms 
[INFO ] 2024-08-14 11:56:08.202 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 11:56:08.202 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 11:56:08.249 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 11:56:08.249 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 11:56:08.249 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 12:04:47.599 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 12:04:47.621 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 12:04:47.622 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 12:04:47.732 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 12:04:47.733 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 3 
[INFO ] 2024-08-14 12:04:47.733 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 3 
[INFO ] 2024-08-14 12:04:47.822 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 87 ms 
[INFO ] 2024-08-14 12:04:47.823 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 88 ms 
[INFO ] 2024-08-14 12:04:48.618 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 12:04:48.625 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 12:04:48.691 - [任务 1 - Copy][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-14 12:04:48.691 - [任务 1 - Copy][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-14 12:04:48.794 - [任务 1 - Copy][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-14 12:04:48.794 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 12:04:48.794 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 12:04:48.794 - [任务 1 - Copy][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-14 12:04:48.800 - [任务 1 - Copy][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-14 12:04:48.851 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 12:04:48.852 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 12:04:48.852 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-14 12:04:48.859 - [任务 1 - Copy][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-14 12:04:48.893 - [任务 1 - Copy][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:04:48.893 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-14 12:04:48.893 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_DISTRICT 
[INFO ] 2024-08-14 12:04:48.894 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-14 12:04:48.909 - [任务 1 - Copy][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-14 12:04:48.909 - [任务 1 - Copy][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:04:48.909 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-14 12:04:48.910 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CUSTOMER 
[INFO ] 2024-08-14 12:04:48.910 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-14 12:04:48.912 - [任务 1 - Copy][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-14 12:04:48.913 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-14 12:04:48.913 - [任务 1 - Copy][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:04:48.913 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CONFIG 
[INFO ] 2024-08-14 12:04:48.914 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 12:04:48.914 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:04:48.914 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 12:04:48.914 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:04:48.947 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-14 12:04:48.948 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: c6de9940-8f43-4ade-91bc-3ee84b998945 
[INFO ] 2024-08-14 12:04:48.993 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 124076930
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c6de9940-8f43-4ade-91bc-3ee84b998945
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c6de9940-8f43-4ade-91bc-3ee84b998945
  database.hostname: localhost
  database.password: ********
  name: c6de9940-8f43-4ade-91bc-3ee84b998945
  pdk.offset.string: {"name":"c6de9940-8f43-4ade-91bc-3ee84b998945","offset":{"{\"server\":\"c6de9940-8f43-4ade-91bc-3ee84b998945\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 12:04:48.993 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 12:04:53.942 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_DISTRICT 
[INFO ] 2024-08-14 12:04:53.942 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CUSTOMER 
[INFO ] 2024-08-14 12:04:54.144 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CONFIG 
[INFO ] 2024-08-14 12:07:41.627 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 12:07:42.040 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 12:07:42.042 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 12:07:42.067 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 12:07:42.067 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 12:07:42.068 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 12:07:42.070 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 12:07:42.070 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 12:07:42.071 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 132 ms 
[INFO ] 2024-08-14 12:07:42.092 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 12:07:42.092 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 12:07:42.092 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 12:07:42.093 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 12:07:42.093 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 12:07:42.094 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 22 ms 
[INFO ] 2024-08-14 12:07:43.772 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 12:07:43.772 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 12:07:43.772 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 12:07:43.799 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 12:07:43.799 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 12:07:44.995 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 12:07:44.995 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 12:07:45.026 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 12:07:45.125 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 12:07:45.129 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 3 
[INFO ] 2024-08-14 12:07:45.129 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 3 
[INFO ] 2024-08-14 12:07:45.227 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 100 ms 
[INFO ] 2024-08-14 12:07:45.227 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 101 ms 
[INFO ] 2024-08-14 12:07:45.488 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 12:07:45.488 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 12:07:45.528 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 12:07:45.529 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 12:07:45.529 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 12:07:45.534 - [任务 1 - Copy][Mysql] - batch offset found: {"BMSQL_DISTRICT":{"offset":null,"status":"OVER"},"BMSQL_CONFIG":{"offset":{},"status":"RUNNING"},"BMSQL_CUSTOMER":{"offset":null,"status":"OVER"}},stream offset found: {"name":"c6de9940-8f43-4ade-91bc-3ee84b998945","offset":{"{\"server\":\"c6de9940-8f43-4ade-91bc-3ee84b998945\"}":"{\"ts_sec\":1723608289,\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-14 12:07:45.534 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 12:08:00.821 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 12:08:00.826 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:08:00.881 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"name":"c6de9940-8f43-4ade-91bc-3ee84b998945","offset":{"{\"server\":\"c6de9940-8f43-4ade-91bc-3ee84b998945\"}":"{\"ts_sec\":1723608289,\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-14 12:08:00.882 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: c6de9940-8f43-4ade-91bc-3ee84b998945 
[INFO ] 2024-08-14 12:08:00.981 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 813139680
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c6de9940-8f43-4ade-91bc-3ee84b998945
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c6de9940-8f43-4ade-91bc-3ee84b998945
  database.hostname: localhost
  database.password: ********
  name: c6de9940-8f43-4ade-91bc-3ee84b998945
  pdk.offset.string: {"name":"c6de9940-8f43-4ade-91bc-3ee84b998945","offset":{"{\"server\":\"c6de9940-8f43-4ade-91bc-3ee84b998945\"}":"{\"ts_sec\":1723608289,\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 12:08:00.981 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 12:09:20.712 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 12:09:21.113 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 12:09:21.115 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 12:09:21.116 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 12:09:21.150 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 12:09:21.150 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 12:09:21.151 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 12:09:21.151 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 12:09:21.152 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 149 ms 
[INFO ] 2024-08-14 12:09:21.153 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 12:09:21.171 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 12:09:21.172 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 12:09:21.172 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 12:09:21.173 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 12:09:21.173 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 21 ms 
[INFO ] 2024-08-14 12:09:25.761 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 12:09:25.763 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 12:09:25.813 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 12:09:25.814 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 12:09:25.814 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 12:09:42.023 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 12:09:42.048 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 12:09:42.048 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 12:09:42.139 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 12:09:42.140 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 5 
[INFO ] 2024-08-14 12:09:42.140 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 5 
[INFO ] 2024-08-14 12:09:42.203 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 63 ms 
[INFO ] 2024-08-14 12:09:42.405 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 63 ms 
[INFO ] 2024-08-14 12:09:42.451 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 12:09:42.451 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 12:09:42.568 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 12:09:42.568 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 12:09:42.570 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 12:09:42.574 - [任务 1 - Copy][Mysql] - batch offset found: {},stream offset found: {"name":"c6de9940-8f43-4ade-91bc-3ee84b998945","offset":{"{\"server\":\"c6de9940-8f43-4ade-91bc-3ee84b998945\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-14 12:09:42.574 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 12:11:31.927 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 12:11:31.968 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_ITEM, offset: null 
[INFO ] 2024-08-14 12:11:31.981 - [任务 1 - Copy][Mysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-08-14 12:11:32.047 - [任务 1 - Copy][Mysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:11:32.049 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-08-14 12:11:32.049 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ITEM 
[INFO ] 2024-08-14 12:11:32.056 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_NEW_ORDER, offset: null 
[INFO ] 2024-08-14 12:11:32.067 - [任务 1 - Copy][Mysql] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-08-14 12:11:32.084 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-08-14 12:11:32.085 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ITEM 
[INFO ] 2024-08-14 12:11:32.244 - [任务 1 - Copy][Mysql] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:11:32.244 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_NEW_ORDER 
[INFO ] 2024-08-14 12:11:32.244 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 12:11:32.245 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:11:32.245 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 12:11:32.245 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:11:32.300 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"name":"c6de9940-8f43-4ade-91bc-3ee84b998945","offset":{"{\"server\":\"c6de9940-8f43-4ade-91bc-3ee84b998945\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-14 12:11:32.306 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: c6de9940-8f43-4ade-91bc-3ee84b998945 
[INFO ] 2024-08-14 12:11:32.309 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1630651193
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c6de9940-8f43-4ade-91bc-3ee84b998945
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c6de9940-8f43-4ade-91bc-3ee84b998945
  database.hostname: localhost
  database.password: ********
  name: c6de9940-8f43-4ade-91bc-3ee84b998945
  pdk.offset.string: {"name":"c6de9940-8f43-4ade-91bc-3ee84b998945","offset":{"{\"server\":\"c6de9940-8f43-4ade-91bc-3ee84b998945\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 12:11:32.515 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 12:11:37.321 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_NEW_ORDER 
[INFO ] 2024-08-14 12:19:33.047 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 12:19:33.389 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 12:19:33.493 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 12:19:33.493 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 12:19:33.521 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 12:19:33.521 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 12:19:33.523 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 12:19:33.524 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 12:19:33.524 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 139 ms 
[INFO ] 2024-08-14 12:19:33.525 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 12:19:33.546 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 12:19:33.546 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 12:19:33.547 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 12:19:33.547 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 12:19:33.750 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 22 ms 
[INFO ] 2024-08-14 12:19:37.094 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 12:19:37.095 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 12:19:37.095 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 12:19:37.124 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 12:19:37.127 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:13:04.897 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 14:13:04.898 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 14:13:04.913 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 14:13:05.013 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 14:13:05.013 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 4 
[INFO ] 2024-08-14 14:13:05.013 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 4 
[INFO ] 2024-08-14 14:13:05.070 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 59 ms 
[INFO ] 2024-08-14 14:13:05.070 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 59 ms 
[INFO ] 2024-08-14 14:13:05.876 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 14:13:05.877 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 14:13:05.928 - [任务 1 - Copy][Pg] - The table BMSQL_ITEM has already exist. 
[INFO ] 2024-08-14 14:13:06.016 - [任务 1 - Copy][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-14 14:13:06.016 - [任务 1 - Copy][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-14 14:13:06.123 - [任务 1 - Copy][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-14 14:13:06.123 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 14:13:06.123 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 14:13:06.123 - [任务 1 - Copy][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-14 14:13:06.127 - [任务 1 - Copy][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-14 14:13:06.127 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 14:13:20.458 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 14:13:20.485 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_ITEM, offset: null 
[INFO ] 2024-08-14 14:13:20.485 - [任务 1 - Copy][Mysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-08-14 14:13:20.552 - [任务 1 - Copy][Mysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:13:20.553 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-08-14 14:13:20.553 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ITEM 
[INFO ] 2024-08-14 14:13:20.553 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-14 14:13:20.554 - [任务 1 - Copy][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-14 14:13:20.577 - [任务 1 - Copy][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:13:20.578 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ITEM 
[INFO ] 2024-08-14 14:13:20.579 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-14 14:13:20.579 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_DISTRICT 
[INFO ] 2024-08-14 14:13:20.579 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-14 14:13:20.580 - [任务 1 - Copy][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-14 14:13:20.583 - [任务 1 - Copy][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:13:20.583 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-14 14:13:20.584 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CUSTOMER 
[INFO ] 2024-08-14 14:13:20.584 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-14 14:13:20.586 - [任务 1 - Copy][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-14 14:13:20.586 - [任务 1 - Copy][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:13:20.586 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-14 14:13:20.586 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CONFIG 
[INFO ] 2024-08-14 14:13:20.586 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 14:13:20.587 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:13:20.587 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 14:13:20.587 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:13:20.616 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-14 14:13:20.621 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: e084c45a-0b53-43a6-ac06-351c1304ccfd 
[INFO ] 2024-08-14 14:13:20.625 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 864693164
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e084c45a-0b53-43a6-ac06-351c1304ccfd
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e084c45a-0b53-43a6-ac06-351c1304ccfd
  database.hostname: localhost
  database.password: ********
  name: e084c45a-0b53-43a6-ac06-351c1304ccfd
  pdk.offset.string: {"name":"e084c45a-0b53-43a6-ac06-351c1304ccfd","offset":{"{\"server\":\"e084c45a-0b53-43a6-ac06-351c1304ccfd\"}":"{\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 14:13:20.835 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 14:13:25.678 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_DISTRICT 
[INFO ] 2024-08-14 14:13:25.698 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CUSTOMER 
[INFO ] 2024-08-14 14:13:25.698 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CONFIG 
[INFO ] 2024-08-14 14:13:49.436 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 14:13:49.672 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 14:13:49.758 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 14:13:49.762 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 14:13:49.773 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:13:49.773 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:13:49.774 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 14:13:49.774 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 14:13:49.775 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 104 ms 
[INFO ] 2024-08-14 14:13:49.792 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 14:13:49.792 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:13:49.792 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:13:49.792 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 14:13:49.792 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 14:13:49.792 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 17 ms 
[INFO ] 2024-08-14 14:13:50.494 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 14:13:50.494 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 14:13:50.495 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 14:13:50.526 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:13:50.741 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:14:12.398 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 14:14:12.398 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 14:14:12.490 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 14:14:12.490 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 14:14:12.533 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 5 
[INFO ] 2024-08-14 14:14:12.533 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 5 
[INFO ] 2024-08-14 14:14:12.626 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 92 ms 
[INFO ] 2024-08-14 14:14:12.626 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 92 ms 
[INFO ] 2024-08-14 14:14:12.906 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 14:14:12.907 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 14:14:12.947 - [任务 1 - Copy][Pg] - The table BMSQL_NEW_ORDER has already exist. 
[INFO ] 2024-08-14 14:14:12.964 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 14:14:12.966 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 14:14:12.966 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 14:14:12.970 - [任务 1 - Copy][Mysql] - batch offset found: {"BMSQL_ITEM":{"offset":null,"status":"OVER"},"BMSQL_DISTRICT":{"offset":null,"status":"OVER"},"BMSQL_CONFIG":{"offset":{},"status":"RUNNING"},"BMSQL_CUSTOMER":{"offset":null,"status":"OVER"}},stream offset found: {"name":"e084c45a-0b53-43a6-ac06-351c1304ccfd","offset":{"{\"server\":\"e084c45a-0b53-43a6-ac06-351c1304ccfd\"}":"{\"ts_sec\":1723616000,\"file\":\"binlog.000034\",\"pos\":308236702}"}} 
[INFO ] 2024-08-14 14:14:12.970 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 14:15:11.738 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 14:15:11.779 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_NEW_ORDER, offset: null 
[INFO ] 2024-08-14 14:15:11.780 - [任务 1 - Copy][Mysql] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-08-14 14:15:11.935 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-08-14 14:15:11.938 - [任务 1 - Copy][Mysql] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:15:11.938 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_NEW_ORDER 
[INFO ] 2024-08-14 14:15:11.938 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 14:15:11.938 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:15:11.938 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 14:15:11.938 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:15:11.948 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"name":"e084c45a-0b53-43a6-ac06-351c1304ccfd","offset":{"{\"server\":\"e084c45a-0b53-43a6-ac06-351c1304ccfd\"}":"{\"ts_sec\":1723616000,\"file\":\"binlog.000034\",\"pos\":308236702}"}} 
[INFO ] 2024-08-14 14:15:11.991 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: e084c45a-0b53-43a6-ac06-351c1304ccfd 
[INFO ] 2024-08-14 14:15:11.991 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_NEW_ORDER 
[INFO ] 2024-08-14 14:15:12.123 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 712618500
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e084c45a-0b53-43a6-ac06-351c1304ccfd
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e084c45a-0b53-43a6-ac06-351c1304ccfd
  database.hostname: localhost
  database.password: ********
  name: e084c45a-0b53-43a6-ac06-351c1304ccfd
  pdk.offset.string: {"name":"e084c45a-0b53-43a6-ac06-351c1304ccfd","offset":{"{\"server\":\"e084c45a-0b53-43a6-ac06-351c1304ccfd\"}":"{\"ts_sec\":1723616000,\"file\":\"binlog.000034\",\"pos\":308236702}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 14:15:12.124 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-14 14:19:02.159 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 14:19:02.284 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 14:19:02.284 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 14:19:02.284 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 14:19:02.302 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:19:02.303 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:19:02.303 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 14:19:02.303 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 14:19:02.305 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 125 ms 
[INFO ] 2024-08-14 14:19:02.305 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 14:19:02.347 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:19:02.351 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:19:02.351 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 14:19:02.352 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 14:19:02.352 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 46 ms 
[INFO ] 2024-08-14 14:19:06.749 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 14:19:06.749 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 14:19:06.749 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 14:19:06.777 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:19:06.983 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:19:33.084 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 14:19:33.122 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 14:19:33.122 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 14:19:33.257 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 14:19:33.258 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 6 
[INFO ] 2024-08-14 14:19:33.258 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 6 
[INFO ] 2024-08-14 14:19:33.462 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 110 ms 
[INFO ] 2024-08-14 14:19:33.462 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 110 ms 
[INFO ] 2024-08-14 14:19:33.701 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 14:19:33.701 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 14:19:33.735 - [任务 1 - Copy][Pg] - The table BMSQL_OORDER has already exist. 
[INFO ] 2024-08-14 14:19:33.746 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 14:19:33.746 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 14:19:33.751 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 14:19:33.752 - [任务 1 - Copy][Mysql] - batch offset found: {"BMSQL_NEW_ORDER":{"offset":{},"status":"RUNNING"},"BMSQL_ITEM":{"offset":null,"status":"OVER"},"BMSQL_DISTRICT":{"offset":null,"status":"OVER"},"BMSQL_CONFIG":{"offset":{},"status":"RUNNING"},"BMSQL_CUSTOMER":{"offset":null,"status":"OVER"}},stream offset found: {"name":"e084c45a-0b53-43a6-ac06-351c1304ccfd","offset":{"{\"server\":\"e084c45a-0b53-43a6-ac06-351c1304ccfd\"}":"{\"ts_sec\":1723616112,\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":1}"}} 
[INFO ] 2024-08-14 14:19:33.752 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 14:21:20.914 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 14:21:20.940 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER, offset: null 
[INFO ] 2024-08-14 14:21:20.940 - [任务 1 - Copy][Mysql] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-08-14 14:21:20.989 - [任务 1 - Copy][Mysql] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:21:20.993 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-08-14 14:21:20.993 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER 
[INFO ] 2024-08-14 14:21:20.995 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 14:21:20.995 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:21:20.996 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 14:21:20.996 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:21:21.027 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"name":"e084c45a-0b53-43a6-ac06-351c1304ccfd","offset":{"{\"server\":\"e084c45a-0b53-43a6-ac06-351c1304ccfd\"}":"{\"ts_sec\":1723616112,\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":1}"}} 
[INFO ] 2024-08-14 14:21:21.031 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: e084c45a-0b53-43a6-ac06-351c1304ccfd 
[INFO ] 2024-08-14 14:21:21.097 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 53780792
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e084c45a-0b53-43a6-ac06-351c1304ccfd
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e084c45a-0b53-43a6-ac06-351c1304ccfd
  database.hostname: localhost
  database.password: ********
  name: e084c45a-0b53-43a6-ac06-351c1304ccfd
  pdk.offset.string: {"name":"e084c45a-0b53-43a6-ac06-351c1304ccfd","offset":{"{\"server\":\"e084c45a-0b53-43a6-ac06-351c1304ccfd\"}":"{\"ts_sec\":1723616112,\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 14:21:21.097 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_OORDER 
[INFO ] 2024-08-14 14:21:21.097 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-14 14:49:27.403 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 14:49:27.706 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 14:49:27.707 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 14:49:27.707 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 14:49:27.737 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:49:27.738 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:49:27.738 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 14:49:27.738 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 14:49:27.740 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 54 ms 
[INFO ] 2024-08-14 14:49:27.740 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 14:49:27.762 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:49:27.763 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:49:27.763 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 14:49:27.763 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 14:49:27.763 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 23 ms 
[INFO ] 2024-08-14 14:49:31.984 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 14:49:31.985 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 14:49:31.985 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 14:49:32.021 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:49:32.023 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:49:51.433 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 14:49:51.433 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 14:49:51.448 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 14:49:51.538 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 14:49:51.538 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 6 
[INFO ] 2024-08-14 14:49:51.538 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 6 
[INFO ] 2024-08-14 14:49:51.650 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 110 ms 
[INFO ] 2024-08-14 14:49:51.652 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 112 ms 
[INFO ] 2024-08-14 14:49:52.491 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 14:49:52.492 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 14:49:52.492 - [任务 1 - Copy][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-14 14:49:52.495 - [任务 1 - Copy][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-14 14:49:56.175 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 14:49:56.219 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 14:49:56.220 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_NEW_ORDER, offset: null 
[INFO ] 2024-08-14 14:49:56.318 - [任务 1 - Copy][Mysql] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-08-14 14:49:56.321 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 14:49:56.321 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 14:49:56.373 - [任务 1 - Copy][Pg] - The table BMSQL_NEW_ORDER has already exist. 
[INFO ] 2024-08-14 14:49:56.374 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-08-14 14:49:56.487 - [任务 1 - Copy][Pg] - The table BMSQL_ITEM has already exist. 
[INFO ] 2024-08-14 14:49:56.490 - [任务 1 - Copy][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-14 14:49:56.613 - [任务 1 - Copy][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-14 14:49:56.614 - [任务 1 - Copy][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-14 14:49:56.797 - [任务 1 - Copy][Pg] - The table BMSQL_OORDER has already exist. 
[INFO ] 2024-08-14 14:49:56.797 - [任务 1 - Copy][Mysql] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:49:56.798 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_NEW_ORDER 
[INFO ] 2024-08-14 14:49:56.798 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_ITEM, offset: null 
[INFO ] 2024-08-14 14:49:56.799 - [任务 1 - Copy][Mysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-08-14 14:49:56.806 - [任务 1 - Copy][Mysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:49:56.807 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-08-14 14:49:56.808 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ITEM 
[INFO ] 2024-08-14 14:49:56.808 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-14 14:49:56.808 - [任务 1 - Copy][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-14 14:49:56.811 - [任务 1 - Copy][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:49:56.811 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-14 14:49:56.811 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_DISTRICT 
[INFO ] 2024-08-14 14:49:56.811 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-14 14:49:56.813 - [任务 1 - Copy][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-14 14:49:56.813 - [任务 1 - Copy][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:49:56.813 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-14 14:49:56.814 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CONFIG 
[INFO ] 2024-08-14 14:49:56.814 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-14 14:49:56.814 - [任务 1 - Copy][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-14 14:49:56.815 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-14 14:49:56.815 - [任务 1 - Copy][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:49:56.815 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CUSTOMER 
[INFO ] 2024-08-14 14:49:56.815 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER, offset: null 
[INFO ] 2024-08-14 14:49:56.815 - [任务 1 - Copy][Mysql] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-08-14 14:49:56.816 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-08-14 14:49:56.816 - [任务 1 - Copy][Mysql] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:49:56.816 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER 
[INFO ] 2024-08-14 14:49:56.816 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 14:49:56.816 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:49:56.817 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 14:49:56.821 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:49:56.829 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-14 14:49:56.859 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_NEW_ORDER 
[INFO ] 2024-08-14 14:49:56.861 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ITEM 
[INFO ] 2024-08-14 14:49:56.868 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: 69f7ef46-7ad5-41b8-adda-c05c9660d345 
[INFO ] 2024-08-14 14:49:56.868 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 910719324
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 69f7ef46-7ad5-41b8-adda-c05c9660d345
  database.port: 3306
  threadName: Debezium-Mysql-Connector-69f7ef46-7ad5-41b8-adda-c05c9660d345
  database.hostname: localhost
  database.password: ********
  name: 69f7ef46-7ad5-41b8-adda-c05c9660d345
  pdk.offset.string: {"name":"69f7ef46-7ad5-41b8-adda-c05c9660d345","offset":{"{\"server\":\"69f7ef46-7ad5-41b8-adda-c05c9660d345\"}":"{\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 14:49:57.070 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-14 14:50:01.031 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 14:50:01.553 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 14:50:01.554 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 14:50:01.554 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 14:50:01.570 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:50:01.571 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:50:01.571 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 14:50:01.571 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 14:50:01.572 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 120 ms 
[INFO ] 2024-08-14 14:50:01.585 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 14:50:01.585 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:50:01.585 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:50:01.585 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 14:50:01.586 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 14:50:01.790 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 14 ms 
[INFO ] 2024-08-14 14:50:02.086 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 14:50:02.086 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 14:50:02.086 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 14:50:02.116 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:50:02.118 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:50:36.347 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 14:50:36.348 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 14:50:36.440 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 14:50:36.440 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 14:50:36.494 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 8 
[INFO ] 2024-08-14 14:50:36.494 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 8 
[INFO ] 2024-08-14 14:50:36.611 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 116 ms 
[INFO ] 2024-08-14 14:50:36.612 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 116 ms 
[INFO ] 2024-08-14 14:50:36.881 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 14:50:36.881 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 14:50:36.906 - [任务 1 - Copy][Pg] - The table BMSQL_OORDER_BACK has already exist. 
[INFO ] 2024-08-14 14:50:36.968 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 14:50:36.968 - [任务 1 - Copy][Pg] - The table BMSQL_ORDER_LINE has already exist. 
[INFO ] 2024-08-14 14:50:36.969 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 14:50:36.979 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 14:50:36.980 - [任务 1 - Copy][Mysql] - batch offset found: {"BMSQL_NEW_ORDER":{"offset":{},"status":"RUNNING"}},stream offset found: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-14 14:50:36.980 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 14:51:14.151 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 14:51:14.152 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER_BACK, offset: null 
[INFO ] 2024-08-14 14:51:14.183 - [任务 1 - Copy][Mysql] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.279 - [任务 1 - Copy][Mysql] - Table [BMSQL_OORDER_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.279 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_OORDER_BACK' counts: 1 
[INFO ] 2024-08-14 14:51:14.280 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER_BACK 
[INFO ] 2024-08-14 14:51:14.281 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_NEW_ORDER, offset: {} 
[INFO ] 2024-08-14 14:51:14.285 - [任务 1 - Copy][Mysql] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.285 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-08-14 14:51:14.311 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_OORDER_BACK 
[INFO ] 2024-08-14 14:51:14.390 - [任务 1 - Copy][Mysql] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.390 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_NEW_ORDER 
[INFO ] 2024-08-14 14:51:14.390 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_ITEM, offset: null 
[INFO ] 2024-08-14 14:51:14.390 - [任务 1 - Copy][Mysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.396 - [任务 1 - Copy][Mysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.397 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-08-14 14:51:14.397 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ITEM 
[INFO ] 2024-08-14 14:51:14.397 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_ORDER_LINE, offset: null 
[INFO ] 2024-08-14 14:51:14.397 - [任务 1 - Copy][Mysql] - Table BMSQL_ORDER_LINE is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.420 - [任务 1 - Copy][Mysql] - Table [BMSQL_ORDER_LINE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.420 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_ORDER_LINE' counts: 0 
[INFO ] 2024-08-14 14:51:14.420 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ORDER_LINE 
[INFO ] 2024-08-14 14:51:14.420 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-14 14:51:14.422 - [任务 1 - Copy][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.422 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-14 14:51:14.423 - [任务 1 - Copy][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.423 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_DISTRICT 
[INFO ] 2024-08-14 14:51:14.423 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-14 14:51:14.423 - [任务 1 - Copy][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.425 - [任务 1 - Copy][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.425 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-14 14:51:14.425 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CONFIG 
[INFO ] 2024-08-14 14:51:14.425 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-14 14:51:14.426 - [任务 1 - Copy][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.426 - [任务 1 - Copy][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.426 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-14 14:51:14.427 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CUSTOMER 
[INFO ] 2024-08-14 14:51:14.427 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER, offset: null 
[INFO ] 2024-08-14 14:51:14.427 - [任务 1 - Copy][Mysql] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-08-14 14:51:14.431 - [任务 1 - Copy][Mysql] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:51:14.431 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-08-14 14:51:14.431 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER 
[INFO ] 2024-08-14 14:51:14.431 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_NEW_ORDER 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Mysql] - Skip table [BMSQL_OORDER_BACK] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ITEM 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Mysql] - Skip table [BMSQL_ORDER_LINE] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ORDER_LINE 
[INFO ] 2024-08-14 14:51:14.432 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 14:51:14.433 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:51:14.433 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 14:51:14.439 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:51:14.440 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-14 14:51:14.471 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: 69f7ef46-7ad5-41b8-adda-c05c9660d345 
[INFO ] 2024-08-14 14:51:14.472 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 493654572
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 69f7ef46-7ad5-41b8-adda-c05c9660d345
  database.port: 3306
  threadName: Debezium-Mysql-Connector-69f7ef46-7ad5-41b8-adda-c05c9660d345
  database.hostname: localhost
  database.password: ********
  name: 69f7ef46-7ad5-41b8-adda-c05c9660d345
  pdk.offset.string: {"name":"69f7ef46-7ad5-41b8-adda-c05c9660d345","offset":{"{\"server\":\"69f7ef46-7ad5-41b8-adda-c05c9660d345\"}":"{\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_OORDER_BACK,test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_ORDER_LINE,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 14:51:14.676 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-14 14:51:19.453 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_DISTRICT 
[INFO ] 2024-08-14 14:51:19.505 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CONFIG 
[INFO ] 2024-08-14 14:51:19.506 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CUSTOMER 
[INFO ] 2024-08-14 14:51:19.506 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_OORDER 
[INFO ] 2024-08-14 14:52:09.212 - [任务 1 - Copy] - Stop task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy)  
[INFO ] 2024-08-14 14:52:09.762 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
[INFO ] 2024-08-14 14:52:09.763 - [任务 1 - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 14:52:09.789 - [任务 1 - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 14:52:09.789 - [任务 1 - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:52:09.789 - [任务 1 - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-49f75b9e-6246-4927-b770-fc259a94f539 
[INFO ] 2024-08-14 14:52:09.790 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] schema data cleaned 
[INFO ] 2024-08-14 14:52:09.790 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] monitor closed 
[INFO ] 2024-08-14 14:52:09.791 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] close complete, cost 131 ms 
[INFO ] 2024-08-14 14:52:09.791 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] running status set to false 
[INFO ] 2024-08-14 14:52:09.808 - [任务 1 - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:52:09.808 - [任务 1 - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-e30e4e77-8146-4182-8b9e-735901345072 
[INFO ] 2024-08-14 14:52:09.808 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] schema data cleaned 
[INFO ] 2024-08-14 14:52:09.808 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] monitor closed 
[INFO ] 2024-08-14 14:52:09.808 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] close complete, cost 17 ms 
[INFO ] 2024-08-14 14:52:14.142 - [任务 1 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 14:52:14.143 - [任务 1 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 14:52:14.143 - [任务 1 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 14:52:14.172 - [任务 1 - Copy] - Remove memory task client succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:52:14.175 - [任务 1 - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy[66bb34b435ec5a7a9fa1b2ac] 
[INFO ] 2024-08-14 14:52:31.373 - [任务 1 - Copy] - Task initialization... 
[INFO ] 2024-08-14 14:52:31.374 - [任务 1 - Copy] - Start task milestones: 66bb34b435ec5a7a9fa1b2ac(任务 1 - Copy) 
[INFO ] 2024-08-14 14:52:31.422 - [任务 1 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 14:52:31.554 - [任务 1 - Copy] - The engine receives 任务 1 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 14:52:31.555 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] start preload schema,table counts: 9 
[INFO ] 2024-08-14 14:52:31.555 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] start preload schema,table counts: 9 
[INFO ] 2024-08-14 14:52:31.875 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] preload schema finished, cost 295 ms 
[INFO ] 2024-08-14 14:52:31.876 - [任务 1 - Copy][Pg] - Node Pg[e30e4e77-8146-4182-8b9e-735901345072] preload schema finished, cost 295 ms 
[INFO ] 2024-08-14 14:52:32.179 - [任务 1 - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 14:52:32.179 - [任务 1 - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 14:52:32.209 - [任务 1 - Copy][Pg] - The table BMSQL_STOCK has already exist. 
[INFO ] 2024-08-14 14:52:32.218 - [任务 1 - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 14:52:32.218 - [任务 1 - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 14:52:32.222 - [任务 1 - Copy][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-14 14:52:32.223 - [任务 1 - Copy][Mysql] - batch offset found: {"BMSQL_OORDER_BACK":{"offset":null,"status":"OVER"},"BMSQL_NEW_ORDER":{"offset":null,"status":"OVER"},"BMSQL_ORDER_LINE":{"offset":null,"status":"OVER"},"BMSQL_ITEM":{"offset":null,"status":"OVER"},"BMSQL_DISTRICT":{"offset":null,"status":"OVER"},"BMSQL_CONFIG":{"offset":{},"status":"RUNNING"}},stream offset found: {"name":"69f7ef46-7ad5-41b8-adda-c05c9660d345","offset":{"{\"server\":\"69f7ef46-7ad5-41b8-adda-c05c9660d345\"}":"{\"ts_sec\":1723618274,\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":1}"}} 
[INFO ] 2024-08-14 14:52:32.223 - [任务 1 - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 14:52:32.307 - [任务 1 - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 14:52:32.308 - [任务 1 - Copy][Mysql] - Starting batch read, table name: BMSQL_STOCK, offset: null 
[INFO ] 2024-08-14 14:52:32.370 - [任务 1 - Copy][Mysql] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2024-08-14 14:52:32.371 - [任务 1 - Copy][Mysql] - Query table 'BMSQL_STOCK' counts: 7899 
[INFO ] 2024-08-14 14:52:32.781 - [任务 1 - Copy][Mysql] - Table [BMSQL_STOCK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 14:52:32.781 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_STOCK 
[INFO ] 2024-08-14 14:52:32.782 - [任务 1 - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 14:52:32.782 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:52:32.782 - [任务 1 - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 14:52:32.782 - [任务 1 - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 14:52:32.818 - [任务 1 - Copy][Mysql] - Starting stream read, table list: [BMSQL_OORDER_BACK, BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"name":"69f7ef46-7ad5-41b8-adda-c05c9660d345","offset":{"{\"server\":\"69f7ef46-7ad5-41b8-adda-c05c9660d345\"}":"{\"ts_sec\":1723618274,\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":1}"}} 
[INFO ] 2024-08-14 14:52:32.819 - [任务 1 - Copy][Mysql] - Starting mysql cdc, server name: 69f7ef46-7ad5-41b8-adda-c05c9660d345 
[INFO ] 2024-08-14 14:52:32.973 - [任务 1 - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1869457572
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 69f7ef46-7ad5-41b8-adda-c05c9660d345
  database.port: 3306
  threadName: Debezium-Mysql-Connector-69f7ef46-7ad5-41b8-adda-c05c9660d345
  database.hostname: localhost
  database.password: ********
  name: 69f7ef46-7ad5-41b8-adda-c05c9660d345
  pdk.offset.string: {"name":"69f7ef46-7ad5-41b8-adda-c05c9660d345","offset":{"{\"server\":\"69f7ef46-7ad5-41b8-adda-c05c9660d345\"}":"{\"ts_sec\":1723618274,\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_OORDER_BACK,test.BMSQL_STOCK,test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_ORDER_LINE,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 14:52:32.974 - [任务 1 - Copy][Pg] - handle CompleteTableSnapshotBMSQL_STOCK 
[INFO ] 2024-08-14 14:52:33.174 - [任务 1 - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-14 16:52:09.766 - [任务 1 - Copy][Mysql] - Node Mysql[49f75b9e-6246-4927-b770-fc259a94f539] running status set to false 
