[INFO ] 2024-08-21 17:01:06.973 - [任务 11] - Task initialization... 
[INFO ] 2024-08-21 17:01:06.976 - [任务 11] - Start task milestones: 66c5acbc875f284fc6609257(任务 11) 
[INFO ] 2024-08-21 17:01:07.038 - [任务 11] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-21 17:01:07.213 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-21 17:01:07.215 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] start preload schema,table counts: 1 
[INFO ] 2024-08-21 17:01:07.216 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] start preload schema,table counts: 1 
[INFO ] 2024-08-21 17:01:07.263 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] preload schema finished, cost 54 ms 
[INFO ] 2024-08-21 17:01:07.264 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] preload schema finished, cost 53 ms 
[INFO ] 2024-08-21 17:01:08.060 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" read batch size: 100 
[INFO ] 2024-08-21 17:01:08.066 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" event queue capacity: 200 
[INFO ] 2024-08-21 17:01:08.066 - [任务 11][DB2TEST - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-21 17:01:08.275 - [任务 11][DB2TEST - Copy] - Table [CUSTOMERS] not open CDC 
[INFO ] 2024-08-21 17:01:08.276 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-21 17:01:08.283 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-21 17:01:08.284 - [任务 11][DB2TEST - Copy] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724230868267} 
[INFO ] 2024-08-21 17:01:08.284 - [任务 11][DB2TEST - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-21 17:01:08.374 - [任务 11][DB2TEST - Copy] - Initial sync started 
[INFO ] 2024-08-21 17:01:08.398 - [任务 11][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-21 17:01:08.400 - [任务 11][DB2TEST - Copy] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-21 17:01:08.563 - [任务 11][DB2TEST - Copy] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-21 17:01:08.563 - [任务 11][DB2TEST - Copy] - Query table 'CUSTOMERS' counts: 3 
[INFO ] 2024-08-21 17:01:08.566 - [任务 11][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-21 17:01:08.566 - [任务 11][DB2TEST - Copy] - Incremental sync starting... 
[INFO ] 2024-08-21 17:01:08.568 - [任务 11][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-21 17:01:08.568 - [任务 11][DB2TEST - Copy] - Starting stream read, table list: [CUSTOMERS], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724230868267} 
[WARN ] 2024-08-21 17:01:11.459 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 1 
[WARN ] 2024-08-21 17:01:14.100 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 2 
[WARN ] 2024-08-21 17:01:16.752 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 3 
[WARN ] 2024-08-21 17:01:19.399 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 4 
[WARN ] 2024-08-21 17:01:21.822 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 5 
[WARN ] 2024-08-21 17:01:24.395 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 6 
[WARN ] 2024-08-21 17:01:27.027 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 7 
[WARN ] 2024-08-21 17:01:29.649 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 8 
[WARN ] 2024-08-21 17:01:32.300 - [任务 11][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 9 
[INFO ] 2024-08-21 17:01:34.727 - [任务 11][DB2TEST - Copy] - Incremental sync completed 
[ERROR] 2024-08-21 17:01:34.801 - [任务 11][DB2TEST - Copy] - java.lang.RuntimeException: Exception occurs in Grpc Log Miner service <-- Error Message -->
java.lang.RuntimeException: Exception occurs in Grpc Log Miner service

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:256)
	io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:256)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more

[INFO ] 2024-08-21 17:01:34.801 - [任务 11][DB2TEST - Copy] - Job suspend in error handle 
[INFO ] 2024-08-21 17:01:35.038 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] running status set to false 
[INFO ] 2024-08-21 17:01:35.039 - [任务 11][DB2TEST - Copy] - Log Miner is shutting down... 
[INFO ] 2024-08-21 17:01:35.111 - [任务 11][DB2TEST - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:01:35.111 - [任务 11][DB2TEST - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:01:35.111 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] schema data cleaned 
[INFO ] 2024-08-21 17:01:35.113 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] monitor closed 
[INFO ] 2024-08-21 17:01:35.133 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] close complete, cost 93 ms 
[INFO ] 2024-08-21 17:01:35.133 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] running status set to false 
[INFO ] 2024-08-21 17:01:35.151 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-21 17:01:35.166 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:01:35.166 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:01:35.166 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] schema data cleaned 
[INFO ] 2024-08-21 17:01:35.168 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] monitor closed 
[INFO ] 2024-08-21 17:01:35.169 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] close complete, cost 37 ms 
[INFO ] 2024-08-21 17:01:36.609 - [任务 11] - Task [任务 11] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-21 17:01:36.658 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-21 17:01:36.658 - [任务 11] - Stop task milestones: 66c5acbc875f284fc6609257(任务 11)  
[INFO ] 2024-08-21 17:01:36.675 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-21 17:01:36.676 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-21 17:01:36.707 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c5acbc875f284fc6609257] 
[INFO ] 2024-08-21 17:01:36.707 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c5acbc875f284fc6609257] 
[INFO ] 2024-08-21 17:06:52.770 - [任务 11] - Task initialization... 
[INFO ] 2024-08-21 17:06:52.857 - [任务 11] - Start task milestones: 66c5acbc875f284fc6609257(任务 11) 
[INFO ] 2024-08-21 17:06:52.857 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-21 17:06:53.012 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-21 17:06:53.182 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] start preload schema,table counts: 3 
[INFO ] 2024-08-21 17:06:53.182 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] start preload schema,table counts: 3 
[INFO ] 2024-08-21 17:06:53.250 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] preload schema finished, cost 66 ms 
[INFO ] 2024-08-21 17:06:53.251 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] preload schema finished, cost 65 ms 
[INFO ] 2024-08-21 17:06:53.976 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" read batch size: 100 
[INFO ] 2024-08-21 17:06:53.978 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" event queue capacity: 200 
[INFO ] 2024-08-21 17:06:53.978 - [任务 11][DB2TEST - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-21 17:06:53.978 - [任务 11][DB2TEST - Copy] - batch offset found: {},stream offset not found. 
[INFO ] 2024-08-21 17:06:53.978 - [任务 11][DB2TEST - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-21 17:06:54.038 - [任务 11][DB2TEST - Copy] - Initial sync started 
[INFO ] 2024-08-21 17:06:54.039 - [任务 11][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-21 17:06:54.121 - [任务 11][DB2TEST - Copy] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-21 17:06:54.123 - [任务 11][DB2TEST - Copy] - Query table 'CUSTOMERS' counts: 3 
[INFO ] 2024-08-21 17:06:54.147 - [任务 11][DB2TEST - Copy] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-21 17:06:54.148 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-21 17:06:54.149 - [任务 11][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS1, offset: null 
[INFO ] 2024-08-21 17:06:54.149 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-21 17:06:54.151 - [任务 11][DB2TEST - Copy] - Table CUSTOMERS1 is going to be initial synced 
[INFO ] 2024-08-21 17:06:54.173 - [任务 11][DB2TEST - Copy] - Query table 'CUSTOMERS1' counts: 2 
[INFO ] 2024-08-21 17:06:54.174 - [任务 11][DB2TEST - Copy] - Table [CUSTOMERS1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-21 17:06:54.175 - [任务 11][DB2TEST - Copy] - Starting batch read, table name: TEST1, offset: null 
[INFO ] 2024-08-21 17:06:54.176 - [任务 11][DB2TEST - Copy] - Table TEST1 is going to be initial synced 
[INFO ] 2024-08-21 17:06:54.201 - [任务 11][DB2TEST - Copy] - Table [TEST1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-21 17:06:54.204 - [任务 11][DB2TEST - Copy] - Query table 'TEST1' counts: 1 
[INFO ] 2024-08-21 17:06:54.204 - [任务 11][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-21 17:06:54.750 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] running status set to false 
[INFO ] 2024-08-21 17:06:54.753 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] running status set to false 
[INFO ] 2024-08-21 17:06:54.765 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-21 17:06:54.775 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:06:54.776 - [任务 11][DB2TEST - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:06:54.782 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:06:54.787 - [任务 11][DB2TEST - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:06:54.796 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] schema data cleaned 
[INFO ] 2024-08-21 17:06:54.797 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] schema data cleaned 
[INFO ] 2024-08-21 17:06:54.798 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] monitor closed 
[INFO ] 2024-08-21 17:06:54.798 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] monitor closed 
[INFO ] 2024-08-21 17:06:54.799 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] close complete, cost 47 ms 
[INFO ] 2024-08-21 17:06:54.799 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] close complete, cost 75 ms 
[INFO ] 2024-08-21 17:06:55.861 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-21 17:06:55.897 - [任务 11] - Stop task milestones: 66c5acbc875f284fc6609257(任务 11)  
[INFO ] 2024-08-21 17:06:55.897 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-21 17:06:55.897 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-21 17:06:55.925 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c5acbc875f284fc6609257] 
[INFO ] 2024-08-21 17:06:55.926 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c5acbc875f284fc6609257] 
[INFO ] 2024-08-21 17:07:20.932 - [任务 11] - Task initialization... 
[INFO ] 2024-08-21 17:07:20.942 - [任务 11] - Start task milestones: 66c5acbc875f284fc6609257(任务 11) 
[INFO ] 2024-08-21 17:07:20.979 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-21 17:07:21.114 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-21 17:07:21.114 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] start preload schema,table counts: 1 
[INFO ] 2024-08-21 17:07:21.115 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] start preload schema,table counts: 1 
[INFO ] 2024-08-21 17:07:21.151 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] preload schema finished, cost 34 ms 
[INFO ] 2024-08-21 17:07:21.152 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] preload schema finished, cost 34 ms 
[INFO ] 2024-08-21 17:07:21.977 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" read batch size: 100 
[INFO ] 2024-08-21 17:07:21.979 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" event queue capacity: 200 
[INFO ] 2024-08-21 17:07:21.979 - [任务 11][DB2TEST - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-21 17:07:21.979 - [任务 11][DB2TEST - Copy] - batch offset found: {},stream offset not found. 
[INFO ] 2024-08-21 17:07:21.979 - [任务 11][DB2TEST - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-21 17:07:22.025 - [任务 11][DB2TEST - Copy] - Initial sync started 
[INFO ] 2024-08-21 17:07:22.034 - [任务 11][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-21 17:07:22.034 - [任务 11][DB2TEST - Copy] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-21 17:07:22.154 - [任务 11][DB2TEST - Copy] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-21 17:07:22.156 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-21 17:07:22.156 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-21 17:07:22.178 - [任务 11][DB2TEST - Copy] - Query table 'CUSTOMERS' counts: 3 
[INFO ] 2024-08-21 17:07:22.384 - [任务 11][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-21 17:07:22.700 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] running status set to false 
[INFO ] 2024-08-21 17:07:22.700 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] running status set to false 
[INFO ] 2024-08-21 17:07:22.715 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-21 17:07:22.723 - [任务 11][DB2TEST - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:07:22.723 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:07:22.723 - [任务 11][DB2TEST - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:07:22.723 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:07:22.723 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] schema data cleaned 
[INFO ] 2024-08-21 17:07:22.723 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] schema data cleaned 
[INFO ] 2024-08-21 17:07:22.723 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] monitor closed 
[INFO ] 2024-08-21 17:07:22.724 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] monitor closed 
[INFO ] 2024-08-21 17:07:22.726 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] close complete, cost 39 ms 
[INFO ] 2024-08-21 17:07:22.726 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] close complete, cost 41 ms 
[INFO ] 2024-08-21 17:07:26.002 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-21 17:07:26.024 - [任务 11] - Stop task milestones: 66c5acbc875f284fc6609257(任务 11)  
[INFO ] 2024-08-21 17:07:26.025 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-21 17:07:26.025 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-21 17:07:26.040 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c5acbc875f284fc6609257] 
[INFO ] 2024-08-21 17:07:26.044 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c5acbc875f284fc6609257] 
[INFO ] 2024-08-21 17:07:48.615 - [任务 11] - Task initialization... 
[INFO ] 2024-08-21 17:07:48.616 - [任务 11] - Start task milestones: 66c5acbc875f284fc6609257(任务 11) 
[INFO ] 2024-08-21 17:07:48.680 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-21 17:07:48.680 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-21 17:07:48.720 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] start preload schema,table counts: 1 
[INFO ] 2024-08-21 17:07:48.720 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] start preload schema,table counts: 1 
[INFO ] 2024-08-21 17:07:48.735 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] preload schema finished, cost 13 ms 
[INFO ] 2024-08-21 17:07:48.735 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] preload schema finished, cost 13 ms 
[INFO ] 2024-08-21 17:07:49.445 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-21 17:07:49.445 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-21 17:07:49.669 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" read batch size: 100 
[INFO ] 2024-08-21 17:07:49.670 - [任务 11][DB2TEST - Copy] - Source node "DB2TEST - Copy" event queue capacity: 200 
[INFO ] 2024-08-21 17:07:49.670 - [任务 11][DB2TEST - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-21 17:07:49.670 - [任务 11][DB2TEST - Copy] - batch offset found: {},stream offset not found. 
[INFO ] 2024-08-21 17:07:49.670 - [任务 11][DB2TEST - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-21 17:07:49.737 - [任务 11][DB2TEST - Copy] - Initial sync started 
[INFO ] 2024-08-21 17:07:49.738 - [任务 11][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS1, offset: null 
[INFO ] 2024-08-21 17:07:49.742 - [任务 11][DB2TEST - Copy] - Table CUSTOMERS1 is going to be initial synced 
[INFO ] 2024-08-21 17:07:49.833 - [任务 11][DB2TEST - Copy] - Table [CUSTOMERS1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-21 17:07:49.833 - [任务 11][DB2TEST - Copy] - Query table 'CUSTOMERS1' counts: 2 
[INFO ] 2024-08-21 17:07:50.037 - [任务 11][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-21 17:07:50.351 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] running status set to false 
[INFO ] 2024-08-21 17:07:50.351 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] running status set to false 
[INFO ] 2024-08-21 17:07:50.393 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-21 17:07:50.393 - [任务 11][DB2TEST - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:07:50.394 - [任务 11][DB2TEST - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-e32c055f-c40d-409d-a181-3c64b21a6884 
[INFO ] 2024-08-21 17:07:50.394 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] schema data cleaned 
[INFO ] 2024-08-21 17:07:50.398 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] monitor closed 
[INFO ] 2024-08-21 17:07:50.403 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:07:50.404 - [任务 11][DB2TEST - Copy] - Node DB2TEST - Copy[e32c055f-c40d-409d-a181-3c64b21a6884] close complete, cost 55 ms 
[INFO ] 2024-08-21 17:07:50.413 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-845158b1-6e81-4ec1-bdbe-d1a4771e205c 
[INFO ] 2024-08-21 17:07:50.415 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] schema data cleaned 
[INFO ] 2024-08-21 17:07:50.415 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] monitor closed 
[INFO ] 2024-08-21 17:07:50.415 - [任务 11][TestDummy] - Node TestDummy[845158b1-6e81-4ec1-bdbe-d1a4771e205c] close complete, cost 59 ms 
[INFO ] 2024-08-21 17:07:51.115 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-21 17:07:51.117 - [任务 11] - Stop task milestones: 66c5acbc875f284fc6609257(任务 11)  
[INFO ] 2024-08-21 17:07:51.146 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-21 17:07:51.148 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-21 17:07:51.184 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c5acbc875f284fc6609257] 
[INFO ] 2024-08-21 17:07:51.186 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c5acbc875f284fc6609257] 
