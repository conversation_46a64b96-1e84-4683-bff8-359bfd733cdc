[INFO ] 2024-07-26 17:40:02.486 - [任务 35] - Task initialization... 
[INFO ] 2024-07-26 17:40:02.489 - [任务 35] - Start task milestones: 66a36ed98191787d1e5af336(任务 35) 
[INFO ] 2024-07-26 17:40:02.589 - [任务 35] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 17:40:02.671 - [任务 35] - The engine receives 任务 35 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 17:40:02.744 - [任务 35][Mysql - Copy] - Node Mysql - Copy[1039e530-d7f8-40e6-a591-d157bb3d24b9] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:40:02.744 - [任务 35][TargetDummy] - Node TargetDummy[7a034368-e223-4164-aa83-d4bf9933a09b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:40:02.744 - [任务 35][Mysql - Copy] - Node <PERSON>sql - Copy[1039e530-d7f8-40e6-a591-d157bb3d24b9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:40:02.744 - [任务 35][TargetDummy] - Node TargetDummy[7a034368-e223-4164-aa83-d4bf9933a09b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:40:04.064 - [任务 35][Mysql - Copy] - Source node "Mysql - Copy" read batch size: 100 
[INFO ] 2024-07-26 17:40:04.065 - [任务 35][Mysql - Copy] - Source node "Mysql - Copy" event queue capacity: 200 
[INFO ] 2024-07-26 17:40:04.065 - [任务 35][Mysql - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 17:40:04.070 - [任务 35][Mysql - Copy] - batch offset found: {},stream offset found: {"filename":"binlog.000033","position":66763391,"gtidSet":""} 
[INFO ] 2024-07-26 17:40:04.071 - [任务 35][Mysql - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 17:40:04.132 - [任务 35][TargetDummy] - Node(TargetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-26 17:40:04.132 - [任务 35][TargetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 17:40:04.185 - [任务 35][Mysql - Copy] - Initial sync started 
[INFO ] 2024-07-26 17:40:04.187 - [任务 35][Mysql - Copy] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-26 17:40:04.188 - [任务 35][Mysql - Copy] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-26 17:40:04.281 - [任务 35][Mysql - Copy] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-26 17:40:04.281 - [任务 35][Mysql - Copy] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 17:40:04.282 - [任务 35][Mysql - Copy] - Initial sync completed 
[INFO ] 2024-07-26 17:40:04.282 - [任务 35][Mysql - Copy] - Incremental sync starting... 
[INFO ] 2024-07-26 17:40:04.282 - [任务 35][Mysql - Copy] - Initial sync completed 
[INFO ] 2024-07-26 17:40:04.282 - [任务 35][Mysql - Copy] - Starting stream read, table list: [CLAIM, _tapdata_heartbeat_table], offset: {"filename":"binlog.000033","position":66763391,"gtidSet":""} 
[INFO ] 2024-07-26 17:40:04.310 - [任务 35][Mysql - Copy] - Starting mysql cdc, server name: b30c1e15-43c8-4881-8025-076b1c41165f 
[INFO ] 2024-07-26 17:40:04.310 - [任务 35][Mysql - Copy] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 896806990
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b30c1e15-43c8-4881-8025-076b1c41165f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b30c1e15-43c8-4881-8025-076b1c41165f
  database.hostname: localhost
  database.password: ********
  name: b30c1e15-43c8-4881-8025-076b1c41165f
  pdk.offset.string: {"name":"b30c1e15-43c8-4881-8025-076b1c41165f","offset":{"{\"server\":\"b30c1e15-43c8-4881-8025-076b1c41165f\"}":"{\"file\":\"binlog.000033\",\"pos\":66763391,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-26 17:40:04.395 - [任务 35][Mysql - Copy] - Connector Mysql incremental start succeed, tables: [CLAIM, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 17:40:40.893 - [任务 35][Mysql - Copy] - Node Mysql - Copy[1039e530-d7f8-40e6-a591-d157bb3d24b9] running status set to false 
[INFO ] 2024-07-26 17:40:40.894 - [任务 35][Mysql - Copy] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-26 17:40:40.913 - [任务 35][Mysql - Copy] - Mysql binlog reader stopped 
[INFO ] 2024-07-26 17:40:40.913 - [任务 35][Mysql - Copy] - Incremental sync completed 
[INFO ] 2024-07-26 17:40:40.913 - [任务 35][Mysql - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-1039e530-d7f8-40e6-a591-d157bb3d24b9 
[INFO ] 2024-07-26 17:40:40.914 - [任务 35][Mysql - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-1039e530-d7f8-40e6-a591-d157bb3d24b9 
[INFO ] 2024-07-26 17:40:40.914 - [任务 35][Mysql - Copy] - Node Mysql - Copy[1039e530-d7f8-40e6-a591-d157bb3d24b9] schema data cleaned 
[INFO ] 2024-07-26 17:40:40.914 - [任务 35][Mysql - Copy] - Node Mysql - Copy[1039e530-d7f8-40e6-a591-d157bb3d24b9] monitor closed 
[INFO ] 2024-07-26 17:40:40.914 - [任务 35][Mysql - Copy] - Node Mysql - Copy[1039e530-d7f8-40e6-a591-d157bb3d24b9] close complete, cost 63 ms 
[INFO ] 2024-07-26 17:40:40.923 - [任务 35][TargetDummy] - Node TargetDummy[7a034368-e223-4164-aa83-d4bf9933a09b] running status set to false 
[INFO ] 2024-07-26 17:40:40.926 - [任务 35][TargetDummy] - Stop connector 
[INFO ] 2024-07-26 17:40:40.933 - [任务 35][TargetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-7a034368-e223-4164-aa83-d4bf9933a09b 
[INFO ] 2024-07-26 17:40:40.933 - [任务 35][TargetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-7a034368-e223-4164-aa83-d4bf9933a09b 
[INFO ] 2024-07-26 17:40:40.934 - [任务 35][TargetDummy] - Node TargetDummy[7a034368-e223-4164-aa83-d4bf9933a09b] schema data cleaned 
[INFO ] 2024-07-26 17:40:40.934 - [任务 35][TargetDummy] - Node TargetDummy[7a034368-e223-4164-aa83-d4bf9933a09b] monitor closed 
[INFO ] 2024-07-26 17:40:40.934 - [任务 35][TargetDummy] - Node TargetDummy[7a034368-e223-4164-aa83-d4bf9933a09b] close complete, cost 11 ms 
[INFO ] 2024-07-26 17:40:42.798 - [任务 35] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 17:40:42.798 - [任务 35] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@34974fba 
[INFO ] 2024-07-26 17:40:42.801 - [任务 35] - Stop task milestones: 66a36ed98191787d1e5af336(任务 35)  
[INFO ] 2024-07-26 17:40:42.939 - [任务 35] - Stopped task aspect(s) 
[INFO ] 2024-07-26 17:40:42.939 - [任务 35] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 17:40:42.967 - [任务 35] - Remove memory task client succeed, task: 任务 35[66a36ed98191787d1e5af336] 
[INFO ] 2024-07-26 17:40:42.968 - [任务 35] - Destroy memory task client cache succeed, task: 任务 35[66a36ed98191787d1e5af336] 
