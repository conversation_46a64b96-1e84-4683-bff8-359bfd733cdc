[INFO ] 2024-06-27 19:03:53.045 - [任务 4] - Start task milestones: 667d46ad13fc7a5f4e980192(任务 4) 
[INFO ] 2024-06-27 19:03:53.045 - [任务 4] - Task initialization... 
[INFO ] 2024-06-27 19:03:53.251 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 19:03:53.323 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 19:03:53.324 - [任务 4][CLAIM] - Node CLAIM[5bd04bc2-cae6-4682-9aab-6fba89b20dc2] start preload schema,table counts: 1 
[INFO ] 2024-06-27 19:03:53.326 - [任务 4][test3] - Node test3[fdaaf9e6-344e-43cf-b144-72a1f29f1c22] start preload schema,table counts: 1 
[INFO ] 2024-06-27 19:03:53.327 - [任务 4][CLAIM] - Node CLAIM[5bd04bc2-cae6-4682-9aab-6fba89b20dc2] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 19:03:53.531 - [任务 4][test3] - Node test3[fdaaf9e6-344e-43cf-b144-72a1f29f1c22] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 19:03:54.045 - [任务 4][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-06-27 19:03:54.047 - [任务 4][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-06-27 19:03:54.047 - [任务 4][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 19:03:54.227 - [任务 4][CLAIM] - batch offset found: {},stream offset found: {"cdcOffset":1719486234,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 19:03:54.227 - [任务 4][CLAIM] - Initial sync started 
[INFO ] 2024-06-27 19:03:54.233 - [任务 4][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-27 19:03:54.233 - [任务 4][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-27 19:03:54.353 - [任务 4][CLAIM] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-27 19:03:54.354 - [任务 4][test3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 19:03:57.564 - [任务 4][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-27 19:03:57.566 - [任务 4][CLAIM] - Initial sync completed 
[INFO ] 2024-06-27 19:03:57.568 - [任务 4][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-27 19:03:57.572 - [任务 4][CLAIM] - Initial sync completed 
[INFO ] 2024-06-27 19:03:57.590 - [任务 4][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719486234,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 19:03:59.248 - [任务 4][CLAIM] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[WARN ] 2024-06-27 19:07:01.271 - [任务 4][test3] - Expiring 1 record(s) for test3-2:120001 ms has passed since batch creation
 - Error record: io.tapdata.entity.event.dml.TapUpdateRecordEvent@131177cd: {"after":{"_id":"6510f74ca270a1cf553","SETTLED_DATE":1165482448000,"CLAIM_ID":"2","SETTLED_AMOUNT":32.0,"CLAIM_REASON":"zH25vvtvdx","POLICY_ID":"WIQZevvvv2Rl","CLAIM_DATE":1002532959000,"LAST_CHANGE":1327069989000},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1719486299000,"tableId":"CLAIM","time":1719486299954,"type":302}
 - Stack trace: org.apache.kafka.common.errors.TimeoutException: Expiring 1 record(s) for test3-2:120001 ms has passed since batch creation
 
[WARN ] 2024-06-27 19:09:40.005 - [任务 4][test3] - Expiring 2 record(s) for test3-2:120000 ms has passed since batch creation
 - Error record: io.tapdata.entity.event.dml.TapUpdateRecordEvent@3653829f: {"after":{"_id":"6510f74ca270a1cf553","SETTLED_DATE":1165482448000,"CLAIM_ID":"2","SETTLED_AMOUNT":33.0,"CLAIM_REASON":"zH25vvtvdx","POLICY_ID":"WIQZevvvv2Rl","CLAIM_DATE":1002532959000,"LAST_CHANGE":1327069989000},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1719486348000,"tableId":"CLAIM","time":1719486348936,"type":302}
 - Stack trace: org.apache.kafka.common.errors.TimeoutException: Expiring 2 record(s) for test3-2:120000 ms has passed since batch creation
 
[WARN ] 2024-06-27 19:09:40.006 - [任务 4][test3] - Expiring 2 record(s) for test3-2:120000 ms has passed since batch creation
 - Error record: io.tapdata.entity.event.dml.TapUpdateRecordEvent@7030edde: {"after":{"_id":"6510f74ca270a1cf553","SETTLED_DATE":1165482448000,"CLAIM_ID":"2","SETTLED_AMOUNT":36.0,"CLAIM_REASON":"zH25vvtvdx","POLICY_ID":"WIQZevvvv2Rl","CLAIM_DATE":1002532959000,"LAST_CHANGE":1327069989000},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1719486412000,"tableId":"CLAIM","time":1719486412959,"type":302}
 - Stack trace: org.apache.kafka.common.errors.TimeoutException: Expiring 2 record(s) for test3-2:120000 ms has passed since batch creation
 
[INFO ] 2024-06-27 19:10:01.307 - [任务 4][CLAIM] - Node CLAIM[5bd04bc2-cae6-4682-9aab-6fba89b20dc2] running status set to false 
