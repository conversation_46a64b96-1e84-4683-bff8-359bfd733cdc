[INFO ] 2024-09-06 10:25:58.027 - [任务 1] - Task initialization... 
[INFO ] 2024-09-06 10:25:58.235 - [任务 1] - Start task milestones: 66d4d8b387d1c857bc300eb3(任务 1) 
[INFO ] 2024-09-06 10:26:04.291 - [任务 1] - load tapTable task 66d4d8b387d1c857bc300eb2-01174249-637c-4903-a839-f6bfc91c7c62 complete, cost 4384ms 
[INFO ] 2024-09-06 10:26:04.457 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-09-06 10:26:04.547 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-06 10:26:04.694 - [任务 1][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:26:04.695 - [任务 1][test_customer] - Node test_customer[f191e13e-21d0-4f08-86d2-44a09576d18c] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:26:04.695 - [任务 1][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:26:04.696 - [任务 1][test_customer] - Node test_customer[f191e13e-21d0-4f08-86d2-44a09576d18c] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:26:04.735 - [任务 1][增强JS] - Node 增强JS[0465ef3c-57f4-452f-ad43-f2b6a9b5ce82] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:26:04.742 - [任务 1][增强JS] - Node 增强JS[0465ef3c-57f4-452f-ad43-f2b6a9b5ce82] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:26:04.753 - [任务 1][增强JS] - Node js_processor(增强JS: 0465ef3c-57f4-452f-ad43-f2b6a9b5ce82) enable batch process 
[INFO ] 2024-09-06 10:26:05.081 - [任务 1][test_customer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-06 10:26:05.239 - [任务 1][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-09-06 10:26:05.239 - [任务 1][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-09-06 10:26:05.240 - [任务 1][BMSQL_CUSTOMER] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-06 10:26:05.251 - [任务 1][BMSQL_CUSTOMER] - batch offset found: {"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"1dc26326-93d2-41ab-868c-dfb2d9be057b","offset":{"{\"server\":\"1dc26326-93d2-41ab-868c-dfb2d9be057b\"}":"{\"file\":\"binlog.000035\",\"pos\":2054948,\"server_id\":1}"}} 
[INFO ] 2024-09-06 10:26:05.252 - [任务 1][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-06 10:26:05.325 - [任务 1][BMSQL_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-09-06 10:26:05.325 - [任务 1][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-09-06 10:26:05.325 - [任务 1][BMSQL_CUSTOMER] - Starting stream read, table list: [BMSQL_CUSTOMER], offset: {"name":"1dc26326-93d2-41ab-868c-dfb2d9be057b","offset":{"{\"server\":\"1dc26326-93d2-41ab-868c-dfb2d9be057b\"}":"{\"file\":\"binlog.000035\",\"pos\":2054948,\"server_id\":1}"}} 
[INFO ] 2024-09-06 10:26:05.349 - [任务 1][BMSQL_CUSTOMER] - Starting mysql cdc, server name: 1dc26326-93d2-41ab-868c-dfb2d9be057b 
[INFO ] 2024-09-06 10:26:05.413 - [任务 1][BMSQL_CUSTOMER] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 498913332
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1dc26326-93d2-41ab-868c-dfb2d9be057b
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1dc26326-93d2-41ab-868c-dfb2d9be057b
  database.hostname: localhost
  database.password: ********
  name: 1dc26326-93d2-41ab-868c-dfb2d9be057b
  pdk.offset.string: {"name":"1dc26326-93d2-41ab-868c-dfb2d9be057b","offset":{"{\"server\":\"1dc26326-93d2-41ab-868c-dfb2d9be057b\"}":"{\"file\":\"binlog.000035\",\"pos\":2054948,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_CUSTOMER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-06 10:26:05.415 - [任务 1][BMSQL_CUSTOMER] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-09-06 11:01:19.299 - [任务 1][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[7b3cf21e-c55e-40c4-b6d4-16216fbd800d] running status set to false 
