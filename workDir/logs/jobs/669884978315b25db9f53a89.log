[INFO ] 2024-07-18 10:57:38.946 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Task initialization... 
[INFO ] 2024-07-18 10:57:38.972 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Start task milestones: 669884978315b25db9f53a89(t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447) 
[INFO ] 2024-07-18 10:57:39.367 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:57:39.368 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - The engine receives t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:57:39.440 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[e682aff2-3207-4aec-91f3-71c06b3bb4d5] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:57:39.440 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[66e60f19-63da-4b99-8edb-3de08ec79b6d] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:57:39.440 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[e682aff2-3207-4aec-91f3-71c06b3bb4d5] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 10:57:39.440 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[66e60f19-63da-4b99-8edb-3de08ec79b6d] preload schema finished, cost 2 ms 
[INFO ] 2024-07-18 10:57:39.716 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:57:39.716 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:57:39.716 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:57:39.716 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-18 10:57:39.810 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:57:39.888 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:57:39.888 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Starting batch read, table name: mock_100w, offset: null 
[INFO ] 2024-07-18 10:57:39.889 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Table mock_100w is going to be initial synced 
[INFO ] 2024-07-18 10:57:39.889 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Start mock_100w batch read 
[INFO ] 2024-07-18 10:57:40.094 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Query table 'mock_100w' counts: 1000000 
[INFO ] 2024-07-18 10:59:11.196 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Compile mock_100w batch read 
[INFO ] 2024-07-18 10:59:11.196 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Table [mock_100w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:59:11.197 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:59:21.845 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[e682aff2-3207-4aec-91f3-71c06b3bb4d5] running status set to false 
[INFO ] 2024-07-18 10:59:21.845 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[66e60f19-63da-4b99-8edb-3de08ec79b6d] running status set to false 
[INFO ] 2024-07-18 10:59:21.848 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 10:59:21.862 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-e682aff2-3207-4aec-91f3-71c06b3bb4d5 
[INFO ] 2024-07-18 10:59:21.862 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-e682aff2-3207-4aec-91f3-71c06b3bb4d5 
[INFO ] 2024-07-18 10:59:21.862 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[e682aff2-3207-4aec-91f3-71c06b3bb4d5] schema data cleaned 
[INFO ] 2024-07-18 10:59:21.862 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[e682aff2-3207-4aec-91f3-71c06b3bb4d5] monitor closed 
[INFO ] 2024-07-18 10:59:21.863 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[e682aff2-3207-4aec-91f3-71c06b3bb4d5] close complete, cost 22 ms 
[INFO ] 2024-07-18 10:59:21.899 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-66e60f19-63da-4b99-8edb-3de08ec79b6d 
[INFO ] 2024-07-18 10:59:21.900 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-66e60f19-63da-4b99-8edb-3de08ec79b6d 
[INFO ] 2024-07-18 10:59:21.900 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[66e60f19-63da-4b99-8edb-3de08ec79b6d] schema data cleaned 
[INFO ] 2024-07-18 10:59:21.900 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[66e60f19-63da-4b99-8edb-3de08ec79b6d] monitor closed 
[INFO ] 2024-07-18 10:59:22.102 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[66e60f19-63da-4b99-8edb-3de08ec79b6d] close complete, cost 58 ms 
[INFO ] 2024-07-18 10:59:26.133 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:59:26.281 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@10eff206 
[INFO ] 2024-07-18 10:59:26.282 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Stop task milestones: 669884978315b25db9f53a89(t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447)  
[INFO ] 2024-07-18 10:59:26.310 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:59:26.310 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:59:26.359 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Remove memory task client succeed, task: t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447[669884978315b25db9f53a89] 
[INFO ] 2024-07-18 10:59:26.359 - [t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447] - Destroy memory task client cache succeed, task: t_2.4-1-mock_to_mdb_full_1717403468657_3537-1721271447[669884978315b25db9f53a89] 
