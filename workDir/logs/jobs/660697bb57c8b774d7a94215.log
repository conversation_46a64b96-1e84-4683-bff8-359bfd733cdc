[INFO ] 2024-03-29 18:28:13.211 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:13.211 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:13.211 - [orders_import_import_import_import_import(100)][478b42bb-03f4-4d5e-b667-0b84bb131719] - Node 478b42bb-03f4-4d5e-b667-0b84bb131719[478b42bb-03f4-4d5e-b667-0b84bb131719] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:13.211 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:13.212 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:13.212 - [orders_import_import_import_import_import(100)][478b42bb-03f4-4d5e-b667-0b84bb131719] - Node 478b42bb-03f4-4d5e-b667-0b84bb131719[478b42bb-03f4-4d5e-b667-0b84bb131719] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:13.228 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:13.229 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3fb89c3d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3fb89c3d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3fb89c3d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:13.751 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:13.753 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:13.753 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:13.754 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:13.754 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:13.754 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 24 ms 
[INFO ] 2024-03-29 18:28:15.788 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:15.789 - [orders_import_import_import_import_import(100)][478b42bb-03f4-4d5e-b667-0b84bb131719] - Node 478b42bb-03f4-4d5e-b667-0b84bb131719[478b42bb-03f4-4d5e-b667-0b84bb131719] running status set to false 
[INFO ] 2024-03-29 18:28:15.789 - [orders_import_import_import_import_import(100)][478b42bb-03f4-4d5e-b667-0b84bb131719] - Node 478b42bb-03f4-4d5e-b667-0b84bb131719[478b42bb-03f4-4d5e-b667-0b84bb131719] schema data cleaned 
[INFO ] 2024-03-29 18:28:15.789 - [orders_import_import_import_import_import(100)][478b42bb-03f4-4d5e-b667-0b84bb131719] - Node 478b42bb-03f4-4d5e-b667-0b84bb131719[478b42bb-03f4-4d5e-b667-0b84bb131719] monitor closed 
[INFO ] 2024-03-29 18:28:15.789 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:15.789 - [orders_import_import_import_import_import(100)][478b42bb-03f4-4d5e-b667-0b84bb131719] - Node 478b42bb-03f4-4d5e-b667-0b84bb131719[478b42bb-03f4-4d5e-b667-0b84bb131719] close complete, cost 5 ms 
[INFO ] 2024-03-29 18:28:15.789 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:15.789 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:28:15.791 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-478b42bb-03f4-4d5e-b667-0b84bb131719 complete, cost 2648ms 
[INFO ] 2024-03-29 18:28:23.913 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:23.913 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:23.913 - [orders_import_import_import_import_import(100)][984819f9-d3e4-4da6-83e4-63f2543222c7] - Node 984819f9-d3e4-4da6-83e4-63f2543222c7[984819f9-d3e4-4da6-83e4-63f2543222c7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:23.913 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:23.913 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:23.913 - [orders_import_import_import_import_import(100)][984819f9-d3e4-4da6-83e4-63f2543222c7] - Node 984819f9-d3e4-4da6-83e4-63f2543222c7[984819f9-d3e4-4da6-83e4-63f2543222c7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:23.936 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:23.937 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f5d2820 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f5d2820 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f5d2820 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:24.032 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:24.032 - [orders_import_import_import_import_import(100)][40329e73-aad7-40e4-ac23-0ffd3a0b6af7] - Node 40329e73-aad7-40e4-ac23-0ffd3a0b6af7[40329e73-aad7-40e4-ac23-0ffd3a0b6af7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:24.032 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:24.032 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:24.032 - [orders_import_import_import_import_import(100)][40329e73-aad7-40e4-ac23-0ffd3a0b6af7] - Node 40329e73-aad7-40e4-ac23-0ffd3a0b6af7[40329e73-aad7-40e4-ac23-0ffd3a0b6af7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:24.040 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:24.040 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:24.088 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55c37778 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55c37778 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55c37778 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:24.088 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:24.089 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:24.089 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:24.089 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:24.089 - [orders_import_import_import_import_import(100)][13a1f17d-2be2-42e0-911f-d1d773cb8626] - Node 13a1f17d-2be2-42e0-911f-d1d773cb8626[13a1f17d-2be2-42e0-911f-d1d773cb8626] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:24.089 - [orders_import_import_import_import_import(100)][13a1f17d-2be2-42e0-911f-d1d773cb8626] - Node 13a1f17d-2be2-42e0-911f-d1d773cb8626[13a1f17d-2be2-42e0-911f-d1d773cb8626] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:24.098 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:24.101 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1d1b3a62 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1d1b3a62 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1d1b3a62 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:24.129 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:24.129 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:24.129 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:24.129 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:24.129 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:24.298 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 15 ms 
[ERROR] 2024-03-29 18:28:24.299 - [orders_import_import_import_import_import(100)][Order Details] - start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:230)
	... 16 more

[INFO ] 2024-03-29 18:28:24.326 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:24.326 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:24.326 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:24.326 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:24.326 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:24.326 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:28:25.051 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.053 - [orders_import_import_import_import_import(100)][d5dd8251-39a0-4312-bd01-191012807917] - Node d5dd8251-39a0-4312-bd01-191012807917[d5dd8251-39a0-4312-bd01-191012807917] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:25.053 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.053 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.053 - [orders_import_import_import_import_import(100)][d5dd8251-39a0-4312-bd01-191012807917] - Node d5dd8251-39a0-4312-bd01-191012807917[d5dd8251-39a0-4312-bd01-191012807917] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.053 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.103 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:25.104 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7c24041f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7c24041f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7c24041f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:25.229 - [orders_import_import_import_import_import(100)][c8aa005c-f43f-4f3d-8185-79b9cf37614f] - Node c8aa005c-f43f-4f3d-8185-79b9cf37614f[c8aa005c-f43f-4f3d-8185-79b9cf37614f] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:25.229 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.229 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.229 - [orders_import_import_import_import_import(100)][c8aa005c-f43f-4f3d-8185-79b9cf37614f] - Node c8aa005c-f43f-4f3d-8185-79b9cf37614f[c8aa005c-f43f-4f3d-8185-79b9cf37614f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.229 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:28:25.229 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 3 ms 
[INFO ] 2024-03-29 18:28:25.278 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:28:25.349 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[ERROR] 2024-03-29 18:28:25.349 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@603638a9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@603638a9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@603638a9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:25.350 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.350 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.350 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:25.350 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:25.350 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 40 ms 
[INFO ] 2024-03-29 18:28:25.441 - [orders_import_import_import_import_import(100)][30ff1421-661d-4985-883c-6ece286f05d8] - Node 30ff1421-661d-4985-883c-6ece286f05d8[30ff1421-661d-4985-883c-6ece286f05d8] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:25.441 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.441 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.441 - [orders_import_import_import_import_import(100)][30ff1421-661d-4985-883c-6ece286f05d8] - Node 30ff1421-661d-4985-883c-6ece286f05d8[30ff1421-661d-4985-883c-6ece286f05d8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.441 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.441 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.464 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.464 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:25.466 - [orders_import_import_import_import_import(100)][17aa878e-4a5f-451a-b2d1-7ff87a0772e9] - Node 17aa878e-4a5f-451a-b2d1-7ff87a0772e9[17aa878e-4a5f-451a-b2d1-7ff87a0772e9] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:25.466 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.466 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.466 - [orders_import_import_import_import_import(100)][17aa878e-4a5f-451a-b2d1-7ff87a0772e9] - Node 17aa878e-4a5f-451a-b2d1-7ff87a0772e9[17aa878e-4a5f-451a-b2d1-7ff87a0772e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:25.491 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:28:25.491 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:28:25.507 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:25.532 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.532 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.532 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:25.533 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:25.533 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 28 ms 
[ERROR] 2024-03-29 18:28:25.539 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@390eb92 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@390eb92 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@390eb92 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:28:25.539 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@193d5d55 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@193d5d55 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@193d5d55 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:25.723 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:25.723 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.723 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.723 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:25.724 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:25.724 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:28:25.888 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:25.888 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.888 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:25.888 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:25.888 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:25.888 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 12 ms 
[INFO ] 2024-03-29 18:28:26.462 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:26.463 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.463 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:26.463 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:28:26.470 - [orders_import_import_import_import_import(100)][984819f9-d3e4-4da6-83e4-63f2543222c7] - Node 984819f9-d3e4-4da6-83e4-63f2543222c7[984819f9-d3e4-4da6-83e4-63f2543222c7] running status set to false 
[INFO ] 2024-03-29 18:28:26.471 - [orders_import_import_import_import_import(100)][984819f9-d3e4-4da6-83e4-63f2543222c7] - Node 984819f9-d3e4-4da6-83e4-63f2543222c7[984819f9-d3e4-4da6-83e4-63f2543222c7] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.471 - [orders_import_import_import_import_import(100)][984819f9-d3e4-4da6-83e4-63f2543222c7] - Node 984819f9-d3e4-4da6-83e4-63f2543222c7[984819f9-d3e4-4da6-83e4-63f2543222c7] monitor closed 
[INFO ] 2024-03-29 18:28:26.473 - [orders_import_import_import_import_import(100)][984819f9-d3e4-4da6-83e4-63f2543222c7] - Node 984819f9-d3e4-4da6-83e4-63f2543222c7[984819f9-d3e4-4da6-83e4-63f2543222c7] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:26.474 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-984819f9-d3e4-4da6-83e4-63f2543222c7 complete, cost 2630ms 
[INFO ] 2024-03-29 18:28:26.553 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:26.553 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:26.554 - [orders_import_import_import_import_import(100)][e1c174b8-1c8f-4407-a901-f5e23e478407] - Node e1c174b8-1c8f-4407-a901-f5e23e478407[e1c174b8-1c8f-4407-a901-f5e23e478407] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:26.554 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:26.554 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:26.554 - [orders_import_import_import_import_import(100)][e1c174b8-1c8f-4407-a901-f5e23e478407] - Node e1c174b8-1c8f-4407-a901-f5e23e478407[e1c174b8-1c8f-4407-a901-f5e23e478407] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:26.563 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:26.564 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.564 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:26.564 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:26.565 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:28:26.589 - [orders_import_import_import_import_import(100)][40329e73-aad7-40e4-ac23-0ffd3a0b6af7] - Node 40329e73-aad7-40e4-ac23-0ffd3a0b6af7[40329e73-aad7-40e4-ac23-0ffd3a0b6af7] running status set to false 
[INFO ] 2024-03-29 18:28:26.589 - [orders_import_import_import_import_import(100)][40329e73-aad7-40e4-ac23-0ffd3a0b6af7] - Node 40329e73-aad7-40e4-ac23-0ffd3a0b6af7[40329e73-aad7-40e4-ac23-0ffd3a0b6af7] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.589 - [orders_import_import_import_import_import(100)][40329e73-aad7-40e4-ac23-0ffd3a0b6af7] - Node 40329e73-aad7-40e4-ac23-0ffd3a0b6af7[40329e73-aad7-40e4-ac23-0ffd3a0b6af7] monitor closed 
[INFO ] 2024-03-29 18:28:26.589 - [orders_import_import_import_import_import(100)][40329e73-aad7-40e4-ac23-0ffd3a0b6af7] - Node 40329e73-aad7-40e4-ac23-0ffd3a0b6af7[40329e73-aad7-40e4-ac23-0ffd3a0b6af7] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:28:26.595 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-40329e73-aad7-40e4-ac23-0ffd3a0b6af7 complete, cost 2594ms 
[ERROR] 2024-03-29 18:28:26.792 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31ff3678 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31ff3678 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31ff3678 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:26.792 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:26.807 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:26.808 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:26.808 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.809 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:26.809 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 18 ms 
[INFO ] 2024-03-29 18:28:26.839 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:26.839 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:26.839 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.840 - [orders_import_import_import_import_import(100)][13a1f17d-2be2-42e0-911f-d1d773cb8626] - Node 13a1f17d-2be2-42e0-911f-d1d773cb8626[13a1f17d-2be2-42e0-911f-d1d773cb8626] running status set to false 
[INFO ] 2024-03-29 18:28:26.840 - [orders_import_import_import_import_import(100)][13a1f17d-2be2-42e0-911f-d1d773cb8626] - Node 13a1f17d-2be2-42e0-911f-d1d773cb8626[13a1f17d-2be2-42e0-911f-d1d773cb8626] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.840 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:26.840 - [orders_import_import_import_import_import(100)][13a1f17d-2be2-42e0-911f-d1d773cb8626] - Node 13a1f17d-2be2-42e0-911f-d1d773cb8626[13a1f17d-2be2-42e0-911f-d1d773cb8626] monitor closed 
[INFO ] 2024-03-29 18:28:26.841 - [orders_import_import_import_import_import(100)][13a1f17d-2be2-42e0-911f-d1d773cb8626] - Node 13a1f17d-2be2-42e0-911f-d1d773cb8626[13a1f17d-2be2-42e0-911f-d1d773cb8626] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:26.841 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:28:26.841 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 18:28:26.842 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 18:28:26.842 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:26.842 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:26.842 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:28:26.843 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-13a1f17d-2be2-42e0-911f-d1d773cb8626 complete, cost 2786ms 
[INFO ] 2024-03-29 18:28:27.510 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.511 - [orders_import_import_import_import_import(100)][74ce0653-c860-42e3-a923-9555c6b3fe99] - Node 74ce0653-c860-42e3-a923-9555c6b3fe99[74ce0653-c860-42e3-a923-9555c6b3fe99] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:27.511 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.511 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.512 - [orders_import_import_import_import_import(100)][74ce0653-c860-42e3-a923-9555c6b3fe99] - Node 74ce0653-c860-42e3-a923-9555c6b3fe99[74ce0653-c860-42e3-a923-9555c6b3fe99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.512 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:28:27.623 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:27.623 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5cf7ad05 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5cf7ad05 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5cf7ad05 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:27.633 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:27.633 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:27.633 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:27.634 - [orders_import_import_import_import_import(100)][d5dd8251-39a0-4312-bd01-191012807917] - Node d5dd8251-39a0-4312-bd01-191012807917[d5dd8251-39a0-4312-bd01-191012807917] running status set to false 
[INFO ] 2024-03-29 18:28:27.634 - [orders_import_import_import_import_import(100)][d5dd8251-39a0-4312-bd01-191012807917] - Node d5dd8251-39a0-4312-bd01-191012807917[d5dd8251-39a0-4312-bd01-191012807917] schema data cleaned 
[INFO ] 2024-03-29 18:28:27.634 - [orders_import_import_import_import_import(100)][d5dd8251-39a0-4312-bd01-191012807917] - Node d5dd8251-39a0-4312-bd01-191012807917[d5dd8251-39a0-4312-bd01-191012807917] monitor closed 
[INFO ] 2024-03-29 18:28:27.634 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:28:27.635 - [orders_import_import_import_import_import(100)][d5dd8251-39a0-4312-bd01-191012807917] - Node d5dd8251-39a0-4312-bd01-191012807917[d5dd8251-39a0-4312-bd01-191012807917] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.678 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-d5dd8251-39a0-4312-bd01-191012807917 complete, cost 2636ms 
[INFO ] 2024-03-29 18:28:27.678 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.678 - [orders_import_import_import_import_import(100)][e07b2870-3003-487a-ab76-1f20bc7c190b] - Node e07b2870-3003-487a-ab76-1f20bc7c190b[e07b2870-3003-487a-ab76-1f20bc7c190b] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:27.678 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.678 - [orders_import_import_import_import_import(100)][e07b2870-3003-487a-ab76-1f20bc7c190b] - Node e07b2870-3003-487a-ab76-1f20bc7c190b[e07b2870-3003-487a-ab76-1f20bc7c190b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.678 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:28:27.678 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:28:27.721 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:27.723 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3df7cc70 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3df7cc70 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3df7cc70 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:27.758 - [orders_import_import_import_import_import(100)][6a086d42-9312-455d-a9fc-955f857c22a4] - Node 6a086d42-9312-455d-a9fc-955f857c22a4[6a086d42-9312-455d-a9fc-955f857c22a4] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:27.758 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.758 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.758 - [orders_import_import_import_import_import(100)][6a086d42-9312-455d-a9fc-955f857c22a4] - Node 6a086d42-9312-455d-a9fc-955f857c22a4[6a086d42-9312-455d-a9fc-955f857c22a4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.758 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.758 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.795 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:27.795 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bf48144 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bf48144 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bf48144 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:27.829 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:27.829 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:27.829 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:27.829 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:27.829 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:27.829 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 26 ms 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][7df031d9-78fa-4c28-bbd3-c95845514e1c] - Node 7df031d9-78fa-4c28-bbd3-c95845514e1c[7df031d9-78fa-4c28-bbd3-c95845514e1c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][7df031d9-78fa-4c28-bbd3-c95845514e1c] - Node 7df031d9-78fa-4c28-bbd3-c95845514e1c[7df031d9-78fa-4c28-bbd3-c95845514e1c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:27.843 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:27.844 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.847 - [orders_import_import_import_import_import(100)][c8aa005c-f43f-4f3d-8185-79b9cf37614f] - Node c8aa005c-f43f-4f3d-8185-79b9cf37614f[c8aa005c-f43f-4f3d-8185-79b9cf37614f] running status set to false 
[INFO ] 2024-03-29 18:28:27.847 - [orders_import_import_import_import_import(100)][c8aa005c-f43f-4f3d-8185-79b9cf37614f] - Node c8aa005c-f43f-4f3d-8185-79b9cf37614f[c8aa005c-f43f-4f3d-8185-79b9cf37614f] schema data cleaned 
[INFO ] 2024-03-29 18:28:27.847 - [orders_import_import_import_import_import(100)][c8aa005c-f43f-4f3d-8185-79b9cf37614f] - Node c8aa005c-f43f-4f3d-8185-79b9cf37614f[c8aa005c-f43f-4f3d-8185-79b9cf37614f] monitor closed 
[INFO ] 2024-03-29 18:28:27.848 - [orders_import_import_import_import_import(100)][c8aa005c-f43f-4f3d-8185-79b9cf37614f] - Node c8aa005c-f43f-4f3d-8185-79b9cf37614f[c8aa005c-f43f-4f3d-8185-79b9cf37614f] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:27.884 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-c8aa005c-f43f-4f3d-8185-79b9cf37614f complete, cost 2686ms 
[INFO ] 2024-03-29 18:28:27.886 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:27.991 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@40efc393 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@40efc393 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@40efc393 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:27.991 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:28.004 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:28.004 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:28.004 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:28.004 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:28.005 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:28:28.046 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:28.047 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:28.047 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:28.047 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:28.047 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:28.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:28.069 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:28.069 - [orders_import_import_import_import_import(100)][17aa878e-4a5f-451a-b2d1-7ff87a0772e9] - Node 17aa878e-4a5f-451a-b2d1-7ff87a0772e9[17aa878e-4a5f-451a-b2d1-7ff87a0772e9] running status set to false 
[INFO ] 2024-03-29 18:28:28.069 - [orders_import_import_import_import_import(100)][17aa878e-4a5f-451a-b2d1-7ff87a0772e9] - Node 17aa878e-4a5f-451a-b2d1-7ff87a0772e9[17aa878e-4a5f-451a-b2d1-7ff87a0772e9] schema data cleaned 
[INFO ] 2024-03-29 18:28:28.069 - [orders_import_import_import_import_import(100)][17aa878e-4a5f-451a-b2d1-7ff87a0772e9] - Node 17aa878e-4a5f-451a-b2d1-7ff87a0772e9[17aa878e-4a5f-451a-b2d1-7ff87a0772e9] monitor closed 
[INFO ] 2024-03-29 18:28:28.069 - [orders_import_import_import_import_import(100)][17aa878e-4a5f-451a-b2d1-7ff87a0772e9] - Node 17aa878e-4a5f-451a-b2d1-7ff87a0772e9[17aa878e-4a5f-451a-b2d1-7ff87a0772e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:28.072 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-17aa878e-4a5f-451a-b2d1-7ff87a0772e9 complete, cost 2657ms 
[INFO ] 2024-03-29 18:28:28.072 - [orders_import_import_import_import_import(100)][30ff1421-661d-4985-883c-6ece286f05d8] - Node 30ff1421-661d-4985-883c-6ece286f05d8[30ff1421-661d-4985-883c-6ece286f05d8] running status set to false 
[INFO ] 2024-03-29 18:28:28.072 - [orders_import_import_import_import_import(100)][30ff1421-661d-4985-883c-6ece286f05d8] - Node 30ff1421-661d-4985-883c-6ece286f05d8[30ff1421-661d-4985-883c-6ece286f05d8] schema data cleaned 
[INFO ] 2024-03-29 18:28:28.072 - [orders_import_import_import_import_import(100)][30ff1421-661d-4985-883c-6ece286f05d8] - Node 30ff1421-661d-4985-883c-6ece286f05d8[30ff1421-661d-4985-883c-6ece286f05d8] monitor closed 
[INFO ] 2024-03-29 18:28:28.073 - [orders_import_import_import_import_import(100)][30ff1421-661d-4985-883c-6ece286f05d8] - Node 30ff1421-661d-4985-883c-6ece286f05d8[30ff1421-661d-4985-883c-6ece286f05d8] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:28.073 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-30ff1421-661d-4985-883c-6ece286f05d8 complete, cost 2697ms 
[INFO ] 2024-03-29 18:28:28.215 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:28.215 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:28.215 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:28.216 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:28.216 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:28.216 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 12 ms 
[INFO ] 2024-03-29 18:28:29.150 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:29.150 - [orders_import_import_import_import_import(100)][e1c174b8-1c8f-4407-a901-f5e23e478407] - Node e1c174b8-1c8f-4407-a901-f5e23e478407[e1c174b8-1c8f-4407-a901-f5e23e478407] running status set to false 
[INFO ] 2024-03-29 18:28:29.150 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:29.150 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:29.150 - [orders_import_import_import_import_import(100)][e1c174b8-1c8f-4407-a901-f5e23e478407] - Node e1c174b8-1c8f-4407-a901-f5e23e478407[e1c174b8-1c8f-4407-a901-f5e23e478407] schema data cleaned 
[INFO ] 2024-03-29 18:28:29.151 - [orders_import_import_import_import_import(100)][e1c174b8-1c8f-4407-a901-f5e23e478407] - Node e1c174b8-1c8f-4407-a901-f5e23e478407[e1c174b8-1c8f-4407-a901-f5e23e478407] monitor closed 
[INFO ] 2024-03-29 18:28:29.151 - [orders_import_import_import_import_import(100)][e1c174b8-1c8f-4407-a901-f5e23e478407] - Node e1c174b8-1c8f-4407-a901-f5e23e478407[e1c174b8-1c8f-4407-a901-f5e23e478407] close complete, cost 16 ms 
[INFO ] 2024-03-29 18:28:29.151 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:28:29.155 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-e1c174b8-1c8f-4407-a901-f5e23e478407 complete, cost 2720ms 
[INFO ] 2024-03-29 18:28:29.164 - [orders_import_import_import_import_import(100)][5b7de45e-637b-42c7-ba91-172848f64c60] - Node 5b7de45e-637b-42c7-ba91-172848f64c60[5b7de45e-637b-42c7-ba91-172848f64c60] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:29.164 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:29.164 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:29.164 - [orders_import_import_import_import_import(100)][5b7de45e-637b-42c7-ba91-172848f64c60] - Node 5b7de45e-637b-42c7-ba91-172848f64c60[5b7de45e-637b-42c7-ba91-172848f64c60] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:29.165 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:29.165 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:29.298 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:29.298 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f0daf91 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f0daf91 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f0daf91 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:29.300 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:29.300 - [orders_import_import_import_import_import(100)][36c4074b-90ba-460c-9dc3-1930d9fa7c3c] - Node 36c4074b-90ba-460c-9dc3-1930d9fa7c3c[36c4074b-90ba-460c-9dc3-1930d9fa7c3c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:29.300 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:29.300 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:29.300 - [orders_import_import_import_import_import(100)][36c4074b-90ba-460c-9dc3-1930d9fa7c3c] - Node 36c4074b-90ba-460c-9dc3-1930d9fa7c3c[36c4074b-90ba-460c-9dc3-1930d9fa7c3c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:29.300 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:29.323 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:29.428 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7da49183 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7da49183 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7da49183 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:29.428 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:29.451 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:29.451 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:29.451 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:29.453 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:29.453 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 31 ms 
[INFO ] 2024-03-29 18:28:29.608 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:29.608 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:29.608 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:29.608 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:29.608 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:29.813 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:28:30.225 - [orders_import_import_import_import_import(100)][74ce0653-c860-42e3-a923-9555c6b3fe99] - Node 74ce0653-c860-42e3-a923-9555c6b3fe99[74ce0653-c860-42e3-a923-9555c6b3fe99] running status set to false 
[INFO ] 2024-03-29 18:28:30.225 - [orders_import_import_import_import_import(100)][74ce0653-c860-42e3-a923-9555c6b3fe99] - Node 74ce0653-c860-42e3-a923-9555c6b3fe99[74ce0653-c860-42e3-a923-9555c6b3fe99] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.225 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:30.225 - [orders_import_import_import_import_import(100)][74ce0653-c860-42e3-a923-9555c6b3fe99] - Node 74ce0653-c860-42e3-a923-9555c6b3fe99[74ce0653-c860-42e3-a923-9555c6b3fe99] monitor closed 
[INFO ] 2024-03-29 18:28:30.226 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.226 - [orders_import_import_import_import_import(100)][74ce0653-c860-42e3-a923-9555c6b3fe99] - Node 74ce0653-c860-42e3-a923-9555c6b3fe99[74ce0653-c860-42e3-a923-9555c6b3fe99] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:28:30.226 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:30.227 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-74ce0653-c860-42e3-a923-9555c6b3fe99 complete, cost 2813ms 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][e07b2870-3003-487a-ab76-1f20bc7c190b] - Node e07b2870-3003-487a-ab76-1f20bc7c190b[e07b2870-3003-487a-ab76-1f20bc7c190b] running status set to false 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][e07b2870-3003-487a-ab76-1f20bc7c190b] - Node e07b2870-3003-487a-ab76-1f20bc7c190b[e07b2870-3003-487a-ab76-1f20bc7c190b] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][e07b2870-3003-487a-ab76-1f20bc7c190b] - Node e07b2870-3003-487a-ab76-1f20bc7c190b[e07b2870-3003-487a-ab76-1f20bc7c190b] monitor closed 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:28:30.252 - [orders_import_import_import_import_import(100)][e07b2870-3003-487a-ab76-1f20bc7c190b] - Node e07b2870-3003-487a-ab76-1f20bc7c190b[e07b2870-3003-487a-ab76-1f20bc7c190b] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:28:30.328 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-e07b2870-3003-487a-ab76-1f20bc7c190b complete, cost 2641ms 
[INFO ] 2024-03-29 18:28:30.328 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:30.328 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.328 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:30.328 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:28:30.329 - [orders_import_import_import_import_import(100)][6a086d42-9312-455d-a9fc-955f857c22a4] - Node 6a086d42-9312-455d-a9fc-955f857c22a4[6a086d42-9312-455d-a9fc-955f857c22a4] running status set to false 
[INFO ] 2024-03-29 18:28:30.329 - [orders_import_import_import_import_import(100)][6a086d42-9312-455d-a9fc-955f857c22a4] - Node 6a086d42-9312-455d-a9fc-955f857c22a4[6a086d42-9312-455d-a9fc-955f857c22a4] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.329 - [orders_import_import_import_import_import(100)][6a086d42-9312-455d-a9fc-955f857c22a4] - Node 6a086d42-9312-455d-a9fc-955f857c22a4[6a086d42-9312-455d-a9fc-955f857c22a4] monitor closed 
[INFO ] 2024-03-29 18:28:30.329 - [orders_import_import_import_import_import(100)][6a086d42-9312-455d-a9fc-955f857c22a4] - Node 6a086d42-9312-455d-a9fc-955f857c22a4[6a086d42-9312-455d-a9fc-955f857c22a4] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.330 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-6a086d42-9312-455d-a9fc-955f857c22a4 complete, cost 2625ms 
[INFO ] 2024-03-29 18:28:30.436 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:30.436 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.436 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:30.436 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.440 - [orders_import_import_import_import_import(100)][7df031d9-78fa-4c28-bbd3-c95845514e1c] - Node 7df031d9-78fa-4c28-bbd3-c95845514e1c[7df031d9-78fa-4c28-bbd3-c95845514e1c] running status set to false 
[INFO ] 2024-03-29 18:28:30.440 - [orders_import_import_import_import_import(100)][7df031d9-78fa-4c28-bbd3-c95845514e1c] - Node 7df031d9-78fa-4c28-bbd3-c95845514e1c[7df031d9-78fa-4c28-bbd3-c95845514e1c] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.440 - [orders_import_import_import_import_import(100)][7df031d9-78fa-4c28-bbd3-c95845514e1c] - Node 7df031d9-78fa-4c28-bbd3-c95845514e1c[7df031d9-78fa-4c28-bbd3-c95845514e1c] monitor closed 
[INFO ] 2024-03-29 18:28:30.441 - [orders_import_import_import_import_import(100)][7df031d9-78fa-4c28-bbd3-c95845514e1c] - Node 7df031d9-78fa-4c28-bbd3-c95845514e1c[7df031d9-78fa-4c28-bbd3-c95845514e1c] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.441 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-7df031d9-78fa-4c28-bbd3-c95845514e1c complete, cost 2667ms 
[INFO ] 2024-03-29 18:28:30.670 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:30.670 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:30.670 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.670 - [orders_import_import_import_import_import(100)][5409017a-9642-4a73-a6ef-675c6a2d8bbf] - Node 5409017a-9642-4a73-a6ef-675c6a2d8bbf[5409017a-9642-4a73-a6ef-675c6a2d8bbf] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:30.670 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.725 - [orders_import_import_import_import_import(100)][5409017a-9642-4a73-a6ef-675c6a2d8bbf] - Node 5409017a-9642-4a73-a6ef-675c6a2d8bbf[5409017a-9642-4a73-a6ef-675c6a2d8bbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.728 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:30.791 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72a6b8e3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72a6b8e3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72a6b8e3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:30.791 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:30.791 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:30.791 - [orders_import_import_import_import_import(100)][1aa33379-d3c6-4c68-8944-a93dd3276ddc] - Node 1aa33379-d3c6-4c68-8944-a93dd3276ddc[1aa33379-d3c6-4c68-8944-a93dd3276ddc] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:30.791 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.792 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.792 - [orders_import_import_import_import_import(100)][1aa33379-d3c6-4c68-8944-a93dd3276ddc] - Node 1aa33379-d3c6-4c68-8944-a93dd3276ddc[1aa33379-d3c6-4c68-8944-a93dd3276ddc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:30.804 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:30.806 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@557726e0 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@557726e0 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@557726e0 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:30.923 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:30.923 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:30.923 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:30.923 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:30.923 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:31.057 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:28:31.057 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:31.058 - [orders_import_import_import_import_import(100)][f085c4c6-549e-4ce9-a208-62fcc7ab47a5] - Node f085c4c6-549e-4ce9-a208-62fcc7ab47a5[f085c4c6-549e-4ce9-a208-62fcc7ab47a5] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:31.058 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:31.058 - [orders_import_import_import_import_import(100)][f085c4c6-549e-4ce9-a208-62fcc7ab47a5] - Node f085c4c6-549e-4ce9-a208-62fcc7ab47a5[f085c4c6-549e-4ce9-a208-62fcc7ab47a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:31.058 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:31.058 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:28:31.084 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:31.090 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5c45260f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5c45260f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5c45260f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:31.113 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:31.114 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:31.114 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:31.114 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:31.114 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:31.115 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 23 ms 
[INFO ] 2024-03-29 18:28:31.323 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:31.336 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:31.336 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:31.336 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:31.336 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:31.541 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 31 ms 
[INFO ] 2024-03-29 18:28:31.814 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:31.814 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:31.814 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:31.818 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:31.819 - [orders_import_import_import_import_import(100)][5b7de45e-637b-42c7-ba91-172848f64c60] - Node 5b7de45e-637b-42c7-ba91-172848f64c60[5b7de45e-637b-42c7-ba91-172848f64c60] running status set to false 
[INFO ] 2024-03-29 18:28:31.819 - [orders_import_import_import_import_import(100)][5b7de45e-637b-42c7-ba91-172848f64c60] - Node 5b7de45e-637b-42c7-ba91-172848f64c60[5b7de45e-637b-42c7-ba91-172848f64c60] schema data cleaned 
[INFO ] 2024-03-29 18:28:31.819 - [orders_import_import_import_import_import(100)][5b7de45e-637b-42c7-ba91-172848f64c60] - Node 5b7de45e-637b-42c7-ba91-172848f64c60[5b7de45e-637b-42c7-ba91-172848f64c60] monitor closed 
[INFO ] 2024-03-29 18:28:31.819 - [orders_import_import_import_import_import(100)][5b7de45e-637b-42c7-ba91-172848f64c60] - Node 5b7de45e-637b-42c7-ba91-172848f64c60[5b7de45e-637b-42c7-ba91-172848f64c60] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:31.865 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-5b7de45e-637b-42c7-ba91-172848f64c60 complete, cost 2762ms 
[INFO ] 2024-03-29 18:28:31.865 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:31.865 - [orders_import_import_import_import_import(100)][36c4074b-90ba-460c-9dc3-1930d9fa7c3c] - Node 36c4074b-90ba-460c-9dc3-1930d9fa7c3c[36c4074b-90ba-460c-9dc3-1930d9fa7c3c] running status set to false 
[INFO ] 2024-03-29 18:28:31.865 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:31.865 - [orders_import_import_import_import_import(100)][36c4074b-90ba-460c-9dc3-1930d9fa7c3c] - Node 36c4074b-90ba-460c-9dc3-1930d9fa7c3c[36c4074b-90ba-460c-9dc3-1930d9fa7c3c] schema data cleaned 
[INFO ] 2024-03-29 18:28:31.866 - [orders_import_import_import_import_import(100)][36c4074b-90ba-460c-9dc3-1930d9fa7c3c] - Node 36c4074b-90ba-460c-9dc3-1930d9fa7c3c[36c4074b-90ba-460c-9dc3-1930d9fa7c3c] monitor closed 
[INFO ] 2024-03-29 18:28:31.866 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:31.866 - [orders_import_import_import_import_import(100)][36c4074b-90ba-460c-9dc3-1930d9fa7c3c] - Node 36c4074b-90ba-460c-9dc3-1930d9fa7c3c[36c4074b-90ba-460c-9dc3-1930d9fa7c3c] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:28:31.867 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:28:31.867 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-36c4074b-90ba-460c-9dc3-1930d9fa7c3c complete, cost 2622ms 
[INFO ] 2024-03-29 18:28:33.271 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:33.271 - [orders_import_import_import_import_import(100)][5409017a-9642-4a73-a6ef-675c6a2d8bbf] - Node 5409017a-9642-4a73-a6ef-675c6a2d8bbf[5409017a-9642-4a73-a6ef-675c6a2d8bbf] running status set to false 
[INFO ] 2024-03-29 18:28:33.271 - [orders_import_import_import_import_import(100)][5409017a-9642-4a73-a6ef-675c6a2d8bbf] - Node 5409017a-9642-4a73-a6ef-675c6a2d8bbf[5409017a-9642-4a73-a6ef-675c6a2d8bbf] schema data cleaned 
[INFO ] 2024-03-29 18:28:33.272 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:33.272 - [orders_import_import_import_import_import(100)][5409017a-9642-4a73-a6ef-675c6a2d8bbf] - Node 5409017a-9642-4a73-a6ef-675c6a2d8bbf[5409017a-9642-4a73-a6ef-675c6a2d8bbf] monitor closed 
[INFO ] 2024-03-29 18:28:33.272 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:33.272 - [orders_import_import_import_import_import(100)][5409017a-9642-4a73-a6ef-675c6a2d8bbf] - Node 5409017a-9642-4a73-a6ef-675c6a2d8bbf[5409017a-9642-4a73-a6ef-675c6a2d8bbf] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:28:33.274 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:28:33.274 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-5409017a-9642-4a73-a6ef-675c6a2d8bbf complete, cost 2646ms 
[INFO ] 2024-03-29 18:28:33.325 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:33.325 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:33.325 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:33.326 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:33.326 - [orders_import_import_import_import_import(100)][1aa33379-d3c6-4c68-8944-a93dd3276ddc] - Node 1aa33379-d3c6-4c68-8944-a93dd3276ddc[1aa33379-d3c6-4c68-8944-a93dd3276ddc] running status set to false 
[INFO ] 2024-03-29 18:28:33.326 - [orders_import_import_import_import_import(100)][1aa33379-d3c6-4c68-8944-a93dd3276ddc] - Node 1aa33379-d3c6-4c68-8944-a93dd3276ddc[1aa33379-d3c6-4c68-8944-a93dd3276ddc] schema data cleaned 
[INFO ] 2024-03-29 18:28:33.326 - [orders_import_import_import_import_import(100)][1aa33379-d3c6-4c68-8944-a93dd3276ddc] - Node 1aa33379-d3c6-4c68-8944-a93dd3276ddc[1aa33379-d3c6-4c68-8944-a93dd3276ddc] monitor closed 
[INFO ] 2024-03-29 18:28:33.326 - [orders_import_import_import_import_import(100)][1aa33379-d3c6-4c68-8944-a93dd3276ddc] - Node 1aa33379-d3c6-4c68-8944-a93dd3276ddc[1aa33379-d3c6-4c68-8944-a93dd3276ddc] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:33.531 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-1aa33379-d3c6-4c68-8944-a93dd3276ddc complete, cost 2566ms 
[INFO ] 2024-03-29 18:28:33.614 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:33.616 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:33.616 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:33.616 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:33.616 - [orders_import_import_import_import_import(100)][f085c4c6-549e-4ce9-a208-62fcc7ab47a5] - Node f085c4c6-549e-4ce9-a208-62fcc7ab47a5[f085c4c6-549e-4ce9-a208-62fcc7ab47a5] running status set to false 
[INFO ] 2024-03-29 18:28:33.617 - [orders_import_import_import_import_import(100)][f085c4c6-549e-4ce9-a208-62fcc7ab47a5] - Node f085c4c6-549e-4ce9-a208-62fcc7ab47a5[f085c4c6-549e-4ce9-a208-62fcc7ab47a5] schema data cleaned 
[INFO ] 2024-03-29 18:28:33.617 - [orders_import_import_import_import_import(100)][f085c4c6-549e-4ce9-a208-62fcc7ab47a5] - Node f085c4c6-549e-4ce9-a208-62fcc7ab47a5[f085c4c6-549e-4ce9-a208-62fcc7ab47a5] monitor closed 
[INFO ] 2024-03-29 18:28:33.617 - [orders_import_import_import_import_import(100)][f085c4c6-549e-4ce9-a208-62fcc7ab47a5] - Node f085c4c6-549e-4ce9-a208-62fcc7ab47a5[f085c4c6-549e-4ce9-a208-62fcc7ab47a5] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:28:33.617 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-f085c4c6-549e-4ce9-a208-62fcc7ab47a5 complete, cost 2599ms 
[INFO ] 2024-03-29 18:28:36.669 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:36.669 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:36.669 - [orders_import_import_import_import_import(100)][5e8c1ef2-7425-4577-ac97-771a1d0521fa] - Node 5e8c1ef2-7425-4577-ac97-771a1d0521fa[5e8c1ef2-7425-4577-ac97-771a1d0521fa] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:28:36.669 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:36.669 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:28:36.669 - [orders_import_import_import_import_import(100)][5e8c1ef2-7425-4577-ac97-771a1d0521fa] - Node 5e8c1ef2-7425-4577-ac97-771a1d0521fa[5e8c1ef2-7425-4577-ac97-771a1d0521fa] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:28:36.738 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:28:36.924 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@a0db033 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@a0db033 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@a0db033 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:28:36.924 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:28:36.938 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:36.938 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:28:36.938 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:28:36.938 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:28:36.938 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 15 ms 
[INFO ] 2024-03-29 18:28:39.285 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:28:39.286 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:28:39.287 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:28:39.287 - [orders_import_import_import_import_import(100)][5e8c1ef2-7425-4577-ac97-771a1d0521fa] - Node 5e8c1ef2-7425-4577-ac97-771a1d0521fa[5e8c1ef2-7425-4577-ac97-771a1d0521fa] running status set to false 
[INFO ] 2024-03-29 18:28:39.287 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:28:39.287 - [orders_import_import_import_import_import(100)][5e8c1ef2-7425-4577-ac97-771a1d0521fa] - Node 5e8c1ef2-7425-4577-ac97-771a1d0521fa[5e8c1ef2-7425-4577-ac97-771a1d0521fa] schema data cleaned 
[INFO ] 2024-03-29 18:28:39.287 - [orders_import_import_import_import_import(100)][5e8c1ef2-7425-4577-ac97-771a1d0521fa] - Node 5e8c1ef2-7425-4577-ac97-771a1d0521fa[5e8c1ef2-7425-4577-ac97-771a1d0521fa] monitor closed 
[INFO ] 2024-03-29 18:28:39.292 - [orders_import_import_import_import_import(100)][5e8c1ef2-7425-4577-ac97-771a1d0521fa] - Node 5e8c1ef2-7425-4577-ac97-771a1d0521fa[5e8c1ef2-7425-4577-ac97-771a1d0521fa] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:28:39.292 - [orders_import_import_import_import_import(100)] - load tapTable task 660697bb57c8b774d7a94215-5e8c1ef2-7425-4577-ac97-771a1d0521fa complete, cost 2735ms 
