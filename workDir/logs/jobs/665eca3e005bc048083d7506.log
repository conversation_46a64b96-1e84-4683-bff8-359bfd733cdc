[INFO ] 2024-06-04 16:04:07.565 - [任务 4] - Task initialization... 
[INFO ] 2024-06-04 16:04:07.566 - [任务 4] - Start task milestones: 665eca3e005bc048083d7506(任务 4) 
[INFO ] 2024-06-04 16:04:07.589 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-04 16:04:07.689 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-04 16:04:07.689 - [任务 4][SourcePg] - Node SourcePg[c0db6710-63a3-4ec1-ba61-b7b4bffba7f1] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:04:07.689 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:04:07.706 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] preload schema finished, cost 18 ms 
[INFO ] 2024-06-04 16:04:07.909 - [任务 4][SourcePg] - Node SourcePg[c0db6710-63a3-4ec1-ba61-b7b4bffba7f1] preload schema finished, cost 18 ms 
[INFO ] 2024-06-04 16:04:08.475 - [任务 4][SourcePg] - Source node "SourcePg" read batch size: 100 
[INFO ] 2024-06-04 16:04:08.475 - [任务 4][SourcePg] - Source node "SourcePg" event queue capacity: 200 
[INFO ] 2024-06-04 16:04:08.475 - [任务 4][SourcePg] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-04 16:04:08.621 - [任务 4][TargetSql] - Node(TargetSql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-04 16:04:08.621 - [任务 4][TargetSql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-04 16:04:08.659 - [任务 4][SourcePg] - new logical replication slot created, slotName:tapdata_cdc_d0e4a1be_dec4_42ff_8b20_b9603424fbfc 
[INFO ] 2024-06-04 16:04:08.660 - [任务 4][SourcePg] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-06-04 16:04:08.660 - [任务 4][SourcePg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-04 16:04:08.719 - [任务 4][SourcePg] - Initial sync started 
[INFO ] 2024-06-04 16:04:08.720 - [任务 4][SourcePg] - Starting batch read, table name: TEST_001, offset: null 
[INFO ] 2024-06-04 16:04:08.770 - [任务 4][SourcePg] - Table TEST_001 is going to be initial synced 
[INFO ] 2024-06-04 16:04:08.771 - [任务 4][SourcePg] - Table [TEST_001] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-04 16:04:08.776 - [任务 4][SourcePg] - Query table 'TEST_001' counts: 4 
[INFO ] 2024-06-04 16:04:08.776 - [任务 4][SourcePg] - Initial sync completed 
[INFO ] 2024-06-04 16:04:08.777 - [任务 4][SourcePg] - Incremental sync starting... 
[INFO ] 2024-06-04 16:04:08.777 - [任务 4][SourcePg] - Initial sync completed 
[INFO ] 2024-06-04 16:04:08.821 - [任务 4][SourcePg] - Starting stream read, table list: [TEST_001], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-06-04 16:04:08.823 - [任务 4][SourcePg] - Using an existing logical replication slot, slotName:tapdata_cdc_d0e4a1be_dec4_42ff_8b20_b9603424fbfc 
[INFO ] 2024-06-04 16:04:10.242 - [任务 4][SourcePg] - Connector PostgreSQL incremental start succeed, tables: [TEST_001], data change syncing 
[INFO ] 2024-06-04 16:04:55.051 - [任务 4] - Stop task milestones: 665eca3e005bc048083d7506(任务 4)  
[INFO ] 2024-06-04 16:04:55.136 - [任务 4][SourcePg] - Node SourcePg[c0db6710-63a3-4ec1-ba61-b7b4bffba7f1] running status set to false 
[INFO ] 2024-06-04 16:04:55.664 - [任务 4][SourcePg] - PDK connector node stopped: HazelcastSourcePdkDataNode-c0db6710-63a3-4ec1-ba61-b7b4bffba7f1 
[INFO ] 2024-06-04 16:04:55.664 - [任务 4][SourcePg] - PDK connector node released: HazelcastSourcePdkDataNode-c0db6710-63a3-4ec1-ba61-b7b4bffba7f1 
[INFO ] 2024-06-04 16:04:55.666 - [任务 4][SourcePg] - Node SourcePg[c0db6710-63a3-4ec1-ba61-b7b4bffba7f1] schema data cleaned 
[INFO ] 2024-06-04 16:04:55.667 - [任务 4][SourcePg] - Node SourcePg[c0db6710-63a3-4ec1-ba61-b7b4bffba7f1] monitor closed 
[INFO ] 2024-06-04 16:04:55.667 - [任务 4][SourcePg] - Node SourcePg[c0db6710-63a3-4ec1-ba61-b7b4bffba7f1] close complete, cost 531 ms 
[INFO ] 2024-06-04 16:04:55.668 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] running status set to false 
[INFO ] 2024-06-04 16:04:55.694 - [任务 4][TargetSql] - PDK connector node stopped: HazelcastTargetPdkDataNode-81c3eb26-5934-47e0-9e00-743a3c755758 
[INFO ] 2024-06-04 16:04:55.694 - [任务 4][TargetSql] - PDK connector node released: HazelcastTargetPdkDataNode-81c3eb26-5934-47e0-9e00-743a3c755758 
[INFO ] 2024-06-04 16:04:55.695 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] schema data cleaned 
[INFO ] 2024-06-04 16:04:55.695 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] monitor closed 
[INFO ] 2024-06-04 16:04:55.695 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] close complete, cost 27 ms 
[INFO ] 2024-06-04 16:04:59.454 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-04 16:04:59.456 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-06-04 16:04:59.456 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-06-04 16:04:59.495 - [任务 4] - Remove memory task client succeed, task: 任务 4[665eca3e005bc048083d7506] 
[INFO ] 2024-06-04 16:04:59.496 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[665eca3e005bc048083d7506] 
[INFO ] 2024-06-04 16:05:24.471 - [任务 4] - Task initialization... 
[INFO ] 2024-06-04 16:05:24.472 - [任务 4] - Start task milestones: 665eca3e005bc048083d7506(任务 4) 
[INFO ] 2024-06-04 16:05:24.491 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-04 16:05:24.580 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-04 16:05:24.581 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:05:24.581 - [任务 4][SourceMongo] - Node SourceMongo[4b5bcf9d-982b-4674-8be4-de98455cbba1] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:05:24.599 - [任务 4][SourceMongo] - Node SourceMongo[4b5bcf9d-982b-4674-8be4-de98455cbba1] preload schema finished, cost 19 ms 
[INFO ] 2024-06-04 16:05:24.599 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] preload schema finished, cost 20 ms 
[INFO ] 2024-06-04 16:05:25.337 - [任务 4][TargetSql] - Node(TargetSql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-04 16:05:25.337 - [任务 4][TargetSql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-04 16:05:25.507 - [任务 4][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-04 16:05:25.507 - [任务 4][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-04 16:05:25.507 - [任务 4][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-04 16:05:25.614 - [任务 4][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1717488325,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-04 16:05:25.664 - [任务 4][SourceMongo] - Initial sync started 
[INFO ] 2024-06-04 16:05:25.664 - [任务 4][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-04 16:05:25.691 - [任务 4][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-04 16:05:25.691 - [任务 4][SourceMongo] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-06-04 16:05:52.838 - [任务 4][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-04 16:05:52.838 - [任务 4][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-04 16:05:52.839 - [任务 4][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-04 16:05:52.839 - [任务 4][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-04 16:05:52.849 - [任务 4][SourceMongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1717488325,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-04 16:05:53.059 - [任务 4][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-04 16:06:10.571 - [任务 4] - Stop task milestones: 665eca3e005bc048083d7506(任务 4)  
[INFO ] 2024-06-04 16:06:11.057 - [任务 4][SourceMongo] - Node SourceMongo[4b5bcf9d-982b-4674-8be4-de98455cbba1] running status set to false 
[INFO ] 2024-06-04 16:06:11.057 - [任务 4][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-4b5bcf9d-982b-4674-8be4-de98455cbba1 
[INFO ] 2024-06-04 16:06:11.057 - [任务 4][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-4b5bcf9d-982b-4674-8be4-de98455cbba1 
[INFO ] 2024-06-04 16:06:11.057 - [任务 4][SourceMongo] - Node SourceMongo[4b5bcf9d-982b-4674-8be4-de98455cbba1] schema data cleaned 
[INFO ] 2024-06-04 16:06:11.058 - [任务 4][SourceMongo] - Node SourceMongo[4b5bcf9d-982b-4674-8be4-de98455cbba1] monitor closed 
[INFO ] 2024-06-04 16:06:11.058 - [任务 4][SourceMongo] - Node SourceMongo[4b5bcf9d-982b-4674-8be4-de98455cbba1] close complete, cost 48 ms 
[INFO ] 2024-06-04 16:06:11.074 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] running status set to false 
[INFO ] 2024-06-04 16:06:11.074 - [任务 4][TargetSql] - PDK connector node stopped: HazelcastTargetPdkDataNode-81c3eb26-5934-47e0-9e00-743a3c755758 
[INFO ] 2024-06-04 16:06:11.074 - [任务 4][TargetSql] - PDK connector node released: HazelcastTargetPdkDataNode-81c3eb26-5934-47e0-9e00-743a3c755758 
[INFO ] 2024-06-04 16:06:11.074 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] schema data cleaned 
[INFO ] 2024-06-04 16:06:11.074 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] monitor closed 
[INFO ] 2024-06-04 16:06:11.075 - [任务 4][TargetSql] - Node TargetSql[81c3eb26-5934-47e0-9e00-743a3c755758] close complete, cost 16 ms 
[INFO ] 2024-06-04 16:06:14.720 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-04 16:06:14.720 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-06-04 16:06:14.720 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-06-04 16:06:14.754 - [任务 4] - Remove memory task client succeed, task: 任务 4[665eca3e005bc048083d7506] 
[INFO ] 2024-06-04 16:06:14.761 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[665eca3e005bc048083d7506] 
