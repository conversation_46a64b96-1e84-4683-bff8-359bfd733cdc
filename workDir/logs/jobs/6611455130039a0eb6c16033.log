[INFO ] 2024-04-06 20:51:44.403 - [任务 54] - Task initialization... 
[INFO ] 2024-04-06 20:51:44.406 - [任务 54] - Start task milestones: 6611455130039a0eb6c16033(任务 54) 
[INFO ] 2024-04-06 20:51:44.580 - [任务 54] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-04-06 20:51:44.580 - [任务 54] - The engine receives 任务 54 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 20:51:44.653 - [任务 54][test1] - Node test1[fe693b51-450e-4b35-a5e2-e112335f77b4] start preload schema,table counts: 1 
[INFO ] 2024-04-06 20:51:44.701 - [任务 54][CLAIM] - Node CLAIM[ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7] start preload schema,table counts: 1 
[INFO ] 2024-04-06 20:51:44.701 - [任务 54][CLAIM] - Node CLAIM[ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7] preload schema finished, cost 39 ms 
[INFO ] 2024-04-06 20:51:44.701 - [任务 54][test1] - Node test1[fe693b51-450e-4b35-a5e2-e112335f77b4] preload schema finished, cost 41 ms 
[ERROR] 2024-04-06 20:51:44.762 - [任务 54][test1] - Map name: PdkStateMap_fe693b51-450e-4b35-a5e2-e112335f77b4 <-- Error Message -->
Map name: PdkStateMap_fe693b51-450e-4b35-a5e2-e112335f77b4

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_fe693b51-450e-4b35-a5e2-e112335f77b4
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[ERROR] 2024-04-06 20:51:44.774 - [任务 54][CLAIM] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@73927dac failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@73927dac failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@73927dac failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@73927dac failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:129)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712407904, "i" : 39 } }, "signature" : { "hash" : { "$binary" : "ZzCUB/z/mbQNgNhi6XR2wZtI9I4=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

[INFO ] 2024-04-06 20:51:44.783 - [任务 54][test1] - Job suspend in error handle 
[INFO ] 2024-04-06 20:51:44.784 - [任务 54][CLAIM] - Node CLAIM[ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7] running status set to false 
[INFO ] 2024-04-06 20:51:44.787 - [任务 54][CLAIM] - PDK connector node stopped: null 
[INFO ] 2024-04-06 20:51:44.787 - [任务 54][CLAIM] - PDK connector node released: null 
[INFO ] 2024-04-06 20:51:44.787 - [任务 54][CLAIM] - Node CLAIM[ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7] schema data cleaned 
[INFO ] 2024-04-06 20:51:44.790 - [任务 54][CLAIM] - Node CLAIM[ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7] monitor closed 
[INFO ] 2024-04-06 20:51:44.793 - [任务 54][CLAIM] - Node CLAIM[ed5cdcf3-e3bf-4e34-80d5-7561ccdf86a7] close complete, cost 8 ms 
[INFO ] 2024-04-06 20:51:44.794 - [任务 54][test1] - Node test1[fe693b51-450e-4b35-a5e2-e112335f77b4] running status set to false 
[INFO ] 2024-04-06 20:51:44.794 - [任务 54][test1] - PDK connector node stopped: null 
[INFO ] 2024-04-06 20:51:44.795 - [任务 54][test1] - PDK connector node released: null 
[INFO ] 2024-04-06 20:51:44.797 - [任务 54][test1] - Node test1[fe693b51-450e-4b35-a5e2-e112335f77b4] schema data cleaned 
[INFO ] 2024-04-06 20:51:44.797 - [任务 54][test1] - Node test1[fe693b51-450e-4b35-a5e2-e112335f77b4] monitor closed 
[INFO ] 2024-04-06 20:51:45.008 - [任务 54][test1] - Node test1[fe693b51-450e-4b35-a5e2-e112335f77b4] close complete, cost 5 ms 
[INFO ] 2024-04-06 20:51:48.657 - [任务 54] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-06 20:51:48.692 - [任务 54] - Stop task milestones: 6611455130039a0eb6c16033(任务 54)  
[INFO ] 2024-04-06 20:51:48.694 - [任务 54] - Stopped task aspect(s) 
[INFO ] 2024-04-06 20:51:48.694 - [任务 54] - Snapshot order controller have been removed 
[INFO ] 2024-04-06 20:51:48.715 - [任务 54] - Remove memory task client succeed, task: 任务 54[6611455130039a0eb6c16033] 
[INFO ] 2024-04-06 20:51:48.717 - [任务 54] - Destroy memory task client cache succeed, task: 任务 54[6611455130039a0eb6c16033] 
