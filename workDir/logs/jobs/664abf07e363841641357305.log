[INFO ] 2024-05-20 03:10:44.012 - [任务 12] - Start task milestones: 664abf07e363841641357305(任务 12) 
[INFO ] 2024-05-20 03:10:44.012 - [任务 12] - Task initialization... 
[INFO ] 2024-05-20 03:10:44.012 - [任务 12] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-20 03:10:44.016 - [任务 12] - The engine receives 任务 12 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-20 03:10:44.020 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] start preload schema,table counts: 1 
[INFO ] 2024-05-20 03:10:44.023 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] start preload schema,table counts: 1 
[INFO ] 2024-05-20 03:10:44.038 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] preload schema finished, cost 27 ms 
[INFO ] 2024-05-20 03:10:44.039 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] preload schema finished, cost 30 ms 
[INFO ] 2024-05-20 03:10:44.797 - [任务 12][SourcePG] - Source node "SourcePG" read batch size: 100 
[INFO ] 2024-05-20 03:10:44.798 - [任务 12][SourcePG] - Source node "SourcePG" event queue capacity: 200 
[INFO ] 2024-05-20 03:10:44.848 - [任务 12][SourcePG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-20 03:10:44.848 - [任务 12][TargetCK] - Node(TargetCK) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-05-20 03:10:44.848 - [任务 12][TargetCK] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-20 03:10:44.926 - [任务 12][SourcePG] - new logical replication slot created, slotName:tapdata_cdc_3df18ae6_e380_4212_9634_78083ac966a8 
[INFO ] 2024-05-20 03:10:44.933 - [任务 12][SourcePG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-05-20 03:10:44.936 - [任务 12][SourcePG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-20 03:10:45.052 - [任务 12][SourcePG] - Initial sync started 
[INFO ] 2024-05-20 03:10:45.052 - [任务 12][SourcePG] - Starting batch read, table name: testtime1, offset: null 
[INFO ] 2024-05-20 03:10:45.058 - [任务 12][SourcePG] - Table testtime1 is going to be initial synced 
[INFO ] 2024-05-20 03:10:45.126 - [任务 12][SourcePG] - Table [testtime1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-20 03:10:45.141 - [任务 12][SourcePG] - Query table 'testtime1' counts: 11 
[INFO ] 2024-05-20 03:10:45.142 - [任务 12][SourcePG] - Initial sync completed 
[INFO ] 2024-05-20 03:10:45.142 - [任务 12][SourcePG] - Incremental sync starting... 
[INFO ] 2024-05-20 03:10:45.142 - [任务 12][SourcePG] - Initial sync completed 
[INFO ] 2024-05-20 03:10:45.179 - [任务 12][SourcePG] - Starting stream read, table list: [testtime1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-05-20 03:10:45.186 - [任务 12][SourcePG] - Using an existing logical replication slot, slotName:tapdata_cdc_3df18ae6_e380_4212_9634_78083ac966a8 
[INFO ] 2024-05-20 03:10:45.791 - [任务 12][SourcePG] - Connector PostgreSQL incremental start succeed, tables: [testtime1], data change syncing 
[INFO ] 2024-05-20 04:10:46.207 - [任务 12][TargetCK] - Clickhouse Optimize Table start, tables: ["testtime1"] 
[INFO ] 2024-05-20 04:10:46.409 - [任务 12][TargetCK] - Clickhouse Optimize Table end 
[INFO ] 2024-05-20 05:10:46.687 - [任务 12][TargetCK] - Clickhouse Optimize Table start, tables: ["testtime1"] 
[WARN ] 2024-05-20 05:11:16.775 - [任务 12][TargetCK] - Clickhouse Optimize Table failed 
[INFO ] 2024-05-20 06:11:16.842 - [任务 12][TargetCK] - Clickhouse Optimize Table start, tables: ["testtime1"] 
[INFO ] 2024-05-20 06:11:16.926 - [任务 12][TargetCK] - Clickhouse Optimize Table end 
[INFO ] 2024-05-20 07:11:17.469 - [任务 12][TargetCK] - Clickhouse Optimize Table start, tables: ["testtime1"] 
[INFO ] 2024-05-20 07:11:17.538 - [任务 12][TargetCK] - Clickhouse Optimize Table end 
[INFO ] 2024-05-20 08:11:18.676 - [任务 12][TargetCK] - Clickhouse Optimize Table start, tables: ["testtime1"] 
[INFO ] 2024-05-20 08:11:18.678 - [任务 12][TargetCK] - Clickhouse Optimize Table end 
[INFO ] 2024-05-20 09:11:18.938 - [任务 12][TargetCK] - Clickhouse Optimize Table start, tables: ["testtime1"] 
[INFO ] 2024-05-20 09:11:18.938 - [任务 12][TargetCK] - Clickhouse Optimize Table end 
[INFO ] 2024-05-20 18:03:26.565 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] running status set to false 
[INFO ] 2024-05-20 18:03:26.615 - [任务 12] - Start task milestones: 664abf07e363841641357305(任务 12) 
[INFO ] 2024-05-20 18:03:26.767 - [任务 12] - Task initialization... 
[INFO ] 2024-05-20 18:03:26.768 - [任务 12] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-20 18:03:27.172 - [任务 12] - The engine receives 任务 12 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-20 18:03:27.691 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] start preload schema,table counts: 1 
[INFO ] 2024-05-20 18:03:27.691 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] start preload schema,table counts: 1 
[INFO ] 2024-05-20 18:03:27.880 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] preload schema finished, cost 186 ms 
[INFO ] 2024-05-20 18:03:27.882 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] preload schema finished, cost 207 ms 
[INFO ] 2024-05-20 18:03:28.742 - [任务 12][TargetCK] - Node(TargetCK) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-05-20 18:03:28.764 - [任务 12][TargetCK] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-20 18:03:28.818 - [任务 12][SourcePG] - Source node "SourcePG" read batch size: 100 
[INFO ] 2024-05-20 18:03:28.818 - [任务 12][SourcePG] - Source node "SourcePG" event queue capacity: 200 
[INFO ] 2024-05-20 18:03:28.820 - [任务 12][SourcePG] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-20 18:03:28.833 - [任务 12][SourcePG] - batch offset found: {"testtime1":{"offset":{},"status":"RUNNING"}},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":374291177320,\"lsn_commit\":374291177104,\"lsn\":374291177320,\"txId\":9919070,\"ts_usec\":1716198227989245}"} 
[INFO ] 2024-05-20 18:03:28.833 - [任务 12][SourcePG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-20 18:03:29.054 - [任务 12][SourcePG] - Incremental sync starting... 
[INFO ] 2024-05-20 18:03:29.055 - [任务 12][SourcePG] - Initial sync completed 
[INFO ] 2024-05-20 18:03:29.068 - [任务 12][SourcePG] - Starting stream read, table list: [testtime1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":374291177320,\"lsn_commit\":374291177104,\"lsn\":374291177320,\"txId\":9919070,\"ts_usec\":1716198227989245}"} 
[INFO ] 2024-05-20 18:03:29.272 - [任务 12][SourcePG] - Using an existing logical replication slot, slotName:tapdata_cdc_3df18ae6_e380_4212_9634_78083ac966a8 
[INFO ] 2024-05-20 18:03:29.854 - [任务 12][SourcePG] - Connector PostgreSQL incremental start succeed, tables: [testtime1], data change syncing 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12] - Stop task milestones: 664abf07e363841641357305(任务 12)  
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] running status set to false 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][SourcePG] - PDK connector node stopped: HazelcastSourcePdkDataNode-b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][SourcePG] - PDK connector node released: HazelcastSourcePdkDataNode-b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] schema data cleaned 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] monitor closed 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][SourcePG] - Node SourcePG[b6cff595-a8cc-4dd6-8bf3-f1b1965efa8d] close complete, cost 105 ms 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] running status set to false 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][TargetCK] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-05-20 19:11:24.161 - [任务 12][TargetCK] - Clickhouse Optimize Table end 
[INFO ] 2024-05-20 19:11:24.162 - [任务 12][TargetCK] - PDK connector node stopped: HazelcastTargetPdkDataNode-15a8c6ca-ddb9-483c-87cf-9b3237395388 
[INFO ] 2024-05-20 19:11:24.162 - [任务 12][TargetCK] - PDK connector node released: HazelcastTargetPdkDataNode-15a8c6ca-ddb9-483c-87cf-9b3237395388 
[INFO ] 2024-05-20 19:11:24.162 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] schema data cleaned 
[INFO ] 2024-05-20 19:11:24.170 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] monitor closed 
[INFO ] 2024-05-20 19:11:24.376 - [任务 12][TargetCK] - Node TargetCK[15a8c6ca-ddb9-483c-87cf-9b3237395388] close complete, cost 171 ms 
[INFO ] 2024-05-20 19:11:24.484 - [任务 12] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-20 19:11:24.484 - [任务 12] - Stopped task aspect(s) 
[INFO ] 2024-05-20 19:11:24.484 - [任务 12] - Snapshot order controller have been removed 
[INFO ] 2024-05-20 19:11:24.536 - [任务 12] - Remove memory task client succeed, task: 任务 12[664abf07e363841641357305] 
[INFO ] 2024-05-20 19:11:24.536 - [任务 12] - Destroy memory task client cache succeed, task: 任务 12[664abf07e363841641357305] 
