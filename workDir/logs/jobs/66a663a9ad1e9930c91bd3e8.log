[INFO ] 2024-07-28 23:31:39.992 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 23:31:39.993 - [任务 1] - Start task milestones: 66a663a9ad1e9930c91bd3e8(任务 1) 
[INFO ] 2024-07-28 23:31:41.025 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 23:31:41.071 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 23:31:41.556 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] start preload schema,table counts: 1 
[INFO ] 2024-07-28 23:31:41.557 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] start preload schema,table counts: 1 
[INFO ] 2024-07-28 23:31:41.557 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 23:31:41.559 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 23:31:42.500 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 23:31:42.500 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 23:31:42.500 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 23:31:42.502 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 23:31:42.573 - [任务 1][TestPO] - Table "test.TestPO" exists, skip auto create table 
[INFO ] 2024-07-28 23:31:42.575 - [任务 1][TestPO] - The table TestPO has already exist. 
[INFO ] 2024-07-28 23:31:42.676 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722180702,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 23:31:42.676 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 23:31:42.683 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-28 23:31:42.683 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 23:31:42.753 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 23:31:42.754 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 23:31:42.756 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 23:31:42.756 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 23:31:42.758 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 23:31:42.758 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722180702,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 23:31:42.966 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 23:32:54.511 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] running status set to false 
[INFO ] 2024-07-28 23:32:54.552 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-03bb846f-fdd5-443c-aacc-cbde8feeb09f 
[INFO ] 2024-07-28 23:32:54.556 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-03bb846f-fdd5-443c-aacc-cbde8feeb09f 
[INFO ] 2024-07-28 23:32:54.556 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] schema data cleaned 
[INFO ] 2024-07-28 23:32:54.556 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] monitor closed 
[INFO ] 2024-07-28 23:32:54.565 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] close complete, cost 81 ms 
[INFO ] 2024-07-28 23:32:54.565 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] running status set to false 
[INFO ] 2024-07-28 23:32:54.604 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a 
[INFO ] 2024-07-28 23:32:54.604 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a 
[INFO ] 2024-07-28 23:32:54.605 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] schema data cleaned 
[INFO ] 2024-07-28 23:32:54.605 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] monitor closed 
[INFO ] 2024-07-28 23:32:54.811 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] close complete, cost 44 ms 
[INFO ] 2024-07-28 23:32:56.206 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 23:32:56.206 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4590a640 
[INFO ] 2024-07-28 23:32:56.341 - [任务 1] - Stop task milestones: 66a663a9ad1e9930c91bd3e8(任务 1)  
[INFO ] 2024-07-28 23:32:56.341 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 23:32:56.341 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 23:32:56.400 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a663a9ad1e9930c91bd3e8] 
[INFO ] 2024-07-28 23:32:56.600 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a663a9ad1e9930c91bd3e8] 
[INFO ] 2024-07-28 23:36:12.251 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 23:36:12.379 - [任务 1] - Start task milestones: 66a663a9ad1e9930c91bd3e8(任务 1) 
[INFO ] 2024-07-28 23:36:12.461 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 23:36:12.555 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 23:36:12.678 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] start preload schema,table counts: 1 
[INFO ] 2024-07-28 23:36:12.681 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] start preload schema,table counts: 1 
[INFO ] 2024-07-28 23:36:12.681 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 23:36:12.681 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-28 23:36:13.507 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 23:36:13.521 - [任务 1][TestPO] - Table "test.TestPO" exists, skip auto create table 
[INFO ] 2024-07-28 23:36:13.522 - [任务 1][TestPO] - The table TestPO has already exist. 
[INFO ] 2024-07-28 23:36:13.637 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 23:36:13.638 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 23:36:13.638 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 23:36:13.747 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722180973,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 23:36:13.856 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 23:36:13.861 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-28 23:36:13.861 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 23:36:13.944 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 23:36:13.944 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 23:36:13.948 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 23:36:13.948 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 23:36:13.955 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 23:36:13.956 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722180973,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 23:36:14.157 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 23:37:04.803 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] running status set to false 
[INFO ] 2024-07-28 23:37:04.815 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-03bb846f-fdd5-443c-aacc-cbde8feeb09f 
[INFO ] 2024-07-28 23:37:04.815 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-03bb846f-fdd5-443c-aacc-cbde8feeb09f 
[INFO ] 2024-07-28 23:37:04.817 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] schema data cleaned 
[INFO ] 2024-07-28 23:37:04.817 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] monitor closed 
[INFO ] 2024-07-28 23:37:04.825 - [任务 1][TESTPO] - Node TESTPO[03bb846f-fdd5-443c-aacc-cbde8feeb09f] close complete, cost 39 ms 
[INFO ] 2024-07-28 23:37:04.825 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] running status set to false 
[INFO ] 2024-07-28 23:37:04.838 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a 
[INFO ] 2024-07-28 23:37:04.838 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a 
[INFO ] 2024-07-28 23:37:04.839 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] schema data cleaned 
[INFO ] 2024-07-28 23:37:04.841 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] monitor closed 
[INFO ] 2024-07-28 23:37:04.841 - [任务 1][TestPO] - Node TestPO[2adb5290-aa4e-4b2a-8711-9fc36c9dbe0a] close complete, cost 19 ms 
[INFO ] 2024-07-28 23:37:05.249 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-28 23:37:07.065 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 23:37:07.065 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ae4ad37 
[INFO ] 2024-07-28 23:37:07.217 - [任务 1] - Stop task milestones: 66a663a9ad1e9930c91bd3e8(任务 1)  
[INFO ] 2024-07-28 23:37:07.217 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 23:37:07.218 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 23:37:07.242 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a663a9ad1e9930c91bd3e8] 
[INFO ] 2024-07-28 23:37:07.244 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a663a9ad1e9930c91bd3e8] 
