[INFO ] 2024-07-05 00:28:36.033 - [任务 48] - Task initialization... 
[INFO ] 2024-07-05 00:28:36.047 - [任务 48] - Start task milestones: 6686cd8e4ef26b0b431bdb06(任务 48) 
[INFO ] 2024-07-05 00:28:36.335 - [任务 48] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 00:28:36.422 - [任务 48] - The engine receives 任务 48 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 00:28:36.510 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] start preload schema,table counts: 1 
[INFO ] 2024-07-05 00:28:36.510 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 00:28:36.553 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] start preload schema,table counts: 1 
[INFO ] 2024-07-05 00:28:36.556 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 00:28:37.511 - [任务 48][CAR_CLAIM_009] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-05 00:28:37.755 - [任务 48][CAR_CLAIM_009] - Source node "CAR_CLAIM_009" read batch size: 100 
[INFO ] 2024-07-05 00:28:37.755 - [任务 48][CAR_CLAIM_009] - Source node "CAR_CLAIM_009" event queue capacity: 200 
[INFO ] 2024-07-05 00:28:37.755 - [任务 48][CAR_CLAIM_009] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 00:28:38.134 - [任务 48][CAR_CLAIM_009] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68575640,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 00:28:38.134 - [任务 48][CAR_CLAIM_009] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 00:28:38.178 - [任务 48][CAR_CLAIM_009] - Initial sync started 
[INFO ] 2024-07-05 00:28:38.179 - [任务 48][CAR_CLAIM_009] - Starting batch read, table name: CAR_CLAIM_009, offset: null 
[INFO ] 2024-07-05 00:28:38.179 - [任务 48][CAR_CLAIM_009] - Table CAR_CLAIM_009 is going to be initial synced 
[INFO ] 2024-07-05 00:28:38.340 - [任务 48][CAR_CLAIM_009] - Query table 'CAR_CLAIM_009' counts: 1080 
[INFO ] 2024-07-05 00:28:38.342 - [任务 48][CAR_CLAIM_009] - Table [CAR_CLAIM_009] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-05 00:28:38.342 - [任务 48][CAR_CLAIM_009] - Initial sync completed 
[INFO ] 2024-07-05 00:28:38.342 - [任务 48][CAR_CLAIM_009] - Incremental sync starting... 
[INFO ] 2024-07-05 00:28:38.342 - [任务 48][CAR_CLAIM_009] - Initial sync completed 
[INFO ] 2024-07-05 00:28:38.346 - [任务 48][CAR_CLAIM_009] - Starting stream read, table list: [CAR_CLAIM_009, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":68575640,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 00:28:38.747 - [任务 48][CAR_CLAIM_009] - total start mining scn: 68575640 
[INFO ] 2024-07-05 00:28:39.971 - [任务 48][CAR_CLAIM_009] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 00:29:25.772 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] running status set to false 
[INFO ] 2024-07-05 00:29:25.934 - [任务 48][CAR_CLAIM_009] - Log Miner is shutting down... 
[INFO ] 2024-07-05 00:29:25.934 - [任务 48][CAR_CLAIM_009] - Log Miner has been closed! 
[INFO ] 2024-07-05 00:29:25.944 - [任务 48][CAR_CLAIM_009] - Incremental sync completed 
[INFO ] 2024-07-05 00:29:25.945 - [任务 48][CAR_CLAIM_009] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 
[ERROR] 2024-07-05 00:29:26.003 - [任务 48][CAR_CLAIM_009] - Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68575931 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('CAR_CLAIM_009','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 19 more

[INFO ] 2024-07-05 00:29:26.004 - [任务 48][CAR_CLAIM_009] - PDK connector node stopped: HazelcastSourcePdkDataNode-7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9 
[INFO ] 2024-07-05 00:29:26.004 - [任务 48][CAR_CLAIM_009] - PDK connector node released: HazelcastSourcePdkDataNode-7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9 
[INFO ] 2024-07-05 00:29:26.004 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] schema data cleaned 
[INFO ] 2024-07-05 00:29:26.004 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] monitor closed 
[INFO ] 2024-07-05 00:29:26.004 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] close complete, cost 259 ms 
[INFO ] 2024-07-05 00:29:26.004 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] running status set to false 
[INFO ] 2024-07-05 00:29:26.027 - [任务 48][CAR_CLAIM_009] - PDK connector node stopped: HazelcastTargetPdkDataNode-8e6fd327-92b8-4e61-a0c8-e6a22ee169f8 
[INFO ] 2024-07-05 00:29:26.027 - [任务 48][CAR_CLAIM_009] - PDK connector node released: HazelcastTargetPdkDataNode-8e6fd327-92b8-4e61-a0c8-e6a22ee169f8 
[INFO ] 2024-07-05 00:29:26.027 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] schema data cleaned 
[INFO ] 2024-07-05 00:29:26.027 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] monitor closed 
[INFO ] 2024-07-05 00:29:26.231 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] close complete, cost 22 ms 
[INFO ] 2024-07-05 00:29:26.358 - [任务 48] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 00:29:26.358 - [任务 48] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@64b9fe91 
[INFO ] 2024-07-05 00:29:26.486 - [任务 48] - Stop task milestones: 6686cd8e4ef26b0b431bdb06(任务 48)  
[INFO ] 2024-07-05 00:29:26.489 - [任务 48] - Stopped task aspect(s) 
[INFO ] 2024-07-05 00:29:26.489 - [任务 48] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 00:29:26.509 - [任务 48] - Remove memory task client succeed, task: 任务 48[6686cd8e4ef26b0b431bdb06] 
[INFO ] 2024-07-05 00:29:26.513 - [任务 48] - Destroy memory task client cache succeed, task: 任务 48[6686cd8e4ef26b0b431bdb06] 
[INFO ] 2024-07-05 00:32:33.048 - [任务 48] - Task initialization... 
[INFO ] 2024-07-05 00:32:33.049 - [任务 48] - Start task milestones: 6686cd8e4ef26b0b431bdb06(任务 48) 
[INFO ] 2024-07-05 00:32:33.232 - [任务 48] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 00:32:33.287 - [任务 48] - The engine receives 任务 48 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 00:32:33.393 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] start preload schema,table counts: 1 
[INFO ] 2024-07-05 00:32:33.393 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] start preload schema,table counts: 1 
[INFO ] 2024-07-05 00:32:33.393 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 00:32:33.394 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 00:32:34.583 - [任务 48][CAR_CLAIM_009] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-05 00:32:34.587 - [任务 48][CAR_CLAIM_009] - Table "test.CAR_CLAIM_009" exists, skip auto create table 
[INFO ] 2024-07-05 00:32:34.587 - [任务 48][CAR_CLAIM_009] - The table CAR_CLAIM_009 has already exist. 
[INFO ] 2024-07-05 00:32:34.885 - [任务 48][CAR_CLAIM_009] - Source node "CAR_CLAIM_009" read batch size: 100 
[INFO ] 2024-07-05 00:32:34.894 - [任务 48][CAR_CLAIM_009] - Source node "CAR_CLAIM_009" event queue capacity: 200 
[INFO ] 2024-07-05 00:32:34.894 - [任务 48][CAR_CLAIM_009] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 00:32:35.260 - [任务 48][CAR_CLAIM_009] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68576930,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 00:32:35.261 - [任务 48][CAR_CLAIM_009] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 00:32:35.336 - [任务 48][CAR_CLAIM_009] - Initial sync started 
[INFO ] 2024-07-05 00:32:35.338 - [任务 48][CAR_CLAIM_009] - Starting batch read, table name: CAR_CLAIM_009, offset: null 
[INFO ] 2024-07-05 00:32:35.338 - [任务 48][CAR_CLAIM_009] - Table CAR_CLAIM_009 is going to be initial synced 
[INFO ] 2024-07-05 00:32:35.415 - [任务 48][CAR_CLAIM_009] - Query table 'CAR_CLAIM_009' counts: 1080 
[INFO ] 2024-07-05 00:32:35.466 - [任务 48][CAR_CLAIM_009] - Table [CAR_CLAIM_009] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-05 00:32:35.467 - [任务 48][CAR_CLAIM_009] - Initial sync completed 
[INFO ] 2024-07-05 00:32:35.467 - [任务 48][CAR_CLAIM_009] - Incremental sync starting... 
[INFO ] 2024-07-05 00:32:35.467 - [任务 48][CAR_CLAIM_009] - Initial sync completed 
[INFO ] 2024-07-05 00:32:35.525 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-05 00:32:35.525 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceOracle enable share cdc: true 
[INFO ] 2024-07-05 00:32:35.526 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 48 enable share cdc: true 
[INFO ] 2024-07-05 00:32:35.536 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceOracle的共享挖掘任务 
[INFO ] 2024-07-05 00:32:35.559 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-05 00:32:35.559 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7755, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_CLAIM_009, version=v2, tableName=CAR_CLAIM_009, externalStorageTableName=ExternalStorage_SHARE_CDC_-1275224446, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 00:32:35.577 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 00:32:35.577 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 00:32:35.590 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 00:32:35.592 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-05 00:32:35.592 - [任务 48][CAR_CLAIM_009] - Init share cdc reader completed 
[INFO ] 2024-07-05 00:32:35.592 - [任务 48][CAR_CLAIM_009] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-05 00:32:35.592 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-05 00:32:35.592 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-05 00:32:35.596 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7755, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_CLAIM_009, version=v2, tableName=CAR_CLAIM_009, externalStorageTableName=ExternalStorage_SHARE_CDC_-1275224446, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 00:32:35.596 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 00:32:35.597 - [任务 48][CAR_CLAIM_009] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009_任务 48, external storage name: ExternalStorage_SHARE_CDC_-1275224446 
[INFO ] 2024-07-05 00:32:35.597 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR_CLAIM_009] 
[INFO ] 2024-07-05 00:32:35.600 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR_CLAIM_009) by timestamp(2024-07-04T16:32:34.880Z): 1 
[INFO ] 2024-07-05 00:32:35.600 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read 'CAR_CLAIM_009' log, sequence: 1 
[INFO ] 2024-07-05 00:32:35.601 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 00:32:35.609 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Find by CAR_CLAIM_009 filter: {sequence=1} 
[INFO ] 2024-07-05 00:32:35.613 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 00:32:35.613 - [任务 48][CAR_CLAIM_009] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table_任务 48, external storage name: ExternalStorage_SHARE_CDC_-1986184445 
[INFO ] 2024-07-05 00:32:35.616 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-05 00:32:35.616 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Find sequence in construct(_tapdata_heartbeat_table) by timestamp(2024-07-04T16:32:34.880Z): 1 
[INFO ] 2024-07-05 00:32:35.616 - [任务 48][CAR_CLAIM_009] - Connector Oracle incremental start succeed, tables: [CAR_CLAIM_009, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-05 00:32:35.616 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 1 
[INFO ] 2024-07-05 00:32:35.719 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=1} 
[INFO ] 2024-07-05 00:32:39.523 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, tableNamespaces=[C##TAPDATA, _tapdata_heartbeat_table], timestamp=1720110756000, date=Fri Jul 05 00:32:36 CST 2024, before=Document{{id=6686ccd14ef26b0b431bdad5, ts=Fri Jul 05 00:32:33 CST 2024}}, after=Document{{id=6686ccd14ef26b0b431bdad5, ts=Fri Jul 05 00:32:34 CST 2024}}, op=u, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5vcmFjbGUuY2RjLm9mZnNldC5PcmFjbGVP
ZmZzZXQUJN7C0aaHzAIAB0kAA2Zub0wABmhleFNjbnQAEkxqYXZhL2xhbmcvU3RyaW5nO0wAB2xh
c3RTY250ABBMamF2YS9sYW5nL0xvbmc7TAALb2Zmc2V0VmFsdWVxAH4AAkwACnBlbmRpbmdTY25x
AH4AAkwACnNvcnRTdHJpbmdxAH4AAUwACXRpbWVzdGFtcHEAfgACeHAAAAAAcHNyAA5qYXZhLmxh
bmcuTG9uZzuL5JDMjyPfAgABSgAFdmFsdWV4cgAQamF2YS5sYW5nLk51bWJlcoaslR0LlOCLAgAA
eHAAAAAABBZmr3BzcQB+AAQAAAAABBZm0XBzcQB+AAQAAAGQfpcwoA==
, type=DATA, connectionId=6686ccd14ef26b0b431bdad5, isReplaceEvent=false, _ts=1720110758}} 
[INFO ] 2024-07-05 02:04:02.404 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] running status set to false 
[INFO ] 2024-07-05 11:27:00.458 - [任务 48][CAR_CLAIM_009] - Incremental sync completed 
[INFO ] 2024-07-05 11:27:00.556 - [任务 48] - Task initialization... 
[INFO ] 2024-07-05 11:27:00.583 - [任务 48] - Start task milestones: 6686cd8e4ef26b0b431bdb06(任务 48) 
[INFO ] 2024-07-05 11:27:02.442 - [任务 48] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 11:27:02.502 - [任务 48] - The engine receives 任务 48 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 11:27:03.358 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] start preload schema,table counts: 1 
[INFO ] 2024-07-05 11:27:03.359 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 11:27:03.382 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] start preload schema,table counts: 1 
[INFO ] 2024-07-05 11:27:03.383 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 11:27:05.788 - [任务 48][CAR_CLAIM_009] - Source node "CAR_CLAIM_009" read batch size: 100 
[INFO ] 2024-07-05 11:27:05.789 - [任务 48][CAR_CLAIM_009] - Source node "CAR_CLAIM_009" event queue capacity: 200 
[INFO ] 2024-07-05 11:27:05.799 - [任务 48][CAR_CLAIM_009] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-05 11:27:05.801 - [任务 48][CAR_CLAIM_009] - batch offset found: {"CAR_CLAIM_009":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"CAR_CLAIM_009":1,"_tapdata_heartbeat_table":5449},"streamOffset":{"sortString":null,"offsetValue":null,"lastScn":68614094,"pendingScn":68614095,"timestamp":1720116228000,"hexScn":null,"fno":0}} 
[INFO ] 2024-07-05 11:27:05.801 - [任务 48][CAR_CLAIM_009] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 11:27:09.221 - [任务 48][CAR_CLAIM_009] - Incremental sync starting... 
[INFO ] 2024-07-05 11:27:09.221 - [任务 48][CAR_CLAIM_009] - Initial sync completed 
[INFO ] 2024-07-05 11:27:09.441 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-05 11:27:09.443 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceOracle enable share cdc: true 
[INFO ] 2024-07-05 11:27:09.451 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 48 enable share cdc: true 
[INFO ] 2024-07-05 11:27:09.503 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceOracle的共享挖掘任务 
[INFO ] 2024-07-05 11:27:09.521 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-05 11:27:09.530 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7755, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_CLAIM_009, version=v2, tableName=CAR_CLAIM_009, externalStorageTableName=ExternalStorage_SHARE_CDC_-1275224446, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 11:27:09.540 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 11:27:09.548 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 11:27:09.555 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 5459 
[INFO ] 2024-07-05 11:27:09.556 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-05 11:27:09.557 - [任务 48][CAR_CLAIM_009] - Init share cdc reader completed 
[INFO ] 2024-07-05 11:27:09.558 - [任务 48][CAR_CLAIM_009] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-05 11:27:09.559 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-05 11:27:09.559 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-05 11:27:09.567 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7755, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_CLAIM_009, version=v2, tableName=CAR_CLAIM_009, externalStorageTableName=ExternalStorage_SHARE_CDC_-1275224446, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 11:27:09.584 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 11:27:09.587 - [任务 48][CAR_CLAIM_009] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009_任务 48, external storage name: ExternalStorage_SHARE_CDC_-1275224446 
[INFO ] 2024-07-05 11:27:09.589 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR_CLAIM_009] 
[INFO ] 2024-07-05 11:27:09.604 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read 'CAR_CLAIM_009' log, sequence: 1 
[INFO ] 2024-07-05 11:27:09.606 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Find by CAR_CLAIM_009 filter: {sequence=1} 
[INFO ] 2024-07-05 11:27:09.622 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 11:27:09.625 - [任务 48][CAR_CLAIM_009] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table_任务 48', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 5459 
[INFO ] 2024-07-05 11:27:09.626 - [任务 48][CAR_CLAIM_009] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table_任务 48, external storage name: ExternalStorage_SHARE_CDC_-1986184445 
[INFO ] 2024-07-05 11:27:10.189 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-05 11:27:10.273 - [任务 48][CAR_CLAIM_009] - Connector Oracle incremental start succeed, tables: [CAR_CLAIM_009, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-05 11:27:10.274 - [任务 48][CAR_CLAIM_009] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-05 11:27:10.274 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 5449 
[INFO ] 2024-07-05 11:27:10.429 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=5449} 
[INFO ] 2024-07-05 11:27:10.555 - [任务 48][CAR_CLAIM_009] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, tableNamespaces=[C##TAPDATA, _tapdata_heartbeat_table], timestamp=1720116229000, date=Fri Jul 05 02:03:49 CST 2024, before=Document{{id=6686ccd14ef26b0b431bdad5, ts=Fri Jul 05 02:03:47 CST 2024}}, after=Document{{id=6686ccd14ef26b0b431bdad5, ts=Fri Jul 05 02:03:48 CST 2024}}, op=u, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5vcmFjbGUuY2RjLm9mZnNldC5PcmFjbGVP
ZmZzZXQUJN7C0aaHzAIAB0kAA2Zub0wABmhleFNjbnQAEkxqYXZhL2xhbmcvU3RyaW5nO0wAB2xh
c3RTY250ABBMamF2YS9sYW5nL0xvbmc7TAALb2Zmc2V0VmFsdWVxAH4AAkwACnBlbmRpbmdTY25x
AH4AAkwACnNvcnRTdHJpbmdxAH4AAUwACXRpbWVzdGFtcHEAfgACeHAAAAAAcHNyAA5qYXZhLmxh
bmcuTG9uZzuL5JDMjyPfAgABSgAFdmFsdWV4cgAQamF2YS5sYW5nLk51bWJlcoaslR0LlOCLAgAA
eHAAAAAABBb303BzcQB+AAQAAAAABBb34nBzcQB+AAQAAAGQfuqziA==
, type=DATA, connectionId=6686ccd14ef26b0b431bdad5, isReplaceEvent=false, _ts=1720116233}} 
[INFO ] 2024-07-05 12:08:44.645 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] running status set to false 
[INFO ] 2024-07-05 12:08:44.697 - [任务 48][CAR_CLAIM_009] - Incremental sync completed 
[INFO ] 2024-07-05 12:08:44.697 - [任务 48][CAR_CLAIM_009] - PDK connector node stopped: HazelcastSourcePdkDataNode-7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9 
[INFO ] 2024-07-05 12:08:44.698 - [任务 48][CAR_CLAIM_009] - PDK connector node released: HazelcastSourcePdkDataNode-7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9 
[INFO ] 2024-07-05 12:08:44.698 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] schema data cleaned 
[INFO ] 2024-07-05 12:08:44.700 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] monitor closed 
[INFO ] 2024-07-05 12:08:44.700 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[7f52c371-39e6-4fd7-9f6f-5f8b6d5a41b9] close complete, cost 90 ms 
[INFO ] 2024-07-05 12:08:44.719 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] running status set to false 
[INFO ] 2024-07-05 12:08:44.719 - [任务 48][CAR_CLAIM_009] - PDK connector node stopped: HazelcastTargetPdkDataNode-8e6fd327-92b8-4e61-a0c8-e6a22ee169f8 
[INFO ] 2024-07-05 12:08:44.719 - [任务 48][CAR_CLAIM_009] - PDK connector node released: HazelcastTargetPdkDataNode-8e6fd327-92b8-4e61-a0c8-e6a22ee169f8 
[INFO ] 2024-07-05 12:08:44.719 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] schema data cleaned 
[INFO ] 2024-07-05 12:08:44.720 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] monitor closed 
[INFO ] 2024-07-05 12:08:44.720 - [任务 48][CAR_CLAIM_009] - Node CAR_CLAIM_009[8e6fd327-92b8-4e61-a0c8-e6a22ee169f8] close complete, cost 19 ms 
[INFO ] 2024-07-05 12:08:47.341 - [任务 48] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:08:47.341 - [任务 48] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5f4933be 
[INFO ] 2024-07-05 12:08:47.341 - [任务 48] - Stop task milestones: 6686cd8e4ef26b0b431bdb06(任务 48)  
[INFO ] 2024-07-05 12:08:47.455 - [任务 48] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:08:47.455 - [任务 48] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:08:47.482 - [任务 48] - Remove memory task client succeed, task: 任务 48[6686cd8e4ef26b0b431bdb06] 
[INFO ] 2024-07-05 12:08:47.484 - [任务 48] - Destroy memory task client cache succeed, task: 任务 48[6686cd8e4ef26b0b431bdb06] 
