[INFO ] 2024-07-26 19:54:50.807 - [任务 2] - Start task milestones: 66a38e59183a6022d03bac8b(任务 2) 
[INFO ] 2024-07-26 19:54:50.980 - [任务 2] - Task initialization... 
[INFO ] 2024-07-26 19:54:51.100 - [任务 2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:54:51.105 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:54:51.214 - [任务 2][POLICY] - Node POLICY[9a6e2aad-2de5-4e0f-83ae-b0b44d08e8bb] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:54:51.215 - [任务 2][POLICY] - Node POLICY[9a6e2aad-2de5-4e0f-83ae-b0b44d08e8bb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:54:51.262 - [任务 2][Tes2] - Node Tes2[ffb238a8-76a2-48f8-b01c-42c2ba7795b0] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:54:51.264 - [任务 2][Tes2] - Node Tes2[ffb238a8-76a2-48f8-b01c-42c2ba7795b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:54:52.479 - [任务 2][Tes2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 19:54:52.665 - [任务 2][POLICY] - Source node "POLICY" read batch size: 500 
[INFO ] 2024-07-26 19:54:52.666 - [任务 2][POLICY] - Source node "POLICY" event queue capacity: 1000 
[INFO ] 2024-07-26 19:54:52.666 - [任务 2][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:54:52.850 - [任务 2][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721994892,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:54:52.850 - [任务 2][POLICY] - Initial sync started 
[INFO ] 2024-07-26 19:54:52.851 - [任务 2][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-26 19:54:52.857 - [任务 2][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-26 19:54:52.901 - [任务 2][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-26 19:54:52.902 - [任务 2][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:54:52.902 - [任务 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-26 19:54:52.902 - [任务 2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-26 19:54:52.902 - [任务 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-26 19:54:52.903 - [任务 2][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721994892,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:54:53.114 - [任务 2][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 20:00:19.374 - [任务 2][POLICY] - Node POLICY[9a6e2aad-2de5-4e0f-83ae-b0b44d08e8bb] running status set to false 
