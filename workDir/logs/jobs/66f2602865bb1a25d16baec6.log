[INFO ] 2024-10-09 18:14:02.374 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:14:02.395 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:14:05.907 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-3d50efac-7ff2-40d8-b10a-5795506a54e8 complete, cost 2422ms 
[INFO ] 2024-10-09 18:14:06.579 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:14:06.785 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:14:06.876 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:14:06.921 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:14:06.922 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:14:06.930 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:14:06.931 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:14:06.932 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:14:06.932 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:14:07.184 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:14:07.266 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:14:07.267 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:14:07.267 - [任务 3 - Copy][FDM_Customer_360_V1] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-09 18:14:07.483 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {"FDM_Customer_360_V1":{"batch_read_connector_status":"RUNNING"}},stream offset not found. 
[INFO ] 2024-10-09 18:14:07.515 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:14:07.536 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:14:07.537 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:14:07.742 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:14:08.093 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 493.04KB, row count: 20, single row data size: 24.65KB 
[INFO ] 2024-10-09 18:14:08.294 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:14:10.245 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:14:10.730 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:14:10.734 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:14:12.615 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:14:13.998 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:14:14.809 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:14:15.615 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:14:16.216 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:14:16.978 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:14:17.792 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:14:18.360 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:14:19.151 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:14:19.753 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:14:20.475 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:14:20.475 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:14:33.485 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:14:33.494 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:14:42.179 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:14:42.197 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:14:42.216 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:14:42.269 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-67afe10f-f332-496b-9b94-d9d5a265f603 
[INFO ] 2024-10-09 18:14:42.270 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:14:42.270 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:14:42.281 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-67afe10f-f332-496b-9b94-d9d5a265f603 
[INFO ] 2024-10-09 18:14:42.283 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:14:42.283 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:14:42.283 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:14:42.283 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:14:42.283 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:14:42.286 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:14:42.286 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-3390ddbe-de3b-4ed9-873d-f22564d6a27f 
[INFO ] 2024-10-09 18:14:42.287 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:14:42.287 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-3390ddbe-de3b-4ed9-873d-f22564d6a27f 
[INFO ] 2024-10-09 18:14:42.303 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:14:42.304 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 132 ms 
[INFO ] 2024-10-09 18:14:42.313 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:14:42.314 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:14:42.314 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 158 ms 
[INFO ] 2024-10-09 18:14:42.314 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 151 ms 
[INFO ] 2024-10-09 18:14:45.077 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:14:45.079 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@23a221f0 
[INFO ] 2024-10-09 18:14:45.216 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:14:45.217 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:14:45.217 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:14:45.275 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:14:45.276 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:15:02.976 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:15:02.977 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:15:04.109 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-71309032-41fb-4176-80df-bd226a41714b complete, cost 795ms 
[INFO ] 2024-10-09 18:15:04.479 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:15:04.646 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:15:04.654 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:15:04.656 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:15:04.656 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:15:04.656 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:15:04.656 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:15:04.656 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:15:04.656 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:15:04.985 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:15:04.988 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:15:04.988 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:15:04.989 - [任务 3 - Copy][FDM_Customer_360_V1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-09 18:15:04.989 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-09 18:15:05.237 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:15:05.245 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:15:05.249 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:15:05.450 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:15:05.667 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 502.81KB, row count: 20, single row data size: 25.14KB 
[INFO ] 2024-10-09 18:15:05.668 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:15:08.100 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:15:08.430 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:15:08.433 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:15:09.727 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:15:11.343 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:15:11.873 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:15:13.087 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:15:13.530 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:15:14.123 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:15:14.934 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:15:15.316 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:15:15.870 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:15:16.480 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:15:16.982 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:15:17.184 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:15:30.063 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:15:30.072 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:15:37.741 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:15:37.753 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:15:37.753 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:15:37.767 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-0e77d18d-393c-418c-b785-5b872c35479d 
[INFO ] 2024-10-09 18:15:37.775 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-0e77d18d-393c-418c-b785-5b872c35479d 
[INFO ] 2024-10-09 18:15:37.779 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:15:37.788 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:15:37.790 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-8c25cc38-6398-4d22-84aa-dac4c996f3eb 
[INFO ] 2024-10-09 18:15:37.790 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:15:37.790 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-8c25cc38-6398-4d22-84aa-dac4c996f3eb 
[INFO ] 2024-10-09 18:15:37.791 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:15:37.791 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:15:37.794 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:15:37.794 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:15:37.796 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:15:37.796 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:15:37.796 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 89 ms 
[INFO ] 2024-10-09 18:15:37.797 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:15:37.797 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:15:37.798 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:15:37.799 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 73 ms 
[INFO ] 2024-10-09 18:15:37.799 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 80 ms 
[INFO ] 2024-10-09 18:15:40.356 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:15:40.360 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@56368507 
[INFO ] 2024-10-09 18:15:40.488 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:15:40.488 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:15:40.503 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:15:40.503 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:15:40.504 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:16:03.351 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:16:03.352 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:16:04.537 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-941bf6c9-ab55-4818-a7cd-67cbb6a0ffd6 complete, cost 875ms 
[INFO ] 2024-10-09 18:16:04.953 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:16:05.071 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:16:05.071 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:16:05.071 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:16:05.071 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:16:05.071 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:16:05.071 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:16:05.072 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:16:05.277 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:16:05.374 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:16:05.374 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:16:05.374 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:16:05.374 - [任务 3 - Copy][FDM_Customer_360_V1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-09 18:16:05.374 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-09 18:16:05.734 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:16:05.741 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:16:05.741 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:16:05.946 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:16:06.163 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 485.40KB, row count: 20, single row data size: 24.27KB 
[INFO ] 2024-10-09 18:16:06.163 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:16:08.387 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:16:08.831 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:16:08.831 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:16:10.300 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:16:11.930 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:16:12.667 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:16:13.656 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:16:14.248 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:16:14.906 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:16:15.714 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:16:16.258 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:16:16.871 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:16:17.236 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:16:17.988 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:16:17.989 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:16:30.732 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:16:30.736 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:16:37.905 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:16:37.905 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:16:37.914 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:16:37.933 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-daf5f04c-febb-44e7-b582-6a43cff3b069 
[INFO ] 2024-10-09 18:16:37.936 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-daf5f04c-febb-44e7-b582-6a43cff3b069 
[INFO ] 2024-10-09 18:16:37.937 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:16:37.943 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-80c958c0-8434-4cab-b4cc-7e79e60c0f8d 
[INFO ] 2024-10-09 18:16:37.943 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:16:37.943 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:16:37.944 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-80c958c0-8434-4cab-b4cc-7e79e60c0f8d 
[INFO ] 2024-10-09 18:16:37.944 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:16:37.944 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:16:37.944 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:16:37.944 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:16:37.944 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:16:37.944 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:16:37.948 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:16:37.948 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:16:37.948 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 44 ms 
[INFO ] 2024-10-09 18:16:37.948 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:16:37.948 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 37 ms 
[INFO ] 2024-10-09 18:16:37.949 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 44 ms 
[INFO ] 2024-10-09 18:16:40.601 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:16:40.605 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@55815f68 
[INFO ] 2024-10-09 18:16:40.736 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:16:40.736 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:16:40.736 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:16:40.748 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:16:40.952 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:17:03.558 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:17:03.559 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:17:04.742 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-56e29ebc-460e-41bb-94bf-e19d4455e0c7 complete, cost 851ms 
[INFO ] 2024-10-09 18:17:05.218 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:17:05.218 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:17:05.328 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:17:05.328 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:17:05.328 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:17:05.328 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 1 ms 
[INFO ] 2024-10-09 18:17:05.328 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 1 ms 
[INFO ] 2024-10-09 18:17:05.328 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:17:05.536 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:17:05.647 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:17:05.647 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:17:05.648 - [任务 3 - Copy][FDM_Customer_360_V1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-09 18:17:05.704 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-09 18:17:05.704 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:17:05.798 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:17:05.798 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:17:05.803 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:17:06.005 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:17:06.286 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 509.63KB, row count: 20, single row data size: 25.48KB 
[INFO ] 2024-10-09 18:17:06.288 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:17:08.715 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:17:09.223 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:17:09.424 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:17:10.845 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:17:12.466 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:17:12.995 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:17:13.909 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:17:14.719 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:17:15.171 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:17:15.845 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:17:16.659 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:17:17.275 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:17:17.526 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:17:18.586 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:17:18.586 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:17:31.201 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:17:31.204 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:17:38.837 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:17:38.839 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:17:38.878 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:17:38.880 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-86abb1a2-59f8-4ff7-9653-6634682363c5 
[INFO ] 2024-10-09 18:17:38.880 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-86abb1a2-59f8-4ff7-9653-6634682363c5 
[INFO ] 2024-10-09 18:17:38.880 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:17:38.897 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-dff6baeb-dc13-4b25-9a45-a6ccfdafb24c 
[INFO ] 2024-10-09 18:17:38.897 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-dff6baeb-dc13-4b25-9a45-a6ccfdafb24c 
[INFO ] 2024-10-09 18:17:38.897 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:17:38.898 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:17:38.898 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:17:38.899 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:17:38.899 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:17:38.909 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 65 ms 
[INFO ] 2024-10-09 18:17:38.909 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:17:38.909 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:17:38.926 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 74 ms 
[INFO ] 2024-10-09 18:17:38.926 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:17:38.927 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:17:38.927 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:17:38.928 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:17:38.928 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 85 ms 
[INFO ] 2024-10-09 18:17:40.898 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:17:40.899 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@318b6b88 
[INFO ] 2024-10-09 18:17:41.030 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:17:41.030 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:17:41.048 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:17:41.048 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:17:41.256 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:18:03.803 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:18:03.804 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:18:04.835 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-28c48290-742b-4fc2-b829-bb0f77b36915 complete, cost 758ms 
[INFO ] 2024-10-09 18:18:05.220 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:18:05.255 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:18:05.371 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:18:05.371 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:18:05.371 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:18:05.371 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:18:05.371 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:18:05.371 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 1 ms 
[INFO ] 2024-10-09 18:18:05.372 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:18:05.651 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:18:05.651 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:18:05.652 - [任务 3 - Copy][FDM_Customer_360_V1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-09 18:18:05.652 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-09 18:18:05.699 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:18:05.854 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:18:05.857 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:18:05.975 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:18:05.976 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:18:06.388 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 496.03KB, row count: 20, single row data size: 24.80KB 
[INFO ] 2024-10-09 18:18:06.388 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:18:08.635 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:18:09.222 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:18:09.222 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:18:10.879 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:18:12.525 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:18:13.340 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:18:14.147 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:18:14.958 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:18:15.570 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:18:16.153 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:18:16.894 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:18:17.485 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:18:18.096 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:18:18.720 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:18:18.926 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:18:31.797 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:18:31.804 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:18:39.444 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:18:39.458 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:18:39.458 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:18:39.480 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-86abc1a2-2cc2-449d-b6f8-8d115f81907f 
[INFO ] 2024-10-09 18:18:39.480 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-86abc1a2-2cc2-449d-b6f8-8d115f81907f 
[INFO ] 2024-10-09 18:18:39.484 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:18:39.489 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-f8c345f4-7746-4cb6-a85d-23bed72f85f9 
[INFO ] 2024-10-09 18:18:39.489 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-f8c345f4-7746-4cb6-a85d-23bed72f85f9 
[INFO ] 2024-10-09 18:18:39.489 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:18:39.491 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:18:39.491 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:18:39.492 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:18:39.492 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:18:39.492 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:18:39.492 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:18:39.493 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:18:39.493 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:18:39.493 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:18:39.493 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:18:39.495 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 63 ms 
[INFO ] 2024-10-09 18:18:39.495 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 68 ms 
[INFO ] 2024-10-09 18:18:39.701 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 59 ms 
[INFO ] 2024-10-09 18:18:41.115 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:18:41.239 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6b3eaa81 
[INFO ] 2024-10-09 18:18:41.240 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:18:41.263 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:18:41.263 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:18:41.280 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:18:41.486 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:19:04.071 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:19:04.072 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:19:05.326 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-b3bf9b44-c058-4814-b940-27fb6eb1adf9 complete, cost 902ms 
[INFO ] 2024-10-09 18:19:05.645 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:19:05.835 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:19:05.835 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:19:05.837 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:19:05.837 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:19:05.838 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:19:05.838 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:19:05.839 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:19:05.840 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:19:06.182 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:19:06.186 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:19:06.192 - [任务 3 - Copy][FDM_Customer_360_V1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-09 18:19:06.192 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-09 18:19:06.393 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:19:06.536 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:19:06.536 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:19:06.546 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:19:06.751 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:19:06.986 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 496.22KB, row count: 20, single row data size: 24.81KB 
[INFO ] 2024-10-09 18:19:06.986 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:19:09.212 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:19:09.216 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:19:09.418 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:19:10.835 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:19:12.146 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:19:12.958 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:19:13.964 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:19:14.578 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:19:15.050 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:19:15.859 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:19:16.384 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:19:16.997 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:19:17.317 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:19:18.328 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:19:18.531 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:19:30.981 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:19:30.982 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:19:38.620 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:19:38.623 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:19:38.623 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:19:38.668 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-5a7c2c8b-dc9e-403d-89e3-6c3b4ab01f70 
[INFO ] 2024-10-09 18:19:38.673 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-5a7c2c8b-dc9e-403d-89e3-6c3b4ab01f70 
[INFO ] 2024-10-09 18:19:38.673 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:19:38.689 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-cedb1e9f-6a2c-461e-b964-377033d5dbbb 
[INFO ] 2024-10-09 18:19:38.689 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-cedb1e9f-6a2c-461e-b964-377033d5dbbb 
[INFO ] 2024-10-09 18:19:38.689 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:19:38.689 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:19:38.689 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:19:38.689 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:19:38.691 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:19:38.691 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:19:38.692 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:19:38.692 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:19:38.692 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:19:38.692 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 81 ms 
[INFO ] 2024-10-09 18:19:38.693 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 76 ms 
[INFO ] 2024-10-09 18:19:38.693 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:19:38.695 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:19:38.695 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 82 ms 
[INFO ] 2024-10-09 18:19:41.370 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:19:41.371 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@c7f9179 
[INFO ] 2024-10-09 18:19:41.496 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:19:41.510 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:19:41.510 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:19:41.524 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:19:41.524 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:20:04.409 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:20:04.409 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:20:05.505 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-9845e66f-f08d-4804-807a-9face95c7acc complete, cost 800ms 
[INFO ] 2024-10-09 18:20:05.896 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:20:05.954 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:20:06.125 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:20:06.125 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:20:06.125 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:20:06.125 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 1 ms 
[INFO ] 2024-10-09 18:20:06.125 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:20:06.125 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:20:06.126 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:20:06.415 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:20:06.415 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:20:06.416 - [任务 3 - Copy][FDM_Customer_360_V1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-09 18:20:06.416 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-09 18:20:06.626 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:20:06.633 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:20:06.633 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:20:06.640 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:20:06.845 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:20:07.136 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 501.62KB, row count: 20, single row data size: 25.08KB 
[INFO ] 2024-10-09 18:20:07.137 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:20:09.462 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:20:10.018 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:20:10.221 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:20:11.402 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:20:12.776 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:20:13.517 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:20:14.434 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:20:14.979 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:20:15.574 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:20:16.388 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:20:16.850 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:20:17.459 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:20:17.835 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:20:18.642 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:20:19.019 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:20:30.988 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:20:31.194 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:20:38.649 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:20:38.650 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:20:38.650 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:20:38.651 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-05df1a19-e582-4847-9426-bae9868ea910 
[INFO ] 2024-10-09 18:20:38.651 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-05df1a19-e582-4847-9426-bae9868ea910 
[INFO ] 2024-10-09 18:20:38.651 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:20:38.660 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-cf22ece3-3966-46da-be83-e234b21c27ec 
[INFO ] 2024-10-09 18:20:38.660 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:20:38.660 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-cf22ece3-3966-46da-be83-e234b21c27ec 
[INFO ] 2024-10-09 18:20:38.660 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:20:38.660 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:20:38.660 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:20:38.661 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:20:38.662 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:20:38.662 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:20:38.662 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:20:38.662 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:20:38.663 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 52 ms 
[INFO ] 2024-10-09 18:20:38.663 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 43 ms 
[INFO ] 2024-10-09 18:20:38.665 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:20:38.665 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:20:38.869 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 48 ms 
[INFO ] 2024-10-09 18:20:41.592 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:20:41.593 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3df786af 
[INFO ] 2024-10-09 18:20:41.705 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:20:41.716 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:20:41.716 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:20:41.730 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:20:41.730 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:21:04.606 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:21:04.607 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:21:05.686 - [任务 3 - Copy] - load tapTable task 66f2602865bb1a25d16baec5-b3ba4b92-017a-4a9b-a8b6-ec5a1aafa905 complete, cost 788ms 
[INFO ] 2024-10-09 18:21:06.006 - [任务 3 - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy] - The engine receives 任务 3 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] start preload schema,table counts: 1 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] preload schema finished, cost 0 ms 
[INFO ] 2024-10-09 18:21:06.144 - [任务 3 - Copy][增强JS] - Node js_processor(增强JS: 1b36da4e-b79c-4a0d-9b28-95344195db8c) enable batch process 
[INFO ] 2024-10-09 18:21:06.515 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-09 18:21:06.537 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" read batch size: 100 
[INFO ] 2024-10-09 18:21:06.544 - [任务 3 - Copy][FDM_Customer_360_V1] - Source node "FDM_Customer_360_V1" event queue capacity: 200 
[INFO ] 2024-10-09 18:21:06.544 - [任务 3 - Copy][FDM_Customer_360_V1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-09 18:21:06.544 - [任务 3 - Copy][FDM_Customer_360_V1] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-09 18:21:06.745 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync started 
[INFO ] 2024-10-09 18:21:06.745 - [任务 3 - Copy][FDM_Customer_360_V1] - Starting batch read, table name: FDM_Customer_360_V1 
[INFO ] 2024-10-09 18:21:06.758 - [任务 3 - Copy][FDM_Customer_360_V1] - Table FDM_Customer_360_V1 is going to be initial synced 
[INFO ] 2024-10-09 18:21:06.963 - [任务 3 - Copy][FDM_Customer_360_V1] - Query table 'FDM_Customer_360_V1' counts: 7099 
[INFO ] 2024-10-09 18:21:07.206 - [任务 3 - Copy] - [DynamicAdjustMemory] Sampling table FDM_Customer_360_V1's data, all data size: 493.79KB, row count: 20, single row data size: 24.69KB 
[INFO ] 2024-10-09 18:21:07.206 - [任务 3 - Copy][FDM_Customer_360_V1] - [DynamicAdjustMemory] Source queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:21:09.432 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] The target node enters the waiting phase until the queue adjustment is completed 
[INFO ] 2024-10-09 18:21:09.533 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Target queue size adjusted, old size: 200, new size: 12 
[INFO ] 2024-10-09 18:21:09.533 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - [DynamicAdjustMemory] Notify target node to process data 
[INFO ] 2024-10-09 18:21:10.847 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-09 18:21:12.209 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-09 18:21:13.023 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-09 18:21:13.831 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-09 18:21:14.131 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-09 18:21:14.703 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-09 18:21:15.516 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-09 18:21:16.033 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-09 18:21:16.851 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-09 18:21:17.459 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-09 18:21:17.959 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-09 18:21:18.164 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Table 'FDM_CUSTOMER_TEST' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-09 18:21:31.090 - [任务 3 - Copy][FDM_Customer_360_V1] - Table [FDM_Customer_360_V1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-09 18:21:31.098 - [任务 3 - Copy][FDM_Customer_360_V1] - Initial sync completed 
[INFO ] 2024-10-09 18:21:38.276 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] running status set to false 
[INFO ] 2024-10-09 18:21:38.277 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] running status set to false 
[INFO ] 2024-10-09 18:21:38.277 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] running status set to false 
[INFO ] 2024-10-09 18:21:38.280 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node stopped: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:21:38.284 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-a7c404cc-9f95-4b9f-858d-8fd3bf422fdd 
[INFO ] 2024-10-09 18:21:38.288 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:21:38.288 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-a7c404cc-9f95-4b9f-858d-8fd3bf422fdd 
[INFO ] 2024-10-09 18:21:38.288 - [任务 3 - Copy][FDM_Customer_360_V1] - PDK connector node released: HazelcastSourcePdkDataNode-c00af971-007a-4423-b01e-d42e9729bdab 
[INFO ] 2024-10-09 18:21:38.288 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:21:38.288 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - PDK connector node released: HazelcastTargetPdkDataNode-0b67cc22-3ff0-41d2-b1f6-a85349c64064 
[INFO ] 2024-10-09 18:21:38.288 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] schema data cleaned 
[INFO ] 2024-10-09 18:21:38.288 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] schema data cleaned 
[INFO ] 2024-10-09 18:21:38.289 - [任务 3 - Copy][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-e4ef0c24-6ba2-4886-9e7a-26a727d7735c 
[INFO ] 2024-10-09 18:21:38.289 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] monitor closed 
[INFO ] 2024-10-09 18:21:38.289 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] monitor closed 
[INFO ] 2024-10-09 18:21:38.291 - [任务 3 - Copy][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-e4ef0c24-6ba2-4886-9e7a-26a727d7735c 
[INFO ] 2024-10-09 18:21:38.292 - [任务 3 - Copy][增强JS] - [ScriptExecutorsManager-66f2602865bb1a25d16baec6-1b36da4e-b79c-4a0d-9b28-95344195db8c-66f257c065bb1a25d16b9de9] schema data cleaned 
[INFO ] 2024-10-09 18:21:38.292 - [任务 3 - Copy][FDM_CUSTOMER_TEST] - Node FDM_CUSTOMER_TEST[0b67cc22-3ff0-41d2-b1f6-a85349c64064] close complete, cost 78 ms 
[INFO ] 2024-10-09 18:21:38.292 - [任务 3 - Copy][FDM_Customer_360_V1] - Node FDM_Customer_360_V1[c00af971-007a-4423-b01e-d42e9729bdab] close complete, cost 83 ms 
[INFO ] 2024-10-09 18:21:38.295 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] schema data cleaned 
[INFO ] 2024-10-09 18:21:38.296 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] monitor closed 
[INFO ] 2024-10-09 18:21:38.502 - [任务 3 - Copy][增强JS] - Node 增强JS[1b36da4e-b79c-4a0d-9b28-95344195db8c] close complete, cost 85 ms 
[INFO ] 2024-10-09 18:21:41.841 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:21:41.844 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@479d8a38 
[INFO ] 2024-10-09 18:21:41.985 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:21:41.985 - [任务 3 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-09 18:21:41.985 - [任务 3 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-09 18:21:42.008 - [任务 3 - Copy] - Remove memory task client succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:21:42.008 - [任务 3 - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy[66f2602865bb1a25d16baec6] 
[INFO ] 2024-10-09 18:22:04.856 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:22:04.968 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:22:04.968 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:22:04.973 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@67949cff 
[INFO ] 2024-10-09 18:22:05.178 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:22:05.216 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:22:05.223 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0c938f58-c316-4879-833b-88cc38cb8daf}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0c938f58-c316-4879-833b-88cc38cb8daf}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0c938f58-c316-4879-833b-88cc38cb8daf}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0c938f58-c316-4879-833b-88cc38cb8daf}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0c938f58-c316-4879-833b-88cc38cb8daf}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0c938f58-c316-4879-833b-88cc38cb8daf}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:23:05.151 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:23:05.152 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:23:05.241 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:23:05.242 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@68ba6c3d 
[INFO ] 2024-10-09 18:23:05.350 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:23:05.361 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:23:05.361 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=43d83e27-609c-4e06-9834-c65b80c7cb1e}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=43d83e27-609c-4e06-9834-c65b80c7cb1e}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=43d83e27-609c-4e06-9834-c65b80c7cb1e}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=43d83e27-609c-4e06-9834-c65b80c7cb1e}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=43d83e27-609c-4e06-9834-c65b80c7cb1e}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=43d83e27-609c-4e06-9834-c65b80c7cb1e}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:24:05.516 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:24:05.517 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:24:05.603 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:24:05.604 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@22588a89 
[INFO ] 2024-10-09 18:24:05.710 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:24:05.719 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:24:05.719 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=34438c84-0590-492a-b7f5-cbaa565d3112}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=34438c84-0590-492a-b7f5-cbaa565d3112}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=34438c84-0590-492a-b7f5-cbaa565d3112}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=34438c84-0590-492a-b7f5-cbaa565d3112}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=34438c84-0590-492a-b7f5-cbaa565d3112}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=34438c84-0590-492a-b7f5-cbaa565d3112}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:25:05.917 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:25:05.918 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:25:06.023 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:25:06.023 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2dea5ba8 
[INFO ] 2024-10-09 18:25:06.157 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:25:06.157 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:25:06.360 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ee808a1d-57cf-4f49-bf2c-ac7c4d7c4fe6}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ee808a1d-57cf-4f49-bf2c-ac7c4d7c4fe6}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ee808a1d-57cf-4f49-bf2c-ac7c4d7c4fe6}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ee808a1d-57cf-4f49-bf2c-ac7c4d7c4fe6}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ee808a1d-57cf-4f49-bf2c-ac7c4d7c4fe6}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ee808a1d-57cf-4f49-bf2c-ac7c4d7c4fe6}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:26:06.271 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:26:06.272 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:26:06.388 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:26:06.389 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c96acd9 
[INFO ] 2024-10-09 18:26:06.496 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:26:06.512 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:26:06.512 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=85cb57f7-cc27-4ab9-ae29-9fcba57c1569}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=85cb57f7-cc27-4ab9-ae29-9fcba57c1569}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=85cb57f7-cc27-4ab9-ae29-9fcba57c1569}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=85cb57f7-cc27-4ab9-ae29-9fcba57c1569}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=85cb57f7-cc27-4ab9-ae29-9fcba57c1569}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=85cb57f7-cc27-4ab9-ae29-9fcba57c1569}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:27:06.460 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:27:06.461 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:27:06.541 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:27:06.541 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@242c88a 
[INFO ] 2024-10-09 18:27:06.648 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:27:06.655 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:27:06.656 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b1edd20e-3853-406f-ab47-242b7bef4725}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b1edd20e-3853-406f-ab47-242b7bef4725}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b1edd20e-3853-406f-ab47-242b7bef4725}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b1edd20e-3853-406f-ab47-242b7bef4725}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b1edd20e-3853-406f-ab47-242b7bef4725}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b1edd20e-3853-406f-ab47-242b7bef4725}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:28:06.870 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:28:06.870 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:28:06.952 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:28:06.953 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4b9654cc 
[INFO ] 2024-10-09 18:28:07.062 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:28:07.075 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:28:07.075 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d747e3e9-22b1-4c63-bc5b-9701f0db2eb2}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d747e3e9-22b1-4c63-bc5b-9701f0db2eb2}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d747e3e9-22b1-4c63-bc5b-9701f0db2eb2}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d747e3e9-22b1-4c63-bc5b-9701f0db2eb2}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d747e3e9-22b1-4c63-bc5b-9701f0db2eb2}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d747e3e9-22b1-4c63-bc5b-9701f0db2eb2}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:29:07.192 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:29:07.287 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:29:07.287 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:29:07.287 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4848e13a 
[INFO ] 2024-10-09 18:29:07.394 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:29:07.405 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:29:07.405 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=96208bd5-ac12-40b6-99fd-e8eb387dfe00}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=96208bd5-ac12-40b6-99fd-e8eb387dfe00}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=96208bd5-ac12-40b6-99fd-e8eb387dfe00}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=96208bd5-ac12-40b6-99fd-e8eb387dfe00}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=96208bd5-ac12-40b6-99fd-e8eb387dfe00}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=96208bd5-ac12-40b6-99fd-e8eb387dfe00}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:30:07.572 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:30:07.652 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:30:07.652 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:30:07.653 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3b5306b6 
[INFO ] 2024-10-09 18:30:07.767 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:30:07.767 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:30:07.971 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fb0c019b-a897-4a8a-b1b7-b131a9edf12c}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fb0c019b-a897-4a8a-b1b7-b131a9edf12c}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fb0c019b-a897-4a8a-b1b7-b131a9edf12c}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fb0c019b-a897-4a8a-b1b7-b131a9edf12c}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fb0c019b-a897-4a8a-b1b7-b131a9edf12c}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fb0c019b-a897-4a8a-b1b7-b131a9edf12c}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:31:07.933 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:31:07.933 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:31:08.036 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:31:08.037 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3693487b 
[INFO ] 2024-10-09 18:31:08.142 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:31:08.170 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:31:08.170 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ef52b7e7-a656-43be-9e2f-f2fcf4548754}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ef52b7e7-a656-43be-9e2f-f2fcf4548754}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ef52b7e7-a656-43be-9e2f-f2fcf4548754}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ef52b7e7-a656-43be-9e2f-f2fcf4548754}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ef52b7e7-a656-43be-9e2f-f2fcf4548754}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ef52b7e7-a656-43be-9e2f-f2fcf4548754}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:32:08.173 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:32:08.174 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:32:08.263 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:32:08.264 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3f141997 
[INFO ] 2024-10-09 18:32:08.369 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:32:08.384 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:32:08.385 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1301fe04-1d05-44b7-9147-514ea9576f04}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1301fe04-1d05-44b7-9147-514ea9576f04}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1301fe04-1d05-44b7-9147-514ea9576f04}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1301fe04-1d05-44b7-9147-514ea9576f04}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1301fe04-1d05-44b7-9147-514ea9576f04}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1301fe04-1d05-44b7-9147-514ea9576f04}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:33:08.424 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:33:08.426 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:33:08.555 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:33:08.555 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1946a614 
[INFO ] 2024-10-09 18:33:08.666 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:33:08.666 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:33:08.872 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=113b86d9-97db-4d48-b86d-94dbffe4dc07}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=113b86d9-97db-4d48-b86d-94dbffe4dc07}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=113b86d9-97db-4d48-b86d-94dbffe4dc07}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=113b86d9-97db-4d48-b86d-94dbffe4dc07}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=113b86d9-97db-4d48-b86d-94dbffe4dc07}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=113b86d9-97db-4d48-b86d-94dbffe4dc07}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:34:08.692 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:34:08.692 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:34:08.888 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:34:08.888 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@22cc944f 
[INFO ] 2024-10-09 18:34:08.888 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:34:08.902 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:34:08.902 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=324a2df7-c16e-497a-9119-ab4f9afe8804}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=324a2df7-c16e-497a-9119-ab4f9afe8804}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=324a2df7-c16e-497a-9119-ab4f9afe8804}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=324a2df7-c16e-497a-9119-ab4f9afe8804}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=324a2df7-c16e-497a-9119-ab4f9afe8804}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=324a2df7-c16e-497a-9119-ab4f9afe8804}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:35:09.125 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:35:09.126 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:35:09.213 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:35:09.214 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1338fe8 
[INFO ] 2024-10-09 18:35:09.327 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:35:09.327 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:35:09.531 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ff85141a-28d7-44ae-9f43-dc039bff3fda}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ff85141a-28d7-44ae-9f43-dc039bff3fda}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ff85141a-28d7-44ae-9f43-dc039bff3fda}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ff85141a-28d7-44ae-9f43-dc039bff3fda}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ff85141a-28d7-44ae-9f43-dc039bff3fda}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ff85141a-28d7-44ae-9f43-dc039bff3fda}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:36:09.439 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:36:09.535 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:36:09.535 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:36:09.537 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@64a7809d 
[INFO ] 2024-10-09 18:36:09.641 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:36:09.651 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:36:09.651 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=547238af-f852-442b-83cd-f0862b79dc13}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=547238af-f852-442b-83cd-f0862b79dc13}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=547238af-f852-442b-83cd-f0862b79dc13}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=547238af-f852-442b-83cd-f0862b79dc13}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=547238af-f852-442b-83cd-f0862b79dc13}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=547238af-f852-442b-83cd-f0862b79dc13}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:37:09.737 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:37:09.737 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:37:09.857 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:37:09.971 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@c810c3c 
[INFO ] 2024-10-09 18:37:09.971 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:37:09.975 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:37:09.975 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ea343c85-cc40-4e51-9b80-a899a6b10d04}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ea343c85-cc40-4e51-9b80-a899a6b10d04}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ea343c85-cc40-4e51-9b80-a899a6b10d04}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ea343c85-cc40-4e51-9b80-a899a6b10d04}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ea343c85-cc40-4e51-9b80-a899a6b10d04}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ea343c85-cc40-4e51-9b80-a899a6b10d04}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:38:10.050 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:38:10.137 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:38:10.137 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:38:10.138 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1751a71c 
[INFO ] 2024-10-09 18:38:10.246 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:38:10.259 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:38:10.260 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=704bbe50-4a16-4343-8d65-30435873b62f}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=704bbe50-4a16-4343-8d65-30435873b62f}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=704bbe50-4a16-4343-8d65-30435873b62f}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=704bbe50-4a16-4343-8d65-30435873b62f}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=704bbe50-4a16-4343-8d65-30435873b62f}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=704bbe50-4a16-4343-8d65-30435873b62f}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:39:00.326 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:39:00.326 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:39:00.424 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:39:00.425 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d85f736 
[INFO ] 2024-10-09 18:39:00.553 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:39:00.553 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:39:00.755 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac31a651-0481-4665-8735-9740e55ba221}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac31a651-0481-4665-8735-9740e55ba221}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac31a651-0481-4665-8735-9740e55ba221}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac31a651-0481-4665-8735-9740e55ba221}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac31a651-0481-4665-8735-9740e55ba221}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac31a651-0481-4665-8735-9740e55ba221}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:40:00.648 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:40:00.649 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:40:00.727 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:40:00.835 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@36c2b371 
[INFO ] 2024-10-09 18:40:00.835 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:40:00.842 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:40:00.845 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0e70fe93-9a48-4233-971c-d9ad95d09ea7}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0e70fe93-9a48-4233-971c-d9ad95d09ea7}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0e70fe93-9a48-4233-971c-d9ad95d09ea7}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0e70fe93-9a48-4233-971c-d9ad95d09ea7}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0e70fe93-9a48-4233-971c-d9ad95d09ea7}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0e70fe93-9a48-4233-971c-d9ad95d09ea7}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:41:01.028 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:41:01.028 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:41:01.117 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:41:01.117 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@63c4eed6 
[INFO ] 2024-10-09 18:41:01.245 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:41:01.254 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:41:01.459 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e80a5f64-7ce0-403f-938a-ce5d11db9b02}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e80a5f64-7ce0-403f-938a-ce5d11db9b02}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e80a5f64-7ce0-403f-938a-ce5d11db9b02}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e80a5f64-7ce0-403f-938a-ce5d11db9b02}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e80a5f64-7ce0-403f-938a-ce5d11db9b02}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e80a5f64-7ce0-403f-938a-ce5d11db9b02}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:42:01.360 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:42:01.360 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:42:01.447 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:42:01.448 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@173f2f39 
[INFO ] 2024-10-09 18:42:01.556 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:42:01.567 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:42:01.567 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=abf99642-f973-40d6-9486-199f9d541fc3}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=abf99642-f973-40d6-9486-199f9d541fc3}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=abf99642-f973-40d6-9486-199f9d541fc3}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=abf99642-f973-40d6-9486-199f9d541fc3}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=abf99642-f973-40d6-9486-199f9d541fc3}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=abf99642-f973-40d6-9486-199f9d541fc3}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:43:01.668 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:43:01.668 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:43:01.767 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:43:01.767 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@23a78de8 
[INFO ] 2024-10-09 18:43:01.896 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:43:01.896 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:43:02.102 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1d1d0c39-42bd-476e-a946-9b131df85d04}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1d1d0c39-42bd-476e-a946-9b131df85d04}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1d1d0c39-42bd-476e-a946-9b131df85d04}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1d1d0c39-42bd-476e-a946-9b131df85d04}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1d1d0c39-42bd-476e-a946-9b131df85d04}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1d1d0c39-42bd-476e-a946-9b131df85d04}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:44:02.025 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:44:02.103 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:44:02.103 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:44:02.212 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7e20a973 
[INFO ] 2024-10-09 18:44:02.212 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:44:02.221 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:44:02.221 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=49c56c2c-2157-4080-9a1b-fc79eb1efe49}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=49c56c2c-2157-4080-9a1b-fc79eb1efe49}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=49c56c2c-2157-4080-9a1b-fc79eb1efe49}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=49c56c2c-2157-4080-9a1b-fc79eb1efe49}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=49c56c2c-2157-4080-9a1b-fc79eb1efe49}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=49c56c2c-2157-4080-9a1b-fc79eb1efe49}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:45:02.352 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:45:02.531 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:45:02.531 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:45:02.532 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d56dee6 
[INFO ] 2024-10-09 18:45:02.642 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:45:02.650 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:45:02.651 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=57f54d90-6c53-485b-8c11-1bde38ed63df}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=57f54d90-6c53-485b-8c11-1bde38ed63df}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=57f54d90-6c53-485b-8c11-1bde38ed63df}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=57f54d90-6c53-485b-8c11-1bde38ed63df}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=57f54d90-6c53-485b-8c11-1bde38ed63df}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=57f54d90-6c53-485b-8c11-1bde38ed63df}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:46:02.719 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:46:02.720 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:46:02.811 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:46:02.811 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@ebc19ba 
[INFO ] 2024-10-09 18:46:02.930 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:46:02.930 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:46:03.135 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d62d422e-5f1a-4b64-8ec5-75a9cc932aa7}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d62d422e-5f1a-4b64-8ec5-75a9cc932aa7}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d62d422e-5f1a-4b64-8ec5-75a9cc932aa7}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d62d422e-5f1a-4b64-8ec5-75a9cc932aa7}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d62d422e-5f1a-4b64-8ec5-75a9cc932aa7}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d62d422e-5f1a-4b64-8ec5-75a9cc932aa7}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:47:03.124 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:47:03.125 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:47:03.259 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:47:03.380 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6e58bf5e 
[INFO ] 2024-10-09 18:47:03.380 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:47:03.581 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:47:03.582 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=795f9978-82f1-49f0-a49d-0bc3c074333c}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=795f9978-82f1-49f0-a49d-0bc3c074333c}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=795f9978-82f1-49f0-a49d-0bc3c074333c}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=795f9978-82f1-49f0-a49d-0bc3c074333c}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=795f9978-82f1-49f0-a49d-0bc3c074333c}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=795f9978-82f1-49f0-a49d-0bc3c074333c}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:48:03.308 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:48:03.401 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:48:03.401 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:48:03.402 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d077b2c 
[INFO ] 2024-10-09 18:48:03.507 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:48:03.515 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:48:03.515 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ae546e51-0dd4-4c35-b59d-7d4bd2553a9f}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ae546e51-0dd4-4c35-b59d-7d4bd2553a9f}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ae546e51-0dd4-4c35-b59d-7d4bd2553a9f}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ae546e51-0dd4-4c35-b59d-7d4bd2553a9f}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ae546e51-0dd4-4c35-b59d-7d4bd2553a9f}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ae546e51-0dd4-4c35-b59d-7d4bd2553a9f}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:49:03.618 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:49:03.619 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:49:03.703 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:49:03.703 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@46d1e4fd 
[INFO ] 2024-10-09 18:49:03.823 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:49:03.826 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:49:03.826 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac2f7023-746a-4207-83bd-ba52843642a8}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac2f7023-746a-4207-83bd-ba52843642a8}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac2f7023-746a-4207-83bd-ba52843642a8}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac2f7023-746a-4207-83bd-ba52843642a8}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac2f7023-746a-4207-83bd-ba52843642a8}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ac2f7023-746a-4207-83bd-ba52843642a8}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:50:03.845 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:50:03.846 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:50:03.938 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:50:03.938 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@18552b14 
[INFO ] 2024-10-09 18:50:04.055 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:50:04.070 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:50:04.070 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=577d5bd2-fa82-4c47-9b96-d2639be5cf94}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=577d5bd2-fa82-4c47-9b96-d2639be5cf94}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=577d5bd2-fa82-4c47-9b96-d2639be5cf94}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=577d5bd2-fa82-4c47-9b96-d2639be5cf94}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=577d5bd2-fa82-4c47-9b96-d2639be5cf94}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=577d5bd2-fa82-4c47-9b96-d2639be5cf94}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:51:04.137 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:51:04.138 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:51:04.220 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:51:04.220 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4e319c34 
[INFO ] 2024-10-09 18:51:04.323 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:51:04.329 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:51:04.333 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fbfe28e2-8f14-406f-8136-1318669a5397}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fbfe28e2-8f14-406f-8136-1318669a5397}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fbfe28e2-8f14-406f-8136-1318669a5397}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fbfe28e2-8f14-406f-8136-1318669a5397}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fbfe28e2-8f14-406f-8136-1318669a5397}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fbfe28e2-8f14-406f-8136-1318669a5397}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:52:04.438 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:52:04.439 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:52:04.523 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:52:04.523 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2196d0b5 
[INFO ] 2024-10-09 18:52:04.642 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:52:04.644 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:52:04.644 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=47d46e73-e909-4976-a829-4f5b7a37dac3}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=47d46e73-e909-4976-a829-4f5b7a37dac3}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=47d46e73-e909-4976-a829-4f5b7a37dac3}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=47d46e73-e909-4976-a829-4f5b7a37dac3}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=47d46e73-e909-4976-a829-4f5b7a37dac3}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=47d46e73-e909-4976-a829-4f5b7a37dac3}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:53:04.643 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:53:04.738 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:53:04.739 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:53:04.739 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@242cb4ef 
[INFO ] 2024-10-09 18:53:04.879 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:53:04.880 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:53:05.086 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=370a46e0-f99f-4fa0-a66c-9d27a6b0ecd2}: System error: null <-- Error Message -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=370a46e0-f99f-4fa0-a66c-9d27a6b0ecd2}: System error: null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=370a46e0-f99f-4fa0-a66c-9d27a6b0ecd2}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=370a46e0-f99f-4fa0-a66c-9d27a6b0ecd2}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=370a46e0-f99f-4fa0-a66c-9d27a6b0ecd2}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=e28eb1e2ea9448aaa91f4fc3d9567df841c49ab838d64c6da6717b2aa15b0dc2', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=370a46e0-f99f-4fa0-a66c-9d27a6b0ecd2}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:54:02.834 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:54:02.838 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:54:03.172 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:54:03.284 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@30168971 
[INFO ] 2024-10-09 18:54:03.285 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:54:03.310 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:54:03.312 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=51ccb472-0b26-4f17-b96e-ccdab45fbe9e}: System error: null <-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=51ccb472-0b26-4f17-b96e-ccdab45fbe9e}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=51ccb472-0b26-4f17-b96e-ccdab45fbe9e}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=51ccb472-0b26-4f17-b96e-ccdab45fbe9e}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

[INFO ] 2024-10-09 18:55:02.118 - [任务 3 - Copy] - Task initialization... 
[INFO ] 2024-10-09 18:55:02.258 - [任务 3 - Copy] - Start task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy) 
[INFO ] 2024-10-09 18:55:02.259 - [任务 3 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-09 18:55:02.261 - [任务 3 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5af522b 
[INFO ] 2024-10-09 18:55:02.376 - [任务 3 - Copy] - Stop task milestones: 66f2602865bb1a25d16baec6(任务 3 - Copy)  
[INFO ] 2024-10-09 18:55:02.391 - [任务 3 - Copy] - Stopped task aspect(s) 
[ERROR] 2024-10-09 18:55:02.392 - [任务 3 - Copy] - reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=683dd215-ba85-4383-a228-bd7cfc4f0431}: System error: null <-- Full Stack Trace -->
reason:RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=683dd215-ba85-4383-a228-bd7cfc4f0431}: System error: null
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:985)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:256)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:191)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:178)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:113)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=683dd215-ba85-4383-a228-bd7cfc4f0431}: System error: null
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:325)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:973)
	... 14 more
Caused by: RestException{uri='http://127.0.0.1:3000/api/Task/transformAllParam/66f2602865bb1a25d16baec6?access_token=fb286daa6bef454dbd884dfb246205d1327dcdeb84854c4594981172172fa03a', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=683dd215-ba85-4383-a228-bd7cfc4f0431}: System error: null
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:323)
	... 15 more

