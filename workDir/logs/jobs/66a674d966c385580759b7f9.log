[INFO ] 2024-07-29 00:42:02.084 - [Heart<PERSON>-<PERSON><PERSON>] - Start task milestones: 66a674d966c385580759b7f9(Heartbeat-<PERSON><PERSON>) 
[INFO ] 2024-07-29 00:42:02.824 - [Heartbeat-<PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 00:42:02.925 - [Heartbeat-Mon<PERSON>] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 00:42:03.606 - [Heartbeat-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[44eb9cfe-f0cf-4962-aace-f985700f7c01] start preload schema,table counts: 1 
[INFO ] 2024-07-29 00:42:03.606 - [Heartbeat-Mon<PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[50ad6c23-246b-482f-a850-3acff7d19349] start preload schema,table counts: 1 
[INFO ] 2024-07-29 00:42:03.627 - [<PERSON><PERSON>-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[50ad6c23-246b-482f-a850-3acff7d19349] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 00:42:03.628 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[44eb9cfe-f0cf-4962-aace-f985700f7c01] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 00:42:05.506 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-29 00:42:05.656 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1722184925, "i": 25}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1722184925, "i": 25}}, "signature": {"hash": {"$binary": {"base64": "GEM491Ib/5E5ODo47J6ZCbjaKwg=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-29 00:42:05.663 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-29 00:42:05.674 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-29 00:42:05.677 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 00:42:05.687 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1722184925657,"lastTimes":1722184925657,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-29 00:42:05.906 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-29 00:42:05.909 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-29 00:42:05.968 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-29 00:42:05.990 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-29 00:42:05.991 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 00:42:06.004 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1722184925657,"lastTimes":1722184925657,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-29 00:42:06.008 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-29 00:42:06.140 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
