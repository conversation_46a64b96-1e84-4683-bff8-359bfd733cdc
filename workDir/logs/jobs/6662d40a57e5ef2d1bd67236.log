[INFO ] 2024-06-07 17:47:18.226 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:18.226 - [任务 6(100)][490750ff-9fcd-4072-9e5b-906d1234e850] - Node 490750ff-9fcd-4072-9e5b-906d1234e850[490750ff-9fcd-4072-9e5b-906d1234e850] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:47:18.227 - [任务 6(100)][a76dc83e-b622-4432-867b-9794d6095d1e] - Node a76dc83e-b622-4432-867b-9794d6095d1e[a76dc83e-b622-4432-867b-9794d6095d1e] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:47:18.227 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:18.227 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:18.227 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:18.228 - [任务 6(100)][a76dc83e-b622-4432-867b-9794d6095d1e] - Node a76dc83e-b622-4432-867b-9794d6095d1e[a76dc83e-b622-4432-867b-9794d6095d1e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:18.228 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 17:47:18.228 - [任务 6(100)][490750ff-9fcd-4072-9e5b-906d1234e850] - Node 490750ff-9fcd-4072-9e5b-906d1234e850[490750ff-9fcd-4072-9e5b-906d1234e850] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:18.228 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:18.241 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:18.244 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:19.009 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:47:19.016 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:19.017 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:19.017 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.020 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:47:19.224 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 33 ms 
[INFO ] 2024-06-07 17:47:19.507 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:47:19.507 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:19.507 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:19.508 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.508 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:47:19.509 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 24 ms 
[INFO ] 2024-06-07 17:47:19.622 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:47:19.622 - [任务 6(100)][490750ff-9fcd-4072-9e5b-906d1234e850] - Node 490750ff-9fcd-4072-9e5b-906d1234e850[490750ff-9fcd-4072-9e5b-906d1234e850] running status set to false 
[INFO ] 2024-06-07 17:47:19.624 - [任务 6(100)][490750ff-9fcd-4072-9e5b-906d1234e850] - Node 490750ff-9fcd-4072-9e5b-906d1234e850[490750ff-9fcd-4072-9e5b-906d1234e850] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.624 - [任务 6(100)][490750ff-9fcd-4072-9e5b-906d1234e850] - Node 490750ff-9fcd-4072-9e5b-906d1234e850[490750ff-9fcd-4072-9e5b-906d1234e850] monitor closed 
[INFO ] 2024-06-07 17:47:19.625 - [任务 6(100)][490750ff-9fcd-4072-9e5b-906d1234e850] - Node 490750ff-9fcd-4072-9e5b-906d1234e850[490750ff-9fcd-4072-9e5b-906d1234e850] close complete, cost 5 ms 
[INFO ] 2024-06-07 17:47:19.625 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-cbbe1f3a-867b-41c2-ae92-cf3e6b367fed 
[INFO ] 2024-06-07 17:47:19.626 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-cbbe1f3a-867b-41c2-ae92-cf3e6b367fed 
[INFO ] 2024-06-07 17:47:19.626 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.630 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.630 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:47:19.635 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 13 ms 
[INFO ] 2024-06-07 17:47:19.635 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-490750ff-9fcd-4072-9e5b-906d1234e850 complete, cost 1620ms 
[INFO ] 2024-06-07 17:47:19.698 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:47:19.699 - [任务 6(100)][a76dc83e-b622-4432-867b-9794d6095d1e] - Node a76dc83e-b622-4432-867b-9794d6095d1e[a76dc83e-b622-4432-867b-9794d6095d1e] running status set to false 
[INFO ] 2024-06-07 17:47:19.699 - [任务 6(100)][a76dc83e-b622-4432-867b-9794d6095d1e] - Node a76dc83e-b622-4432-867b-9794d6095d1e[a76dc83e-b622-4432-867b-9794d6095d1e] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.700 - [任务 6(100)][a76dc83e-b622-4432-867b-9794d6095d1e] - Node a76dc83e-b622-4432-867b-9794d6095d1e[a76dc83e-b622-4432-867b-9794d6095d1e] monitor closed 
[INFO ] 2024-06-07 17:47:19.701 - [任务 6(100)][a76dc83e-b622-4432-867b-9794d6095d1e] - Node a76dc83e-b622-4432-867b-9794d6095d1e[a76dc83e-b622-4432-867b-9794d6095d1e] close complete, cost 4 ms 
[INFO ] 2024-06-07 17:47:19.703 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-bf3700be-1986-4236-95a4-9856c2811299 
[INFO ] 2024-06-07 17:47:19.703 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-bf3700be-1986-4236-95a4-9856c2811299 
[INFO ] 2024-06-07 17:47:19.708 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.708 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:47:19.709 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:47:19.709 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 15 ms 
[INFO ] 2024-06-07 17:47:19.921 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-a76dc83e-b622-4432-867b-9794d6095d1e complete, cost 1690ms 
[INFO ] 2024-06-07 17:47:20.805 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:20.808 - [任务 6(100)][174ea414-6b4f-451c-95a2-ad335be89bd8] - Node 174ea414-6b4f-451c-95a2-ad335be89bd8[174ea414-6b4f-451c-95a2-ad335be89bd8] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:47:20.808 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:20.808 - [任务 6(100)][174ea414-6b4f-451c-95a2-ad335be89bd8] - Node 174ea414-6b4f-451c-95a2-ad335be89bd8[174ea414-6b4f-451c-95a2-ad335be89bd8] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 17:47:20.808 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 17:47:20.808 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 17:47:20.978 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:47:20.982 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:20.982 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:20.982 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:47:20.982 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:47:20.982 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 4 ms 
[INFO ] 2024-06-07 17:47:21.093 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:47:21.093 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-fee98a28-599c-4d29-bbde-e9e2f160e042 
[INFO ] 2024-06-07 17:47:21.093 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-fee98a28-599c-4d29-bbde-e9e2f160e042 
[INFO ] 2024-06-07 17:47:21.094 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:47:21.095 - [任务 6(100)][174ea414-6b4f-451c-95a2-ad335be89bd8] - Node 174ea414-6b4f-451c-95a2-ad335be89bd8[174ea414-6b4f-451c-95a2-ad335be89bd8] running status set to false 
[INFO ] 2024-06-07 17:47:21.095 - [任务 6(100)][174ea414-6b4f-451c-95a2-ad335be89bd8] - Node 174ea414-6b4f-451c-95a2-ad335be89bd8[174ea414-6b4f-451c-95a2-ad335be89bd8] schema data cleaned 
[INFO ] 2024-06-07 17:47:21.095 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:47:21.095 - [任务 6(100)][174ea414-6b4f-451c-95a2-ad335be89bd8] - Node 174ea414-6b4f-451c-95a2-ad335be89bd8[174ea414-6b4f-451c-95a2-ad335be89bd8] monitor closed 
[INFO ] 2024-06-07 17:47:21.095 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:47:21.095 - [任务 6(100)][174ea414-6b4f-451c-95a2-ad335be89bd8] - Node 174ea414-6b4f-451c-95a2-ad335be89bd8[174ea414-6b4f-451c-95a2-ad335be89bd8] close complete, cost 0 ms 
[INFO ] 2024-06-07 17:47:21.095 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 4 ms 
[INFO ] 2024-06-07 17:47:21.099 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-174ea414-6b4f-451c-95a2-ad335be89bd8 complete, cost 366ms 
[INFO ] 2024-06-07 17:47:42.986 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:42.989 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:47:42.989 - [任务 6(100)][b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] - Node b1cef67a-9bb2-4d28-9bde-06e01a0a4f01[b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:47:42.989 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:42.993 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:42.993 - [任务 6(100)][b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] - Node b1cef67a-9bb2-4d28-9bde-06e01a0a4f01[b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:47:43.158 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:47:43.158 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:43.158 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:47:43.158 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:47:43.159 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:47:43.159 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 4 ms 
[ERROR] 2024-06-07 17:47:43.380 - [任务 6(100)][增强JS] - javax.script.ScriptException: java.lang.RuntimeException: script execute error <-- Error Message -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error

<-- Simple Stack Trace -->
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=TEST.USER, DRIVER=4.25.13
	com.ibm.db2.jcc.am.b6.a(b6.java:810)
	com.ibm.db2.jcc.am.b6.a(b6.java:66)
	com.ibm.db2.jcc.am.b6.a(b6.java:140)
	com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:124)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=TEST.USER, DRIVER=4.25.13
	at com.ibm.db2.jcc.am.b6.a(b6.java:810)
	at com.ibm.db2.jcc.am.b6.a(b6.java:66)
	at com.ibm.db2.jcc.am.b6.a(b6.java:140)
	at com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	at com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	at com.ibm.db2.jcc.am.k3.a(k3.java:2234)
	at com.ibm.db2.jcc.t4.ab.i(ab.java:206)
	at com.ibm.db2.jcc.t4.ab.b(ab.java:96)
	at com.ibm.db2.jcc.t4.p.a(p.java:32)
	at com.ibm.db2.jcc.t4.av.i(av.java:150)
	at com.ibm.db2.jcc.am.k3.al(k3.java:2203)
	at com.ibm.db2.jcc.am.k3.a(k3.java:3330)
	at com.ibm.db2.jcc.am.k3.e(k3.java:1131)
	at com.ibm.db2.jcc.am.k3.execute(k3.java:1110)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.DefaultSqlExecutor.execute(DefaultSqlExecutor.java:49)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:76)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:64)
	at io.tapdata.connector.db2.Db2Connector.lambda$registerCapabilities$2(Db2Connector.java:136)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:238)
	... 52 more

[INFO ] 2024-06-07 17:47:45.948 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:47:45.960 - [任务 6(100)][b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] - Node b1cef67a-9bb2-4d28-9bde-06e01a0a4f01[b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] running status set to false 
[INFO ] 2024-06-07 17:47:45.961 - [任务 6(100)][b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] - Node b1cef67a-9bb2-4d28-9bde-06e01a0a4f01[b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] schema data cleaned 
[INFO ] 2024-06-07 17:47:45.961 - [任务 6(100)][b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] - Node b1cef67a-9bb2-4d28-9bde-06e01a0a4f01[b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] monitor closed 
[INFO ] 2024-06-07 17:47:45.961 - [任务 6(100)][b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] - Node b1cef67a-9bb2-4d28-9bde-06e01a0a4f01[b1cef67a-9bb2-4d28-9bde-06e01a0a4f01] close complete, cost 14 ms 
[INFO ] 2024-06-07 17:47:45.974 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-0f353d56-0416-4db7-8933-5ead165316f4 
[INFO ] 2024-06-07 17:47:45.974 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-0f353d56-0416-4db7-8933-5ead165316f4 
[INFO ] 2024-06-07 17:47:45.975 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:47:45.977 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:47:45.978 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:47:45.978 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 49 ms 
[INFO ] 2024-06-07 17:47:46.187 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-b1cef67a-9bb2-4d28-9bde-06e01a0a4f01 complete, cost 3146ms 
[INFO ] 2024-06-07 17:48:12.120 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:12.121 - [任务 6(100)][60cdc387-83c0-4631-8ae3-6f3f3c08b443] - Node 60cdc387-83c0-4631-8ae3-6f3f3c08b443[60cdc387-83c0-4631-8ae3-6f3f3c08b443] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:48:12.121 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:12.127 - [任务 6(100)][60cdc387-83c0-4631-8ae3-6f3f3c08b443] - Node 60cdc387-83c0-4631-8ae3-6f3f3c08b443[60cdc387-83c0-4631-8ae3-6f3f3c08b443] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:12.128 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:12.128 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 17:48:12.286 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:12.286 - [任务 6(100)][dd505f37-634c-400d-bfd4-e82490430ab0] - Node dd505f37-634c-400d-bfd4-e82490430ab0[dd505f37-634c-400d-bfd4-e82490430ab0] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:48:12.286 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:12.287 - [任务 6(100)][dd505f37-634c-400d-bfd4-e82490430ab0] - Node dd505f37-634c-400d-bfd4-e82490430ab0[dd505f37-634c-400d-bfd4-e82490430ab0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:12.310 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:12.310 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:12.310 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:48:12.315 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:12.315 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:12.315 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:48:12.316 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:48:12.440 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 6 ms 
[INFO ] 2024-06-07 17:48:12.440 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:48:12.449 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:12.449 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:12.450 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:48:12.450 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:48:12.450 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 11 ms 
[ERROR] 2024-06-07 17:48:12.614 - [任务 6(100)][增强JS] - javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check <-- Error Message -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[ERROR] 2024-06-07 17:48:12.618 - [任务 6(100)][增强JS] - javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check <-- Error Message -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-06-07 17:48:15.124 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:48:15.126 - [任务 6(100)][60cdc387-83c0-4631-8ae3-6f3f3c08b443] - Node 60cdc387-83c0-4631-8ae3-6f3f3c08b443[60cdc387-83c0-4631-8ae3-6f3f3c08b443] running status set to false 
[INFO ] 2024-06-07 17:48:15.126 - [任务 6(100)][60cdc387-83c0-4631-8ae3-6f3f3c08b443] - Node 60cdc387-83c0-4631-8ae3-6f3f3c08b443[60cdc387-83c0-4631-8ae3-6f3f3c08b443] schema data cleaned 
[INFO ] 2024-06-07 17:48:15.126 - [任务 6(100)][60cdc387-83c0-4631-8ae3-6f3f3c08b443] - Node 60cdc387-83c0-4631-8ae3-6f3f3c08b443[60cdc387-83c0-4631-8ae3-6f3f3c08b443] monitor closed 
[INFO ] 2024-06-07 17:48:15.126 - [任务 6(100)][60cdc387-83c0-4631-8ae3-6f3f3c08b443] - Node 60cdc387-83c0-4631-8ae3-6f3f3c08b443[60cdc387-83c0-4631-8ae3-6f3f3c08b443] close complete, cost 21 ms 
[INFO ] 2024-06-07 17:48:15.131 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-b2ad9e7e-3591-4972-9b85-c75bfb168132 
[INFO ] 2024-06-07 17:48:15.131 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-b2ad9e7e-3591-4972-9b85-c75bfb168132 
[INFO ] 2024-06-07 17:48:15.133 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:15.133 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:48:15.133 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:48:15.134 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 40 ms 
[INFO ] 2024-06-07 17:48:15.148 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-60cdc387-83c0-4631-8ae3-6f3f3c08b443 complete, cost 3120ms 
[INFO ] 2024-06-07 17:48:15.148 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:48:15.150 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-ad8a6b8a-d87c-4687-977e-8b6963575019 
[INFO ] 2024-06-07 17:48:15.150 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-ad8a6b8a-d87c-4687-977e-8b6963575019 
[INFO ] 2024-06-07 17:48:15.163 - [任务 6(100)][dd505f37-634c-400d-bfd4-e82490430ab0] - Node dd505f37-634c-400d-bfd4-e82490430ab0[dd505f37-634c-400d-bfd4-e82490430ab0] running status set to false 
[INFO ] 2024-06-07 17:48:15.163 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:15.168 - [任务 6(100)][dd505f37-634c-400d-bfd4-e82490430ab0] - Node dd505f37-634c-400d-bfd4-e82490430ab0[dd505f37-634c-400d-bfd4-e82490430ab0] schema data cleaned 
[INFO ] 2024-06-07 17:48:15.168 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:48:15.168 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:48:15.168 - [任务 6(100)][dd505f37-634c-400d-bfd4-e82490430ab0] - Node dd505f37-634c-400d-bfd4-e82490430ab0[dd505f37-634c-400d-bfd4-e82490430ab0] monitor closed 
[INFO ] 2024-06-07 17:48:15.169 - [任务 6(100)][dd505f37-634c-400d-bfd4-e82490430ab0] - Node dd505f37-634c-400d-bfd4-e82490430ab0[dd505f37-634c-400d-bfd4-e82490430ab0] close complete, cost 15 ms 
[INFO ] 2024-06-07 17:48:15.169 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 21 ms 
[INFO ] 2024-06-07 17:48:15.170 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-dd505f37-634c-400d-bfd4-e82490430ab0 complete, cost 2920ms 
[INFO ] 2024-06-07 17:48:15.382 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:15.382 - [任务 6(100)][ca8e0834-f29f-4dcc-871c-4576b7181472] - Node ca8e0834-f29f-4dcc-871c-4576b7181472[ca8e0834-f29f-4dcc-871c-4576b7181472] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:48:15.382 - [任务 6(100)][ca8e0834-f29f-4dcc-871c-4576b7181472] - Node ca8e0834-f29f-4dcc-871c-4576b7181472[ca8e0834-f29f-4dcc-871c-4576b7181472] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:15.382 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:15.382 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:15.382 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:15.543 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:48:15.543 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:15.543 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:15.543 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:48:15.543 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:48:15.665 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 3 ms 
[ERROR] 2024-06-07 17:48:15.665 - [任务 6(100)][增强JS] - javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check <-- Error Message -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.IllegalArgumentException: The specified connection source [mysql-connection-name] could not build the executor, please check
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.getScriptExecutor(ScriptExecutorsManager.java:78)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-06-07 17:48:18.197 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:48:18.201 - [任务 6(100)][ca8e0834-f29f-4dcc-871c-4576b7181472] - Node ca8e0834-f29f-4dcc-871c-4576b7181472[ca8e0834-f29f-4dcc-871c-4576b7181472] running status set to false 
[INFO ] 2024-06-07 17:48:18.201 - [任务 6(100)][ca8e0834-f29f-4dcc-871c-4576b7181472] - Node ca8e0834-f29f-4dcc-871c-4576b7181472[ca8e0834-f29f-4dcc-871c-4576b7181472] schema data cleaned 
[INFO ] 2024-06-07 17:48:18.204 - [任务 6(100)][ca8e0834-f29f-4dcc-871c-4576b7181472] - Node ca8e0834-f29f-4dcc-871c-4576b7181472[ca8e0834-f29f-4dcc-871c-4576b7181472] monitor closed 
[INFO ] 2024-06-07 17:48:18.204 - [任务 6(100)][ca8e0834-f29f-4dcc-871c-4576b7181472] - Node ca8e0834-f29f-4dcc-871c-4576b7181472[ca8e0834-f29f-4dcc-871c-4576b7181472] close complete, cost 3 ms 
[INFO ] 2024-06-07 17:48:18.207 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-f4c02fc1-e9c5-4176-8b34-5860f26df6de 
[INFO ] 2024-06-07 17:48:18.208 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-f4c02fc1-e9c5-4176-8b34-5860f26df6de 
[INFO ] 2024-06-07 17:48:18.208 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:18.212 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:48:18.212 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:48:18.214 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 16 ms 
[INFO ] 2024-06-07 17:48:18.214 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-ca8e0834-f29f-4dcc-871c-4576b7181472 complete, cost 2874ms 
[INFO ] 2024-06-07 17:48:36.386 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:36.387 - [任务 6(100)][1ba01a48-dd62-4383-9e46-cacf2543c9a0] - Node 1ba01a48-dd62-4383-9e46-cacf2543c9a0[1ba01a48-dd62-4383-9e46-cacf2543c9a0] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:48:36.387 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:36.387 - [任务 6(100)][1ba01a48-dd62-4383-9e46-cacf2543c9a0] - Node 1ba01a48-dd62-4383-9e46-cacf2543c9a0[1ba01a48-dd62-4383-9e46-cacf2543c9a0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:36.387 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:36.387 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 17:48:36.565 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:48:36.571 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:36.571 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:36.572 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:48:36.572 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:48:36.573 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 16 ms 
[INFO ] 2024-06-07 17:48:36.778 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[ERROR] 2024-06-07 17:48:36.982 - [任务 6(100)][增强JS] - javax.script.ScriptException: java.lang.RuntimeException: script execute error <-- Error Message -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error

<-- Simple Stack Trace -->
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.T, DRIVER=4.25.13
	com.ibm.db2.jcc.am.b6.a(b6.java:810)
	com.ibm.db2.jcc.am.b6.a(b6.java:66)
	com.ibm.db2.jcc.am.b6.a(b6.java:140)
	com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.T, DRIVER=4.25.13
	at com.ibm.db2.jcc.am.b6.a(b6.java:810)
	at com.ibm.db2.jcc.am.b6.a(b6.java:66)
	at com.ibm.db2.jcc.am.b6.a(b6.java:140)
	at com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	at com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	at com.ibm.db2.jcc.am.k3.a(k3.java:2234)
	at com.ibm.db2.jcc.t4.ab.i(ab.java:206)
	at com.ibm.db2.jcc.t4.ab.b(ab.java:96)
	at com.ibm.db2.jcc.t4.p.a(p.java:32)
	at com.ibm.db2.jcc.t4.av.i(av.java:150)
	at com.ibm.db2.jcc.am.k3.al(k3.java:2203)
	at com.ibm.db2.jcc.am.k3.a(k3.java:3330)
	at com.ibm.db2.jcc.am.k3.e(k3.java:1131)
	at com.ibm.db2.jcc.am.k3.execute(k3.java:1110)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.DefaultSqlExecutor.execute(DefaultSqlExecutor.java:49)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:76)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:64)
	at io.tapdata.connector.db2.Db2Connector.lambda$registerCapabilities$2(Db2Connector.java:136)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:238)
	... 55 more

[INFO ] 2024-06-07 17:48:39.438 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:48:39.438 - [任务 6(100)][1ba01a48-dd62-4383-9e46-cacf2543c9a0] - Node 1ba01a48-dd62-4383-9e46-cacf2543c9a0[1ba01a48-dd62-4383-9e46-cacf2543c9a0] running status set to false 
[INFO ] 2024-06-07 17:48:39.438 - [任务 6(100)][1ba01a48-dd62-4383-9e46-cacf2543c9a0] - Node 1ba01a48-dd62-4383-9e46-cacf2543c9a0[1ba01a48-dd62-4383-9e46-cacf2543c9a0] schema data cleaned 
[INFO ] 2024-06-07 17:48:39.438 - [任务 6(100)][1ba01a48-dd62-4383-9e46-cacf2543c9a0] - Node 1ba01a48-dd62-4383-9e46-cacf2543c9a0[1ba01a48-dd62-4383-9e46-cacf2543c9a0] monitor closed 
[INFO ] 2024-06-07 17:48:39.441 - [任务 6(100)][1ba01a48-dd62-4383-9e46-cacf2543c9a0] - Node 1ba01a48-dd62-4383-9e46-cacf2543c9a0[1ba01a48-dd62-4383-9e46-cacf2543c9a0] close complete, cost 0 ms 
[INFO ] 2024-06-07 17:48:39.441 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-d578cf32-4423-44fa-8aeb-b593cfcf969f 
[INFO ] 2024-06-07 17:48:39.442 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-d578cf32-4423-44fa-8aeb-b593cfcf969f 
[INFO ] 2024-06-07 17:48:39.442 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:39.467 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-80096cfc-115e-4a03-aea4-02369c47144d 
[INFO ] 2024-06-07 17:48:39.467 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-80096cfc-115e-4a03-aea4-02369c47144d 
[INFO ] 2024-06-07 17:48:39.467 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:39.470 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:48:39.470 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:48:39.473 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 33 ms 
[INFO ] 2024-06-07 17:48:39.473 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-1ba01a48-dd62-4383-9e46-cacf2543c9a0 complete, cost 3177ms 
[INFO ] 2024-06-07 17:48:56.111 - [任务 6(100)][38382892-589e-4a0a-a3ea-e792bbdcd334] - Node 38382892-589e-4a0a-a3ea-e792bbdcd334[38382892-589e-4a0a-a3ea-e792bbdcd334] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:48:56.111 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:56.111 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:56.111 - [任务 6(100)][38382892-589e-4a0a-a3ea-e792bbdcd334] - Node 38382892-589e-4a0a-a3ea-e792bbdcd334[38382892-589e-4a0a-a3ea-e792bbdcd334] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:56.111 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:56.111 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:56.330 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:48:56.331 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:56.331 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:56.331 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:48:56.331 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:48:56.331 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 24 ms 
[INFO ] 2024-06-07 17:48:56.688 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 17:48:56.862 - [任务 6(100)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 17:48:56.868 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:48:56.868 - [任务 6(100)][38382892-589e-4a0a-a3ea-e792bbdcd334] - Node 38382892-589e-4a0a-a3ea-e792bbdcd334[38382892-589e-4a0a-a3ea-e792bbdcd334] running status set to false 
[INFO ] 2024-06-07 17:48:56.868 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-d8afe25f-d2cd-46b3-9f58-1353556ef108 
[INFO ] 2024-06-07 17:48:56.868 - [任务 6(100)][38382892-589e-4a0a-a3ea-e792bbdcd334] - Node 38382892-589e-4a0a-a3ea-e792bbdcd334[38382892-589e-4a0a-a3ea-e792bbdcd334] schema data cleaned 
[INFO ] 2024-06-07 17:48:56.868 - [任务 6(100)][38382892-589e-4a0a-a3ea-e792bbdcd334] - Node 38382892-589e-4a0a-a3ea-e792bbdcd334[38382892-589e-4a0a-a3ea-e792bbdcd334] monitor closed 
[INFO ] 2024-06-07 17:48:56.869 - [任务 6(100)][38382892-589e-4a0a-a3ea-e792bbdcd334] - Node 38382892-589e-4a0a-a3ea-e792bbdcd334[38382892-589e-4a0a-a3ea-e792bbdcd334] close complete, cost 0 ms 
[INFO ] 2024-06-07 17:48:56.869 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-d8afe25f-d2cd-46b3-9f58-1353556ef108 
[INFO ] 2024-06-07 17:48:56.869 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:56.875 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-6401411f-84aa-434f-81b9-0666b97db733 
[INFO ] 2024-06-07 17:48:56.875 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-6401411f-84aa-434f-81b9-0666b97db733 
[INFO ] 2024-06-07 17:48:56.875 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:56.878 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:48:56.878 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:48:56.882 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 16 ms 
[INFO ] 2024-06-07 17:48:56.882 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-38382892-589e-4a0a-a3ea-e792bbdcd334 complete, cost 861ms 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][30be1d2f-2b15-4ff5-9872-32c7846923a3] - Node 30be1d2f-2b15-4ff5-9872-32c7846923a3[30be1d2f-2b15-4ff5-9872-32c7846923a3] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 4 ms 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][30be1d2f-2b15-4ff5-9872-32c7846923a3] - Node 30be1d2f-2b15-4ff5-9872-32c7846923a3[30be1d2f-2b15-4ff5-9872-32c7846923a3] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:53:27.297 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 21 ms 
[INFO ] 2024-06-07 17:53:27.298 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 17:53:27.455 - [任务 6(100)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 17:53:27.462 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:53:27.462 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-3c15f5ad-8646-4678-96a3-a1e78db9a013 
[INFO ] 2024-06-07 17:53:27.462 - [任务 6(100)][30be1d2f-2b15-4ff5-9872-32c7846923a3] - Node 30be1d2f-2b15-4ff5-9872-32c7846923a3[30be1d2f-2b15-4ff5-9872-32c7846923a3] running status set to false 
[INFO ] 2024-06-07 17:53:27.462 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-3c15f5ad-8646-4678-96a3-a1e78db9a013 
[INFO ] 2024-06-07 17:53:27.462 - [任务 6(100)][30be1d2f-2b15-4ff5-9872-32c7846923a3] - Node 30be1d2f-2b15-4ff5-9872-32c7846923a3[30be1d2f-2b15-4ff5-9872-32c7846923a3] schema data cleaned 
[INFO ] 2024-06-07 17:53:27.462 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:27.463 - [任务 6(100)][30be1d2f-2b15-4ff5-9872-32c7846923a3] - Node 30be1d2f-2b15-4ff5-9872-32c7846923a3[30be1d2f-2b15-4ff5-9872-32c7846923a3] monitor closed 
[INFO ] 2024-06-07 17:53:27.463 - [任务 6(100)][30be1d2f-2b15-4ff5-9872-32c7846923a3] - Node 30be1d2f-2b15-4ff5-9872-32c7846923a3[30be1d2f-2b15-4ff5-9872-32c7846923a3] close complete, cost 1 ms 
[INFO ] 2024-06-07 17:53:27.472 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-c185375a-d69b-4779-83aa-36948cc6caea 
[INFO ] 2024-06-07 17:53:27.472 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-c185375a-d69b-4779-83aa-36948cc6caea 
[INFO ] 2024-06-07 17:53:27.472 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:27.476 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:53:27.476 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:53:27.482 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 19 ms 
[INFO ] 2024-06-07 17:53:27.482 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-30be1d2f-2b15-4ff5-9872-32c7846923a3 complete, cost 738ms 
[INFO ] 2024-06-07 17:53:36.673 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:36.674 - [任务 6(100)][a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] - Node a6a83539-11a8-4e5a-b67c-e743ea6ba3b5[a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:53:36.674 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:36.675 - [任务 6(100)][a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] - Node a6a83539-11a8-4e5a-b67c-e743ea6ba3b5[a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:53:36.675 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:53:36.675 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:53:37.229 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:53:37.230 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:37.241 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:37.242 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.244 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:53:37.264 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 21 ms 
[INFO ] 2024-06-07 17:53:37.266 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 17:53:37.499 - [任务 6(100)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 17:53:37.501 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:53:37.502 - [任务 6(100)][a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] - Node a6a83539-11a8-4e5a-b67c-e743ea6ba3b5[a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] running status set to false 
[INFO ] 2024-06-07 17:53:37.502 - [任务 6(100)][a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] - Node a6a83539-11a8-4e5a-b67c-e743ea6ba3b5[a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.502 - [任务 6(100)][a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] - Node a6a83539-11a8-4e5a-b67c-e743ea6ba3b5[a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] monitor closed 
[INFO ] 2024-06-07 17:53:37.502 - [任务 6(100)][a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] - Node a6a83539-11a8-4e5a-b67c-e743ea6ba3b5[a6a83539-11a8-4e5a-b67c-e743ea6ba3b5] close complete, cost 0 ms 
[INFO ] 2024-06-07 17:53:37.504 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-63806f99-1f92-478f-8cc5-bd7cd117525c 
[INFO ] 2024-06-07 17:53:37.505 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-63806f99-1f92-478f-8cc5-bd7cd117525c 
[INFO ] 2024-06-07 17:53:37.505 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.511 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-f1cb3fe0-47f2-4c29-976f-3e246fc6a59b 
[INFO ] 2024-06-07 17:53:37.511 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-f1cb3fe0-47f2-4c29-976f-3e246fc6a59b 
[INFO ] 2024-06-07 17:53:37.512 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.512 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.512 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:53:37.512 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 13 ms 
[INFO ] 2024-06-07 17:53:37.716 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-a6a83539-11a8-4e5a-b67c-e743ea6ba3b5 complete, cost 902ms 
[INFO ] 2024-06-07 18:01:33.684 - [任务 6(100)][8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] - Node 8f2d41a6-bea7-4055-bdbd-bfdc996b0bef[8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:01:33.684 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:33.684 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:33.685 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 18:01:33.685 - [任务 6(100)][8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] - Node 8f2d41a6-bea7-4055-bdbd-bfdc996b0bef[8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 18:01:33.685 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:01:33.918 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:01:33.924 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:33.924 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:33.924 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:01:33.924 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:01:34.002 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 7 ms 
[INFO ] 2024-06-07 18:01:34.002 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 18:01:34.168 - [任务 6(100)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 18:01:34.175 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:01:34.176 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-a6dfe948-38d4-4632-8b94-5dbf3d7eee80 
[INFO ] 2024-06-07 18:01:34.176 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-a6dfe948-38d4-4632-8b94-5dbf3d7eee80 
[INFO ] 2024-06-07 18:01:34.176 - [任务 6(100)][8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] - Node 8f2d41a6-bea7-4055-bdbd-bfdc996b0bef[8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] running status set to false 
[INFO ] 2024-06-07 18:01:34.176 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:34.176 - [任务 6(100)][8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] - Node 8f2d41a6-bea7-4055-bdbd-bfdc996b0bef[8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] schema data cleaned 
[INFO ] 2024-06-07 18:01:34.176 - [任务 6(100)][8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] - Node 8f2d41a6-bea7-4055-bdbd-bfdc996b0bef[8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] monitor closed 
[INFO ] 2024-06-07 18:01:34.180 - [任务 6(100)][8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] - Node 8f2d41a6-bea7-4055-bdbd-bfdc996b0bef[8f2d41a6-bea7-4055-bdbd-bfdc996b0bef] close complete, cost 1 ms 
[INFO ] 2024-06-07 18:01:34.181 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-e3351da4-64af-4817-b720-ef973b6a9bb8 
[INFO ] 2024-06-07 18:01:34.181 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-e3351da4-64af-4817-b720-ef973b6a9bb8 
[INFO ] 2024-06-07 18:01:34.181 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:34.183 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:01:34.183 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:01:34.185 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 12 ms 
[INFO ] 2024-06-07 18:01:34.185 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-8f2d41a6-bea7-4055-bdbd-bfdc996b0bef complete, cost 563ms 
[INFO ] 2024-06-07 18:01:49.270 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:49.270 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:49.271 - [任务 6(100)][32eca577-ef4a-430c-941e-9b627c7e1c80] - Node 32eca577-ef4a-430c-941e-9b627c7e1c80[32eca577-ef4a-430c-941e-9b627c7e1c80] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:01:49.271 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 18:01:49.271 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 18:01:49.273 - [任务 6(100)][32eca577-ef4a-430c-941e-9b627c7e1c80] - Node 32eca577-ef4a-430c-941e-9b627c7e1c80[32eca577-ef4a-430c-941e-9b627c7e1c80] preload schema finished, cost 1 ms 
[INFO ] 2024-06-07 18:01:49.449 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:01:49.452 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:49.452 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:49.453 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:01:49.453 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:01:49.453 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 13 ms 
[INFO ] 2024-06-07 18:01:49.827 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[ERROR] 2024-06-07 18:01:50.078 - [任务 6(100)][增强JS] - javax.script.ScriptException: java.lang.RuntimeException: script execute error <-- Error Message -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error

<-- Simple Stack Trace -->
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.TEST.TEST1, DRIVER=4.25.13
	com.ibm.db2.jcc.am.b6.a(b6.java:810)
	com.ibm.db2.jcc.am.b6.a(b6.java:66)
	com.ibm.db2.jcc.am.b6.a(b6.java:140)
	com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.TEST.TEST1, DRIVER=4.25.13
	at com.ibm.db2.jcc.am.b6.a(b6.java:810)
	at com.ibm.db2.jcc.am.b6.a(b6.java:66)
	at com.ibm.db2.jcc.am.b6.a(b6.java:140)
	at com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	at com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	at com.ibm.db2.jcc.am.k3.a(k3.java:2234)
	at com.ibm.db2.jcc.t4.ab.i(ab.java:206)
	at com.ibm.db2.jcc.t4.ab.b(ab.java:96)
	at com.ibm.db2.jcc.t4.p.a(p.java:32)
	at com.ibm.db2.jcc.t4.av.i(av.java:150)
	at com.ibm.db2.jcc.am.k3.al(k3.java:2203)
	at com.ibm.db2.jcc.am.k3.a(k3.java:3330)
	at com.ibm.db2.jcc.am.k3.e(k3.java:1131)
	at com.ibm.db2.jcc.am.k3.execute(k3.java:1110)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.DefaultSqlExecutor.execute(DefaultSqlExecutor.java:49)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:76)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:64)
	at io.tapdata.connector.db2.Db2Connector.lambda$registerCapabilities$2(Db2Connector.java:136)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:238)
	... 55 more

[INFO ] 2024-06-07 18:01:52.468 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:01:52.471 - [任务 6(100)][32eca577-ef4a-430c-941e-9b627c7e1c80] - Node 32eca577-ef4a-430c-941e-9b627c7e1c80[32eca577-ef4a-430c-941e-9b627c7e1c80] running status set to false 
[INFO ] 2024-06-07 18:01:52.471 - [任务 6(100)][32eca577-ef4a-430c-941e-9b627c7e1c80] - Node 32eca577-ef4a-430c-941e-9b627c7e1c80[32eca577-ef4a-430c-941e-9b627c7e1c80] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.471 - [任务 6(100)][32eca577-ef4a-430c-941e-9b627c7e1c80] - Node 32eca577-ef4a-430c-941e-9b627c7e1c80[32eca577-ef4a-430c-941e-9b627c7e1c80] monitor closed 
[INFO ] 2024-06-07 18:01:52.479 - [任务 6(100)][32eca577-ef4a-430c-941e-9b627c7e1c80] - Node 32eca577-ef4a-430c-941e-9b627c7e1c80[32eca577-ef4a-430c-941e-9b627c7e1c80] close complete, cost 12 ms 
[INFO ] 2024-06-07 18:01:52.479 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-96431629-a1b8-4968-8caf-6eb5430dc8b9 
[INFO ] 2024-06-07 18:01:52.479 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-96431629-a1b8-4968-8caf-6eb5430dc8b9 
[INFO ] 2024-06-07 18:01:52.479 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.497 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-f03589a0-2a13-429e-be0c-c7ed671553e6 
[INFO ] 2024-06-07 18:01:52.498 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-f03589a0-2a13-429e-be0c-c7ed671553e6 
[INFO ] 2024-06-07 18:01:52.498 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.502 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.502 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:01:52.506 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 44 ms 
[INFO ] 2024-06-07 18:01:52.506 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-32eca577-ef4a-430c-941e-9b627c7e1c80 complete, cost 3326ms 
[INFO ] 2024-06-07 18:02:05.188 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(100)][dd2e330b-b169-47ee-a552-31a8463dac43] - Node dd2e330b-b169-47ee-a552-31a8463dac43[dd2e330b-b169-47ee-a552-31a8463dac43] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(100)][dd2e330b-b169-47ee-a552-31a8463dac43] - Node dd2e330b-b169-47ee-a552-31a8463dac43[dd2e330b-b169-47ee-a552-31a8463dac43] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:05.361 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:02:05.361 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:05.361 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:05.361 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:02:05.361 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:02:05.362 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 3 ms 
[INFO ] 2024-06-07 18:02:05.567 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 18:02:05.763 - [任务 6(100)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 18:02:05.772 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:02:05.774 - [任务 6(100)][dd2e330b-b169-47ee-a552-31a8463dac43] - Node dd2e330b-b169-47ee-a552-31a8463dac43[dd2e330b-b169-47ee-a552-31a8463dac43] running status set to false 
[INFO ] 2024-06-07 18:02:05.774 - [任务 6(100)][dd2e330b-b169-47ee-a552-31a8463dac43] - Node dd2e330b-b169-47ee-a552-31a8463dac43[dd2e330b-b169-47ee-a552-31a8463dac43] schema data cleaned 
[INFO ] 2024-06-07 18:02:05.774 - [任务 6(100)][dd2e330b-b169-47ee-a552-31a8463dac43] - Node dd2e330b-b169-47ee-a552-31a8463dac43[dd2e330b-b169-47ee-a552-31a8463dac43] monitor closed 
[INFO ] 2024-06-07 18:02:05.774 - [任务 6(100)][dd2e330b-b169-47ee-a552-31a8463dac43] - Node dd2e330b-b169-47ee-a552-31a8463dac43[dd2e330b-b169-47ee-a552-31a8463dac43] close complete, cost 1 ms 
[INFO ] 2024-06-07 18:02:05.800 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-bc6c8301-31e2-490c-b181-b8dd01478eaf 
[INFO ] 2024-06-07 18:02:05.800 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-bc6c8301-31e2-490c-b181-b8dd01478eaf 
[INFO ] 2024-06-07 18:02:05.800 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:05.829 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-cb15b160-2bac-4f38-a3ff-7f84df63f1fd 
[INFO ] 2024-06-07 18:02:05.830 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-cb15b160-2bac-4f38-a3ff-7f84df63f1fd 
[INFO ] 2024-06-07 18:02:05.833 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:05.833 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:02:05.833 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:02:05.834 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 68 ms 
[INFO ] 2024-06-07 18:02:05.848 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-dd2e330b-b169-47ee-a552-31a8463dac43 complete, cost 694ms 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(100)][3823f8c3-14a0-42d1-a916-0d3c2ddbec52] - Node 3823f8c3-14a0-42d1-a916-0d3c2ddbec52[3823f8c3-14a0-42d1-a916-0d3c2ddbec52] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(100)][3823f8c3-14a0-42d1-a916-0d3c2ddbec52] - Node 3823f8c3-14a0-42d1-a916-0d3c2ddbec52[3823f8c3-14a0-42d1-a916-0d3c2ddbec52] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:19.765 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:19.977 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:02:19.978 - [任务 6(100)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:19.979 - [任务 6(100)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:19.979 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:02:19.979 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:02:19.979 - [任务 6(100)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 3 ms 
[INFO ] 2024-06-07 18:02:20.243 - [任务 6(100)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 18:02:20.533 - [任务 6(100)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 18:02:20.541 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:02:20.542 - [任务 6(100)][3823f8c3-14a0-42d1-a916-0d3c2ddbec52] - Node 3823f8c3-14a0-42d1-a916-0d3c2ddbec52[3823f8c3-14a0-42d1-a916-0d3c2ddbec52] running status set to false 
[INFO ] 2024-06-07 18:02:20.542 - [任务 6(100)][3823f8c3-14a0-42d1-a916-0d3c2ddbec52] - Node 3823f8c3-14a0-42d1-a916-0d3c2ddbec52[3823f8c3-14a0-42d1-a916-0d3c2ddbec52] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.542 - [任务 6(100)][3823f8c3-14a0-42d1-a916-0d3c2ddbec52] - Node 3823f8c3-14a0-42d1-a916-0d3c2ddbec52[3823f8c3-14a0-42d1-a916-0d3c2ddbec52] monitor closed 
[INFO ] 2024-06-07 18:02:20.542 - [任务 6(100)][3823f8c3-14a0-42d1-a916-0d3c2ddbec52] - Node 3823f8c3-14a0-42d1-a916-0d3c2ddbec52[3823f8c3-14a0-42d1-a916-0d3c2ddbec52] close complete, cost 0 ms 
[INFO ] 2024-06-07 18:02:20.545 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-1830cf6f-759c-4853-9b7e-a6e5d5bfc9e7 
[INFO ] 2024-06-07 18:02:20.545 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-1830cf6f-759c-4853-9b7e-a6e5d5bfc9e7 
[INFO ] 2024-06-07 18:02:20.545 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.553 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-63f064a0-fb80-4b27-b77d-a70ba64fa961 
[INFO ] 2024-06-07 18:02:20.553 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-63f064a0-fb80-4b27-b77d-a70ba64fa961 
[INFO ] 2024-06-07 18:02:20.554 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67236-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.556 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.556 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:02:20.559 - [任务 6(100)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 18 ms 
[INFO ] 2024-06-07 18:02:20.559 - [任务 6(100)] - load tapTable task 6662d40a57e5ef2d1bd67236-3823f8c3-14a0-42d1-a916-0d3c2ddbec52 complete, cost 864ms 
