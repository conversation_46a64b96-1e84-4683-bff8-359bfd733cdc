[INFO ] 2024-07-02 12:16:43.264 - [任务 36] - Start task milestones: 66810f4e6fad3a13e50cb996(任务 36) 
[INFO ] 2024-07-02 12:16:43.311 - [任务 36] - Task initialization... 
[INFO ] 2024-07-02 12:16:43.680 - [任务 36] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-02 12:16:43.682 - [任务 36] - The engine receives 任务 36 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 12:16:43.774 - [任务 36][表编辑] - Node 表编辑[9d3e4249-5054-4245-9096-1c67233a758b] start preload schema,table counts: 5 
[INFO ] 2024-07-02 12:16:43.775 - [任务 36][表编辑] - Node 表编辑[9d3e4249-5054-4245-9096-1c67233a758b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:16:43.778 - [任务 36][SouceMysql] - Node SouceMysql[48f25445-ec49-4be1-bb7a-4a1bf6b6964b] start preload schema,table counts: 5 
[INFO ] 2024-07-02 12:16:43.778 - [任务 36][SourceMongo] - Node SourceMongo[41e22bc5-1dde-473c-8ad6-6c34a89092f0] start preload schema,table counts: 5 
[INFO ] 2024-07-02 12:16:43.778 - [任务 36][SouceMysql] - Node SouceMysql[48f25445-ec49-4be1-bb7a-4a1bf6b6964b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:16:43.982 - [任务 36][SourceMongo] - Node SourceMongo[41e22bc5-1dde-473c-8ad6-6c34a89092f0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:16:44.595 - [任务 36][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 12:16:44.595 - [任务 36][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 12:16:44.602 - [任务 36][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-02 12:16:44.606 - [任务 36][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-02 12:16:44.606 - [任务 36][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 12:16:44.638 - [任务 36][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":4849349,"gtidSet":""} 
[INFO ] 2024-07-02 12:16:44.638 - [任务 36] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-02 12:16:44.842 - [任务 36][SouceMysql] - Initial sync started 
[INFO ] 2024-07-02 12:16:44.986 - [任务 36][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapDropTableEvent@4590fae2: {"tableId":"CLAIM2","type":208} 
[INFO ] 2024-07-02 12:16:44.987 - [任务 36][SouceMysql] - Drop table in memory qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM2_667413fd7b5e1f6c3b139e78_66810f4e6fad3a13e50cb996 
[INFO ] 2024-07-02 12:16:45.067 - [任务 36][SouceMysql] - Drop table schema transform finished 
[INFO ] 2024-07-02 12:16:45.089 - [任务 36][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapDropTableEvent@4be5167a: {"tableId":"CLAIM3","type":208} 
[INFO ] 2024-07-02 12:16:45.089 - [任务 36][SouceMysql] - Drop table in memory qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM3_667413fd7b5e1f6c3b139e78_66810f4e6fad3a13e50cb996 
[INFO ] 2024-07-02 12:16:45.206 - [任务 36][SouceMysql] - Drop table schema transform finished 
[INFO ] 2024-07-02 12:16:45.206 - [任务 36][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapDropTableEvent@67103418: {"tableId":"CLAIMBACK","type":208} 
[INFO ] 2024-07-02 12:16:45.249 - [任务 36][SouceMysql] - Drop table in memory qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIMBACK_667413fd7b5e1f6c3b139e78_66810f4e6fad3a13e50cb996 
[INFO ] 2024-07-02 12:16:45.249 - [任务 36][SouceMysql] - Drop table schema transform finished 
[INFO ] 2024-07-02 12:16:45.287 - [任务 36][SouceMysql] - Found new table(s): [CLAIM1_WIM] 
[INFO ] 2024-07-02 12:16:45.288 - [任务 36][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-07-02 12:16:45.339 - [任务 36][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@116cb384: {"table":{"comment":"","id":"CLAIM1_WIM","indexList":[{"indexFields":[{"fieldAsc":true,"name":"_id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":10,"name":"CLAIM1_WIM","nameFieldMap":{"_id":{"autoInc":false,"comment":"","dataType":"varchar(24)","name":"_id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"byteRatio":3,"bytes":24,"defaultValue":1,"type":10},"virtual":false},"CLAIM_AMOUNT":{"autoInc":false,"comment":"","dataType":"double(255,0)","name":"CLAIM_AMOUNT","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"fixed":false,"maxValue":999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999,"minValue":-999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999,"precision":255,"scale":0,"type":8},"virtual":false},"CLAIM_DATE":{"autoInc":false,"comment":"","dataType":"datetime(3)","name":"CLAIM_DATE","nullable":true,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"tapType":{"defaultFraction":0,"fraction":3,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1},"virtual":false},"CLAIM_ID":{"autoInc":false,"comment":"","dataType":"varchar(100)","name":"CLAIM_ID","nullable":true,"partitionKey":false,"pos":4,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":100,"defaultValue":1,"type":10},"virtual":false},"CLAIM_REASON":{"autoInc":false,"comment":"","dataType":"varchar(140)","name":"CLAIM_REASON","nullable":true,"partitionKey":false,"pos":5,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":140,"defaultValue":1,"type":10},"virtual":false},"CLAIM_TYPE":{"autoInc":false,"comment":"","dataType":"varchar(110)","name":"CLAIM_TYPE","nullable":true,"partitionKey":false,"pos":6,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":110,"defaultValue":1,"type":10},"virtual":false},"LAST_CHANGE":{"autoInc":false,"comment":"","dataType":"datetime(3)","name":"LAST_CHANGE","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"primaryKeyPos":0,"tapType":{"defaultFraction":0,"fraction":3,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1},"virtual":false},"POLICY_ID":{"autoInc":false,"comment":"","dataType":"varchar(100)","name":"POLICY_ID","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":100,"defaultValue":1,"type":10},"virtual":false},"SETTLED_AMOUNT":{"autoInc":false,"comment":"","dataType":"double(255,0)","name":"SETTLED_AMOUNT","nullable":true,"partitionKey":false,"pos":9,"primaryKey":false,"primaryKeyPos":0,"tapType":{"fixed":false,"maxValue":999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999,"minValue":-999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999,"precision":255,"scale":0,"type":8},"virtual":false},"SETTLED_DATE":{"autoInc":false,"comment":"","dataType":"datetime(3)","name":"SETTLED_DATE","nullable":true,"partitionKey":false,"pos":10,"primaryKey":false,"primaryKeyPos":0,"tapType":{"defaultFraction":0,"fraction":3,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"_id"}],"indexMap":{"_id":{"fieldAsc":true,"name":"_id"}},"unique":true}},"tableId":"CLAIM1_WIM","type":206} 
[INFO ] 2024-07-02 12:16:45.340 - [任务 36][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM1_WIM_667413fd7b5e1f6c3b139e78_66810f4e6fad3a13e50cb996 
[INFO ] 2024-07-02 12:16:45.414 - [任务 36][SouceMysql] - Create new table schema transform finished: TapTable id CLAIM1_WIM name CLAIM1_WIM storageEngine null charset null number of fields 10 
[INFO ] 2024-07-02 12:16:45.420 - [任务 36][SouceMysql] - Table CLAIM2 is detected that it has been removed, the snapshot read will be skipped 
[INFO ] 2024-07-02 12:16:45.424 - [任务 36][SouceMysql] - Table CLAIM3 is detected that it has been removed, the snapshot read will be skipped 
[INFO ] 2024-07-02 12:16:45.425 - [任务 36][SouceMysql] - Table CLAIMBACK is detected that it has been removed, the snapshot read will be skipped 
[INFO ] 2024-07-02 12:16:45.425 - [任务 36][SouceMysql] - Starting batch read, table name: CLAIM1, offset: null 
[INFO ] 2024-07-02 12:16:45.450 - [任务 36][SouceMysql] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-07-02 12:16:45.450 - [任务 36][SouceMysql] - Query table 'CLAIM1' counts: 0 
[INFO ] 2024-07-02 12:16:45.455 - [任务 36][SouceMysql] - Table [CLAIM1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 12:16:45.455 - [任务 36][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-02 12:16:45.490 - [任务 36][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-02 12:16:45.490 - [任务 36][SouceMysql] - Query table 'CLAIM' counts: 0 
[INFO ] 2024-07-02 12:16:45.491 - [任务 36][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 12:16:45.492 - [任务 36][SouceMysql] - Starting batch read, table name: CLAIM1_WIM, offset: null 
[INFO ] 2024-07-02 12:16:45.506 - [任务 36][SouceMysql] - Table CLAIM1_WIM is going to be initial synced 
[INFO ] 2024-07-02 12:16:45.506 - [任务 36][SouceMysql] - Query table 'CLAIM1_WIM' counts: 1053 
[WARN ] 2024-07-02 12:16:45.645 - [任务 36][SourceMongo] - Drop table event will be ignored at sink node: tESTCLAIM2BBBCD 
[WARN ] 2024-07-02 12:16:45.645 - [任务 36][SourceMongo] - DDL event: TapDropTableEvent does not supported 
[WARN ] 2024-07-02 12:16:45.645 - [任务 36][SourceMongo] - Drop table event will be ignored at sink node: tESTCLAIM3BBBCD 
[WARN ] 2024-07-02 12:16:45.646 - [任务 36][SourceMongo] - DDL event: TapDropTableEvent does not supported 
[WARN ] 2024-07-02 12:16:45.647 - [任务 36][SourceMongo] - Drop table event will be ignored at sink node: BACD 
[WARN ] 2024-07-02 12:16:45.647 - [任务 36][SourceMongo] - DDL event: TapDropTableEvent does not supported 
[INFO ] 2024-07-02 12:16:45.881 - [任务 36][SouceMysql] - Table [CLAIM1_WIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 12:16:45.891 - [任务 36][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-02 12:16:45.897 - [任务 36][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-02 12:16:45.898 - [任务 36][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-02 12:16:45.898 - [任务 36][SouceMysql] - Starting stream read, table list: [CLAIM1_WIM, CLAIM2, CLAIM3, CLAIMBACK, CLAIM1, CLAIM], offset: {"filename":"binlog.000032","position":4849349,"gtidSet":""} 
[INFO ] 2024-07-02 12:16:45.974 - [任务 36][SouceMysql] - Starting mysql cdc, server name: 5ff4302f-6786-4c9c-bfcd-97ea78a8a89d 
[INFO ] 2024-07-02 12:16:45.975 - [任务 36][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1394032944
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 5ff4302f-6786-4c9c-bfcd-97ea78a8a89d
  database.port: 3306
  threadName: Debezium-Mysql-Connector-5ff4302f-6786-4c9c-bfcd-97ea78a8a89d
  database.hostname: localhost
  database.password: ********
  name: 5ff4302f-6786-4c9c-bfcd-97ea78a8a89d
  pdk.offset.string: {"name":"5ff4302f-6786-4c9c-bfcd-97ea78a8a89d","offset":{"{\"server\":\"5ff4302f-6786-4c9c-bfcd-97ea78a8a89d\"}":"{\"file\":\"binlog.000032\",\"pos\":4849349,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.CLAIM1_WIM,test2.CLAIM2,test2.CLAIM3,test2.CLAIMBACK,test2.CLAIM1,test2.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-02 12:16:46.156 - [任务 36][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM1_WIM, CLAIM2, CLAIM3, CLAIMBACK, CLAIM1, CLAIM], data change syncing 
[INFO ] 2024-07-02 12:19:03.468 - [任务 36][SouceMysql] - Node SouceMysql[48f25445-ec49-4be1-bb7a-4a1bf6b6964b] running status set to false 
[INFO ] 2024-07-02 12:19:03.470 - [任务 36][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-02 12:19:03.470 - [任务 36][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-02 12:19:03.472 - [任务 36][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-02 12:19:03.480 - [任务 36][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-48f25445-ec49-4be1-bb7a-4a1bf6b6964b 
[INFO ] 2024-07-02 12:19:03.480 - [任务 36][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-48f25445-ec49-4be1-bb7a-4a1bf6b6964b 
[INFO ] 2024-07-02 12:19:03.481 - [任务 36][SouceMysql] - Node SouceMysql[48f25445-ec49-4be1-bb7a-4a1bf6b6964b] schema data cleaned 
[INFO ] 2024-07-02 12:19:03.482 - [任务 36][SouceMysql] - Node SouceMysql[48f25445-ec49-4be1-bb7a-4a1bf6b6964b] monitor closed 
[INFO ] 2024-07-02 12:19:03.484 - [任务 36][SouceMysql] - Node SouceMysql[48f25445-ec49-4be1-bb7a-4a1bf6b6964b] close complete, cost 118 ms 
[INFO ] 2024-07-02 12:19:03.484 - [任务 36][表编辑] - Node 表编辑[9d3e4249-5054-4245-9096-1c67233a758b] running status set to false 
[INFO ] 2024-07-02 12:19:03.485 - [任务 36][表编辑] - Node 表编辑[9d3e4249-5054-4245-9096-1c67233a758b] schema data cleaned 
[INFO ] 2024-07-02 12:19:03.485 - [任务 36][表编辑] - Node 表编辑[9d3e4249-5054-4245-9096-1c67233a758b] monitor closed 
[INFO ] 2024-07-02 12:19:03.486 - [任务 36][表编辑] - Node 表编辑[9d3e4249-5054-4245-9096-1c67233a758b] close complete, cost 1 ms 
[INFO ] 2024-07-02 12:19:03.486 - [任务 36][SourceMongo] - Node SourceMongo[41e22bc5-1dde-473c-8ad6-6c34a89092f0] running status set to false 
[INFO ] 2024-07-02 12:19:03.521 - [任务 36][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-41e22bc5-1dde-473c-8ad6-6c34a89092f0 
[INFO ] 2024-07-02 12:19:03.521 - [任务 36][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-41e22bc5-1dde-473c-8ad6-6c34a89092f0 
[INFO ] 2024-07-02 12:19:03.521 - [任务 36][SourceMongo] - Node SourceMongo[41e22bc5-1dde-473c-8ad6-6c34a89092f0] schema data cleaned 
[INFO ] 2024-07-02 12:19:03.521 - [任务 36][SourceMongo] - Node SourceMongo[41e22bc5-1dde-473c-8ad6-6c34a89092f0] monitor closed 
[INFO ] 2024-07-02 12:19:03.522 - [任务 36][SourceMongo] - Node SourceMongo[41e22bc5-1dde-473c-8ad6-6c34a89092f0] close complete, cost 35 ms 
[INFO ] 2024-07-02 12:19:04.709 - [任务 36] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 12:19:04.716 - [任务 36] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@26ddd2be 
[INFO ] 2024-07-02 12:19:04.846 - [任务 36] - Stop task milestones: 66810f4e6fad3a13e50cb996(任务 36)  
[INFO ] 2024-07-02 12:19:04.846 - [任务 36] - Stopped task aspect(s) 
[INFO ] 2024-07-02 12:19:04.846 - [任务 36] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 12:19:04.868 - [任务 36] - Remove memory task client succeed, task: 任务 36[66810f4e6fad3a13e50cb996] 
[INFO ] 2024-07-02 12:19:04.870 - [任务 36] - Destroy memory task client cache succeed, task: 任务 36[66810f4e6fad3a13e50cb996] 
