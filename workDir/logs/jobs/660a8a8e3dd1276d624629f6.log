[INFO ] 2024-04-01 18:21:40.230 - [任务 40] - Start task milestones: 660a8a8e3dd1276d624629f6(任务 40) 
[INFO ] 2024-04-01 18:21:40.231 - [任务 40] - Task initialization... 
[INFO ] 2024-04-01 18:21:40.232 - [任务 40] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-01 18:21:40.232 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-01 18:21:40.442 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:21:40.443 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:21:40.601 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] preload schema finished, cost 151 ms 
[INFO ] 2024-04-01 18:21:40.604 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] preload schema finished, cost 150 ms 
[INFO ] 2024-04-01 18:21:42.203 - [任务 40][test11] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-01 18:21:42.208 - [任务 40][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-01 18:21:42.217 - [任务 40][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-01 18:21:42.219 - [任务 40][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-01 18:21:42.243 - [任务 40][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145948832,"gtidSet":""} 
[INFO ] 2024-04-01 18:21:42.248 - [任务 40][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-04-01 18:21:42.539 - [任务 40][CLAIM] - Initial sync started 
[INFO ] 2024-04-01 18:21:42.553 - [任务 40][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-01 18:21:42.553 - [任务 40][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-01 18:21:42.726 - [任务 40][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-01 18:21:42.729 - [任务 40][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:21:42.729 - [任务 40][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-01 18:21:42.734 - [任务 40][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:21:42.811 - [任务 40][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145948832,"gtidSet":""} 
[INFO ] 2024-04-01 18:21:42.811 - [任务 40][CLAIM] - Starting mysql cdc, server name: b66f51e2-ee43-4196-b009-a424ff27ee3c 
[INFO ] 2024-04-01 18:21:43.017 - [任务 40][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 450362006
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b66f51e2-ee43-4196-b009-a424ff27ee3c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b66f51e2-ee43-4196-b009-a424ff27ee3c
  database.hostname: 127.0.0.1
  database.password: ********
  name: b66f51e2-ee43-4196-b009-a424ff27ee3c
  pdk.offset.string: {"name":"b66f51e2-ee43-4196-b009-a424ff27ee3c","offset":{"{\"server\":\"b66f51e2-ee43-4196-b009-a424ff27ee3c\"}":"{\"file\":\"binlog.000020\",\"pos\":145948832,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-01 18:21:43.021 - [任务 40][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[ERROR] 2024-04-01 18:21:43.350 - [任务 40][test11] - java.lang.NullPointerException <-- Error Message -->
java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.saveToSnapshot(HazelcastTargetPdkBaseNode.java:1023)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataStartCdcEvent(HazelcastTargetPdkBaseNode.java:813)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:586)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:552)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:533)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.saveToSnapshot(HazelcastTargetPdkBaseNode.java:1086)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataStartCdcEvent(HazelcastTargetPdkBaseNode.java:813)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:586)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:552)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:533)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:477)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.saveToSnapshot(HazelcastTargetPdkBaseNode.java:1023)
	... 14 more

[INFO ] 2024-04-01 18:21:43.529 - [任务 40][test11] - Job suspend in error handle 
[INFO ] 2024-04-01 18:21:43.530 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] running status set to false 
[INFO ] 2024-04-01 18:21:43.571 - [任务 40][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-01 18:21:43.571 - [任务 40][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-01 18:21:43.584 - [任务 40][CLAIM] - Incremental sync completed 
[INFO ] 2024-04-01 18:21:43.585 - [任务 40][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-5a65d075-96cf-4eed-a8e0-a8a3e81008f4 
[INFO ] 2024-04-01 18:21:43.589 - [任务 40][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-5a65d075-96cf-4eed-a8e0-a8a3e81008f4 
[INFO ] 2024-04-01 18:21:43.589 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] schema data cleaned 
[INFO ] 2024-04-01 18:21:43.617 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] monitor closed 
[INFO ] 2024-04-01 18:21:43.618 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] close complete, cost 90 ms 
[INFO ] 2024-04-01 18:21:43.618 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] running status set to false 
[INFO ] 2024-04-01 18:21:43.668 - [任务 40][test11] - PDK connector node stopped: HazelcastTargetPdkDataNode-1c22835d-c439-4a81-b3a0-7d176e68e20b 
[INFO ] 2024-04-01 18:21:43.669 - [任务 40][test11] - PDK connector node released: HazelcastTargetPdkDataNode-1c22835d-c439-4a81-b3a0-7d176e68e20b 
[INFO ] 2024-04-01 18:21:43.669 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] schema data cleaned 
[INFO ] 2024-04-01 18:21:43.669 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] monitor closed 
[INFO ] 2024-04-01 18:21:43.878 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] close complete, cost 53 ms 
[INFO ] 2024-04-01 18:21:45.852 - [任务 40] - Task [任务 40] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-01 18:21:45.857 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-01 18:21:45.896 - [任务 40] - Stop task milestones: 660a8a8e3dd1276d624629f6(任务 40)  
[INFO ] 2024-04-01 18:21:45.896 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-04-01 18:21:45.940 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-04-01 18:21:45.940 - [任务 40] - Remove memory task client succeed, task: 任务 40[660a8a8e3dd1276d624629f6] 
[INFO ] 2024-04-01 18:21:45.940 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[660a8a8e3dd1276d624629f6] 
[INFO ] 2024-04-01 18:25:05.040 - [任务 40] - Start task milestones: 660a8a8e3dd1276d624629f6(任务 40) 
[INFO ] 2024-04-01 18:25:05.040 - [任务 40] - Task initialization... 
[INFO ] 2024-04-01 18:25:05.040 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-01 18:25:05.040 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-01 18:25:05.040 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:25:05.040 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:25:05.110 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] preload schema finished, cost 66 ms 
[INFO ] 2024-04-01 18:25:05.110 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] preload schema finished, cost 66 ms 
[INFO ] 2024-04-01 18:25:05.950 - [任务 40][test11] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-01 18:25:05.972 - [任务 40][test11] - Table "test.test11" exists, skip auto create table 
[INFO ] 2024-04-01 18:25:05.974 - [任务 40][test11] - The table test11 has already exist. 
[INFO ] 2024-04-01 18:25:06.084 - [任务 40][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-01 18:25:06.085 - [任务 40][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-01 18:25:06.086 - [任务 40][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-01 18:25:06.090 - [任务 40][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145948832,"gtidSet":""} 
[INFO ] 2024-04-01 18:25:06.175 - [任务 40][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-04-01 18:25:06.175 - [任务 40][CLAIM] - Initial sync started 
[INFO ] 2024-04-01 18:25:06.175 - [任务 40][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-01 18:25:06.244 - [任务 40][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-01 18:25:06.320 - [任务 40][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-01 18:25:06.324 - [任务 40][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:25:06.324 - [任务 40][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-01 18:25:06.325 - [任务 40][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:25:06.326 - [任务 40][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145948832,"gtidSet":""} 
[INFO ] 2024-04-01 18:25:06.382 - [任务 40][CLAIM] - Starting mysql cdc, server name: be8ec02d-5a1f-4c39-8020-54f6ea724de8 
[INFO ] 2024-04-01 18:25:06.382 - [任务 40][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 424253069
  time.precision.mode: adaptive_time_microseconds
  database.server.name: be8ec02d-5a1f-4c39-8020-54f6ea724de8
  database.port: 3306
  threadName: Debezium-Mysql-Connector-be8ec02d-5a1f-4c39-8020-54f6ea724de8
  database.hostname: 127.0.0.1
  database.password: ********
  name: be8ec02d-5a1f-4c39-8020-54f6ea724de8
  pdk.offset.string: {"name":"be8ec02d-5a1f-4c39-8020-54f6ea724de8","offset":{"{\"server\":\"be8ec02d-5a1f-4c39-8020-54f6ea724de8\"}":"{\"file\":\"binlog.000020\",\"pos\":145948832,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-01 18:25:06.586 - [任务 40][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[ERROR] 2024-04-01 18:25:50.076 - [任务 40][test11] - java.lang.NullPointerException <-- Error Message -->
java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.saveToSnapshot(HazelcastTargetPdkBaseNode.java:1023)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataStartCdcEvent(HazelcastTargetPdkBaseNode.java:813)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:586)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:552)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:533)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.saveToSnapshot(HazelcastTargetPdkBaseNode.java:1086)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataStartCdcEvent(HazelcastTargetPdkBaseNode.java:813)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:586)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:552)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:533)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:477)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.saveToSnapshot(HazelcastTargetPdkBaseNode.java:1023)
	... 14 more

[INFO ] 2024-04-01 18:25:50.284 - [任务 40][test11] - Job suspend in error handle 
[INFO ] 2024-04-01 18:25:50.582 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] running status set to false 
[INFO ] 2024-04-01 18:25:50.646 - [任务 40][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-01 18:25:50.646 - [任务 40][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-01 18:25:50.646 - [任务 40][CLAIM] - Incremental sync completed 
[INFO ] 2024-04-01 18:25:50.651 - [任务 40][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-5a65d075-96cf-4eed-a8e0-a8a3e81008f4 
[INFO ] 2024-04-01 18:25:50.652 - [任务 40][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-5a65d075-96cf-4eed-a8e0-a8a3e81008f4 
[INFO ] 2024-04-01 18:25:50.654 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] schema data cleaned 
[INFO ] 2024-04-01 18:25:50.654 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] monitor closed 
[INFO ] 2024-04-01 18:26:34.050 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] close complete, cost 81 ms 
[INFO ] 2024-04-01 18:26:34.115 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] running status set to false 
[INFO ] 2024-04-01 18:26:34.228 - [任务 40] - Task [任务 40] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-01 18:26:34.228 - [任务 40][test11] - PDK connector node stopped: HazelcastTargetPdkDataNode-1c22835d-c439-4a81-b3a0-7d176e68e20b 
[INFO ] 2024-04-01 18:26:34.237 - [任务 40][test11] - PDK connector node released: HazelcastTargetPdkDataNode-1c22835d-c439-4a81-b3a0-7d176e68e20b 
[INFO ] 2024-04-01 18:26:34.240 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] schema data cleaned 
[INFO ] 2024-04-01 18:26:34.242 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] monitor closed 
[INFO ] 2024-04-01 18:26:34.247 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] close complete, cost 43587 ms 
[INFO ] 2024-04-01 18:26:39.249 - [任务 40] - Task [任务 40] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-01 18:26:39.267 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-01 18:26:39.268 - [任务 40] - Stop task milestones: 660a8a8e3dd1276d624629f6(任务 40)  
[INFO ] 2024-04-01 18:26:39.299 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-04-01 18:26:39.299 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-04-01 18:26:39.317 - [任务 40] - Remove memory task client succeed, task: 任务 40[660a8a8e3dd1276d624629f6] 
[INFO ] 2024-04-01 18:26:39.319 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[660a8a8e3dd1276d624629f6] 
[INFO ] 2024-04-01 18:29:16.352 - [任务 40] - Start task milestones: 660a8a8e3dd1276d624629f6(任务 40) 
[INFO ] 2024-04-01 18:29:16.353 - [任务 40] - Task initialization... 
[INFO ] 2024-04-01 18:29:16.353 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-01 18:29:16.354 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-01 18:29:16.946 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:29:16.948 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:29:17.106 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] preload schema finished, cost 158 ms 
[INFO ] 2024-04-01 18:29:17.312 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] preload schema finished, cost 160 ms 
[INFO ] 2024-04-01 18:29:18.156 - [任务 40][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-01 18:29:18.157 - [任务 40][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-01 18:29:18.158 - [任务 40][test11] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-01 18:29:18.159 - [任务 40][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-01 18:29:18.182 - [任务 40][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145948832,"gtidSet":""} 
[INFO ] 2024-04-01 18:29:18.185 - [任务 40][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-04-01 18:29:18.195 - [任务 40][test11] - Table "test.test11" exists, skip auto create table 
[INFO ] 2024-04-01 18:29:18.198 - [任务 40][test11] - The table test11 has already exist. 
[INFO ] 2024-04-01 18:29:18.358 - [任务 40][CLAIM] - Initial sync started 
[INFO ] 2024-04-01 18:29:18.358 - [任务 40][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-01 18:29:18.373 - [任务 40][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-01 18:29:18.531 - [任务 40][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-01 18:29:18.534 - [任务 40][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:29:18.534 - [任务 40][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-01 18:29:18.535 - [任务 40][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:29:18.540 - [任务 40][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145948832,"gtidSet":""} 
[INFO ] 2024-04-01 18:29:18.635 - [任务 40][CLAIM] - Starting mysql cdc, server name: aeb9aa9d-7e83-408e-9697-97fd0210d6a8 
[INFO ] 2024-04-01 18:29:18.637 - [任务 40][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 497567190
  time.precision.mode: adaptive_time_microseconds
  database.server.name: aeb9aa9d-7e83-408e-9697-97fd0210d6a8
  database.port: 3306
  threadName: Debezium-Mysql-Connector-aeb9aa9d-7e83-408e-9697-97fd0210d6a8
  database.hostname: 127.0.0.1
  database.password: ********
  name: aeb9aa9d-7e83-408e-9697-97fd0210d6a8
  pdk.offset.string: {"name":"aeb9aa9d-7e83-408e-9697-97fd0210d6a8","offset":{"{\"server\":\"aeb9aa9d-7e83-408e-9697-97fd0210d6a8\"}":"{\"file\":\"binlog.000020\",\"pos\":145948832,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-01 18:29:18.825 - [任务 40][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-01 18:30:11.887 - [任务 40][CLAIM] - Read DDL: alter table CLAIM rename column `name1` to `name`, about to be packaged as some event(s) 
[INFO ] 2024-04-01 18:30:11.893 - [任务 40][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='aeb9aa9d-7e83-408e-9697-97fd0210d6a8', offset={{"server":"aeb9aa9d-7e83-408e-9697-97fd0210d6a8"}={"ts_sec":1711967411,"file":"binlog.000020","pos":145949093,"server_id":1}}} 
[INFO ] 2024-04-01 18:30:11.893 - [任务 40][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@7e064f5d} 
[INFO ] 2024-04-01 18:30:12.097 - [任务 40][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660a8a8e3dd1276d624629f6 
[INFO ] 2024-04-01 18:30:12.461 - [任务 40][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-04-01 18:31:59.557 - [任务 40] - Stop task milestones: 660a8a8e3dd1276d624629f6(任务 40)  
[INFO ] 2024-04-01 18:31:59.557 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] running status set to false 
[INFO ] 2024-04-01 18:31:59.641 - [任务 40][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-01 18:31:59.642 - [任务 40][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-5a65d075-96cf-4eed-a8e0-a8a3e81008f4 
[INFO ] 2024-04-01 18:31:59.642 - [任务 40][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-5a65d075-96cf-4eed-a8e0-a8a3e81008f4 
[INFO ] 2024-04-01 18:31:59.643 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] schema data cleaned 
[INFO ] 2024-04-01 18:31:59.643 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] monitor closed 
[INFO ] 2024-04-01 18:31:59.645 - [任务 40][CLAIM] - Node CLAIM[5a65d075-96cf-4eed-a8e0-a8a3e81008f4] close complete, cost 99 ms 
[INFO ] 2024-04-01 18:31:59.716 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] running status set to false 
[INFO ] 2024-04-01 18:31:59.717 - [任务 40][test11] - PDK connector node stopped: HazelcastTargetPdkDataNode-1c22835d-c439-4a81-b3a0-7d176e68e20b 
[INFO ] 2024-04-01 18:31:59.720 - [任务 40][test11] - PDK connector node released: HazelcastTargetPdkDataNode-1c22835d-c439-4a81-b3a0-7d176e68e20b 
[INFO ] 2024-04-01 18:31:59.720 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] schema data cleaned 
[INFO ] 2024-04-01 18:31:59.720 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] monitor closed 
[INFO ] 2024-04-01 18:31:59.722 - [任务 40][test11] - Node test11[1c22835d-c439-4a81-b3a0-7d176e68e20b] close complete, cost 74 ms 
[INFO ] 2024-04-01 18:32:03.012 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-01 18:32:03.013 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-04-01 18:32:03.017 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-04-01 18:32:03.105 - [任务 40] - Remove memory task client succeed, task: 任务 40[660a8a8e3dd1276d624629f6] 
[INFO ] 2024-04-01 18:32:03.106 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[660a8a8e3dd1276d624629f6] 
