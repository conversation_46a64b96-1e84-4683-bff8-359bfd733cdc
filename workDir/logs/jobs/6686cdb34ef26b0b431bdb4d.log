[INFO ] 2024-07-05 00:28:36.031 - [Heartbeat-Source<PERSON><PERSON><PERSON>] - Start task milestones: 6686cdb34ef26b0b431bdb4d(Heartbeat-SourceOracle) 
[INFO ] 2024-07-05 00:28:36.334 - [Heartbeat-SourceO<PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-05 00:28:36.426 - [Heartbeat-SourceOracle] - The engine receives Heartbeat-SourceOracle task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 00:28:36.509 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] start preload schema,table counts: 1 
[INFO ] 2024-07-05 00:28:36.510 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] start preload schema,table counts: 1 
[INFO ] 2024-07-05 00:28:36.510 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 00:28:36.510 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 00:28:37.518 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-05 00:28:37.518 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-05 00:28:37.518 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 00:28:37.566 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1720110517517,"lastTimes":1720110517517,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-05 00:28:37.573 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-05 00:28:37.573 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-05 00:28:37.583 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-05 00:28:37.586 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-05 00:28:37.589 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-05 00:28:37.590 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1720110517517,"lastTimes":1720110517517,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-05 00:28:37.590 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-05 00:28:37.590 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-05 00:28:37.982 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 02:04:02.602 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] running status set to false 
[INFO ] 2024-07-05 11:27:00.457 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-05 11:27:00.459 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-25caab4f-c84d-4686-b4cd-eab64ceaf077 
[INFO ] 2024-07-05 11:27:00.462 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-25caab4f-c84d-4686-b4cd-eab64ceaf077 
[INFO ] 2024-07-05 11:27:00.462 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] schema data cleaned 
[INFO ] 2024-07-05 11:27:00.467 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] monitor closed 
[INFO ] 2024-07-05 11:27:00.472 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] close complete, cost 73 ms 
[INFO ] 2024-07-05 11:27:00.474 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] running status set to false 
[INFO ] 2024-07-05 11:27:00.486 - [Heartbeat-SourceOracle] - Start task milestones: 6686cdb34ef26b0b431bdb4d(Heartbeat-SourceOracle) 
[INFO ] 2024-07-05 11:27:02.388 - [Heartbeat-SourceOracle] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 11:27:02.488 - [Heartbeat-SourceOracle] - The engine receives Heartbeat-SourceOracle task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 11:27:03.357 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] start preload schema,table counts: 1 
[INFO ] 2024-07-05 11:27:03.358 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 11:27:03.382 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] start preload schema,table counts: 1 
[INFO ] 2024-07-05 11:27:03.382 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 11:27:05.000 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-05 11:27:05.000 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-05 11:27:05.011 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-05 11:27:05.089 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1720110517566,"lastTimes":1720110517571,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1720110517517,"lastTimes":1720116241617,"lastTN":5699,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":5698,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-05 11:27:05.213 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1720110517517,"lastTimes":1720116241617,"lastTN":5699,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":5698,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-05 11:27:05.216 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-05 11:27:05.216 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-05 11:27:05.576 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 12:08:05.781 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] running status set to false 
[INFO ] 2024-07-05 12:08:05.783 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-05 12:08:05.791 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-25caab4f-c84d-4686-b4cd-eab64ceaf077 
[INFO ] 2024-07-05 12:08:05.791 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-25caab4f-c84d-4686-b4cd-eab64ceaf077 
[INFO ] 2024-07-05 12:08:05.791 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] schema data cleaned 
[INFO ] 2024-07-05 12:08:05.791 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] monitor closed 
[INFO ] 2024-07-05 12:08:05.792 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25caab4f-c84d-4686-b4cd-eab64ceaf077] close complete, cost 14 ms 
[INFO ] 2024-07-05 12:08:05.832 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] running status set to false 
[INFO ] 2024-07-05 12:08:05.833 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-fb8454f2-0771-43ad-9c11-7b4f94cafbb0 
[INFO ] 2024-07-05 12:08:05.833 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-fb8454f2-0771-43ad-9c11-7b4f94cafbb0 
[INFO ] 2024-07-05 12:08:05.833 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] schema data cleaned 
[INFO ] 2024-07-05 12:08:05.833 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] monitor closed 
[INFO ] 2024-07-05 12:08:05.834 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fb8454f2-0771-43ad-9c11-7b4f94cafbb0] close complete, cost 41 ms 
[INFO ] 2024-07-05 12:08:06.280 - [Heartbeat-SourceOracle] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:08:06.280 - [Heartbeat-SourceOracle] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@25214d24 
[INFO ] 2024-07-05 12:08:06.280 - [Heartbeat-SourceOracle] - Stop task milestones: 6686cdb34ef26b0b431bdb4d(Heartbeat-SourceOracle)  
[INFO ] 2024-07-05 12:08:06.394 - [Heartbeat-SourceOracle] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:08:06.394 - [Heartbeat-SourceOracle] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:08:06.412 - [Heartbeat-SourceOracle] - Remove memory task client succeed, task: Heartbeat-SourceOracle[6686cdb34ef26b0b431bdb4d] 
[INFO ] 2024-07-05 12:08:06.415 - [Heartbeat-SourceOracle] - Destroy memory task client cache succeed, task: Heartbeat-SourceOracle[6686cdb34ef26b0b431bdb4d] 
