[INFO ] 2024-11-01 10:13:28.950 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 10:13:28.987 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 10:13:29.748 - [任务 10] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-11-01 10:13:29.749 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 10:13:30.133 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:13:30.143 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:13:30.144 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 10:13:30.144 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 10:13:31.069 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 10:13:31.078 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 10:13:31.211 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 10:13:31.212 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 10:13:31.213 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 10:13:31.229 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 10:13:56.140 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 10:13:56.197 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 10:13:56.205 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 10:14:18.608 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table 
[ERROR] 2024-11-01 10:14:18.695 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:362)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 10:14:18.739 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 10:14:18.740 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 10:14:18.743 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 10:14:18.754 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 10:14:18.755 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 10:14:18.775 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 10:14:18.776 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:14:18.777 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 10:14:18.777 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:14:18.834 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 10:14:18.834 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 0f273ba5-f388-4340-9130-74358bdca4d3 
[INFO ] 2024-11-01 10:14:19.041 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"0f273ba5-f388-4340-9130-74358bdca4d3","offset":{"{\"server\":\"0f273ba5-f388-4340-9130-74358bdca4d3\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1657272350
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0f273ba5-f388-4340-9130-74358bdca4d3
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0f273ba5-f388-4340-9130-74358bdca4d3
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 0f273ba5-f388-4340-9130-74358bdca4d3
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 10:14:19.096 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 10:14:19.098 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 10:14:19.655 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 10:14:19.657 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 10:14:19.661 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 10:14:19.701 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:14:19.703 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:14:19.706 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 10:14:19.707 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 10:14:19.720 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 627 ms 
[INFO ] 2024-11-01 10:14:19.721 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 10:14:19.736 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:14:19.739 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:14:19.739 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 10:14:19.740 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 10:14:19.741 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 24 ms 
[INFO ] 2024-11-01 10:14:23.496 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 10:14:23.546 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 10:14:23.557 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@329d5a4a 
[INFO ] 2024-11-01 10:14:23.716 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 10:14:23.718 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 10:14:23.721 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 10:14:23.754 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 10:14:23.754 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 10:15:42.093 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 10:15:42.095 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 10:15:42.300 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 10:15:42.506 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 10:15:42.522 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:15:42.523 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:15:42.524 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 10:15:42.524 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 10:15:43.457 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 10:15:43.468 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 10:15:43.552 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 10:15:43.557 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 10:15:43.557 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 10:15:43.562 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 10:15:43.563 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 10:15:47.786 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 10:18:19.559 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 10:18:19.667 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table 
[INFO ] 2024-11-01 10:18:19.668 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 10:18:19.668 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 10:18:19.668 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 10:18:19.668 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 10:18:19.677 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 10:18:19.683 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:18:19.686 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 10:18:19.686 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:18:19.687 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[ERROR] 2024-11-01 10:18:19.700 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:362)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 10:18:19.703 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 10:18:19.764 - [任务 10][Mysql3306] - Starting mysql cdc, server name: b787d7c6-5f5a-41eb-a936-7166ee63c306 
[INFO ] 2024-11-01 10:18:19.765 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"b787d7c6-5f5a-41eb-a936-7166ee63c306","offset":{"{\"server\":\"b787d7c6-5f5a-41eb-a936-7166ee63c306\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1165245122
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b787d7c6-5f5a-41eb-a936-7166ee63c306
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b787d7c6-5f5a-41eb-a936-7166ee63c306
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: b787d7c6-5f5a-41eb-a936-7166ee63c306
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 10:18:19.984 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 10:18:20.048 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 10:18:20.048 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 10:18:20.048 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 10:18:20.049 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 10:18:20.071 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:18:20.071 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:18:20.071 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 10:18:20.072 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 10:18:20.075 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 80 ms 
[INFO ] 2024-11-01 10:18:20.075 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 10:18:20.082 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:18:20.083 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:18:20.083 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 10:18:20.083 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 10:18:20.084 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 10 ms 
[INFO ] 2024-11-01 10:18:24.565 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 10:18:24.578 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 10:18:24.579 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@22d566c0 
[INFO ] 2024-11-01 10:18:24.717 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 10:18:24.717 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 10:18:24.717 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 10:18:24.733 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 10:18:24.735 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 10:22:03.343 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 10:22:03.343 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 10:22:03.490 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 10:22:03.490 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 10:22:03.536 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:22:03.536 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:22:03.536 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 10:22:03.537 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 10:22:04.442 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 10:22:04.447 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 10:24:48.024 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 10:24:48.059 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 10:24:48.074 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 10:24:48.074 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 10:24:48.095 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 10:24:48.095 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table 
[ERROR] 2024-11-01 10:24:48.136 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:362)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 10:24:48.169 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 10:24:48.169 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 10:24:48.170 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 10:24:48.267 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 10:24:48.268 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 10:24:48.268 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 10:24:48.268 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 10:24:48.270 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 10:24:48.271 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:24:48.274 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 10:24:48.280 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:24:48.281 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 10:24:48.307 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 61188cb3-c18e-4b46-8235-4a1ae6c38a72 
[INFO ] 2024-11-01 10:24:48.311 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"61188cb3-c18e-4b46-8235-4a1ae6c38a72","offset":{"{\"server\":\"61188cb3-c18e-4b46-8235-4a1ae6c38a72\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1124555611
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 61188cb3-c18e-4b46-8235-4a1ae6c38a72
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-61188cb3-c18e-4b46-8235-4a1ae6c38a72
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 61188cb3-c18e-4b46-8235-4a1ae6c38a72
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 10:24:48.518 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 10:24:48.775 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 10:24:48.779 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 10:24:48.779 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 10:24:48.779 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 10:24:48.799 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:24:48.804 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:24:48.804 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 10:24:48.804 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 10:24:48.806 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 71 ms 
[INFO ] 2024-11-01 10:24:48.807 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 10:24:48.816 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:24:48.816 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:24:48.818 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 10:24:48.819 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 10:24:49.023 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 18 ms 
[INFO ] 2024-11-01 10:24:52.913 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 10:24:52.922 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 10:24:52.932 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49191732 
[INFO ] 2024-11-01 10:24:52.932 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 10:24:53.101 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 10:24:53.104 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 10:24:53.122 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 10:24:53.329 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 10:29:02.258 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 10:29:02.408 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 10:29:02.409 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 10:29:02.494 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 10:29:02.494 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:29:02.496 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 10:29:02.496 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 10:29:02.498 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 1 ms 
[INFO ] 2024-11-01 10:29:03.384 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 10:29:03.387 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 10:29:03.495 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 10:30:26.111 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 10:30:26.111 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table 
[ERROR] 2024-11-01 10:30:26.222 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:362)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 10:30:26.223 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 10:30:26.223 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 10:30:26.224 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 10:30:26.225 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 10:30:26.241 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 10:30:26.242 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 10:30:26.354 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 10:30:26.356 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 10:30:26.360 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 10:30:26.360 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 10:30:26.362 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 10:30:26.363 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:30:26.365 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 10:30:26.380 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 10:30:26.388 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 10:30:26.404 - [任务 10][Mysql3306] - Starting mysql cdc, server name: acc1e13b-33eb-4cdd-85c1-4c34bcde6007 
[INFO ] 2024-11-01 10:30:26.407 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"acc1e13b-33eb-4cdd-85c1-4c34bcde6007","offset":{"{\"server\":\"acc1e13b-33eb-4cdd-85c1-4c34bcde6007\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2022636305
  time.precision.mode: adaptive_time_microseconds
  database.server.name: acc1e13b-33eb-4cdd-85c1-4c34bcde6007
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-acc1e13b-33eb-4cdd-85c1-4c34bcde6007
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: acc1e13b-33eb-4cdd-85c1-4c34bcde6007
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 10:30:26.617 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 10:30:26.870 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 10:30:26.872 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 10:30:26.884 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 10:30:26.886 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 10:30:26.903 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:30:26.904 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 10:30:26.904 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 10:30:26.904 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 10:30:26.910 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 90 ms 
[INFO ] 2024-11-01 10:30:26.917 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 10:30:26.926 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:30:26.927 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 10:30:26.927 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 10:30:26.931 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 10:30:26.934 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 18 ms 
[INFO ] 2024-11-01 10:30:31.124 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 10:30:31.125 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 10:30:31.126 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@21a4fd37 
[INFO ] 2024-11-01 10:30:31.127 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 10:30:31.298 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 10:30:31.298 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 10:30:31.317 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 10:30:31.318 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 11:21:31.198 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 11:21:31.199 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 11:21:31.952 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 11:21:32.155 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 11:21:32.355 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 11:21:32.355 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 11:21:32.356 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 11:21:32.356 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-01 11:21:33.603 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 11:21:33.620 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 11:21:33.628 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 11:21:33.630 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 11:21:33.632 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 11:21:33.663 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 11:21:33.663 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 11:21:33.758 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 11:21:33.759 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 11:21:33.761 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 11:21:33.773 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 11:21:33.798 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 11:21:33.799 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 11:21:33.801 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 11:21:33.801 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 11:21:33.856 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 11:21:33.859 - [任务 10][Mysql3306] - Starting mysql cdc, server name: d1d3b59a-e962-45d7-aa84-296a4408ce69 
[INFO ] 2024-11-01 11:21:33.873 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 11:21:35.105 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 11:24:58.351 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table 
[INFO ] 2024-11-01 11:24:58.377 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"d1d3b59a-e962-45d7-aa84-296a4408ce69","offset":{"{\"server\":\"d1d3b59a-e962-45d7-aa84-296a4408ce69\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: *********
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d1d3b59a-e962-45d7-aa84-296a4408ce69
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d1d3b59a-e962-45d7-aa84-296a4408ce69
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: d1d3b59a-e962-45d7-aa84-296a4408ce69
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[ERROR] 2024-11-01 11:24:58.394 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:362)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 11:24:58.395 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 11:24:58.629 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 11:24:58.833 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 11:24:59.190 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 11:24:59.199 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 11:24:59.203 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 11:24:59.228 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 11:24:59.234 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 11:24:59.241 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 11:24:59.245 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 11:24:59.249 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 615 ms 
[INFO ] 2024-11-01 11:24:59.249 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 11:24:59.283 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 11:24:59.285 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 11:24:59.285 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 11:24:59.285 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 11:24:59.489 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 40 ms 
[INFO ] 2024-11-01 11:25:03.184 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 11:25:03.234 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 11:25:03.241 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@25184cfe 
[INFO ] 2024-11-01 11:25:03.450 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 11:25:03.450 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 11:25:03.488 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 11:25:03.489 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 11:25:03.489 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 11:29:05.329 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 11:29:05.330 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 11:29:05.531 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 11:29:05.607 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 11:29:05.609 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 11:29:05.609 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 11:29:05.612 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 11:29:05.612 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 11:29:06.513 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 11:29:06.656 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 11:29:06.656 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 11:29:11.698 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 11:29:11.769 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 11:29:11.769 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 11:30:33.977 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 11:31:56.595 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table 
[INFO ] 2024-11-01 11:32:37.279 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[ERROR] 2024-11-01 11:32:37.283 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:362)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 11:32:37.284 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 11:32:37.291 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 11:32:37.498 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 11:32:37.499 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 11:32:37.504 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 11:32:37.504 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 11:32:37.505 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 11:32:37.505 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 11:32:37.508 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 11:32:37.509 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 11:32:37.559 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 11:32:37.560 - [任务 10][Mysql3306] - Starting mysql cdc, server name: eb1683d4-6455-456e-bb18-e82d7b3ab883 
[INFO ] 2024-11-01 11:32:37.629 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"eb1683d4-6455-456e-bb18-e82d7b3ab883","offset":{"{\"server\":\"eb1683d4-6455-456e-bb18-e82d7b3ab883\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1768102088
  time.precision.mode: adaptive_time_microseconds
  database.server.name: eb1683d4-6455-456e-bb18-e82d7b3ab883
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-eb1683d4-6455-456e-bb18-e82d7b3ab883
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: eb1683d4-6455-456e-bb18-e82d7b3ab883
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 11:32:37.630 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 11:32:38.036 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 11:32:38.070 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 11:32:38.074 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 11:32:38.083 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 11:32:38.100 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 11:32:38.100 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 11:32:38.101 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 11:32:38.104 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 11:32:38.105 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 156 ms 
[INFO ] 2024-11-01 11:32:38.105 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 11:32:38.129 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 11:32:38.130 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 11:32:38.130 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 11:32:38.131 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 11:32:38.148 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 38 ms 
[INFO ] 2024-11-01 11:32:42.251 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 11:32:42.261 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 11:32:42.262 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7956e177 
[INFO ] 2024-11-01 11:32:42.263 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 11:32:42.393 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 11:32:42.394 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 11:32:42.417 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 11:32:42.418 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 11:55:42.873 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 11:55:42.877 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 11:55:43.486 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 11:55:43.539 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 11:55:43.926 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 11:55:43.927 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 11:55:43.927 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 11:55:43.927 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 11:55:45.145 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 11:55:45.145 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 11:55:45.149 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 11:55:45.150 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 11:55:45.151 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 11:55:45.187 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 11:55:45.193 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 11:55:45.271 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 11:55:45.273 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 11:55:45.278 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 11:55:45.279 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 11:55:45.294 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 11:55:45.299 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 11:55:45.299 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 11:55:45.299 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 11:55:45.355 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 11:55:45.356 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 6bfca439-07e5-466f-8a44-20a41c033a86 
[INFO ] 2024-11-01 11:55:45.362 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 11:55:45.368 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 11:56:24.939 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: wimcustomertestio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@20c4eccb: {"indexList":[{"indexFields":[{"fieldAsc":true},{"fieldAsc":true,"name":"id"}],"name":"idx_phone_prefix","primary":false,"unique":false}],"tableId":"wimcustomertest","time":1730433345360,"type":101}
 
[INFO ] 2024-11-01 11:56:24.953 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"6bfca439-07e5-466f-8a44-20a41c033a86","offset":{"{\"server\":\"6bfca439-07e5-466f-8a44-20a41c033a86\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1534418453
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6bfca439-07e5-466f-8a44-20a41c033a86
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6bfca439-07e5-466f-8a44-20a41c033a86
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 6bfca439-07e5-466f-8a44-20a41c033a86
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[ERROR] 2024-11-01 11:56:25.002 - [任务 10][Mysql3307] - Table name: wimcustomertestio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@20c4eccb: {"indexList":[{"indexFields":[{"fieldAsc":true},{"fieldAsc":true,"name":"id"}],"name":"idx_phone_prefix","primary":false,"unique":false}],"tableId":"wimcustomertest","time":1730433345360,"type":101}
 <-- Error Message -->
Table name: wimcustomertestio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@20c4eccb: {"indexList":[{"indexFields":[{"fieldAsc":true},{"fieldAsc":true,"name":"id"}],"name":"idx_phone_prefix","primary":false,"unique":false}],"tableId":"wimcustomertest","time":1730433345360,"type":101}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Table name: wimcustomertest
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:379)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:210)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:154)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:142)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:107)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:363)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:359)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:368)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 23 more
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:370)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 11:56:25.003 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 11:56:25.206 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 11:56:25.416 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 11:56:25.673 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 11:56:25.674 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 11:56:25.696 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 11:56:25.698 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 11:56:25.698 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 11:56:25.702 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 11:56:25.702 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 11:56:25.716 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 378 ms 
[INFO ] 2024-11-01 11:56:25.723 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 11:56:25.739 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 11:56:25.741 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 11:56:25.742 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 11:56:25.742 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 11:56:25.743 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 32 ms 
[INFO ] 2024-11-01 11:56:29.929 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 11:56:29.957 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 11:56:29.960 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@626ef2e6 
[INFO ] 2024-11-01 11:56:30.108 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 11:56:30.111 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 11:56:30.165 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 11:56:30.166 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 11:56:30.167 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 12:05:12.712 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 12:05:12.715 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 12:05:12.919 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 12:05:12.989 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 12:05:12.990 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 12:05:12.990 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 12:05:12.991 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 12:05:13.197 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 12:05:13.938 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 12:05:13.939 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 12:05:13.939 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 12:05:13.944 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 12:05:13.944 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 12:05:14.011 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 12:05:14.016 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 12:05:14.017 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 12:05:14.026 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 12:05:14.026 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 12:05:14.028 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 12:05:14.028 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 12:05:14.029 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 12:05:14.029 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 12:05:14.053 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 67e49a25-a7ba-450a-9e15-270a09b3a856 
[INFO ] 2024-11-01 12:05:14.053 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"67e49a25-a7ba-450a-9e15-270a09b3a856","offset":{"{\"server\":\"67e49a25-a7ba-450a-9e15-270a09b3a856\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1309965887
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 67e49a25-a7ba-450a-9e15-270a09b3a856
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-67e49a25-a7ba-450a-9e15-270a09b3a856
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 67e49a25-a7ba-450a-9e15-270a09b3a856
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 12:05:14.134 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 12:05:14.134 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 12:05:14.134 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 12:05:14.308 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 12:05:14.311 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 12:05:54.204 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: wimcustomertestio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2fa1effd: {"indexList":[{"indexFields":[{"fieldAsc":true},{"fieldAsc":true,"name":"id"}],"name":"idx_phone_prefix","primary":false,"unique":false}],"tableId":"wimcustomertest","time":1730433914305,"type":101}
 
[ERROR] 2024-11-01 12:05:54.223 - [任务 10][Mysql3307] - Table name: wimcustomertestio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2fa1effd: {"indexList":[{"indexFields":[{"fieldAsc":true},{"fieldAsc":true,"name":"id"}],"name":"idx_phone_prefix","primary":false,"unique":false}],"tableId":"wimcustomertest","time":1730433914305,"type":101}
 <-- Error Message -->
Table name: wimcustomertestio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2fa1effd: {"indexList":[{"indexFields":[{"fieldAsc":true},{"fieldAsc":true,"name":"id"}],"name":"idx_phone_prefix","primary":false,"unique":false}],"tableId":"wimcustomertest","time":1730433914305,"type":101}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Table name: wimcustomertest
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:379)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:210)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:154)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:142)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:107)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:363)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:359)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:368)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 23 more
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:370)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 12:05:54.224 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 12:05:54.607 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 12:05:54.663 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 12:05:54.669 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 12:05:54.676 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 12:05:54.697 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 12:05:54.697 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 12:05:54.698 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 12:05:54.698 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 12:05:54.703 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 98 ms 
[INFO ] 2024-11-01 12:05:54.703 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 12:05:54.715 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 12:05:54.715 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 12:05:54.716 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 12:05:54.716 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 12:05:54.926 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 15 ms 
[INFO ] 2024-11-01 12:05:59.169 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 12:05:59.176 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 12:05:59.179 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@712e223c 
[INFO ] 2024-11-01 12:05:59.301 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 12:05:59.301 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 12:05:59.325 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 12:05:59.328 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 12:05:59.328 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 12:17:31.530 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 12:17:31.736 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 12:17:31.819 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 12:17:31.820 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 12:17:31.860 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 12:17:31.861 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 12:17:31.861 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 12:17:31.861 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 12:17:32.798 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 12:17:32.799 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 12:17:32.799 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 12:17:32.803 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 12:17:32.864 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 12:17:32.868 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 12:17:32.877 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 12:17:32.878 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 12:17:32.883 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 12:17:32.883 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 12:17:32.891 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 12:17:32.894 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 12:17:32.894 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 12:17:32.894 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 12:17:32.917 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 0c5a6e34-ada5-49b3-b119-dd3b0ac57b55 
[INFO ] 2024-11-01 12:17:32.918 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"0c5a6e34-ada5-49b3-b119-dd3b0ac57b55","offset":{"{\"server\":\"0c5a6e34-ada5-49b3-b119-dd3b0ac57b55\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 410391845
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0c5a6e34-ada5-49b3-b119-dd3b0ac57b55
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0c5a6e34-ada5-49b3-b119-dd3b0ac57b55
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 0c5a6e34-ada5-49b3-b119-dd3b0ac57b55
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 12:17:32.974 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 12:17:32.975 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 12:17:32.975 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 12:17:33.163 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length 
[ERROR] 2024-11-01 12:17:33.164 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$8(HazelcastTargetPdkDataNode.java:278)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:208)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:154)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:142)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:107)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$7(HazelcastTargetPdkDataNode.java:280)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-11-01 12:17:33.350 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 12:17:33.350 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 12:17:33.377 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 12:17:33.377 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 12:17:33.398 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 12:17:33.398 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 12:17:33.398 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 12:17:33.398 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 12:17:33.404 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 12:17:33.410 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 56 ms 
[INFO ] 2024-11-01 12:17:33.410 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 12:17:33.414 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 12:17:33.414 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 12:17:33.415 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 12:17:33.415 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 12:17:33.416 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 11 ms 
[INFO ] 2024-11-01 12:17:34.891 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 12:17:34.892 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 12:17:34.895 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1b5b7c16 
[INFO ] 2024-11-01 12:17:34.895 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 12:17:35.020 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 12:17:35.020 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 12:17:35.050 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 12:17:35.050 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 14:29:36.810 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 14:29:36.810 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 14:29:36.910 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 14:29:36.986 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 14:29:36.987 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 14:29:36.987 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 14:29:36.987 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 14:29:36.987 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 14:29:37.976 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 14:29:37.980 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 14:29:38.089 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length 
[ERROR] 2024-11-01 14:29:38.092 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$8(HazelcastTargetPdkDataNode.java:278)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:208)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:154)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:142)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:107)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$7(HazelcastTargetPdkDataNode.java:280)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-11-01 14:29:38.121 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 14:29:38.121 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 14:29:38.121 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 14:29:38.121 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 14:29:38.126 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 14:29:38.127 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 14:29:38.194 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 14:29:38.194 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 14:29:38.195 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 14:29:38.197 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 14:29:38.198 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 14:29:38.198 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 14:29:38.198 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 14:29:38.199 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 14:29:38.232 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 14:29:38.233 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 6803e081-9792-482d-ba1e-29f75a75de35 
[INFO ] 2024-11-01 14:29:38.298 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"6803e081-9792-482d-ba1e-29f75a75de35","offset":{"{\"server\":\"6803e081-9792-482d-ba1e-29f75a75de35\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 175115230
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6803e081-9792-482d-ba1e-29f75a75de35
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6803e081-9792-482d-ba1e-29f75a75de35
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 6803e081-9792-482d-ba1e-29f75a75de35
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 14:29:38.299 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 14:29:38.803 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 14:29:38.810 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 14:29:38.810 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 14:29:38.829 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 14:29:38.829 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 14:29:38.829 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 14:29:38.829 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 14:29:38.832 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 14:29:38.838 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 146 ms 
[INFO ] 2024-11-01 14:29:38.840 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 14:29:38.846 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 14:29:38.846 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 14:29:38.846 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 14:29:38.847 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 14:29:39.049 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 15 ms 
[INFO ] 2024-11-01 14:29:39.050 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 14:29:39.060 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 14:29:39.062 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1fb56bda 
[INFO ] 2024-11-01 14:29:39.062 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 14:29:39.205 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 14:29:39.205 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 14:29:39.244 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 14:29:39.245 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 14:30:24.849 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 14:30:24.851 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 14:30:24.963 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 14:30:24.963 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 14:30:24.998 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 14:30:24.998 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 14:30:24.998 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 14:30:24.998 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 14:30:25.894 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 14:30:25.896 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 14:30:26.043 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 14:30:26.043 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 14:30:26.043 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 14:30:26.051 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 14:30:26.105 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 14:30:26.105 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 14:30:26.109 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 14:30:26.109 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 14:30:26.109 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 14:30:26.111 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 14:30:26.111 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 14:30:26.111 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 14:30:26.111 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 14:30:26.145 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 14:30:26.145 - [任务 10][Mysql3306] - Starting mysql cdc, server name: f4f216c4-26a6-44fa-a33b-65b7f5135634 
[INFO ] 2024-11-01 14:30:26.177 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"f4f216c4-26a6-44fa-a33b-65b7f5135634","offset":{"{\"server\":\"f4f216c4-26a6-44fa-a33b-65b7f5135634\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 259760789
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f4f216c4-26a6-44fa-a33b-65b7f5135634
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f4f216c4-26a6-44fa-a33b-65b7f5135634
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: f4f216c4-26a6-44fa-a33b-65b7f5135634
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 14:30:26.177 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[WARN ] 2024-11-01 14:30:27.590 - [任务 10][Mysql3307] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[60ecb9cf-8228-4c43-9988-caa64964401e], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-11-01 14:30:38.282 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 14:30:38.417 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 14:30:38.418 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 14:30:38.425 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 14:30:38.425 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 14:30:38.425 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 14:30:38.426 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 14:30:38.432 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 14:30:38.432 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 152 ms 
[INFO ] 2024-11-01 14:30:38.432 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 14:30:38.460 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 14:30:38.463 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 14:30:38.463 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 14:30:38.465 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 14:30:38.465 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 32 ms 
[INFO ] 2024-11-01 14:30:39.319 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 14:30:39.322 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1fc252bb 
[INFO ] 2024-11-01 14:30:39.322 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 14:30:39.468 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 14:30:39.469 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 14:30:39.550 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 14:30:39.550 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 15:36:00.393 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 15:36:00.395 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 15:36:00.554 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 15:36:00.707 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 15:36:00.809 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 15:36:00.810 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 15:36:00.810 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 15:36:01.016 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 15:36:01.760 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 15:36:01.761 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 15:36:01.761 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 15:36:01.763 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 15:36:01.867 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 15:36:01.868 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 15:36:01.868 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 15:36:01.868 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 15:36:01.868 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 15:36:01.870 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 15:36:01.871 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 15:36:01.871 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 15:36:01.874 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 15:36:01.874 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 15:36:01.917 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 42ed9f7f-e89f-41fd-aa8e-3d4bcacf1cef 
[INFO ] 2024-11-01 15:36:01.918 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"42ed9f7f-e89f-41fd-aa8e-3d4bcacf1cef","offset":{"{\"server\":\"42ed9f7f-e89f-41fd-aa8e-3d4bcacf1cef\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1999231424
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 42ed9f7f-e89f-41fd-aa8e-3d4bcacf1cef
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-42ed9f7f-e89f-41fd-aa8e-3d4bcacf1cef
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 42ed9f7f-e89f-41fd-aa8e-3d4bcacf1cef
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 15:36:01.947 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 15:36:01.948 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 15:36:23.594 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[WARN ] 2024-11-01 15:36:23.969 - [任务 10][Mysql3307] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[60ecb9cf-8228-4c43-9988-caa64964401e], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-11-01 15:36:32.605 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 15:36:32.680 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 15:36:32.681 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 15:36:32.681 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 15:36:32.696 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 15:36:32.696 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 15:36:32.697 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 15:36:32.701 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 15:36:32.702 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 97 ms 
[INFO ] 2024-11-01 15:36:32.702 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 15:36:32.725 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 15:36:32.726 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 15:36:32.726 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 15:36:32.727 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 15:36:32.727 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 26 ms 
[INFO ] 2024-11-01 15:36:33.555 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 15:36:33.556 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3493481d 
[INFO ] 2024-11-01 15:36:33.559 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 15:36:33.694 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 15:36:33.696 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 15:36:33.721 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 15:36:33.723 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 15:37:08.205 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 15:37:08.285 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 15:37:08.285 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 15:37:08.365 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 15:37:08.381 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 15:37:08.384 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 15:37:08.384 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 15:37:08.384 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 15:37:09.329 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 15:37:09.329 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 15:37:09.329 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 15:37:09.331 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 15:37:09.392 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 15:37:09.393 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 15:37:09.396 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 15:37:09.396 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 15:37:09.403 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 15:37:09.403 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 15:37:09.403 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 15:37:09.403 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 15:37:09.406 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 15:37:09.406 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 15:37:09.431 - [任务 10][Mysql3306] - Starting mysql cdc, server name: c5072920-948a-42ef-881a-0456b5d4dfa5 
[INFO ] 2024-11-01 15:37:09.431 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"c5072920-948a-42ef-881a-0456b5d4dfa5","offset":{"{\"server\":\"c5072920-948a-42ef-881a-0456b5d4dfa5\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1483116820
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c5072920-948a-42ef-881a-0456b5d4dfa5
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c5072920-948a-42ef-881a-0456b5d4dfa5
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: c5072920-948a-42ef-881a-0456b5d4dfa5
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 15:37:09.516 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 15:37:09.516 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 15:37:09.516 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 15:38:08.037 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length 
[ERROR] 2024-11-01 15:38:08.068 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$8(HazelcastTargetPdkDataNode.java:278)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:208)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:154)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:142)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:107)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$7(HazelcastTargetPdkDataNode.java:280)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-11-01 15:38:08.069 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 15:38:08.382 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 15:38:08.445 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 15:38:08.446 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 15:38:08.446 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 15:38:08.464 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 15:38:08.464 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 15:38:08.467 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 15:38:08.469 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 15:38:08.471 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 98 ms 
[INFO ] 2024-11-01 15:38:08.471 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 15:38:08.486 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 15:38:08.486 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 15:38:08.486 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 15:38:08.487 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 15:38:08.695 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 16 ms 
[INFO ] 2024-11-01 15:38:12.920 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 15:38:12.944 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 15:38:12.944 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7667f4e9 
[INFO ] 2024-11-01 15:38:12.946 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 15:38:13.085 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 15:38:13.085 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 15:38:13.102 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 15:38:13.103 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:17:33.088 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 16:17:33.089 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 16:17:33.759 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 16:17:33.760 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 16:17:34.170 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:17:34.171 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:17:34.174 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 16:17:34.177 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 16:17:35.380 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 16:17:35.382 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 16:17:38.516 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 16:17:38.558 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 16:17:38.558 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 16:17:38.617 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 16:17:38.618 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 16:17:38.728 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 16:17:38.733 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 16:17:38.733 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 16:17:38.734 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 16:17:38.741 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 16:17:38.745 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:17:38.745 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 16:17:38.745 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:17:38.751 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 16:17:38.833 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 1073b559-6f70-4c45-a0f4-9738f0d13af6 
[INFO ] 2024-11-01 16:17:38.837 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length 
[INFO ] 2024-11-01 16:17:38.861 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"1073b559-6f70-4c45-a0f4-9738f0d13af6","offset":{"{\"server\":\"1073b559-6f70-4c45-a0f4-9738f0d13af6\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1489278838
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1073b559-6f70-4c45-a0f4-9738f0d13af6
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1073b559-6f70-4c45-a0f4-9738f0d13af6
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 1073b559-6f70-4c45-a0f4-9738f0d13af6
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[ERROR] 2024-11-01 16:17:38.863 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$8(HazelcastTargetPdkDataNode.java:279)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:274)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$7(HazelcastTargetPdkDataNode.java:281)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-11-01 16:17:39.053 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 16:17:39.053 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 16:17:39.194 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 16:17:39.600 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 16:17:39.604 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 16:17:39.605 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 16:17:39.623 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:17:39.626 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:17:39.626 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 16:17:39.651 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 16:17:39.652 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 447 ms 
[INFO ] 2024-11-01 16:17:39.652 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 16:17:39.678 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:17:39.679 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:17:39.679 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 16:17:39.679 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 16:17:39.681 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 29 ms 
[INFO ] 2024-11-01 16:17:43.595 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 16:17:43.596 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 16:17:43.596 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6109be47 
[INFO ] 2024-11-01 16:17:43.720 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 16:17:43.753 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 16:17:43.754 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 16:17:43.804 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:17:43.804 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:18:02.177 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 16:18:02.179 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 16:18:02.385 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 16:18:02.477 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 16:18:02.477 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:18:02.481 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 16:18:02.481 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:18:02.684 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 16:18:03.441 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 16:18:03.445 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 16:18:05.823 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length 
[INFO ] 2024-11-01 16:18:05.828 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 16:18:05.830 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[ERROR] 2024-11-01 16:18:05.839 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$8(HazelcastTargetPdkDataNode.java:279)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:274)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'phoneNumber' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$7(HazelcastTargetPdkDataNode.java:281)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-11-01 16:18:05.840 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 16:18:05.848 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 16:18:05.848 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 16:18:05.929 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 16:18:05.929 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 16:18:05.929 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 16:18:05.929 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 16:18:05.931 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 16:18:05.932 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 16:18:05.933 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:18:05.933 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 16:18:05.933 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:18:05.967 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 16:18:05.968 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 39f13333-ab25-48d4-aecd-e13c6cb819c8 
[INFO ] 2024-11-01 16:18:06.026 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"39f13333-ab25-48d4-aecd-e13c6cb819c8","offset":{"{\"server\":\"39f13333-ab25-48d4-aecd-e13c6cb819c8\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1347493683
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 39f13333-ab25-48d4-aecd-e13c6cb819c8
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-39f13333-ab25-48d4-aecd-e13c6cb819c8
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 39f13333-ab25-48d4-aecd-e13c6cb819c8
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 16:18:06.027 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 16:18:06.438 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 16:18:06.518 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 16:18:06.519 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 16:18:06.519 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 16:18:06.535 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:18:06.535 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:18:06.536 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 16:18:06.540 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 16:18:06.545 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 115 ms 
[INFO ] 2024-11-01 16:18:06.546 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 16:18:06.556 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:18:06.557 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:18:06.558 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 16:18:06.558 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 16:18:06.560 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 17 ms 
[INFO ] 2024-11-01 16:18:10.779 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 16:18:10.781 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 16:18:10.781 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@a3fc38a 
[INFO ] 2024-11-01 16:18:10.892 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 16:18:10.892 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 16:18:10.894 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 16:18:10.910 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:18:10.911 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:18:25.031 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 16:18:25.031 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 16:18:25.162 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 16:18:25.275 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 16:18:25.411 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:18:25.412 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:18:25.414 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 1 ms 
[INFO ] 2024-11-01 16:18:25.414 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 16:18:26.319 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 16:18:37.045 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 16:18:37.241 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 16:18:37.242 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 16:18:37.242 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 16:18:37.250 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 16:18:37.250 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 16:18:37.281 - [任务 10][Mysql3307] - Table: wimcustomertest already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-01 16:18:37.289 - [任务 10][Mysql3307] - Table: wimcustomertest will create Index: TapIndex name idx_phone_prefix indexFields: [TapIndexField name null fieldAsc true indexType null; TapIndexField name id fieldAsc true indexType null; ] 
[INFO ] 2024-11-01 16:18:37.316 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 16:18:37.317 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 16:18:37.319 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 16:18:37.320 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 16:18:37.349 - [任务 10][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table 
[INFO ] 2024-11-01 16:18:37.363 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[ERROR] 2024-11-01 16:18:37.372 - [任务 10][Mysql3307] - Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: wimcustomertest, java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:364)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:360)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'null' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:371)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-01 16:18:37.373 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:18:37.373 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 16:18:37.373 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:18:37.373 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4383,"gtidSet":""} 
[INFO ] 2024-11-01 16:18:37.373 - [任务 10][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-01 16:18:37.401 - [任务 10][Mysql3306] - Starting mysql cdc, server name: 71fed79b-e415-4b7a-8ab9-4128c76be37f 
[INFO ] 2024-11-01 16:18:37.446 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"71fed79b-e415-4b7a-8ab9-4128c76be37f","offset":{"{\"server\":\"71fed79b-e415-4b7a-8ab9-4128c76be37f\"}":"{\"file\":\"binlog.000037\",\"pos\":4383,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1080536617
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 71fed79b-e415-4b7a-8ab9-4128c76be37f
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-71fed79b-e415-4b7a-8ab9-4128c76be37f
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 71fed79b-e415-4b7a-8ab9-4128c76be37f
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 16:18:37.449 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 16:18:37.831 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 16:18:37.838 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 16:18:37.840 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 16:18:37.840 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 16:18:37.865 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:18:37.865 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:18:37.866 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 16:18:37.872 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 16:18:37.872 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 70 ms 
[INFO ] 2024-11-01 16:18:37.873 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 16:18:37.887 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:18:37.888 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:18:37.888 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 16:18:37.888 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 16:18:38.090 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 17 ms 
[INFO ] 2024-11-01 16:18:42.097 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-01 16:18:42.097 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 16:18:42.214 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@605906a7 
[INFO ] 2024-11-01 16:18:42.214 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 16:18:42.228 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 16:18:42.229 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 16:18:42.239 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:18:42.241 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:23:34.837 - [任务 10] - Start task milestones: 67234d2e91df3016ec3e4454(任务 10) 
[INFO ] 2024-11-01 16:23:34.837 - [任务 10] - Task initialization... 
[INFO ] 2024-11-01 16:23:35.018 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-01 16:23:35.054 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-01 16:23:35.226 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:23:35.235 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] preload schema finished, cost 3 ms 
[INFO ] 2024-11-01 16:23:35.284 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] start preload schema,table counts: 1 
[INFO ] 2024-11-01 16:23:35.284 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] preload schema finished, cost 0 ms 
[INFO ] 2024-11-01 16:23:36.259 - [任务 10][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-01 16:23:36.261 - [任务 10][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-01 16:23:36.261 - [任务 10][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-01 16:23:36.269 - [任务 10][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":4680,"gtidSet":""} 
[INFO ] 2024-11-01 16:23:36.271 - [任务 10][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-01 16:23:36.349 - [任务 10][Mysql3306] - Initial sync started 
[INFO ] 2024-11-01 16:23:36.350 - [任务 10][Mysql3306] - Starting batch read, table name: wimcustomertest 
[INFO ] 2024-11-01 16:23:36.350 - [任务 10][Mysql3306] - Query snapshot row size completed: Mysql3306(60ecb9cf-8228-4c43-9988-caa64964401e) 
[INFO ] 2024-11-01 16:23:36.351 - [任务 10][Mysql3306] - Table wimcustomertest is going to be initial synced 
[INFO ] 2024-11-01 16:23:36.395 - [任务 10][Mysql3306] - Table [wimcustomertest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-01 16:23:36.397 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:23:36.398 - [任务 10][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-01 16:23:36.400 - [任务 10][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-01 16:23:36.403 - [任务 10][Mysql3306] - Starting stream read, table list: [wimcustomertest], offset: {"filename":"binlog.000037","position":4680,"gtidSet":""} 
[INFO ] 2024-11-01 16:23:36.440 - [任务 10][Mysql3306] - Starting mysql cdc, server name: a6728d4d-140b-437a-8099-a66794893399 
[INFO ] 2024-11-01 16:23:36.458 - [任务 10][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a6728d4d-140b-437a-8099-a66794893399","offset":{"{\"server\":\"a6728d4d-140b-437a-8099-a66794893399\"}":"{\"file\":\"binlog.000037\",\"pos\":4680,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 747314628
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a6728d4d-140b-437a-8099-a66794893399
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-a6728d4d-140b-437a-8099-a66794893399
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a6728d4d-140b-437a-8099-a66794893399
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.wimcustomertest
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-01 16:23:36.458 - [任务 10][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-01 16:23:36.458 - [任务 10][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-01 16:23:39.941 - [任务 10][Mysql3306] - Connector Mysql incremental start succeed, tables: [wimcustomertest], data change syncing 
[INFO ] 2024-11-01 16:34:22.513 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] running status set to false 
[INFO ] 2024-11-01 16:34:22.513 - [任务 10][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-01 16:34:22.513 - [任务 10][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-01 16:34:22.514 - [任务 10][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-01 16:34:22.524 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:34:22.525 - [任务 10][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-60ecb9cf-8228-4c43-9988-caa64964401e 
[INFO ] 2024-11-01 16:34:22.525 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] schema data cleaned 
[INFO ] 2024-11-01 16:34:22.529 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] monitor closed 
[INFO ] 2024-11-01 16:34:22.530 - [任务 10][Mysql3306] - Node Mysql3306[60ecb9cf-8228-4c43-9988-caa64964401e] close complete, cost 128 ms 
[INFO ] 2024-11-01 16:34:22.530 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] running status set to false 
[INFO ] 2024-11-01 16:34:22.568 - [任务 10][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:34:22.569 - [任务 10][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-eca6d15b-43a1-439c-a54e-1ab5f0663bdd 
[INFO ] 2024-11-01 16:34:22.569 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] schema data cleaned 
[INFO ] 2024-11-01 16:34:22.569 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] monitor closed 
[INFO ] 2024-11-01 16:34:22.570 - [任务 10][Mysql3307] - Node Mysql3307[eca6d15b-43a1-439c-a54e-1ab5f0663bdd] close complete, cost 40 ms 
[INFO ] 2024-11-01 16:34:25.240 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-01 16:34:25.242 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2df79cde 
[INFO ] 2024-11-01 16:34:25.380 - [任务 10] - Stop task milestones: 67234d2e91df3016ec3e4454(任务 10)  
[INFO ] 2024-11-01 16:34:25.380 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-11-01 16:34:25.380 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-11-01 16:34:25.427 - [任务 10] - Remove memory task client succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
[INFO ] 2024-11-01 16:34:25.427 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[67234d2e91df3016ec3e4454] 
