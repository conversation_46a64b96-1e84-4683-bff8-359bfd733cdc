[INFO ] 2024-03-29 16:20:38.891 - [products_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 16:20:38.919 - [products_import_import_import_import_import_import_import] - Start task milestones: 660677cf18990e56b52705af(products_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 16:20:38.921 - [products_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 16:20:39.064 - [products_import_import_import_import_import_import_import] - The engine receives products_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 16:20:39.132 - [products_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[1e60657f-ca82-44c1-a3e8-0828ccf828d9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:20:39.132 - [products_import_import_import_import_import_import_import][merge] - Node merge[f6816639-bc59-4ab4-8910-61249cb9b28b] start preload schema,table counts: 3 
[INFO ] 2024-03-29 16:20:39.134 - [products_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[4afc19d0-15fb-4a72-8567-508423d8cea0] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:20:39.135 - [products_import_import_import_import_import_import_import][Categories] - Node Categories[912a79c6-9ebf-474e-94cc-2661aa812e02] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:20:39.135 - [products_import_import_import_import_import_import_import][Products] - Node Products[12eedb21-c9ad-4f7c-afb9-57f15ebdbb80] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:20:39.135 - [products_import_import_import_import_import_import_import][products] - Node products[d33f3814-2dae-4758-a472-ca81a7bf6542] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:20:39.186 - [products_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[1e60657f-ca82-44c1-a3e8-0828ccf828d9] preload schema finished, cost 51 ms 
[INFO ] 2024-03-29 16:20:39.186 - [products_import_import_import_import_import_import_import][Categories] - Node Categories[912a79c6-9ebf-474e-94cc-2661aa812e02] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 16:20:39.186 - [products_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[4afc19d0-15fb-4a72-8567-508423d8cea0] preload schema finished, cost 51 ms 
[INFO ] 2024-03-29 16:20:39.186 - [products_import_import_import_import_import_import_import][Products] - Node Products[12eedb21-c9ad-4f7c-afb9-57f15ebdbb80] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 16:20:39.204 - [products_import_import_import_import_import_import_import][products] - Node products[d33f3814-2dae-4758-a472-ca81a7bf6542] preload schema finished, cost 68 ms 
[INFO ] 2024-03-29 16:20:39.228 - [products_import_import_import_import_import_import_import][merge] - Node merge[f6816639-bc59-4ab4-8910-61249cb9b28b] preload schema finished, cost 92 ms 
[INFO ] 2024-03-29 16:20:39.228 - [products_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 16:20:39.433 - [products_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Products(4afc19d0-15fb-4a72-8567-508423d8cea0)
    ->Categories(1e60657f-ca82-44c1-a3e8-0828ccf828d9)
} 
[INFO ] 2024-03-29 16:20:39.569 - [products_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Categories_1e60657f-ca82-44c1-a3e8-0828ccf828d9__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:20:39.569 - [products_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 16:20:39.569 - [products_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 16:20:40.377 - [products_import_import_import_import_import_import_import][products] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 16:20:40.422 - [products_import_import_import_import_import_import_import][Categories] - Source node "Categories" read batch size: 500 
[INFO ] 2024-03-29 16:20:40.423 - [products_import_import_import_import_import_import_import][Categories] - Source node "Categories" event queue capacity: 1000 
[INFO ] 2024-03-29 16:20:40.423 - [products_import_import_import_import_import_import_import][Categories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:20:40.424 - [products_import_import_import_import_import_import_import][Categories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:20:40.578 - [products_import_import_import_import_import_import_import] - Node[Categories] is waiting for running 
[INFO ] 2024-03-29 16:20:40.578 - [products_import_import_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 16:20:40.578 - [products_import_import_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 16:20:40.578 - [products_import_import_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:20:40.626 - [products_import_import_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:20:40.626 - [products_import_import_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 16:20:40.626 - [products_import_import_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 16:20:40.638 - [products_import_import_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 16:20:40.682 - [products_import_import_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 16:20:40.682 - [products_import_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 16:20:41.793 - [products_import_import_import_import_import_import_import] - Node[Products] finish, notify next layer to run 
[INFO ] 2024-03-29 16:20:41.794 - [products_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 16:20:41.794 - [products_import_import_import_import_import_import_import][Categories] - Initial sync started 
[INFO ] 2024-03-29 16:20:41.794 - [products_import_import_import_import_import_import_import][Categories] - Starting batch read, table name: Categories, offset: null 
[INFO ] 2024-03-29 16:20:41.794 - [products_import_import_import_import_import_import_import][Categories] - Table Categories is going to be initial synced 
[INFO ] 2024-03-29 16:20:41.835 - [products_import_import_import_import_import_import_import][Categories] - Query table 'Categories' counts: 1 
[INFO ] 2024-03-29 16:20:42.041 - [products_import_import_import_import_import_import_import][Categories] - Initial sync completed 
[INFO ] 2024-03-29 16:20:42.692 - [products_import_import_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 16:20:42.694 - [products_import_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 16:20:42.703 - [products_import_import_import_import_import_import_import][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:20:42.796 - [products_import_import_import_import_import_import_import][Products] - Starting mysql cdc, server name: 5c1db5c6-4db6-4c38-9275-a57fb66457b2 
[INFO ] 2024-03-29 16:20:42.798 - [products_import_import_import_import_import_import_import][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1174131496
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 5c1db5c6-4db6-4c38-9275-a57fb66457b2
  database.port: 3307
  threadName: Debezium-Mysql-Connector-5c1db5c6-4db6-4c38-9275-a57fb66457b2
  database.hostname: 127.0.0.1
  database.password: ********
  name: 5c1db5c6-4db6-4c38-9275-a57fb66457b2
  pdk.offset.string: {"name":"5c1db5c6-4db6-4c38-9275-a57fb66457b2","offset":{"{\"server\":\"5c1db5c6-4db6-4c38-9275-a57fb66457b2\"}":"{\"file\":\"binlog.000008\",\"pos\":6371734,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 16:20:42.852 - [products_import_import_import_import_import_import_import][Categories] - Incremental sync starting... 
[INFO ] 2024-03-29 16:20:42.852 - [products_import_import_import_import_import_import_import][Categories] - Initial sync completed 
[INFO ] 2024-03-29 16:20:42.853 - [products_import_import_import_import_import_import_import][Categories] - Starting stream read, table list: [Categories], offset: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:20:42.887 - [products_import_import_import_import_import_import_import][Categories] - Starting mysql cdc, server name: 2d119c52-26ea-47df-9dcb-e5fcae7788e3 
[INFO ] 2024-03-29 16:20:43.034 - [products_import_import_import_import_import_import_import][Categories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1002602037
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2d119c52-26ea-47df-9dcb-e5fcae7788e3
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2d119c52-26ea-47df-9dcb-e5fcae7788e3
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2d119c52-26ea-47df-9dcb-e5fcae7788e3
  pdk.offset.string: {"name":"2d119c52-26ea-47df-9dcb-e5fcae7788e3","offset":{"{\"server\":\"2d119c52-26ea-47df-9dcb-e5fcae7788e3\"}":"{\"file\":\"binlog.000008\",\"pos\":6371734,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Categories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 16:20:43.034 - [products_import_import_import_import_import_import_import][Categories] - Connector Mysql incremental start succeed, tables: [Categories], data change syncing 
[INFO ] 2024-03-29 16:20:43.034 - [products_import_import_import_import_import_import_import][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 16:31:38.463 - [products_import_import_import_import_import_import_import] - Stop task milestones: 660677cf18990e56b52705af(products_import_import_import_import_import_import_import)  
[INFO ] 2024-03-29 16:31:38.660 - [products_import_import_import_import_import_import_import][Products] - Node Products[12eedb21-c9ad-4f7c-afb9-57f15ebdbb80] running status set to false 
[INFO ] 2024-03-29 16:31:38.680 - [products_import_import_import_import_import_import_import][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 16:31:38.681 - [products_import_import_import_import_import_import_import][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 16:31:38.689 - [products_import_import_import_import_import_import_import][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-12eedb21-c9ad-4f7c-afb9-57f15ebdbb80 
[INFO ] 2024-03-29 16:31:38.689 - [products_import_import_import_import_import_import_import][Products] - PDK connector node released: HazelcastSourcePdkDataNode-12eedb21-c9ad-4f7c-afb9-57f15ebdbb80 
[INFO ] 2024-03-29 16:31:38.689 - [products_import_import_import_import_import_import_import][Products] - Node Products[12eedb21-c9ad-4f7c-afb9-57f15ebdbb80] schema data cleaned 
[INFO ] 2024-03-29 16:31:38.689 - [products_import_import_import_import_import_import_import][Products] - Node Products[12eedb21-c9ad-4f7c-afb9-57f15ebdbb80] monitor closed 
[INFO ] 2024-03-29 16:31:38.690 - [products_import_import_import_import_import_import_import][Products] - Node Products[12eedb21-c9ad-4f7c-afb9-57f15ebdbb80] close complete, cost 30 ms 
[INFO ] 2024-03-29 16:31:38.690 - [products_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[4afc19d0-15fb-4a72-8567-508423d8cea0] running status set to false 
[INFO ] 2024-03-29 16:31:38.731 - [products_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[4afc19d0-15fb-4a72-8567-508423d8cea0] schema data cleaned 
[INFO ] 2024-03-29 16:31:38.731 - [products_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[4afc19d0-15fb-4a72-8567-508423d8cea0] monitor closed 
[INFO ] 2024-03-29 16:31:38.731 - [products_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[4afc19d0-15fb-4a72-8567-508423d8cea0] close complete, cost 41 ms 
[INFO ] 2024-03-29 16:31:38.752 - [products_import_import_import_import_import_import_import][Categories] - Node Categories[912a79c6-9ebf-474e-94cc-2661aa812e02] running status set to false 
[INFO ] 2024-03-29 16:31:38.752 - [products_import_import_import_import_import_import_import][Categories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 16:31:38.752 - [products_import_import_import_import_import_import_import][Categories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 16:31:38.758 - [products_import_import_import_import_import_import_import][Categories] - PDK connector node stopped: HazelcastSourcePdkDataNode-912a79c6-9ebf-474e-94cc-2661aa812e02 
[INFO ] 2024-03-29 16:31:38.759 - [products_import_import_import_import_import_import_import][Categories] - PDK connector node released: HazelcastSourcePdkDataNode-912a79c6-9ebf-474e-94cc-2661aa812e02 
[INFO ] 2024-03-29 16:31:38.759 - [products_import_import_import_import_import_import_import][Categories] - Node Categories[912a79c6-9ebf-474e-94cc-2661aa812e02] schema data cleaned 
[INFO ] 2024-03-29 16:31:38.759 - [products_import_import_import_import_import_import_import][Categories] - Node Categories[912a79c6-9ebf-474e-94cc-2661aa812e02] monitor closed 
[INFO ] 2024-03-29 16:31:38.759 - [products_import_import_import_import_import_import_import][Categories] - Node Categories[912a79c6-9ebf-474e-94cc-2661aa812e02] close complete, cost 27 ms 
[INFO ] 2024-03-29 16:31:38.794 - [products_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[1e60657f-ca82-44c1-a3e8-0828ccf828d9] running status set to false 
[INFO ] 2024-03-29 16:31:38.794 - [products_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[1e60657f-ca82-44c1-a3e8-0828ccf828d9] schema data cleaned 
[INFO ] 2024-03-29 16:31:38.794 - [products_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[1e60657f-ca82-44c1-a3e8-0828ccf828d9] monitor closed 
[INFO ] 2024-03-29 16:31:38.794 - [products_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[1e60657f-ca82-44c1-a3e8-0828ccf828d9] close complete, cost 35 ms 
[INFO ] 2024-03-29 16:31:38.795 - [products_import_import_import_import_import_import_import][merge] - Node merge[f6816639-bc59-4ab4-8910-61249cb9b28b] running status set to false 
[INFO ] 2024-03-29 16:31:38.795 - [products_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Categories_1e60657f-ca82-44c1-a3e8-0828ccf828d9__TPORIG 
[INFO ] 2024-03-29 16:31:38.798 - [products_import_import_import_import_import_import_import][merge] - Node merge[f6816639-bc59-4ab4-8910-61249cb9b28b] schema data cleaned 
[INFO ] 2024-03-29 16:31:38.798 - [products_import_import_import_import_import_import_import][merge] - Node merge[f6816639-bc59-4ab4-8910-61249cb9b28b] monitor closed 
[INFO ] 2024-03-29 16:31:38.798 - [products_import_import_import_import_import_import_import][merge] - Node merge[f6816639-bc59-4ab4-8910-61249cb9b28b] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:31:38.799 - [products_import_import_import_import_import_import_import][products] - Node products[d33f3814-2dae-4758-a472-ca81a7bf6542] running status set to false 
[INFO ] 2024-03-29 16:31:38.812 - [products_import_import_import_import_import_import_import][products] - PDK connector node stopped: HazelcastTargetPdkDataNode-d33f3814-2dae-4758-a472-ca81a7bf6542 
[INFO ] 2024-03-29 16:31:38.812 - [products_import_import_import_import_import_import_import][products] - PDK connector node released: HazelcastTargetPdkDataNode-d33f3814-2dae-4758-a472-ca81a7bf6542 
[INFO ] 2024-03-29 16:31:38.812 - [products_import_import_import_import_import_import_import][products] - Node products[d33f3814-2dae-4758-a472-ca81a7bf6542] schema data cleaned 
[INFO ] 2024-03-29 16:31:38.812 - [products_import_import_import_import_import_import_import][products] - Node products[d33f3814-2dae-4758-a472-ca81a7bf6542] monitor closed 
[INFO ] 2024-03-29 16:31:39.018 - [products_import_import_import_import_import_import_import][products] - Node products[d33f3814-2dae-4758-a472-ca81a7bf6542] close complete, cost 13 ms 
[INFO ] 2024-03-29 16:31:42.969 - [products_import_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 16:31:42.969 - [products_import_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 16:31:42.969 - [products_import_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 16:31:43.037 - [products_import_import_import_import_import_import_import] - Remove memory task client succeed, task: products_import_import_import_import_import_import_import[660677cf18990e56b52705af] 
[INFO ] 2024-03-29 16:31:43.037 - [products_import_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: products_import_import_import_import_import_import_import[660677cf18990e56b52705af] 
