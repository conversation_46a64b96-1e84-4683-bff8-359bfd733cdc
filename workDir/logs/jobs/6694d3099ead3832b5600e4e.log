[INFO ] 2024-07-15 17:00:51.611 - [任务 7] - Task initialization... 
[INFO ] 2024-07-15 17:00:51.613 - [任务 7] - Start task milestones: 6694d3099ead3832b5600e4e(任务 7) 
[INFO ] 2024-07-15 17:00:51.783 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 17:00:51.826 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 17:00:51.956 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:00:51.956 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:00:51.956 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:00:51.957 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:01:31.363 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" read batch size: 100 
[INFO ] 2024-07-15 17:01:31.363 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" event queue capacity: 200 
[INFO ] 2024-07-15 17:01:31.363 - [任务 7][TestReplicationTimeCalculation] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 17:01:31.499 - [任务 7][TransformTimeCalculation] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 17:01:31.502 - [任务 7][TestReplicationTimeCalculation] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000160","position":345175271,"gtidSet":""} 
[INFO ] 2024-07-15 17:01:31.555 - [任务 7][TestReplicationTimeCalculation] - Initial sync started 
[INFO ] 2024-07-15 17:01:31.558 - [任务 7][TestReplicationTimeCalculation] - Starting batch read, table name: TestReplicationTimeCalculation, offset: null 
[INFO ] 2024-07-15 17:01:31.681 - [任务 7][TestReplicationTimeCalculation] - Table TestReplicationTimeCalculation is going to be initial synced 
[INFO ] 2024-07-15 17:01:31.681 - [任务 7][TestReplicationTimeCalculation] - Query table 'TestReplicationTimeCalculation' counts: 1 
[INFO ] 2024-07-15 17:01:31.681 - [任务 7][TestReplicationTimeCalculation] - Table [TestReplicationTimeCalculation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 17:01:31.681 - [任务 7][TestReplicationTimeCalculation] - Initial sync completed 
[INFO ] 2024-07-15 17:01:31.681 - [任务 7][TestReplicationTimeCalculation] - Incremental sync starting... 
[INFO ] 2024-07-15 17:01:31.681 - [任务 7][TestReplicationTimeCalculation] - Initial sync completed 
[INFO ] 2024-07-15 17:01:31.682 - [任务 7][TestReplicationTimeCalculation] - Starting stream read, table list: [TestReplicationTimeCalculation], offset: {"filename":"mysql-bin.000160","position":345175271,"gtidSet":""} 
[INFO ] 2024-07-15 17:01:31.860 - [任务 7][TestReplicationTimeCalculation] - Starting mysql cdc, server name: 9b6a7b80-11a8-4b06-b753-cc63faa60c51 
[INFO ] 2024-07-15 17:01:31.860 - [任务 7][TestReplicationTimeCalculation] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1571279925
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9b6a7b80-11a8-4b06-b753-cc63faa60c51
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9b6a7b80-11a8-4b06-b753-cc63faa60c51
  database.hostname: *************
  database.password: ********
  name: 9b6a7b80-11a8-4b06-b753-cc63faa60c51
  pdk.offset.string: {"name":"9b6a7b80-11a8-4b06-b753-cc63faa60c51","offset":{"{\"server\":\"9b6a7b80-11a8-4b06-b753-cc63faa60c51\"}":"{\"file\":\"mysql-bin.000160\",\"pos\":345175271,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.TestReplicationTimeCalculation
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-15 17:01:32.220 - [任务 7][TestReplicationTimeCalculation] - Connector Mysql incremental start succeed, tables: [TestReplicationTimeCalculation], data change syncing 
[INFO ] 2024-07-15 17:01:56.781 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] running status set to false 
[INFO ] 2024-07-15 17:01:56.876 - [任务 7][TestReplicationTimeCalculation] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-15 17:01:56.876 - [任务 7][TestReplicationTimeCalculation] - Mysql binlog reader stopped 
[INFO ] 2024-07-15 17:01:56.893 - [任务 7][TestReplicationTimeCalculation] - Incremental sync completed 
[INFO ] 2024-07-15 17:01:56.893 - [任务 7][TestReplicationTimeCalculation] - PDK connector node stopped: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:01:56.894 - [任务 7][TestReplicationTimeCalculation] - PDK connector node released: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:01:56.894 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] schema data cleaned 
[INFO ] 2024-07-15 17:01:56.897 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] monitor closed 
[INFO ] 2024-07-15 17:01:56.897 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] close complete, cost 143 ms 
[INFO ] 2024-07-15 17:01:56.930 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] running status set to false 
[INFO ] 2024-07-15 17:01:56.930 - [任务 7][TransformTimeCalculation] - PDK connector node stopped: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:01:56.930 - [任务 7][TransformTimeCalculation] - PDK connector node released: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:01:56.931 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] schema data cleaned 
[INFO ] 2024-07-15 17:01:56.931 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] monitor closed 
[INFO ] 2024-07-15 17:01:56.931 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] close complete, cost 34 ms 
[INFO ] 2024-07-15 17:02:01.397 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 17:02:01.398 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3df30c6e 
[INFO ] 2024-07-15 17:02:01.536 - [任务 7] - Stop task milestones: 6694d3099ead3832b5600e4e(任务 7)  
[INFO ] 2024-07-15 17:02:01.536 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-07-15 17:02:01.536 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 17:02:01.571 - [任务 7] - Remove memory task client succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:02:01.571 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:02:23.445 - [任务 7] - Task initialization... 
[INFO ] 2024-07-15 17:02:23.446 - [任务 7] - Start task milestones: 6694d3099ead3832b5600e4e(任务 7) 
[INFO ] 2024-07-15 17:02:23.568 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 17:02:23.674 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 17:02:23.674 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:02:23.676 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:02:23.676 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 17:02:23.676 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 17:02:24.541 - [任务 7][TransformTimeCalculation] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 17:02:50.653 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" read batch size: 100 
[INFO ] 2024-07-15 17:02:50.654 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" event queue capacity: 200 
[INFO ] 2024-07-15 17:02:50.654 - [任务 7][TestReplicationTimeCalculation] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 17:02:50.744 - [任务 7][TestReplicationTimeCalculation] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000160","position":345194336,"gtidSet":""} 
[INFO ] 2024-07-15 17:02:50.805 - [任务 7][TestReplicationTimeCalculation] - Initial sync started 
[INFO ] 2024-07-15 17:02:50.821 - [任务 7][TestReplicationTimeCalculation] - Starting batch read, table name: TestReplicationTimeCalculation, offset: null 
[INFO ] 2024-07-15 17:02:50.821 - [任务 7][TestReplicationTimeCalculation] - Table TestReplicationTimeCalculation is going to be initial synced 
[INFO ] 2024-07-15 17:02:50.925 - [任务 7][TestReplicationTimeCalculation] - Query table 'TestReplicationTimeCalculation' counts: 1 
[INFO ] 2024-07-15 17:02:50.927 - [任务 7][TestReplicationTimeCalculation] - Table [TestReplicationTimeCalculation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 17:02:50.928 - [任务 7][TestReplicationTimeCalculation] - Initial sync completed 
[INFO ] 2024-07-15 17:02:50.928 - [任务 7][TestReplicationTimeCalculation] - Incremental sync starting... 
[INFO ] 2024-07-15 17:02:50.928 - [任务 7][TestReplicationTimeCalculation] - Initial sync completed 
[INFO ] 2024-07-15 17:02:51.007 - [任务 7][TestReplicationTimeCalculation] - Starting stream read, table list: [TestReplicationTimeCalculation], offset: {"filename":"mysql-bin.000160","position":345194336,"gtidSet":""} 
[INFO ] 2024-07-15 17:02:51.009 - [任务 7][TestReplicationTimeCalculation] - Starting mysql cdc, server name: d7614a44-e293-4e06-9866-9d5628502408 
[INFO ] 2024-07-15 17:02:51.215 - [任务 7][TestReplicationTimeCalculation] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 771214612
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d7614a44-e293-4e06-9866-9d5628502408
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d7614a44-e293-4e06-9866-9d5628502408
  database.hostname: *************
  database.password: ********
  name: d7614a44-e293-4e06-9866-9d5628502408
  pdk.offset.string: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"file\":\"mysql-bin.000160\",\"pos\":345194336,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.TestReplicationTimeCalculation
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-15 17:02:51.364 - [任务 7][TestReplicationTimeCalculation] - Connector Mysql incremental start succeed, tables: [TestReplicationTimeCalculation], data change syncing 
[INFO ] 2024-07-15 17:05:25.563 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] running status set to false 
[INFO ] 2024-07-15 17:05:25.563 - [任务 7][TestReplicationTimeCalculation] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-15 17:05:25.563 - [任务 7][TestReplicationTimeCalculation] - Mysql binlog reader stopped 
[INFO ] 2024-07-15 17:05:25.575 - [任务 7][TestReplicationTimeCalculation] - Incremental sync completed 
[INFO ] 2024-07-15 17:05:25.576 - [任务 7][TestReplicationTimeCalculation] - PDK connector node stopped: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:05:25.576 - [任务 7][TestReplicationTimeCalculation] - PDK connector node released: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:05:25.576 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] schema data cleaned 
[INFO ] 2024-07-15 17:05:25.576 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] monitor closed 
[INFO ] 2024-07-15 17:05:25.578 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] close complete, cost 171 ms 
[INFO ] 2024-07-15 17:05:25.578 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] running status set to false 
[INFO ] 2024-07-15 17:05:25.605 - [任务 7][TransformTimeCalculation] - PDK connector node stopped: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:05:25.605 - [任务 7][TransformTimeCalculation] - PDK connector node released: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:05:25.605 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] schema data cleaned 
[INFO ] 2024-07-15 17:05:25.605 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] monitor closed 
[INFO ] 2024-07-15 17:05:25.605 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] close complete, cost 27 ms 
[INFO ] 2024-07-15 17:05:30.041 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 17:05:30.041 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@41d637ae 
[INFO ] 2024-07-15 17:05:30.043 - [任务 7] - Stop task milestones: 6694d3099ead3832b5600e4e(任务 7)  
[INFO ] 2024-07-15 17:05:30.177 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-07-15 17:05:30.178 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 17:05:30.213 - [任务 7] - Remove memory task client succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:05:30.216 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:05:39.631 - [任务 7] - Task initialization... 
[INFO ] 2024-07-15 17:05:39.839 - [任务 7] - Start task milestones: 6694d3099ead3832b5600e4e(任务 7) 
[INFO ] 2024-07-15 17:05:39.839 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 17:05:39.886 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 17:05:39.943 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:05:39.943 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:05:39.944 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 17:05:39.944 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 17:10:07.984 - [任务 7][TransformTimeCalculation] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 17:10:08.480 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" read batch size: 100 
[INFO ] 2024-07-15 17:10:08.480 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" event queue capacity: 200 
[INFO ] 2024-07-15 17:10:08.480 - [任务 7][TestReplicationTimeCalculation] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 17:10:08.568 - [任务 7][TestReplicationTimeCalculation] - batch offset found: {"TestReplicationTimeCalculation":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"ts_sec\":1721034320,\"file\":\"mysql-bin.000160\",\"pos\":345254316,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-07-15 17:10:08.568 - [任务 7][TestReplicationTimeCalculation] - Incremental sync starting... 
[INFO ] 2024-07-15 17:10:08.568 - [任务 7][TestReplicationTimeCalculation] - Initial sync completed 
[INFO ] 2024-07-15 17:10:08.574 - [任务 7][TestReplicationTimeCalculation] - Starting stream read, table list: [TestReplicationTimeCalculation], offset: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"ts_sec\":1721034320,\"file\":\"mysql-bin.000160\",\"pos\":345254316,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-07-15 17:10:08.602 - [任务 7][TestReplicationTimeCalculation] - Starting mysql cdc, server name: d7614a44-e293-4e06-9866-9d5628502408 
[INFO ] 2024-07-15 17:10:08.603 - [任务 7][TestReplicationTimeCalculation] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 380059303
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d7614a44-e293-4e06-9866-9d5628502408
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d7614a44-e293-4e06-9866-9d5628502408
  database.hostname: *************
  database.password: ********
  name: d7614a44-e293-4e06-9866-9d5628502408
  pdk.offset.string: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"ts_sec\":1721034320,\"file\":\"mysql-bin.000160\",\"pos\":345254316,\"row\":1,\"server_id\":1121,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.TestReplicationTimeCalculation
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-15 17:10:09.007 - [任务 7][TestReplicationTimeCalculation] - Connector Mysql incremental start succeed, tables: [TestReplicationTimeCalculation], data change syncing 
[INFO ] 2024-07-15 17:10:18.535 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] running status set to false 
[INFO ] 2024-07-15 17:10:18.535 - [任务 7][TestReplicationTimeCalculation] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-15 17:10:18.546 - [任务 7][TestReplicationTimeCalculation] - Mysql binlog reader stopped 
[INFO ] 2024-07-15 17:10:18.547 - [任务 7][TestReplicationTimeCalculation] - Incremental sync completed 
[INFO ] 2024-07-15 17:10:18.573 - [任务 7][TestReplicationTimeCalculation] - PDK connector node stopped: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:10:18.573 - [任务 7][TestReplicationTimeCalculation] - PDK connector node released: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:10:18.573 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] schema data cleaned 
[INFO ] 2024-07-15 17:10:18.573 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] monitor closed 
[INFO ] 2024-07-15 17:10:18.575 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] close complete, cost 88 ms 
[INFO ] 2024-07-15 17:10:18.575 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] running status set to false 
[INFO ] 2024-07-15 17:10:18.582 - [任务 7][TransformTimeCalculation] - PDK connector node stopped: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:10:18.582 - [任务 7][TransformTimeCalculation] - PDK connector node released: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:10:18.582 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] schema data cleaned 
[INFO ] 2024-07-15 17:10:18.583 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] monitor closed 
[INFO ] 2024-07-15 17:10:18.583 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] close complete, cost 8 ms 
[INFO ] 2024-07-15 17:10:23.117 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 17:10:23.117 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@892f9b0 
[INFO ] 2024-07-15 17:10:23.314 - [任务 7] - Stop task milestones: 6694d3099ead3832b5600e4e(任务 7)  
[INFO ] 2024-07-15 17:10:23.314 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-07-15 17:10:23.314 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 17:10:23.340 - [任务 7] - Remove memory task client succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:10:23.343 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:13:08.806 - [任务 7] - Task initialization... 
[INFO ] 2024-07-15 17:13:08.807 - [任务 7] - Start task milestones: 6694d3099ead3832b5600e4e(任务 7) 
[INFO ] 2024-07-15 17:13:09.012 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 17:13:09.048 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 17:13:09.100 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:13:09.100 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:13:09.101 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:13:09.101 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:13:09.377 - [任务 7][TransformTimeCalculation] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 17:14:12.442 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" read batch size: 100 
[INFO ] 2024-07-15 17:14:12.442 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" event queue capacity: 200 
[INFO ] 2024-07-15 17:14:12.445 - [任务 7][TestReplicationTimeCalculation] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 17:14:12.524 - [任务 7][TestReplicationTimeCalculation] - batch offset found: {"TestReplicationTimeCalculation":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"ts_sec\":1721034320,\"file\":\"mysql-bin.000160\",\"pos\":345254316,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-07-15 17:14:12.524 - [任务 7][TestReplicationTimeCalculation] - Incremental sync starting... 
[INFO ] 2024-07-15 17:14:12.525 - [任务 7][TestReplicationTimeCalculation] - Initial sync completed 
[INFO ] 2024-07-15 17:14:12.572 - [任务 7][TestReplicationTimeCalculation] - Starting stream read, table list: [TestReplicationTimeCalculation], offset: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"ts_sec\":1721034320,\"file\":\"mysql-bin.000160\",\"pos\":345254316,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-07-15 17:14:12.572 - [任务 7][TestReplicationTimeCalculation] - Starting mysql cdc, server name: d7614a44-e293-4e06-9866-9d5628502408 
[INFO ] 2024-07-15 17:14:12.777 - [任务 7][TestReplicationTimeCalculation] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 791958258
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d7614a44-e293-4e06-9866-9d5628502408
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d7614a44-e293-4e06-9866-9d5628502408
  database.hostname: *************
  database.password: ********
  name: d7614a44-e293-4e06-9866-9d5628502408
  pdk.offset.string: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"ts_sec\":1721034320,\"file\":\"mysql-bin.000160\",\"pos\":345254316,\"row\":1,\"server_id\":1121,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.TestReplicationTimeCalculation
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-15 17:14:12.899 - [任务 7][TestReplicationTimeCalculation] - Connector Mysql incremental start succeed, tables: [TestReplicationTimeCalculation], data change syncing 
[INFO ] 2024-07-15 17:14:32.476 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] running status set to false 
[INFO ] 2024-07-15 17:14:32.491 - [任务 7][TestReplicationTimeCalculation] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-15 17:14:32.491 - [任务 7][TestReplicationTimeCalculation] - Mysql binlog reader stopped 
[INFO ] 2024-07-15 17:14:32.525 - [任务 7][TestReplicationTimeCalculation] - Incremental sync completed 
[INFO ] 2024-07-15 17:14:32.525 - [任务 7][TestReplicationTimeCalculation] - PDK connector node stopped: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:14:32.526 - [任务 7][TestReplicationTimeCalculation] - PDK connector node released: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:14:32.526 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] schema data cleaned 
[INFO ] 2024-07-15 17:14:32.527 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] monitor closed 
[INFO ] 2024-07-15 17:14:32.527 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] close complete, cost 85 ms 
[INFO ] 2024-07-15 17:14:32.527 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] running status set to false 
[INFO ] 2024-07-15 17:14:32.537 - [任务 7][TransformTimeCalculation] - PDK connector node stopped: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:14:32.537 - [任务 7][TransformTimeCalculation] - PDK connector node released: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:14:32.537 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] schema data cleaned 
[INFO ] 2024-07-15 17:14:32.537 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] monitor closed 
[INFO ] 2024-07-15 17:14:32.537 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] close complete, cost 9 ms 
[INFO ] 2024-07-15 17:14:37.024 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 17:14:37.025 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5414b6f5 
[INFO ] 2024-07-15 17:14:37.167 - [任务 7] - Stop task milestones: 6694d3099ead3832b5600e4e(任务 7)  
[INFO ] 2024-07-15 17:14:37.167 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-07-15 17:14:37.167 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 17:14:37.187 - [任务 7] - Remove memory task client succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:14:37.191 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:19:21.675 - [任务 7] - Task initialization... 
[INFO ] 2024-07-15 17:19:21.675 - [任务 7] - Start task milestones: 6694d3099ead3832b5600e4e(任务 7) 
[INFO ] 2024-07-15 17:19:21.842 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 17:19:21.924 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 17:19:22.022 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:19:22.022 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:19:22.022 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:19:22.022 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:21:13.750 - [任务 7][TransformTimeCalculation] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 17:21:14.168 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" read batch size: 100 
[INFO ] 2024-07-15 17:21:14.169 - [任务 7][TestReplicationTimeCalculation] - Source node "TestReplicationTimeCalculation" event queue capacity: 200 
[INFO ] 2024-07-15 17:21:14.169 - [任务 7][TestReplicationTimeCalculation] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 17:21:14.233 - [任务 7][TestReplicationTimeCalculation] - batch offset found: {},stream offset found: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"file\":\"mysql-bin.000160\",\"pos\":345396184,\"server_id\":1121}"}} 
[INFO ] 2024-07-15 17:21:14.233 - [任务 7][TestReplicationTimeCalculation] - Incremental sync starting... 
[INFO ] 2024-07-15 17:21:14.233 - [任务 7][TestReplicationTimeCalculation] - Initial sync completed 
[INFO ] 2024-07-15 17:21:14.267 - [任务 7][TestReplicationTimeCalculation] - Starting stream read, table list: [TestReplicationTimeCalculation], offset: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"file\":\"mysql-bin.000160\",\"pos\":345396184,\"server_id\":1121}"}} 
[INFO ] 2024-07-15 17:21:14.268 - [任务 7][TestReplicationTimeCalculation] - Starting mysql cdc, server name: d7614a44-e293-4e06-9866-9d5628502408 
[INFO ] 2024-07-15 17:21:14.472 - [任务 7][TestReplicationTimeCalculation] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 627454720
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d7614a44-e293-4e06-9866-9d5628502408
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d7614a44-e293-4e06-9866-9d5628502408
  database.hostname: *************
  database.password: ********
  name: d7614a44-e293-4e06-9866-9d5628502408
  pdk.offset.string: {"name":"d7614a44-e293-4e06-9866-9d5628502408","offset":{"{\"server\":\"d7614a44-e293-4e06-9866-9d5628502408\"}":"{\"file\":\"mysql-bin.000160\",\"pos\":345396184,\"server_id\":1121}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.TestReplicationTimeCalculation
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-15 17:21:14.554 - [任务 7][TestReplicationTimeCalculation] - Connector Mysql incremental start succeed, tables: [TestReplicationTimeCalculation], data change syncing 
[INFO ] 2024-07-15 17:58:23.765 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] running status set to false 
[INFO ] 2024-07-15 17:58:23.765 - [任务 7][TestReplicationTimeCalculation] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-15 17:58:23.765 - [任务 7][TestReplicationTimeCalculation] - Mysql binlog reader stopped 
[INFO ] 2024-07-15 17:58:23.766 - [任务 7][TestReplicationTimeCalculation] - Incremental sync completed 
[INFO ] 2024-07-15 17:58:23.791 - [任务 7][TestReplicationTimeCalculation] - PDK connector node stopped: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:58:23.792 - [任务 7][TestReplicationTimeCalculation] - PDK connector node released: HazelcastSourcePdkDataNode-b7964701-7a47-4dd2-b52b-fa63e201b1dd 
[INFO ] 2024-07-15 17:58:23.792 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] schema data cleaned 
[INFO ] 2024-07-15 17:58:23.795 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] monitor closed 
[INFO ] 2024-07-15 17:58:23.795 - [任务 7][TestReplicationTimeCalculation] - Node TestReplicationTimeCalculation[b7964701-7a47-4dd2-b52b-fa63e201b1dd] close complete, cost 163 ms 
[INFO ] 2024-07-15 17:58:23.795 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] running status set to false 
[INFO ] 2024-07-15 17:58:23.814 - [任务 7][TransformTimeCalculation] - PDK connector node stopped: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:58:23.815 - [任务 7][TransformTimeCalculation] - PDK connector node released: HazelcastTargetPdkDataNode-fb6d7412-73c2-49a8-97f2-6ca6d9a05527 
[INFO ] 2024-07-15 17:58:23.815 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] schema data cleaned 
[INFO ] 2024-07-15 17:58:23.818 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] monitor closed 
[INFO ] 2024-07-15 17:58:23.820 - [任务 7][TransformTimeCalculation] - Node TransformTimeCalculation[fb6d7412-73c2-49a8-97f2-6ca6d9a05527] close complete, cost 22 ms 
[INFO ] 2024-07-15 17:58:25.923 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 17:58:25.924 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ae21992 
[INFO ] 2024-07-15 17:58:26.092 - [任务 7] - Stop task milestones: 6694d3099ead3832b5600e4e(任务 7)  
[INFO ] 2024-07-15 17:58:26.092 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-07-15 17:58:26.092 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 17:58:26.155 - [任务 7] - Remove memory task client succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
[INFO ] 2024-07-15 17:58:26.155 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6694d3099ead3832b5600e4e] 
