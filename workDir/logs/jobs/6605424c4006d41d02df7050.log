[INFO ] 2024-03-28 18:11:25.102 - [orders(100)][170410b8-28df-42bc-9f50-5790b906c2d6] - Node 170410b8-28df-42bc-9f50-5790b906c2d6[170410b8-28df-42bc-9f50-5790b906c2d6] start preload schema,table counts: 0 
[INFO ] 2024-03-28 18:11:25.103 - [orders(100)][Order Details] - Node Order Details[1f9b1b67-f99c-4556-9ea9-bb07318fdeea] start preload schema,table counts: 1 
[INFO ] 2024-03-28 18:11:25.103 - [orders(100)][Order Details] - Node Order Details[26a6d822-**************-7abd39b5d76a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 18:11:25.103 - [orders(100)][170410b8-28df-42bc-9f50-5790b906c2d6] - Node 170410b8-28df-42bc-9f50-5790b906c2d6[170410b8-28df-42bc-9f50-5790b906c2d6] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 18:11:25.103 - [orders(100)][Order Details] - Node Order Details[1f9b1b67-f99c-4556-9ea9-bb07318fdeea] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 18:11:25.103 - [orders(100)][Order Details] - Node Order Details[26a6d822-**************-7abd39b5d76a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 18:11:25.151 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 18:11:25.353 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ab3d798 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ab3d798 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ab3d798 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 18:11:25.659 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 18:11:25.675 - [orders(100)][Order Details] - Node Order Details[1f9b1b67-f99c-4556-9ea9-bb07318fdeea] running status set to false 
[INFO ] 2024-03-28 18:11:25.675 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-1f9b1b67-f99c-4556-9ea9-bb07318fdeea 
[INFO ] 2024-03-28 18:11:25.675 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-1f9b1b67-f99c-4556-9ea9-bb07318fdeea 
[INFO ] 2024-03-28 18:11:25.675 - [orders(100)][Order Details] - Node Order Details[1f9b1b67-f99c-4556-9ea9-bb07318fdeea] schema data cleaned 
[INFO ] 2024-03-28 18:11:25.676 - [orders(100)][Order Details] - Node Order Details[1f9b1b67-f99c-4556-9ea9-bb07318fdeea] monitor closed 
[INFO ] 2024-03-28 18:11:25.877 - [orders(100)][Order Details] - Node Order Details[1f9b1b67-f99c-4556-9ea9-bb07318fdeea] close complete, cost 13 ms 
[INFO ] 2024-03-28 18:11:27.700 - [orders(100)][Order Details] - Node Order Details[26a6d822-**************-7abd39b5d76a] running status set to false 
[INFO ] 2024-03-28 18:11:27.700 - [orders(100)][Order Details] - Node Order Details[26a6d822-**************-7abd39b5d76a] schema data cleaned 
[INFO ] 2024-03-28 18:11:27.700 - [orders(100)][Order Details] - Node Order Details[26a6d822-**************-7abd39b5d76a] monitor closed 
[INFO ] 2024-03-28 18:11:27.701 - [orders(100)][Order Details] - Node Order Details[26a6d822-**************-7abd39b5d76a] close complete, cost 2 ms 
[INFO ] 2024-03-28 18:11:27.702 - [orders(100)][170410b8-28df-42bc-9f50-5790b906c2d6] - Node 170410b8-28df-42bc-9f50-5790b906c2d6[170410b8-28df-42bc-9f50-5790b906c2d6] running status set to false 
[INFO ] 2024-03-28 18:11:27.702 - [orders(100)][170410b8-28df-42bc-9f50-5790b906c2d6] - Node 170410b8-28df-42bc-9f50-5790b906c2d6[170410b8-28df-42bc-9f50-5790b906c2d6] schema data cleaned 
[INFO ] 2024-03-28 18:11:27.702 - [orders(100)][170410b8-28df-42bc-9f50-5790b906c2d6] - Node 170410b8-28df-42bc-9f50-5790b906c2d6[170410b8-28df-42bc-9f50-5790b906c2d6] monitor closed 
[INFO ] 2024-03-28 18:11:27.705 - [orders(100)][170410b8-28df-42bc-9f50-5790b906c2d6] - Node 170410b8-28df-42bc-9f50-5790b906c2d6[170410b8-28df-42bc-9f50-5790b906c2d6] close complete, cost 0 ms 
[INFO ] 2024-03-28 18:11:27.705 - [orders(100)] - load tapTable task 6605424c4006d41d02df7050-170410b8-28df-42bc-9f50-5790b906c2d6 complete, cost 2745ms 
