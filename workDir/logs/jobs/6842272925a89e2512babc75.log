[TRACE] 2025-06-06 07:24:56.927 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 07:24:57.121 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 07:24:57.121 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 07:24:57.319 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 07:24:57.471 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 07:24:57.581 - [任务 15] - Task started 
[TRACE] 2025-06-06 07:24:57.581 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 07:24:57.581 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 07:24:57.582 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 07:24:57.582 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 07:24:57.582 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 07:24:58.249 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 07:24:58.249 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 07:24:58.253 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 07:24:58.348 - [任务 15][Sybase] - Apply table structure to target database 
[WARN ] 2025-06-06 07:24:58.351 - [任务 15][Sybase] - Table t1 not exists, skip drop 
[INFO ] 2025-06-06 07:24:58.562 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 07:24:58.563 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 07:24:58.564 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-06 07:24:58.564 - [任务 15][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-06 07:24:58.624 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 07:24:58.624 - [任务 15][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_0106e1f2_b8de_4a78_b964_cf517141b225 
[INFO ] 2025-06-06 07:24:58.733 - [任务 15][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 07:24:58.734 - [任务 15][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-06 07:24:58.738 - [任务 15][PGMaster] - Initial sync started 
[INFO ] 2025-06-06 07:24:58.738 - [任务 15][PGMaster] - Starting batch read from table: t1 
[TRACE] 2025-06-06 07:24:58.741 - [任务 15][PGMaster] - Table t1 is going to be initial synced 
[TRACE] 2025-06-06 07:24:58.741 - [任务 15][PGMaster] - Query snapshot row size completed: PGMaster(14c995c1-3311-471b-a69b-fb764534e740) 
[INFO ] 2025-06-06 07:24:58.742 - [任务 15][PGMaster] - Table t1 has been completed batch read 
[TRACE] 2025-06-06 07:24:58.742 - [任务 15][PGMaster] - Initial sync completed 
[INFO ] 2025-06-06 07:24:58.743 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 07:24:58.743 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 07:24:58.744 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 07:24:58.744 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 07:24:58.744 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 07:24:58.744 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 07:24:58.799 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_0106e1f2_b8de_4a78_b964_cf517141b225 
[TRACE] 2025-06-06 07:24:58.799 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[TRACE] 2025-06-06 07:24:59.444 - [任务 15][Sybase] - Process after table "t1" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-06 07:24:59.445 - [任务 15][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-06 07:24:59.445 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 07:25:08.275 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 07:25:18.479 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 07:25:28.334 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 07:25:38.509 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84070040,"lsn":84070040,"txId":750,"ts_usec":1749165934787028}'} 
[INFO ] 2025-06-06 07:25:48.419 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84070408,"lsn_commit":84070408,"lsn":84070408,"ts_usec":1749165934787028}'} 
[INFO ] 2025-06-06 07:25:58.505 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84070408,"lsn_commit":84070408,"lsn":84070408,"ts_usec":1749165934787028}'} 
[INFO ] 2025-06-06 07:26:08.634 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84070408,"lsn_commit":84070408,"lsn":84070408,"ts_usec":1749165934787028}'} 
[INFO ] 2025-06-06 07:26:18.601 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84070408,"lsn_commit":84070408,"lsn":84070408,"ts_usec":1749165934787028}'} 
[INFO ] 2025-06-06 07:26:28.779 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84070408,"lsn_commit":84070408,"lsn":84070408,"ts_usec":1749165934787028}'} 
[INFO ] 2025-06-06 07:26:38.695 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:26:48.887 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:26:58.745 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:27:09.070 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:27:19.024 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:27:28.944 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:27:39.096 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:27:49.008 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:27:59.192 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:28:09.075 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:28:19.280 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071136,"lsn_commit":84071136,"lsn":84071136,"ts_usec":1749165993545954}'} 
[INFO ] 2025-06-06 07:28:29.140 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:28:39.320 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:28:49.181 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:28:59.331 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:29:09.228 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:29:19.396 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:29:29.291 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:29:39.449 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[INFO ] 2025-06-06 07:29:49.343 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[TRACE] 2025-06-06 07:29:49.691 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[TRACE] 2025-06-06 07:29:49.924 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 07:29:49.925 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749165898401 
[TRACE] 2025-06-06 07:29:49.925 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749165898401 
[TRACE] 2025-06-06 07:29:49.925 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 07:29:49.928 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 07:29:49.929 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 240 ms 
[TRACE] 2025-06-06 07:29:49.930 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[INFO ] 2025-06-06 07:29:49.930 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84071376,"lsn_commit":84071376,"lsn":84071376,"ts_usec":1749166100047905}'} 
[TRACE] 2025-06-06 07:29:49.990 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749165898183 
[TRACE] 2025-06-06 07:29:49.990 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749165898183 
[TRACE] 2025-06-06 07:29:49.990 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 07:29:49.990 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 07:29:50.197 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 62 ms 
[TRACE] 2025-06-06 07:29:55.883 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 07:29:56.859 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@26bc97f1 
[TRACE] 2025-06-06 07:29:56.859 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ad95cf5 
[TRACE] 2025-06-06 07:29:56.991 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 07:29:56.991 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 07:29:56.991 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 07:29:56.991 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 07:29:57.045 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 07:29:57.046 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 07:30:04.371 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 07:30:04.556 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 07:30:04.556 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 07:30:04.713 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 07:30:04.786 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 07:30:04.786 - [任务 15] - Task started 
[TRACE] 2025-06-06 07:30:04.817 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 07:30:04.817 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 07:30:04.818 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 07:30:04.818 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 07:30:04.818 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 07:30:04.962 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 07:30:04.962 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 07:30:04.962 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 07:30:04.999 - [任务 15][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-06 07:30:05.024 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 07:30:05.024 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 07:30:05.024 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[INFO ] 2025-06-06 07:30:05.026 - [任务 15][PGMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-06 07:30:05.026 - [任务 15][PGMaster] - Use existing batch read offset: {"t1":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84071376,\"lsn_commit\":84071376,\"lsn\":84071376,\"ts_usec\":1749166100047905}"} 
[INFO ] 2025-06-06 07:30:05.083 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 07:30:05.084 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 07:30:05.084 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 07:30:05.085 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84071376,\"lsn_commit\":84071376,\"lsn\":84071376,\"ts_usec\":1749166100047905}"} 
[INFO ] 2025-06-06 07:30:14.908 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 07:30:36.195 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 07:30:36.196 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_0106e1f2_b8de_4a78_b964_cf517141b225 
[TRACE] 2025-06-06 07:30:37.310 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-06-06 09:47:27.695 - [任务 15] - This task is already running 
[TRACE] 2025-06-06 11:39:12.093 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[TRACE] 2025-06-06 11:39:12.671 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 11:39:12.678 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749166204852 
[TRACE] 2025-06-06 11:39:12.678 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749166204852 
[TRACE] 2025-06-06 11:39:12.678 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 11:39:12.679 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 11:39:12.680 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 626 ms 
[TRACE] 2025-06-06 11:39:12.680 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[TRACE] 2025-06-06 11:39:12.760 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749166204897 
[TRACE] 2025-06-06 11:39:12.760 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749166204897 
[TRACE] 2025-06-06 11:39:12.760 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 11:39:12.760 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 11:39:12.761 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 80 ms 
[TRACE] 2025-06-06 11:39:18.990 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 11:39:19.994 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@61f1420d 
[TRACE] 2025-06-06 11:39:20.013 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@173c89e9 
[TRACE] 2025-06-06 11:39:20.013 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 11:39:20.135 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 11:39:20.135 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 11:39:20.135 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 11:39:20.192 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:39:20.193 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:40:52.518 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 11:40:52.610 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 11:40:52.610 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 11:40:52.705 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 11:40:52.705 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 11:40:52.817 - [任务 15] - Task started 
[TRACE] 2025-06-06 11:40:52.817 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:40:52.817 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:40:52.817 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 11:40:52.818 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 11:40:52.942 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 11:40:52.942 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 11:40:52.942 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 11:40:52.942 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 11:40:53.000 - [任务 15][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-06 11:40:53.001 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 11:40:53.001 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 11:40:53.001 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[INFO ] 2025-06-06 11:40:53.005 - [任务 15][PGMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-06 11:40:53.005 - [任务 15][PGMaster] - Use existing batch read offset: {"t1":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84071376,\"lsn_commit\":84071376,\"lsn\":84071376,\"ts_usec\":1749166100047905}"} 
[INFO ] 2025-06-06 11:40:53.056 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 11:40:53.056 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 11:40:53.058 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 11:40:53.058 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84071376,\"lsn_commit\":84071376,\"lsn\":84071376,\"ts_usec\":1749166100047905}"} 
[INFO ] 2025-06-06 11:40:58.501 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 11:40:58.529 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 11:40:58.532 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_0106e1f2_b8de_4a78_b964_cf517141b225 
[TRACE] 2025-06-06 11:40:58.738 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-06-06 11:43:08.169 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:43:18.182 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:43:28.384 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:43:38.333 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:43:48.260 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:43:58.437 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:44:08.329 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:44:18.487 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:44:28.396 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[WARN ] 2025-06-06 11:44:37.749 - [任务 15][PGMaster] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.net.SocketException: Broken pipe
	java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:425)
	java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:445)
	java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:831)
	java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1035)
	org.postgresql.util.internal.PgBufferedOutputStream.flushBuffer(PgBufferedOutputStream.java:41)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-06 11:44:37.950 - [任务 15][PGMaster] - Retry operation SOURCE_STREAM_READ, retry times 1/15, first retry time 2025-06-06 11:44:37, next retry time 2025-06-06 11:45:37 
[INFO ] 2025-06-06 11:44:38.561 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:44:48.446 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:44:58.600 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:45:08.475 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:45:18.631 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:45:28.513 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:45:38.700 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:45:48.837 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[WARN ] 2025-06-06 11:45:48.842 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-06-06 11:45:49.862 - [任务 15][PGMaster] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.net.ConnectException: Connection refused
	java.base/sun.nio.ch.Net.pollConnect(Native Method)
	java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-06 11:45:49.862 - [任务 15][PGMaster] - Retry operation SOURCE_STREAM_READ, retry times 2/15, first retry time 2025-06-06 11:44:37, next retry time 2025-06-06 11:46:49 
[INFO ] 2025-06-06 11:45:58.958 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:46:08.878 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:46:19.047 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[INFO ] 2025-06-06 11:46:28.948 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[TRACE] 2025-06-06 11:46:30.343 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[WARN ] 2025-06-06 11:46:30.347 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 11:46:30.347 - [任务 15][PGMaster] - Retry operation SOURCE_STREAM_READ failed, total cost 00:01:52.652000 
[TRACE] 2025-06-06 11:46:30.348 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181252838 
[TRACE] 2025-06-06 11:46:30.348 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181252838 
[TRACE] 2025-06-06 11:46:30.348 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 11:46:30.348 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 11:46:30.348 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 7 ms 
[TRACE] 2025-06-06 11:46:30.348 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[INFO ] 2025-06-06 11:46:30.349 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072464,"lsn_commit":84072464,"lsn":84072464,"ts_usec":1749181431599566}'} 
[TRACE] 2025-06-06 11:46:30.359 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 11:46:30.359 - [任务 15][PGMaster] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed. 
[ERROR] 2025-06-06 11:46:30.396 - [任务 15][PGMaster] - java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:290)
	io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:561)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:290)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (null) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 23 more

[TRACE] 2025-06-06 11:46:30.396 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181252878 
[TRACE] 2025-06-06 11:46:30.397 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181252878 
[TRACE] 2025-06-06 11:46:30.397 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 11:46:30.397 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 11:46:30.397 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 47 ms 
[TRACE] 2025-06-06 11:46:38.934 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 11:46:39.897 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@3d3b05f 
[TRACE] 2025-06-06 11:46:39.899 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f2aa9f6 
[TRACE] 2025-06-06 11:46:40.050 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 11:46:40.050 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 11:46:40.051 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 11:46:40.051 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 11:46:40.095 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:46:40.095 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:48:29.880 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 11:48:29.962 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 11:48:29.962 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 11:48:30.051 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 11:48:30.051 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 11:48:30.268 - [任务 15] - Task started 
[TRACE] 2025-06-06 11:48:30.281 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:48:30.283 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:48:30.285 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 1 ms 
[TRACE] 2025-06-06 11:48:30.285 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 1 ms 
[INFO ] 2025-06-06 11:48:30.286 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 11:48:30.475 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 11:48:30.475 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 11:48:30.475 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 11:48:30.492 - [任务 15][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-06 11:48:30.533 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 11:48:30.533 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 11:48:30.533 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[INFO ] 2025-06-06 11:48:30.534 - [任务 15][PGMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-06 11:48:30.534 - [任务 15][PGMaster] - Use existing batch read offset: {"t1":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84072464,\"lsn_commit\":84072464,\"lsn\":84072464,\"ts_usec\":1749181431599566}"} 
[INFO ] 2025-06-06 11:48:30.581 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 11:48:30.581 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 11:48:30.581 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 11:48:30.581 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84072464,\"lsn_commit\":84072464,\"lsn\":84072464,\"ts_usec\":1749181431599566}"} 
[INFO ] 2025-06-06 11:48:30.581 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 11:49:01.626 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 11:49:32.809 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_0106e1f2_b8de_4a78_b964_cf517141b225 
[TRACE] 2025-06-06 11:49:34.028 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-06-06 11:49:42.950 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":100850160,"lsn_commit":100850160,"lsn":100850160,"ts_usec":1749181663226333}'} 
[INFO ] 2025-06-06 11:49:52.801 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":100850160,"lsn_commit":100850160,"lsn":100850160,"ts_usec":1749181663226333}'} 
[TRACE] 2025-06-06 11:49:57.059 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[TRACE] 2025-06-06 11:49:57.163 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 11:49:57.171 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181710360 
[TRACE] 2025-06-06 11:49:57.171 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181710360 
[TRACE] 2025-06-06 11:49:57.171 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 11:49:57.174 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 11:49:57.174 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 276 ms 
[TRACE] 2025-06-06 11:49:57.175 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[INFO ] 2025-06-06 11:49:57.176 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":100850160,"lsn_commit":100850160,"lsn":100850160,"ts_usec":1749181663226333}'} 
[TRACE] 2025-06-06 11:49:57.252 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181710397 
[TRACE] 2025-06-06 11:49:57.252 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181710397 
[TRACE] 2025-06-06 11:49:57.252 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 11:49:57.252 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 11:49:57.253 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 78 ms 
[TRACE] 2025-06-06 11:50:02.934 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 11:50:03.840 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@667083ab 
[TRACE] 2025-06-06 11:50:03.841 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@79097633 
[TRACE] 2025-06-06 11:50:03.986 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 11:50:03.988 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 11:50:03.988 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 11:50:03.989 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 11:50:04.058 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:50:04.059 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:50:14.193 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 11:50:14.282 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 11:50:14.282 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 11:50:14.366 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 11:50:14.366 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 11:50:14.416 - [任务 15] - Task started 
[TRACE] 2025-06-06 11:50:14.416 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:50:14.416 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:50:14.416 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 11:50:14.416 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 11:50:14.537 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 11:50:14.538 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 11:50:14.538 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 11:50:14.538 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 11:50:14.552 - [任务 15][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-06 11:50:14.585 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 11:50:14.585 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 11:50:14.585 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[INFO ] 2025-06-06 11:50:14.585 - [任务 15][PGMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-06 11:50:14.586 - [任务 15][PGMaster] - Use existing batch read offset: {"t1":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":100850160,\"lsn_commit\":100850160,\"lsn\":100850160,\"ts_usec\":1749181663226333}"} 
[INFO ] 2025-06-06 11:50:14.638 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 11:50:14.639 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 11:50:14.639 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 11:50:14.640 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":100850160,\"lsn_commit\":100850160,\"lsn\":100850160,\"ts_usec\":1749181663226333}"} 
[INFO ] 2025-06-06 11:50:14.640 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 11:50:39.335 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 11:50:39.406 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_0106e1f2_b8de_4a78_b964_cf517141b225 
[TRACE] 2025-06-06 11:50:39.407 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[TRACE] 2025-06-06 11:50:47.939 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[TRACE] 2025-06-06 11:50:48.254 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 11:50:48.263 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181814430 
[TRACE] 2025-06-06 11:50:48.264 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181814430 
[TRACE] 2025-06-06 11:50:48.264 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 11:50:48.267 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 11:50:48.268 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 500 ms 
[TRACE] 2025-06-06 11:50:48.268 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[TRACE] 2025-06-06 11:50:48.309 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181814478 
[TRACE] 2025-06-06 11:50:48.309 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181814478 
[TRACE] 2025-06-06 11:50:48.309 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 11:50:48.309 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 11:50:48.310 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 41 ms 
[TRACE] 2025-06-06 11:50:54.390 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 11:50:55.360 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@4b43a58a 
[TRACE] 2025-06-06 11:50:55.362 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20a19195 
[TRACE] 2025-06-06 11:50:55.505 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 11:50:55.505 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 11:50:55.505 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 11:50:55.506 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 11:50:55.557 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:50:55.558 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:51:28.234 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 11:51:28.237 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 11:51:28.401 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 11:51:28.401 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 11:51:28.453 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 11:51:28.453 - [任务 15] - Task started 
[TRACE] 2025-06-06 11:51:28.468 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:51:28.468 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 11:51:28.468 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:51:28.468 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 11:51:28.669 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 11:51:29.092 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 11:51:29.100 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 11:51:29.100 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 11:51:29.159 - [任务 15][Sybase] - Apply table structure to target database 
[TRACE] 2025-06-06 11:51:29.160 - [任务 15][Sybase] - The table t1 has already exist. 
[INFO ] 2025-06-06 11:51:29.361 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 11:51:29.361 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 11:51:29.361 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-06 11:51:29.361 - [任务 15][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-06 11:52:12.819 - [任务 15][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84072232,\"lsn_commit\":84072232,\"lsn\":84072232,\"ts_usec\":1749181367901602}"} 
[INFO ] 2025-06-06 11:52:12.819 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 11:52:12.819 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 11:52:12.819 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 11:52:12.820 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84072232,\"lsn_commit\":84072232,\"lsn\":84072232,\"ts_usec\":1749181367901602}"} 
[INFO ] 2025-06-06 11:52:12.820 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 11:52:13.949 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 11:52:13.949 - [任务 15][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_e055fd3d_a194_440a_8d11_ffb21bb9dcac 
[TRACE] 2025-06-06 11:52:14.150 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-06-06 11:52:14.421 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:52:22.929 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:52:32.794 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[TRACE] 2025-06-06 11:52:37.855 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[TRACE] 2025-06-06 11:52:38.147 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 11:52:38.147 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181889212 
[TRACE] 2025-06-06 11:52:38.147 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749181889212 
[TRACE] 2025-06-06 11:52:38.148 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 11:52:38.149 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 11:52:38.149 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 294 ms 
[TRACE] 2025-06-06 11:52:38.150 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[INFO ] 2025-06-06 11:52:38.150 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[TRACE] 2025-06-06 11:52:38.187 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181889024 
[TRACE] 2025-06-06 11:52:38.187 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749181889024 
[TRACE] 2025-06-06 11:52:38.188 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 11:52:38.189 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 11:52:38.189 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 39 ms 
[TRACE] 2025-06-06 11:52:47.811 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 11:52:48.814 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@2c037676 
[TRACE] 2025-06-06 11:52:48.815 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@713d1a2d 
[TRACE] 2025-06-06 11:52:48.817 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 11:52:48.940 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 11:52:48.940 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 11:52:48.988 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 11:52:48.988 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:52:48.988 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[INFO ] 2025-06-06 11:52:53.925 - [任务 15] - This task already stopped. 
[TRACE] 2025-06-06 11:56:43.970 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 11:56:44.178 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 11:56:44.234 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 11:56:44.431 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 11:56:44.579 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 11:56:44.579 - [任务 15] - Task started 
[TRACE] 2025-06-06 11:56:44.600 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:56:44.600 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:56:44.600 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 11:56:44.601 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 11:56:44.601 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 11:56:45.196 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 11:56:45.196 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 11:56:45.196 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 11:56:45.275 - [任务 15][Sybase] - Apply table structure to target database 
[TRACE] 2025-06-06 11:56:45.277 - [任务 15][Sybase] - The table t1 has already exist. 
[INFO ] 2025-06-06 11:56:45.445 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 11:56:45.445 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 11:56:45.445 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-06 11:56:45.445 - [任务 15][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-06 11:57:21.591 - [任务 15][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84072232,\"lsn_commit\":84072232,\"lsn\":84072232,\"ts_usec\":1749181367901602}"} 
[INFO ] 2025-06-06 11:57:21.591 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 11:57:21.591 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 11:57:21.591 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 11:57:21.591 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":84072232,\"lsn_commit\":84072232,\"lsn\":84072232,\"ts_usec\":1749181367901602}"} 
[INFO ] 2025-06-06 11:57:21.591 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 11:57:21.619 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 11:57:21.620 - [任务 15][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_a9b4fd2d_5cc6_419c_8d53_3266941b93ac 
[TRACE] 2025-06-06 11:57:21.824 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-06-06 11:57:22.005 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:57:27.252 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:57:37.418 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:57:47.421 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:57:57.330 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":84072232,"lsn_commit":84072232,"lsn":84072232,"ts_usec":1749181367901602}'} 
[INFO ] 2025-06-06 11:58:07.499 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":100851776,"lsn_commit":100851776,"lsn":100851776,"ts_usec":1749182279124775}'} 
[INFO ] 2025-06-06 11:58:17.370 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":100851776,"lsn_commit":100851776,"lsn":100851776,"ts_usec":1749182279124775}'} 
[TRACE] 2025-06-06 11:58:19.718 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[TRACE] 2025-06-06 11:58:19.718 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 11:58:19.724 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749182205299 
[TRACE] 2025-06-06 11:58:19.724 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749182205299 
[TRACE] 2025-06-06 11:58:19.724 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 11:58:19.724 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 11:58:19.725 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 22 ms 
[TRACE] 2025-06-06 11:58:19.725 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[INFO ] 2025-06-06 11:58:19.725 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":100851776,"lsn_commit":100851776,"lsn":100851776,"ts_usec":1749182279124775}'} 
[TRACE] 2025-06-06 11:58:19.779 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749182205132 
[TRACE] 2025-06-06 11:58:19.783 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749182205132 
[TRACE] 2025-06-06 11:58:19.783 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 11:58:19.784 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 11:58:19.784 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 59 ms 
[TRACE] 2025-06-06 11:58:27.312 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 11:58:28.318 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@22a87a06 
[TRACE] 2025-06-06 11:58:28.318 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3c2ba022 
[TRACE] 2025-06-06 11:58:28.325 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 11:58:28.501 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 11:58:28.501 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 11:58:28.501 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 11:58:28.677 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:58:28.677 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:58:49.561 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 11:58:49.767 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 11:58:49.875 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 11:58:49.875 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 11:58:49.978 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 11:58:50.016 - [任务 15] - Task started 
[TRACE] 2025-06-06 11:58:50.016 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:58:50.016 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 11:58:50.016 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] start preload schema,table counts: 1 
[TRACE] 2025-06-06 11:58:50.020 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 11:58:50.144 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 11:58:50.144 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 11:58:50.144 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 11:58:50.144 - [任务 15][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-06 11:58:50.198 - [任务 15][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-06 11:58:50.198 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 11:58:50.198 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 11:58:50.198 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[INFO ] 2025-06-06 11:58:50.198 - [任务 15][PGMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-06 11:58:50.199 - [任务 15][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":100851776,\"lsn_commit\":100851776,\"lsn\":100851776,\"ts_usec\":1749182279124775}"} 
[INFO ] 2025-06-06 11:58:50.247 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 11:58:50.247 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 11:58:50.247 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 11:58:50.248 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":100851776,\"lsn_commit\":100851776,\"lsn\":100851776,\"ts_usec\":1749182279124775}"} 
[INFO ] 2025-06-06 11:58:50.248 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 11:59:09.271 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 11:59:09.271 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_a9b4fd2d_5cc6_419c_8d53_3266941b93ac 
[TRACE] 2025-06-06 11:59:09.472 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[TRACE] 2025-06-06 11:59:16.320 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] running status set to false 
[TRACE] 2025-06-06 11:59:16.618 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 11:59:16.618 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749182330043 
[TRACE] 2025-06-06 11:59:16.618 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_14c995c1-3311-471b-a69b-fb764534e740_1749182330043 
[TRACE] 2025-06-06 11:59:16.618 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] schema data cleaned 
[TRACE] 2025-06-06 11:59:16.618 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] monitor closed 
[TRACE] 2025-06-06 11:59:16.619 - [任务 15][PGMaster] - Node PGMaster[14c995c1-3311-471b-a69b-fb764534e740] close complete, cost 298 ms 
[TRACE] 2025-06-06 11:59:16.619 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] running status set to false 
[TRACE] 2025-06-06 11:59:16.650 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749182330082 
[TRACE] 2025-06-06 11:59:16.650 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_9dd7fe4d-03a3-481e-a321-f540638084dc_1749182330082 
[TRACE] 2025-06-06 11:59:16.651 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] schema data cleaned 
[TRACE] 2025-06-06 11:59:16.651 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] monitor closed 
[TRACE] 2025-06-06 11:59:16.651 - [任务 15][Sybase] - Node Sybase[9dd7fe4d-03a3-481e-a321-f540638084dc] close complete, cost 32 ms 
[TRACE] 2025-06-06 11:59:24.287 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 11:59:25.290 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@71e0efe6 
[TRACE] 2025-06-06 11:59:25.293 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@43a8a8ad 
[TRACE] 2025-06-06 11:59:25.294 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 11:59:25.447 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 11:59:25.448 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 11:59:25.448 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 11:59:25.510 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 11:59:25.512 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 13:28:00.120 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 13:28:00.122 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 13:28:00.844 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 13:28:00.844 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 13:28:01.051 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 13:28:01.181 - [任务 15] - Task started 
[TRACE] 2025-06-06 13:28:01.185 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] start preload schema,table counts: 1 
[TRACE] 2025-06-06 13:28:01.192 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] start preload schema,table counts: 1 
[TRACE] 2025-06-06 13:28:01.197 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 13:28:01.197 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 13:28:01.199 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 13:28:01.914 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 13:28:01.918 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 13:28:01.923 - [任务 15][Sybase] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-06 13:28:02.133 - [任务 15][Sybase] - Apply table structure to target database 
[TRACE] 2025-06-06 13:28:02.137 - [任务 15][Sybase] - The table t1 has already exist. 
[INFO ] 2025-06-06 13:28:02.216 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 13:28:02.219 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 13:28:02.219 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-06 13:28:05.756 - [任务 15][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-06 13:28:05.842 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 13:28:05.844 - [任务 15][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_7024fd00_d562_4e4b_a0f9_31b043e7c77d 
[INFO ] 2025-06-06 13:28:05.913 - [任务 15][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 13:28:05.914 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 13:28:05.914 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 13:28:05.918 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 13:28:05.919 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 13:28:05.919 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 13:28:05.930 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 13:28:05.930 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_7024fd00_d562_4e4b_a0f9_31b043e7c77d 
[TRACE] 2025-06-06 13:28:06.332 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-06-06 13:28:07.740 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 13:28:11.925 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 13:28:22.127 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 13:28:31.993 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101001832,"lsn":101001832,"txId":760,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:28:42.162 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:28:52.049 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:29:02.185 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:29:12.093 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:29:22.107 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:29:32.229 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:29:42.497 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:29:52.601 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:30:02.452 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:30:12.471 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:30:22.494 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:30:32.751 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:30:42.581 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:30:52.673 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:31:02.668 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:31:12.710 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:31:22.739 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:31:32.782 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:31:42.831 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:31:52.887 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:32:02.896 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:32:12.920 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:32:22.964 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:32:32.982 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:32:43.007 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:32:53.033 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:33:03.149 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:33:13.237 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:33:23.094 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:33:33.126 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:33:43.147 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:33:53.168 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:34:03.306 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:34:13.369 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:34:23.242 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:34:33.342 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:34:43.460 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:34:53.318 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:35:03.337 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:35:13.404 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:35:23.428 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:35:33.443 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:35:43.459 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:35:53.479 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:36:03.521 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:36:13.532 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:36:23.560 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:36:33.581 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:36:43.607 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:36:53.625 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:37:03.645 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:37:13.669 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:37:23.686 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:37:33.712 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:37:43.738 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:37:53.848 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:38:03.946 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:38:13.846 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:38:23.884 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:38:33.972 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:38:43.855 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:38:53.873 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:39:03.889 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:39:14.089 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:39:23.934 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:39:33.953 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:39:43.975 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:39:54.047 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:40:04.131 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:40:14.049 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:40:24.067 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:40:34.109 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:40:44.132 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:40:54.163 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:41:04.191 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:41:14.196 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:41:24.223 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:41:34.240 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:41:44.267 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:41:54.285 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:42:04.351 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:42:14.329 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:42:24.352 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:42:34.467 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:42:44.593 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:42:54.407 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:43:04.457 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:43:14.575 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:43:24.470 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:43:34.499 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:43:44.525 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:43:54.774 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:44:04.862 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:44:14.747 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:44:24.716 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:44:34.735 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:44:44.756 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:44:54.776 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:45:04.912 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:45:14.820 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:45:24.835 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:45:34.917 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:45:45.064 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:45:54.897 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:46:04.911 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:46:14.926 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:46:24.951 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:46:35.043 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:46:45.175 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:46:55.048 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:47:05.113 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:47:15.074 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:47:25.246 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:47:35.118 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:47:45.261 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:47:55.188 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:48:05.380 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:48:15.218 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:48:25.400 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:48:35.251 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:48:45.452 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:48:55.437 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:49:05.320 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:49:15.465 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:49:25.498 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:49:35.592 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:49:45.433 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:49:55.469 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:50:05.451 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:50:15.474 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:50:25.583 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:50:35.695 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:50:45.542 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:50:55.755 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:51:05.580 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:51:15.600 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:51:25.708 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:51:35.826 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:51:45.646 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:51:55.664 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:52:05.789 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:52:15.846 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:52:25.722 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:52:35.738 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:52:45.748 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:52:55.757 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:53:05.769 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:53:15.907 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:53:25.800 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:53:35.818 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:53:45.840 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:53:55.856 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:54:05.911 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:54:15.959 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:54:26.074 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:54:35.959 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:54:46.017 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:54:56.147 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:55:05.997 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:55:16.017 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:55:26.040 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:55:36.058 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:55:46.077 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:55:56.148 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:56:06.163 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:56:16.278 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:56:26.158 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:56:36.173 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:56:46.228 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:56:56.347 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:57:06.227 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:57:16.249 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:57:26.356 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:57:36.441 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:57:46.309 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:57:56.328 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:58:06.439 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:58:16.559 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:58:26.421 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:58:36.470 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:58:46.526 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:58:56.661 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:59:06.498 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:59:16.525 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:59:26.541 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:59:36.584 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:59:46.691 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 13:59:56.805 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:00:06.623 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:00:16.645 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:00:26.752 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:00:36.864 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:00:46.888 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:00:56.721 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:01:06.789 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:01:16.845 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:01:26.772 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:01:36.945 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:01:46.808 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:01:57.010 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:02:06.972 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:02:16.882 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:02:26.912 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:02:37.080 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:02:46.961 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:02:57.147 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:03:07.012 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:03:17.200 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:03:27.073 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:03:37.258 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:03:47.132 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:03:57.314 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:04:07.180 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:04:17.329 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:04:27.251 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:04:37.424 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:04:47.324 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:04:57.508 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:05:07.400 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:05:17.597 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:05:27.480 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:05:37.663 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:05:47.559 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:05:57.579 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:06:07.800 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:06:17.748 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:06:27.671 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:06:37.844 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:06:47.741 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:06:57.916 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:07:07.899 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:07:17.816 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:07:28.007 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:07:37.878 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:07:48.047 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:07:57.932 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:08:08.087 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:08:17.981 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:08:28.173 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:08:38.033 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:08:48.054 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:08:58.237 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:09:08.099 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:09:18.285 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:09:28.139 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:09:38.309 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:09:48.202 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:09:58.388 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:10:08.265 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:10:18.447 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:10:28.321 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:10:38.544 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:10:48.405 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:10:58.546 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:11:08.485 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:11:18.668 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:11:28.548 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:11:38.716 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:11:48.594 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:11:58.768 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:12:08.657 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:12:18.812 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:12:28.720 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:12:38.901 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:12:48.779 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:12:58.964 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:13:08.819 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:13:18.985 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:13:28.877 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:13:39.014 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:13:48.921 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:13:59.100 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:14:08.986 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:14:19.149 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:14:29.046 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:14:39.200 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:14:49.094 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:14:59.263 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:15:09.162 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:15:19.335 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:15:29.214 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:15:39.378 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:15:49.271 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:15:59.287 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:16:09.388 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:16:19.404 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:16:29.419 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:16:39.595 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:16:49.449 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:16:59.657 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:17:09.487 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:17:19.507 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:17:29.696 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:17:39.687 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:17:49.587 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:17:59.748 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:18:09.634 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:18:19.830 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:18:29.688 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:18:39.876 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:18:49.737 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:18:59.910 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:19:09.796 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:19:19.975 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:19:29.850 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:19:40.013 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:19:49.914 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:20:00.101 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:20:10.067 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:20:20.007 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:20:30.188 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:20:40.042 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:20:50.179 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:21:00.080 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:21:10.264 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:21:20.105 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:21:30.120 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:21:40.288 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:21:50.150 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:22:00.353 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:22:10.190 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:22:20.376 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:22:30.232 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:22:40.404 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:22:50.285 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:23:00.471 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:23:10.357 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:23:20.540 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:23:30.406 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:23:40.588 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:23:50.478 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:24:00.671 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:24:10.515 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:24:20.690 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:24:30.579 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:24:40.769 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:24:50.616 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:25:00.798 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:25:10.675 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:25:20.856 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:25:30.725 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:25:40.901 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:25:50.778 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:26:00.988 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:26:10.833 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:26:21.004 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:26:30.877 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:26:41.052 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:26:50.958 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:27:01.133 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:27:11.033 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:27:21.188 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:27:31.080 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:27:41.255 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:27:51.120 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:28:01.308 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:28:11.183 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:28:21.363 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:28:31.239 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:28:41.423 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:28:51.294 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:29:01.520 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:29:11.458 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:29:21.366 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:29:31.549 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:29:41.415 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:29:51.591 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:30:01.447 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:30:11.585 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:30:21.495 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:30:31.680 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:30:41.552 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:30:51.737 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:31:01.595 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:31:11.775 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:31:21.645 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:31:31.821 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:31:41.715 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:31:51.901 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:32:01.763 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:32:11.958 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:32:21.816 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:32:31.980 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:32:41.866 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:32:52.046 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:33:01.927 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:33:12.096 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:33:21.988 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:33:32.149 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:33:42.031 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:33:52.188 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:34:02.064 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:34:12.200 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:34:22.104 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:34:32.290 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:34:42.154 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:34:52.325 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:35:02.197 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:35:12.349 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:35:22.243 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:35:32.405 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:35:42.279 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:35:52.468 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:36:02.433 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:36:12.345 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:36:22.486 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:36:32.422 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:36:42.593 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:36:52.466 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:37:02.645 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:37:12.517 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:37:22.678 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:37:32.568 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:37:42.737 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:37:52.625 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:38:02.793 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:38:12.672 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:38:22.819 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:38:32.727 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:38:42.886 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:38:52.766 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:39:02.896 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:39:12.821 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:39:22.999 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:39:32.873 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:39:43.055 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:39:52.924 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:40:03.079 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:40:12.981 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:40:23.154 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:40:33.028 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:40:43.206 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:40:53.085 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:41:03.250 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:41:13.139 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:41:23.318 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:41:33.198 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:41:43.224 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:41:53.408 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:42:03.276 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:42:13.463 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:42:23.330 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:42:33.491 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:42:43.375 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:42:53.553 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:43:03.412 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:43:13.586 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:43:23.452 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:43:33.633 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:43:43.491 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:43:53.672 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:44:03.523 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:44:13.710 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:44:23.548 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:44:33.702 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:44:43.593 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:44:53.751 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:45:03.639 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:45:13.812 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:45:23.680 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:45:33.854 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:45:43.736 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:45:53.915 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:46:03.775 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:46:13.789 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:46:23.967 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:46:33.824 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:46:43.962 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:46:53.865 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:47:04.035 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:47:13.913 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:47:24.082 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:47:33.961 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:47:44.109 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:47:54.020 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:48:04.167 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:48:14.066 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:48:24.222 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:48:34.140 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:48:44.274 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:48:54.200 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:49:04.370 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:49:14.249 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:49:24.412 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:49:34.352 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:49:44.531 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:49:54.394 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:50:04.560 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:50:14.431 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:50:24.674 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:50:34.551 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:50:44.733 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:50:54.601 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:51:04.769 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:51:14.646 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:51:24.774 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:51:34.683 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:51:44.868 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:51:54.724 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:52:04.909 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:52:14.753 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:52:24.926 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:52:34.782 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:52:44.955 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:52:54.812 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:53:04.827 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:53:15.014 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:53:24.857 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:53:35.032 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:53:44.889 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:53:55.067 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:54:04.920 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:54:14.935 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:54:25.100 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:54:34.976 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:54:45.001 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:54:55.159 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:55:05.050 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:55:15.247 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:55:25.092 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:55:35.282 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:55:45.143 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:55:55.317 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:56:05.201 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:56:15.355 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:56:25.232 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:56:35.399 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:56:45.265 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:56:55.450 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:57:05.305 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:57:15.323 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:57:25.432 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:57:35.353 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:57:45.529 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[INFO ] 2025-06-06 14:57:55.391 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[TRACE] 2025-06-06 14:58:01.052 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] running status set to false 
[TRACE] 2025-06-06 14:58:01.527 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 14:58:01.528 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_674926dd-eeca-44d7-88e8-062af52a5e7f_1749187682050 
[TRACE] 2025-06-06 14:58:01.529 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_674926dd-eeca-44d7-88e8-062af52a5e7f_1749187682050 
[TRACE] 2025-06-06 14:58:01.529 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] schema data cleaned 
[TRACE] 2025-06-06 14:58:01.532 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] monitor closed 
[TRACE] 2025-06-06 14:58:01.532 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] close complete, cost 485 ms 
[TRACE] 2025-06-06 14:58:01.534 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] running status set to false 
[INFO ] 2025-06-06 14:58:01.535 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='{"lsn_proc":101002584,"lsn_commit":101002584,"lsn":101002584,"ts_usec":1749187711024596}'} 
[TRACE] 2025-06-06 14:58:01.584 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f20fb3d7-279b-4dc1-8790-ef24c42cc0a0_1749187681797 
[TRACE] 2025-06-06 14:58:01.584 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f20fb3d7-279b-4dc1-8790-ef24c42cc0a0_1749187681797 
[TRACE] 2025-06-06 14:58:01.584 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] schema data cleaned 
[TRACE] 2025-06-06 14:58:01.585 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] monitor closed 
[TRACE] 2025-06-06 14:58:01.789 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] close complete, cost 52 ms 
[TRACE] 2025-06-06 14:58:06.782 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 14:58:07.788 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@18c73fee 
[TRACE] 2025-06-06 14:58:07.801 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49fef722 
[TRACE] 2025-06-06 14:58:07.803 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 14:58:07.924 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 14:58:07.925 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 14:58:07.991 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 14:58:07.991 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 14:58:07.991 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 17:18:50.189 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 17:18:50.191 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 17:18:50.365 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 17:18:50.365 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 17:18:50.469 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 17:18:50.470 - [任务 15] - Task started 
[TRACE] 2025-06-06 17:18:50.494 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] start preload schema,table counts: 1 
[TRACE] 2025-06-06 17:18:50.494 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 17:18:50.494 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] start preload schema,table counts: 1 
[TRACE] 2025-06-06 17:18:50.494 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 17:18:50.698 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 17:18:51.068 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 17:18:51.076 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 17:18:51.076 - [任务 15][Sybase] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-06 17:18:51.177 - [任务 15][Sybase] - Apply table structure to target database 
[TRACE] 2025-06-06 17:18:51.184 - [任务 15][Sybase] - The table t1 has already exist. 
[INFO ] 2025-06-06 17:18:51.459 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 17:18:51.460 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 17:18:51.460 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-06 17:18:51.460 - [任务 15][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-06 17:18:53.879 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 17:18:53.895 - [任务 15][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_5c2873ad_f27a_4135_80e6_b396d1fda431 
[INFO ] 2025-06-06 17:18:53.898 - [任务 15][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 17:18:53.952 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 17:18:53.953 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 17:18:53.953 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 17:18:53.953 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 17:18:53.953 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 17:18:53.956 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 17:18:53.956 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_5c2873ad_f27a_4135_80e6_b396d1fda431 
[TRACE] 2025-06-06 17:18:54.160 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[INFO ] 2025-06-06 17:18:55.784 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 17:19:01.275 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[INFO ] 2025-06-06 17:19:11.222 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[TRACE] 2025-06-06 17:19:14.856 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] running status set to false 
[TRACE] 2025-06-06 17:19:15.094 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 17:19:15.094 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_674926dd-eeca-44d7-88e8-062af52a5e7f_1749201531217 
[TRACE] 2025-06-06 17:19:15.094 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_674926dd-eeca-44d7-88e8-062af52a5e7f_1749201531217 
[TRACE] 2025-06-06 17:19:15.094 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] schema data cleaned 
[TRACE] 2025-06-06 17:19:15.094 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] monitor closed 
[TRACE] 2025-06-06 17:19:15.095 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] close complete, cost 240 ms 
[TRACE] 2025-06-06 17:19:15.095 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] running status set to false 
[INFO ] 2025-06-06 17:19:15.095 - [任务 15][Sybase] - streamOffset value is PostgresOffset{sortString='null', offsetValue=null, sourceOffset='null'} 
[TRACE] 2025-06-06 17:19:15.135 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f20fb3d7-279b-4dc1-8790-ef24c42cc0a0_1749201531009 
[TRACE] 2025-06-06 17:19:15.135 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f20fb3d7-279b-4dc1-8790-ef24c42cc0a0_1749201531009 
[TRACE] 2025-06-06 17:19:15.135 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] schema data cleaned 
[TRACE] 2025-06-06 17:19:15.135 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] monitor closed 
[TRACE] 2025-06-06 17:19:15.338 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] close complete, cost 40 ms 
[TRACE] 2025-06-06 17:19:24.695 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 17:19:25.590 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@68cb8db4 
[TRACE] 2025-06-06 17:19:25.592 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72d43017 
[TRACE] 2025-06-06 17:19:25.720 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 17:19:25.721 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 17:19:25.722 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 17:19:25.722 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 17:19:25.765 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 17:19:25.767 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 17:20:07.796 - [任务 15] - Task initialization... 
[TRACE] 2025-06-06 17:20:07.797 - [任务 15] - Start task milestones: 6842272925a89e2512babc75(任务 15) 
[INFO ] 2025-06-06 17:20:08.024 - [任务 15] - Loading table structure completed 
[TRACE] 2025-06-06 17:20:08.212 - [任务 15] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-06 17:20:08.213 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-06 17:20:08.271 - [任务 15] - Task started 
[TRACE] 2025-06-06 17:20:08.271 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] start preload schema,table counts: 1 
[TRACE] 2025-06-06 17:20:08.271 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] start preload schema,table counts: 1 
[TRACE] 2025-06-06 17:20:08.271 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] preload schema finished, cost 0 ms 
[TRACE] 2025-06-06 17:20:08.271 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] preload schema finished, cost 0 ms 
[INFO ] 2025-06-06 17:20:08.396 - [任务 15][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-06 17:20:08.401 - [任务 15][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-06 17:20:08.401 - [任务 15][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-06 17:20:08.401 - [任务 15][Sybase] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-06 17:20:08.488 - [任务 15][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-06 17:20:08.488 - [任务 15][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-06 17:20:08.488 - [任务 15][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-06 17:20:08.488 - [任务 15][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[INFO ] 2025-06-06 17:20:08.491 - [任务 15][PGMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-06 17:20:08.491 - [任务 15][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 17:20:08.541 - [任务 15][PGMaster] - Batch read completed. 
[TRACE] 2025-06-06 17:20:08.541 - [任务 15][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-06 17:20:08.541 - [任务 15][PGMaster] - Initial sync completed 
[TRACE] 2025-06-06 17:20:46.397 - [任务 15][PGMaster] - Starting stream read, table list: [t1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-06 17:20:46.409 - [任务 15][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-06 17:20:46.409 - [任务 15][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-06 17:20:46.614 - [任务 15][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_5c2873ad_f27a_4135_80e6_b396d1fda431 
[TRACE] 2025-06-06 17:20:47.628 - [任务 15][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [t1], data change syncing 
[TRACE] 2025-06-06 17:55:16.870 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] running status set to false 
[TRACE] 2025-06-06 17:55:17.232 - [任务 15][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-06 17:55:17.233 - [任务 15][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_674926dd-eeca-44d7-88e8-062af52a5e7f_1749201608337 
[TRACE] 2025-06-06 17:55:17.234 - [任务 15][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_674926dd-eeca-44d7-88e8-062af52a5e7f_1749201608337 
[TRACE] 2025-06-06 17:55:17.234 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] schema data cleaned 
[TRACE] 2025-06-06 17:55:17.234 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] monitor closed 
[TRACE] 2025-06-06 17:55:17.234 - [任务 15][PGMaster] - Node PGMaster[674926dd-eeca-44d7-88e8-062af52a5e7f] close complete, cost 450 ms 
[TRACE] 2025-06-06 17:55:17.234 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] running status set to false 
[TRACE] 2025-06-06 17:55:17.278 - [任务 15][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f20fb3d7-279b-4dc1-8790-ef24c42cc0a0_1749201608337 
[TRACE] 2025-06-06 17:55:17.278 - [任务 15][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f20fb3d7-279b-4dc1-8790-ef24c42cc0a0_1749201608337 
[TRACE] 2025-06-06 17:55:17.278 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] schema data cleaned 
[TRACE] 2025-06-06 17:55:17.278 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] monitor closed 
[TRACE] 2025-06-06 17:55:17.278 - [任务 15][Sybase] - Node Sybase[f20fb3d7-279b-4dc1-8790-ef24c42cc0a0] close complete, cost 44 ms 
[TRACE] 2025-06-06 17:55:23.015 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-06 17:55:24.019 - [任务 15] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@538d9c0a 
[TRACE] 2025-06-06 17:55:24.025 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@30d756c7 
[TRACE] 2025-06-06 17:55:24.025 - [任务 15] - Stop task milestones: 6842272925a89e2512babc75(任务 15)  
[TRACE] 2025-06-06 17:55:24.158 - [任务 15] - Stopped task aspect(s) 
[TRACE] 2025-06-06 17:55:24.159 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2025-06-06 17:55:24.159 - [任务 15] - Task stopped. 
[TRACE] 2025-06-06 17:55:24.207 - [任务 15] - Remove memory task client succeed, task: 任务 15[6842272925a89e2512babc75] 
[TRACE] 2025-06-06 17:55:24.207 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6842272925a89e2512babc75] 
