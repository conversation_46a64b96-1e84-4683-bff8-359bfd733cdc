[INFO ] 2024-03-29 16:30:24.674 - [employees_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 16:30:24.754 - [employees_import_import_import_import_import_import_import] - Start task milestones: 660677cf18990e56b52705a4(employees_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 16:30:24.755 - [employees_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 16:30:25.170 - [employees_import_import_import_import_import_import_import] - The engine receives employees_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[90d8cdf7-17f0-4cbb-b585-fcbe121ebe49] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[d0686d0a-6a42-4e8b-a679-1ab54b8fb512] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[4c312b86-0063-481b-af1f-face07433a06] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[0b84140a-876e-4859-86f0-91213001a2c5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[ed5abbfa-da94-4475-acb5-4c4fcbd06e73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][merge] - Node merge[05cfcb4e-08d2-465a-a7ce-20861b5c7847] start preload schema,table counts: 6 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Territories] - Node Territories[6f3d2c70-c3b8-4181-bf8d-c652fdaea122] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[74102339-ddf4-4a91-8e02-444e951516c6] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[db24b76a-43a4-46a2-ba0d-0b68d4bcd640] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Region] - Node Region[d3705c30-c8a1-4e6f-858d-28ec3bc5066e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dc7f15f4-6bae-40f0-97c5-195867375062] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.180 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[31152eb7-9fe1-4867-869b-2b89580126d3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.253 - [employees_import_import_import_import_import_import_import][employees] - Node employees[48032b03-fb44-44d0-81e0-0ece8835c045] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:30:25.254 - [employees_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[d0686d0a-6a42-4e8b-a679-1ab54b8fb512] preload schema finished, cost 75 ms 
[INFO ] 2024-03-29 16:30:25.254 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[90d8cdf7-17f0-4cbb-b585-fcbe121ebe49] preload schema finished, cost 75 ms 
[INFO ] 2024-03-29 16:30:25.254 - [employees_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dc7f15f4-6bae-40f0-97c5-195867375062] preload schema finished, cost 74 ms 
[INFO ] 2024-03-29 16:30:25.254 - [employees_import_import_import_import_import_import_import][Region] - Node Region[d3705c30-c8a1-4e6f-858d-28ec3bc5066e] preload schema finished, cost 74 ms 
[INFO ] 2024-03-29 16:30:25.254 - [employees_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[4c312b86-0063-481b-af1f-face07433a06] preload schema finished, cost 74 ms 
[INFO ] 2024-03-29 16:30:25.258 - [employees_import_import_import_import_import_import_import][Territories] - Node Territories[6f3d2c70-c3b8-4181-bf8d-c652fdaea122] preload schema finished, cost 79 ms 
[INFO ] 2024-03-29 16:30:25.259 - [employees_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[ed5abbfa-da94-4475-acb5-4c4fcbd06e73] preload schema finished, cost 80 ms 
[INFO ] 2024-03-29 16:30:25.259 - [employees_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec] preload schema finished, cost 79 ms 
[INFO ] 2024-03-29 16:30:25.312 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[74102339-ddf4-4a91-8e02-444e951516c6] preload schema finished, cost 133 ms 
[INFO ] 2024-03-29 16:30:25.313 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[31152eb7-9fe1-4867-869b-2b89580126d3] preload schema finished, cost 130 ms 
[INFO ] 2024-03-29 16:30:25.313 - [employees_import_import_import_import_import_import_import][employees] - Node employees[48032b03-fb44-44d0-81e0-0ece8835c045] preload schema finished, cost 99 ms 
[INFO ] 2024-03-29 16:30:25.313 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[db24b76a-43a4-46a2-ba0d-0b68d4bcd640] preload schema finished, cost 133 ms 
[INFO ] 2024-03-29 16:30:25.313 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[0b84140a-876e-4859-86f0-91213001a2c5] preload schema finished, cost 134 ms 
[INFO ] 2024-03-29 16:30:25.388 - [employees_import_import_import_import_import_import_import][merge] - Node merge[05cfcb4e-08d2-465a-a7ce-20861b5c7847] preload schema finished, cost 208 ms 
[INFO ] 2024-03-29 16:30:25.390 - [employees_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 16:30:25.390 - [employees_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Territories(ed5abbfa-da94-4475-acb5-4c4fcbd06e73)
    ->Region(46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec)
} 
[INFO ] 2024-03-29 16:30:25.390 - [employees_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  EmployeeTerritories(d0686d0a-6a42-4e8b-a679-1ab54b8fb512)
    ->Territories(ed5abbfa-da94-4475-acb5-4c4fcbd06e73)
} 
[INFO ] 2024-03-29 16:30:25.390 - [employees_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Employees(db24b76a-43a4-46a2-ba0d-0b68d4bcd640)
    ->Employees(74102339-ddf4-4a91-8e02-444e951516c6)
    ->EmployeeTerritories(d0686d0a-6a42-4e8b-a679-1ab54b8fb512)
} 
[INFO ] 2024-03-29 16:30:25.595 - [employees_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_74102339-ddf4-4a91-8e02-444e951516c6__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:30:26.765 - [employees_import_import_import_import_import_import_import][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 16:30:26.766 - [employees_import_import_import_import_import_import_import][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 16:30:26.766 - [employees_import_import_import_import_import_import_import][Territories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:30:26.768 - [employees_import_import_import_import_import_import_import][Territories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:30:26.887 - [employees_import_import_import_import_import_import_import] - Node[Territories] is waiting for running 
[INFO ] 2024-03-29 16:30:26.887 - [employees_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 16:30:26.887 - [employees_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 16:30:26.887 - [employees_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:30:26.959 - [employees_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:30:26.959 - [employees_import_import_import_import_import_import_import] - Node[Employees] is waiting for running 
[INFO ] 2024-03-29 16:30:27.018 - [employees_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 16:30:27.021 - [employees_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 16:30:27.021 - [employees_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:30:27.021 - [employees_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:30:27.076 - [employees_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_d0686d0a-6a42-4e8b-a679-1ab54b8fb512__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:30:27.086 - [employees_import_import_import_import_import_import_import][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 16:30:27.092 - [employees_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 16:30:27.094 - [employees_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 16:30:27.094 - [employees_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 16:30:27.143 - [employees_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 16:30:27.143 - [employees_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 16:30:27.422 - [employees_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_ed5abbfa-da94-4475-acb5-4c4fcbd06e73__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:30:27.534 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 16:30:27.534 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 16:30:27.534 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:30:27.537 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:30:27.674 - [employees_import_import_import_import_import_import_import] - Node[EmployeeTerritories] is waiting for running 
[INFO ] 2024-03-29 16:30:27.675 - [employees_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Region_46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:30:27.737 - [employees_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 16:30:27.737 - [employees_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 16:30:27.783 - [employees_import_import_import_import_import_import_import][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 16:30:27.783 - [employees_import_import_import_import_import_import_import][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 16:30:27.783 - [employees_import_import_import_import_import_import_import][Region] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:30:27.838 - [employees_import_import_import_import_import_import_import][Region] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:30:27.838 - [employees_import_import_import_import_import_import_import] - Node[Region] is waiting for running 
[INFO ] 2024-03-29 16:30:28.589 - [employees_import_import_import_import_import_import_import] - Node[Employees] finish, notify next layer to run 
[INFO ] 2024-03-29 16:30:28.589 - [employees_import_import_import_import_import_import_import] - Next layer have been notified: [null,null] 
[INFO ] 2024-03-29 16:30:28.590 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync started 
[INFO ] 2024-03-29 16:30:28.590 - [employees_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 16:30:28.594 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Starting batch read, table name: EmployeeTerritories, offset: null 
[INFO ] 2024-03-29 16:30:28.595 - [employees_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 16:30:28.595 - [employees_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 16:30:28.716 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Table EmployeeTerritories is going to be initial synced 
[INFO ] 2024-03-29 16:30:28.716 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Query table 'EmployeeTerritories' counts: 1 
[INFO ] 2024-03-29 16:30:28.716 - [employees_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 16:30:28.716 - [employees_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 16:30:28.717 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[ERROR] 2024-03-29 16:30:29.407 - [employees_import_import_import_import_import_import_import][merge] - - Map name: HazelcastMergeNode_EmployeeTerritories_d0686d0a-6a42-4e8b-a679-1ab54b8fb512__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1} <-- Error Message -->
- Map name: HazelcastMergeNode_EmployeeTerritories_d0686d0a-6a42-4e8b-a679-1ab54b8fb512__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_EmployeeTerritories_d0686d0a-6a42-4e8b-a679-1ab54b8fb512__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getJoinValueKeyBySource(HazelcastMergeNode.java:1303)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1190)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_EmployeeTerritories_d0686d0a-6a42-4e8b-a679-1ab54b8fb512__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getJoinValueKeyBySource(HazelcastMergeNode.java:1303)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 16:30:29.614 - [employees_import_import_import_import_import_import_import][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 16:30:29.886 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[90d8cdf7-17f0-4cbb-b585-fcbe121ebe49] running status set to false 
[INFO ] 2024-03-29 16:30:29.899 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 16:30:29.899 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync completed 
[INFO ] 2024-03-29 16:30:29.924 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - PDK connector node stopped: HazelcastSourcePdkDataNode-90d8cdf7-17f0-4cbb-b585-fcbe121ebe49 
[INFO ] 2024-03-29 16:30:29.924 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - PDK connector node released: HazelcastSourcePdkDataNode-90d8cdf7-17f0-4cbb-b585-fcbe121ebe49 
[INFO ] 2024-03-29 16:30:29.924 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[90d8cdf7-17f0-4cbb-b585-fcbe121ebe49] schema data cleaned 
[INFO ] 2024-03-29 16:30:29.924 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[90d8cdf7-17f0-4cbb-b585-fcbe121ebe49] monitor closed 
[INFO ] 2024-03-29 16:30:29.928 - [employees_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[90d8cdf7-17f0-4cbb-b585-fcbe121ebe49] close complete, cost 42 ms 
[INFO ] 2024-03-29 16:30:29.928 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[31152eb7-9fe1-4867-869b-2b89580126d3] running status set to false 
[INFO ] 2024-03-29 16:30:29.937 - [employees_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 16:30:29.937 - [employees_import_import_import_import_import_import_import][Employees] - Incremental sync completed 
[INFO ] 2024-03-29 16:30:29.948 - [employees_import_import_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-31152eb7-9fe1-4867-869b-2b89580126d3 
[INFO ] 2024-03-29 16:30:29.948 - [employees_import_import_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-31152eb7-9fe1-4867-869b-2b89580126d3 
[INFO ] 2024-03-29 16:30:29.948 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[31152eb7-9fe1-4867-869b-2b89580126d3] schema data cleaned 
[INFO ] 2024-03-29 16:30:29.948 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[31152eb7-9fe1-4867-869b-2b89580126d3] monitor closed 
[INFO ] 2024-03-29 16:30:29.949 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[31152eb7-9fe1-4867-869b-2b89580126d3] close complete, cost 20 ms 
[INFO ] 2024-03-29 16:30:29.949 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[db24b76a-43a4-46a2-ba0d-0b68d4bcd640] running status set to false 
[INFO ] 2024-03-29 16:30:30.040 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[db24b76a-43a4-46a2-ba0d-0b68d4bcd640] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.042 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[db24b76a-43a4-46a2-ba0d-0b68d4bcd640] monitor closed 
[INFO ] 2024-03-29 16:30:30.043 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[db24b76a-43a4-46a2-ba0d-0b68d4bcd640] close complete, cost 93 ms 
[INFO ] 2024-03-29 16:30:30.109 - [employees_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[d0686d0a-6a42-4e8b-a679-1ab54b8fb512] running status set to false 
[INFO ] 2024-03-29 16:30:30.111 - [employees_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[d0686d0a-6a42-4e8b-a679-1ab54b8fb512] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.111 - [employees_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[d0686d0a-6a42-4e8b-a679-1ab54b8fb512] monitor closed 
[INFO ] 2024-03-29 16:30:30.112 - [employees_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[d0686d0a-6a42-4e8b-a679-1ab54b8fb512] close complete, cost 68 ms 
[INFO ] 2024-03-29 16:30:30.113 - [employees_import_import_import_import_import_import_import][Region] - Node Region[d3705c30-c8a1-4e6f-858d-28ec3bc5066e] running status set to false 
[INFO ] 2024-03-29 16:30:30.126 - [employees_import_import_import_import_import_import_import][Region] - Initial sync started 
[INFO ] 2024-03-29 16:30:30.126 - [employees_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 16:30:30.133 - [employees_import_import_import_import_import_import_import][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 16:30:30.133 - [employees_import_import_import_import_import_import_import][Region] - Incremental sync completed 
[INFO ] 2024-03-29 16:30:30.142 - [employees_import_import_import_import_import_import_import][Region] - PDK connector node stopped: HazelcastSourcePdkDataNode-d3705c30-c8a1-4e6f-858d-28ec3bc5066e 
[INFO ] 2024-03-29 16:30:30.142 - [employees_import_import_import_import_import_import_import][Region] - PDK connector node released: HazelcastSourcePdkDataNode-d3705c30-c8a1-4e6f-858d-28ec3bc5066e 
[INFO ] 2024-03-29 16:30:30.142 - [employees_import_import_import_import_import_import_import][Region] - Node Region[d3705c30-c8a1-4e6f-858d-28ec3bc5066e] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.148 - [employees_import_import_import_import_import_import_import][Region] - Node Region[d3705c30-c8a1-4e6f-858d-28ec3bc5066e] monitor closed 
[INFO ] 2024-03-29 16:30:30.148 - [employees_import_import_import_import_import_import_import][Region] - Node Region[d3705c30-c8a1-4e6f-858d-28ec3bc5066e] close complete, cost 33 ms 
[INFO ] 2024-03-29 16:30:30.198 - [employees_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[4c312b86-0063-481b-af1f-face07433a06] running status set to false 
[INFO ] 2024-03-29 16:30:30.198 - [employees_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[4c312b86-0063-481b-af1f-face07433a06] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.198 - [employees_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[4c312b86-0063-481b-af1f-face07433a06] monitor closed 
[INFO ] 2024-03-29 16:30:30.199 - [employees_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[4c312b86-0063-481b-af1f-face07433a06] close complete, cost 51 ms 
[INFO ] 2024-03-29 16:30:30.199 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[0b84140a-876e-4859-86f0-91213001a2c5] running status set to false 
[INFO ] 2024-03-29 16:30:30.216 - [employees_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 16:30:30.216 - [employees_import_import_import_import_import_import_import][Employees] - Incremental sync completed 
[INFO ] 2024-03-29 16:30:30.217 - [employees_import_import_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-0b84140a-876e-4859-86f0-91213001a2c5 
[INFO ] 2024-03-29 16:30:30.217 - [employees_import_import_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-0b84140a-876e-4859-86f0-91213001a2c5 
[INFO ] 2024-03-29 16:30:30.217 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[0b84140a-876e-4859-86f0-91213001a2c5] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.217 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[0b84140a-876e-4859-86f0-91213001a2c5] monitor closed 
[INFO ] 2024-03-29 16:30:30.218 - [employees_import_import_import_import_import_import_import][Employees] - Node Employees[0b84140a-876e-4859-86f0-91213001a2c5] close complete, cost 18 ms 
[INFO ] 2024-03-29 16:30:30.267 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[74102339-ddf4-4a91-8e02-444e951516c6] running status set to false 
[INFO ] 2024-03-29 16:30:30.268 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[74102339-ddf4-4a91-8e02-444e951516c6] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.268 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[74102339-ddf4-4a91-8e02-444e951516c6] monitor closed 
[INFO ] 2024-03-29 16:30:30.268 - [employees_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[74102339-ddf4-4a91-8e02-444e951516c6] close complete, cost 50 ms 
[INFO ] 2024-03-29 16:30:30.268 - [employees_import_import_import_import_import_import_import][Territories] - Node Territories[6f3d2c70-c3b8-4181-bf8d-c652fdaea122] running status set to false 
[INFO ] 2024-03-29 16:30:30.274 - [employees_import_import_import_import_import_import_import][Territories] - Initial sync started 
[INFO ] 2024-03-29 16:30:30.283 - [employees_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 16:30:30.283 - [employees_import_import_import_import_import_import_import][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 16:30:30.283 - [employees_import_import_import_import_import_import_import][Territories] - Incremental sync completed 
[INFO ] 2024-03-29 16:30:30.287 - [employees_import_import_import_import_import_import_import][Territories] - PDK connector node stopped: HazelcastSourcePdkDataNode-6f3d2c70-c3b8-4181-bf8d-c652fdaea122 
[INFO ] 2024-03-29 16:30:30.287 - [employees_import_import_import_import_import_import_import][Territories] - PDK connector node released: HazelcastSourcePdkDataNode-6f3d2c70-c3b8-4181-bf8d-c652fdaea122 
[INFO ] 2024-03-29 16:30:30.287 - [employees_import_import_import_import_import_import_import][Territories] - Node Territories[6f3d2c70-c3b8-4181-bf8d-c652fdaea122] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.287 - [employees_import_import_import_import_import_import_import][Territories] - Node Territories[6f3d2c70-c3b8-4181-bf8d-c652fdaea122] monitor closed 
[INFO ] 2024-03-29 16:30:30.288 - [employees_import_import_import_import_import_import_import][Territories] - Node Territories[6f3d2c70-c3b8-4181-bf8d-c652fdaea122] close complete, cost 19 ms 
[INFO ] 2024-03-29 16:30:30.288 - [employees_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dc7f15f4-6bae-40f0-97c5-195867375062] running status set to false 
[INFO ] 2024-03-29 16:30:30.335 - [employees_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dc7f15f4-6bae-40f0-97c5-195867375062] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.335 - [employees_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dc7f15f4-6bae-40f0-97c5-195867375062] monitor closed 
[INFO ] 2024-03-29 16:30:30.336 - [employees_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dc7f15f4-6bae-40f0-97c5-195867375062] close complete, cost 47 ms 
[INFO ] 2024-03-29 16:30:30.336 - [employees_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[ed5abbfa-da94-4475-acb5-4c4fcbd06e73] running status set to false 
[INFO ] 2024-03-29 16:30:30.383 - [employees_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[ed5abbfa-da94-4475-acb5-4c4fcbd06e73] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.383 - [employees_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[ed5abbfa-da94-4475-acb5-4c4fcbd06e73] monitor closed 
[INFO ] 2024-03-29 16:30:30.384 - [employees_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[ed5abbfa-da94-4475-acb5-4c4fcbd06e73] close complete, cost 47 ms 
[INFO ] 2024-03-29 16:30:30.384 - [employees_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec] running status set to false 
[INFO ] 2024-03-29 16:30:30.428 - [employees_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.428 - [employees_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec] monitor closed 
[INFO ] 2024-03-29 16:30:30.429 - [employees_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec] close complete, cost 44 ms 
[INFO ] 2024-03-29 16:30:30.429 - [employees_import_import_import_import_import_import_import][merge] - Node merge[05cfcb4e-08d2-465a-a7ce-20861b5c7847] running status set to false 
[INFO ] 2024-03-29 16:30:30.429 - [employees_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Employees_74102339-ddf4-4a91-8e02-444e951516c6__TPORIG 
[INFO ] 2024-03-29 16:30:30.430 - [employees_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_EmployeeTerritories_d0686d0a-6a42-4e8b-a679-1ab54b8fb512__TPORIG 
[INFO ] 2024-03-29 16:30:30.430 - [employees_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Region_46bef1d5-6d2c-4b4d-a9b3-c10eb71881ec__TPORIG 
[INFO ] 2024-03-29 16:30:30.430 - [employees_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Territories_ed5abbfa-da94-4475-acb5-4c4fcbd06e73__TPORIG 
[INFO ] 2024-03-29 16:30:30.431 - [employees_import_import_import_import_import_import_import][merge] - Node merge[05cfcb4e-08d2-465a-a7ce-20861b5c7847] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.431 - [employees_import_import_import_import_import_import_import][merge] - Node merge[05cfcb4e-08d2-465a-a7ce-20861b5c7847] monitor closed 
[INFO ] 2024-03-29 16:30:30.431 - [employees_import_import_import_import_import_import_import][merge] - Node merge[05cfcb4e-08d2-465a-a7ce-20861b5c7847] close complete, cost 2 ms 
[INFO ] 2024-03-29 16:30:30.431 - [employees_import_import_import_import_import_import_import][employees] - Node employees[48032b03-fb44-44d0-81e0-0ece8835c045] running status set to false 
[INFO ] 2024-03-29 16:30:30.440 - [employees_import_import_import_import_import_import_import][employees] - PDK connector node stopped: HazelcastTargetPdkDataNode-48032b03-fb44-44d0-81e0-0ece8835c045 
[INFO ] 2024-03-29 16:30:30.440 - [employees_import_import_import_import_import_import_import][employees] - PDK connector node released: HazelcastTargetPdkDataNode-48032b03-fb44-44d0-81e0-0ece8835c045 
[INFO ] 2024-03-29 16:30:30.440 - [employees_import_import_import_import_import_import_import][employees] - Node employees[48032b03-fb44-44d0-81e0-0ece8835c045] schema data cleaned 
[INFO ] 2024-03-29 16:30:30.441 - [employees_import_import_import_import_import_import_import][employees] - Node employees[48032b03-fb44-44d0-81e0-0ece8835c045] monitor closed 
[INFO ] 2024-03-29 16:30:30.441 - [employees_import_import_import_import_import_import_import][employees] - Node employees[48032b03-fb44-44d0-81e0-0ece8835c045] close complete, cost 10 ms 
[INFO ] 2024-03-29 16:30:32.778 - [employees_import_import_import_import_import_import_import] - Task [employees_import_import_import_import_import_import_import] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 16:30:32.778 - [employees_import_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 16:30:32.812 - [employees_import_import_import_import_import_import_import] - Stop task milestones: 660677cf18990e56b52705a4(employees_import_import_import_import_import_import_import)  
[INFO ] 2024-03-29 16:30:32.813 - [employees_import_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 16:30:32.829 - [employees_import_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 16:30:32.829 - [employees_import_import_import_import_import_import_import] - Remove memory task client succeed, task: employees_import_import_import_import_import_import_import[660677cf18990e56b52705a4] 
[INFO ] 2024-03-29 16:30:32.829 - [employees_import_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: employees_import_import_import_import_import_import_import[660677cf18990e56b52705a4] 
