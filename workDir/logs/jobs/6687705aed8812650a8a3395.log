[INFO ] 2024-07-05 12:03:24.202 - [Oracle -Mysql 2] - Task initialization... 
[INFO ] 2024-07-05 12:03:24.249 - [Oracle -Mysql 2] - Start task milestones: 6687705aed8812650a8a3395(Oracle -Mysql 2) 
[INFO ] 2024-07-05 12:03:24.341 - [Oracle -Mysql 2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-05 12:03:24.404 - [Oracle -Mysql 2] - The engine receives Oracle -Mysql 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 12:03:24.613 - [Oracle -Mysql 2][POLICY] - Node POLICY[cba884b8-89fa-4be8-9303-c2af7a0f6656] start preload schema,table counts: 1 
[INFO ] 2024-07-05 12:03:24.613 - [Oracle -Mysql 2][POLICY2] - Node POLICY2[3786c4b5-a504-487b-bd6f-b4c405eee222] start preload schema,table counts: 1 
[INFO ] 2024-07-05 12:03:24.613 - [Oracle -Mysql 2][POLICY] - Node POLICY[cba884b8-89fa-4be8-9303-c2af7a0f6656] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 12:03:24.613 - [Oracle -Mysql 2][POLICY2] - Node POLICY2[3786c4b5-a504-487b-bd6f-b4c405eee222] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 12:03:25.887 - [Oracle -Mysql 2][POLICY2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-05 12:03:26.930 - [Oracle -Mysql 2][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-05 12:03:26.931 - [Oracle -Mysql 2][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-05 12:03:26.931 - [Oracle -Mysql 2][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 12:03:27.364 - [Oracle -Mysql 2][POLICY] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68913980,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:03:27.372 - [Oracle -Mysql 2][POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 12:03:27.418 - [Oracle -Mysql 2][POLICY] - Initial sync started 
[INFO ] 2024-07-05 12:03:27.418 - [Oracle -Mysql 2][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-05 12:03:27.419 - [Oracle -Mysql 2][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-05 12:03:27.529 - [Oracle -Mysql 2][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-05 12:03:27.530 - [Oracle -Mysql 2][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-05 12:03:27.530 - [Oracle -Mysql 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-05 12:03:27.530 - [Oracle -Mysql 2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-05 12:03:27.530 - [Oracle -Mysql 2][POLICY] - Initial sync completed 
[INFO ] 2024-07-05 12:03:27.576 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-05 12:03:27.576 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TAPDATA2Oracle enable share cdc: true 
[INFO ] 2024-07-05 12:03:27.576 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task Oracle -Mysql 2 enable share cdc: true 
[INFO ] 2024-07-05 12:03:27.584 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TAPDATA2Oracle的共享挖掘任务 
[INFO ] 2024-07-05 12:03:27.601 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-05 12:03:27.601 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:03:27.638 - [Oracle -Mysql 2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_Oracle -Mysql 2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 12:03:27.638 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c769, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1700608932, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:03:27.642 - [Oracle -Mysql 2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table_Oracle -Mysql 2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 12:03:27.643 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-05 12:03:27.643 - [Oracle -Mysql 2][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-05 12:03:27.643 - [Oracle -Mysql 2][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-05 12:03:27.643 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-05 12:03:27.643 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-05 12:03:27.649 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:03:27.649 - [Oracle -Mysql 2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_Oracle -Mysql 2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 12:03:27.652 - [Oracle -Mysql 2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_Oracle -Mysql 2, external storage name: ExternalStorage_SHARE_CDC_-825204492 
[INFO ] 2024-07-05 12:03:27.652 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-05 12:03:27.654 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c769, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1700608932, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:03:27.654 - [Oracle -Mysql 2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table_Oracle -Mysql 2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 12:03:27.655 - [Oracle -Mysql 2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table_Oracle -Mysql 2, external storage name: ExternalStorage_SHARE_CDC_-1700608932 
[INFO ] 2024-07-05 12:03:27.655 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-05 12:03:27.661 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(_tapdata_heartbeat_table) by timestamp(2024-07-05T04:03:26.930Z): 1 
[INFO ] 2024-07-05 12:03:27.662 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-05T04:03:26.930Z): 1 
[INFO ] 2024-07-05 12:03:27.663 - [Oracle -Mysql 2][POLICY] - Connector Oracle incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-05 12:03:27.663 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-05 12:03:27.663 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 1 
[INFO ] 2024-07-05 12:03:27.673 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-05 12:03:27.674 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=1} 
[INFO ] 2024-07-05 12:03:30.692 - [Oracle -Mysql 2][POLICY] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, tableNamespaces=[C##TAPDATA2, _tapdata_heartbeat_table], timestamp=1720152207000, date=Fri Jul 05 12:03:27 CST 2024, before=Document{{id=6687678eed8812650a8a3044, ts=Fri Jul 05 12:01:40 CST 2024}}, after=Document{{id=6687678eed8812650a8a3044, ts=Fri Jul 05 12:03:26 CST 2024}}, op=u, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5vcmFjbGUuY2RjLm9mZnNldC5PcmFjbGVP
ZmZzZXQUJN7C0aaHzAIAB0kAA2Zub0wABmhleFNjbnQAEkxqYXZhL2xhbmcvU3RyaW5nO0wAB2xh
c3RTY250ABBMamF2YS9sYW5nL0xvbmc7TAALb2Zmc2V0VmFsdWVxAH4AAkwACnBlbmRpbmdTY25x
AH4AAkwACnNvcnRTdHJpbmdxAH4AAUwACXRpbWVzdGFtcHEAfgACeHAAAAAAcHNyAA5qYXZhLmxh
bmcuTG9uZzuL5JDMjyPfAgABSgAFdmFsdWV4cgAQamF2YS5sYW5nLk51bWJlcoaslR0LlOCLAgAA
eHAAAAAABBuLSHBzcQB+AAQAAAAABBuLYXBzcQB+AAQAAAGQgQ+umA==
, type=DATA, connectionId=6687678eed8812650a8a3044, isReplaceEvent=false, _ts=1720152210}} 
[INFO ] 2024-07-05 12:09:00.453 - [Oracle -Mysql 2][POLICY] - Node POLICY[cba884b8-89fa-4be8-9303-c2af7a0f6656] running status set to false 
[INFO ] 2024-07-05 12:09:00.502 - [Oracle -Mysql 2][POLICY] - Incremental sync completed 
[INFO ] 2024-07-05 12:09:00.502 - [Oracle -Mysql 2][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-cba884b8-89fa-4be8-9303-c2af7a0f6656 
[INFO ] 2024-07-05 12:09:00.504 - [Oracle -Mysql 2][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-cba884b8-89fa-4be8-9303-c2af7a0f6656 
[INFO ] 2024-07-05 12:09:00.504 - [Oracle -Mysql 2][POLICY] - Node POLICY[cba884b8-89fa-4be8-9303-c2af7a0f6656] schema data cleaned 
[INFO ] 2024-07-05 12:09:00.507 - [Oracle -Mysql 2][POLICY] - Node POLICY[cba884b8-89fa-4be8-9303-c2af7a0f6656] monitor closed 
[INFO ] 2024-07-05 12:09:00.507 - [Oracle -Mysql 2][POLICY] - Node POLICY[cba884b8-89fa-4be8-9303-c2af7a0f6656] close complete, cost 76 ms 
[INFO ] 2024-07-05 12:09:00.518 - [Oracle -Mysql 2][POLICY2] - Node POLICY2[3786c4b5-a504-487b-bd6f-b4c405eee222] running status set to false 
[INFO ] 2024-07-05 12:09:00.518 - [Oracle -Mysql 2][POLICY2] - PDK connector node stopped: HazelcastTargetPdkDataNode-3786c4b5-a504-487b-bd6f-b4c405eee222 
[INFO ] 2024-07-05 12:09:00.519 - [Oracle -Mysql 2][POLICY2] - PDK connector node released: HazelcastTargetPdkDataNode-3786c4b5-a504-487b-bd6f-b4c405eee222 
[INFO ] 2024-07-05 12:09:00.519 - [Oracle -Mysql 2][POLICY2] - Node POLICY2[3786c4b5-a504-487b-bd6f-b4c405eee222] schema data cleaned 
[INFO ] 2024-07-05 12:09:00.519 - [Oracle -Mysql 2][POLICY2] - Node POLICY2[3786c4b5-a504-487b-bd6f-b4c405eee222] monitor closed 
[INFO ] 2024-07-05 12:09:00.519 - [Oracle -Mysql 2][POLICY2] - Node POLICY2[3786c4b5-a504-487b-bd6f-b4c405eee222] close complete, cost 12 ms 
[INFO ] 2024-07-05 12:09:03.496 - [Oracle -Mysql 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:09:03.497 - [Oracle -Mysql 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7a75db7e 
[INFO ] 2024-07-05 12:09:03.648 - [Oracle -Mysql 2] - Stop task milestones: 6687705aed8812650a8a3395(Oracle -Mysql 2)  
[INFO ] 2024-07-05 12:09:03.648 - [Oracle -Mysql 2] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:09:03.648 - [Oracle -Mysql 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:09:03.666 - [Oracle -Mysql 2] - Remove memory task client succeed, task: Oracle -Mysql 2[6687705aed8812650a8a3395] 
[INFO ] 2024-07-05 12:09:03.669 - [Oracle -Mysql 2] - Destroy memory task client cache succeed, task: Oracle -Mysql 2[6687705aed8812650a8a3395] 
