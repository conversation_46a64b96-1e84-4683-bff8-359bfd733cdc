[INFO ] 2024-07-18 10:53:18.220 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Task initialization... 
[INFO ] 2024-07-18 10:53:18.422 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Start task milestones: 669883938315b25db9f539b3(t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186) 
[INFO ] 2024-07-18 10:53:18.541 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:53:18.714 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - The engine receives t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:53:18.773 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[f00ae409-e5b1-44d9-8936-e4fbf82d9674] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:53:18.773 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[52823993-dfcb-4cc9-8f45-a15dc32fdf4a] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:53:18.773 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[f00ae409-e5b1-44d9-8936-e4fbf82d9674] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 10:53:18.975 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[52823993-dfcb-4cc9-8f45-a15dc32fdf4a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:53:19.048 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:53:19.048 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:53:19.053 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:53:19.054 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721271199049,"lastTimes":1721271199049,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:53:19.250 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:53:19.250 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Starting batch read, table name: mock_100w, offset: null 
[INFO ] 2024-07-18 10:53:19.251 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Table mock_100w is going to be initial synced 
[INFO ] 2024-07-18 10:53:19.251 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Start mock_100w batch read 
[INFO ] 2024-07-18 10:53:19.455 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Query table 'mock_100w' counts: 1000000 
[INFO ] 2024-07-18 10:53:19.483 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:54:29.604 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Compile mock_100w batch read 
[INFO ] 2024-07-18 10:54:29.605 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Table [mock_100w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:54:29.605 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:54:29.605 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 10:54:29.605 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:54:29.606 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Starting stream read, table list: [mock_100w], offset: {"syncStage":null,"beginTimes":1721271199049,"lastTimes":1721271199049,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:54:29.607 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Start [mock_100w] stream read 
[INFO ] 2024-07-18 10:54:29.607 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Connector Dummy incremental start succeed, tables: [mock_100w], data change syncing 
[INFO ] 2024-07-18 10:57:15.620 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[52823993-dfcb-4cc9-8f45-a15dc32fdf4a] running status set to false 
[INFO ] 2024-07-18 10:57:15.622 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Compile [mock_100w] batch read 
[INFO ] 2024-07-18 10:57:15.622 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 10:57:15.632 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-52823993-dfcb-4cc9-8f45-a15dc32fdf4a 
[INFO ] 2024-07-18 10:57:15.633 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-52823993-dfcb-4cc9-8f45-a15dc32fdf4a 
[INFO ] 2024-07-18 10:57:15.633 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[52823993-dfcb-4cc9-8f45-a15dc32fdf4a] schema data cleaned 
[INFO ] 2024-07-18 10:57:15.634 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[52823993-dfcb-4cc9-8f45-a15dc32fdf4a] monitor closed 
[INFO ] 2024-07-18 10:57:15.634 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[52823993-dfcb-4cc9-8f45-a15dc32fdf4a] close complete, cost 45 ms 
[INFO ] 2024-07-18 10:57:15.634 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[f00ae409-e5b1-44d9-8936-e4fbf82d9674] running status set to false 
[WARN ] 2024-07-18 10:57:15.751 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Communications link failure

The last packet successfully received from the server was 7 milliseconds ago. The last packet sent successfully to the server was 25 milliseconds ago.
 - Error record: io.tapdata.entity.event.dml.TapUpdateRecordEvent@7d5769b2: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.811000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"76380df1-1b5b-455c-be23-1a03a0a09483"},"before":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.811000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"76380df1-1b5b-455c-be23-1a03a0a09483"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271289811,"tableId":"mock_100w","time":1721271289811,"type":302}
 - Stack trace: java.sql.BatchUpdateException: Communications link failure

The last packet successfully received from the server was 7 milliseconds ago. The last packet sent successfully to the server was 25 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224)
	at com.mysql.cj.jdbc.ServerPreparedStatement.executeBatchSerially(ServerPreparedStatement.java:307)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418)
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:102)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:89)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:455)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:189)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 7 milliseconds ago. The last packet sent successfully to the server was 25 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:555)
	at com.mysql.cj.jdbc.ServerPreparedStatement.executeInternal(ServerPreparedStatement.java:339)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ServerPreparedStatement.executeBatchSerially(ServerPreparedStatement.java:279)
	... 33 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 7 milliseconds ago. The last packet sent successfully to the server was 25 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:870)
	at com.mysql.cj.NativeSession.clearInputStream(NativeSession.java:290)
	at com.mysql.cj.ServerPreparedQuery.serverResetStatement(ServerPreparedQuery.java:577)
	at com.mysql.cj.ServerPreparedQuery.prepareExecutePacket(ServerPreparedQuery.java:225)
	at com.mysql.cj.ServerPreparedQuery.serverExecute(ServerPreparedQuery.java:200)
	at com.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:553)
	... 37 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:866)
	... 42 more
 
[INFO ] 2024-07-18 10:57:15.764 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-f00ae409-e5b1-44d9-8936-e4fbf82d9674 
[INFO ] 2024-07-18 10:57:15.764 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-f00ae409-e5b1-44d9-8936-e4fbf82d9674 
[INFO ] 2024-07-18 10:57:15.764 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[f00ae409-e5b1-44d9-8936-e4fbf82d9674] schema data cleaned 
[INFO ] 2024-07-18 10:57:15.764 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[f00ae409-e5b1-44d9-8936-e4fbf82d9674] monitor closed 
[INFO ] 2024-07-18 10:57:15.827 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[f00ae409-e5b1-44d9-8936-e4fbf82d9674] close complete, cost 131 ms 
[INFO ] 2024-07-18 10:57:15.828 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@62c2adb: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.816000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"5ee8a15c-21e2-4a5b-9f4b-7caa2f9bae46"},"containsIllegalDate":false,"referenceTime":1721271289816,"tableId":"mock_100w","time":1721271289816,"type":300}, nodeIds=[52823993-dfcb-4cc9-8f45-a15dc32fdf4a], sourceTime=1721271289816, sourceSerialNo=null} 
[ERROR] 2024-07-18 10:57:16.029 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - target write record(s) failed <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:173)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:189)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	... 7 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 9 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:858)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: java.net.SocketException: Socket closed
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:128)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:455)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	... 25 more
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:762)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:701)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:156)
	at com.mysql.cj.NativeSession.queryServerVariable(NativeSession.java:589)
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1366)
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1359)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:386)
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:102)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:109)
	... 27 more

[INFO ] 2024-07-18 10:57:16.459 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@37bacc1b: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"1ede86d0-a04f-4026-a0a1-cbf88256dad1"},"before":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"1ede86d0-a04f-4026-a0a1-cbf88256dad1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271289812,"tableId":"mock_100w","time":1721271289812,"type":302}, nodeIds=[52823993-dfcb-4cc9-8f45-a15dc32fdf4a], sourceTime=1721271289812, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:57:16.475 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@11ecb28d: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"1969a591-4cc0-4330-a5a9-098b0db2a6b5"},"before":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"1969a591-4cc0-4330-a5a9-098b0db2a6b5"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271289812,"tableId":"mock_100w","time":1721271289812,"type":302}, nodeIds=[52823993-dfcb-4cc9-8f45-a15dc32fdf4a], sourceTime=1721271289812, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:57:16.475 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@7d008fd: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"23f2b5d8-3dc0-43a6-9dee-353288296128"},"before":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"23f2b5d8-3dc0-43a6-9dee-353288296128"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271289812,"tableId":"mock_100w","time":1721271289812,"type":302}, nodeIds=[52823993-dfcb-4cc9-8f45-a15dc32fdf4a], sourceTime=1721271289812, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:57:16.483 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@36eb877: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"abb03ffe-0e74-4a10-92d1-a925208c6c83"},"before":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"abb03ffe-0e74-4a10-92d1-a925208c6c83"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271289812,"tableId":"mock_100w","time":1721271289812,"type":302}, nodeIds=[52823993-dfcb-4cc9-8f45-a15dc32fdf4a], sourceTime=1721271289812, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:57:16.490 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@6fea0fbb: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.819000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"f3b95221-a07c-4d37-9061-75d5016963c1"},"before":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.819000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"f3b95221-a07c-4d37-9061-75d5016963c1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721271289819,"tableId":"mock_100w","time":1721271289819,"type":302}, nodeIds=[52823993-dfcb-4cc9-8f45-a15dc32fdf4a], sourceTime=1721271289819, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:57:16.491 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186][qa_mysql_repl_33306_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: source_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@212bd83f: {"after":{"rstring_3":"3qG5oAc7","rstring_2":"3qG5oAc7","rstring_1":"3qG5oAc7","created":"2024-07-18 02:54:49.812000","rstring_9":"3qG5oAc7","rstring_8":"3qG5oAc7","title":"3qG5oAc7","rstring_7":"3qG5oAc7","rint_1":9627.1837,"rstring_6":"3qG5oAc7","rstring_5":"3qG5oAc7","rstring_4":"3qG5oAc7","rint_3":9627.1837,"rint_2":9627.1837,"rstring_10":"3qG5oAc7","id":"9f858fb9-28c6-4f77-9360-4f9b23ed8285"},"containsIllegalDate":false,"referenceTime":1721271289812,"tableId":"mock_100w","time":1721271289812,"type":300}, nodeIds=[52823993-dfcb-4cc9-8f45-a15dc32fdf4a], sourceTime=1721271289812, sourceSerialNo=null} 
[INFO ] 2024-07-18 10:57:20.523 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:57:20.523 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2068ff77 
[INFO ] 2024-07-18 10:57:20.692 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Stop task milestones: 669883938315b25db9f539b3(t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186)  
[INFO ] 2024-07-18 10:57:20.693 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:57:20.693 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:57:20.766 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Remove memory task client succeed, task: t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186[669883938315b25db9f539b3] 
[INFO ] 2024-07-18 10:57:20.766 - [t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186] - Destroy memory task client cache succeed, task: t_2.3-1-mock_to_mysql_full/realtime_1717403468657_3537-1721271186[669883938315b25db9f539b3] 
