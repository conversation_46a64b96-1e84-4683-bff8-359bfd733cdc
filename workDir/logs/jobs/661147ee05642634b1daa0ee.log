[INFO ] 2024-04-06 21:02:55.836 - [任务 1] - Start task milestones: 661147ee05642634b1daa0ee(任务 1) 
[INFO ] 2024-04-06 21:02:55.837 - [任务 1] - Task initialization... 
[INFO ] 2024-04-06 21:02:55.837 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-04-06 21:02:55.837 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 21:02:56.077 - [任务 1][test] - Node test[a1880da5-45db-4981-a5bb-c452be4410d8] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:02:56.077 - [任务 1][CLAIM] - Node CLAIM[9c5e3b6e-9e51-43b3-a163-ea3e65199c64] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:02:56.126 - [任务 1][CLAIM] - Node CLAIM[9c5e3b6e-9e51-43b3-a163-ea3e65199c64] preload schema finished, cost 47 ms 
[INFO ] 2024-04-06 21:02:56.126 - [任务 1][test] - Node test[a1880da5-45db-4981-a5bb-c452be4410d8] preload schema finished, cost 74 ms 
[INFO ] 2024-04-06 21:02:57.306 - [任务 1][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-06 21:02:57.308 - [任务 1][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-06 21:02:57.309 - [任务 1][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-06 21:02:57.362 - [任务 1][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":157,"gtidSet":""} 
[INFO ] 2024-04-06 21:02:57.368 - [任务 1][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-06 21:02:57.520 - [任务 1][CLAIM] - Initial sync started 
[INFO ] 2024-04-06 21:02:57.530 - [任务 1][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-06 21:02:57.604 - [任务 1][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-06 21:02:57.608 - [任务 1][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-06 21:02:57.807 - [任务 1][CLAIM] - Initial sync completed 
[INFO ] 2024-04-06 21:02:57.810 - [任务 1][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-06 21:02:57.811 - [任务 1][CLAIM] - Initial sync completed 
[INFO ] 2024-04-06 21:02:57.894 - [任务 1][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000021","position":157,"gtidSet":""} 
[INFO ] 2024-04-06 21:02:57.895 - [任务 1][CLAIM] - Starting mysql cdc, server name: 57b5a7e3-6e2c-4cb0-920b-17ad711e2024 
[INFO ] 2024-04-06 21:02:58.096 - [任务 1][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1872141425
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 57b5a7e3-6e2c-4cb0-920b-17ad711e2024
  database.port: 3306
  threadName: Debezium-Mysql-Connector-57b5a7e3-6e2c-4cb0-920b-17ad711e2024
  database.hostname: localhost
  database.password: ********
  name: 57b5a7e3-6e2c-4cb0-920b-17ad711e2024
  pdk.offset.string: {"name":"57b5a7e3-6e2c-4cb0-920b-17ad711e2024","offset":{"{\"server\":\"57b5a7e3-6e2c-4cb0-920b-17ad711e2024\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-06 21:02:58.301 - [任务 1][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-06 21:03:14.011 - [任务 1] - Stop task milestones: 661147ee05642634b1daa0ee(任务 1)  
[INFO ] 2024-04-06 21:03:14.504 - [任务 1][CLAIM] - Node CLAIM[9c5e3b6e-9e51-43b3-a163-ea3e65199c64] running status set to false 
[INFO ] 2024-04-06 21:03:14.505 - [任务 1][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-06 21:03:14.505 - [任务 1][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-06 21:03:14.521 - [任务 1][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9c5e3b6e-9e51-43b3-a163-ea3e65199c64 
[INFO ] 2024-04-06 21:03:14.522 - [任务 1][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9c5e3b6e-9e51-43b3-a163-ea3e65199c64 
[INFO ] 2024-04-06 21:03:14.522 - [任务 1][CLAIM] - Node CLAIM[9c5e3b6e-9e51-43b3-a163-ea3e65199c64] schema data cleaned 
[INFO ] 2024-04-06 21:03:14.527 - [任务 1][CLAIM] - Node CLAIM[9c5e3b6e-9e51-43b3-a163-ea3e65199c64] monitor closed 
[INFO ] 2024-04-06 21:03:14.527 - [任务 1][CLAIM] - Node CLAIM[9c5e3b6e-9e51-43b3-a163-ea3e65199c64] close complete, cost 80 ms 
[INFO ] 2024-04-06 21:03:14.528 - [任务 1][test] - Node test[a1880da5-45db-4981-a5bb-c452be4410d8] running status set to false 
[INFO ] 2024-04-06 21:03:14.556 - [任务 1][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-a1880da5-45db-4981-a5bb-c452be4410d8 
[INFO ] 2024-04-06 21:03:14.556 - [任务 1][test] - PDK connector node released: HazelcastTargetPdkDataNode-a1880da5-45db-4981-a5bb-c452be4410d8 
[INFO ] 2024-04-06 21:03:14.557 - [任务 1][test] - Node test[a1880da5-45db-4981-a5bb-c452be4410d8] schema data cleaned 
[INFO ] 2024-04-06 21:03:14.566 - [任务 1][test] - Node test[a1880da5-45db-4981-a5bb-c452be4410d8] monitor closed 
[INFO ] 2024-04-06 21:03:14.567 - [任务 1][test] - Node test[a1880da5-45db-4981-a5bb-c452be4410d8] close complete, cost 30 ms 
[INFO ] 2024-04-06 21:03:14.733 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-06 21:03:14.733 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-04-06 21:03:14.893 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-04-06 21:03:14.893 - [任务 1] - Remove memory task client succeed, task: 任务 1[661147ee05642634b1daa0ee] 
[INFO ] 2024-04-06 21:03:15.099 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[661147ee05642634b1daa0ee] 
