[INFO ] 2024-07-17 19:56:35.520 - [任务 1] - Start task milestones: 6697b1573ea63301f61948b5(任务 1) 
[INFO ] 2024-07-17 19:56:35.542 - [任务 1] - Task initialization... 
[INFO ] 2024-07-17 19:56:35.749 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-17 19:56:35.925 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 19:56:35.927 - [任务 1][p] - Node p[31029ee5-05f4-491c-b8d5-bbaf11d0de63] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:56:35.929 - [任务 1][POLICY] - Node POLICY[22b48d75-c324-4425-a044-bbe3c51d9d1e] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:56:35.930 - [任务 1][POLICY] - Node POLICY[22b48d75-c324-4425-a044-bbe3c51d9d1e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:56:35.930 - [任务 1][p] - Node p[31029ee5-05f4-491c-b8d5-bbaf11d0de63] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:56:36.914 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-17 19:56:36.920 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-17 19:56:36.921 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 19:56:36.950 - [任务 1][p] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 19:56:36.951 - [任务 1][p] - Table "test.p" exists, skip auto create table 
[INFO ] 2024-07-17 19:56:37.056 - [任务 1][p] - The table p has already exist. 
[INFO ] 2024-07-17 19:56:37.057 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721217396,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 19:56:37.118 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-17 19:56:37.119 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-17 19:56:37.127 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-17 19:56:37.127 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-17 19:56:37.150 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 19:56:37.150 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-17 19:56:37.150 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-17 19:56:37.196 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-17 19:56:37.196 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-17 19:56:37.197 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection source enable share cdc: true 
[INFO ] 2024-07-17 19:56:37.197 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-17 19:56:37.236 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自source的共享挖掘任务 
[INFO ] 2024-07-17 19:56:37.237 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav393?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-17 19:56:37.451 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b17366ab5ede8acc6011, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b1493ea63301f61948aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-510503781, shareCdcTaskId=6697b1733ea63301f61948f2, connectionId=6697b1493ea63301f61948aa) 
[INFO ] 2024-07-17 19:56:37.484 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_-510503781', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 19:56:52.509 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-17 19:56:52.512 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-17 19:56:52.513 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-17 19:56:52.514 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-17 19:56:52.514 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-17 19:56:52.534 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b17366ab5ede8acc6011, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b1493ea63301f61948aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-510503781, shareCdcTaskId=6697b1733ea63301f61948f2, connectionId=6697b1493ea63301f61948aa) 
[INFO ] 2024-07-17 19:56:52.546 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_-510503781', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 19:56:52.546 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-510503781 
[INFO ] 2024-07-17 19:56:52.549 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-17 19:56:52.557 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-17T11:56:36.920Z): 0 
[INFO ] 2024-07-17 19:56:52.557 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-17 19:56:52.558 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 0 
[INFO ] 2024-07-17 19:56:52.767 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=0} 
[INFO ] 2024-07-17 20:03:24.156 - [任务 1][POLICY] - Node POLICY[22b48d75-c324-4425-a044-bbe3c51d9d1e] running status set to false 
[INFO ] 2024-07-17 20:03:24.156 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-17 20:03:24.170 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-22b48d75-c324-4425-a044-bbe3c51d9d1e 
[INFO ] 2024-07-17 20:03:24.170 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-22b48d75-c324-4425-a044-bbe3c51d9d1e 
[INFO ] 2024-07-17 20:03:24.170 - [任务 1][POLICY] - Node POLICY[22b48d75-c324-4425-a044-bbe3c51d9d1e] schema data cleaned 
[INFO ] 2024-07-17 20:03:24.172 - [任务 1][POLICY] - Node POLICY[22b48d75-c324-4425-a044-bbe3c51d9d1e] monitor closed 
[INFO ] 2024-07-17 20:03:24.173 - [任务 1][POLICY] - Node POLICY[22b48d75-c324-4425-a044-bbe3c51d9d1e] close complete, cost 26 ms 
[INFO ] 2024-07-17 20:03:24.174 - [任务 1][p] - Node p[31029ee5-05f4-491c-b8d5-bbaf11d0de63] running status set to false 
[INFO ] 2024-07-17 20:03:24.191 - [任务 1][p] - PDK connector node stopped: HazelcastTargetPdkDataNode-31029ee5-05f4-491c-b8d5-bbaf11d0de63 
[INFO ] 2024-07-17 20:03:24.192 - [任务 1][p] - PDK connector node released: HazelcastTargetPdkDataNode-31029ee5-05f4-491c-b8d5-bbaf11d0de63 
[INFO ] 2024-07-17 20:03:24.192 - [任务 1][p] - Node p[31029ee5-05f4-491c-b8d5-bbaf11d0de63] schema data cleaned 
[INFO ] 2024-07-17 20:03:24.195 - [任务 1][p] - Node p[31029ee5-05f4-491c-b8d5-bbaf11d0de63] monitor closed 
[INFO ] 2024-07-17 20:03:24.195 - [任务 1][p] - Node p[31029ee5-05f4-491c-b8d5-bbaf11d0de63] close complete, cost 21 ms 
[INFO ] 2024-07-17 20:03:28.951 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 20:03:28.956 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f5fb908 
[INFO ] 2024-07-17 20:03:28.956 - [任务 1] - Stop task milestones: 6697b1573ea63301f61948b5(任务 1)  
[INFO ] 2024-07-17 20:03:29.108 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-17 20:03:29.109 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 20:03:29.158 - [任务 1] - Remove memory task client succeed, task: 任务 1[6697b1573ea63301f61948b5] 
[INFO ] 2024-07-17 20:03:29.158 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6697b1573ea63301f61948b5] 
