[INFO ] 2024-10-18 17:19:17.169 - [任务 1] - Start task milestones: 671227e678b74d01a8eecb44(任务 1) 
[INFO ] 2024-10-18 17:19:17.176 - [任务 1] - Task initialization... 
[INFO ] 2024-10-18 17:19:19.996 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-10-18 17:19:19.998 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-18 17:19:20.502 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] start preload schema,table counts: 130 
[INFO ] 2024-10-18 17:19:20.505 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] start preload schema,table counts: 130 
[INFO ] 2024-10-18 17:19:20.505 - [任务 1][DestMongo] - <PERSON><PERSON>[22cba993-88bc-4ad4-94e1-e23f69989edc] preload schema finished, cost 0 ms 
[INFO ] 2024-10-18 17:19:20.505 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] preload schema finished, cost 0 ms 
[INFO ] 2024-10-18 17:19:21.271 - [任务 1][DestMongo] - Node(DestMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-18 17:19:21.276 - [任务 1][DestMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-18 17:19:21.614 - [任务 1][TestMysql] - Source node "TestMysql" read batch size: 100 
[INFO ] 2024-10-18 17:19:21.615 - [任务 1][TestMysql] - Source node "TestMysql" event queue capacity: 200 
[INFO ] 2024-10-18 17:19:21.616 - [任务 1][TestMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-18 17:19:21.635 - [任务 1][TestMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":938080,"gtidSet":""} 
[INFO ] 2024-10-18 17:19:22.307 - [任务 1][TestMysql] - Initial sync started 
[INFO ] 2024-10-18 17:19:22.317 - [任务 1][TestMysql] - Starting batch read, table name: testAutoInspect 
[INFO ] 2024-10-18 17:19:22.380 - [任务 1][TestMysql] - Table testAutoInspect is going to be initial synced 
[INFO ] 2024-10-18 17:19:22.381 - [任务 1][TestMysql] - Query table 'testAutoInspect' counts: 4 
[INFO ] 2024-10-18 17:19:22.389 - [任务 1][TestMysql] - Table [testAutoInspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:19:22.389 - [任务 1][TestMysql] - Starting batch read, table name: Tes 
[INFO ] 2024-10-18 17:19:22.389 - [任务 1][TestMysql] - Table Tes is going to be initial synced 
[INFO ] 2024-10-18 17:19:22.401 - [任务 1][TestMysql] - Query table 'Tes' counts: 600 
[INFO ] 2024-10-18 17:19:37.156 - [任务 1][TestMysql] - Table [Tes] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:19:37.165 - [任务 1][TestMysql] - Starting batch read, table name: shippers 
[INFO ] 2024-10-18 17:19:37.169 - [任务 1][TestMysql] - Table shippers is going to be initial synced 
[INFO ] 2024-10-18 17:19:37.180 - [任务 1][TestMysql] - Query table 'shippers' counts: 1 
[INFO ] 2024-10-18 17:19:37.180 - [任务 1][TestMysql] - Table [shippers] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:19:37.180 - [任务 1][TestMysql] - Starting batch read, table name: TFloat 
[INFO ] 2024-10-18 17:19:37.198 - [任务 1][TestMysql] - Table TFloat is going to be initial synced 
[INFO ] 2024-10-18 17:19:37.198 - [任务 1][TestMysql] - Query table 'TFloat' counts: 1 
[INFO ] 2024-10-18 17:19:37.199 - [任务 1][TestMysql] - Table [TFloat] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:19:37.200 - [任务 1][TestMysql] - Starting batch read, table name: Test2 
[INFO ] 2024-10-18 17:19:37.200 - [任务 1][TestMysql] - Table Test2 is going to be initial synced 
[INFO ] 2024-10-18 17:19:37.280 - [任务 1][TestMysql] - Query table 'Test2' counts: 695 
[INFO ] 2024-10-18 17:19:37.283 - [任务 1][TestMysql] - Table [Test2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:19:37.283 - [任务 1][TestMysql] - Starting batch read, table name: testIndex 
[INFO ] 2024-10-18 17:19:37.283 - [任务 1][TestMysql] - Table testIndex is going to be initial synced 
[INFO ] 2024-10-18 17:19:37.298 - [任务 1][TestMysql] - Table [testIndex] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:19:37.299 - [任务 1][TestMysql] - Query table 'testIndex' counts: 2 
[INFO ] 2024-10-18 17:19:37.300 - [任务 1][TestMysql] - Starting batch read, table name: customer6 
[INFO ] 2024-10-18 17:19:37.300 - [任务 1][TestMysql] - Table customer6 is going to be initial synced 
[INFO ] 2024-10-18 17:19:37.505 - [任务 1][TestMysql] - Query table 'customer6' counts: 120000 
[INFO ] 2024-10-18 17:19:44.351 - [任务 1][TestMysql] - Table [customer6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:19:44.359 - [任务 1][TestMysql] - Starting batch read, table name: customer7 
[INFO ] 2024-10-18 17:19:44.363 - [任务 1][TestMysql] - Table customer7 is going to be initial synced 
[INFO ] 2024-10-18 17:19:44.564 - [任务 1][TestMysql] - Query table 'customer7' counts: 2000001 
[INFO ] 2024-10-18 17:21:34.494 - [任务 1][TestMysql] - Table [customer7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:21:34.498 - [任务 1][TestMysql] - Starting batch read, table name: customer4 
[INFO ] 2024-10-18 17:21:34.498 - [任务 1][TestMysql] - Table customer4 is going to be initial synced 
[INFO ] 2024-10-18 17:21:34.513 - [任务 1][TestMysql] - Table [customer4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:21:34.519 - [任务 1][TestMysql] - Query table 'customer4' counts: 0 
[INFO ] 2024-10-18 17:21:34.520 - [任务 1][TestMysql] - Starting batch read, table name: CUSTOMER 
[INFO ] 2024-10-18 17:21:34.520 - [任务 1][TestMysql] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-10-18 17:21:34.527 - [任务 1][TestMysql] - Query table 'CUSTOMER' counts: 600 
[INFO ] 2024-10-18 17:21:34.557 - [任务 1][TestMysql] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:21:34.558 - [任务 1][TestMysql] - Starting batch read, table name: customer5 
[INFO ] 2024-10-18 17:21:34.558 - [任务 1][TestMysql] - Table customer5 is going to be initial synced 
[INFO ] 2024-10-18 17:21:34.763 - [任务 1][TestMysql] - Query table 'customer5' counts: 1000000 
[INFO ] 2024-10-18 17:21:54.614 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] running status set to false 
[INFO ] 2024-10-18 17:21:54.646 - [任务 1][TestMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-3ce0b15c-89e0-4d57-a4a5-b789195cd317 
[INFO ] 2024-10-18 17:21:54.647 - [任务 1][TestMysql] - PDK connector node released: HazelcastSourcePdkDataNode-3ce0b15c-89e0-4d57-a4a5-b789195cd317 
[INFO ] 2024-10-18 17:21:54.649 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] schema data cleaned 
[INFO ] 2024-10-18 17:21:54.649 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] monitor closed 
[INFO ] 2024-10-18 17:21:54.664 - [任务 1][TestMysql] - Initial sync completed 
[INFO ] 2024-10-18 17:21:54.665 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] close complete, cost 46 ms 
[INFO ] 2024-10-18 17:21:54.671 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] running status set to false 
[INFO ] 2024-10-18 17:21:54.674 - [任务 1][TestMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`, `CITY`, `AGE`, `FIRST_NAME`, `LAST_NAME`, `DATE_OF_BIRTH`, `JOB1`, `EMAIL`, `ZIP`, `PHONE` FROM `test`.`customer5`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-10-18 17:21:54.702 - [任务 1][DestMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-22cba993-88bc-4ad4-94e1-e23f69989edc 
[INFO ] 2024-10-18 17:21:54.703 - [任务 1][DestMongo] - PDK connector node released: HazelcastTargetPdkDataNode-22cba993-88bc-4ad4-94e1-e23f69989edc 
[INFO ] 2024-10-18 17:21:54.704 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] schema data cleaned 
[INFO ] 2024-10-18 17:21:54.708 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] monitor closed 
[INFO ] 2024-10-18 17:21:54.708 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] close complete, cost 42 ms 
[WARN ] 2024-10-18 17:21:54.731 - [任务 1][DestMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: 
 - Remaining retry 0 time(s)
 - Period 60 second(s) 
[ERROR] 2024-10-18 17:21:54.733 - [任务 1][TestMysql] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`, `CITY`, `AGE`, `FIRST_NAME`, `LAST_NAME`, `DATE_OF_BIRTH`, `JOB1`, `EMAIL`, `ZIP`, `PHONE` FROM `test`.`customer5`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`, `CITY`, `AGE`, `FIRST_NAME`, `LAST_NAME`, `DATE_OF_BIRTH`, `JOB1`, `EMAIL`, `ZIP`, `PHONE` FROM `test`.`customer5`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Stream closed.
	java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	java.net.SocketInputStream.available(SocketInputStream.java:259)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`, `CITY`, `AGE`, `FIRST_NAME`, `LAST_NAME`, `DATE_OF_BIRTH`, `JOB1`, `EMAIL`, `ZIP`, `PHONE` FROM `test`.`customer5`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`, `CITY`, `AGE`, `FIRST_NAME`, `LAST_NAME`, `DATE_OF_BIRTH`, `JOB1`, `EMAIL`, `ZIP`, `PHONE` FROM `test`.`customer5`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`, `CITY`, `AGE`, `FIRST_NAME`, `LAST_NAME`, `DATE_OF_BIRTH`, `JOB1`, `EMAIL`, `ZIP`, `PHONE` FROM `test`.`customer5`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`, `CITY`, `AGE`, `FIRST_NAME`, `LAST_NAME`, `DATE_OF_BIRTH`, `JOB1`, `EMAIL`, `ZIP`, `PHONE` FROM `test`.`customer5`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 25 more
Caused by: java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 28 more

[INFO ] 2024-10-18 17:21:54.784 - [任务 1][DestMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@24654b4f: {"after":{"ZIP":"13480","CITY":"Oppenheim","JOB1":"Vertreter / Vertreterin","PHONE":"+49-4611-41132053","CUSTOMER_ID":327800,"LAST_NAME":"Sauer","EMAIL":"<EMAIL>","DATE_OF_BIRTH":1704206402000,"FIRST_NAME":"Lambert","AGE":18},"containsIllegalDate":false,"tableId":"customer5","time":1729243314566,"type":300}, nodeIds=[3ce0b15c-89e0-4d57-a4a5-b789195cd317], sourceTime=1729243161616, sourceSerialNo=null} 
[ERROR] 2024-10-18 17:21:54.788 - [任务 1][DestMongo] - java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@24654b4f: {"after":{"ZIP":"13480","CITY":"Oppenheim","JOB1":"Vertreter / Vertreterin","PHONE":"+49-4611-41132053","CUSTOMER_ID":327800,"LAST_NAME":"Sauer","EMAIL":"<EMAIL>","DATE_OF_BIRTH":1704206402000,"FIRST_NAME":"Lambert","AGE":18},"containsIllegalDate":false,"tableId":"customer5","time":1729243314566,"type":300}, nodeIds=[3ce0b15c-89e0-4d57-a4a5-b789195cd317], sourceTime=1729243161616, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@24654b4f: {"after":{"ZIP":"13480","CITY":"Oppenheim","JOB1":"Vertreter / Vertreterin","PHONE":"+49-4611-41132053","CUSTOMER_ID":327800,"LAST_NAME":"Sauer","EMAIL":"<EMAIL>","DATE_OF_BIRTH":1704206402000,"FIRST_NAME":"Lambert","AGE":18},"containsIllegalDate":false,"tableId":"customer5","time":1729243314566,"type":300}, nodeIds=[3ce0b15c-89e0-4d57-a4a5-b789195cd317], sourceTime=1729243161616, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:826)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:663)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:623)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:595)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:647)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:594)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:815)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:515)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:515)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:781)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:714)
	... 12 more
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:821)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:826)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 27 more

[INFO ] 2024-10-18 17:21:57.752 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-18 17:21:57.757 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@22ee1ac0 
[INFO ] 2024-10-18 17:21:57.883 - [任务 1] - Stop task milestones: 671227e678b74d01a8eecb44(任务 1)  
[INFO ] 2024-10-18 17:21:57.883 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-18 17:21:57.989 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-18 17:21:57.990 - [任务 1] - Remove memory task client succeed, task: 任务 1[671227e678b74d01a8eecb44] 
[INFO ] 2024-10-18 17:21:58.194 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671227e678b74d01a8eecb44] 
[INFO ] 2024-10-18 17:22:32.328 - [任务 1] - Start task milestones: 671227e678b74d01a8eecb44(任务 1) 
[INFO ] 2024-10-18 17:22:32.535 - [任务 1] - Task initialization... 
[INFO ] 2024-10-18 17:22:33.895 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-18 17:22:34.077 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-18 17:22:34.078 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] start preload schema,table counts: 130 
[INFO ] 2024-10-18 17:22:34.081 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] start preload schema,table counts: 130 
[INFO ] 2024-10-18 17:22:34.082 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] preload schema finished, cost 0 ms 
[INFO ] 2024-10-18 17:22:34.083 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] preload schema finished, cost 1 ms 
[INFO ] 2024-10-18 17:22:34.274 - [任务 1][DestMongo] - Node(DestMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-18 17:22:34.275 - [任务 1][DestMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-18 17:22:34.423 - [任务 1][TestMysql] - Source node "TestMysql" read batch size: 100 
[INFO ] 2024-10-18 17:22:34.424 - [任务 1][TestMysql] - Source node "TestMysql" event queue capacity: 200 
[INFO ] 2024-10-18 17:22:34.427 - [任务 1][TestMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-18 17:22:34.629 - [任务 1][TestMysql] - batch offset found: {"testAutoInspect":{"batch_read_connector_status":"OVER"},"Tes":{"batch_read_connector_status":"OVER"},"customer6":{"batch_read_connector_status":"OVER"},"customer7":{"batch_read_connector_status":"OVER"},"shippers":{"batch_read_connector_status":"OVER"},"customer4":{"batch_read_connector_status":"OVER"},"CUSTOMER":{"batch_read_connector_status":"OVER"},"customer5":{"batch_read_connector_status":"RUNNING"},"TFloat":{"batch_read_connector_status":"OVER"},"Test2":{"batch_read_connector_status":"OVER"},"testIndex":{"batch_read_connector_status":"OVER"}},stream offset found: {"filename":"binlog.000036","position":938080,"gtidSet":""} 
[INFO ] 2024-10-18 17:22:35.093 - [任务 1][TestMysql] - Initial sync started 
[INFO ] 2024-10-18 17:22:35.094 - [任务 1][TestMysql] - Skip table [testAutoInspect] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.094 - [任务 1][TestMysql] - Skip table [Tes] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.094 - [任务 1][TestMysql] - Skip table [shippers] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.094 - [任务 1][TestMysql] - Skip table [TFloat] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.095 - [任务 1][TestMysql] - Skip table [Test2] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.100 - [任务 1][TestMysql] - Skip table [testIndex] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.101 - [任务 1][TestMysql] - Skip table [customer6] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.101 - [任务 1][TestMysql] - Skip table [customer7] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.101 - [任务 1][TestMysql] - Skip table [customer4] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.101 - [任务 1][TestMysql] - Skip table [CUSTOMER] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-18 17:22:35.101 - [任务 1][TestMysql] - Starting batch read, table name: customer5 
[INFO ] 2024-10-18 17:22:35.101 - [任务 1][TestMysql] - Table customer5 is going to be initial synced 
[INFO ] 2024-10-18 17:22:35.232 - [任务 1][TestMysql] - Query table 'customer5' counts: 1000000 
[INFO ] 2024-10-18 17:22:47.223 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.236 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.237 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.237 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.237 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.237 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.246 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.249 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.250 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-18 17:22:47.250 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-18 17:22:47.250 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-18 17:22:47.250 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-18 17:22:47.365 - [任务 1][DestMongo] - Table 'customer5' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-18 17:22:47.366 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[INFO ] 2024-10-18 17:22:47.366 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[INFO ] 2024-10-18 17:22:47.366 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[INFO ] 2024-10-18 17:22:47.366 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[INFO ] 2024-10-18 17:22:47.366 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[INFO ] 2024-10-18 17:22:47.367 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 17 
[INFO ] 2024-10-18 17:22:47.367 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 17 
[INFO ] 2024-10-18 17:22:47.576 - [任务 1][DestMongo] - Table 'customer5' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 19 
[INFO ] 2024-10-18 17:23:03.965 - [任务 1][TestMysql] - Table [customer5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:23:04.012 - [任务 1][TestMysql] - Starting batch read, table name: customer2 
[INFO ] 2024-10-18 17:23:04.042 - [任务 1][TestMysql] - Table customer2 is going to be initial synced 
[INFO ] 2024-10-18 17:23:05.684 - [任务 1][TestMysql] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-10-18 17:26:49.273 - [任务 1][TestMysql] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:26:49.279 - [任务 1][TestMysql] - Starting batch read, table name: customer1 
[INFO ] 2024-10-18 17:26:49.279 - [任务 1][TestMysql] - Table customer1 is going to be initial synced 
[INFO ] 2024-10-18 17:26:49.285 - [任务 1][TestMysql] - Table [customer1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:26:49.286 - [任务 1][TestMysql] - Query table 'customer1' counts: 0 
[INFO ] 2024-10-18 17:26:49.286 - [任务 1][TestMysql] - Starting batch read, table name: Testabc 
[INFO ] 2024-10-18 17:26:49.286 - [任务 1][TestMysql] - Table Testabc is going to be initial synced 
[INFO ] 2024-10-18 17:26:49.293 - [任务 1][TestMysql] - Query table 'Testabc' counts: 676 
[INFO ] 2024-10-18 17:26:49.293 - [任务 1][TestMysql] - Table [Testabc] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:26:49.294 - [任务 1][TestMysql] - Starting batch read, table name: policyTest 
[INFO ] 2024-10-18 17:26:49.294 - [任务 1][TestMysql] - Table policyTest is going to be initial synced 
[INFO ] 2024-10-18 17:26:49.499 - [任务 1][TestMysql] - Query table 'policyTest' counts: 695 
[INFO ] 2024-10-18 17:26:55.288 - [任务 1][TestMysql] - Table [policyTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:26:55.288 - [任务 1][TestMysql] - Starting batch read, table name: testReference 
[INFO ] 2024-10-18 17:26:55.292 - [任务 1][TestMysql] - Table testReference is going to be initial synced 
[INFO ] 2024-10-18 17:26:55.292 - [任务 1][TestMysql] - Table [testReference] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:26:55.293 - [任务 1][TestMysql] - Query table 'testReference' counts: 4 
[INFO ] 2024-10-18 17:26:55.293 - [任务 1][TestMysql] - Starting batch read, table name: TEST3 
[INFO ] 2024-10-18 17:26:55.293 - [任务 1][TestMysql] - Table TEST3 is going to be initial synced 
[INFO ] 2024-10-18 17:26:55.494 - [任务 1][TestMysql] - Query table 'TEST3' counts: 1053 
[INFO ] 2024-10-18 17:27:04.455 - [任务 1][TestMysql] - Table [TEST3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:27:04.465 - [任务 1][TestMysql] - Starting batch read, table name: TEST4 
[INFO ] 2024-10-18 17:27:04.465 - [任务 1][TestMysql] - Table TEST4 is going to be initial synced 
[INFO ] 2024-10-18 17:27:04.669 - [任务 1][TestMysql] - Query table 'TEST4' counts: 1053 
[INFO ] 2024-10-18 17:27:07.503 - [任务 1][TestMysql] - Table [TEST4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:27:07.511 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-18 17:27:07.513 - [任务 1][TestMysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-18 17:27:07.526 - [任务 1][TestMysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:27:07.526 - [任务 1][TestMysql] - Query table 'BMSQL_ITEM' counts: 7 
[INFO ] 2024-10-18 17:27:07.527 - [任务 1][TestMysql] - Starting batch read, table name: TEST5 
[INFO ] 2024-10-18 17:27:07.527 - [任务 1][TestMysql] - Table TEST5 is going to be initial synced 
[INFO ] 2024-10-18 17:27:07.530 - [任务 1][TestMysql] - Table [TEST5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:27:07.533 - [任务 1][TestMysql] - Query table 'TEST5' counts: 0 
[INFO ] 2024-10-18 17:27:07.533 - [任务 1][TestMysql] - Starting batch read, table name: TEST6 
[INFO ] 2024-10-18 17:27:07.533 - [任务 1][TestMysql] - Table TEST6 is going to be initial synced 
[INFO ] 2024-10-18 17:27:07.534 - [任务 1][TestMysql] - Table [TEST6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:27:07.535 - [任务 1][TestMysql] - Query table 'TEST6' counts: 1 
[INFO ] 2024-10-18 17:27:07.535 - [任务 1][TestMysql] - Starting batch read, table name: TEST7 
[INFO ] 2024-10-18 17:27:07.535 - [任务 1][TestMysql] - Table TEST7 is going to be initial synced 
[INFO ] 2024-10-18 17:27:07.539 - [任务 1][TestMysql] - Table [TEST7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:27:07.540 - [任务 1][TestMysql] - Query table 'TEST7' counts: 1 
[INFO ] 2024-10-18 17:27:07.540 - [任务 1][TestMysql] - Starting batch read, table name: CLAIMBACK 
[INFO ] 2024-10-18 17:27:07.540 - [任务 1][TestMysql] - Table CLAIMBACK is going to be initial synced 
[INFO ] 2024-10-18 17:27:07.542 - [任务 1][TestMysql] - Table [CLAIMBACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:27:07.542 - [任务 1][TestMysql] - Query table 'CLAIMBACK' counts: 0 
[INFO ] 2024-10-18 17:27:07.543 - [任务 1][TestMysql] - Starting batch read, table name: TESTPer 
[INFO ] 2024-10-18 17:27:07.543 - [任务 1][TestMysql] - Table TESTPer is going to be initial synced 
[INFO ] 2024-10-18 17:27:07.746 - [任务 1][TestMysql] - Query table 'TESTPer' counts: 5000000 
[INFO ] 2024-10-18 17:28:20.961 - [任务 1][TestMysql] - Table [TESTPer] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:20.965 - [任务 1][TestMysql] - Starting batch read, table name: MDM_Targe_CLAIM_BACK 
[INFO ] 2024-10-18 17:28:20.965 - [任务 1][TestMysql] - Table MDM_Targe_CLAIM_BACK is going to be initial synced 
[INFO ] 2024-10-18 17:28:21.021 - [任务 1][TestMysql] - Query table 'MDM_Targe_CLAIM_BACK' counts: 1076 
[INFO ] 2024-10-18 17:28:21.025 - [任务 1][TestMysql] - Table [MDM_Targe_CLAIM_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:21.026 - [任务 1][TestMysql] - Starting batch read, table name: TESTDDL2 
[INFO ] 2024-10-18 17:28:21.026 - [任务 1][TestMysql] - Table TESTDDL2 is going to be initial synced 
[INFO ] 2024-10-18 17:28:21.043 - [任务 1][TestMysql] - Table [TESTDDL2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:21.043 - [任务 1][TestMysql] - Query table 'TESTDDL2' counts: 1 
[INFO ] 2024-10-18 17:28:21.043 - [任务 1][TestMysql] - Starting batch read, table name: TestPO 
[INFO ] 2024-10-18 17:28:21.044 - [任务 1][TestMysql] - Table TestPO is going to be initial synced 
[INFO ] 2024-10-18 17:28:21.247 - [任务 1][TestMysql] - Query table 'TestPO' counts: 600 
[INFO ] 2024-10-18 17:28:23.994 - [任务 1][TestMysql] - Table [TestPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:23.999 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_OORDER_BACK2 
[INFO ] 2024-10-18 17:28:24.000 - [任务 1][TestMysql] - Table BMSQL_OORDER_BACK2 is going to be initial synced 
[INFO ] 2024-10-18 17:28:24.007 - [任务 1][TestMysql] - Table [BMSQL_OORDER_BACK2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:24.007 - [任务 1][TestMysql] - Query table 'BMSQL_OORDER_BACK2' counts: 1 
[INFO ] 2024-10-18 17:28:24.008 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_OORDER_BACK 
[INFO ] 2024-10-18 17:28:24.008 - [任务 1][TestMysql] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2024-10-18 17:28:24.012 - [任务 1][TestMysql] - Table [BMSQL_OORDER_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:24.012 - [任务 1][TestMysql] - Query table 'BMSQL_OORDER_BACK' counts: 1 
[INFO ] 2024-10-18 17:28:24.013 - [任务 1][TestMysql] - Starting batch read, table name: TestCustomer 
[INFO ] 2024-10-18 17:28:24.013 - [任务 1][TestMysql] - Table TestCustomer is going to be initial synced 
[INFO ] 2024-10-18 17:28:24.217 - [任务 1][TestMysql] - Query table 'TestCustomer' counts: 100000 
[INFO ] 2024-10-18 17:28:41.010 - [任务 1][TestMysql] - Table [TestCustomer] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:41.014 - [任务 1][TestMysql] - Starting batch read, table name: POLICY 
[INFO ] 2024-10-18 17:28:41.014 - [任务 1][TestMysql] - Table POLICY is going to be initial synced 
[INFO ] 2024-10-18 17:28:41.039 - [任务 1][TestMysql] - Query table 'POLICY' counts: 601 
[INFO ] 2024-10-18 17:28:41.039 - [任务 1][TestMysql] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:41.040 - [任务 1][TestMysql] - Starting batch read, table name: TESTE 
[INFO ] 2024-10-18 17:28:41.040 - [任务 1][TestMysql] - Table TESTE is going to be initial synced 
[INFO ] 2024-10-18 17:28:41.241 - [任务 1][TestMysql] - Query table 'TESTE' counts: 695 
[INFO ] 2024-10-18 17:28:44.047 - [任务 1][TestMysql] - Table [TESTE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:44.047 - [任务 1][TestMysql] - Starting batch read, table name: CLAIM2 
[INFO ] 2024-10-18 17:28:44.047 - [任务 1][TestMysql] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-10-18 17:28:44.051 - [任务 1][TestMysql] - Query table 'CLAIM2' counts: 695 
[INFO ] 2024-10-18 17:28:47.095 - [任务 1][TestMysql] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:47.105 - [任务 1][TestMysql] - Starting batch read, table name: CLAIM3 
[INFO ] 2024-10-18 17:28:47.105 - [任务 1][TestMysql] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-10-18 17:28:47.109 - [任务 1][TestMysql] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:47.110 - [任务 1][TestMysql] - Query table 'CLAIM3' counts: 1 
[INFO ] 2024-10-18 17:28:47.110 - [任务 1][TestMysql] - Starting batch read, table name: CLAIM1 
[INFO ] 2024-10-18 17:28:47.114 - [任务 1][TestMysql] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-10-18 17:28:47.114 - [任务 1][TestMysql] - Table [CLAIM1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:47.114 - [任务 1][TestMysql] - Query table 'CLAIM1' counts: 0 
[INFO ] 2024-10-18 17:28:47.115 - [任务 1][TestMysql] - Starting batch read, table name: customerTest 
[INFO ] 2024-10-18 17:28:47.125 - [任务 1][TestMysql] - Table customerTest is going to be initial synced 
[INFO ] 2024-10-18 17:28:47.126 - [任务 1][TestMysql] - Query table 'customerTest' counts: 100 
[INFO ] 2024-10-18 17:28:47.127 - [任务 1][TestMysql] - Table [customerTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:47.127 - [任务 1][TestMysql] - Starting batch read, table name: orders 
[INFO ] 2024-10-18 17:28:47.130 - [任务 1][TestMysql] - Table orders is going to be initial synced 
[INFO ] 2024-10-18 17:28:47.130 - [任务 1][TestMysql] - Table [orders] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:47.130 - [任务 1][TestMysql] - Query table 'orders' counts: 1 
[INFO ] 2024-10-18 17:28:47.130 - [任务 1][TestMysql] - Starting batch read, table name: t1 
[INFO ] 2024-10-18 17:28:47.141 - [任务 1][TestMysql] - Table t1 is going to be initial synced 
[INFO ] 2024-10-18 17:28:47.142 - [任务 1][TestMysql] - Table [t1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:28:47.142 - [任务 1][TestMysql] - Query table 't1' counts: 0 
[INFO ] 2024-10-18 17:28:47.143 - [任务 1][TestMysql] - Starting batch read, table name: t2 
[INFO ] 2024-10-18 17:28:47.319 - [任务 1][TestMysql] - Table t2 is going to be initial synced 
[INFO ] 2024-10-18 17:28:47.319 - [任务 1][TestMysql] - Query table 't2' counts: 1296000 
[INFO ] 2024-10-18 17:29:22.589 - [任务 1][TestMysql] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:22.596 - [任务 1][TestMysql] - Starting batch read, table name: TESTCLAIM 
[INFO ] 2024-10-18 17:29:22.598 - [任务 1][TestMysql] - Table TESTCLAIM is going to be initial synced 
[INFO ] 2024-10-18 17:29:22.800 - [任务 1][TestMysql] - Query table 'TESTCLAIM' counts: 1076 
[INFO ] 2024-10-18 17:29:25.612 - [任务 1][TestMysql] - Table [TESTCLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:25.613 - [任务 1][TestMysql] - Starting batch read, table name: TString 
[INFO ] 2024-10-18 17:29:25.613 - [任务 1][TestMysql] - Table TString is going to be initial synced 
[INFO ] 2024-10-18 17:29:25.623 - [任务 1][TestMysql] - Table [TString] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:25.623 - [任务 1][TestMysql] - Query table 'TString' counts: 0 
[INFO ] 2024-10-18 17:29:25.623 - [任务 1][TestMysql] - Starting batch read, table name: HreatBeatTest1 
[INFO ] 2024-10-18 17:29:25.629 - [任务 1][TestMysql] - Table HreatBeatTest1 is going to be initial synced 
[INFO ] 2024-10-18 17:29:25.630 - [任务 1][TestMysql] - Query table 'HreatBeatTest1' counts: 1129 
[INFO ] 2024-10-18 17:29:31.758 - [任务 1][TestMysql] - Table [HreatBeatTest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.761 - [任务 1][TestMysql] - Starting batch read, table name: wimCLAIM1inspect 
[INFO ] 2024-10-18 17:29:31.761 - [任务 1][TestMysql] - Table wimCLAIM1inspect is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.763 - [任务 1][TestMysql] - Table [wimCLAIM1inspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.763 - [任务 1][TestMysql] - Query table 'wimCLAIM1inspect' counts: 0 
[INFO ] 2024-10-18 17:29:31.764 - [任务 1][TestMysql] - Starting batch read, table name: wimtest2 
[INFO ] 2024-10-18 17:29:31.765 - [任务 1][TestMysql] - Table wimtest2 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.768 - [任务 1][TestMysql] - Table [wimtest2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.768 - [任务 1][TestMysql] - Query table 'wimtest2' counts: 0 
[INFO ] 2024-10-18 17:29:31.768 - [任务 1][TestMysql] - Starting batch read, table name: wimtest3 
[INFO ] 2024-10-18 17:29:31.769 - [任务 1][TestMysql] - Table wimtest3 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.776 - [任务 1][TestMysql] - Table [wimtest3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.777 - [任务 1][TestMysql] - Query table 'wimtest3' counts: 0 
[INFO ] 2024-10-18 17:29:31.779 - [任务 1][TestMysql] - Starting batch read, table name: wimtest4 
[INFO ] 2024-10-18 17:29:31.779 - [任务 1][TestMysql] - Table wimtest4 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.780 - [任务 1][TestMysql] - Table [wimtest4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.781 - [任务 1][TestMysql] - Query table 'wimtest4' counts: 0 
[INFO ] 2024-10-18 17:29:31.781 - [任务 1][TestMysql] - Starting batch read, table name: WCLAIM2 
[INFO ] 2024-10-18 17:29:31.783 - [任务 1][TestMysql] - Table WCLAIM2 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.783 - [任务 1][TestMysql] - Table [WCLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.783 - [任务 1][TestMysql] - Query table 'WCLAIM2' counts: 0 
[INFO ] 2024-10-18 17:29:31.783 - [任务 1][TestMysql] - Starting batch read, table name: WCLAIM3 
[INFO ] 2024-10-18 17:29:31.785 - [任务 1][TestMysql] - Table WCLAIM3 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.785 - [任务 1][TestMysql] - Table [WCLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.785 - [任务 1][TestMysql] - Query table 'WCLAIM3' counts: 1 
[INFO ] 2024-10-18 17:29:31.785 - [任务 1][TestMysql] - Starting batch read, table name: WCLAIM4 
[INFO ] 2024-10-18 17:29:31.789 - [任务 1][TestMysql] - Table WCLAIM4 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.789 - [任务 1][TestMysql] - Query table 'WCLAIM4' counts: 1 
[INFO ] 2024-10-18 17:29:31.790 - [任务 1][TestMysql] - Table [WCLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.790 - [任务 1][TestMysql] - Starting batch read, table name: slave 
[INFO ] 2024-10-18 17:29:31.794 - [任务 1][TestMysql] - Table slave is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.794 - [任务 1][TestMysql] - Table [slave] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.795 - [任务 1][TestMysql] - Query table 'slave' counts: 0 
[INFO ] 2024-10-18 17:29:31.795 - [任务 1][TestMysql] - Starting batch read, table name: T 
[INFO ] 2024-10-18 17:29:31.798 - [任务 1][TestMysql] - Table T is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.798 - [任务 1][TestMysql] - Table [T] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.798 - [任务 1][TestMysql] - Query table 'T' counts: 2 
[INFO ] 2024-10-18 17:29:31.798 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_DISTRICT 
[INFO ] 2024-10-18 17:29:31.801 - [任务 1][TestMysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.801 - [任务 1][TestMysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-10-18 17:29:31.801 - [任务 1][TestMysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.801 - [任务 1][TestMysql] - Starting batch read, table name: TDecimal 
[INFO ] 2024-10-18 17:29:31.805 - [任务 1][TestMysql] - Table TDecimal is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.805 - [任务 1][TestMysql] - Table [TDecimal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.806 - [任务 1][TestMysql] - Query table 'TDecimal' counts: 1 
[INFO ] 2024-10-18 17:29:31.806 - [任务 1][TestMysql] - Starting batch read, table name: WIMTEST1 
[INFO ] 2024-10-18 17:29:31.810 - [任务 1][TestMysql] - Table WIMTEST1 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.810 - [任务 1][TestMysql] - Query table 'WIMTEST1' counts: 2 
[INFO ] 2024-10-18 17:29:31.810 - [任务 1][TestMysql] - Table [WIMTEST1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.811 - [任务 1][TestMysql] - Starting batch read, table name: Tes2 
[INFO ] 2024-10-18 17:29:31.811 - [任务 1][TestMysql] - Table Tes2 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.827 - [任务 1][TestMysql] - Query table 'Tes2' counts: 695 
[INFO ] 2024-10-18 17:29:31.827 - [任务 1][TestMysql] - Table [Tes2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.827 - [任务 1][TestMysql] - Starting batch read, table name: testArr 
[INFO ] 2024-10-18 17:29:31.828 - [任务 1][TestMysql] - Table testArr is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.832 - [任务 1][TestMysql] - Table [testArr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.832 - [任务 1][TestMysql] - Query table 'testArr' counts: 3 
[INFO ] 2024-10-18 17:29:31.832 - [任务 1][TestMysql] - Starting batch read, table name: TEST_VARCHAR2 
[INFO ] 2024-10-18 17:29:31.832 - [任务 1][TestMysql] - Table TEST_VARCHAR2 is going to be initial synced 
[INFO ] 2024-10-18 17:29:31.845 - [任务 1][TestMysql] - Table [TEST_VARCHAR2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:29:31.845 - [任务 1][TestMysql] - Query table 'TEST_VARCHAR2' counts: 0 
[INFO ] 2024-10-18 17:29:31.847 - [任务 1][TestMysql] - Starting batch read, table name: wimCLAIMinspect 
[INFO ] 2024-10-18 17:29:31.847 - [任务 1][TestMysql] - Table wimCLAIMinspect is going to be initial synced 
[INFO ] 2024-10-18 17:29:32.055 - [任务 1][TestMysql] - Query table 'wimCLAIMinspect' counts: 1130 
[INFO ] 2024-10-18 17:30:20.329 - [任务 1][TestMysql] - Table [wimCLAIMinspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:20.336 - [任务 1][TestMysql] - Starting batch read, table name: testPNew 
[INFO ] 2024-10-18 17:30:20.336 - [任务 1][TestMysql] - Table testPNew is going to be initial synced 
[INFO ] 2024-10-18 17:30:20.350 - [任务 1][TestMysql] - Query table 'testPNew' counts: 695 
[INFO ] 2024-10-18 17:30:20.350 - [任务 1][TestMysql] - Table [testPNew] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:20.350 - [任务 1][TestMysql] - Starting batch read, table name: _tapdata_heartbeat_table 
[INFO ] 2024-10-18 17:30:20.351 - [任务 1][TestMysql] - Table _tapdata_heartbeat_table is going to be initial synced 
[INFO ] 2024-10-18 17:30:20.353 - [任务 1][TestMysql] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:20.353 - [任务 1][TestMysql] - Query table '_tapdata_heartbeat_table' counts: 4 
[INFO ] 2024-10-18 17:30:20.354 - [任务 1][TestMysql] - Starting batch read, table name: claim_back 
[INFO ] 2024-10-18 17:30:20.354 - [任务 1][TestMysql] - Table claim_back is going to be initial synced 
[INFO ] 2024-10-18 17:30:20.560 - [任务 1][TestMysql] - Query table 'claim_back' counts: 1076 
[INFO ] 2024-10-18 17:30:29.431 - [任务 1][TestMysql] - Table [claim_back] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:29.434 - [任务 1][TestMysql] - Starting batch read, table name: master_not_arr_index 
[INFO ] 2024-10-18 17:30:29.435 - [任务 1][TestMysql] - Table master_not_arr_index is going to be initial synced 
[INFO ] 2024-10-18 17:30:29.444 - [任务 1][TestMysql] - Table [master_not_arr_index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:29.444 - [任务 1][TestMysql] - Query table 'master_not_arr_index' counts: 2 
[INFO ] 2024-10-18 17:30:29.445 - [任务 1][TestMysql] - Starting batch read, table name: SQLServerTest1 
[INFO ] 2024-10-18 17:30:29.445 - [任务 1][TestMysql] - Table SQLServerTest1 is going to be initial synced 
[INFO ] 2024-10-18 17:30:29.449 - [任务 1][TestMysql] - Table [SQLServerTest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:29.449 - [任务 1][TestMysql] - Query table 'SQLServerTest1' counts: 2 
[INFO ] 2024-10-18 17:30:29.449 - [任务 1][TestMysql] - Starting batch read, table name: TUINT 
[INFO ] 2024-10-18 17:30:29.449 - [任务 1][TestMysql] - Table TUINT is going to be initial synced 
[INFO ] 2024-10-18 17:30:29.453 - [任务 1][TestMysql] - Query table 'TUINT' counts: 1 
[INFO ] 2024-10-18 17:30:29.453 - [任务 1][TestMysql] - Table [TUINT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:29.454 - [任务 1][TestMysql] - Starting batch read, table name: GuangFaTestTable 
[INFO ] 2024-10-18 17:30:29.454 - [任务 1][TestMysql] - Table GuangFaTestTable is going to be initial synced 
[INFO ] 2024-10-18 17:30:29.461 - [任务 1][TestMysql] - Table [GuangFaTestTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:29.461 - [任务 1][TestMysql] - Query table 'GuangFaTestTable' counts: 0 
[INFO ] 2024-10-18 17:30:29.462 - [任务 1][TestMysql] - Starting batch read, table name: WCLAIM 
[INFO ] 2024-10-18 17:30:29.462 - [任务 1][TestMysql] - Table WCLAIM is going to be initial synced 
[INFO ] 2024-10-18 17:30:29.663 - [任务 1][TestMysql] - Query table 'WCLAIM' counts: 1130 
[INFO ] 2024-10-18 17:30:44.534 - [任务 1][TestMysql] - Table [WCLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:44.534 - [任务 1][TestMysql] - Starting batch read, table name: wimtest7inspect 
[INFO ] 2024-10-18 17:30:44.552 - [任务 1][TestMysql] - Table wimtest7inspect is going to be initial synced 
[INFO ] 2024-10-18 17:30:44.552 - [任务 1][TestMysql] - Query table 'wimtest7inspect' counts: 1075 
[INFO ] 2024-10-18 17:30:47.586 - [任务 1][TestMysql] - Table [wimtest7inspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:47.586 - [任务 1][TestMysql] - Starting batch read, table name: Territories 
[INFO ] 2024-10-18 17:30:47.586 - [任务 1][TestMysql] - Table Territories is going to be initial synced 
[INFO ] 2024-10-18 17:30:47.589 - [任务 1][TestMysql] - Query table 'Territories' counts: 1 
[INFO ] 2024-10-18 17:30:47.589 - [任务 1][TestMysql] - Table [Territories] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:47.590 - [任务 1][TestMysql] - Starting batch read, table name: dorisTest 
[INFO ] 2024-10-18 17:30:47.590 - [任务 1][TestMysql] - Table dorisTest is going to be initial synced 
[INFO ] 2024-10-18 17:30:47.592 - [任务 1][TestMysql] - Table [dorisTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:47.592 - [任务 1][TestMysql] - Query table 'dorisTest' counts: 2 
[INFO ] 2024-10-18 17:30:47.593 - [任务 1][TestMysql] - Starting batch read, table name: wimtest3inspect 
[INFO ] 2024-10-18 17:30:47.593 - [任务 1][TestMysql] - Table wimtest3inspect is going to be initial synced 
[INFO ] 2024-10-18 17:30:47.596 - [任务 1][TestMysql] - Table [wimtest3inspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:47.596 - [任务 1][TestMysql] - Query table 'wimtest3inspect' counts: 1 
[INFO ] 2024-10-18 17:30:47.597 - [任务 1][TestMysql] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-18 17:30:47.597 - [任务 1][TestMysql] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-18 17:30:47.798 - [任务 1][TestMysql] - Query table '0620_CAR_CLAIM_M' counts: 1081 
[INFO ] 2024-10-18 17:30:59.873 - [任务 1][TestMysql] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:59.874 - [任务 1][TestMysql] - Starting batch read, table name: Test1 
[INFO ] 2024-10-18 17:30:59.896 - [任务 1][TestMysql] - Table Test1 is going to be initial synced 
[INFO ] 2024-10-18 17:30:59.896 - [任务 1][TestMysql] - Table [Test1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:59.905 - [任务 1][TestMysql] - Query table 'Test1' counts: 1 
[INFO ] 2024-10-18 17:30:59.906 - [任务 1][TestMysql] - Starting batch read, table name: p 
[INFO ] 2024-10-18 17:30:59.906 - [任务 1][TestMysql] - Table p is going to be initial synced 
[INFO ] 2024-10-18 17:30:59.953 - [任务 1][TestMysql] - Query table 'p' counts: 695 
[INFO ] 2024-10-18 17:30:59.954 - [任务 1][TestMysql] - Table [p] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:59.955 - [任务 1][TestMysql] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-18 17:30:59.955 - [任务 1][TestMysql] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-18 17:30:59.964 - [任务 1][TestMysql] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:30:59.965 - [任务 1][TestMysql] - Query table 'testNotNull' counts: 2 
[INFO ] 2024-10-18 17:30:59.965 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_NEW_ORDER 
[INFO ] 2024-10-18 17:30:59.974 - [任务 1][TestMysql] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-10-18 17:30:59.974 - [任务 1][TestMysql] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-10-18 17:31:12.052 - [任务 1][TestMysql] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:12.054 - [任务 1][TestMysql] - Starting batch read, table name: Custom_CLAIM 
[INFO ] 2024-10-18 17:31:12.055 - [任务 1][TestMysql] - Table Custom_CLAIM is going to be initial synced 
[INFO ] 2024-10-18 17:31:12.257 - [任务 1][TestMysql] - Query table 'Custom_CLAIM' counts: 1130 
[INFO ] 2024-10-18 17:31:15.104 - [任务 1][TestMysql] - Table [Custom_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:15.104 - [任务 1][TestMysql] - Starting batch read, table name: testUnion1 
[INFO ] 2024-10-18 17:31:15.104 - [任务 1][TestMysql] - Table testUnion1 is going to be initial synced 
[INFO ] 2024-10-18 17:31:15.107 - [任务 1][TestMysql] - Table [testUnion1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:15.107 - [任务 1][TestMysql] - Query table 'testUnion1' counts: 7 
[INFO ] 2024-10-18 17:31:15.107 - [任务 1][TestMysql] - Starting batch read, table name: testUnion2 
[INFO ] 2024-10-18 17:31:15.108 - [任务 1][TestMysql] - Table testUnion2 is going to be initial synced 
[INFO ] 2024-10-18 17:31:15.111 - [任务 1][TestMysql] - Table [testUnion2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:15.112 - [任务 1][TestMysql] - Query table 'testUnion2' counts: 6 
[INFO ] 2024-10-18 17:31:15.113 - [任务 1][TestMysql] - Starting batch read, table name: testUnion3 
[INFO ] 2024-10-18 17:31:15.113 - [任务 1][TestMysql] - Table testUnion3 is going to be initial synced 
[INFO ] 2024-10-18 17:31:15.114 - [任务 1][TestMysql] - Table [testUnion3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:15.116 - [任务 1][TestMysql] - Query table 'testUnion3' counts: 6 
[INFO ] 2024-10-18 17:31:15.116 - [任务 1][TestMysql] - Starting batch read, table name: repliceTime 
[INFO ] 2024-10-18 17:31:15.116 - [任务 1][TestMysql] - Table repliceTime is going to be initial synced 
[INFO ] 2024-10-18 17:31:15.118 - [任务 1][TestMysql] - Table [repliceTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:15.119 - [任务 1][TestMysql] - Query table 'repliceTime' counts: 1 
[INFO ] 2024-10-18 17:31:15.119 - [任务 1][TestMysql] - Starting batch read, table name: WCLAIM3test1 
[INFO ] 2024-10-18 17:31:15.119 - [任务 1][TestMysql] - Table WCLAIM3test1 is going to be initial synced 
[INFO ] 2024-10-18 17:31:15.121 - [任务 1][TestMysql] - Table [WCLAIM3test1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:15.122 - [任务 1][TestMysql] - Query table 'WCLAIM3test1' counts: 1 
[INFO ] 2024-10-18 17:31:15.122 - [任务 1][TestMysql] - Starting batch read, table name: WimCLAIM 
[INFO ] 2024-10-18 17:31:15.122 - [任务 1][TestMysql] - Table WimCLAIM is going to be initial synced 
[INFO ] 2024-10-18 17:31:15.126 - [任务 1][TestMysql] - Query table 'WimCLAIM' counts: 1130 
[INFO ] 2024-10-18 17:31:33.351 - [任务 1][TestMysql] - Table [WimCLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:33.352 - [任务 1][TestMysql] - Starting batch read, table name: TESTPolicy 
[INFO ] 2024-10-18 17:31:33.352 - [任务 1][TestMysql] - Table TESTPolicy is going to be initial synced 
[INFO ] 2024-10-18 17:31:33.377 - [任务 1][TestMysql] - Query table 'TESTPolicy' counts: 695 
[INFO ] 2024-10-18 17:31:33.377 - [任务 1][TestMysql] - Table [TESTPolicy] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:33.378 - [任务 1][TestMysql] - Starting batch read, table name: SqlServerTest2 
[INFO ] 2024-10-18 17:31:33.378 - [任务 1][TestMysql] - Table SqlServerTest2 is going to be initial synced 
[INFO ] 2024-10-18 17:31:33.380 - [任务 1][TestMysql] - Table [SqlServerTest2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:33.380 - [任务 1][TestMysql] - Query table 'SqlServerTest2' counts: 0 
[INFO ] 2024-10-18 17:31:33.381 - [任务 1][TestMysql] - Starting batch read, table name: wimtest1inspect 
[INFO ] 2024-10-18 17:31:33.381 - [任务 1][TestMysql] - Table wimtest1inspect is going to be initial synced 
[INFO ] 2024-10-18 17:31:33.384 - [任务 1][TestMysql] - Table [wimtest1inspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:33.384 - [任务 1][TestMysql] - Query table 'wimtest1inspect' counts: 1 
[INFO ] 2024-10-18 17:31:33.385 - [任务 1][TestMysql] - Starting batch read, table name: CkTest1 
[INFO ] 2024-10-18 17:31:33.385 - [任务 1][TestMysql] - Table CkTest1 is going to be initial synced 
[INFO ] 2024-10-18 17:31:33.389 - [任务 1][TestMysql] - Query table 'CkTest1' counts: 1 
[INFO ] 2024-10-18 17:31:33.389 - [任务 1][TestMysql] - Table [CkTest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:33.389 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_STOCK 
[INFO ] 2024-10-18 17:31:33.389 - [任务 1][TestMysql] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2024-10-18 17:31:33.590 - [任务 1][TestMysql] - Query table 'BMSQL_STOCK' counts: 7899 
[INFO ] 2024-10-18 17:31:48.566 - [任务 1][TestMysql] - Table [BMSQL_STOCK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:48.567 - [任务 1][TestMysql] - Starting batch read, table name: EmployeeTerritories 
[INFO ] 2024-10-18 17:31:48.568 - [任务 1][TestMysql] - Table EmployeeTerritories is going to be initial synced 
[INFO ] 2024-10-18 17:31:48.576 - [任务 1][TestMysql] - Table [EmployeeTerritories] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:48.576 - [任务 1][TestMysql] - Query table 'EmployeeTerritories' counts: 1 
[INFO ] 2024-10-18 17:31:48.577 - [任务 1][TestMysql] - Starting batch read, table name: WCLAIM2test1 
[INFO ] 2024-10-18 17:31:48.577 - [任务 1][TestMysql] - Table WCLAIM2test1 is going to be initial synced 
[INFO ] 2024-10-18 17:31:48.580 - [任务 1][TestMysql] - Table [WCLAIM2test1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:48.580 - [任务 1][TestMysql] - Query table 'WCLAIM2test1' counts: 1 
[INFO ] 2024-10-18 17:31:48.580 - [任务 1][TestMysql] - Starting batch read, table name: TIDB 
[INFO ] 2024-10-18 17:31:48.580 - [任务 1][TestMysql] - Table TIDB is going to be initial synced 
[INFO ] 2024-10-18 17:31:48.605 - [任务 1][TestMysql] - Query table 'TIDB' counts: 600 
[INFO ] 2024-10-18 17:31:48.606 - [任务 1][TestMysql] - Table [TIDB] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:48.606 - [任务 1][TestMysql] - Starting batch read, table name: slave_not_arr_index2 
[INFO ] 2024-10-18 17:31:48.606 - [任务 1][TestMysql] - Table slave_not_arr_index2 is going to be initial synced 
[INFO ] 2024-10-18 17:31:48.610 - [任务 1][TestMysql] - Table [slave_not_arr_index2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:48.611 - [任务 1][TestMysql] - Query table 'slave_not_arr_index2' counts: 0 
[INFO ] 2024-10-18 17:31:48.611 - [任务 1][TestMysql] - Starting batch read, table name: bmsql_customer_test 
[INFO ] 2024-10-18 17:31:48.612 - [任务 1][TestMysql] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-10-18 17:31:48.614 - [任务 1][TestMysql] - Query table 'bmsql_customer_test' counts: 4 
[INFO ] 2024-10-18 17:31:48.614 - [任务 1][TestMysql] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:48.614 - [任务 1][TestMysql] - Starting batch read, table name: PoTest 
[INFO ] 2024-10-18 17:31:48.619 - [任务 1][TestMysql] - Table PoTest is going to be initial synced 
[INFO ] 2024-10-18 17:31:48.619 - [任务 1][TestMysql] - Query table 'PoTest' counts: 600 
[INFO ] 2024-10-18 17:31:57.764 - [任务 1][TestMysql] - Table [PoTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:31:57.767 - [任务 1][TestMysql] - Starting batch read, table name: KafkaTest 
[INFO ] 2024-10-18 17:31:57.767 - [任务 1][TestMysql] - Table KafkaTest is going to be initial synced 
[INFO ] 2024-10-18 17:31:57.968 - [任务 1][TestMysql] - Query table 'KafkaTest' counts: 25000 
[INFO ] 2024-10-18 17:32:10.085 - [任务 1][TestMysql] - Table [KafkaTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:10.086 - [任务 1][TestMysql] - Starting batch read, table name: wim_test1 
[INFO ] 2024-10-18 17:32:10.086 - [任务 1][TestMysql] - Table wim_test1 is going to be initial synced 
[INFO ] 2024-10-18 17:32:10.089 - [任务 1][TestMysql] - Table [wim_test1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:10.090 - [任务 1][TestMysql] - Query table 'wim_test1' counts: 0 
[INFO ] 2024-10-18 17:32:10.090 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-10-18 17:32:10.123 - [任务 1][TestMysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-10-18 17:32:10.123 - [任务 1][TestMysql] - Query table 'BMSQL_CUSTOMER' counts: 9999 
[INFO ] 2024-10-18 17:32:16.340 - [任务 1][TestMysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.341 - [任务 1][TestMysql] - Starting batch read, table name: testRep 
[INFO ] 2024-10-18 17:32:16.342 - [任务 1][TestMysql] - Table testRep is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.343 - [任务 1][TestMysql] - Table [testRep] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.343 - [任务 1][TestMysql] - Query table 'testRep' counts: 1 
[INFO ] 2024-10-18 17:32:16.344 - [任务 1][TestMysql] - Starting batch read, table name: T_Enum 
[INFO ] 2024-10-18 17:32:16.344 - [任务 1][TestMysql] - Table T_Enum is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.347 - [任务 1][TestMysql] - Table [T_Enum] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.347 - [任务 1][TestMysql] - Query table 'T_Enum' counts: 0 
[INFO ] 2024-10-18 17:32:16.347 - [任务 1][TestMysql] - Starting batch read, table name: logTest 
[INFO ] 2024-10-18 17:32:16.347 - [任务 1][TestMysql] - Table logTest is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.349 - [任务 1][TestMysql] - Table [logTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.349 - [任务 1][TestMysql] - Query table 'logTest' counts: 1 
[INFO ] 2024-10-18 17:32:16.349 - [任务 1][TestMysql] - Starting batch read, table name: TestInspect 
[INFO ] 2024-10-18 17:32:16.349 - [任务 1][TestMysql] - Table TestInspect is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.364 - [任务 1][TestMysql] - Query table 'TestInspect' counts: 603 
[INFO ] 2024-10-18 17:32:16.364 - [任务 1][TestMysql] - Table [TestInspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.364 - [任务 1][TestMysql] - Starting batch read, table name: users 
[INFO ] 2024-10-18 17:32:16.364 - [任务 1][TestMysql] - Table users is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.366 - [任务 1][TestMysql] - Table [users] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.366 - [任务 1][TestMysql] - Query table 'users' counts: 5 
[INFO ] 2024-10-18 17:32:16.366 - [任务 1][TestMysql] - Starting batch read, table name: wimtest6inspect 
[INFO ] 2024-10-18 17:32:16.366 - [任务 1][TestMysql] - Table wimtest6inspect is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.368 - [任务 1][TestMysql] - Query table 'wimtest6inspect' counts: 1 
[INFO ] 2024-10-18 17:32:16.368 - [任务 1][TestMysql] - Table [wimtest6inspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.368 - [任务 1][TestMysql] - Starting batch read, table name: wimtest2inspect 
[INFO ] 2024-10-18 17:32:16.368 - [任务 1][TestMysql] - Table wimtest2inspect is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.370 - [任务 1][TestMysql] - Table [wimtest2inspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:16.370 - [任务 1][TestMysql] - Query table 'wimtest2inspect' counts: 1 
[INFO ] 2024-10-18 17:32:16.370 - [任务 1][TestMysql] - Starting batch read, table name: wimtest4inspect 
[INFO ] 2024-10-18 17:32:16.370 - [任务 1][TestMysql] - Table wimtest4inspect is going to be initial synced 
[INFO ] 2024-10-18 17:32:16.572 - [任务 1][TestMysql] - Query table 'wimtest4inspect' counts: 694 
[INFO ] 2024-10-18 17:32:28.345 - [任务 1][TestMysql] - Table [wimtest4inspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:28.373 - [任务 1][TestMysql] - Starting batch read, table name: dorisTest1 
[INFO ] 2024-10-18 17:32:28.385 - [任务 1][TestMysql] - Table dorisTest1 is going to be initial synced 
[INFO ] 2024-10-18 17:32:28.399 - [任务 1][TestMysql] - Table [dorisTest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:28.399 - [任务 1][TestMysql] - Query table 'dorisTest1' counts: 16 
[INFO ] 2024-10-18 17:32:28.401 - [任务 1][TestMysql] - Starting batch read, table name: AA00PP 
[INFO ] 2024-10-18 17:32:28.402 - [任务 1][TestMysql] - Table AA00PP is going to be initial synced 
[INFO ] 2024-10-18 17:32:28.418 - [任务 1][TestMysql] - Table [AA00PP] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:28.419 - [任务 1][TestMysql] - Query table 'AA00PP' counts: 1 
[INFO ] 2024-10-18 17:32:28.419 - [任务 1][TestMysql] - Starting batch read, table name: TEST 
[INFO ] 2024-10-18 17:32:28.419 - [任务 1][TestMysql] - Table TEST is going to be initial synced 
[INFO ] 2024-10-18 17:32:28.625 - [任务 1][TestMysql] - Query table 'TEST' counts: 695 
[INFO ] 2024-10-18 17:32:40.597 - [任务 1][TestMysql] - Table [TEST] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:40.598 - [任务 1][TestMysql] - Starting batch read, table name: WCLAIM4test1 
[INFO ] 2024-10-18 17:32:40.619 - [任务 1][TestMysql] - Table WCLAIM4test1 is going to be initial synced 
[INFO ] 2024-10-18 17:32:40.620 - [任务 1][TestMysql] - Table [WCLAIM4test1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:40.623 - [任务 1][TestMysql] - Query table 'WCLAIM4test1' counts: 1 
[INFO ] 2024-10-18 17:32:40.623 - [任务 1][TestMysql] - Starting batch read, table name: claimtest1 
[INFO ] 2024-10-18 17:32:40.623 - [任务 1][TestMysql] - Table claimtest1 is going to be initial synced 
[INFO ] 2024-10-18 17:32:40.627 - [任务 1][TestMysql] - Table [claimtest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:40.627 - [任务 1][TestMysql] - Query table 'claimtest1' counts: 1 
[INFO ] 2024-10-18 17:32:40.628 - [任务 1][TestMysql] - Starting batch read, table name: employees 
[INFO ] 2024-10-18 17:32:40.628 - [任务 1][TestMysql] - Table employees is going to be initial synced 
[INFO ] 2024-10-18 17:32:40.630 - [任务 1][TestMysql] - Table [employees] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:40.630 - [任务 1][TestMysql] - Query table 'employees' counts: 1 
[INFO ] 2024-10-18 17:32:40.630 - [任务 1][TestMysql] - Starting batch read, table name: WiTest 
[INFO ] 2024-10-18 17:32:40.630 - [任务 1][TestMysql] - Table WiTest is going to be initial synced 
[INFO ] 2024-10-18 17:32:40.830 - [任务 1][TestMysql] - Query table 'WiTest' counts: 600 
[INFO ] 2024-10-18 17:32:49.643 - [任务 1][TestMysql] - Table [WiTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:49.643 - [任务 1][TestMysql] - Starting batch read, table name: BMSQL_WAREHOUSE 
[INFO ] 2024-10-18 17:32:49.644 - [任务 1][TestMysql] - Table BMSQL_WAREHOUSE is going to be initial synced 
[INFO ] 2024-10-18 17:32:49.648 - [任务 1][TestMysql] - Query table 'BMSQL_WAREHOUSE' counts: 0 
[INFO ] 2024-10-18 17:32:49.649 - [任务 1][TestMysql] - Table [BMSQL_WAREHOUSE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:49.650 - [任务 1][TestMysql] - Starting batch read, table name: INT864 
[INFO ] 2024-10-18 17:32:49.650 - [任务 1][TestMysql] - Table INT864 is going to be initial synced 
[INFO ] 2024-10-18 17:32:49.660 - [任务 1][TestMysql] - Query table 'INT864' counts: 1 
[INFO ] 2024-10-18 17:32:49.660 - [任务 1][TestMysql] - Table [INT864] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:49.661 - [任务 1][TestMysql] - Starting batch read, table name: wimT1 
[INFO ] 2024-10-18 17:32:49.661 - [任务 1][TestMysql] - Table wimT1 is going to be initial synced 
[INFO ] 2024-10-18 17:32:49.665 - [任务 1][TestMysql] - Table [wimT1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:49.665 - [任务 1][TestMysql] - Query table 'wimT1' counts: 1 
[INFO ] 2024-10-18 17:32:49.666 - [任务 1][TestMysql] - Starting batch read, table name: platform_application 
[INFO ] 2024-10-18 17:32:49.667 - [任务 1][TestMysql] - Table platform_application is going to be initial synced 
[INFO ] 2024-10-18 17:32:49.672 - [任务 1][TestMysql] - Query table 'platform_application' counts: 2 
[INFO ] 2024-10-18 17:32:49.672 - [任务 1][TestMysql] - Table [platform_application] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:49.672 - [任务 1][TestMysql] - Starting batch read, table name: TTUPLE 
[INFO ] 2024-10-18 17:32:49.672 - [任务 1][TestMysql] - Table TTUPLE is going to be initial synced 
[INFO ] 2024-10-18 17:32:49.676 - [任务 1][TestMysql] - Table [TTUPLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:49.676 - [任务 1][TestMysql] - Query table 'TTUPLE' counts: 4 
[INFO ] 2024-10-18 17:32:49.677 - [任务 1][TestMysql] - Starting batch read, table name: WIMTESTCLAIM 
[INFO ] 2024-10-18 17:32:49.677 - [任务 1][TestMysql] - Table WIMTESTCLAIM is going to be initial synced 
[INFO ] 2024-10-18 17:32:49.882 - [任务 1][TestMysql] - Query table 'WIMTESTCLAIM' counts: 1130 
[INFO ] 2024-10-18 17:32:52.667 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] running status set to false 
[INFO ] 2024-10-18 17:32:52.692 - [任务 1][TestMysql] - Table [WIMTESTCLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-18 17:32:52.692 - [任务 1][TestMysql] - Initial sync completed 
[INFO ] 2024-10-18 17:32:52.708 - [任务 1][TestMysql] - Initial sync completed 
[INFO ] 2024-10-18 17:32:52.708 - [任务 1][TestMysql] - Incremental sync starting... 
[INFO ] 2024-10-18 17:32:52.713 - [任务 1][TestMysql] - Incremental sync completed 
[INFO ] 2024-10-18 17:32:52.713 - [任务 1][TestMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-3ce0b15c-89e0-4d57-a4a5-b789195cd317 
[INFO ] 2024-10-18 17:32:52.714 - [任务 1][TestMysql] - PDK connector node released: HazelcastSourcePdkDataNode-3ce0b15c-89e0-4d57-a4a5-b789195cd317 
[INFO ] 2024-10-18 17:32:52.714 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] schema data cleaned 
[INFO ] 2024-10-18 17:32:52.715 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] monitor closed 
[INFO ] 2024-10-18 17:32:52.715 - [任务 1][TestMysql] - Node TestMysql[3ce0b15c-89e0-4d57-a4a5-b789195cd317] close complete, cost 52 ms 
[INFO ] 2024-10-18 17:32:52.733 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] running status set to false 
[INFO ] 2024-10-18 17:32:52.733 - [任务 1][DestMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-22cba993-88bc-4ad4-94e1-e23f69989edc 
[INFO ] 2024-10-18 17:32:52.733 - [任务 1][DestMongo] - PDK connector node released: HazelcastTargetPdkDataNode-22cba993-88bc-4ad4-94e1-e23f69989edc 
[INFO ] 2024-10-18 17:32:52.733 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] schema data cleaned 
[INFO ] 2024-10-18 17:32:52.734 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] monitor closed 
[INFO ] 2024-10-18 17:32:52.734 - [任务 1][DestMongo] - Node DestMongo[22cba993-88bc-4ad4-94e1-e23f69989edc] close complete, cost 18 ms 
[INFO ] 2024-10-18 17:32:53.544 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-18 17:32:53.553 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2d1185f1 
[INFO ] 2024-10-18 17:32:53.553 - [任务 1] - Stop task milestones: 671227e678b74d01a8eecb44(任务 1)  
[INFO ] 2024-10-18 17:32:53.704 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-18 17:32:53.704 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-18 17:32:53.730 - [任务 1] - Remove memory task client succeed, task: 任务 1[671227e678b74d01a8eecb44] 
[INFO ] 2024-10-18 17:32:53.732 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671227e678b74d01a8eecb44] 
