[INFO ] 2024-07-26 17:40:02.475 - [Heartbeat-Mysql - Co<PERSON>] - Start task milestones: 66a36ef28191787d1e5af383(Heartbeat-Mysql - Copy) 
[INFO ] 2024-07-26 17:40:02.589 - [Heartbeat-Mysql - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 17:40:02.671 - [Heartbeat-Mysql - Copy] - The engine receives Heartbeat-Mysql - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 17:40:02.744 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4d58a1ae-7813-4560-9b36-98136c8125f4] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:40:02.744 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c147a574-94d2-4fca-8220-327982a2d906] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:40:02.744 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c147a574-94d2-4fca-8220-327982a2d906] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:40:02.744 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4d58a1ae-7813-4560-9b36-98136c8125f4] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 17:40:03.718 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 17:40:03.718 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 17:40:03.718 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 17:40:03.719 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721986803718,"lastTimes":1721986803718,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 17:40:03.719 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 17:40:03.840 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 17:40:03.852 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 17:40:03.852 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 17:40:03.858 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 17:40:03.858 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 17:40:03.861 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721986803718,"lastTimes":1721986803718,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 17:40:03.861 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 17:40:03.865 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 17:40:04.395 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-26 17:40:04.601 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Table "test._tapdata_heartbeat_table" exists, skip auto create table 
[INFO ] 2024-07-26 18:00:03.316 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c147a574-94d2-4fca-8220-327982a2d906] running status set to false 
[INFO ] 2024-07-26 18:00:03.317 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:00:03.322 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-c147a574-94d2-4fca-8220-327982a2d906 
[INFO ] 2024-07-26 18:00:03.322 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-c147a574-94d2-4fca-8220-327982a2d906 
[INFO ] 2024-07-26 18:00:03.323 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c147a574-94d2-4fca-8220-327982a2d906] schema data cleaned 
[INFO ] 2024-07-26 18:00:03.323 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c147a574-94d2-4fca-8220-327982a2d906] monitor closed 
[INFO ] 2024-07-26 18:00:03.324 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c147a574-94d2-4fca-8220-327982a2d906] close complete, cost 8 ms 
[INFO ] 2024-07-26 18:00:03.346 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4d58a1ae-7813-4560-9b36-98136c8125f4] running status set to false 
[INFO ] 2024-07-26 18:00:03.346 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-4d58a1ae-7813-4560-9b36-98136c8125f4 
[INFO ] 2024-07-26 18:00:03.346 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-4d58a1ae-7813-4560-9b36-98136c8125f4 
[INFO ] 2024-07-26 18:00:03.346 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4d58a1ae-7813-4560-9b36-98136c8125f4] schema data cleaned 
[INFO ] 2024-07-26 18:00:03.346 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4d58a1ae-7813-4560-9b36-98136c8125f4] monitor closed 
[INFO ] 2024-07-26 18:00:03.551 - [Heartbeat-Mysql - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4d58a1ae-7813-4560-9b36-98136c8125f4] close complete, cost 22 ms 
[INFO ] 2024-07-26 18:00:07.202 - [Heartbeat-Mysql - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:00:07.202 - [Heartbeat-Mysql - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4946b818 
[INFO ] 2024-07-26 18:00:07.324 - [Heartbeat-Mysql - Copy] - Stop task milestones: 66a36ef28191787d1e5af383(Heartbeat-Mysql - Copy)  
[INFO ] 2024-07-26 18:00:07.324 - [Heartbeat-Mysql - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:00:07.324 - [Heartbeat-Mysql - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:00:07.345 - [Heartbeat-Mysql - Copy] - Remove memory task client succeed, task: Heartbeat-Mysql - Copy[66a36ef28191787d1e5af383] 
[INFO ] 2024-07-26 18:00:07.346 - [Heartbeat-Mysql - Copy] - Destroy memory task client cache succeed, task: Heartbeat-Mysql - Copy[66a36ef28191787d1e5af383] 
