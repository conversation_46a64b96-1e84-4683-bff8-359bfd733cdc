[INFO ] 2024-04-03 21:29:28.949 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] start preload schema,table counts: 1 
[INFO ] 2024-04-03 21:29:28.952 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] start preload schema,table counts: 1 
[INFO ] 2024-04-03 21:29:28.969 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] start preload schema,table counts: 1 
[INFO ] 2024-04-03 21:29:28.970 - [任务 48(100)][e9d7328a-4f72-436a-86c7-fbeff5a2983d] - Node e9d7328a-4f72-436a-86c7-fbeff5a2983d[e9d7328a-4f72-436a-86c7-fbeff5a2983d] start preload schema,table counts: 0 
[INFO ] 2024-04-03 21:29:28.970 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-03 21:29:28.970 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] preload schema finished, cost 0 ms 
[INFO ] 2024-04-03 21:29:28.970 - [任务 48(100)][e9d7328a-4f72-436a-86c7-fbeff5a2983d] - Node e9d7328a-4f72-436a-86c7-fbeff5a2983d[e9d7328a-4f72-436a-86c7-fbeff5a2983d] preload schema finished, cost 0 ms 
[INFO ] 2024-04-03 21:29:28.971 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] preload schema finished, cost 4 ms 
[INFO ] 2024-04-03 21:29:28.971 - [任务 48(100)][9d25c792-d9e0-4d47-9713-68d235c1b9af] - Node 9d25c792-d9e0-4d47-9713-68d235c1b9af[9d25c792-d9e0-4d47-9713-68d235c1b9af] start preload schema,table counts: 0 
[INFO ] 2024-04-03 21:29:28.971 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] start preload schema,table counts: 1 
[INFO ] 2024-04-03 21:29:28.971 - [任务 48(100)][9d25c792-d9e0-4d47-9713-68d235c1b9af] - Node 9d25c792-d9e0-4d47-9713-68d235c1b9af[9d25c792-d9e0-4d47-9713-68d235c1b9af] preload schema finished, cost 0 ms 
[INFO ] 2024-04-03 21:29:28.972 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-03 21:29:45.144 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] running status set to false 
[INFO ] 2024-04-03 21:29:45.186 - [任务 48(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-ee297ae9-4a84-4caf-82fb-eb294a74ff4b 
[INFO ] 2024-04-03 21:29:45.188 - [任务 48(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-ee297ae9-4a84-4caf-82fb-eb294a74ff4b 
[INFO ] 2024-04-03 21:29:45.190 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] schema data cleaned 
[INFO ] 2024-04-03 21:29:45.204 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] monitor closed 
[INFO ] 2024-04-03 21:29:45.206 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] close complete, cost 55 ms 
[INFO ] 2024-04-03 21:29:45.237 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] running status set to false 
[INFO ] 2024-04-03 21:29:45.238 - [任务 48(100)][9d25c792-d9e0-4d47-9713-68d235c1b9af] - Node 9d25c792-d9e0-4d47-9713-68d235c1b9af[9d25c792-d9e0-4d47-9713-68d235c1b9af] running status set to false 
[INFO ] 2024-04-03 21:29:45.238 - [任务 48(100)][9d25c792-d9e0-4d47-9713-68d235c1b9af] - Node 9d25c792-d9e0-4d47-9713-68d235c1b9af[9d25c792-d9e0-4d47-9713-68d235c1b9af] schema data cleaned 
[INFO ] 2024-04-03 21:29:45.239 - [任务 48(100)][9d25c792-d9e0-4d47-9713-68d235c1b9af] - Node 9d25c792-d9e0-4d47-9713-68d235c1b9af[9d25c792-d9e0-4d47-9713-68d235c1b9af] monitor closed 
[INFO ] 2024-04-03 21:29:45.241 - [任务 48(100)][9d25c792-d9e0-4d47-9713-68d235c1b9af] - Node 9d25c792-d9e0-4d47-9713-68d235c1b9af[9d25c792-d9e0-4d47-9713-68d235c1b9af] close complete, cost 4 ms 
[INFO ] 2024-04-03 21:29:45.259 - [任务 48(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d7934215-46b9-4a6b-9ea6-e786f287550e 
[INFO ] 2024-04-03 21:29:45.260 - [任务 48(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d7934215-46b9-4a6b-9ea6-e786f287550e 
[INFO ] 2024-04-03 21:29:45.270 - [任务 48(100)][增强JS] - [ScriptExecutorsManager-660cd3918fa47a4fca4f011f-3a714d52-e40b-4d77-b773-4163f262ad3f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-03 21:29:45.272 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] schema data cleaned 
[INFO ] 2024-04-03 21:29:45.272 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] monitor closed 
[INFO ] 2024-04-03 21:29:45.274 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] close complete, cost 43 ms 
[INFO ] 2024-04-03 21:29:45.481 - [任务 48(100)] - load tapTable task 660cd3918fa47a4fca4f011f-9d25c792-d9e0-4d47-9713-68d235c1b9af complete, cost 17037ms 
[INFO ] 2024-04-03 21:29:52.994 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] running status set to false 
[INFO ] 2024-04-03 21:29:53.140 - [任务 48(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-ee297ae9-4a84-4caf-82fb-eb294a74ff4b 
[INFO ] 2024-04-03 21:29:53.141 - [任务 48(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-ee297ae9-4a84-4caf-82fb-eb294a74ff4b 
[INFO ] 2024-04-03 21:29:53.142 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] schema data cleaned 
[INFO ] 2024-04-03 21:29:53.143 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] monitor closed 
[INFO ] 2024-04-03 21:29:53.148 - [任务 48(100)][test2] - Node test2[ee297ae9-4a84-4caf-82fb-eb294a74ff4b] close complete, cost 195 ms 
[INFO ] 2024-04-03 21:30:00.550 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] running status set to false 
[INFO ] 2024-04-03 21:30:00.552 - [任务 48(100)][e9d7328a-4f72-436a-86c7-fbeff5a2983d] - Node e9d7328a-4f72-436a-86c7-fbeff5a2983d[e9d7328a-4f72-436a-86c7-fbeff5a2983d] running status set to false 
[INFO ] 2024-04-03 21:30:00.553 - [任务 48(100)][e9d7328a-4f72-436a-86c7-fbeff5a2983d] - Node e9d7328a-4f72-436a-86c7-fbeff5a2983d[e9d7328a-4f72-436a-86c7-fbeff5a2983d] schema data cleaned 
[INFO ] 2024-04-03 21:30:00.553 - [任务 48(100)][e9d7328a-4f72-436a-86c7-fbeff5a2983d] - Node e9d7328a-4f72-436a-86c7-fbeff5a2983d[e9d7328a-4f72-436a-86c7-fbeff5a2983d] monitor closed 
[INFO ] 2024-04-03 21:30:00.553 - [任务 48(100)][e9d7328a-4f72-436a-86c7-fbeff5a2983d] - Node e9d7328a-4f72-436a-86c7-fbeff5a2983d[e9d7328a-4f72-436a-86c7-fbeff5a2983d] close complete, cost 16 ms 
[INFO ] 2024-04-03 21:30:00.716 - [任务 48(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-fc7994e1-5087-4952-9982-2200373da030 
[INFO ] 2024-04-03 21:30:00.718 - [任务 48(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-fc7994e1-5087-4952-9982-2200373da030 
[INFO ] 2024-04-03 21:30:00.721 - [任务 48(100)][增强JS] - [ScriptExecutorsManager-660cd3918fa47a4fca4f011f-3a714d52-e40b-4d77-b773-4163f262ad3f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-03 21:30:00.727 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] schema data cleaned 
[INFO ] 2024-04-03 21:30:00.728 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] monitor closed 
[INFO ] 2024-04-03 21:30:00.734 - [任务 48(100)][增强JS] - Node 增强JS[3a714d52-e40b-4d77-b773-4163f262ad3f] close complete, cost 205 ms 
[INFO ] 2024-04-03 21:30:00.734 - [任务 48(100)] - load tapTable task 660cd3918fa47a4fca4f011f-e9d7328a-4f72-436a-86c7-fbeff5a2983d complete, cost 32486ms 
