[INFO ] 2024-07-17 16:12:31.449 - [任务 2] - Start task milestones: 66977ca1b92eda1a86f51468(任务 2) 
[INFO ] 2024-07-17 16:12:31.662 - [任务 2] - Task initialization... 
[INFO ] 2024-07-17 16:12:32.037 - [任务 2] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-17 16:12:32.037 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 16:12:32.092 - [任务 2][TargeJushanMongo] - <PERSON><PERSON>[75c6dfd8-e487-493c-ba9d-fcd362e3c465] start preload schema,table counts: 2 
[INFO ] 2024-07-17 16:12:32.092 - [任务 2][TargetMysql] - Node TargetMysql[e6653ddc-2abc-4319-99f4-14d12fdc1182] start preload schema,table counts: 2 
[INFO ] 2024-07-17 16:12:32.092 - [任务 2][Targe<PERSON>ush<PERSON><PERSON>ong<PERSON>] - Node <PERSON>rge<PERSON>o[75c6dfd8-e487-493c-ba9d-fcd362e3c465] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:12:32.093 - [任务 2][TargetMysql] - Node TargetMysql[e6653ddc-2abc-4319-99f4-14d12fdc1182] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:12:32.706 - [任务 2][TargeJushanMongo] - Node(TargeJushanMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-17 16:12:32.707 - [任务 2][TargeJushanMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 16:12:32.743 - [任务 2][TargetMysql] - Source node "TargetMysql" read batch size: 100 
[INFO ] 2024-07-17 16:12:32.744 - [任务 2][TargetMysql] - Source node "TargetMysql" event queue capacity: 200 
[INFO ] 2024-07-17 16:12:32.744 - [任务 2][TargetMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 16:12:32.895 - [任务 2][TargetMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":127787290,"gtidSet":""} 
[INFO ] 2024-07-17 16:12:32.896 - [任务 2][TargetMysql] - Initial sync started 
[INFO ] 2024-07-17 16:12:32.900 - [任务 2][TargetMysql] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-17 16:12:32.900 - [任务 2][TargetMysql] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-17 16:12:33.114 - [任务 2][TargetMysql] - Query table 'CUSTOMER' counts: 675 
[INFO ] 2024-07-17 16:12:33.152 - [任务 2][TargetMysql] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 16:12:33.152 - [任务 2][TargetMysql] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-17 16:12:33.152 - [任务 2][TargetMysql] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-17 16:12:33.180 - [任务 2][TargetMysql] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-17 16:12:33.446 - [任务 2][TargetMysql] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 16:12:33.447 - [任务 2][TargetMysql] - Initial sync completed 
[INFO ] 2024-07-17 16:12:33.447 - [任务 2][TargetMysql] - Incremental sync starting... 
[INFO ] 2024-07-17 16:12:33.448 - [任务 2][TargetMysql] - Initial sync completed 
[INFO ] 2024-07-17 16:12:33.548 - [任务 2][TargetMysql] - Starting stream read, table list: [CUSTOMER, POLICY], offset: {"filename":"binlog.000032","position":127787290,"gtidSet":""} 
[INFO ] 2024-07-17 16:12:33.548 - [任务 2][TargetMysql] - Starting mysql cdc, server name: 09879db2-b995-407b-a993-3c48b5a24b81 
[INFO ] 2024-07-17 16:12:33.749 - [任务 2][TargetMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1012066451
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 09879db2-b995-407b-a993-3c48b5a24b81
  database.port: 3306
  threadName: Debezium-Mysql-Connector-09879db2-b995-407b-a993-3c48b5a24b81
  database.hostname: localhost
  database.password: ********
  name: 09879db2-b995-407b-a993-3c48b5a24b81
  pdk.offset.string: {"name":"09879db2-b995-407b-a993-3c48b5a24b81","offset":{"{\"server\":\"09879db2-b995-407b-a993-3c48b5a24b81\"}":"{\"file\":\"binlog.000032\",\"pos\":127787290,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-17 16:12:33.959 - [任务 2][TargetMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, POLICY], data change syncing 
[INFO ] 2024-07-17 16:15:06.092 - [任务 2][TargetMysql] - Node TargetMysql[e6653ddc-2abc-4319-99f4-14d12fdc1182] running status set to false 
[INFO ] 2024-07-17 16:15:06.138 - [任务 2][TargetMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-17 16:15:06.141 - [任务 2][TargetMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-17 16:15:06.142 - [任务 2][TargetMysql] - Incremental sync completed 
[INFO ] 2024-07-17 16:15:06.148 - [任务 2][TargetMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-e6653ddc-2abc-4319-99f4-14d12fdc1182 
[INFO ] 2024-07-17 16:15:06.148 - [任务 2][TargetMysql] - PDK connector node released: HazelcastSourcePdkDataNode-e6653ddc-2abc-4319-99f4-14d12fdc1182 
[INFO ] 2024-07-17 16:15:06.148 - [任务 2][TargetMysql] - Node TargetMysql[e6653ddc-2abc-4319-99f4-14d12fdc1182] schema data cleaned 
[INFO ] 2024-07-17 16:15:06.151 - [任务 2][TargetMysql] - Node TargetMysql[e6653ddc-2abc-4319-99f4-14d12fdc1182] monitor closed 
[INFO ] 2024-07-17 16:15:06.151 - [任务 2][TargetMysql] - Node TargetMysql[e6653ddc-2abc-4319-99f4-14d12fdc1182] close complete, cost 98 ms 
[INFO ] 2024-07-17 16:15:06.151 - [任务 2][TargeJushanMongo] - Node TargeJushanMongo[75c6dfd8-e487-493c-ba9d-fcd362e3c465] running status set to false 
[INFO ] 2024-07-17 16:15:06.185 - [任务 2][TargeJushanMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-75c6dfd8-e487-493c-ba9d-fcd362e3c465 
[INFO ] 2024-07-17 16:15:06.186 - [任务 2][TargeJushanMongo] - PDK connector node released: HazelcastTargetPdkDataNode-75c6dfd8-e487-493c-ba9d-fcd362e3c465 
[INFO ] 2024-07-17 16:15:06.186 - [任务 2][TargeJushanMongo] - Node TargeJushanMongo[75c6dfd8-e487-493c-ba9d-fcd362e3c465] schema data cleaned 
[INFO ] 2024-07-17 16:15:06.190 - [任务 2][TargeJushanMongo] - Node TargeJushanMongo[75c6dfd8-e487-493c-ba9d-fcd362e3c465] monitor closed 
[INFO ] 2024-07-17 16:15:06.195 - [任务 2][TargeJushanMongo] - Node TargeJushanMongo[75c6dfd8-e487-493c-ba9d-fcd362e3c465] close complete, cost 35 ms 
[INFO ] 2024-07-17 16:15:09.616 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 16:15:09.621 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@704b7277 
[INFO ] 2024-07-17 16:15:09.641 - [任务 2] - Stop task milestones: 66977ca1b92eda1a86f51468(任务 2)  
[INFO ] 2024-07-17 16:15:09.807 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-07-17 16:15:09.894 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 16:15:09.895 - [任务 2] - Remove memory task client succeed, task: 任务 2[66977ca1b92eda1a86f51468] 
[INFO ] 2024-07-17 16:15:09.895 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66977ca1b92eda1a86f51468] 
