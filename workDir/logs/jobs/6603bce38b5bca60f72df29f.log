[INFO ] 2024-03-27 14:30:07.140 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:07.141 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:07.141 - [任务 22(100)][4b016b1c-0946-462c-bded-fea75f310a34] - Node 4b016b1c-0946-462c-bded-fea75f310a34[4b016b1c-0946-462c-bded-fea75f310a34] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:30:07.142 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:07.143 - [任务 22(100)][4b016b1c-0946-462c-bded-fea75f310a34] - Node 4b016b1c-0946-462c-bded-fea75f310a34[4b016b1c-0946-462c-bded-fea75f310a34] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:07.144 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:07.902 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:30:07.920 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:07.920 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:07.920 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:30:07.920 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:30:07.920 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 21 ms 
[INFO ] 2024-03-27 14:30:08.146 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:30:08.149 - [任务 22(100)][4b016b1c-0946-462c-bded-fea75f310a34] - Node 4b016b1c-0946-462c-bded-fea75f310a34[4b016b1c-0946-462c-bded-fea75f310a34] running status set to false 
[INFO ] 2024-03-27 14:30:08.153 - [任务 22(100)][4b016b1c-0946-462c-bded-fea75f310a34] - Node 4b016b1c-0946-462c-bded-fea75f310a34[4b016b1c-0946-462c-bded-fea75f310a34] schema data cleaned 
[INFO ] 2024-03-27 14:30:08.154 - [任务 22(100)][4b016b1c-0946-462c-bded-fea75f310a34] - Node 4b016b1c-0946-462c-bded-fea75f310a34[4b016b1c-0946-462c-bded-fea75f310a34] monitor closed 
[INFO ] 2024-03-27 14:30:08.155 - [任务 22(100)][4b016b1c-0946-462c-bded-fea75f310a34] - Node 4b016b1c-0946-462c-bded-fea75f310a34[4b016b1c-0946-462c-bded-fea75f310a34] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:30:08.158 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-8a108dbe-a6b8-4a4d-9dc1-98e93b7fd3f4 
[INFO ] 2024-03-27 14:30:08.158 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-8a108dbe-a6b8-4a4d-9dc1-98e93b7fd3f4 
[INFO ] 2024-03-27 14:30:08.158 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:30:08.159 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:30:08.159 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:30:08.159 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 14 ms 
[INFO ] 2024-03-27 14:30:08.161 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-4b016b1c-0946-462c-bded-fea75f310a34 complete, cost 1087ms 
[INFO ] 2024-03-27 14:30:08.579 - [任务 22(100)][100b16a6-281c-4fa3-bd10-3e13463a5937] - Node 100b16a6-281c-4fa3-bd10-3e13463a5937[100b16a6-281c-4fa3-bd10-3e13463a5937] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:30:08.581 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:08.581 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:08.581 - [任务 22(100)][100b16a6-281c-4fa3-bd10-3e13463a5937] - Node 100b16a6-281c-4fa3-bd10-3e13463a5937[100b16a6-281c-4fa3-bd10-3e13463a5937] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:08.581 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:08.582 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:08.910 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:30:08.926 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:08.926 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:08.927 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:30:08.927 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:30:08.927 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 21 ms 
[INFO ] 2024-03-27 14:30:09.141 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:30:09.143 - [任务 22(100)][100b16a6-281c-4fa3-bd10-3e13463a5937] - Node 100b16a6-281c-4fa3-bd10-3e13463a5937[100b16a6-281c-4fa3-bd10-3e13463a5937] running status set to false 
[INFO ] 2024-03-27 14:30:09.143 - [任务 22(100)][100b16a6-281c-4fa3-bd10-3e13463a5937] - Node 100b16a6-281c-4fa3-bd10-3e13463a5937[100b16a6-281c-4fa3-bd10-3e13463a5937] schema data cleaned 
[INFO ] 2024-03-27 14:30:09.143 - [任务 22(100)][100b16a6-281c-4fa3-bd10-3e13463a5937] - Node 100b16a6-281c-4fa3-bd10-3e13463a5937[100b16a6-281c-4fa3-bd10-3e13463a5937] monitor closed 
[INFO ] 2024-03-27 14:30:09.143 - [任务 22(100)][100b16a6-281c-4fa3-bd10-3e13463a5937] - Node 100b16a6-281c-4fa3-bd10-3e13463a5937[100b16a6-281c-4fa3-bd10-3e13463a5937] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:30:09.151 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-bfeb3b9c-0dd4-4deb-a11f-7a7cb1a3f39b 
[INFO ] 2024-03-27 14:30:09.151 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-bfeb3b9c-0dd4-4deb-a11f-7a7cb1a3f39b 
[INFO ] 2024-03-27 14:30:09.152 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:30:09.153 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:30:09.153 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:30:09.153 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 12 ms 
[INFO ] 2024-03-27 14:30:09.159 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-100b16a6-281c-4fa3-bd10-3e13463a5937 complete, cost 656ms 
[INFO ] 2024-03-27 14:30:25.582 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:25.584 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:25.585 - [任务 22(100)][7797bea5-b1bd-4b19-a6a8-4879fe3fa128] - Node 7797bea5-b1bd-4b19-a6a8-4879fe3fa128[7797bea5-b1bd-4b19-a6a8-4879fe3fa128] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:30:25.585 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:25.585 - [任务 22(100)][7797bea5-b1bd-4b19-a6a8-4879fe3fa128] - Node 7797bea5-b1bd-4b19-a6a8-4879fe3fa128[7797bea5-b1bd-4b19-a6a8-4879fe3fa128] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:25.585 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:25.913 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:30:25.932 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:25.933 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:25.933 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:30:25.933 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:30:25.934 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 33 ms 
[INFO ] 2024-03-27 14:30:26.149 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:30:26.151 - [任务 22(100)][7797bea5-b1bd-4b19-a6a8-4879fe3fa128] - Node 7797bea5-b1bd-4b19-a6a8-4879fe3fa128[7797bea5-b1bd-4b19-a6a8-4879fe3fa128] running status set to false 
[INFO ] 2024-03-27 14:30:26.151 - [任务 22(100)][7797bea5-b1bd-4b19-a6a8-4879fe3fa128] - Node 7797bea5-b1bd-4b19-a6a8-4879fe3fa128[7797bea5-b1bd-4b19-a6a8-4879fe3fa128] schema data cleaned 
[INFO ] 2024-03-27 14:30:26.151 - [任务 22(100)][7797bea5-b1bd-4b19-a6a8-4879fe3fa128] - Node 7797bea5-b1bd-4b19-a6a8-4879fe3fa128[7797bea5-b1bd-4b19-a6a8-4879fe3fa128] monitor closed 
[INFO ] 2024-03-27 14:30:26.152 - [任务 22(100)][7797bea5-b1bd-4b19-a6a8-4879fe3fa128] - Node 7797bea5-b1bd-4b19-a6a8-4879fe3fa128[7797bea5-b1bd-4b19-a6a8-4879fe3fa128] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:30:26.159 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-59348aa7-1c50-4c8f-a338-7e96a99dee21 
[INFO ] 2024-03-27 14:30:26.159 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-59348aa7-1c50-4c8f-a338-7e96a99dee21 
[INFO ] 2024-03-27 14:30:26.160 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:30:26.162 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:30:26.163 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:30:26.163 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 13 ms 
[INFO ] 2024-03-27 14:30:26.170 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-7797bea5-b1bd-4b19-a6a8-4879fe3fa128 complete, cost 658ms 
[INFO ] 2024-03-27 14:30:32.432 - [任务 22(100)][4ad455d5-b0f7-47d5-8ad6-6906ef62275f] - Node 4ad455d5-b0f7-47d5-8ad6-6906ef62275f[4ad455d5-b0f7-47d5-8ad6-6906ef62275f] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:30:32.434 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:32.434 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:32.434 - [任务 22(100)][4ad455d5-b0f7-47d5-8ad6-6906ef62275f] - Node 4ad455d5-b0f7-47d5-8ad6-6906ef62275f[4ad455d5-b0f7-47d5-8ad6-6906ef62275f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:32.434 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 2 ms 
[INFO ] 2024-03-27 14:30:32.436 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 2 ms 
[INFO ] 2024-03-27 14:30:32.779 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:30:32.793 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:32.793 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:32.793 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:30:32.793 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:30:32.794 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 22 ms 
[INFO ] 2024-03-27 14:30:33.045 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:30:33.050 - [任务 22(100)][4ad455d5-b0f7-47d5-8ad6-6906ef62275f] - Node 4ad455d5-b0f7-47d5-8ad6-6906ef62275f[4ad455d5-b0f7-47d5-8ad6-6906ef62275f] running status set to false 
[INFO ] 2024-03-27 14:30:33.050 - [任务 22(100)][4ad455d5-b0f7-47d5-8ad6-6906ef62275f] - Node 4ad455d5-b0f7-47d5-8ad6-6906ef62275f[4ad455d5-b0f7-47d5-8ad6-6906ef62275f] schema data cleaned 
[INFO ] 2024-03-27 14:30:33.050 - [任务 22(100)][4ad455d5-b0f7-47d5-8ad6-6906ef62275f] - Node 4ad455d5-b0f7-47d5-8ad6-6906ef62275f[4ad455d5-b0f7-47d5-8ad6-6906ef62275f] monitor closed 
[INFO ] 2024-03-27 14:30:33.050 - [任务 22(100)][4ad455d5-b0f7-47d5-8ad6-6906ef62275f] - Node 4ad455d5-b0f7-47d5-8ad6-6906ef62275f[4ad455d5-b0f7-47d5-8ad6-6906ef62275f] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:30:33.062 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-4915221f-3b86-4d6b-a388-fa1378434a81 
[INFO ] 2024-03-27 14:30:33.062 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-4915221f-3b86-4d6b-a388-fa1378434a81 
[INFO ] 2024-03-27 14:30:33.062 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:30:33.064 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:30:33.064 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:30:33.064 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 21 ms 
[INFO ] 2024-03-27 14:30:33.070 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-4ad455d5-b0f7-47d5-8ad6-6906ef62275f complete, cost 712ms 
[INFO ] 2024-03-27 14:30:33.751 - [任务 22(100)][9e56f172-d19d-4338-a97e-877e7f2d218b] - Node 9e56f172-d19d-4338-a97e-877e7f2d218b[9e56f172-d19d-4338-a97e-877e7f2d218b] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:30:33.753 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:33.753 - [任务 22(100)][9e56f172-d19d-4338-a97e-877e7f2d218b] - Node 9e56f172-d19d-4338-a97e-877e7f2d218b[9e56f172-d19d-4338-a97e-877e7f2d218b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:33.753 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:33.753 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:33.754 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:34.095 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:30:34.108 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:34.108 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:34.109 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:30:34.109 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:30:34.109 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 24 ms 
[INFO ] 2024-03-27 14:30:34.331 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:30:34.340 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f3f38ee5-943d-419a-adbc-1ca073a32b8d 
[INFO ] 2024-03-27 14:30:34.342 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f3f38ee5-943d-419a-adbc-1ca073a32b8d 
[INFO ] 2024-03-27 14:30:34.342 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:30:34.342 - [任务 22(100)][9e56f172-d19d-4338-a97e-877e7f2d218b] - Node 9e56f172-d19d-4338-a97e-877e7f2d218b[9e56f172-d19d-4338-a97e-877e7f2d218b] running status set to false 
[INFO ] 2024-03-27 14:30:34.343 - [任务 22(100)][9e56f172-d19d-4338-a97e-877e7f2d218b] - Node 9e56f172-d19d-4338-a97e-877e7f2d218b[9e56f172-d19d-4338-a97e-877e7f2d218b] schema data cleaned 
[INFO ] 2024-03-27 14:30:34.343 - [任务 22(100)][9e56f172-d19d-4338-a97e-877e7f2d218b] - Node 9e56f172-d19d-4338-a97e-877e7f2d218b[9e56f172-d19d-4338-a97e-877e7f2d218b] monitor closed 
[INFO ] 2024-03-27 14:30:34.343 - [任务 22(100)][9e56f172-d19d-4338-a97e-877e7f2d218b] - Node 9e56f172-d19d-4338-a97e-877e7f2d218b[9e56f172-d19d-4338-a97e-877e7f2d218b] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:30:34.343 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:30:34.344 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:30:34.344 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 12 ms 
[INFO ] 2024-03-27 14:30:34.349 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-9e56f172-d19d-4338-a97e-877e7f2d218b complete, cost 684ms 
[INFO ] 2024-03-27 14:30:47.052 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:47.053 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:47.053 - [任务 22(100)][61b907b0-f9b3-4513-b4dd-6bc8aee32b57] - Node 61b907b0-f9b3-4513-b4dd-6bc8aee32b57[61b907b0-f9b3-4513-b4dd-6bc8aee32b57] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:30:47.053 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:47.053 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:47.053 - [任务 22(100)][61b907b0-f9b3-4513-b4dd-6bc8aee32b57] - Node 61b907b0-f9b3-4513-b4dd-6bc8aee32b57[61b907b0-f9b3-4513-b4dd-6bc8aee32b57] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:47.383 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:30:47.396 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:47.397 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:30:47.397 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:30:47.397 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:30:47.397 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 22 ms 
[INFO ] 2024-03-27 14:30:47.622 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:30:47.641 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f06a6492-0b01-44b4-a867-1e751ad88999 
[INFO ] 2024-03-27 14:30:47.641 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f06a6492-0b01-44b4-a867-1e751ad88999 
[INFO ] 2024-03-27 14:30:47.641 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:30:47.642 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:30:47.642 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:30:47.643 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 21 ms 
[INFO ] 2024-03-27 14:30:47.668 - [任务 22(100)][61b907b0-f9b3-4513-b4dd-6bc8aee32b57] - Node 61b907b0-f9b3-4513-b4dd-6bc8aee32b57[61b907b0-f9b3-4513-b4dd-6bc8aee32b57] running status set to false 
[INFO ] 2024-03-27 14:30:47.669 - [任务 22(100)][61b907b0-f9b3-4513-b4dd-6bc8aee32b57] - Node 61b907b0-f9b3-4513-b4dd-6bc8aee32b57[61b907b0-f9b3-4513-b4dd-6bc8aee32b57] schema data cleaned 
[INFO ] 2024-03-27 14:30:47.669 - [任务 22(100)][61b907b0-f9b3-4513-b4dd-6bc8aee32b57] - Node 61b907b0-f9b3-4513-b4dd-6bc8aee32b57[61b907b0-f9b3-4513-b4dd-6bc8aee32b57] monitor closed 
[INFO ] 2024-03-27 14:30:47.669 - [任务 22(100)][61b907b0-f9b3-4513-b4dd-6bc8aee32b57] - Node 61b907b0-f9b3-4513-b4dd-6bc8aee32b57[61b907b0-f9b3-4513-b4dd-6bc8aee32b57] close complete, cost 1 ms 
[INFO ] 2024-03-27 14:30:47.674 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-61b907b0-f9b3-4513-b4dd-6bc8aee32b57 complete, cost 688ms 
[INFO ] 2024-03-27 14:31:01.727 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:01.730 - [任务 22(100)][a091d09b-0cb0-4391-aba1-51d2d8584309] - Node a091d09b-0cb0-4391-aba1-51d2d8584309[a091d09b-0cb0-4391-aba1-51d2d8584309] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:01.730 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:01.730 - [任务 22(100)][a091d09b-0cb0-4391-aba1-51d2d8584309] - Node a091d09b-0cb0-4391-aba1-51d2d8584309[a091d09b-0cb0-4391-aba1-51d2d8584309] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:01.730 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:01.730 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 3 ms 
[INFO ] 2024-03-27 14:31:02.045 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:02.061 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:02.061 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:02.061 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:02.061 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:02.061 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 22 ms 
[INFO ] 2024-03-27 14:31:02.282 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:02.298 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-163f7542-3378-4027-9167-ca182773cf5f 
[INFO ] 2024-03-27 14:31:02.298 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-163f7542-3378-4027-9167-ca182773cf5f 
[INFO ] 2024-03-27 14:31:02.298 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:02.300 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:02.301 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:02.301 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 19 ms 
[INFO ] 2024-03-27 14:31:02.319 - [任务 22(100)][a091d09b-0cb0-4391-aba1-51d2d8584309] - Node a091d09b-0cb0-4391-aba1-51d2d8584309[a091d09b-0cb0-4391-aba1-51d2d8584309] running status set to false 
[INFO ] 2024-03-27 14:31:02.321 - [任务 22(100)][a091d09b-0cb0-4391-aba1-51d2d8584309] - Node a091d09b-0cb0-4391-aba1-51d2d8584309[a091d09b-0cb0-4391-aba1-51d2d8584309] schema data cleaned 
[INFO ] 2024-03-27 14:31:02.321 - [任务 22(100)][a091d09b-0cb0-4391-aba1-51d2d8584309] - Node a091d09b-0cb0-4391-aba1-51d2d8584309[a091d09b-0cb0-4391-aba1-51d2d8584309] monitor closed 
[INFO ] 2024-03-27 14:31:02.321 - [任务 22(100)][a091d09b-0cb0-4391-aba1-51d2d8584309] - Node a091d09b-0cb0-4391-aba1-51d2d8584309[a091d09b-0cb0-4391-aba1-51d2d8584309] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:31:02.325 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-a091d09b-0cb0-4391-aba1-51d2d8584309 complete, cost 718ms 
[INFO ] 2024-03-27 14:31:03.251 - [任务 22(100)][f9541862-bbcc-469c-887d-2a93b585997a] - Node f9541862-bbcc-469c-887d-2a93b585997a[f9541862-bbcc-469c-887d-2a93b585997a] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:03.252 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:03.252 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:03.252 - [任务 22(100)][f9541862-bbcc-469c-887d-2a93b585997a] - Node f9541862-bbcc-469c-887d-2a93b585997a[f9541862-bbcc-469c-887d-2a93b585997a] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:03.252 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:03.252 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:03.608 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:03.623 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:03.624 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:03.624 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:03.624 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:03.625 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 23 ms 
[INFO ] 2024-03-27 14:31:03.856 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:03.870 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-08a5c2d7-663f-4065-b98d-ecdee4d3a8b7 
[INFO ] 2024-03-27 14:31:03.870 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-08a5c2d7-663f-4065-b98d-ecdee4d3a8b7 
[INFO ] 2024-03-27 14:31:03.870 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:03.871 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:03.871 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:03.872 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 22 ms 
[INFO ] 2024-03-27 14:31:03.879 - [任务 22(100)][f9541862-bbcc-469c-887d-2a93b585997a] - Node f9541862-bbcc-469c-887d-2a93b585997a[f9541862-bbcc-469c-887d-2a93b585997a] running status set to false 
[INFO ] 2024-03-27 14:31:03.879 - [任务 22(100)][f9541862-bbcc-469c-887d-2a93b585997a] - Node f9541862-bbcc-469c-887d-2a93b585997a[f9541862-bbcc-469c-887d-2a93b585997a] schema data cleaned 
[INFO ] 2024-03-27 14:31:03.879 - [任务 22(100)][f9541862-bbcc-469c-887d-2a93b585997a] - Node f9541862-bbcc-469c-887d-2a93b585997a[f9541862-bbcc-469c-887d-2a93b585997a] monitor closed 
[INFO ] 2024-03-27 14:31:03.879 - [任务 22(100)][f9541862-bbcc-469c-887d-2a93b585997a] - Node f9541862-bbcc-469c-887d-2a93b585997a[f9541862-bbcc-469c-887d-2a93b585997a] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:31:03.882 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-f9541862-bbcc-469c-887d-2a93b585997a complete, cost 728ms 
[INFO ] 2024-03-27 14:31:05.340 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:05.342 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:05.342 - [任务 22(100)][19bd4d2b-4da5-4449-8ad6-2ba8614f578a] - Node 19bd4d2b-4da5-4449-8ad6-2ba8614f578a[19bd4d2b-4da5-4449-8ad6-2ba8614f578a] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:05.342 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:05.342 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:05.342 - [任务 22(100)][19bd4d2b-4da5-4449-8ad6-2ba8614f578a] - Node 19bd4d2b-4da5-4449-8ad6-2ba8614f578a[19bd4d2b-4da5-4449-8ad6-2ba8614f578a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:05.685 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:05.701 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:05.701 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:05.701 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:05.701 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:05.701 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 23 ms 
[INFO ] 2024-03-27 14:31:05.819 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:05.831 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:05.834 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:05.835 - [任务 22(100)][8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] - Node 8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0[8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:05.835 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:05.835 - [任务 22(100)][8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] - Node 8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0[8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:05.939 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:05.943 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0183a2b3-2551-4a45-8202-881aa5306a6c 
[INFO ] 2024-03-27 14:31:05.943 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0183a2b3-2551-4a45-8202-881aa5306a6c 
[INFO ] 2024-03-27 14:31:05.944 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:05.944 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:05.944 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:05.945 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 6 ms 
[INFO ] 2024-03-27 14:31:05.961 - [任务 22(100)][19bd4d2b-4da5-4449-8ad6-2ba8614f578a] - Node 19bd4d2b-4da5-4449-8ad6-2ba8614f578a[19bd4d2b-4da5-4449-8ad6-2ba8614f578a] running status set to false 
[INFO ] 2024-03-27 14:31:05.962 - [任务 22(100)][19bd4d2b-4da5-4449-8ad6-2ba8614f578a] - Node 19bd4d2b-4da5-4449-8ad6-2ba8614f578a[19bd4d2b-4da5-4449-8ad6-2ba8614f578a] schema data cleaned 
[INFO ] 2024-03-27 14:31:05.962 - [任务 22(100)][19bd4d2b-4da5-4449-8ad6-2ba8614f578a] - Node 19bd4d2b-4da5-4449-8ad6-2ba8614f578a[19bd4d2b-4da5-4449-8ad6-2ba8614f578a] monitor closed 
[INFO ] 2024-03-27 14:31:05.962 - [任务 22(100)][19bd4d2b-4da5-4449-8ad6-2ba8614f578a] - Node 19bd4d2b-4da5-4449-8ad6-2ba8614f578a[19bd4d2b-4da5-4449-8ad6-2ba8614f578a] close complete, cost 1 ms 
[INFO ] 2024-03-27 14:31:05.964 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-19bd4d2b-4da5-4449-8ad6-2ba8614f578a complete, cost 693ms 
[INFO ] 2024-03-27 14:31:06.154 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:06.175 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:06.176 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:06.176 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:06.176 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:06.177 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 32 ms 
[INFO ] 2024-03-27 14:31:06.412 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:06.421 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-9d8dda97-32db-4a8f-9069-bf3103273909 
[INFO ] 2024-03-27 14:31:06.421 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-9d8dda97-32db-4a8f-9069-bf3103273909 
[INFO ] 2024-03-27 14:31:06.421 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:06.422 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:06.422 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:06.424 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 17 ms 
[INFO ] 2024-03-27 14:31:06.445 - [任务 22(100)][8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] - Node 8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0[8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] running status set to false 
[INFO ] 2024-03-27 14:31:06.445 - [任务 22(100)][8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] - Node 8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0[8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] schema data cleaned 
[INFO ] 2024-03-27 14:31:06.445 - [任务 22(100)][8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] - Node 8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0[8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] monitor closed 
[INFO ] 2024-03-27 14:31:06.446 - [任务 22(100)][8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] - Node 8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0[8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:31:06.450 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-8fb7ba0f-1b5d-48b5-aec9-f003bc7c28f0 complete, cost 670ms 
[INFO ] 2024-03-27 14:31:09.885 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:09.885 - [任务 22(100)][9ba67b81-0452-41ce-b4df-4a40eacb5ab3] - Node 9ba67b81-0452-41ce-b4df-4a40eacb5ab3[9ba67b81-0452-41ce-b4df-4a40eacb5ab3] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:09.885 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:09.885 - [任务 22(100)][9ba67b81-0452-41ce-b4df-4a40eacb5ab3] - Node 9ba67b81-0452-41ce-b4df-4a40eacb5ab3[9ba67b81-0452-41ce-b4df-4a40eacb5ab3] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:09.885 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:09.885 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:10.158 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:10.172 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:10.172 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:10.172 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:10.172 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:10.173 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 13 ms 
[INFO ] 2024-03-27 14:31:10.388 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:10.393 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-71cee923-c681-4316-90ba-21052e949a8a 
[INFO ] 2024-03-27 14:31:10.393 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-71cee923-c681-4316-90ba-21052e949a8a 
[INFO ] 2024-03-27 14:31:10.393 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:10.394 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:10.395 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:10.395 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 6 ms 
[INFO ] 2024-03-27 14:31:10.408 - [任务 22(100)][9ba67b81-0452-41ce-b4df-4a40eacb5ab3] - Node 9ba67b81-0452-41ce-b4df-4a40eacb5ab3[9ba67b81-0452-41ce-b4df-4a40eacb5ab3] running status set to false 
[INFO ] 2024-03-27 14:31:10.408 - [任务 22(100)][9ba67b81-0452-41ce-b4df-4a40eacb5ab3] - Node 9ba67b81-0452-41ce-b4df-4a40eacb5ab3[9ba67b81-0452-41ce-b4df-4a40eacb5ab3] schema data cleaned 
[INFO ] 2024-03-27 14:31:10.409 - [任务 22(100)][9ba67b81-0452-41ce-b4df-4a40eacb5ab3] - Node 9ba67b81-0452-41ce-b4df-4a40eacb5ab3[9ba67b81-0452-41ce-b4df-4a40eacb5ab3] monitor closed 
[INFO ] 2024-03-27 14:31:10.409 - [任务 22(100)][9ba67b81-0452-41ce-b4df-4a40eacb5ab3] - Node 9ba67b81-0452-41ce-b4df-4a40eacb5ab3[9ba67b81-0452-41ce-b4df-4a40eacb5ab3] close complete, cost 1 ms 
[INFO ] 2024-03-27 14:31:10.410 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-9ba67b81-0452-41ce-b4df-4a40eacb5ab3 complete, cost 564ms 
[INFO ] 2024-03-27 14:31:12.552 - [任务 22(100)][b75fca7a-b3d7-4198-924a-423dda6fb410] - Node b75fca7a-b3d7-4198-924a-423dda6fb410[b75fca7a-b3d7-4198-924a-423dda6fb410] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:12.553 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:12.553 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:12.553 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:12.553 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:12.553 - [任务 22(100)][b75fca7a-b3d7-4198-924a-423dda6fb410] - Node b75fca7a-b3d7-4198-924a-423dda6fb410[b75fca7a-b3d7-4198-924a-423dda6fb410] preload schema finished, cost 2 ms 
[INFO ] 2024-03-27 14:31:12.876 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:12.905 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:12.906 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:12.907 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:12.907 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:12.907 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 31 ms 
[INFO ] 2024-03-27 14:31:13.137 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:13.140 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a16c2652-bea8-45b5-a222-87857f803ec5 
[INFO ] 2024-03-27 14:31:13.141 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a16c2652-bea8-45b5-a222-87857f803ec5 
[INFO ] 2024-03-27 14:31:13.141 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:13.142 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:13.143 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:13.143 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 5 ms 
[INFO ] 2024-03-27 14:31:13.163 - [任务 22(100)][b75fca7a-b3d7-4198-924a-423dda6fb410] - Node b75fca7a-b3d7-4198-924a-423dda6fb410[b75fca7a-b3d7-4198-924a-423dda6fb410] running status set to false 
[INFO ] 2024-03-27 14:31:13.163 - [任务 22(100)][b75fca7a-b3d7-4198-924a-423dda6fb410] - Node b75fca7a-b3d7-4198-924a-423dda6fb410[b75fca7a-b3d7-4198-924a-423dda6fb410] schema data cleaned 
[INFO ] 2024-03-27 14:31:13.163 - [任务 22(100)][b75fca7a-b3d7-4198-924a-423dda6fb410] - Node b75fca7a-b3d7-4198-924a-423dda6fb410[b75fca7a-b3d7-4198-924a-423dda6fb410] monitor closed 
[INFO ] 2024-03-27 14:31:13.163 - [任务 22(100)][b75fca7a-b3d7-4198-924a-423dda6fb410] - Node b75fca7a-b3d7-4198-924a-423dda6fb410[b75fca7a-b3d7-4198-924a-423dda6fb410] close complete, cost 1 ms 
[INFO ] 2024-03-27 14:31:13.174 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-b75fca7a-b3d7-4198-924a-423dda6fb410 complete, cost 690ms 
[INFO ] 2024-03-27 14:31:40.714 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:40.715 - [任务 22(100)][3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] - Node 3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44[3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:40.715 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:40.715 - [任务 22(100)][3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] - Node 3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44[3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:40.715 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:31:40.715 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:41.022 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:41.037 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:41.037 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:41.037 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:41.037 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:41.037 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 15 ms 
[INFO ] 2024-03-27 14:31:41.260 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:41.264 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6139c9b9-207f-4f80-ad72-d9fdae288b23 
[INFO ] 2024-03-27 14:31:41.264 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-6139c9b9-207f-4f80-ad72-d9fdae288b23 
[INFO ] 2024-03-27 14:31:41.265 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:41.265 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:41.265 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:41.265 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 5 ms 
[INFO ] 2024-03-27 14:31:41.279 - [任务 22(100)][3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] - Node 3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44[3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] running status set to false 
[INFO ] 2024-03-27 14:31:41.279 - [任务 22(100)][3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] - Node 3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44[3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] schema data cleaned 
[INFO ] 2024-03-27 14:31:41.279 - [任务 22(100)][3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] - Node 3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44[3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] monitor closed 
[INFO ] 2024-03-27 14:31:41.279 - [任务 22(100)][3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] - Node 3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44[3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44] close complete, cost 1 ms 
[INFO ] 2024-03-27 14:31:41.284 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-3ae4c9e4-fbf0-40e4-966a-99cf6d2a9d44 complete, cost 622ms 
[INFO ] 2024-03-27 14:31:43.059 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:43.060 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:31:43.061 - [任务 22(100)][7842a957-c831-4ac1-b998-5c9c9fe0a7e1] - Node 7842a957-c831-4ac1-b998-5c9c9fe0a7e1[7842a957-c831-4ac1-b998-5c9c9fe0a7e1] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:31:43.061 - [任务 22(100)][7842a957-c831-4ac1-b998-5c9c9fe0a7e1] - Node 7842a957-c831-4ac1-b998-5c9c9fe0a7e1[7842a957-c831-4ac1-b998-5c9c9fe0a7e1] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:31:43.061 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 2 ms 
[INFO ] 2024-03-27 14:31:43.061 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 3 ms 
[INFO ] 2024-03-27 14:31:43.382 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:31:43.395 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:43.395 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:31:43.396 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:31:43.396 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:31:43.396 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 16 ms 
[INFO ] 2024-03-27 14:31:43.614 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:31:43.621 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-fac483e5-ec8f-4903-96f3-8f85a09212a7 
[INFO ] 2024-03-27 14:31:43.622 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-fac483e5-ec8f-4903-96f3-8f85a09212a7 
[INFO ] 2024-03-27 14:31:43.622 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:31:43.623 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:31:43.623 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:31:43.624 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 11 ms 
[INFO ] 2024-03-27 14:31:43.645 - [任务 22(100)][7842a957-c831-4ac1-b998-5c9c9fe0a7e1] - Node 7842a957-c831-4ac1-b998-5c9c9fe0a7e1[7842a957-c831-4ac1-b998-5c9c9fe0a7e1] running status set to false 
[INFO ] 2024-03-27 14:31:43.646 - [任务 22(100)][7842a957-c831-4ac1-b998-5c9c9fe0a7e1] - Node 7842a957-c831-4ac1-b998-5c9c9fe0a7e1[7842a957-c831-4ac1-b998-5c9c9fe0a7e1] schema data cleaned 
[INFO ] 2024-03-27 14:31:43.646 - [任务 22(100)][7842a957-c831-4ac1-b998-5c9c9fe0a7e1] - Node 7842a957-c831-4ac1-b998-5c9c9fe0a7e1[7842a957-c831-4ac1-b998-5c9c9fe0a7e1] monitor closed 
[INFO ] 2024-03-27 14:31:43.647 - [任务 22(100)][7842a957-c831-4ac1-b998-5c9c9fe0a7e1] - Node 7842a957-c831-4ac1-b998-5c9c9fe0a7e1[7842a957-c831-4ac1-b998-5c9c9fe0a7e1] close complete, cost 2 ms 
[INFO ] 2024-03-27 14:31:43.653 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-7842a957-c831-4ac1-b998-5c9c9fe0a7e1 complete, cost 684ms 
[INFO ] 2024-03-27 14:38:58.684 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:38:58.687 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:38:58.688 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:38:58.688 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:38:58.689 - [任务 22(100)][1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] - Node 1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77[1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:38:58.689 - [任务 22(100)][1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] - Node 1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77[1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:38:58.956 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:38:58.963 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:38:58.963 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:38:58.964 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:38:58.964 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:38:58.964 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 9 ms 
[INFO ] 2024-03-27 14:38:59.195 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:38:59.204 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7712cea7-3fb9-4b7a-a992-4d431ee5583d 
[INFO ] 2024-03-27 14:38:59.204 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-7712cea7-3fb9-4b7a-a992-4d431ee5583d 
[INFO ] 2024-03-27 14:38:59.204 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:38:59.206 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:38:59.206 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:38:59.207 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 12 ms 
[INFO ] 2024-03-27 14:38:59.229 - [任务 22(100)][1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] - Node 1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77[1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] running status set to false 
[INFO ] 2024-03-27 14:38:59.229 - [任务 22(100)][1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] - Node 1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77[1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] schema data cleaned 
[INFO ] 2024-03-27 14:38:59.229 - [任务 22(100)][1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] - Node 1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77[1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] monitor closed 
[INFO ] 2024-03-27 14:38:59.229 - [任务 22(100)][1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] - Node 1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77[1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77] close complete, cost 1 ms 
[INFO ] 2024-03-27 14:38:59.233 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-1b07535a-0c0b-4fd4-bee9-f9c6a3f67f77 complete, cost 624ms 
[INFO ] 2024-03-27 14:38:59.936 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:38:59.938 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:38:59.941 - [任务 22(100)][f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] - Node f0078f47-9a0d-44e7-9fe1-4660a5ef24ba[f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:38:59.942 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:38:59.942 - [任务 22(100)][f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] - Node f0078f47-9a0d-44e7-9fe1-4660a5ef24ba[f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:38:59.942 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 2 ms 
[INFO ] 2024-03-27 14:39:00.268 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 14:39:00.272 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:39:00.272 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 14:39:00.272 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 14:39:00.272 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 14:39:00.273 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 22 ms 
[INFO ] 2024-03-27 14:39:00.489 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 14:39:00.498 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ac4a27b3-ceab-4716-800f-c534ed0a1bbc 
[INFO ] 2024-03-27 14:39:00.498 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ac4a27b3-ceab-4716-800f-c534ed0a1bbc 
[INFO ] 2024-03-27 14:39:00.498 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:39:00.499 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 14:39:00.499 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 14:39:00.500 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 15 ms 
[INFO ] 2024-03-27 14:39:00.520 - [任务 22(100)][f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] - Node f0078f47-9a0d-44e7-9fe1-4660a5ef24ba[f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] running status set to false 
[INFO ] 2024-03-27 14:39:00.520 - [任务 22(100)][f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] - Node f0078f47-9a0d-44e7-9fe1-4660a5ef24ba[f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] schema data cleaned 
[INFO ] 2024-03-27 14:39:00.520 - [任务 22(100)][f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] - Node f0078f47-9a0d-44e7-9fe1-4660a5ef24ba[f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] monitor closed 
[INFO ] 2024-03-27 14:39:00.520 - [任务 22(100)][f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] - Node f0078f47-9a0d-44e7-9fe1-4660a5ef24ba[f0078f47-9a0d-44e7-9fe1-4660a5ef24ba] close complete, cost 2 ms 
[INFO ] 2024-03-27 14:39:00.522 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-f0078f47-9a0d-44e7-9fe1-4660a5ef24ba complete, cost 664ms 
[INFO ] 2024-03-27 15:51:18.767 - [任务 22(100)][1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] - Node 1af169bd-6ce4-4203-a9f5-feaa2fc2a67f[1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] start preload schema,table counts: 0 
[INFO ] 2024-03-27 15:51:18.770 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 15:51:18.770 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 15:51:18.771 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 15:51:18.771 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 15:51:18.771 - [任务 22(100)][1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] - Node 1af169bd-6ce4-4203-a9f5-feaa2fc2a67f[1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 15:51:18.771 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 15:51:18.771 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 22 ms 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d6186e5e-4570-4ade-a7f6-1a96cec97bf4 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d6186e5e-4570-4ade-a7f6-1a96cec97bf4 
[INFO ] 2024-03-27 15:51:18.772 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 15:51:18.773 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 15:51:18.773 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 15:51:18.773 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 7 ms 
[INFO ] 2024-03-27 15:51:18.773 - [任务 22(100)][1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] - Node 1af169bd-6ce4-4203-a9f5-feaa2fc2a67f[1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] running status set to false 
[INFO ] 2024-03-27 15:51:18.773 - [任务 22(100)][1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] - Node 1af169bd-6ce4-4203-a9f5-feaa2fc2a67f[1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] schema data cleaned 
[INFO ] 2024-03-27 15:51:18.773 - [任务 22(100)][1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] - Node 1af169bd-6ce4-4203-a9f5-feaa2fc2a67f[1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] monitor closed 
[INFO ] 2024-03-27 15:51:18.774 - [任务 22(100)][1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] - Node 1af169bd-6ce4-4203-a9f5-feaa2fc2a67f[1af169bd-6ce4-4203-a9f5-feaa2fc2a67f] close complete, cost 0 ms 
[INFO ] 2024-03-27 15:51:18.774 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-1af169bd-6ce4-4203-a9f5-feaa2fc2a67f complete, cost 734ms 
[INFO ] 2024-03-27 16:25:52.184 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:25:52.219 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:25:52.221 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:25:52.222 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:25:52.223 - [任务 22(100)][3597d795-7768-406e-a3dd-19c6a3c00aeb] - Node 3597d795-7768-406e-a3dd-19c6a3c00aeb[3597d795-7768-406e-a3dd-19c6a3c00aeb] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:25:52.224 - [任务 22(100)][3597d795-7768-406e-a3dd-19c6a3c00aeb] - Node 3597d795-7768-406e-a3dd-19c6a3c00aeb[3597d795-7768-406e-a3dd-19c6a3c00aeb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:25:53.074 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:25:53.174 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:25:53.256 - [任务 22(100)][3597d795-7768-406e-a3dd-19c6a3c00aeb] - Node 3597d795-7768-406e-a3dd-19c6a3c00aeb[3597d795-7768-406e-a3dd-19c6a3c00aeb] running status set to false 
[INFO ] 2024-03-27 16:25:53.260 - [任务 22(100)][3597d795-7768-406e-a3dd-19c6a3c00aeb] - Node 3597d795-7768-406e-a3dd-19c6a3c00aeb[3597d795-7768-406e-a3dd-19c6a3c00aeb] schema data cleaned 
[INFO ] 2024-03-27 16:25:53.260 - [任务 22(100)][3597d795-7768-406e-a3dd-19c6a3c00aeb] - Node 3597d795-7768-406e-a3dd-19c6a3c00aeb[3597d795-7768-406e-a3dd-19c6a3c00aeb] monitor closed 
[INFO ] 2024-03-27 16:25:53.261 - [任务 22(100)][3597d795-7768-406e-a3dd-19c6a3c00aeb] - Node 3597d795-7768-406e-a3dd-19c6a3c00aeb[3597d795-7768-406e-a3dd-19c6a3c00aeb] close complete, cost 3 ms 
[INFO ] 2024-03-27 16:25:53.320 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:25:53.325 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-38d371a2-8da4-4733-98d4-981fff518c3f 
[INFO ] 2024-03-27 16:25:53.325 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:25:53.327 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:25:53.332 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-38d371a2-8da4-4733-98d4-981fff518c3f 
[INFO ] 2024-03-27 16:25:53.333 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:25:53.335 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:25:53.335 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 254 ms 
[INFO ] 2024-03-27 16:25:53.336 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:25:53.338 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:25:53.339 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 166 ms 
[INFO ] 2024-03-27 16:25:53.347 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-3597d795-7768-406e-a3dd-19c6a3c00aeb complete, cost 1551ms 
[INFO ] 2024-03-27 16:28:04.109 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:04.110 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:04.111 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:04.112 - [任务 22(100)][3f277858-d6c9-456d-887e-f57f70b00358] - Node 3f277858-d6c9-456d-887e-f57f70b00358[3f277858-d6c9-456d-887e-f57f70b00358] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:04.112 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:04.113 - [任务 22(100)][3f277858-d6c9-456d-887e-f57f70b00358] - Node 3f277858-d6c9-456d-887e-f57f70b00358[3f277858-d6c9-456d-887e-f57f70b00358] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:04.140 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:04.155 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:04.155 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:04.155 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:04.156 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:04.157 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 21 ms 
[INFO ] 2024-03-27 16:28:04.419 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:04.439 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6fd07c22-a23d-4f2a-a72a-9c2dfb1cf9ed 
[INFO ] 2024-03-27 16:28:04.439 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-6fd07c22-a23d-4f2a-a72a-9c2dfb1cf9ed 
[INFO ] 2024-03-27 16:28:04.439 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:04.442 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:04.442 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:04.442 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 31 ms 
[INFO ] 2024-03-27 16:28:04.464 - [任务 22(100)][3f277858-d6c9-456d-887e-f57f70b00358] - Node 3f277858-d6c9-456d-887e-f57f70b00358[3f277858-d6c9-456d-887e-f57f70b00358] running status set to false 
[INFO ] 2024-03-27 16:28:04.465 - [任务 22(100)][3f277858-d6c9-456d-887e-f57f70b00358] - Node 3f277858-d6c9-456d-887e-f57f70b00358[3f277858-d6c9-456d-887e-f57f70b00358] schema data cleaned 
[INFO ] 2024-03-27 16:28:04.466 - [任务 22(100)][3f277858-d6c9-456d-887e-f57f70b00358] - Node 3f277858-d6c9-456d-887e-f57f70b00358[3f277858-d6c9-456d-887e-f57f70b00358] monitor closed 
[INFO ] 2024-03-27 16:28:04.466 - [任务 22(100)][3f277858-d6c9-456d-887e-f57f70b00358] - Node 3f277858-d6c9-456d-887e-f57f70b00358[3f277858-d6c9-456d-887e-f57f70b00358] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:28:04.470 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-3f277858-d6c9-456d-887e-f57f70b00358 complete, cost 722ms 
[INFO ] 2024-03-27 16:28:12.312 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:12.312 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:12.316 - [任务 22(100)][848a2ebe-bf9c-4a34-900f-bc9dcfd73329] - Node 848a2ebe-bf9c-4a34-900f-bc9dcfd73329[848a2ebe-bf9c-4a34-900f-bc9dcfd73329] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:12.318 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:12.318 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:12.319 - [任务 22(100)][848a2ebe-bf9c-4a34-900f-bc9dcfd73329] - Node 848a2ebe-bf9c-4a34-900f-bc9dcfd73329[848a2ebe-bf9c-4a34-900f-bc9dcfd73329] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:12.611 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:12.621 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:12.622 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:12.623 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:12.623 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:12.624 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 24 ms 
[INFO ] 2024-03-27 16:28:12.869 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:12.890 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-905d3e2d-6ea3-40ef-909d-9f5979c09422 
[INFO ] 2024-03-27 16:28:12.891 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-905d3e2d-6ea3-40ef-909d-9f5979c09422 
[INFO ] 2024-03-27 16:28:12.891 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:12.892 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:12.892 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:12.897 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 33 ms 
[INFO ] 2024-03-27 16:28:12.909 - [任务 22(100)][848a2ebe-bf9c-4a34-900f-bc9dcfd73329] - Node 848a2ebe-bf9c-4a34-900f-bc9dcfd73329[848a2ebe-bf9c-4a34-900f-bc9dcfd73329] running status set to false 
[INFO ] 2024-03-27 16:28:12.910 - [任务 22(100)][848a2ebe-bf9c-4a34-900f-bc9dcfd73329] - Node 848a2ebe-bf9c-4a34-900f-bc9dcfd73329[848a2ebe-bf9c-4a34-900f-bc9dcfd73329] schema data cleaned 
[INFO ] 2024-03-27 16:28:12.910 - [任务 22(100)][848a2ebe-bf9c-4a34-900f-bc9dcfd73329] - Node 848a2ebe-bf9c-4a34-900f-bc9dcfd73329[848a2ebe-bf9c-4a34-900f-bc9dcfd73329] monitor closed 
[INFO ] 2024-03-27 16:28:12.910 - [任务 22(100)][848a2ebe-bf9c-4a34-900f-bc9dcfd73329] - Node 848a2ebe-bf9c-4a34-900f-bc9dcfd73329[848a2ebe-bf9c-4a34-900f-bc9dcfd73329] close complete, cost 2 ms 
[INFO ] 2024-03-27 16:28:12.913 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-848a2ebe-bf9c-4a34-900f-bc9dcfd73329 complete, cost 653ms 
[INFO ] 2024-03-27 16:28:48.766 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:48.767 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:48.768 - [任务 22(100)][9314f24c-9aa6-43d4-8aec-215057c5eddb] - Node 9314f24c-9aa6-43d4-8aec-215057c5eddb[9314f24c-9aa6-43d4-8aec-215057c5eddb] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:48.768 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:48.768 - [任务 22(100)][9314f24c-9aa6-43d4-8aec-215057c5eddb] - Node 9314f24c-9aa6-43d4-8aec-215057c5eddb[9314f24c-9aa6-43d4-8aec-215057c5eddb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:48.768 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:49.463 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:49.493 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:49.494 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:49.494 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:49.494 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:49.498 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:49.506 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 34 ms 
[INFO ] 2024-03-27 16:28:49.507 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-49e1b25a-be71-4009-ace6-a8cb17b6317b 
[INFO ] 2024-03-27 16:28:49.510 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-49e1b25a-be71-4009-ace6-a8cb17b6317b 
[INFO ] 2024-03-27 16:28:49.514 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:49.518 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:49.526 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:49.531 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 23 ms 
[INFO ] 2024-03-27 16:28:49.534 - [任务 22(100)][9314f24c-9aa6-43d4-8aec-215057c5eddb] - Node 9314f24c-9aa6-43d4-8aec-215057c5eddb[9314f24c-9aa6-43d4-8aec-215057c5eddb] running status set to false 
[INFO ] 2024-03-27 16:28:49.538 - [任务 22(100)][9314f24c-9aa6-43d4-8aec-215057c5eddb] - Node 9314f24c-9aa6-43d4-8aec-215057c5eddb[9314f24c-9aa6-43d4-8aec-215057c5eddb] schema data cleaned 
[INFO ] 2024-03-27 16:28:49.542 - [任务 22(100)][9314f24c-9aa6-43d4-8aec-215057c5eddb] - Node 9314f24c-9aa6-43d4-8aec-215057c5eddb[9314f24c-9aa6-43d4-8aec-215057c5eddb] monitor closed 
[INFO ] 2024-03-27 16:28:49.546 - [任务 22(100)][9314f24c-9aa6-43d4-8aec-215057c5eddb] - Node 9314f24c-9aa6-43d4-8aec-215057c5eddb[9314f24c-9aa6-43d4-8aec-215057c5eddb] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:28:49.554 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-9314f24c-9aa6-43d4-8aec-215057c5eddb complete, cost 820ms 
[INFO ] 2024-03-27 16:28:54.730 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:54.731 - [任务 22(100)][5e4ce769-26c6-4d69-a1d0-021f05aa0f87] - Node 5e4ce769-26c6-4d69-a1d0-021f05aa0f87[5e4ce769-26c6-4d69-a1d0-021f05aa0f87] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:54.732 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:54.732 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:54.732 - [任务 22(100)][5e4ce769-26c6-4d69-a1d0-021f05aa0f87] - Node 5e4ce769-26c6-4d69-a1d0-021f05aa0f87[5e4ce769-26c6-4d69-a1d0-021f05aa0f87] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:54.733 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:54.991 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:55.004 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:55.005 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:55.006 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.007 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:55.007 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 16 ms 
[INFO ] 2024-03-27 16:28:55.256 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:55.259 - [任务 22(100)][5e4ce769-26c6-4d69-a1d0-021f05aa0f87] - Node 5e4ce769-26c6-4d69-a1d0-021f05aa0f87[5e4ce769-26c6-4d69-a1d0-021f05aa0f87] running status set to false 
[INFO ] 2024-03-27 16:28:55.264 - [任务 22(100)][5e4ce769-26c6-4d69-a1d0-021f05aa0f87] - Node 5e4ce769-26c6-4d69-a1d0-021f05aa0f87[5e4ce769-26c6-4d69-a1d0-021f05aa0f87] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.266 - [任务 22(100)][5e4ce769-26c6-4d69-a1d0-021f05aa0f87] - Node 5e4ce769-26c6-4d69-a1d0-021f05aa0f87[5e4ce769-26c6-4d69-a1d0-021f05aa0f87] monitor closed 
[INFO ] 2024-03-27 16:28:55.266 - [任务 22(100)][5e4ce769-26c6-4d69-a1d0-021f05aa0f87] - Node 5e4ce769-26c6-4d69-a1d0-021f05aa0f87[5e4ce769-26c6-4d69-a1d0-021f05aa0f87] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:28:55.275 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-52032d3a-2167-4179-a502-16d088c2895d 
[INFO ] 2024-03-27 16:28:55.276 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-52032d3a-2167-4179-a502-16d088c2895d 
[INFO ] 2024-03-27 16:28:55.277 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.280 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.280 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:55.280 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 33 ms 
[INFO ] 2024-03-27 16:28:55.285 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-5e4ce769-26c6-4d69-a1d0-021f05aa0f87 complete, cost 611ms 
[INFO ] 2024-03-27 16:28:55.373 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:55.374 - [任务 22(100)][bd58ebec-d671-440e-b0a6-ba7e3b54eee6] - Node bd58ebec-d671-440e-b0a6-ba7e3b54eee6[bd58ebec-d671-440e-b0a6-ba7e3b54eee6] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:55.374 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:55.374 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:55.374 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:55.374 - [任务 22(100)][bd58ebec-d671-440e-b0a6-ba7e3b54eee6] - Node bd58ebec-d671-440e-b0a6-ba7e3b54eee6[bd58ebec-d671-440e-b0a6-ba7e3b54eee6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:55.646 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:55.654 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:55.654 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:55.654 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.654 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:55.655 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 11 ms 
[INFO ] 2024-03-27 16:28:55.882 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:55.884 - [任务 22(100)][bd58ebec-d671-440e-b0a6-ba7e3b54eee6] - Node bd58ebec-d671-440e-b0a6-ba7e3b54eee6[bd58ebec-d671-440e-b0a6-ba7e3b54eee6] running status set to false 
[INFO ] 2024-03-27 16:28:55.884 - [任务 22(100)][bd58ebec-d671-440e-b0a6-ba7e3b54eee6] - Node bd58ebec-d671-440e-b0a6-ba7e3b54eee6[bd58ebec-d671-440e-b0a6-ba7e3b54eee6] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.885 - [任务 22(100)][bd58ebec-d671-440e-b0a6-ba7e3b54eee6] - Node bd58ebec-d671-440e-b0a6-ba7e3b54eee6[bd58ebec-d671-440e-b0a6-ba7e3b54eee6] monitor closed 
[INFO ] 2024-03-27 16:28:55.885 - [任务 22(100)][bd58ebec-d671-440e-b0a6-ba7e3b54eee6] - Node bd58ebec-d671-440e-b0a6-ba7e3b54eee6[bd58ebec-d671-440e-b0a6-ba7e3b54eee6] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:28:55.892 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-faafa57a-386f-4668-9d19-96493c9f9e91 
[INFO ] 2024-03-27 16:28:55.893 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-faafa57a-386f-4668-9d19-96493c9f9e91 
[INFO ] 2024-03-27 16:28:55.893 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.894 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:55.894 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:55.894 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 13 ms 
[INFO ] 2024-03-27 16:28:55.897 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-bd58ebec-d671-440e-b0a6-ba7e3b54eee6 complete, cost 595ms 
[INFO ] 2024-03-27 16:28:56.569 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:56.570 - [任务 22(100)][b6403b14-b9d0-407b-9046-bf9189d2b3b2] - Node b6403b14-b9d0-407b-9046-bf9189d2b3b2[b6403b14-b9d0-407b-9046-bf9189d2b3b2] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:56.570 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:56.570 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:56.570 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:56.570 - [任务 22(100)][b6403b14-b9d0-407b-9046-bf9189d2b3b2] - Node b6403b14-b9d0-407b-9046-bf9189d2b3b2[b6403b14-b9d0-407b-9046-bf9189d2b3b2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:56.735 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:56.735 - [任务 22(100)][cec4a620-6134-4bc2-b60f-61d25a3ad8a1] - Node cec4a620-6134-4bc2-b60f-61d25a3ad8a1[cec4a620-6134-4bc2-b60f-61d25a3ad8a1] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:56.735 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:56.735 - [任务 22(100)][cec4a620-6134-4bc2-b60f-61d25a3ad8a1] - Node cec4a620-6134-4bc2-b60f-61d25a3ad8a1[cec4a620-6134-4bc2-b60f-61d25a3ad8a1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:56.735 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:56.736 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:56.894 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:56.904 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:56.904 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:56.904 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:56.905 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:56.905 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 11 ms 
[INFO ] 2024-03-27 16:28:57.334 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:57.354 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:57.358 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:57.358 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:57.358 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.358 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:57.364 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d992db55-c945-4d06-a4e9-5a6905e9c74b 
[INFO ] 2024-03-27 16:28:57.365 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 27 ms 
[INFO ] 2024-03-27 16:28:57.365 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d992db55-c945-4d06-a4e9-5a6905e9c74b 
[INFO ] 2024-03-27 16:28:57.365 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.366 - [任务 22(100)][cec4a620-6134-4bc2-b60f-61d25a3ad8a1] - Node cec4a620-6134-4bc2-b60f-61d25a3ad8a1[cec4a620-6134-4bc2-b60f-61d25a3ad8a1] running status set to false 
[INFO ] 2024-03-27 16:28:57.366 - [任务 22(100)][cec4a620-6134-4bc2-b60f-61d25a3ad8a1] - Node cec4a620-6134-4bc2-b60f-61d25a3ad8a1[cec4a620-6134-4bc2-b60f-61d25a3ad8a1] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.366 - [任务 22(100)][cec4a620-6134-4bc2-b60f-61d25a3ad8a1] - Node cec4a620-6134-4bc2-b60f-61d25a3ad8a1[cec4a620-6134-4bc2-b60f-61d25a3ad8a1] monitor closed 
[INFO ] 2024-03-27 16:28:57.366 - [任务 22(100)][cec4a620-6134-4bc2-b60f-61d25a3ad8a1] - Node cec4a620-6134-4bc2-b60f-61d25a3ad8a1[cec4a620-6134-4bc2-b60f-61d25a3ad8a1] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:28:57.366 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.366 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:57.366 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 9 ms 
[INFO ] 2024-03-27 16:28:57.367 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-cec4a620-6134-4bc2-b60f-61d25a3ad8a1 complete, cost 695ms 
[INFO ] 2024-03-27 16:28:57.444 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:57.446 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:57.447 - [任务 22(100)][801e84b3-6696-4272-ab72-438e49b41785] - Node 801e84b3-6696-4272-ab72-438e49b41785[801e84b3-6696-4272-ab72-438e49b41785] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:57.448 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:57.448 - [任务 22(100)][801e84b3-6696-4272-ab72-438e49b41785] - Node 801e84b3-6696-4272-ab72-438e49b41785[801e84b3-6696-4272-ab72-438e49b41785] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:57.448 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:57.617 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:57.619 - [任务 22(100)][b6403b14-b9d0-407b-9046-bf9189d2b3b2] - Node b6403b14-b9d0-407b-9046-bf9189d2b3b2[b6403b14-b9d0-407b-9046-bf9189d2b3b2] running status set to false 
[INFO ] 2024-03-27 16:28:57.619 - [任务 22(100)][b6403b14-b9d0-407b-9046-bf9189d2b3b2] - Node b6403b14-b9d0-407b-9046-bf9189d2b3b2[b6403b14-b9d0-407b-9046-bf9189d2b3b2] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.619 - [任务 22(100)][b6403b14-b9d0-407b-9046-bf9189d2b3b2] - Node b6403b14-b9d0-407b-9046-bf9189d2b3b2[b6403b14-b9d0-407b-9046-bf9189d2b3b2] monitor closed 
[INFO ] 2024-03-27 16:28:57.619 - [任务 22(100)][b6403b14-b9d0-407b-9046-bf9189d2b3b2] - Node b6403b14-b9d0-407b-9046-bf9189d2b3b2[b6403b14-b9d0-407b-9046-bf9189d2b3b2] close complete, cost 6 ms 
[INFO ] 2024-03-27 16:28:57.619 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-2b02c52d-abaf-499b-abe1-01c161307195 
[INFO ] 2024-03-27 16:28:57.619 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-2b02c52d-abaf-499b-abe1-01c161307195 
[INFO ] 2024-03-27 16:28:57.619 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.621 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.622 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:57.622 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 16 ms 
[INFO ] 2024-03-27 16:28:57.623 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-b6403b14-b9d0-407b-9046-bf9189d2b3b2 complete, cost 1132ms 
[INFO ] 2024-03-27 16:28:57.748 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:57.753 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:57.753 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:57.754 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.754 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:57.755 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 6 ms 
[INFO ] 2024-03-27 16:28:57.973 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:57.977 - [任务 22(100)][801e84b3-6696-4272-ab72-438e49b41785] - Node 801e84b3-6696-4272-ab72-438e49b41785[801e84b3-6696-4272-ab72-438e49b41785] running status set to false 
[INFO ] 2024-03-27 16:28:57.978 - [任务 22(100)][801e84b3-6696-4272-ab72-438e49b41785] - Node 801e84b3-6696-4272-ab72-438e49b41785[801e84b3-6696-4272-ab72-438e49b41785] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.978 - [任务 22(100)][801e84b3-6696-4272-ab72-438e49b41785] - Node 801e84b3-6696-4272-ab72-438e49b41785[801e84b3-6696-4272-ab72-438e49b41785] monitor closed 
[INFO ] 2024-03-27 16:28:57.978 - [任务 22(100)][801e84b3-6696-4272-ab72-438e49b41785] - Node 801e84b3-6696-4272-ab72-438e49b41785[801e84b3-6696-4272-ab72-438e49b41785] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:28:57.978 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1b97baa6-4c0d-47e4-9d6a-59fbe7bfc513 
[INFO ] 2024-03-27 16:28:57.978 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-1b97baa6-4c0d-47e4-9d6a-59fbe7bfc513 
[INFO ] 2024-03-27 16:28:57.979 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.979 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:57.979 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:57.979 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 7 ms 
[INFO ] 2024-03-27 16:28:57.981 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-801e84b3-6696-4272-ab72-438e49b41785 complete, cost 611ms 
[INFO ] 2024-03-27 16:28:58.525 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:58.526 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:28:58.527 - [任务 22(100)][e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] - Node e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82[e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:28:58.528 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:58.529 - [任务 22(100)][e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] - Node e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82[e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:58.529 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:28:58.799 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:28:58.813 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:58.813 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:28:58.813 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:28:58.813 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:28:58.813 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 15 ms 
[INFO ] 2024-03-27 16:28:59.021 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:28:59.025 - [任务 22(100)][e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] - Node e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82[e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] running status set to false 
[INFO ] 2024-03-27 16:28:59.027 - [任务 22(100)][e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] - Node e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82[e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] schema data cleaned 
[INFO ] 2024-03-27 16:28:59.027 - [任务 22(100)][e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] - Node e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82[e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] monitor closed 
[INFO ] 2024-03-27 16:28:59.027 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-52dd4df2-ac25-4cc9-ac89-f11102d874ad 
[INFO ] 2024-03-27 16:28:59.028 - [任务 22(100)][e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] - Node e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82[e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82] close complete, cost 6 ms 
[INFO ] 2024-03-27 16:28:59.028 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-52dd4df2-ac25-4cc9-ac89-f11102d874ad 
[INFO ] 2024-03-27 16:28:59.028 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:28:59.030 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:28:59.031 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:28:59.031 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 12 ms 
[INFO ] 2024-03-27 16:28:59.033 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-e7f1b07a-3f0d-4e5d-b094-4cbaffcfaa82 complete, cost 582ms 
[INFO ] 2024-03-27 16:29:00.919 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:00.921 - [任务 22(100)][bc2fc8b8-8579-422a-b0b7-a6410fd99a83] - Node bc2fc8b8-8579-422a-b0b7-a6410fd99a83[bc2fc8b8-8579-422a-b0b7-a6410fd99a83] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:00.921 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:00.921 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 16:29:00.921 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:00.921 - [任务 22(100)][bc2fc8b8-8579-422a-b0b7-a6410fd99a83] - Node bc2fc8b8-8579-422a-b0b7-a6410fd99a83[bc2fc8b8-8579-422a-b0b7-a6410fd99a83] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:01.180 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:01.190 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:01.190 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:01.191 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:01.191 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:01.192 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 12 ms 
[INFO ] 2024-03-27 16:29:01.395 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:01.401 - [任务 22(100)][bc2fc8b8-8579-422a-b0b7-a6410fd99a83] - Node bc2fc8b8-8579-422a-b0b7-a6410fd99a83[bc2fc8b8-8579-422a-b0b7-a6410fd99a83] running status set to false 
[INFO ] 2024-03-27 16:29:01.401 - [任务 22(100)][bc2fc8b8-8579-422a-b0b7-a6410fd99a83] - Node bc2fc8b8-8579-422a-b0b7-a6410fd99a83[bc2fc8b8-8579-422a-b0b7-a6410fd99a83] schema data cleaned 
[INFO ] 2024-03-27 16:29:01.401 - [任务 22(100)][bc2fc8b8-8579-422a-b0b7-a6410fd99a83] - Node bc2fc8b8-8579-422a-b0b7-a6410fd99a83[bc2fc8b8-8579-422a-b0b7-a6410fd99a83] monitor closed 
[INFO ] 2024-03-27 16:29:01.402 - [任务 22(100)][bc2fc8b8-8579-422a-b0b7-a6410fd99a83] - Node bc2fc8b8-8579-422a-b0b7-a6410fd99a83[bc2fc8b8-8579-422a-b0b7-a6410fd99a83] close complete, cost 2 ms 
[INFO ] 2024-03-27 16:29:01.402 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ff1354eb-cfc4-4249-9f49-d0cfd406946c 
[INFO ] 2024-03-27 16:29:01.402 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ff1354eb-cfc4-4249-9f49-d0cfd406946c 
[INFO ] 2024-03-27 16:29:01.403 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:01.404 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:01.404 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:01.404 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 12 ms 
[INFO ] 2024-03-27 16:29:01.407 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-bc2fc8b8-8579-422a-b0b7-a6410fd99a83 complete, cost 561ms 
[INFO ] 2024-03-27 16:29:03.754 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:03.755 - [任务 22(100)][babfeaa2-cc7e-48aa-8e6e-194331443c44] - Node babfeaa2-cc7e-48aa-8e6e-194331443c44[babfeaa2-cc7e-48aa-8e6e-194331443c44] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:03.755 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:03.755 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:03.755 - [任务 22(100)][babfeaa2-cc7e-48aa-8e6e-194331443c44] - Node babfeaa2-cc7e-48aa-8e6e-194331443c44[babfeaa2-cc7e-48aa-8e6e-194331443c44] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:03.755 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:03.863 - [任务 22(100)][01412c30-1ca5-48ab-87be-c79bf5ab3e1b] - Node 01412c30-1ca5-48ab-87be-c79bf5ab3e1b[01412c30-1ca5-48ab-87be-c79bf5ab3e1b] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:03.863 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:03.863 - [任务 22(100)][01412c30-1ca5-48ab-87be-c79bf5ab3e1b] - Node 01412c30-1ca5-48ab-87be-c79bf5ab3e1b[01412c30-1ca5-48ab-87be-c79bf5ab3e1b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:03.865 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:03.865 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:03.865 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:04.025 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:04.033 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:04.033 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:04.033 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.033 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:04.033 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 9 ms 
[INFO ] 2024-03-27 16:29:04.448 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:04.458 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:04.458 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:04.458 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.461 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:04.462 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 10 ms 
[INFO ] 2024-03-27 16:29:04.466 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:04.469 - [任务 22(100)][01412c30-1ca5-48ab-87be-c79bf5ab3e1b] - Node 01412c30-1ca5-48ab-87be-c79bf5ab3e1b[01412c30-1ca5-48ab-87be-c79bf5ab3e1b] running status set to false 
[INFO ] 2024-03-27 16:29:04.469 - [任务 22(100)][01412c30-1ca5-48ab-87be-c79bf5ab3e1b] - Node 01412c30-1ca5-48ab-87be-c79bf5ab3e1b[01412c30-1ca5-48ab-87be-c79bf5ab3e1b] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.469 - [任务 22(100)][01412c30-1ca5-48ab-87be-c79bf5ab3e1b] - Node 01412c30-1ca5-48ab-87be-c79bf5ab3e1b[01412c30-1ca5-48ab-87be-c79bf5ab3e1b] monitor closed 
[INFO ] 2024-03-27 16:29:04.469 - [任务 22(100)][01412c30-1ca5-48ab-87be-c79bf5ab3e1b] - Node 01412c30-1ca5-48ab-87be-c79bf5ab3e1b[01412c30-1ca5-48ab-87be-c79bf5ab3e1b] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:29:04.474 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e0536f90-2d01-4081-9c3a-8d435a9d559a 
[INFO ] 2024-03-27 16:29:04.474 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e0536f90-2d01-4081-9c3a-8d435a9d559a 
[INFO ] 2024-03-27 16:29:04.474 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.476 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.477 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:04.478 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 10 ms 
[INFO ] 2024-03-27 16:29:04.479 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-01412c30-1ca5-48ab-87be-c79bf5ab3e1b complete, cost 656ms 
[INFO ] 2024-03-27 16:29:04.733 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:04.738 - [任务 22(100)][babfeaa2-cc7e-48aa-8e6e-194331443c44] - Node babfeaa2-cc7e-48aa-8e6e-194331443c44[babfeaa2-cc7e-48aa-8e6e-194331443c44] running status set to false 
[INFO ] 2024-03-27 16:29:04.738 - [任务 22(100)][babfeaa2-cc7e-48aa-8e6e-194331443c44] - Node babfeaa2-cc7e-48aa-8e6e-194331443c44[babfeaa2-cc7e-48aa-8e6e-194331443c44] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.738 - [任务 22(100)][babfeaa2-cc7e-48aa-8e6e-194331443c44] - Node babfeaa2-cc7e-48aa-8e6e-194331443c44[babfeaa2-cc7e-48aa-8e6e-194331443c44] monitor closed 
[INFO ] 2024-03-27 16:29:04.738 - [任务 22(100)][babfeaa2-cc7e-48aa-8e6e-194331443c44] - Node babfeaa2-cc7e-48aa-8e6e-194331443c44[babfeaa2-cc7e-48aa-8e6e-194331443c44] close complete, cost 4 ms 
[INFO ] 2024-03-27 16:29:04.738 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d30d2d65-18f4-45dc-bc49-2f0998a6dbc1 
[INFO ] 2024-03-27 16:29:04.738 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d30d2d65-18f4-45dc-bc49-2f0998a6dbc1 
[INFO ] 2024-03-27 16:29:04.739 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.739 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:04.739 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:04.740 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 20 ms 
[INFO ] 2024-03-27 16:29:04.746 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-babfeaa2-cc7e-48aa-8e6e-194331443c44 complete, cost 1043ms 
[INFO ] 2024-03-27 16:29:04.909 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:04.909 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:04.910 - [任务 22(100)][6b81c443-b285-4cdf-bde6-1e5fe271fbf0] - Node 6b81c443-b285-4cdf-bde6-1e5fe271fbf0[6b81c443-b285-4cdf-bde6-1e5fe271fbf0] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:04.910 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:04.910 - [任务 22(100)][6b81c443-b285-4cdf-bde6-1e5fe271fbf0] - Node 6b81c443-b285-4cdf-bde6-1e5fe271fbf0[6b81c443-b285-4cdf-bde6-1e5fe271fbf0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:04.910 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:05.182 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:05.187 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:05.187 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:05.187 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:05.187 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:05.188 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 6 ms 
[INFO ] 2024-03-27 16:29:05.402 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:05.404 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-b38004af-ebdb-4a29-89c8-2f7d384d8a89 
[INFO ] 2024-03-27 16:29:05.405 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-b38004af-ebdb-4a29-89c8-2f7d384d8a89 
[INFO ] 2024-03-27 16:29:05.405 - [任务 22(100)][6b81c443-b285-4cdf-bde6-1e5fe271fbf0] - Node 6b81c443-b285-4cdf-bde6-1e5fe271fbf0[6b81c443-b285-4cdf-bde6-1e5fe271fbf0] running status set to false 
[INFO ] 2024-03-27 16:29:05.406 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:05.406 - [任务 22(100)][6b81c443-b285-4cdf-bde6-1e5fe271fbf0] - Node 6b81c443-b285-4cdf-bde6-1e5fe271fbf0[6b81c443-b285-4cdf-bde6-1e5fe271fbf0] schema data cleaned 
[INFO ] 2024-03-27 16:29:05.406 - [任务 22(100)][6b81c443-b285-4cdf-bde6-1e5fe271fbf0] - Node 6b81c443-b285-4cdf-bde6-1e5fe271fbf0[6b81c443-b285-4cdf-bde6-1e5fe271fbf0] monitor closed 
[INFO ] 2024-03-27 16:29:05.406 - [任务 22(100)][6b81c443-b285-4cdf-bde6-1e5fe271fbf0] - Node 6b81c443-b285-4cdf-bde6-1e5fe271fbf0[6b81c443-b285-4cdf-bde6-1e5fe271fbf0] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:29:05.407 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:05.407 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:05.407 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 6 ms 
[INFO ] 2024-03-27 16:29:05.408 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-6b81c443-b285-4cdf-bde6-1e5fe271fbf0 complete, cost 571ms 
[INFO ] 2024-03-27 16:29:41.593 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:41.594 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:41.594 - [任务 22(100)][cac00f0b-cd59-4f41-b617-b36eda850d65] - Node cac00f0b-cd59-4f41-b617-b36eda850d65[cac00f0b-cd59-4f41-b617-b36eda850d65] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:41.594 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:41.594 - [任务 22(100)][cac00f0b-cd59-4f41-b617-b36eda850d65] - Node cac00f0b-cd59-4f41-b617-b36eda850d65[cac00f0b-cd59-4f41-b617-b36eda850d65] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:41.594 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:41.855 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:41.864 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:41.864 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:41.864 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:41.864 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:41.864 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 12 ms 
[INFO ] 2024-03-27 16:29:42.074 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:42.077 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-9e8a7a25-1c42-4a86-8704-7a9d84a17332 
[INFO ] 2024-03-27 16:29:42.077 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-9e8a7a25-1c42-4a86-8704-7a9d84a17332 
[INFO ] 2024-03-27 16:29:42.077 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:42.080 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:42.080 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:42.081 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 7 ms 
[INFO ] 2024-03-27 16:29:42.097 - [任务 22(100)][cac00f0b-cd59-4f41-b617-b36eda850d65] - Node cac00f0b-cd59-4f41-b617-b36eda850d65[cac00f0b-cd59-4f41-b617-b36eda850d65] running status set to false 
[INFO ] 2024-03-27 16:29:42.098 - [任务 22(100)][cac00f0b-cd59-4f41-b617-b36eda850d65] - Node cac00f0b-cd59-4f41-b617-b36eda850d65[cac00f0b-cd59-4f41-b617-b36eda850d65] schema data cleaned 
[INFO ] 2024-03-27 16:29:42.098 - [任务 22(100)][cac00f0b-cd59-4f41-b617-b36eda850d65] - Node cac00f0b-cd59-4f41-b617-b36eda850d65[cac00f0b-cd59-4f41-b617-b36eda850d65] monitor closed 
[INFO ] 2024-03-27 16:29:42.098 - [任务 22(100)][cac00f0b-cd59-4f41-b617-b36eda850d65] - Node cac00f0b-cd59-4f41-b617-b36eda850d65[cac00f0b-cd59-4f41-b617-b36eda850d65] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:29:42.101 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-cac00f0b-cd59-4f41-b617-b36eda850d65 complete, cost 592ms 
[INFO ] 2024-03-27 16:29:53.737 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:53.740 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:53.741 - [任务 22(100)][63de62f8-d6d4-40c7-a4a7-b7c935ce366e] - Node 63de62f8-d6d4-40c7-a4a7-b7c935ce366e[63de62f8-d6d4-40c7-a4a7-b7c935ce366e] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:53.741 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:53.741 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 16:29:53.741 - [任务 22(100)][63de62f8-d6d4-40c7-a4a7-b7c935ce366e] - Node 63de62f8-d6d4-40c7-a4a7-b7c935ce366e[63de62f8-d6d4-40c7-a4a7-b7c935ce366e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:54.003 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:54.011 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:54.011 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:54.011 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.011 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:54.011 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 9 ms 
[INFO ] 2024-03-27 16:29:54.167 - [任务 22(100)][22409dc5-5b64-486e-8b5e-3b33182c070b] - Node 22409dc5-5b64-486e-8b5e-3b33182c070b[22409dc5-5b64-486e-8b5e-3b33182c070b] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:54.167 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:54.167 - [任务 22(100)][22409dc5-5b64-486e-8b5e-3b33182c070b] - Node 22409dc5-5b64-486e-8b5e-3b33182c070b[22409dc5-5b64-486e-8b5e-3b33182c070b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:54.167 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:54.167 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:54.167 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:54.249 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:54.254 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-684aa0ea-f5dd-49ae-8815-aacd7fcea53c 
[INFO ] 2024-03-27 16:29:54.255 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-684aa0ea-f5dd-49ae-8815-aacd7fcea53c 
[INFO ] 2024-03-27 16:29:54.255 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.256 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.256 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:54.256 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 11 ms 
[INFO ] 2024-03-27 16:29:54.269 - [任务 22(100)][63de62f8-d6d4-40c7-a4a7-b7c935ce366e] - Node 63de62f8-d6d4-40c7-a4a7-b7c935ce366e[63de62f8-d6d4-40c7-a4a7-b7c935ce366e] running status set to false 
[INFO ] 2024-03-27 16:29:54.269 - [任务 22(100)][63de62f8-d6d4-40c7-a4a7-b7c935ce366e] - Node 63de62f8-d6d4-40c7-a4a7-b7c935ce366e[63de62f8-d6d4-40c7-a4a7-b7c935ce366e] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.270 - [任务 22(100)][63de62f8-d6d4-40c7-a4a7-b7c935ce366e] - Node 63de62f8-d6d4-40c7-a4a7-b7c935ce366e[63de62f8-d6d4-40c7-a4a7-b7c935ce366e] monitor closed 
[INFO ] 2024-03-27 16:29:54.270 - [任务 22(100)][63de62f8-d6d4-40c7-a4a7-b7c935ce366e] - Node 63de62f8-d6d4-40c7-a4a7-b7c935ce366e[63de62f8-d6d4-40c7-a4a7-b7c935ce366e] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:29:54.272 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-63de62f8-d6d4-40c7-a4a7-b7c935ce366e complete, cost 610ms 
[INFO ] 2024-03-27 16:29:54.473 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:54.484 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:54.484 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:54.484 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.484 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:54.484 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 24 ms 
[INFO ] 2024-03-27 16:29:54.684 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:54.686 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-b4b7b22f-a4bf-48ad-b571-b81b320558a5 
[INFO ] 2024-03-27 16:29:54.687 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-b4b7b22f-a4bf-48ad-b571-b81b320558a5 
[INFO ] 2024-03-27 16:29:54.687 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.690 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.690 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:54.690 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 6 ms 
[INFO ] 2024-03-27 16:29:54.714 - [任务 22(100)][22409dc5-5b64-486e-8b5e-3b33182c070b] - Node 22409dc5-5b64-486e-8b5e-3b33182c070b[22409dc5-5b64-486e-8b5e-3b33182c070b] running status set to false 
[INFO ] 2024-03-27 16:29:54.714 - [任务 22(100)][22409dc5-5b64-486e-8b5e-3b33182c070b] - Node 22409dc5-5b64-486e-8b5e-3b33182c070b[22409dc5-5b64-486e-8b5e-3b33182c070b] schema data cleaned 
[INFO ] 2024-03-27 16:29:54.714 - [任务 22(100)][22409dc5-5b64-486e-8b5e-3b33182c070b] - Node 22409dc5-5b64-486e-8b5e-3b33182c070b[22409dc5-5b64-486e-8b5e-3b33182c070b] monitor closed 
[INFO ] 2024-03-27 16:29:54.714 - [任务 22(100)][22409dc5-5b64-486e-8b5e-3b33182c070b] - Node 22409dc5-5b64-486e-8b5e-3b33182c070b[22409dc5-5b64-486e-8b5e-3b33182c070b] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:29:54.717 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-22409dc5-5b64-486e-8b5e-3b33182c070b complete, cost 579ms 
[INFO ] 2024-03-27 16:29:56.599 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:56.599 - [任务 22(100)][0b3ee29a-4707-4eee-924d-be02b7047c50] - Node 0b3ee29a-4707-4eee-924d-be02b7047c50[0b3ee29a-4707-4eee-924d-be02b7047c50] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:29:56.599 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:29:56.599 - [任务 22(100)][0b3ee29a-4707-4eee-924d-be02b7047c50] - Node 0b3ee29a-4707-4eee-924d-be02b7047c50[0b3ee29a-4707-4eee-924d-be02b7047c50] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:56.600 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 16:29:56.601 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:29:56.883 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:29:56.894 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:56.894 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:29:56.895 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:29:56.895 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:29:56.895 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 14 ms 
[INFO ] 2024-03-27 16:29:57.126 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:29:57.134 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5bb94aaf-f675-43ef-848d-4dd61f3e0ad7 
[INFO ] 2024-03-27 16:29:57.134 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-5bb94aaf-f675-43ef-848d-4dd61f3e0ad7 
[INFO ] 2024-03-27 16:29:57.134 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:29:57.135 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:29:57.135 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:29:57.136 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 12 ms 
[INFO ] 2024-03-27 16:29:57.157 - [任务 22(100)][0b3ee29a-4707-4eee-924d-be02b7047c50] - Node 0b3ee29a-4707-4eee-924d-be02b7047c50[0b3ee29a-4707-4eee-924d-be02b7047c50] running status set to false 
[INFO ] 2024-03-27 16:29:57.158 - [任务 22(100)][0b3ee29a-4707-4eee-924d-be02b7047c50] - Node 0b3ee29a-4707-4eee-924d-be02b7047c50[0b3ee29a-4707-4eee-924d-be02b7047c50] schema data cleaned 
[INFO ] 2024-03-27 16:29:57.158 - [任务 22(100)][0b3ee29a-4707-4eee-924d-be02b7047c50] - Node 0b3ee29a-4707-4eee-924d-be02b7047c50[0b3ee29a-4707-4eee-924d-be02b7047c50] monitor closed 
[INFO ] 2024-03-27 16:29:57.159 - [任务 22(100)][0b3ee29a-4707-4eee-924d-be02b7047c50] - Node 0b3ee29a-4707-4eee-924d-be02b7047c50[0b3ee29a-4707-4eee-924d-be02b7047c50] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:29:57.160 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-0b3ee29a-4707-4eee-924d-be02b7047c50 complete, cost 619ms 
[INFO ] 2024-03-27 16:30:04.890 - [任务 22(100)][ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] - Node ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4[ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:30:04.893 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:30:04.894 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:30:04.895 - [任务 22(100)][ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] - Node ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4[ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:30:04.895 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:30:04.895 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:30:05.212 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:30:05.221 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:30:05.221 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:30:05.221 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:30:05.221 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:30:05.221 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 9 ms 
[INFO ] 2024-03-27 16:30:05.432 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:30:05.438 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-66d88fcc-1b50-4177-8d16-3f269633e09c 
[INFO ] 2024-03-27 16:30:05.439 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-66d88fcc-1b50-4177-8d16-3f269633e09c 
[INFO ] 2024-03-27 16:30:05.439 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:30:05.442 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:30:05.442 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:30:05.442 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 11 ms 
[INFO ] 2024-03-27 16:30:05.459 - [任务 22(100)][ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] - Node ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4[ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] running status set to false 
[INFO ] 2024-03-27 16:30:05.460 - [任务 22(100)][ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] - Node ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4[ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] schema data cleaned 
[INFO ] 2024-03-27 16:30:05.460 - [任务 22(100)][ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] - Node ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4[ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] monitor closed 
[INFO ] 2024-03-27 16:30:05.460 - [任务 22(100)][ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] - Node ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4[ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:30:05.463 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-ac78a89c-b0c8-4e5d-b0f1-d27089fa62c4 complete, cost 672ms 
[INFO ] 2024-03-27 16:48:39.722 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:48:39.724 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:48:39.727 - [任务 22(100)][fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] - Node fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8[fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:48:39.729 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:39.730 - [任务 22(100)][fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] - Node fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8[fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:39.731 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:40.009 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:48:40.019 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:48:40.020 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:48:40.020 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:48:40.020 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:48:40.020 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 12 ms 
[INFO ] 2024-03-27 16:48:40.265 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:48:40.280 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-dd3e240b-0e15-439c-b34e-04b2bb670f7a 
[INFO ] 2024-03-27 16:48:40.280 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-dd3e240b-0e15-439c-b34e-04b2bb670f7a 
[INFO ] 2024-03-27 16:48:40.281 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:48:40.282 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:48:40.282 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:48:40.282 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 20 ms 
[INFO ] 2024-03-27 16:48:40.335 - [任务 22(100)][fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] - Node fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8[fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] running status set to false 
[INFO ] 2024-03-27 16:48:40.338 - [任务 22(100)][fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] - Node fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8[fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] schema data cleaned 
[INFO ] 2024-03-27 16:48:40.338 - [任务 22(100)][fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] - Node fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8[fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] monitor closed 
[INFO ] 2024-03-27 16:48:40.342 - [任务 22(100)][fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] - Node fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8[fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8] close complete, cost 10 ms 
[INFO ] 2024-03-27 16:48:40.350 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-fbf43d93-91c3-4fab-ab64-0ecaf6ac7fc8 complete, cost 729ms 
[INFO ] 2024-03-27 16:48:42.052 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:48:42.053 - [任务 22(100)][1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] - Node 1b2017f3-f84b-49e6-ba4f-bcec3c2f734b[1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:48:42.053 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:48:42.053 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:42.053 - [任务 22(100)][1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] - Node 1b2017f3-f84b-49e6-ba4f-bcec3c2f734b[1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:42.053 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:42.335 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:48:42.347 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:48:42.347 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:48:42.347 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:48:42.347 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:48:42.347 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 13 ms 
[INFO ] 2024-03-27 16:48:42.591 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:48:42.606 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a802256c-de34-4a48-9d3b-bdd2b7226aa7 
[INFO ] 2024-03-27 16:48:42.611 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a802256c-de34-4a48-9d3b-bdd2b7226aa7 
[INFO ] 2024-03-27 16:48:42.611 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:48:42.611 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:48:42.611 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:48:42.612 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 21 ms 
[INFO ] 2024-03-27 16:48:42.640 - [任务 22(100)][1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] - Node 1b2017f3-f84b-49e6-ba4f-bcec3c2f734b[1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] running status set to false 
[INFO ] 2024-03-27 16:48:42.641 - [任务 22(100)][1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] - Node 1b2017f3-f84b-49e6-ba4f-bcec3c2f734b[1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] schema data cleaned 
[INFO ] 2024-03-27 16:48:42.641 - [任务 22(100)][1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] - Node 1b2017f3-f84b-49e6-ba4f-bcec3c2f734b[1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] monitor closed 
[INFO ] 2024-03-27 16:48:42.642 - [任务 22(100)][1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] - Node 1b2017f3-f84b-49e6-ba4f-bcec3c2f734b[1b2017f3-f84b-49e6-ba4f-bcec3c2f734b] close complete, cost 4 ms 
[INFO ] 2024-03-27 16:48:42.649 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-1b2017f3-f84b-49e6-ba4f-bcec3c2f734b complete, cost 648ms 
[INFO ] 2024-03-27 16:48:42.757 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:48:42.759 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:48:42.760 - [任务 22(100)][8f5a5751-f69d-402c-9f6b-56c1170c6b01] - Node 8f5a5751-f69d-402c-9f6b-56c1170c6b01[8f5a5751-f69d-402c-9f6b-56c1170c6b01] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:48:42.760 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:42.760 - [任务 22(100)][8f5a5751-f69d-402c-9f6b-56c1170c6b01] - Node 8f5a5751-f69d-402c-9f6b-56c1170c6b01[8f5a5751-f69d-402c-9f6b-56c1170c6b01] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:42.760 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:48:43.048 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] running status set to false 
[INFO ] 2024-03-27 16:48:43.057 - [任务 22(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:48:43.057 - [任务 22(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd3b9b78-cbc2-4518-9fb7-9297384b2944 
[INFO ] 2024-03-27 16:48:43.057 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] schema data cleaned 
[INFO ] 2024-03-27 16:48:43.057 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] monitor closed 
[INFO ] 2024-03-27 16:48:43.058 - [任务 22(100)][test9] - Node test9[cd3b9b78-cbc2-4518-9fb7-9297384b2944] close complete, cost 15 ms 
[INFO ] 2024-03-27 16:48:43.293 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] running status set to false 
[INFO ] 2024-03-27 16:48:43.304 - [任务 22(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-49edf13e-0436-4c24-a510-763025704977 
[INFO ] 2024-03-27 16:48:43.305 - [任务 22(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-49edf13e-0436-4c24-a510-763025704977 
[INFO ] 2024-03-27 16:48:43.305 - [任务 22(100)][增强JS] - [ScriptExecutorsManager-6603bce38b5bca60f72df29f-575b12c9-7612-4ef2-952c-3632dc7fa09c-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:48:43.305 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] schema data cleaned 
[INFO ] 2024-03-27 16:48:43.305 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] monitor closed 
[INFO ] 2024-03-27 16:48:43.305 - [任务 22(100)][增强JS] - Node 增强JS[575b12c9-7612-4ef2-952c-3632dc7fa09c] close complete, cost 18 ms 
[INFO ] 2024-03-27 16:48:43.320 - [任务 22(100)][8f5a5751-f69d-402c-9f6b-56c1170c6b01] - Node 8f5a5751-f69d-402c-9f6b-56c1170c6b01[8f5a5751-f69d-402c-9f6b-56c1170c6b01] running status set to false 
[INFO ] 2024-03-27 16:48:43.320 - [任务 22(100)][8f5a5751-f69d-402c-9f6b-56c1170c6b01] - Node 8f5a5751-f69d-402c-9f6b-56c1170c6b01[8f5a5751-f69d-402c-9f6b-56c1170c6b01] schema data cleaned 
[INFO ] 2024-03-27 16:48:43.321 - [任务 22(100)][8f5a5751-f69d-402c-9f6b-56c1170c6b01] - Node 8f5a5751-f69d-402c-9f6b-56c1170c6b01[8f5a5751-f69d-402c-9f6b-56c1170c6b01] monitor closed 
[INFO ] 2024-03-27 16:48:43.321 - [任务 22(100)][8f5a5751-f69d-402c-9f6b-56c1170c6b01] - Node 8f5a5751-f69d-402c-9f6b-56c1170c6b01[8f5a5751-f69d-402c-9f6b-56c1170c6b01] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:48:43.325 - [任务 22(100)] - load tapTable task 6603bce38b5bca60f72df29f-8f5a5751-f69d-402c-9f6b-56c1170c6b01 complete, cost 644ms 
