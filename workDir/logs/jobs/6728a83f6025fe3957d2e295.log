[INFO ] 2024-11-05 10:52:49.703 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 10:52:49.838 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:52:49.838 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:52:49.840 - [测试共享缓存(101)][f5c83389-8b31-4108-92fd-d8fe9d30ee40] - Node f5c83389-8b31-4108-92fd-d8fe9d30ee40[f5c83389-8b31-4108-92fd-d8fe9d30ee40] start preload schema,table counts: 0 
[INFO ] 2024-11-05 10:52:49.840 - [测试共享缓存(101)][f5c83389-8b31-4108-92fd-d8fe9d30ee40] - Node f5c83389-8b31-4108-92fd-d8fe9d30ee40[f5c83389-8b31-4108-92fd-d8fe9d30ee40] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 10:52:49.950 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 108 ms 
[INFO ] 2024-11-05 10:52:49.951 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 103 ms 
[INFO ] 2024-11-05 10:52:49.951 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 10:52:50.282 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 10:52:50.282 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:52:50.282 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:52:50.285 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 10:52:50.288 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 10:52:50.289 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 40 ms 
[INFO ] 2024-11-05 10:52:50.801 - [测试共享缓存(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: defaultValue is not defined 
[ERROR] 2024-11-05 10:52:51.007 - [测试共享缓存(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: defaultValue is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: defaultValue is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: defaultValue is not defined
	<js>.process(<eval>:2)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: defaultValue is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: defaultValue is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: defaultValue is not defined
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-11-05 10:52:53.424 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 10:52:53.438 - [测试共享缓存(101)][f5c83389-8b31-4108-92fd-d8fe9d30ee40] - Node f5c83389-8b31-4108-92fd-d8fe9d30ee40[f5c83389-8b31-4108-92fd-d8fe9d30ee40] running status set to false 
[INFO ] 2024-11-05 10:52:53.440 - [测试共享缓存(101)][f5c83389-8b31-4108-92fd-d8fe9d30ee40] - Node f5c83389-8b31-4108-92fd-d8fe9d30ee40[f5c83389-8b31-4108-92fd-d8fe9d30ee40] schema data cleaned 
[INFO ] 2024-11-05 10:52:53.451 - [测试共享缓存(101)][f5c83389-8b31-4108-92fd-d8fe9d30ee40] - Node f5c83389-8b31-4108-92fd-d8fe9d30ee40[f5c83389-8b31-4108-92fd-d8fe9d30ee40] monitor closed 
[INFO ] 2024-11-05 10:52:53.470 - [测试共享缓存(101)][f5c83389-8b31-4108-92fd-d8fe9d30ee40] - Node f5c83389-8b31-4108-92fd-d8fe9d30ee40[f5c83389-8b31-4108-92fd-d8fe9d30ee40] close complete, cost 99 ms 
[INFO ] 2024-11-05 10:52:53.470 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-a80ed9cb-f765-4053-8139-46cbfc471f97 
[INFO ] 2024-11-05 10:52:53.470 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-a80ed9cb-f765-4053-8139-46cbfc471f97 
[INFO ] 2024-11-05 10:52:53.470 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 10:52:53.475 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 10:52:53.476 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 10:52:53.480 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 134 ms 
[INFO ] 2024-11-05 10:52:53.504 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 10:52:53.504 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 10:52:53.504 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 10:52:53.709 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 3834ms 
[INFO ] 2024-11-05 10:53:09.112 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 10:53:09.187 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:53:09.189 - [测试共享缓存(101)][94c80e19-5dbf-40ea-87cb-dad1cb284788] - Node 94c80e19-5dbf-40ea-87cb-dad1cb284788[94c80e19-5dbf-40ea-87cb-dad1cb284788] start preload schema,table counts: 0 
[INFO ] 2024-11-05 10:53:09.189 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:53:09.189 - [测试共享缓存(101)][94c80e19-5dbf-40ea-87cb-dad1cb284788] - Node 94c80e19-5dbf-40ea-87cb-dad1cb284788[94c80e19-5dbf-40ea-87cb-dad1cb284788] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 10:53:09.204 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 15 ms 
[INFO ] 2024-11-05 10:53:09.204 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 16 ms 
[INFO ] 2024-11-05 10:53:09.254 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 10:53:09.512 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 10:53:09.549 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:53:09.549 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:53:09.555 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 10:53:09.557 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 10:53:09.696 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 49 ms 
[INFO ] 2024-11-05 10:53:09.937 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 10:53:09.950 - [测试共享缓存(101)][94c80e19-5dbf-40ea-87cb-dad1cb284788] - Node 94c80e19-5dbf-40ea-87cb-dad1cb284788[94c80e19-5dbf-40ea-87cb-dad1cb284788] running status set to false 
[INFO ] 2024-11-05 10:53:09.960 - [测试共享缓存(101)][94c80e19-5dbf-40ea-87cb-dad1cb284788] - Node 94c80e19-5dbf-40ea-87cb-dad1cb284788[94c80e19-5dbf-40ea-87cb-dad1cb284788] schema data cleaned 
[INFO ] 2024-11-05 10:53:09.961 - [测试共享缓存(101)][94c80e19-5dbf-40ea-87cb-dad1cb284788] - Node 94c80e19-5dbf-40ea-87cb-dad1cb284788[94c80e19-5dbf-40ea-87cb-dad1cb284788] monitor closed 
[INFO ] 2024-11-05 10:53:09.961 - [测试共享缓存(101)][94c80e19-5dbf-40ea-87cb-dad1cb284788] - Node 94c80e19-5dbf-40ea-87cb-dad1cb284788[94c80e19-5dbf-40ea-87cb-dad1cb284788] close complete, cost 19 ms 
[INFO ] 2024-11-05 10:53:09.961 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-249da6c4-aada-4bfa-a595-78971e8c11ac 
[INFO ] 2024-11-05 10:53:09.961 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-249da6c4-aada-4bfa-a595-78971e8c11ac 
[INFO ] 2024-11-05 10:53:09.964 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 10:53:09.964 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 10:53:09.965 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 10:53:09.969 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 34 ms 
[INFO ] 2024-11-05 10:53:09.969 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 10:53:09.969 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 10:53:09.969 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 10:53:10.028 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 880ms 
[INFO ] 2024-11-05 10:53:42.559 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 10:53:42.618 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:53:42.618 - [测试共享缓存(101)][ac7983fc-437d-4431-b366-f004d08e8ed0] - Node ac7983fc-437d-4431-b366-f004d08e8ed0[ac7983fc-437d-4431-b366-f004d08e8ed0] start preload schema,table counts: 0 
[INFO ] 2024-11-05 10:53:42.618 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:53:42.619 - [测试共享缓存(101)][ac7983fc-437d-4431-b366-f004d08e8ed0] - Node ac7983fc-437d-4431-b366-f004d08e8ed0[ac7983fc-437d-4431-b366-f004d08e8ed0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 10:53:42.639 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 14 ms 
[INFO ] 2024-11-05 10:53:42.642 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 11 ms 
[INFO ] 2024-11-05 10:53:42.643 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 10:53:42.920 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 10:53:42.949 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:53:42.949 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:53:42.950 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 10:53:42.950 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 10:53:42.951 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 41 ms 
[INFO ] 2024-11-05 10:53:43.269 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 10:53:43.272 - [测试共享缓存(101)][ac7983fc-437d-4431-b366-f004d08e8ed0] - Node ac7983fc-437d-4431-b366-f004d08e8ed0[ac7983fc-437d-4431-b366-f004d08e8ed0] running status set to false 
[INFO ] 2024-11-05 10:53:43.273 - [测试共享缓存(101)][ac7983fc-437d-4431-b366-f004d08e8ed0] - Node ac7983fc-437d-4431-b366-f004d08e8ed0[ac7983fc-437d-4431-b366-f004d08e8ed0] schema data cleaned 
[INFO ] 2024-11-05 10:53:43.273 - [测试共享缓存(101)][ac7983fc-437d-4431-b366-f004d08e8ed0] - Node ac7983fc-437d-4431-b366-f004d08e8ed0[ac7983fc-437d-4431-b366-f004d08e8ed0] monitor closed 
[INFO ] 2024-11-05 10:53:43.273 - [测试共享缓存(101)][ac7983fc-437d-4431-b366-f004d08e8ed0] - Node ac7983fc-437d-4431-b366-f004d08e8ed0[ac7983fc-437d-4431-b366-f004d08e8ed0] close complete, cost 5 ms 
[INFO ] 2024-11-05 10:53:43.276 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-272bd5b8-4e2c-44ca-9b93-85ad7ca884e8 
[INFO ] 2024-11-05 10:53:43.278 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-272bd5b8-4e2c-44ca-9b93-85ad7ca884e8 
[INFO ] 2024-11-05 10:53:43.278 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 10:53:43.281 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 10:53:43.281 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 10:53:43.296 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 35 ms 
[INFO ] 2024-11-05 10:53:43.296 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 10:53:43.296 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 10:53:43.296 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 10:53:43.511 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 754ms 
[INFO ] 2024-11-05 10:53:56.354 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 10:53:56.412 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:53:56.413 - [测试共享缓存(101)][0925dd27-f1c2-4b64-a51c-a0c6dc618810] - Node 0925dd27-f1c2-4b64-a51c-a0c6dc618810[0925dd27-f1c2-4b64-a51c-a0c6dc618810] start preload schema,table counts: 0 
[INFO ] 2024-11-05 10:53:56.413 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:53:56.413 - [测试共享缓存(101)][0925dd27-f1c2-4b64-a51c-a0c6dc618810] - Node 0925dd27-f1c2-4b64-a51c-a0c6dc618810[0925dd27-f1c2-4b64-a51c-a0c6dc618810] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 10:53:56.433 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 20 ms 
[INFO ] 2024-11-05 10:53:56.433 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 20 ms 
[INFO ] 2024-11-05 10:53:56.433 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 10:53:56.859 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 10:53:56.859 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:53:56.859 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:53:56.859 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 10:53:56.859 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 10:53:56.860 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 17 ms 
[INFO ] 2024-11-05 10:53:57.168 - [测试共享缓存(101)][增强JS] - childName 
[INFO ] 2024-11-05 10:53:57.171 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 10:53:57.182 - [测试共享缓存(101)][0925dd27-f1c2-4b64-a51c-a0c6dc618810] - Node 0925dd27-f1c2-4b64-a51c-a0c6dc618810[0925dd27-f1c2-4b64-a51c-a0c6dc618810] running status set to false 
[INFO ] 2024-11-05 10:53:57.184 - [测试共享缓存(101)][0925dd27-f1c2-4b64-a51c-a0c6dc618810] - Node 0925dd27-f1c2-4b64-a51c-a0c6dc618810[0925dd27-f1c2-4b64-a51c-a0c6dc618810] schema data cleaned 
[INFO ] 2024-11-05 10:53:57.184 - [测试共享缓存(101)][0925dd27-f1c2-4b64-a51c-a0c6dc618810] - Node 0925dd27-f1c2-4b64-a51c-a0c6dc618810[0925dd27-f1c2-4b64-a51c-a0c6dc618810] monitor closed 
[INFO ] 2024-11-05 10:53:57.184 - [测试共享缓存(101)][0925dd27-f1c2-4b64-a51c-a0c6dc618810] - Node 0925dd27-f1c2-4b64-a51c-a0c6dc618810[0925dd27-f1c2-4b64-a51c-a0c6dc618810] close complete, cost 3 ms 
[INFO ] 2024-11-05 10:53:57.187 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-0d9e234a-d075-46b3-8aa9-ead5aeb34d20 
[INFO ] 2024-11-05 10:53:57.187 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-0d9e234a-d075-46b3-8aa9-ead5aeb34d20 
[INFO ] 2024-11-05 10:53:57.187 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 10:53:57.189 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 10:53:57.189 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 10:53:57.192 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 18 ms 
[INFO ] 2024-11-05 10:53:57.192 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 10:53:57.192 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 10:53:57.192 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 10:53:57.193 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 860ms 
[INFO ] 2024-11-05 10:55:43.966 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 10:55:43.967 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:55:43.967 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 10:55:43.967 - [测试共享缓存(101)][1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] - Node 1e64fcef-ff0e-4be6-8b9c-3b26f3d12938[1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] start preload schema,table counts: 0 
[INFO ] 2024-11-05 10:55:43.967 - [测试共享缓存(101)][1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] - Node 1e64fcef-ff0e-4be6-8b9c-3b26f3d12938[1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 10:55:44.001 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 24 ms 
[INFO ] 2024-11-05 10:55:44.001 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 25 ms 
[INFO ] 2024-11-05 10:55:44.001 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 10:55:44.290 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 10:55:44.317 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:55:44.317 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 10:55:44.318 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 10:55:44.318 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 10:55:44.503 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 29 ms 
[INFO ] 2024-11-05 10:55:44.671 - [测试共享缓存(101)][增强JS] - {name=childName1, id=1} 
[INFO ] 2024-11-05 10:55:44.688 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 10:55:44.688 - [测试共享缓存(101)][1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] - Node 1e64fcef-ff0e-4be6-8b9c-3b26f3d12938[1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] running status set to false 
[INFO ] 2024-11-05 10:55:44.688 - [测试共享缓存(101)][1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] - Node 1e64fcef-ff0e-4be6-8b9c-3b26f3d12938[1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] schema data cleaned 
[INFO ] 2024-11-05 10:55:44.688 - [测试共享缓存(101)][1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] - Node 1e64fcef-ff0e-4be6-8b9c-3b26f3d12938[1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] monitor closed 
[INFO ] 2024-11-05 10:55:44.692 - [测试共享缓存(101)][1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] - Node 1e64fcef-ff0e-4be6-8b9c-3b26f3d12938[1e64fcef-ff0e-4be6-8b9c-3b26f3d12938] close complete, cost 8 ms 
[INFO ] 2024-11-05 10:55:44.692 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-aaf1098a-0d49-4d06-b094-353f896e4a82 
[INFO ] 2024-11-05 10:55:44.692 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-aaf1098a-0d49-4d06-b094-353f896e4a82 
[INFO ] 2024-11-05 10:55:44.692 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 10:55:44.694 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 10:55:44.694 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 10:55:44.695 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 21 ms 
[INFO ] 2024-11-05 10:55:44.703 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 10:55:44.703 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 10:55:44.703 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 10:55:44.703 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 822ms 
[INFO ] 2024-11-05 11:03:00.377 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 11:03:00.378 - [测试共享缓存(101)][f712db9c-b059-4f84-ab2d-1c0e40e8fec9] - Node f712db9c-b059-4f84-ab2d-1c0e40e8fec9[f712db9c-b059-4f84-ab2d-1c0e40e8fec9] start preload schema,table counts: 0 
[INFO ] 2024-11-05 11:03:00.378 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:03:00.378 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:03:00.378 - [测试共享缓存(101)][f712db9c-b059-4f84-ab2d-1c0e40e8fec9] - Node f712db9c-b059-4f84-ab2d-1c0e40e8fec9[f712db9c-b059-4f84-ab2d-1c0e40e8fec9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 11:03:00.394 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 17 ms 
[INFO ] 2024-11-05 11:03:00.394 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 17 ms 
[INFO ] 2024-11-05 11:03:00.395 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 11:03:00.644 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 11:03:00.664 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:03:00.664 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:03:00.664 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 11:03:00.667 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 11:03:00.816 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 24 ms 
[INFO ] 2024-11-05 11:03:01.031 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 11:03:01.044 - [测试共享缓存(101)][f712db9c-b059-4f84-ab2d-1c0e40e8fec9] - Node f712db9c-b059-4f84-ab2d-1c0e40e8fec9[f712db9c-b059-4f84-ab2d-1c0e40e8fec9] running status set to false 
[INFO ] 2024-11-05 11:03:01.044 - [测试共享缓存(101)][f712db9c-b059-4f84-ab2d-1c0e40e8fec9] - Node f712db9c-b059-4f84-ab2d-1c0e40e8fec9[f712db9c-b059-4f84-ab2d-1c0e40e8fec9] schema data cleaned 
[INFO ] 2024-11-05 11:03:01.044 - [测试共享缓存(101)][f712db9c-b059-4f84-ab2d-1c0e40e8fec9] - Node f712db9c-b059-4f84-ab2d-1c0e40e8fec9[f712db9c-b059-4f84-ab2d-1c0e40e8fec9] monitor closed 
[INFO ] 2024-11-05 11:03:01.059 - [测试共享缓存(101)][f712db9c-b059-4f84-ab2d-1c0e40e8fec9] - Node f712db9c-b059-4f84-ab2d-1c0e40e8fec9[f712db9c-b059-4f84-ab2d-1c0e40e8fec9] close complete, cost 10 ms 
[INFO ] 2024-11-05 11:03:01.063 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-9fc8ef47-d06e-4f08-9b32-a07152e53863 
[INFO ] 2024-11-05 11:03:01.065 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-9fc8ef47-d06e-4f08-9b32-a07152e53863 
[INFO ] 2024-11-05 11:03:01.065 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 11:03:01.069 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 11:03:01.070 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 11:03:01.070 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 51 ms 
[INFO ] 2024-11-05 11:03:01.080 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 11:03:01.081 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 11:03:01.081 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 11:03:01.081 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 801ms 
[INFO ] 2024-11-05 11:03:14.789 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 11:03:14.789 - [测试共享缓存(101)][6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] - Node 6b95e0a9-b3b3-406a-bbf5-54a7eb090cce[6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] start preload schema,table counts: 0 
[INFO ] 2024-11-05 11:03:14.789 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:03:14.789 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:03:14.790 - [测试共享缓存(101)][6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] - Node 6b95e0a9-b3b3-406a-bbf5-54a7eb090cce[6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 11:03:14.810 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 15 ms 
[INFO ] 2024-11-05 11:03:14.813 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 11:03:14.813 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 24 ms 
[INFO ] 2024-11-05 11:03:15.236 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 11:03:15.259 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:03:15.260 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:03:15.261 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 11:03:15.261 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 11:03:15.435 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 35 ms 
[INFO ] 2024-11-05 11:03:15.595 - [测试共享缓存(101)][增强JS] - childName1 
[INFO ] 2024-11-05 11:03:15.604 - [测试共享缓存(101)][6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] - Node 6b95e0a9-b3b3-406a-bbf5-54a7eb090cce[6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] running status set to false 
[INFO ] 2024-11-05 11:03:15.605 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 11:03:15.605 - [测试共享缓存(101)][6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] - Node 6b95e0a9-b3b3-406a-bbf5-54a7eb090cce[6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] schema data cleaned 
[INFO ] 2024-11-05 11:03:15.605 - [测试共享缓存(101)][6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] - Node 6b95e0a9-b3b3-406a-bbf5-54a7eb090cce[6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] monitor closed 
[INFO ] 2024-11-05 11:03:15.605 - [测试共享缓存(101)][6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] - Node 6b95e0a9-b3b3-406a-bbf5-54a7eb090cce[6b95e0a9-b3b3-406a-bbf5-54a7eb090cce] close complete, cost 3 ms 
[INFO ] 2024-11-05 11:03:15.609 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-c0e8e8f1-ff0f-4205-ab8c-a261e9629ae3 
[INFO ] 2024-11-05 11:03:15.609 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-c0e8e8f1-ff0f-4205-ab8c-a261e9629ae3 
[INFO ] 2024-11-05 11:03:15.611 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 11:03:15.611 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 11:03:15.611 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 11:03:15.611 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 19 ms 
[INFO ] 2024-11-05 11:03:15.615 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 11:03:15.615 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 11:03:15.615 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 11:03:15.615 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 905ms 
[INFO ] 2024-11-05 11:06:37.559 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 11:06:37.560 - [测试共享缓存(101)][ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] - Node ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc[ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] start preload schema,table counts: 0 
[INFO ] 2024-11-05 11:06:37.560 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:06:37.560 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:06:37.560 - [测试共享缓存(101)][ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] - Node ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc[ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 11:06:37.581 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 20 ms 
[INFO ] 2024-11-05 11:06:37.582 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 22 ms 
[INFO ] 2024-11-05 11:06:37.582 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 11:06:37.838 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 11:06:37.854 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:06:37.854 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:06:37.854 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 11:06:37.854 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 11:06:38.030 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 17 ms 
[INFO ] 2024-11-05 11:06:38.229 - [测试共享缓存(101)][增强JS] - childName4 
[INFO ] 2024-11-05 11:06:38.229 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 11:06:38.240 - [测试共享缓存(101)][ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] - Node ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc[ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] running status set to false 
[INFO ] 2024-11-05 11:06:38.240 - [测试共享缓存(101)][ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] - Node ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc[ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] schema data cleaned 
[INFO ] 2024-11-05 11:06:38.240 - [测试共享缓存(101)][ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] - Node ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc[ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] monitor closed 
[INFO ] 2024-11-05 11:06:38.242 - [测试共享缓存(101)][ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] - Node ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc[ad717f23-ae5b-48bb-9c27-cc4cd83c6bdc] close complete, cost 9 ms 
[INFO ] 2024-11-05 11:06:38.243 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-8aae0337-3340-4d1a-b22a-03ff0153bcba 
[INFO ] 2024-11-05 11:06:38.243 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-8aae0337-3340-4d1a-b22a-03ff0153bcba 
[INFO ] 2024-11-05 11:06:38.243 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 11:06:38.245 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 11:06:38.245 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 11:06:38.255 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 17 ms 
[INFO ] 2024-11-05 11:06:38.255 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 11:06:38.255 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 11:06:38.255 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 11:06:38.424 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 768ms 
[INFO ] 2024-11-05 11:11:14.979 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 11:11:14.981 - [测试共享缓存(101)][816743d2-2792-42aa-b6b9-954dfaaedb3e] - Node 816743d2-2792-42aa-b6b9-954dfaaedb3e[816743d2-2792-42aa-b6b9-954dfaaedb3e] start preload schema,table counts: 0 
[INFO ] 2024-11-05 11:11:14.981 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:11:14.981 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:11:14.984 - [测试共享缓存(101)][816743d2-2792-42aa-b6b9-954dfaaedb3e] - Node 816743d2-2792-42aa-b6b9-954dfaaedb3e[816743d2-2792-42aa-b6b9-954dfaaedb3e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 11:11:15.008 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 33 ms 
[INFO ] 2024-11-05 11:11:15.008 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 33 ms 
[INFO ] 2024-11-05 11:11:15.008 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 11:11:15.451 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 11:11:15.469 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:11:15.469 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:11:15.470 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 11:11:15.470 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 11:11:15.678 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 32 ms 
[INFO ] 2024-11-05 11:11:15.873 - [测试共享缓存(101)][增强JS] - child5 
[INFO ] 2024-11-05 11:11:15.885 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 11:11:15.886 - [测试共享缓存(101)][816743d2-2792-42aa-b6b9-954dfaaedb3e] - Node 816743d2-2792-42aa-b6b9-954dfaaedb3e[816743d2-2792-42aa-b6b9-954dfaaedb3e] running status set to false 
[INFO ] 2024-11-05 11:11:15.886 - [测试共享缓存(101)][816743d2-2792-42aa-b6b9-954dfaaedb3e] - Node 816743d2-2792-42aa-b6b9-954dfaaedb3e[816743d2-2792-42aa-b6b9-954dfaaedb3e] schema data cleaned 
[INFO ] 2024-11-05 11:11:15.886 - [测试共享缓存(101)][816743d2-2792-42aa-b6b9-954dfaaedb3e] - Node 816743d2-2792-42aa-b6b9-954dfaaedb3e[816743d2-2792-42aa-b6b9-954dfaaedb3e] monitor closed 
[INFO ] 2024-11-05 11:11:15.886 - [测试共享缓存(101)][816743d2-2792-42aa-b6b9-954dfaaedb3e] - Node 816743d2-2792-42aa-b6b9-954dfaaedb3e[816743d2-2792-42aa-b6b9-954dfaaedb3e] close complete, cost 1 ms 
[INFO ] 2024-11-05 11:11:15.888 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-73d55a71-486f-4ead-ae00-034f2773f051 
[INFO ] 2024-11-05 11:11:15.888 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-73d55a71-486f-4ead-ae00-034f2773f051 
[INFO ] 2024-11-05 11:11:15.888 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 11:11:15.889 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 11:11:15.889 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 11:11:15.889 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 13 ms 
[INFO ] 2024-11-05 11:11:15.906 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 11:11:15.906 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 11:11:15.906 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 11:11:15.906 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 1045ms 
[INFO ] 2024-11-05 11:30:39.905 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 11:30:40.003 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:30:40.003 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:30:40.004 - [测试共享缓存(101)][73210ff6-a698-4f61-96de-2c6ffae12967] - Node 73210ff6-a698-4f61-96de-2c6ffae12967[73210ff6-a698-4f61-96de-2c6ffae12967] start preload schema,table counts: 0 
[INFO ] 2024-11-05 11:30:40.004 - [测试共享缓存(101)][73210ff6-a698-4f61-96de-2c6ffae12967] - Node 73210ff6-a698-4f61-96de-2c6ffae12967[73210ff6-a698-4f61-96de-2c6ffae12967] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 11:30:40.042 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 38 ms 
[INFO ] 2024-11-05 11:30:40.043 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 38 ms 
[INFO ] 2024-11-05 11:30:40.043 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 11:30:40.427 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 11:30:40.428 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:30:40.428 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:30:40.428 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 11:30:40.429 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 11:30:40.429 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 40 ms 
[INFO ] 2024-11-05 11:31:10.584 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 11:31:10.595 - [测试共享缓存(101)][73210ff6-a698-4f61-96de-2c6ffae12967] - Node 73210ff6-a698-4f61-96de-2c6ffae12967[73210ff6-a698-4f61-96de-2c6ffae12967] running status set to false 
[INFO ] 2024-11-05 11:31:11.795 - [测试共享缓存(101)][73210ff6-a698-4f61-96de-2c6ffae12967] - Node 73210ff6-a698-4f61-96de-2c6ffae12967[73210ff6-a698-4f61-96de-2c6ffae12967] schema data cleaned 
[INFO ] 2024-11-05 11:31:45.384 - [测试共享缓存(101)][73210ff6-a698-4f61-96de-2c6ffae12967] - Node 73210ff6-a698-4f61-96de-2c6ffae12967[73210ff6-a698-4f61-96de-2c6ffae12967] monitor closed 
[INFO ] 2024-11-05 11:31:49.774 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-52be4c66-ace5-402b-b251-86592deaef83 
[INFO ] 2024-11-05 11:32:05.594 - [测试共享缓存(101)][73210ff6-a698-4f61-96de-2c6ffae12967] - Node 73210ff6-a698-4f61-96de-2c6ffae12967[73210ff6-a698-4f61-96de-2c6ffae12967] close complete, cost 33742 ms 
[INFO ] 2024-11-05 11:32:11.107 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-52be4c66-ace5-402b-b251-86592deaef83 
[INFO ] 2024-11-05 11:32:11.219 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 11:32:11.219 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 11:32:11.219 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 11:32:11.228 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 60712 ms 
[INFO ] 2024-11-05 11:32:11.231 - [测试共享缓存(101)][增强JS] - nameupdate2 
[INFO ] 2024-11-05 11:32:11.237 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 11:32:11.241 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 11:32:11.245 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 11:32:11.245 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 91354ms 
[INFO ] 2024-11-05 11:43:06.592 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 11:43:06.657 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:43:06.658 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 11:43:06.659 - [测试共享缓存(101)][b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] - Node b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d[b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] start preload schema,table counts: 0 
[INFO ] 2024-11-05 11:43:06.660 - [测试共享缓存(101)][b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] - Node b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d[b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 11:43:06.710 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 52 ms 
[INFO ] 2024-11-05 11:43:06.714 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 49 ms 
[INFO ] 2024-11-05 11:43:06.716 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 11:43:07.821 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 11:43:07.824 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:43:07.830 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 11:43:07.830 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 11:43:07.834 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 11:43:07.844 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 90 ms 
[INFO ] 2024-11-05 11:43:18.486 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 11:43:18.508 - [测试共享缓存(101)][b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] - Node b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d[b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] running status set to false 
[INFO ] 2024-11-05 11:43:18.511 - [测试共享缓存(101)][b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] - Node b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d[b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] schema data cleaned 
[INFO ] 2024-11-05 11:43:18.514 - [测试共享缓存(101)][b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] - Node b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d[b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] monitor closed 
[INFO ] 2024-11-05 11:43:18.515 - [测试共享缓存(101)][b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] - Node b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d[b861e7e9-4c18-4a0e-9d08-e7fc691a9e5d] close complete, cost 38 ms 
[INFO ] 2024-11-05 11:43:18.528 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-f8aea741-a565-4073-8976-f1fcb8729f50 
[INFO ] 2024-11-05 11:43:18.529 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-f8aea741-a565-4073-8976-f1fcb8729f50 
[INFO ] 2024-11-05 11:43:18.543 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 11:43:18.543 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 11:43:18.543 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 11:43:18.548 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 82 ms 
[INFO ] 2024-11-05 11:43:18.568 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 11:43:18.569 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 11:43:18.569 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 11:43:18.569 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 12120ms 
[INFO ] 2024-11-05 11:43:36.437 - [测试共享缓存(101)][增强JS] - nameupdate2 
[INFO ] 2024-11-05 14:24:48.579 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 14:24:48.606 - [测试共享缓存(101)][4524a95f-b056-44cd-8cf9-ffab20cf0fd1] - Node 4524a95f-b056-44cd-8cf9-ffab20cf0fd1[4524a95f-b056-44cd-8cf9-ffab20cf0fd1] start preload schema,table counts: 0 
[INFO ] 2024-11-05 14:24:48.606 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 14:24:48.609 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 14:24:48.611 - [测试共享缓存(101)][4524a95f-b056-44cd-8cf9-ffab20cf0fd1] - Node 4524a95f-b056-44cd-8cf9-ffab20cf0fd1[4524a95f-b056-44cd-8cf9-ffab20cf0fd1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 14:24:48.645 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 38 ms 
[INFO ] 2024-11-05 14:24:48.645 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 36 ms 
[INFO ] 2024-11-05 14:24:48.851 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 14:24:49.029 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 14:24:49.029 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 14:24:49.031 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 14:24:49.031 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 14:24:49.031 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 14:24:49.032 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 53 ms 
[INFO ] 2024-11-05 14:24:59.537 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 14:24:59.562 - [测试共享缓存(101)][4524a95f-b056-44cd-8cf9-ffab20cf0fd1] - Node 4524a95f-b056-44cd-8cf9-ffab20cf0fd1[4524a95f-b056-44cd-8cf9-ffab20cf0fd1] running status set to false 
[INFO ] 2024-11-05 14:24:59.564 - [测试共享缓存(101)][4524a95f-b056-44cd-8cf9-ffab20cf0fd1] - Node 4524a95f-b056-44cd-8cf9-ffab20cf0fd1[4524a95f-b056-44cd-8cf9-ffab20cf0fd1] schema data cleaned 
[INFO ] 2024-11-05 14:24:59.564 - [测试共享缓存(101)][4524a95f-b056-44cd-8cf9-ffab20cf0fd1] - Node 4524a95f-b056-44cd-8cf9-ffab20cf0fd1[4524a95f-b056-44cd-8cf9-ffab20cf0fd1] monitor closed 
[INFO ] 2024-11-05 14:24:59.564 - [测试共享缓存(101)][4524a95f-b056-44cd-8cf9-ffab20cf0fd1] - Node 4524a95f-b056-44cd-8cf9-ffab20cf0fd1[4524a95f-b056-44cd-8cf9-ffab20cf0fd1] close complete, cost 34 ms 
[INFO ] 2024-11-05 14:24:59.584 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-1c8c6a42-baae-4703-92dd-eef869687797 
[INFO ] 2024-11-05 14:24:59.585 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-1c8c6a42-baae-4703-92dd-eef869687797 
[INFO ] 2024-11-05 14:24:59.599 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 14:24:59.602 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 14:24:59.603 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 14:24:59.637 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 80 ms 
[INFO ] 2024-11-05 14:24:59.637 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 14:24:59.637 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 14:24:59.637 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 14:24:59.849 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 11308ms 
[INFO ] 2024-11-05 14:31:43.761 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 14:31:43.834 - [测试共享缓存(101)][3f3a879d-ea96-4862-af69-36ffcbe61162] - Node 3f3a879d-ea96-4862-af69-36ffcbe61162[3f3a879d-ea96-4862-af69-36ffcbe61162] start preload schema,table counts: 0 
[INFO ] 2024-11-05 14:31:43.834 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 14:31:43.835 - [测试共享缓存(101)][3f3a879d-ea96-4862-af69-36ffcbe61162] - Node 3f3a879d-ea96-4862-af69-36ffcbe61162[3f3a879d-ea96-4862-af69-36ffcbe61162] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 14:31:43.852 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 14:31:43.853 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 19 ms 
[INFO ] 2024-11-05 14:31:43.853 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 17 ms 
[INFO ] 2024-11-05 14:31:43.853 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 14:31:44.122 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 14:31:44.142 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 14:31:44.142 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 14:31:44.142 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 14:31:44.142 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 14:31:44.143 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 36 ms 
[INFO ] 2024-11-05 14:31:45.426 - [测试共享缓存(101)][增强JS] - name2 
[INFO ] 2024-11-05 14:31:45.430 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 14:31:45.434 - [测试共享缓存(101)][3f3a879d-ea96-4862-af69-36ffcbe61162] - Node 3f3a879d-ea96-4862-af69-36ffcbe61162[3f3a879d-ea96-4862-af69-36ffcbe61162] running status set to false 
[INFO ] 2024-11-05 14:31:45.434 - [测试共享缓存(101)][3f3a879d-ea96-4862-af69-36ffcbe61162] - Node 3f3a879d-ea96-4862-af69-36ffcbe61162[3f3a879d-ea96-4862-af69-36ffcbe61162] schema data cleaned 
[INFO ] 2024-11-05 14:31:45.434 - [测试共享缓存(101)][3f3a879d-ea96-4862-af69-36ffcbe61162] - Node 3f3a879d-ea96-4862-af69-36ffcbe61162[3f3a879d-ea96-4862-af69-36ffcbe61162] monitor closed 
[INFO ] 2024-11-05 14:31:45.435 - [测试共享缓存(101)][3f3a879d-ea96-4862-af69-36ffcbe61162] - Node 3f3a879d-ea96-4862-af69-36ffcbe61162[3f3a879d-ea96-4862-af69-36ffcbe61162] close complete, cost 1 ms 
[INFO ] 2024-11-05 14:31:45.438 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-7b2ee608-59f8-41a7-9af9-dd242066d520 
[INFO ] 2024-11-05 14:31:45.439 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-7b2ee608-59f8-41a7-9af9-dd242066d520 
[INFO ] 2024-11-05 14:31:45.443 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 14:31:45.446 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 14:31:45.446 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 14:31:45.446 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 20 ms 
[INFO ] 2024-11-05 14:31:45.451 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 14:31:45.452 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 14:31:45.452 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 14:31:45.456 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 1700ms 
[INFO ] 2024-11-05 14:32:01.636 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 14:32:01.636 - [测试共享缓存(101)][c29c7796-89cc-4a35-a579-30772c9ceb46] - Node c29c7796-89cc-4a35-a579-30772c9ceb46[c29c7796-89cc-4a35-a579-30772c9ceb46] start preload schema,table counts: 0 
[INFO ] 2024-11-05 14:32:01.636 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 14:32:01.636 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 14:32:01.636 - [测试共享缓存(101)][c29c7796-89cc-4a35-a579-30772c9ceb46] - Node c29c7796-89cc-4a35-a579-30772c9ceb46[c29c7796-89cc-4a35-a579-30772c9ceb46] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 14:32:01.660 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 22 ms 
[INFO ] 2024-11-05 14:32:01.661 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 23 ms 
[INFO ] 2024-11-05 14:32:01.662 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 14:32:01.982 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 14:32:01.982 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 14:32:01.982 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 14:32:01.985 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 14:32:01.985 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 14:32:01.986 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 25 ms 
[INFO ] 2024-11-05 14:32:36.591 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 14:32:36.614 - [测试共享缓存(101)][c29c7796-89cc-4a35-a579-30772c9ceb46] - Node c29c7796-89cc-4a35-a579-30772c9ceb46[c29c7796-89cc-4a35-a579-30772c9ceb46] running status set to false 
[INFO ] 2024-11-05 14:32:36.646 - [测试共享缓存(101)][c29c7796-89cc-4a35-a579-30772c9ceb46] - Node c29c7796-89cc-4a35-a579-30772c9ceb46[c29c7796-89cc-4a35-a579-30772c9ceb46] schema data cleaned 
[INFO ] 2024-11-05 14:32:36.647 - [测试共享缓存(101)][c29c7796-89cc-4a35-a579-30772c9ceb46] - Node c29c7796-89cc-4a35-a579-30772c9ceb46[c29c7796-89cc-4a35-a579-30772c9ceb46] monitor closed 
[INFO ] 2024-11-05 14:32:36.650 - [测试共享缓存(101)][c29c7796-89cc-4a35-a579-30772c9ceb46] - Node c29c7796-89cc-4a35-a579-30772c9ceb46[c29c7796-89cc-4a35-a579-30772c9ceb46] close complete, cost 169 ms 
[INFO ] 2024-11-05 14:32:36.654 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-e3f357cf-15c2-4b69-b251-ca3c623c1205 
[INFO ] 2024-11-05 14:32:36.654 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-e3f357cf-15c2-4b69-b251-ca3c623c1205 
[INFO ] 2024-11-05 14:32:36.654 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 14:32:36.673 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 14:32:36.677 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 14:32:36.677 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 225 ms 
[INFO ] 2024-11-05 14:32:36.692 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 14:32:36.692 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 14:32:36.692 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 14:32:36.779 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 35179ms 
[INFO ] 2024-11-05 15:41:12.708 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 15:41:12.791 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 15:41:12.792 - [测试共享缓存(101)][6a62c274-fb66-43fe-8fa3-25b9200b48fa] - Node 6a62c274-fb66-43fe-8fa3-25b9200b48fa[6a62c274-fb66-43fe-8fa3-25b9200b48fa] start preload schema,table counts: 0 
[INFO ] 2024-11-05 15:41:12.792 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 15:41:12.792 - [测试共享缓存(101)][6a62c274-fb66-43fe-8fa3-25b9200b48fa] - Node 6a62c274-fb66-43fe-8fa3-25b9200b48fa[6a62c274-fb66-43fe-8fa3-25b9200b48fa] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 15:41:12.811 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 23 ms 
[INFO ] 2024-11-05 15:41:12.811 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 23 ms 
[INFO ] 2024-11-05 15:41:12.811 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 15:41:13.257 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 15:41:13.271 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 15:41:13.271 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 15:41:13.272 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 15:41:13.483 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 15:41:13.484 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 17 ms 
[INFO ] 2024-11-05 15:41:13.755 - [测试共享缓存(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined 
[ERROR] 2024-11-05 15:41:13.756 - [测试共享缓存(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	<js>.process(<eval>:4)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-11-05 15:41:16.295 - [测试共享缓存(101)][6a62c274-fb66-43fe-8fa3-25b9200b48fa] - Node 6a62c274-fb66-43fe-8fa3-25b9200b48fa[6a62c274-fb66-43fe-8fa3-25b9200b48fa] running status set to false 
[INFO ] 2024-11-05 15:41:16.295 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 15:41:16.296 - [测试共享缓存(101)][6a62c274-fb66-43fe-8fa3-25b9200b48fa] - Node 6a62c274-fb66-43fe-8fa3-25b9200b48fa[6a62c274-fb66-43fe-8fa3-25b9200b48fa] schema data cleaned 
[INFO ] 2024-11-05 15:41:16.296 - [测试共享缓存(101)][6a62c274-fb66-43fe-8fa3-25b9200b48fa] - Node 6a62c274-fb66-43fe-8fa3-25b9200b48fa[6a62c274-fb66-43fe-8fa3-25b9200b48fa] monitor closed 
[INFO ] 2024-11-05 15:41:16.296 - [测试共享缓存(101)][6a62c274-fb66-43fe-8fa3-25b9200b48fa] - Node 6a62c274-fb66-43fe-8fa3-25b9200b48fa[6a62c274-fb66-43fe-8fa3-25b9200b48fa] close complete, cost 5 ms 
[INFO ] 2024-11-05 15:41:16.325 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-80d4e15c-2fa7-4005-b836-1b36e0d77b4f 
[INFO ] 2024-11-05 15:41:16.325 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-80d4e15c-2fa7-4005-b836-1b36e0d77b4f 
[INFO ] 2024-11-05 15:41:16.325 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 15:41:16.335 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 15:41:16.335 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 15:41:16.335 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 55 ms 
[INFO ] 2024-11-05 15:41:16.343 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 15:41:16.344 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 15:41:16.344 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 15:41:16.344 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 3645ms 
[INFO ] 2024-11-05 15:41:35.122 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 15:41:35.129 - [测试共享缓存(101)][2810c779-5085-411c-959f-5acf50c61ca3] - Node 2810c779-5085-411c-959f-5acf50c61ca3[2810c779-5085-411c-959f-5acf50c61ca3] start preload schema,table counts: 0 
[INFO ] 2024-11-05 15:41:35.129 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 15:41:35.130 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] start preload schema,table counts: 1 
[INFO ] 2024-11-05 15:41:35.130 - [测试共享缓存(101)][2810c779-5085-411c-959f-5acf50c61ca3] - Node 2810c779-5085-411c-959f-5acf50c61ca3[2810c779-5085-411c-959f-5acf50c61ca3] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 15:41:35.144 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] preload schema finished, cost 13 ms 
[INFO ] 2024-11-05 15:41:35.144 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 13 ms 
[INFO ] 2024-11-05 15:41:35.345 - [测试共享缓存(101)][增强JS] - Node js_processor(增强JS: 0c3be230-bc37-4fc1-a59d-0a707d836a37) enable batch process 
[INFO ] 2024-11-05 15:41:35.418 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 15:41:35.418 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 15:41:35.421 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 15:41:35.421 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 15:41:35.421 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 15:41:35.624 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 25 ms 
[INFO ] 2024-11-05 15:41:45.380 - [测试共享缓存(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined 
[ERROR] 2024-11-05 15:41:45.381 - [测试共享缓存(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	<js>.process(<eval>:4)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-11-05 15:41:47.937 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] running status set to false 
[INFO ] 2024-11-05 15:41:47.978 - [测试共享缓存(101)][2810c779-5085-411c-959f-5acf50c61ca3] - Node 2810c779-5085-411c-959f-5acf50c61ca3[2810c779-5085-411c-959f-5acf50c61ca3] running status set to false 
[INFO ] 2024-11-05 15:41:47.997 - [测试共享缓存(101)][2810c779-5085-411c-959f-5acf50c61ca3] - Node 2810c779-5085-411c-959f-5acf50c61ca3[2810c779-5085-411c-959f-5acf50c61ca3] schema data cleaned 
[INFO ] 2024-11-05 15:41:47.997 - [测试共享缓存(101)][2810c779-5085-411c-959f-5acf50c61ca3] - Node 2810c779-5085-411c-959f-5acf50c61ca3[2810c779-5085-411c-959f-5acf50c61ca3] monitor closed 
[INFO ] 2024-11-05 15:41:48.010 - [测试共享缓存(101)][2810c779-5085-411c-959f-5acf50c61ca3] - Node 2810c779-5085-411c-959f-5acf50c61ca3[2810c779-5085-411c-959f-5acf50c61ca3] close complete, cost 65 ms 
[INFO ] 2024-11-05 15:41:48.010 - [测试共享缓存(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Mysql3306-faa774e7-56bb-482f-8cae-3d4fb00c22f0 
[INFO ] 2024-11-05 15:41:48.011 - [测试共享缓存(101)][增强JS] - PDK connector node released: ScriptExecutor-Mysql3306-faa774e7-56bb-482f-8cae-3d4fb00c22f0 
[INFO ] 2024-11-05 15:41:48.011 - [测试共享缓存(101)][增强JS] - [ScriptExecutorsManager-6728a83f6025fe3957d2e295-0c3be230-bc37-4fc1-a59d-0a707d836a37-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-05 15:41:48.017 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] schema data cleaned 
[INFO ] 2024-11-05 15:41:48.017 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] monitor closed 
[INFO ] 2024-11-05 15:41:48.017 - [测试共享缓存(101)][增强JS] - Node 增强JS[0c3be230-bc37-4fc1-a59d-0a707d836a37] close complete, cost 88 ms 
[INFO ] 2024-11-05 15:41:48.030 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 15:41:48.030 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 15:41:48.030 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 15:41:48.031 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 12997ms 
[INFO ] 2024-11-05 15:45:43.277 - [测试共享缓存(101)] - 6728a83f6025fe3957d2e295 task start 
[INFO ] 2024-11-05 15:45:43.315 - [测试共享缓存(101)][标准JS] - Node 标准JS[d51c7c58-43bf-40d4-b405-4d5a62d69ad9] start preload schema,table counts: 1 
[INFO ] 2024-11-05 15:45:43.315 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] start preload schema,table counts: 1 
[INFO ] 2024-11-05 15:45:43.316 - [测试共享缓存(101)][234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] - Node 234a6ce8-e942-4bbd-9c1a-6dcd8217fca2[234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] start preload schema,table counts: 0 
[INFO ] 2024-11-05 15:45:43.316 - [测试共享缓存(101)][234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] - Node 234a6ce8-e942-4bbd-9c1a-6dcd8217fca2[234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] preload schema finished, cost 0 ms 
[INFO ] 2024-11-05 15:45:43.328 - [测试共享缓存(101)][标准JS] - Node 标准JS[d51c7c58-43bf-40d4-b405-4d5a62d69ad9] preload schema finished, cost 19 ms 
[INFO ] 2024-11-05 15:45:43.328 - [测试共享缓存(101)][标准JS] - Node standard_js_processor(标准JS: d51c7c58-43bf-40d4-b405-4d5a62d69ad9) enable batch process 
[INFO ] 2024-11-05 15:45:43.531 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] preload schema finished, cost 14 ms 
[INFO ] 2024-11-05 15:45:43.557 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] running status set to false 
[INFO ] 2024-11-05 15:45:43.557 - [测试共享缓存(101)][parent_table] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 15:45:43.558 - [测试共享缓存(101)][parent_table] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2ee77b53-4699-4238-be0e-5b95922677ef 
[INFO ] 2024-11-05 15:45:43.558 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] schema data cleaned 
[INFO ] 2024-11-05 15:45:43.558 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] monitor closed 
[INFO ] 2024-11-05 15:45:43.558 - [测试共享缓存(101)][parent_table] - Node parent_table[2ee77b53-4699-4238-be0e-5b95922677ef] close complete, cost 23 ms 
[INFO ] 2024-11-05 15:45:43.608 - [测试共享缓存(101)][标准JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined 
[ERROR] 2024-11-05 15:45:43.613 - [测试共享缓存(101)][标准JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	<js>.process(<eval>:2)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: b is not defined
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-11-05 15:45:46.190 - [测试共享缓存(101)][标准JS] - Node 标准JS[d51c7c58-43bf-40d4-b405-4d5a62d69ad9] running status set to false 
[INFO ] 2024-11-05 15:45:46.191 - [测试共享缓存(101)][234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] - Node 234a6ce8-e942-4bbd-9c1a-6dcd8217fca2[234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] running status set to false 
[INFO ] 2024-11-05 15:45:46.192 - [测试共享缓存(101)][234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] - Node 234a6ce8-e942-4bbd-9c1a-6dcd8217fca2[234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] schema data cleaned 
[INFO ] 2024-11-05 15:45:46.192 - [测试共享缓存(101)][234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] - Node 234a6ce8-e942-4bbd-9c1a-6dcd8217fca2[234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] monitor closed 
[INFO ] 2024-11-05 15:45:46.192 - [测试共享缓存(101)][234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] - Node 234a6ce8-e942-4bbd-9c1a-6dcd8217fca2[234a6ce8-e942-4bbd-9c1a-6dcd8217fca2] close complete, cost 38 ms 
[INFO ] 2024-11-05 15:45:46.200 - [测试共享缓存(101)][标准JS] - Node 标准JS[d51c7c58-43bf-40d4-b405-4d5a62d69ad9] schema data cleaned 
[INFO ] 2024-11-05 15:45:46.201 - [测试共享缓存(101)][标准JS] - Node 标准JS[d51c7c58-43bf-40d4-b405-4d5a62d69ad9] monitor closed 
[INFO ] 2024-11-05 15:45:46.201 - [测试共享缓存(101)][标准JS] - Node 标准JS[d51c7c58-43bf-40d4-b405-4d5a62d69ad9] close complete, cost 54 ms 
[INFO ] 2024-11-05 15:45:46.207 - [测试共享缓存(101)] - Closed task monitor(s)
null 
[INFO ] 2024-11-05 15:45:46.207 - [测试共享缓存(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-05 15:45:46.207 - [测试共享缓存(101)] - Stopped task aspect(s) 
[INFO ] 2024-11-05 15:45:46.207 - [测试共享缓存(101)] - test run task 6728a83f6025fe3957d2e295 complete, cost 2961ms 
