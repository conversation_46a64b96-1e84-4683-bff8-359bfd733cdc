[INFO ] 2024-07-30 17:36:43.605 - [任务 1] - Start task milestones: 66a8b34b4e88ce426f2f2f87(任务 1) 
[INFO ] 2024-07-30 17:36:43.606 - [任务 1] - Task initialization... 
[INFO ] 2024-07-30 17:36:43.744 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-30 17:36:43.744 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-30 17:36:44.332 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] start preload schema,table counts: 1 
[INFO ] 2024-07-30 17:36:44.333 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] start preload schema,table counts: 1 
[INFO ] 2024-07-30 17:36:44.396 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] preload schema finished, cost 65 ms 
[INFO ] 2024-07-30 17:36:44.396 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] preload schema finished, cost 62 ms 
[INFO ] 2024-07-30 17:36:45.751 - [任务 1][BMSQL_CONFIG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-30 17:36:45.753 - [任务 1][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" read batch size: 100 
[INFO ] 2024-07-30 17:36:45.753 - [任务 1][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" event queue capacity: 200 
[INFO ] 2024-07-30 17:36:45.753 - [任务 1][BMSQL_CONFIG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-30 17:36:45.772 - [任务 1][BMSQL_CONFIG] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":158,"gtidSet":""} 
[INFO ] 2024-07-30 17:36:45.970 - [任务 1][BMSQL_CONFIG] - Initial sync started 
[INFO ] 2024-07-30 17:36:45.974 - [任务 1][BMSQL_CONFIG] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-07-30 17:36:46.040 - [任务 1][BMSQL_CONFIG] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-07-30 17:36:46.041 - [任务 1][BMSQL_CONFIG] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-30 17:36:46.041 - [任务 1][BMSQL_CONFIG] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-07-30 17:36:46.042 - [任务 1][BMSQL_CONFIG] - Initial sync completed 
[INFO ] 2024-07-30 17:36:46.045 - [任务 1][BMSQL_CONFIG] - Incremental sync starting... 
[INFO ] 2024-07-30 17:36:46.045 - [任务 1][BMSQL_CONFIG] - Initial sync completed 
[INFO ] 2024-07-30 17:36:46.099 - [任务 1][BMSQL_CONFIG] - Starting stream read, table list: [BMSQL_CONFIG], offset: {"filename":"binlog.000021","position":158,"gtidSet":""} 
[INFO ] 2024-07-30 17:36:46.101 - [任务 1][BMSQL_CONFIG] - Starting mysql cdc, server name: 6c2399e5-2361-40aa-bde2-f2fed1e7a80e 
[INFO ] 2024-07-30 17:36:46.304 - [任务 1][BMSQL_CONFIG] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 922028286
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6c2399e5-2361-40aa-bde2-f2fed1e7a80e
  database.port: 3307
  threadName: Debezium-Mysql-Connector-6c2399e5-2361-40aa-bde2-f2fed1e7a80e
  database.hostname: localhost
  database.password: ********
  name: 6c2399e5-2361-40aa-bde2-f2fed1e7a80e
  pdk.offset.string: {"name":"6c2399e5-2361-40aa-bde2-f2fed1e7a80e","offset":{"{\"server\":\"6c2399e5-2361-40aa-bde2-f2fed1e7a80e\"}":"{\"file\":\"binlog.000021\",\"pos\":158,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-30 17:36:46.371 - [任务 1][BMSQL_CONFIG] - Connector Mysql incremental start succeed, tables: [BMSQL_CONFIG], data change syncing 
[INFO ] 2024-07-30 17:52:02.399 - [任务 1][BMSQL_CONFIG] - Mysql binlog reader stopped 
[WARN ] 2024-07-30 17:52:02.612 - [任务 1][BMSQL_CONFIG] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.EOFException: Failed to read next byte from position 1384
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-30 17:53:14.931 - [任务 1][BMSQL_CONFIG] - Starting mysql cdc, server name: 6c2399e5-2361-40aa-bde2-f2fed1e7a80e 
[INFO ] 2024-07-30 17:53:15.003 - [任务 1][BMSQL_CONFIG] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 216807624
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6c2399e5-2361-40aa-bde2-f2fed1e7a80e
  database.port: 3307
  threadName: Debezium-Mysql-Connector-6c2399e5-2361-40aa-bde2-f2fed1e7a80e
  database.hostname: localhost
  database.password: ********
  name: 6c2399e5-2361-40aa-bde2-f2fed1e7a80e
  pdk.offset.string: {"name":"6c2399e5-2361-40aa-bde2-f2fed1e7a80e","offset":{"{\"server\":\"6c2399e5-2361-40aa-bde2-f2fed1e7a80e\"}":"{\"ts_sec\":1722332206,\"file\":\"binlog.000021\",\"pos\":158,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-30 17:53:15.004 - [任务 1][BMSQL_CONFIG] - Connector Mysql incremental start succeed, tables: [BMSQL_CONFIG], data change syncing 
[INFO ] 2024-07-30 17:53:26.564 - [任务 1] - Stop task milestones: 66a8b34b4e88ce426f2f2f87(任务 1)  
[INFO ] 2024-07-30 17:53:26.768 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] running status set to false 
[INFO ] 2024-07-30 17:53:26.804 - [任务 1][BMSQL_CONFIG] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-30 17:53:26.804 - [任务 1][BMSQL_CONFIG] - Mysql binlog reader stopped 
[INFO ] 2024-07-30 17:53:26.817 - [任务 1][BMSQL_CONFIG] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-07-30 17:53:26.817 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 17:53:26.819 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 17:53:26.822 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] schema data cleaned 
[INFO ] 2024-07-30 17:53:26.823 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] monitor closed 
[INFO ] 2024-07-30 17:53:26.827 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] close complete, cost 136 ms 
[INFO ] 2024-07-30 17:53:26.865 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] running status set to false 
[INFO ] 2024-07-30 17:53:26.865 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 17:53:26.866 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 17:53:26.866 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] schema data cleaned 
[INFO ] 2024-07-30 17:53:26.867 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] monitor closed 
[INFO ] 2024-07-30 17:53:26.868 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] close complete, cost 41 ms 
[INFO ] 2024-07-30 17:53:28.250 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 17:53:28.254 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 17:53:28.254 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-30 17:53:28.325 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 17:53:28.327 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 17:53:47.735 - [任务 1] - Start task milestones: 66a8b34b4e88ce426f2f2f87(任务 1) 
[INFO ] 2024-07-30 17:53:47.767 - [任务 1] - Task initialization... 
[INFO ] 2024-07-30 17:53:47.767 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-30 17:53:47.970 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-30 17:53:47.975 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] start preload schema,table counts: 1 
[INFO ] 2024-07-30 17:53:47.975 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] start preload schema,table counts: 1 
[INFO ] 2024-07-30 17:53:48.010 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] preload schema finished, cost 36 ms 
[INFO ] 2024-07-30 17:53:48.011 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] preload schema finished, cost 34 ms 
[INFO ] 2024-07-30 17:53:48.825 - [任务 1][BMSQL_CONFIG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-30 17:53:48.997 - [任务 1][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" read batch size: 100 
[INFO ] 2024-07-30 17:53:48.998 - [任务 1][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" event queue capacity: 200 
[INFO ] 2024-07-30 17:53:48.999 - [任务 1][BMSQL_CONFIG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-30 17:53:49.070 - [任务 1][BMSQL_CONFIG] - batch offset found: {},stream offset found: {"filename":"binlog.000022","position":158,"gtidSet":""} 
[INFO ] 2024-07-30 17:53:49.071 - [任务 1][BMSQL_CONFIG] - Initial sync started 
[INFO ] 2024-07-30 17:53:49.078 - [任务 1][BMSQL_CONFIG] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-07-30 17:53:49.079 - [任务 1][BMSQL_CONFIG] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-07-30 17:53:49.110 - [任务 1][BMSQL_CONFIG] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-30 17:53:49.114 - [任务 1][BMSQL_CONFIG] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-07-30 17:53:49.114 - [任务 1][BMSQL_CONFIG] - Initial sync completed 
[INFO ] 2024-07-30 17:53:49.123 - [任务 1][BMSQL_CONFIG] - Incremental sync starting... 
[INFO ] 2024-07-30 17:53:49.126 - [任务 1][BMSQL_CONFIG] - Initial sync completed 
[INFO ] 2024-07-30 17:53:49.128 - [任务 1][BMSQL_CONFIG] - Starting stream read, table list: [BMSQL_CONFIG], offset: {"filename":"binlog.000022","position":158,"gtidSet":""} 
[INFO ] 2024-07-30 17:53:49.155 - [任务 1][BMSQL_CONFIG] - Starting mysql cdc, server name: 39c687c1-1eab-4424-a8f9-9cc19ac9b00a 
[INFO ] 2024-07-30 17:53:49.228 - [任务 1][BMSQL_CONFIG] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 255500712
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 39c687c1-1eab-4424-a8f9-9cc19ac9b00a
  database.port: 3307
  threadName: Debezium-Mysql-Connector-39c687c1-1eab-4424-a8f9-9cc19ac9b00a
  database.hostname: localhost
  database.password: ********
  name: 39c687c1-1eab-4424-a8f9-9cc19ac9b00a
  pdk.offset.string: {"name":"39c687c1-1eab-4424-a8f9-9cc19ac9b00a","offset":{"{\"server\":\"39c687c1-1eab-4424-a8f9-9cc19ac9b00a\"}":"{\"file\":\"binlog.000022\",\"pos\":158,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-30 17:53:49.228 - [任务 1][BMSQL_CONFIG] - Connector Mysql incremental start succeed, tables: [BMSQL_CONFIG], data change syncing 
[INFO ] 2024-07-30 17:55:09.296 - [任务 1][BMSQL_CONFIG] - Mysql binlog reader stopped 
[INFO ] 2024-07-30 17:55:09.296 - [任务 1][BMSQL_CONFIG] - Incremental sync completed 
[ERROR] 2024-07-30 17:55:09.359 - [任务 1][BMSQL_CONFIG] - java.lang.RuntimeException: java.io.EOFException: Failed to read next byte from position 646 <-- Error Message -->
java.lang.RuntimeException: java.io.EOFException: Failed to read next byte from position 646

<-- Simple Stack Trace -->
Caused by: java.io.EOFException: Failed to read next byte from position 646
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.io.EOFException: Failed to read next byte from position 646
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.io.EOFException: Failed to read next byte from position 646
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:159)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:390)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:655)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.io.EOFException: Failed to read next byte from position 646
	at com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	at com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	at com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	at com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	at com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	at io.debezium.connector.mysql.MySqlStreamingChangeEventSource$1.nextEvent(MySqlStreamingChangeEventSource.java:233)
	at com.github.shyiko.mysql.binlog.BinaryLogClient.listenForEventPackets(BinaryLogClient.java:1078)
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:649)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-07-30 17:55:09.359 - [任务 1][BMSQL_CONFIG] - Job suspend in error handle 
[INFO ] 2024-07-30 17:55:09.832 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] running status set to false 
[INFO ] 2024-07-30 17:55:09.832 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 17:55:09.836 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 17:55:09.836 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] schema data cleaned 
[INFO ] 2024-07-30 17:55:09.839 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] monitor closed 
[INFO ] 2024-07-30 17:55:09.840 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] close complete, cost 40 ms 
[INFO ] 2024-07-30 17:55:09.840 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] running status set to false 
[INFO ] 2024-07-30 17:55:09.856 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 17:55:09.856 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 17:55:09.857 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] schema data cleaned 
[INFO ] 2024-07-30 17:55:09.857 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] monitor closed 
[INFO ] 2024-07-30 17:55:10.061 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] close complete, cost 19 ms 
[INFO ] 2024-07-30 17:55:37.157 - [任务 1] - Task [任务 1] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-30 17:55:37.230 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 17:55:37.230 - [任务 1] - Stop task milestones: 66a8b34b4e88ce426f2f2f87(任务 1)  
[INFO ] 2024-07-30 17:55:37.293 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 17:56:47.828 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-30 17:56:56.247 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 17:56:56.247 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 18:40:29.832 - [任务 1] - Start task milestones: 66a8b34b4e88ce426f2f2f87(任务 1) 
[INFO ] 2024-07-30 18:40:29.832 - [任务 1] - Task initialization... 
[INFO ] 2024-07-30 18:40:29.902 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-30 18:40:29.902 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-30 18:40:29.960 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] start preload schema,table counts: 1 
[INFO ] 2024-07-30 18:40:29.961 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] start preload schema,table counts: 1 
[INFO ] 2024-07-30 18:40:29.993 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] preload schema finished, cost 30 ms 
[INFO ] 2024-07-30 18:40:29.994 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] preload schema finished, cost 30 ms 
[INFO ] 2024-07-30 18:40:31.001 - [任务 1][BMSQL_CONFIG] - Write batch size: 100, max wait ms per batch: 500 
[ERROR] 2024-07-30 18:40:37.769 - [任务 1][BMSQL_CONFIG] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@1dca311d failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@1dca311d failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more


<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@1dca311d failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@1dca311d failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[INFO ] 2024-07-30 18:41:12.311 - [任务 1][BMSQL_CONFIG] - Job suspend in error handle 
[INFO ] 2024-07-30 18:41:12.426 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] running status set to false 
[INFO ] 2024-07-30 18:41:12.434 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 18:41:12.434 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 18:41:12.434 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] schema data cleaned 
[INFO ] 2024-07-30 18:41:12.434 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] monitor closed 
[INFO ] 2024-07-30 18:41:12.440 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] close complete, cost 40 ms 
[INFO ] 2024-07-30 18:41:12.441 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] running status set to false 
[INFO ] 2024-07-30 18:41:12.462 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 18:41:12.462 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 18:41:12.462 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] schema data cleaned 
[INFO ] 2024-07-30 18:41:12.462 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] monitor closed 
[INFO ] 2024-07-30 18:41:12.518 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] close complete, cost 63 ms 
[INFO ] 2024-07-30 18:41:17.460 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 18:41:17.460 - [任务 1] - Stop task milestones: 66a8b34b4e88ce426f2f2f87(任务 1)  
[INFO ] 2024-07-30 18:41:17.478 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 18:41:17.478 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-30 18:41:17.501 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 18:41:17.712 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 18:46:56.068 - [任务 1] - Start task milestones: 66a8b34b4e88ce426f2f2f87(任务 1) 
[INFO ] 2024-07-30 18:46:56.089 - [任务 1] - Task initialization... 
[INFO ] 2024-07-30 18:46:56.089 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-30 18:46:56.196 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-30 18:46:56.197 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] start preload schema,table counts: 1 
[INFO ] 2024-07-30 18:46:56.197 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] start preload schema,table counts: 1 
[INFO ] 2024-07-30 18:46:56.226 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] preload schema finished, cost 33 ms 
[INFO ] 2024-07-30 18:46:56.432 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] preload schema finished, cost 41 ms 
[INFO ] 2024-07-30 18:46:57.243 - [任务 1][BMSQL_CONFIG] - Write batch size: 100, max wait ms per batch: 500 
[ERROR] 2024-07-30 18:47:26.634 - [任务 1][BMSQL_CONFIG] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@10070999 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@10070999 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more


<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@10070999 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@10070999 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[INFO ] 2024-07-30 18:47:26.650 - [任务 1][BMSQL_CONFIG] - Job suspend in error handle 
[INFO ] 2024-07-30 18:47:26.651 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] running status set to false 
[INFO ] 2024-07-30 18:47:26.661 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] running status set to false 
[INFO ] 2024-07-30 18:47:26.680 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 18:47:26.680 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 18:47:26.681 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 18:47:26.681 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 18:47:26.681 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] schema data cleaned 
[INFO ] 2024-07-30 18:47:26.681 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] schema data cleaned 
[INFO ] 2024-07-30 18:47:26.684 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] monitor closed 
[INFO ] 2024-07-30 18:47:26.684 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] monitor closed 
[INFO ] 2024-07-30 18:47:26.696 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] close complete, cost 36 ms 
[INFO ] 2024-07-30 18:47:26.698 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] close complete, cost 25 ms 
[INFO ] 2024-07-30 18:48:31.538 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 18:48:31.538 - [任务 1] - Stop task milestones: 66a8b34b4e88ce426f2f2f87(任务 1)  
[INFO ] 2024-07-30 18:48:31.553 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 18:48:31.553 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-30 18:48:31.557 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 18:48:31.559 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 18:48:31.576 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 18:48:31.581 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 18:48:50.320 - [任务 1] - Start task milestones: 66a8b34b4e88ce426f2f2f87(任务 1) 
[INFO ] 2024-07-30 18:48:50.321 - [任务 1] - Task initialization... 
[INFO ] 2024-07-30 18:48:50.382 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-30 18:48:50.383 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-30 18:48:50.421 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] start preload schema,table counts: 1 
[INFO ] 2024-07-30 18:48:50.422 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] start preload schema,table counts: 1 
[INFO ] 2024-07-30 18:48:50.452 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] preload schema finished, cost 29 ms 
[INFO ] 2024-07-30 18:48:50.452 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] preload schema finished, cost 30 ms 
[INFO ] 2024-07-30 18:48:51.463 - [任务 1][BMSQL_CONFIG] - Write batch size: 100, max wait ms per batch: 500 
[ERROR] 2024-07-30 18:49:00.072 - [任务 1][BMSQL_CONFIG] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@5b71b3f0 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@5b71b3f0 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more


<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@5b71b3f0 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2759/1718100133@5b71b3f0 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to init pdk connector, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:179)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	... 7 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:115)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	... 8 more
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[INFO ] 2024-07-30 18:49:00.090 - [任务 1][BMSQL_CONFIG] - Job suspend in error handle 
[INFO ] 2024-07-30 18:49:00.091 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] running status set to false 
[INFO ] 2024-07-30 18:49:00.105 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 18:49:00.105 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastSourcePdkDataNode-251bf2f1-0b81-4741-a9d9-0d9f05581e08 
[INFO ] 2024-07-30 18:49:00.105 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] schema data cleaned 
[INFO ] 2024-07-30 18:49:00.107 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] monitor closed 
[INFO ] 2024-07-30 18:49:00.110 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[251bf2f1-0b81-4741-a9d9-0d9f05581e08] close complete, cost 19 ms 
[INFO ] 2024-07-30 18:49:00.110 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] running status set to false 
[INFO ] 2024-07-30 18:49:00.119 - [任务 1][BMSQL_CONFIG] - PDK connector node stopped: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 18:49:00.119 - [任务 1][BMSQL_CONFIG] - PDK connector node released: HazelcastTargetPdkDataNode-d72b3763-83af-4956-88f7-003265f73fc8 
[INFO ] 2024-07-30 18:49:00.119 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] schema data cleaned 
[INFO ] 2024-07-30 18:49:00.119 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] monitor closed 
[INFO ] 2024-07-30 18:49:00.120 - [任务 1][BMSQL_CONFIG] - Node BMSQL_CONFIG[d72b3763-83af-4956-88f7-003265f73fc8] close complete, cost 10 ms 
[INFO ] 2024-07-30 18:49:28.677 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 18:49:28.687 - [任务 1] - Stop task milestones: 66a8b34b4e88ce426f2f2f87(任务 1)  
[INFO ] 2024-07-30 18:49:28.687 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-30 18:49:28.703 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-30 18:49:28.705 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
[INFO ] 2024-07-30 18:49:28.705 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a8b34b4e88ce426f2f2f87] 
