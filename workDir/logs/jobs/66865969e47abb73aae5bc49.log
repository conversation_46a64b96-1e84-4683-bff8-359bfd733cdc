[INFO ] 2024-07-15 03:31:52.640 - [测试挖掘+心跳任务 Mongo-Mong] - Task initialization... 
[INFO ] 2024-07-15 03:31:52.673 - [测试挖掘+心跳任务 Mongo-Mong] - Start task milestones: 66865969e47abb73aae5bc49(测试挖掘+心跳任务 Mongo-Mong) 
[INFO ] 2024-07-15 03:31:53.897 - [测试挖掘+心跳任务 Mongo-Mong] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-15 03:31:53.974 - [测试挖掘+心跳任务 Mongo-Mong] - The engine receives 测试挖掘+心跳任务 Mongo-Mong task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 03:31:55.034 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Node TargetMongo[d5206848-6518-4aa7-96cb-e5dc7dbe9848] start preload schema,table counts: 2 
[INFO ] 2024-07-15 03:31:55.037 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Node SourceMongo[f2b95712-ee5d-4663-b9e6-57fb200c3f08] start preload schema,table counts: 2 
[INFO ] 2024-07-15 03:31:55.041 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Node TargetMongo[d5206848-6518-4aa7-96cb-e5dc7dbe9848] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 03:31:55.044 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Node SourceMongo[f2b95712-ee5d-4663-b9e6-57fb200c3f08] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 03:31:56.418 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Node(TargetMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-15 03:31:56.418 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-15 03:31:56.419 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 03:31:56.419 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-15 03:31:56.419 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Sync progress not exists, will run task as first time 
[INFO ] 2024-07-15 03:31:56.419 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 03:31:56.434 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Sync progress not exists, will run task as first time 
[INFO ] 2024-07-15 03:31:56.639 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1720985516,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 03:31:56.706 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Initial sync started 
[INFO ] 2024-07-15 03:31:56.710 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-15 03:31:56.860 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-15 03:31:56.860 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-15 03:31:57.219 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 03:31:57.233 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 03:31:57.234 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 03:31:57.381 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 03:31:57.382 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 03:31:57.386 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-15 03:31:57.387 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-15 03:31:57.399 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-15 03:31:57.472 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 03:31:57.474 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-15 03:31:57.475 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试挖掘+心跳任务 Mongo-Mong enable share cdc: true 
[INFO ] 2024-07-15 03:31:57.533 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-15 03:31:57.607 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 03:31:57.607 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=668659a566ab5ede8a1d5ead, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-1027434295, shareCdcTaskId=668659a5e47abb73aae5bcd7, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-15 03:31:57.792 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_CUSTOMER_测试挖掘+心跳任务 Mongo-Mong', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1027434295', head seq: 0, tail seq: 7 
[INFO ] 2024-07-15 03:31:57.795 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-15 03:31:57.855 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_测试挖掘+心跳任务 Mongo-Mong', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 716 
[INFO ] 2024-07-15 03:31:57.855 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=668659a566ab5ede8a1d5eac, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-233661435, shareCdcTaskId=668659a5e47abb73aae5bcd7, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-15 03:31:57.897 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务__tapdata_heartbeat_table_测试挖掘+心跳任务 Mongo-Mong', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-233661435', head seq: 0, tail seq: 181025 
[INFO ] 2024-07-15 03:31:57.898 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 03:31:57.900 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-15 03:31:57.901 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 03:31:57.902 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 03:31:57.902 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 3, partition size: 1, read thread number: 3 
[INFO ] 2024-07-15 03:31:57.921 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=668659a566ab5ede8a1d5ead, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-1027434295, shareCdcTaskId=668659a5e47abb73aae5bcd7, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-15 03:31:57.922 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_CUSTOMER_测试挖掘+心跳任务 Mongo-Mong', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1027434295', head seq: 0, tail seq: 7 
[INFO ] 2024-07-15 03:31:57.923 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_CUSTOMER_测试挖掘+心跳任务 Mongo-Mong, external storage name: ExternalStorage_SHARE_CDC_-1027434295 
[INFO ] 2024-07-15 03:31:57.923 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-07-15 03:31:57.939 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-15 03:31:57.940 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-07-14T19:31:56.417Z): 8 
[INFO ] 2024-07-15 03:31:57.946 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 8 
[INFO ] 2024-07-15 03:31:57.947 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_测试挖掘+心跳任务 Mongo-Mong', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 716 
[INFO ] 2024-07-15 03:31:57.956 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=8} 
[INFO ] 2024-07-15 03:31:57.956 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_测试挖掘+心跳任务 Mongo-Mong, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-15 03:31:57.979 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 03:31:57.980 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-14T19:31:56.417Z): 717 
[INFO ] 2024-07-15 03:31:57.980 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=668659a566ab5ede8a1d5eac, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-233661435, shareCdcTaskId=668659a5e47abb73aae5bcd7, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-15 03:31:57.980 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 717 
[INFO ] 2024-07-15 03:31:57.980 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=717} 
[INFO ] 2024-07-15 03:31:57.983 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务__tapdata_heartbeat_table_测试挖掘+心跳任务 Mongo-Mong', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-233661435', head seq: 0, tail seq: 181025 
[INFO ] 2024-07-15 03:31:57.994 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务__tapdata_heartbeat_table_测试挖掘+心跳任务 Mongo-Mong, external storage name: ExternalStorage_SHARE_CDC_-233661435 
[INFO ] 2024-07-15 03:31:57.995 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-15 03:31:58.007 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(_tapdata_heartbeat_table) by timestamp(2024-07-14T19:31:56.417Z): 181026 
[INFO ] 2024-07-15 03:31:58.011 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 03:31:58.015 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 181026 
[INFO ] 2024-07-15 03:31:58.015 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=181026} 
[INFO ] 2024-07-15 03:35:38.110 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1720985730000, date=Mon Jul 15 03:35:30 CST 2024, before=Document{{}}, after=Document{{_id=6690d93166ab5ede8a72876e, id=6690d8be457e901dd0b0ef12, ts=Mon Jul 15 03:35:29 CST 2024}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2OTQyODgyMDAwMDAwMUYyQjAyMkMwMTAwMjk2RTVBMTAwNDEx
NDM0QkVCRDBCMzRDMUNCOTBEQTM2QjQ5MEYxQ0U1NDY2NDVGNjk2NDAwNjQ2NjkwRDkzMTY2QUI1
RURFOEE3Mjg3NkUwMDA0In2o
, type=DATA, connectionId=6674feb868ca1e3afc2a0d99, isReplaceEvent=false, _ts=1720985731}} 
[INFO ] 2024-07-15 03:39:07.702 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Node SourceMongo[f2b95712-ee5d-4663-b9e6-57fb200c3f08] running status set to false 
[INFO ] 2024-07-15 03:39:07.740 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-15 03:39:07.741 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-f2b95712-ee5d-4663-b9e6-57fb200c3f08 
[INFO ] 2024-07-15 03:39:07.741 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-f2b95712-ee5d-4663-b9e6-57fb200c3f08 
[INFO ] 2024-07-15 03:39:07.741 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Node SourceMongo[f2b95712-ee5d-4663-b9e6-57fb200c3f08] schema data cleaned 
[INFO ] 2024-07-15 03:39:07.742 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Node SourceMongo[f2b95712-ee5d-4663-b9e6-57fb200c3f08] monitor closed 
[INFO ] 2024-07-15 03:39:07.744 - [测试挖掘+心跳任务 Mongo-Mong][SourceMongo] - Node SourceMongo[f2b95712-ee5d-4663-b9e6-57fb200c3f08] close complete, cost 68 ms 
[INFO ] 2024-07-15 03:39:07.744 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Node TargetMongo[d5206848-6518-4aa7-96cb-e5dc7dbe9848] running status set to false 
[INFO ] 2024-07-15 03:39:07.766 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-d5206848-6518-4aa7-96cb-e5dc7dbe9848 
[INFO ] 2024-07-15 03:39:07.766 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - PDK connector node released: HazelcastTargetPdkDataNode-d5206848-6518-4aa7-96cb-e5dc7dbe9848 
[INFO ] 2024-07-15 03:39:07.767 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Node TargetMongo[d5206848-6518-4aa7-96cb-e5dc7dbe9848] schema data cleaned 
[INFO ] 2024-07-15 03:39:07.768 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Node TargetMongo[d5206848-6518-4aa7-96cb-e5dc7dbe9848] monitor closed 
[INFO ] 2024-07-15 03:39:07.978 - [测试挖掘+心跳任务 Mongo-Mong][TargetMongo] - Node TargetMongo[d5206848-6518-4aa7-96cb-e5dc7dbe9848] close complete, cost 24 ms 
[INFO ] 2024-07-15 03:39:11.683 - [测试挖掘+心跳任务 Mongo-Mong] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 03:39:11.686 - [测试挖掘+心跳任务 Mongo-Mong] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57d9606b 
[INFO ] 2024-07-15 03:39:11.694 - [测试挖掘+心跳任务 Mongo-Mong] - Stop task milestones: 66865969e47abb73aae5bc49(测试挖掘+心跳任务 Mongo-Mong)  
[INFO ] 2024-07-15 03:39:11.811 - [测试挖掘+心跳任务 Mongo-Mong] - Stopped task aspect(s) 
[INFO ] 2024-07-15 03:39:11.811 - [测试挖掘+心跳任务 Mongo-Mong] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 03:39:11.834 - [测试挖掘+心跳任务 Mongo-Mong] - Remove memory task client succeed, task: 测试挖掘+心跳任务 Mongo-Mong[66865969e47abb73aae5bc49] 
[INFO ] 2024-07-15 03:39:11.834 - [测试挖掘+心跳任务 Mongo-Mong] - Destroy memory task client cache succeed, task: 测试挖掘+心跳任务 Mongo-Mong[66865969e47abb73aae5bc49] 
