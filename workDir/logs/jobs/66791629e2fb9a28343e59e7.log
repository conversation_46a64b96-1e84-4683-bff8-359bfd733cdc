[INFO ] 2024-06-25 14:22:52.092 - [任务 18] - Task initialization... 
[INFO ] 2024-06-25 14:22:52.094 - [任务 18] - Start task milestones: 66791629e2fb9a28343e59e7(任务 18) 
[INFO ] 2024-06-25 14:22:52.370 - [任务 18] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:22:52.486 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:22:52.486 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:22:52.487 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:22:52.487 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:22:52.487 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:22:53.266 - [任务 18][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:22:53.266 - [任务 18][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:22:53.434 - [任务 18][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 14:22:53.434 - [任务 18][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 14:22:53.439 - [任务 18][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 14:22:53.439 - [任务 18][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-25 14:22:53.501 - [任务 18][SouceMysql] - Initial sync started 
[INFO ] 2024-06-25 14:22:53.501 - [任务 18][SouceMysql] - Starting batch read, table name: CLAIM1, offset: null 
[INFO ] 2024-06-25 14:22:53.513 - [任务 18][SouceMysql] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-06-25 14:22:53.542 - [任务 18][SouceMysql] - Table [CLAIM1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:22:53.542 - [任务 18][SouceMysql] - Query table 'CLAIM1' counts: 0 
[INFO ] 2024-06-25 14:22:53.554 - [任务 18][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 14:22:53.556 - [任务 18][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 14:22:53.767 - [任务 18][SouceMysql] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 14:22:53.772 - [任务 18][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:22:53.775 - [任务 18][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 14:22:53.777 - [任务 18][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 14:22:53.777 - [任务 18][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 14:22:53.777 - [任务 18][SouceMysql] - Starting stream read, table list: [CLAIM1, CLAIM], offset: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-25 14:22:53.826 - [任务 18][SouceMysql] - Starting mysql cdc, server name: bb66e246-e2f5-4c14-90c0-34ac046633b4 
[INFO ] 2024-06-25 14:22:53.827 - [任务 18][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1833201941
  time.precision.mode: adaptive_time_microseconds
  database.server.name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.port: 3306
  threadName: Debezium-Mysql-Connector-bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.hostname: localhost
  database.password: ********
  name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  pdk.offset.string: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM1,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 14:22:54.035 - [任务 18][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM1, CLAIM], data change syncing 
[INFO ] 2024-06-25 14:29:32.751 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] running status set to false 
[INFO ] 2024-06-25 14:37:17.216 - [任务 18] - Start task milestones: 66791629e2fb9a28343e59e7(任务 18) 
[INFO ] 2024-06-25 14:37:17.220 - [任务 18] - Task initialization... 
[INFO ] 2024-06-25 14:37:18.689 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:37:18.900 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:37:19.624 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:37:19.643 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:37:19.643 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:37:19.661 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:37:20.780 - [任务 18][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:37:20.782 - [任务 18][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:37:20.866 - [任务 18][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 14:37:20.866 - [任务 18][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 14:37:20.867 - [任务 18][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-25 14:37:20.909 - [任务 18][SouceMysql] - batch offset found: {"CLAIM1":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"CLAIM":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"ts_sec\":1719296574,\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-25 14:37:21.049 - [任务 18][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 14:37:21.050 - [任务 18][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 14:37:21.066 - [任务 18][SouceMysql] - Starting stream read, table list: [CLAIM1, CLAIM], offset: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"ts_sec\":1719296574,\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-25 14:37:21.133 - [任务 18][SouceMysql] - Starting mysql cdc, server name: bb66e246-e2f5-4c14-90c0-34ac046633b4 
[INFO ] 2024-06-25 14:37:21.196 - [任务 18][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 101027046
  time.precision.mode: adaptive_time_microseconds
  database.server.name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.port: 3306
  threadName: Debezium-Mysql-Connector-bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.hostname: localhost
  database.password: ********
  name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  pdk.offset.string: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"ts_sec\":1719296574,\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM1,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 14:37:22.235 - [任务 18][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM1, CLAIM], data change syncing 
[INFO ] 2024-06-25 16:01:00.729 - [任务 18][SouceMysql] - Mysql binlog reader stopped 
[WARN ] 2024-06-25 16:01:00.730 - [任务 18][SouceMysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Unknown exception occur when operate table: unknown
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 16:02:00.802 - [任务 18][SouceMysql] - Starting mysql cdc, server name: bb66e246-e2f5-4c14-90c0-34ac046633b4 
[INFO ] 2024-06-25 16:02:00.802 - [任务 18][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 261247254
  time.precision.mode: adaptive_time_microseconds
  database.server.name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.port: 3306
  threadName: Debezium-Mysql-Connector-bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.hostname: localhost
  database.password: ********
  name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  pdk.offset.string: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990386,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM1,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:02:00.903 - [任务 18][SouceMysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-06-25 16:02:00.903 - [任务 18][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM1, CLAIM], data change syncing 
[WARN ] 2024-06-25 16:34:22.038 - [任务 18][SourceMongo] - Save to snapshot failed, collection: Task/syncProgress/66791629e2fb9a28343e59e7, object: {13e7bfcb-96cd-4add-b441-f36c7e2b8fb4,f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd=SyncProgress{eventSerialNo=146, syncStage='CDC', batchOffset='{}', streamOffset='MysqlStreamOffset{name='bb66e246-e2f5-4c14-90c0-34ac046633b4', offset={{"server":"bb66e246-e2f5-4c14-90c0-34ac046633b4"}={"file":"binlog.000031","pos":1060990946,"server_id":1}}}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/66791629e2fb9a28343e59e7": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-06-25 16:34:27.365 - [任务 18] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-06-25 16:34:27.580 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] running status set to false 
[INFO ] 2024-06-25 16:34:27.595 - [任务 18][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:34:27.605 - [任务 18][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:34:27.605 - [任务 18][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-13e7bfcb-96cd-4add-b441-f36c7e2b8fb4 
[INFO ] 2024-06-25 16:34:27.605 - [任务 18][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-13e7bfcb-96cd-4add-b441-f36c7e2b8fb4 
[INFO ] 2024-06-25 16:34:27.606 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] schema data cleaned 
[INFO ] 2024-06-25 16:34:27.608 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] monitor closed 
[INFO ] 2024-06-25 16:34:27.610 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] close complete, cost 67 ms 
[INFO ] 2024-06-25 16:34:27.610 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] running status set to false 
[INFO ] 2024-06-25 16:38:42.617 - [任务 18][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd 
[INFO ] 2024-06-25 16:38:42.617 - [任务 18][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd 
[INFO ] 2024-06-25 16:38:42.617 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] schema data cleaned 
[INFO ] 2024-06-25 16:38:42.617 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] monitor closed 
[INFO ] 2024-06-25 16:38:42.624 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] close complete, cost 255012 ms 
[INFO ] 2024-06-25 16:38:44.068 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 16:38:44.068 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c61b73b 
[INFO ] 2024-06-25 16:38:44.079 - [任务 18] - Stop task milestones: 66791629e2fb9a28343e59e7(任务 18)  
[INFO ] 2024-06-25 16:38:44.079 - [任务 18] - Stopped task aspect(s) 
[INFO ] 2024-06-25 16:38:44.079 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 16:38:44.079 - [任务 18] - Remove memory task client succeed, task: 任务 18[66791629e2fb9a28343e59e7] 
[INFO ] 2024-06-25 16:38:44.081 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[66791629e2fb9a28343e59e7] 
[INFO ] 2024-06-25 16:43:48.465 - [任务 18] - Start task milestones: 66791629e2fb9a28343e59e7(任务 18) 
[INFO ] 2024-06-25 16:43:48.468 - [任务 18] - Task initialization... 
[INFO ] 2024-06-25 16:43:48.676 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 16:43:48.736 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 16:43:48.871 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] start preload schema,table counts: 2 
[INFO ] 2024-06-25 16:43:48.871 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] start preload schema,table counts: 2 
[INFO ] 2024-06-25 16:43:48.871 - [任务 18][SourceMongo] - Node SourceMongo[f4c3c5c5-b4eb-4b11-bdda-bdaf3c8f2dbd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:43:48.872 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 16:43:49.388 - [任务 18][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 16:43:49.388 - [任务 18][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 16:43:49.689 - [任务 18][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 16:43:49.689 - [任务 18][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 16:43:49.689 - [任务 18][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-25 16:43:49.693 - [任务 18][SouceMysql] - batch offset found: {},stream offset found: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:43:49.746 - [任务 18][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:43:49.746 - [任务 18][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:43:49.747 - [任务 18][SouceMysql] - Starting stream read, table list: [CLAIM1, CLAIM], offset: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:43:49.764 - [任务 18][SouceMysql] - Starting mysql cdc, server name: bb66e246-e2f5-4c14-90c0-34ac046633b4 
[INFO ] 2024-06-25 16:43:49.766 - [任务 18][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 530749290
  time.precision.mode: adaptive_time_microseconds
  database.server.name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.port: 3306
  threadName: Debezium-Mysql-Connector-bb66e246-e2f5-4c14-90c0-34ac046633b4
  database.hostname: localhost
  database.password: ********
  name: bb66e246-e2f5-4c14-90c0-34ac046633b4
  pdk.offset.string: {"name":"bb66e246-e2f5-4c14-90c0-34ac046633b4","offset":{"{\"server\":\"bb66e246-e2f5-4c14-90c0-34ac046633b4\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM1,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:43:49.968 - [任务 18][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM1, CLAIM], data change syncing 
[INFO ] 2024-06-25 16:58:42.676 - [任务 18][SouceMysql] - Node SouceMysql[13e7bfcb-96cd-4add-b441-f36c7e2b8fb4] running status set to false 
[INFO ] 2024-06-25 16:58:42.765 - [任务 18][SouceMysql] - Incremental sync completed 
[WARN ] 2024-06-25 16:58:42.769 - [任务 18][SouceMysql] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-13e7bfcb-96cd-4add-b441-f36c7e2b8fb4 
