[INFO ] 2024-07-01 15:20:19.251 - [任务 1] - Task initialization... 
[INFO ] 2024-07-01 15:20:19.462 - [任务 1] - Start task milestones: 66825890c94d2f5d40a10e91(任务 1) 
[INFO ] 2024-07-01 15:20:20.143 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-01 15:20:20.143 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 15:20:20.637 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] start preload schema,table counts: 2 
[INFO ] 2024-07-01 15:20:20.641 - [任务 1][Mongo] - Node <PERSON>[c689125e-7c7d-44cb-9b67-4670ef6c196a] start preload schema,table counts: 2 
[INFO ] 2024-07-01 15:20:20.641 - [任务 1][Mysql] - Node <PERSON>[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:20:20.641 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:20:21.514 - [任务 1][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-01 15:20:21.515 - [任务 1][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-01 15:20:21.515 - [任务 1][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 15:20:21.521 - [任务 1][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 15:20:21.690 - [任务 1][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 15:20:21.691 - [任务 1][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1719818421,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:20:21.811 - [任务 1][Mongo] - Initial sync started 
[INFO ] 2024-07-01 15:20:21.833 - [任务 1][Mongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-01 15:20:21.834 - [任务 1][Mongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-01 15:20:21.946 - [任务 1][Mongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-01 15:20:21.946 - [任务 1][Mongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:20:21.946 - [任务 1][Mongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 15:20:21.947 - [任务 1][Mongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 15:20:22.067 - [任务 1][Mongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 15:20:22.068 - [任务 1][Mongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:20:22.068 - [任务 1][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:20:22.069 - [任务 1][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-01 15:20:22.078 - [任务 1][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:20:22.079 - [任务 1][Mongo] - Starting stream read, table list: [POLICY, CLAIM], offset: {"cdcOffset":1719818421,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:20:22.283 - [任务 1][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, CLAIM], data change syncing 
[INFO ] 2024-07-01 15:22:51.807 - [任务 1] - Stop task milestones: 66825890c94d2f5d40a10e91(任务 1)  
[INFO ] 2024-07-01 15:22:51.859 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] running status set to false 
[INFO ] 2024-07-01 15:22:51.877 - [任务 1][Mongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-c689125e-7c7d-44cb-9b67-4670ef6c196a 
[INFO ] 2024-07-01 15:22:51.877 - [任务 1][Mongo] - PDK connector node released: HazelcastSourcePdkDataNode-c689125e-7c7d-44cb-9b67-4670ef6c196a 
[INFO ] 2024-07-01 15:22:51.881 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] schema data cleaned 
[INFO ] 2024-07-01 15:22:51.881 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] monitor closed 
[INFO ] 2024-07-01 15:22:51.885 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] close complete, cost 29 ms 
[INFO ] 2024-07-01 15:22:51.885 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] running status set to false 
[INFO ] 2024-07-01 15:22:51.929 - [任务 1][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-481417b8-1a0e-4a32-9f3f-fb3396a95d2a 
[INFO ] 2024-07-01 15:22:51.929 - [任务 1][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-481417b8-1a0e-4a32-9f3f-fb3396a95d2a 
[INFO ] 2024-07-01 15:22:51.929 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] schema data cleaned 
[INFO ] 2024-07-01 15:22:51.930 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] monitor closed 
[INFO ] 2024-07-01 15:22:51.930 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] close complete, cost 44 ms 
[INFO ] 2024-07-01 15:22:56.853 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 15:22:56.854 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-01 15:22:56.937 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 15:22:56.937 - [任务 1] - Remove memory task client succeed, task: 任务 1[66825890c94d2f5d40a10e91] 
[INFO ] 2024-07-01 15:22:57.139 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66825890c94d2f5d40a10e91] 
[INFO ] 2024-07-01 15:23:35.937 - [任务 1] - Task initialization... 
[INFO ] 2024-07-01 15:23:36.143 - [任务 1] - Start task milestones: 66825890c94d2f5d40a10e91(任务 1) 
[INFO ] 2024-07-01 15:23:36.181 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 15:23:36.182 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 15:23:36.235 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] start preload schema,table counts: 2 
[INFO ] 2024-07-01 15:23:36.235 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] start preload schema,table counts: 2 
[INFO ] 2024-07-01 15:23:36.235 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:23:36.235 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:23:36.897 - [任务 1][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-01 15:23:36.897 - [任务 1][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-01 15:23:36.897 - [任务 1][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 15:23:36.989 - [任务 1][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1719818616,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:23:37.038 - [任务 1][Mongo] - Initial sync started 
[INFO ] 2024-07-01 15:23:37.045 - [任务 1][Mongo] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-01 15:23:37.075 - [任务 1][Mongo] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-01 15:23:37.075 - [任务 1][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 15:23:37.106 - [任务 1][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 15:23:37.107 - [任务 1][Mongo] - Query table 'CUSTOMER' counts: 674 
[INFO ] 2024-07-01 15:23:37.281 - [任务 1][Mongo] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:23:37.286 - [任务 1][Mongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 15:23:37.286 - [任务 1][Mongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 15:23:37.382 - [任务 1][Mongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 15:23:37.383 - [任务 1][Mongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:23:37.383 - [任务 1][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:23:37.386 - [任务 1][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-01 15:23:37.387 - [任务 1][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:23:37.387 - [任务 1][Mongo] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"cdcOffset":1719818616,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:23:37.588 - [任务 1][Mongo] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-07-01 15:24:22.543 - [任务 1] - Stop task milestones: 66825890c94d2f5d40a10e91(任务 1)  
[INFO ] 2024-07-01 15:24:22.716 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] running status set to false 
[INFO ] 2024-07-01 15:24:22.729 - [任务 1][Mongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-c689125e-7c7d-44cb-9b67-4670ef6c196a 
[INFO ] 2024-07-01 15:24:22.729 - [任务 1][Mongo] - PDK connector node released: HazelcastSourcePdkDataNode-c689125e-7c7d-44cb-9b67-4670ef6c196a 
[INFO ] 2024-07-01 15:24:22.729 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] schema data cleaned 
[INFO ] 2024-07-01 15:24:22.732 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] monitor closed 
[INFO ] 2024-07-01 15:24:22.739 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] close complete, cost 21 ms 
[INFO ] 2024-07-01 15:24:22.739 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] running status set to false 
[INFO ] 2024-07-01 15:24:22.762 - [任务 1][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-481417b8-1a0e-4a32-9f3f-fb3396a95d2a 
[INFO ] 2024-07-01 15:24:22.762 - [任务 1][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-481417b8-1a0e-4a32-9f3f-fb3396a95d2a 
[INFO ] 2024-07-01 15:24:22.764 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] schema data cleaned 
[INFO ] 2024-07-01 15:24:22.764 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] monitor closed 
[INFO ] 2024-07-01 15:24:22.974 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] close complete, cost 31 ms 
[INFO ] 2024-07-01 15:24:27.105 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 15:24:27.105 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-01 15:24:27.105 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 15:24:27.149 - [任务 1] - Remove memory task client succeed, task: 任务 1[66825890c94d2f5d40a10e91] 
[INFO ] 2024-07-01 15:24:27.149 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66825890c94d2f5d40a10e91] 
[INFO ] 2024-07-01 15:25:25.546 - [任务 1] - Task initialization... 
[INFO ] 2024-07-01 15:25:25.547 - [任务 1] - Start task milestones: 66825890c94d2f5d40a10e91(任务 1) 
[INFO ] 2024-07-01 15:25:25.931 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 15:25:25.932 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 15:25:26.038 - [任务 1][表编辑] - Node 表编辑[42fc5072-d374-48cc-bc48-691a794d7902] start preload schema,table counts: 2 
[INFO ] 2024-07-01 15:25:26.040 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] start preload schema,table counts: 2 
[INFO ] 2024-07-01 15:25:26.040 - [任务 1][表编辑] - Node 表编辑[42fc5072-d374-48cc-bc48-691a794d7902] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:25:26.040 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:25:26.072 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] start preload schema,table counts: 2 
[INFO ] 2024-07-01 15:25:26.072 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:25:26.779 - [任务 1][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 15:25:26.785 - [任务 1][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 15:25:26.785 - [任务 1][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-01 15:25:26.785 - [任务 1][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-01 15:25:26.788 - [任务 1][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 15:25:26.989 - [任务 1][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1719818726,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:25:27.020 - [任务 1][Mongo] - Initial sync started 
[INFO ] 2024-07-01 15:25:27.020 - [任务 1][Mongo] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-01 15:25:27.030 - [任务 1][Mongo] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-01 15:25:27.170 - [任务 1][Mongo] - Query table 'CUSTOMER' counts: 674 
[INFO ] 2024-07-01 15:25:27.170 - [任务 1][Mongo] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:25:27.173 - [任务 1][Mongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 15:25:27.173 - [任务 1][Mongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 15:25:27.283 - [任务 1][Mongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 15:25:27.293 - [任务 1][Mongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:25:27.293 - [任务 1][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:25:27.293 - [任务 1][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-01 15:25:27.293 - [任务 1][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:25:27.294 - [任务 1][Mongo] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"cdcOffset":1719818726,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:25:27.307 - [任务 1][Mongo] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-07-01 15:28:12.489 - [任务 1] - Stop task milestones: 66825890c94d2f5d40a10e91(任务 1)  
[INFO ] 2024-07-01 15:28:12.756 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] running status set to false 
[INFO ] 2024-07-01 15:28:12.780 - [任务 1][Mongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-c689125e-7c7d-44cb-9b67-4670ef6c196a 
[INFO ] 2024-07-01 15:28:12.780 - [任务 1][Mongo] - PDK connector node released: HazelcastSourcePdkDataNode-c689125e-7c7d-44cb-9b67-4670ef6c196a 
[INFO ] 2024-07-01 15:28:12.785 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] schema data cleaned 
[INFO ] 2024-07-01 15:28:12.785 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] monitor closed 
[INFO ] 2024-07-01 15:28:12.804 - [任务 1][Mongo] - Node Mongo[c689125e-7c7d-44cb-9b67-4670ef6c196a] close complete, cost 39 ms 
[INFO ] 2024-07-01 15:28:12.805 - [任务 1][表编辑] - Node 表编辑[42fc5072-d374-48cc-bc48-691a794d7902] running status set to false 
[INFO ] 2024-07-01 15:28:12.805 - [任务 1][表编辑] - Node 表编辑[42fc5072-d374-48cc-bc48-691a794d7902] schema data cleaned 
[INFO ] 2024-07-01 15:28:12.805 - [任务 1][表编辑] - Node 表编辑[42fc5072-d374-48cc-bc48-691a794d7902] monitor closed 
[INFO ] 2024-07-01 15:28:12.805 - [任务 1][表编辑] - Node 表编辑[42fc5072-d374-48cc-bc48-691a794d7902] close complete, cost 14 ms 
[INFO ] 2024-07-01 15:28:12.805 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] running status set to false 
[INFO ] 2024-07-01 15:28:12.822 - [任务 1][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-481417b8-1a0e-4a32-9f3f-fb3396a95d2a 
[INFO ] 2024-07-01 15:28:12.824 - [任务 1][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-481417b8-1a0e-4a32-9f3f-fb3396a95d2a 
[INFO ] 2024-07-01 15:28:12.825 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] schema data cleaned 
[INFO ] 2024-07-01 15:28:12.825 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] monitor closed 
[INFO ] 2024-07-01 15:28:12.830 - [任务 1][Mysql] - Node Mysql[481417b8-1a0e-4a32-9f3f-fb3396a95d2a] close complete, cost 17 ms 
[INFO ] 2024-07-01 15:28:17.385 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 15:28:17.385 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-01 15:28:17.444 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 15:28:17.444 - [任务 1] - Remove memory task client succeed, task: 任务 1[66825890c94d2f5d40a10e91] 
[INFO ] 2024-07-01 15:28:17.444 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66825890c94d2f5d40a10e91] 
