[INFO ] 2024-12-18 03:30:22.861 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 03:30:23.261 - [CDC log cache task from MongoTarget] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-12-18 03:30:23.262 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 03:30:23.359 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 1 
[INFO ] 2024-12-18 03:30:23.359 - [CDC log cache task from MongoTarget][MongoTarget] - <PERSON><PERSON>[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 03:30:23.430 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 03:30:23.430 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 03:30:23.492 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 03:30:24.386 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 03:30:24.781 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 03:30:24.781 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 03:30:24.781 - [CDC log cache task from MongoTarget][MongoTarget] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 03:30:24.973 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 03:30:25.008 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CLAIM], offset: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 03:30:25.024 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 12:06:55.676 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 12:12:52.228 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 12:12:52.394 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 12:12:52.418 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:12:52.522 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 4 
[INFO ] 2024-12-18 12:12:52.527 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:12:52.534 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 12:12:52.544 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 12:12:52.680 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:12:52.710 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:12:52.712 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:12:52.713 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:12:53.129 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 12:12:53.129 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 12:12:53.131 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 12:12:53.289 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 12:12:53.418 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CLAIM.2, CAR.CLAIM, CAR.CLAIM.3, CAR.COLLECTION], offset: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 12:12:53.426 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 12:12:53.578 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CLAIM.2, CAR.CLAIM, CAR.CLAIM.3, CAR.COLLECTION], data change syncing 
[INFO ] 2024-12-18 12:57:52.982 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 12:57:52.982 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734495172926 
[INFO ] 2024-12-18 12:57:52.982 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734495172926 
[INFO ] 2024-12-18 12:57:52.983 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 12:57:52.983 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 12:57:52.984 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 60 ms 
[INFO ] 2024-12-18 12:57:52.985 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 12:57:53.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 12:57:53.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 12:57:53.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 12:57:53.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 12:57:53.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 45 ms 
[INFO ] 2024-12-18 12:57:53.903 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 12:57:53.904 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@78f92798 
[INFO ] 2024-12-18 12:57:54.021 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 12:57:54.034 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 12:57:54.034 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 12:57:54.080 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 12:57:54.082 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 12:58:43.304 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 12:58:43.431 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 12:58:43.431 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:58:43.501 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 12:58:43.507 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 5 
[INFO ] 2024-12-18 12:58:43.507 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 12:58:43.507 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:58:43.526 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:58:43.526 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:58:43.526 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:58:43.526 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:58:43.536 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 12:58:43.536 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:58:43.561 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 12:58:43.561 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 12:58:43.679 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 12:58:43.679 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 12:58:43.679 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 12:58:43.775 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 12:58:43.777 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 12:58:43.777 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.POLICY, CAR.CLAIM.2, CAR.CLAIM.3, CAR.COLLECTION], offset: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 12:58:43.985 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY, CAR.CLAIM.2, CAR.CLAIM.3, CAR.COLLECTION], data change syncing 
[INFO ] 2024-12-18 13:05:43.882 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 13:09:38.863 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 13:09:38.970 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:09:39.080 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:09:39.080 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 8 
[INFO ] 2024-12-18 13:09:39.080 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:09:39.086 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:09:39.086 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 13:09:39.124 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.124 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.124 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.124 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.124 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.124 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.150 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.278 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:39.279 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 13:09:39.423 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:09:39.424 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:09:39.424 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:09:39.430 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:09:39.611 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.POLICY.3, CAR.POLICY, CAR.CLAIM, CAR.COLLECTION, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1], offset: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:09:39.611 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY.3, CAR.POLICY, CAR.CLAIM, CAR.COLLECTION, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1], data change syncing 
[INFO ] 2024-12-18 13:16:53.385 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[WARN ] 2024-12-18 13:16:53.405 - [CDC log cache task from MongoTarget][MongoTarget] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734498579254 
[INFO ] 2024-12-18 13:16:53.443 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734498579254 
[INFO ] 2024-12-18 13:16:53.447 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 13:21:50.564 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 13:21:50.727 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:21:50.764 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:21:50.975 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 11 
[INFO ] 2024-12-18 13:21:50.976 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:21:50.976 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 13:21:50.976 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:21:51.029 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.035 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.035 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.035 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.035 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.035 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.035 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.245 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.484 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:21:51.551 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:21:51.551 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:21:51.560 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:21:51.616 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:21:51.705 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, CAR.COLLECTION, CLAIMTYPE, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:21:51.921 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, CAR.COLLECTION, CLAIMTYPE, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 13:21:51.949 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 13:58:36.036 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 13:58:36.053 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734499311411 
[INFO ] 2024-12-18 13:58:36.053 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734499311411 
[INFO ] 2024-12-18 13:58:36.053 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 13:58:36.053 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 13:58:36.054 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 19 ms 
[INFO ] 2024-12-18 13:58:36.054 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 13:58:36.073 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 13:58:36.073 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 13:58:36.073 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 13:58:36.073 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 13:58:36.275 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 19 ms 
[INFO ] 2024-12-18 13:58:39.495 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:58:39.495 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@69c3e732 
[INFO ] 2024-12-18 13:58:39.608 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 13:58:39.628 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:58:39.628 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:58:39.653 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 13:58:39.654 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:01:02.303 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:01:02.577 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:01:02.660 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:01:02.661 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:01:02.661 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:01:02.675 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:01:02.677 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:01:02.711 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.712 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.719 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.725 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.726 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.743 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:01:02.743 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.751 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.751 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.751 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.752 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.755 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.755 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.756 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.756 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.764 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.774 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.774 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.775 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.786 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:02.787 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:02.988 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:01:03.183 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:01:03.183 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:01:03.183 - [CDC log cache task from MongoTarget][MongoTarget] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:01:03.332 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:01:03.429 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:01:03.634 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:01:21.928 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:01:21.928 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501663063 
[INFO ] 2024-12-18 14:01:21.928 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501663063 
[INFO ] 2024-12-18 14:01:21.928 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:01:21.928 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:01:21.929 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 24 ms 
[INFO ] 2024-12-18 14:01:21.929 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:01:21.954 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:01:21.954 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:01:21.954 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:01:21.955 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:01:22.156 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 25 ms 
[INFO ] 2024-12-18 14:01:24.804 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:01:24.914 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c840014 
[INFO ] 2024-12-18 14:01:24.914 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:01:24.934 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:01:24.935 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:01:24.971 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:01:24.974 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:01:39.606 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:01:39.763 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:01:39.763 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:01:39.843 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:01:39.843 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:01:39.845 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:01:39.851 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:01:39.895 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.896 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.908 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.923 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.931 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.933 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.933 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.933 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.940 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.940 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.940 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:39.946 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:01:39.952 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.978 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.980 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.988 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:39.988 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:40.011 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:40.029 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:40.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:40.030 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:40.047 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:40.047 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:40.084 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:40.084 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:01:40.087 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:01:40.087 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:01:40.203 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:01:40.206 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:01:40.207 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:01:40.304 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:01:40.305 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:01:40.510 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:02:07.535 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:02:07.548 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501700088 
[INFO ] 2024-12-18 14:02:07.549 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501700088 
[INFO ] 2024-12-18 14:02:07.549 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:02:07.549 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:02:07.549 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 24 ms 
[INFO ] 2024-12-18 14:02:07.550 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:02:07.550 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:02:07.550 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:02:07.550 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:02:07.550 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:02:07.752 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:02:10.030 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:02:10.030 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b2047a5 
[INFO ] 2024-12-18 14:02:10.148 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:02:10.148 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:02:10.148 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:02:10.175 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:02:10.178 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:02:12.355 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:02:12.355 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:02:12.445 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:02:12.445 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:02:12.446 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 14:02:12.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:02:12.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:02:12.496 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.496 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.496 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.510 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.510 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.511 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.512 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.515 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.515 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.518 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.518 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.526 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.526 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:02:12.529 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.529 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.531 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.531 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.531 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.535 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.549 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.549 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.549 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.554 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.558 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.558 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:02:12.560 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.561 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:02:12.650 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:02:12.650 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:02:12.651 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:02:12.651 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:02:12.656 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:02:12.825 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:02:12.825 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:02:30.025 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:02:30.041 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501732554 
[INFO ] 2024-12-18 14:02:30.041 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501732554 
[INFO ] 2024-12-18 14:02:30.041 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:02:30.041 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:02:30.041 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 23 ms 
[INFO ] 2024-12-18 14:02:30.041 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:02:30.042 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:02:30.042 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:02:30.042 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:02:30.042 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:02:30.042 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:02:30.202 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:02:30.202 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4ed7b69b 
[INFO ] 2024-12-18 14:02:30.318 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:02:30.321 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:02:30.321 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:02:30.357 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:02:30.358 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:03:14.415 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:03:14.746 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:03:14.746 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:03:14.798 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:03:14.798 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 14:03:14.815 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:03:14.821 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:03:14.925 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.926 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.926 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.926 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.928 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.928 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.928 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.928 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.928 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.933 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.933 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.943 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:03:14.943 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.946 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.946 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.950 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.950 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.962 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.962 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.974 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.974 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.974 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.974 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.974 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.974 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:14.976 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.981 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.989 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:14.989 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:15.013 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:15.013 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:15.014 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:03:15.016 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:15.017 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:03:15.133 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:03:15.133 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:03:15.133 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:03:15.291 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:03:15.291 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:03:15.291 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:03:15.380 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:03:27.725 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:03:27.743 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501795007 
[INFO ] 2024-12-18 14:03:27.743 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501795007 
[INFO ] 2024-12-18 14:03:27.743 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:03:27.743 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:03:27.744 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 29 ms 
[INFO ] 2024-12-18 14:03:27.744 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:03:27.744 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:03:27.744 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:03:27.744 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:03:27.745 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:03:27.945 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:03:30.427 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:03:30.427 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5274ce1c 
[INFO ] 2024-12-18 14:03:30.544 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:03:30.574 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:03:30.574 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:03:30.614 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:03:30.614 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:04:01.268 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:04:01.467 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:04:01.573 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:04:01.573 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:04:01.573 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 14:04:01.618 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:04:01.619 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:04:01.638 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.640 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.640 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.640 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.640 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.650 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.650 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.651 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.651 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.669 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.669 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.669 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.675 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.675 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.678 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:04:01.679 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.681 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.681 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.683 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.683 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.686 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.686 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.698 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.698 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.698 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.698 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.698 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.702 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.706 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.712 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.712 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.718 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.718 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:01.721 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:01.721 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:04:01.796 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:04:01.796 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:04:01.796 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:04:01.894 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:04:01.895 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:04:02.095 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:04:19.504 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:04:19.522 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501841689 
[INFO ] 2024-12-18 14:04:19.522 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501841689 
[INFO ] 2024-12-18 14:04:19.522 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:04:19.524 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:04:19.524 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 24 ms 
[INFO ] 2024-12-18 14:04:19.524 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:04:19.524 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:04:19.524 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:04:19.524 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:04:19.527 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:04:19.527 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 3 ms 
[INFO ] 2024-12-18 14:04:20.689 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:04:20.689 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f63dc30 
[INFO ] 2024-12-18 14:04:20.796 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:04:20.857 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:04:20.857 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:04:20.894 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:04:20.903 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:04:26.731 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:04:26.859 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:04:26.963 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:04:26.963 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:04:26.963 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 14:04:26.970 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:04:26.970 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:26.996 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.019 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:04:27.020 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.031 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.031 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.039 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.039 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.039 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.039 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.039 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.043 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.043 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.048 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.048 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.049 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.049 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.051 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.051 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.053 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.053 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.058 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.058 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.064 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.065 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:04:27.072 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:04:27.072 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:04:27.179 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:04:27.179 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:04:27.185 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:04:27.185 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:04:27.331 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:04:27.331 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:04:41.640 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:04:41.642 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501867076 
[INFO ] 2024-12-18 14:04:41.642 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501867076 
[INFO ] 2024-12-18 14:04:41.642 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:04:41.645 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:04:41.645 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 31 ms 
[INFO ] 2024-12-18 14:04:41.646 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:04:41.646 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:04:41.646 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:04:41.646 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:04:41.646 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:04:41.647 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 1 ms 
[INFO ] 2024-12-18 14:04:45.947 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:04:45.947 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1328a80c 
[INFO ] 2024-12-18 14:04:46.074 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:04:46.074 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:04:46.074 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:04:46.094 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:04:46.096 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:05:29.757 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:05:30.188 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:05:30.326 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:05:30.326 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:05:30.350 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:05:30.357 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:05:30.357 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:05:30.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.466 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.467 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.467 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.467 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.469 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.469 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.486 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.486 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.497 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.497 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.502 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.502 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.504 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.504 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:05:30.521 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.522 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.525 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.525 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.537 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.537 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.549 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.549 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.549 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.550 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.550 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.564 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.564 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:05:30.568 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:05:30.568 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:05:30.627 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:05:30.627 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:05:30.627 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:05:30.629 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:05:30.823 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:05:30.823 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:06:05.120 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:06:05.137 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501930462 
[INFO ] 2024-12-18 14:06:05.137 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734501930462 
[INFO ] 2024-12-18 14:06:05.137 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:06:05.137 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:06:05.138 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 41 ms 
[INFO ] 2024-12-18 14:06:05.138 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:06:05.139 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:06:05.139 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:06:05.139 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:06:05.139 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:06:05.343 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 1 ms 
[INFO ] 2024-12-18 14:06:06.256 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:06:06.256 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@571b5421 
[INFO ] 2024-12-18 14:06:06.390 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:06:06.391 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:06:06.391 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:06:06.438 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:06:06.440 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:10:11.434 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:10:11.581 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:10:11.670 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:10:11.670 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:10:11.670 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:10:11.687 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:10:11.693 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.740 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.746 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.746 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.755 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.755 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.759 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.761 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:10:11.764 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.766 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.766 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.766 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.770 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.770 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.771 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.771 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.773 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.773 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.775 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.775 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.775 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.775 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.779 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.779 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.784 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:10:11.784 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:10:11.893 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:10:11.893 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:10:11.893 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:10:11.893 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:10:11.896 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:10:12.081 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:10:12.081 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:11:24.467 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:11:24.468 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734502211796 
[INFO ] 2024-12-18 14:11:24.468 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734502211796 
[INFO ] 2024-12-18 14:11:24.468 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:11:24.468 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:11:24.469 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 37 ms 
[INFO ] 2024-12-18 14:11:24.470 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:11:24.470 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:11:24.470 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:11:24.470 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:11:24.470 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:11:24.470 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:11:26.838 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:11:26.838 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@391bf1d2 
[INFO ] 2024-12-18 14:11:26.948 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:11:26.957 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:11:26.957 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:11:26.980 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:11:26.980 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:11:29.611 - [CDC log cache task from MongoTarget] - Start task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget) 
[INFO ] 2024-12-18 14:11:29.807 - [CDC log cache task from MongoTarget] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 14:11:29.864 - [CDC log cache task from MongoTarget] - The engine receives CDC log cache task from MongoTarget task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:11:29.940 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] start preload schema,table counts: 17 
[INFO ] 2024-12-18 14:11:29.940 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:11:29.968 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-12-18 14:11:29.976 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-18 14:11:30.020 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.023 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.027 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.039 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523733', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.039 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691778', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.048 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_674958521', head seq: 0, tail seq: 1 
[INFO ] 2024-12-18 14:11:30.049 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-1660872473', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.064 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1192523734', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.064 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.078 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.078 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.078 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_699691780', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.082 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1042067711', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.082 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323149', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.096 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-804323148', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.096 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.096 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1545530031', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.097 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_42811224', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.107 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_-945749013', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.107 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.107 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.110 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_2116397731', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.110 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1792520433', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.113 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1062771021', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.113 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_162555228', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.120 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 14:11:30.120 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM', name space: 'tapdatav317.ExternalStorage_SHARE_CDC_1162179033', head seq: 0, tail seq: 0 
[INFO ] 2024-12-18 14:11:30.207 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-12-18 14:11:30.207 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 14:11:30.207 - [CDC log cache task from MongoTarget][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 14:11:30.210 - [CDC log cache task from MongoTarget][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 14:11:30.210 - [CDC log cache task from MongoTarget][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:11:30.420 - [CDC log cache task from MongoTarget][MongoTarget] - Starting stream read, table list: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], offset: {"cdcOffset":1734501663,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 14:11:30.625 - [CDC log cache task from MongoTarget][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM.1, TEST.CAR.CLAIM, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, TARGET.CAR.CLAIM, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 14:14:15.920 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] running status set to false 
[INFO ] 2024-12-18 14:14:15.920 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734502290099 
[INFO ] 2024-12-18 14:14:15.920 - [CDC log cache task from MongoTarget][MongoTarget] - PDK connector node released: HazelcastSourcePdkShareCDCNode_4190a03983014cbba6816b3238bcbc63_1734502290099 
[INFO ] 2024-12-18 14:14:15.920 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] schema data cleaned 
[INFO ] 2024-12-18 14:14:15.921 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] monitor closed 
[INFO ] 2024-12-18 14:14:15.922 - [CDC log cache task from MongoTarget][MongoTarget] - Node MongoTarget[4190a03983014cbba6816b3238bcbc63] close complete, cost 70 ms 
[INFO ] 2024-12-18 14:14:15.923 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] running status set to false 
[INFO ] 2024-12-18 14:14:15.923 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-12-18 14:14:15.923 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-12-18 14:14:15.923 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] schema data cleaned 
[INFO ] 2024-12-18 14:14:15.923 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] monitor closed 
[INFO ] 2024-12-18 14:14:15.923 - [CDC log cache task from MongoTarget][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad18225dd25d41f58d07fe516028e051] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:14:17.173 - [CDC log cache task from MongoTarget] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:14:17.173 - [CDC log cache task from MongoTarget] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@308e48f1 
[INFO ] 2024-12-18 14:14:17.285 - [CDC log cache task from MongoTarget] - Stop task milestones: 6761d14eaa4ad53b67c6f1eb(CDC log cache task from MongoTarget)  
[INFO ] 2024-12-18 14:14:17.312 - [CDC log cache task from MongoTarget] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:14:17.315 - [CDC log cache task from MongoTarget] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:14:17.401 - [CDC log cache task from MongoTarget] - Remove memory task client succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
[INFO ] 2024-12-18 14:14:17.401 - [CDC log cache task from MongoTarget] - Destroy memory task client cache succeed, task: CDC log cache task from MongoTarget[6761d14eaa4ad53b67c6f1eb] 
