[INFO ] 2024-07-12 11:28:20.310 - [任务 58] - Task initialization... 
[INFO ] 2024-07-12 11:28:20.514 - [任务 58] - Start task milestones: 6690a2817c91bf6e98248d7d(任务 58) 
[INFO ] 2024-07-12 11:28:20.514 - [任务 58] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 11:28:20.557 - [任务 58] - The engine receives 任务 58 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 11:28:20.600 - [任务 58][test2] - Node test2[ede5aff6-5add-4372-991f-4c9cb1ca4b19] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:28:20.601 - [任务 58][POLICY] - Node POLICY[63ebe45f-b75e-45f9-9e3d-75f81e0be481] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:28:20.601 - [任务 58][test2] - Node test2[ede5aff6-5add-4372-991f-4c9cb1ca4b19] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:28:20.601 - [任务 58][POLICY] - Node POLICY[63ebe45f-b75e-45f9-9e3d-75f81e0be481] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:28:25.445 - [任务 58][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 11:28:25.558 - [任务 58][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-12 11:28:25.558 - [任务 58][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-12 11:28:25.558 - [任务 58][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 11:28:25.600 - [任务 58][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":93528350,"gtidSet":""} 
[INFO ] 2024-07-12 11:28:25.600 - [任务 58][POLICY] - Initial sync started 
[INFO ] 2024-07-12 11:28:25.601 - [任务 58][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-12 11:28:25.601 - [任务 58][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-12 11:28:25.745 - [任务 58][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-12 11:28:25.745 - [任务 58][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 11:28:25.746 - [任务 58][POLICY] - Initial sync completed 
[INFO ] 2024-07-12 11:28:25.746 - [任务 58][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-12 11:28:25.746 - [任务 58][POLICY] - Initial sync completed 
[INFO ] 2024-07-12 11:28:25.746 - [任务 58][POLICY] - Starting stream read, table list: [POLICY], offset: {"filename":"binlog.000032","position":93528350,"gtidSet":""} 
[INFO ] 2024-07-12 11:28:25.770 - [任务 58][POLICY] - Starting mysql cdc, server name: 101cdd69-f557-4cf9-9118-bbfd14d3590e 
[INFO ] 2024-07-12 11:28:25.774 - [任务 58][POLICY] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 571606657
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 101cdd69-f557-4cf9-9118-bbfd14d3590e
  database.port: 3306
  threadName: Debezium-Mysql-Connector-101cdd69-f557-4cf9-9118-bbfd14d3590e
  database.hostname: localhost
  database.password: ********
  name: 101cdd69-f557-4cf9-9118-bbfd14d3590e
  pdk.offset.string: {"name":"101cdd69-f557-4cf9-9118-bbfd14d3590e","offset":{"{\"server\":\"101cdd69-f557-4cf9-9118-bbfd14d3590e\"}":"{\"file\":\"binlog.000032\",\"pos\":93528350,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-12 11:28:25.985 - [任务 58][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-12 11:28:36.015 - [任务 58][POLICY] - Node POLICY[63ebe45f-b75e-45f9-9e3d-75f81e0be481] running status set to false 
[INFO ] 2024-07-12 11:28:36.022 - [任务 58][POLICY] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-12 11:28:36.022 - [任务 58][POLICY] - Mysql binlog reader stopped 
[INFO ] 2024-07-12 11:28:36.025 - [任务 58][POLICY] - Incremental sync completed 
[INFO ] 2024-07-12 11:28:36.029 - [任务 58][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-63ebe45f-b75e-45f9-9e3d-75f81e0be481 
[INFO ] 2024-07-12 11:28:36.029 - [任务 58][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-63ebe45f-b75e-45f9-9e3d-75f81e0be481 
[INFO ] 2024-07-12 11:28:36.029 - [任务 58][POLICY] - Node POLICY[63ebe45f-b75e-45f9-9e3d-75f81e0be481] schema data cleaned 
[INFO ] 2024-07-12 11:28:36.029 - [任务 58][POLICY] - Node POLICY[63ebe45f-b75e-45f9-9e3d-75f81e0be481] monitor closed 
[INFO ] 2024-07-12 11:28:36.030 - [任务 58][POLICY] - Node POLICY[63ebe45f-b75e-45f9-9e3d-75f81e0be481] close complete, cost 87 ms 
[INFO ] 2024-07-12 11:28:36.030 - [任务 58][test2] - Node test2[ede5aff6-5add-4372-991f-4c9cb1ca4b19] running status set to false 
[INFO ] 2024-07-12 11:28:36.049 - [任务 58][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-ede5aff6-5add-4372-991f-4c9cb1ca4b19 
[INFO ] 2024-07-12 11:28:36.049 - [任务 58][test2] - PDK connector node released: HazelcastTargetPdkDataNode-ede5aff6-5add-4372-991f-4c9cb1ca4b19 
[INFO ] 2024-07-12 11:28:36.050 - [任务 58][test2] - Node test2[ede5aff6-5add-4372-991f-4c9cb1ca4b19] schema data cleaned 
[INFO ] 2024-07-12 11:28:36.052 - [任务 58][test2] - Node test2[ede5aff6-5add-4372-991f-4c9cb1ca4b19] monitor closed 
[INFO ] 2024-07-12 11:28:36.052 - [任务 58][test2] - Node test2[ede5aff6-5add-4372-991f-4c9cb1ca4b19] close complete, cost 21 ms 
[INFO ] 2024-07-12 11:28:39.683 - [任务 58] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 11:28:39.683 - [任务 58] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5d6f511c 
[INFO ] 2024-07-12 11:28:39.804 - [任务 58] - Stop task milestones: 6690a2817c91bf6e98248d7d(任务 58)  
[INFO ] 2024-07-12 11:28:39.818 - [任务 58] - Stopped task aspect(s) 
[INFO ] 2024-07-12 11:28:39.819 - [任务 58] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 11:28:39.841 - [任务 58] - Remove memory task client succeed, task: 任务 58[6690a2817c91bf6e98248d7d] 
[INFO ] 2024-07-12 11:28:39.843 - [任务 58] - Destroy memory task client cache succeed, task: 任务 58[6690a2817c91bf6e98248d7d] 
