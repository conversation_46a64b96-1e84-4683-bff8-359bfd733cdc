[INFO ] 2024-04-06 21:11:28.463 - [任务 2] - Task initialization... 
[INFO ] 2024-04-06 21:11:28.464 - [任务 2] - Start task milestones: 661149dabf72be48c786c0c2(任务 2) 
[INFO ] 2024-04-06 21:11:28.464 - [任务 2] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-04-06 21:11:28.465 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 21:11:28.733 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:11:28.738 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:11:28.892 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] preload schema finished, cost 158 ms 
[INFO ] 2024-04-06 21:11:29.097 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] preload schema finished, cost 157 ms 
[INFO ] 2024-04-06 21:11:35.948 - [任务 2][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-06 21:11:36.041 - [任务 2][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-06 21:11:36.043 - [任务 2][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-06 21:11:36.043 - [任务 2][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-06 21:11:36.108 - [任务 2][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":157,"gtidSet":""} 
[INFO ] 2024-04-06 21:11:36.325 - [任务 2][CLAIM] - Initial sync started 
[INFO ] 2024-04-06 21:11:36.325 - [任务 2][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-06 21:11:36.384 - [任务 2][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-06 21:11:36.384 - [任务 2][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-06 21:11:37.040 - [任务 2][CLAIM] - Initial sync completed 
[INFO ] 2024-04-06 21:11:37.050 - [任务 2][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-06 21:11:37.051 - [任务 2][CLAIM] - Initial sync completed 
[INFO ] 2024-04-06 21:11:37.151 - [任务 2][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000021","position":157,"gtidSet":""} 
[INFO ] 2024-04-06 21:11:37.152 - [任务 2][CLAIM] - Starting mysql cdc, server name: 57d762e0-7538-48fc-80d8-adcd046f05ba 
[INFO ] 2024-04-06 21:11:37.353 - [任务 2][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1379005532
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  database.port: 3306
  threadName: Debezium-Mysql-Connector-57d762e0-7538-48fc-80d8-adcd046f05ba
  database.hostname: localhost
  database.password: ********
  name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  pdk.offset.string: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-06 21:11:37.562 - [任务 2][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-06 21:12:13.483 - [任务 2] - Stop task milestones: 661149dabf72be48c786c0c2(任务 2)  
[INFO ] 2024-04-06 21:12:13.523 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] running status set to false 
[INFO ] 2024-04-06 21:12:13.565 - [任务 2][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-06 21:12:13.569 - [任务 2][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-06 21:12:13.592 - [任务 2][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-db9f9337-2413-48a0-8c02-7235b2294f56 
[INFO ] 2024-04-06 21:12:13.593 - [任务 2][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-db9f9337-2413-48a0-8c02-7235b2294f56 
[INFO ] 2024-04-06 21:12:13.593 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] schema data cleaned 
[INFO ] 2024-04-06 21:12:13.597 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] monitor closed 
[INFO ] 2024-04-06 21:12:13.597 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] close complete, cost 74 ms 
[INFO ] 2024-04-06 21:12:13.608 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] running status set to false 
[INFO ] 2024-04-06 21:12:13.662 - [任务 2][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-1df98c0d-3100-4142-960a-fd5323f7c5cd 
[INFO ] 2024-04-06 21:12:13.665 - [任务 2][test1] - PDK connector node released: HazelcastTargetPdkDataNode-1df98c0d-3100-4142-960a-fd5323f7c5cd 
[INFO ] 2024-04-06 21:12:13.665 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] schema data cleaned 
[INFO ] 2024-04-06 21:12:13.676 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] monitor closed 
[INFO ] 2024-04-06 21:12:13.686 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] close complete, cost 58 ms 
[INFO ] 2024-04-06 21:12:18.724 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-06 21:12:18.725 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-04-06 21:12:18.775 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-04-06 21:12:18.776 - [任务 2] - Remove memory task client succeed, task: 任务 2[661149dabf72be48c786c0c2] 
[INFO ] 2024-04-06 21:12:18.980 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[661149dabf72be48c786c0c2] 
[INFO ] 2024-04-06 21:12:25.168 - [任务 2] - Task initialization... 
[INFO ] 2024-04-06 21:12:25.171 - [任务 2] - Start task milestones: 661149dabf72be48c786c0c2(任务 2) 
[INFO ] 2024-04-06 21:12:25.320 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-06 21:12:25.320 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 21:12:25.403 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:12:25.409 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:12:25.473 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] preload schema finished, cost 66 ms 
[INFO ] 2024-04-06 21:12:25.473 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] preload schema finished, cost 65 ms 
[INFO ] 2024-04-06 21:12:25.828 - [任务 2][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-06 21:12:25.830 - [任务 2][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-06 21:12:25.860 - [任务 2][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-06 21:12:25.862 - [任务 2][CLAIM] - batch offset found: {"CLAIM":{}},stream offset found: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"ts_sec\":1712409097,\"file\":\"binlog.000021\",\"pos\":157}"}} 
[INFO ] 2024-04-06 21:12:25.976 - [任务 2][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-06 21:12:25.976 - [任务 2][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-06 21:12:25.976 - [任务 2][CLAIM] - Initial sync completed 
[INFO ] 2024-04-06 21:12:25.984 - [任务 2][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"ts_sec\":1712409097,\"file\":\"binlog.000021\",\"pos\":157}"}} 
[INFO ] 2024-04-06 21:12:26.011 - [任务 2][CLAIM] - Starting mysql cdc, server name: 57d762e0-7538-48fc-80d8-adcd046f05ba 
[INFO ] 2024-04-06 21:12:26.012 - [任务 2][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2130918489
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  database.port: 3306
  threadName: Debezium-Mysql-Connector-57d762e0-7538-48fc-80d8-adcd046f05ba
  database.hostname: localhost
  database.password: ********
  name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  pdk.offset.string: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"ts_sec\":1712409097,\"file\":\"binlog.000021\",\"pos\":157}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-06 21:12:26.216 - [任务 2][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-06 21:34:28.317 - [任务 2] - Task initialization... 
[INFO ] 2024-04-06 21:34:28.318 - [任务 2] - Start task milestones: 661149dabf72be48c786c0c2(任务 2) 
[INFO ] 2024-04-06 21:34:28.318 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-06 21:34:28.319 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 21:34:28.674 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:34:28.675 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] start preload schema,table counts: 1 
[INFO ] 2024-04-06 21:34:28.772 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] preload schema finished, cost 97 ms 
[INFO ] 2024-04-06 21:34:28.976 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] preload schema finished, cost 99 ms 
[INFO ] 2024-04-06 21:34:29.784 - [任务 2][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-06 21:34:29.849 - [任务 2][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-06 21:34:29.850 - [任务 2][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-06 21:34:29.857 - [任务 2][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-06 21:34:29.873 - [任务 2][CLAIM] - batch offset found: {},stream offset found: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":1}"}} 
[INFO ] 2024-04-06 21:34:30.008 - [任务 2][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-06 21:34:30.008 - [任务 2][CLAIM] - Initial sync completed 
[INFO ] 2024-04-06 21:34:30.044 - [任务 2][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":1}"}} 
[INFO ] 2024-04-06 21:34:30.045 - [任务 2][CLAIM] - Starting mysql cdc, server name: 57d762e0-7538-48fc-80d8-adcd046f05ba 
[INFO ] 2024-04-06 21:34:30.250 - [任务 2][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2011421771
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  database.port: 3306
  threadName: Debezium-Mysql-Connector-57d762e0-7538-48fc-80d8-adcd046f05ba
  database.hostname: localhost
  database.password: ********
  name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  pdk.offset.string: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-06 21:34:30.747 - [任务 2][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-06 22:09:27.810 - [任务 2] - Start task milestones: 661149dabf72be48c786c0c2(任务 2) 
[INFO ] 2024-04-06 22:09:27.811 - [任务 2] - Task initialization... 
[INFO ] 2024-04-06 22:09:27.811 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-06 22:09:27.812 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 22:09:28.156 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] start preload schema,table counts: 1 
[INFO ] 2024-04-06 22:09:28.157 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] start preload schema,table counts: 1 
[INFO ] 2024-04-06 22:09:28.281 - [任务 2][test1] - Node test1[1df98c0d-3100-4142-960a-fd5323f7c5cd] preload schema finished, cost 152 ms 
[INFO ] 2024-04-06 22:09:28.283 - [任务 2][CLAIM] - Node CLAIM[db9f9337-2413-48a0-8c02-7235b2294f56] preload schema finished, cost 122 ms 
[INFO ] 2024-04-06 22:09:28.905 - [任务 2][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-06 22:09:29.287 - [任务 2][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-06 22:09:29.294 - [任务 2][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-06 22:09:29.294 - [任务 2][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-06 22:09:29.480 - [任务 2][CLAIM] - batch offset found: {},stream offset found: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":1}"}} 
[INFO ] 2024-04-06 22:09:29.480 - [任务 2][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-06 22:09:29.481 - [任务 2][CLAIM] - Initial sync completed 
[INFO ] 2024-04-06 22:09:29.489 - [任务 2][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":1}"}} 
[INFO ] 2024-04-06 22:09:29.556 - [任务 2][CLAIM] - Starting mysql cdc, server name: 57d762e0-7538-48fc-80d8-adcd046f05ba 
[INFO ] 2024-04-06 22:09:29.557 - [任务 2][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1815599416
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  database.port: 3306
  threadName: Debezium-Mysql-Connector-57d762e0-7538-48fc-80d8-adcd046f05ba
  database.hostname: localhost
  database.password: ********
  name: 57d762e0-7538-48fc-80d8-adcd046f05ba
  pdk.offset.string: {"name":"57d762e0-7538-48fc-80d8-adcd046f05ba","offset":{"{\"server\":\"57d762e0-7538-48fc-80d8-adcd046f05ba\"}":"{\"file\":\"binlog.000021\",\"pos\":157,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-06 22:09:30.368 - [任务 2][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
