[INFO ] 2024-07-12 15:20:15.231 - [任务 59] - Task initialization... 
[INFO ] 2024-07-12 15:20:15.231 - [任务 59] - Start task milestones: 6690d8eb457e901dd0b0ef60(任务 59) 
[INFO ] 2024-07-12 15:20:15.521 - [任务 59] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-12 15:20:15.525 - [任务 59] - The engine receives 任务 59 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 15:20:15.678 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] start preload schema,table counts: 1 
[INFO ] 2024-07-12 15:20:15.678 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] start preload schema,table counts: 1 
[INFO ] 2024-07-12 15:20:15.678 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] preload schema finished, cost 1 ms 
[INFO ] 2024-07-12 15:20:15.681 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] preload schema finished, cost 1 ms 
[INFO ] 2024-07-12 15:20:17.113 - [任务 59][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 15:20:17.228 - [任务 59][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-12 15:20:17.228 - [任务 59][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-12 15:20:17.228 - [任务 59][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 15:20:17.388 - [任务 59][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1720768817,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-12 15:20:17.444 - [任务 59][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-12 15:20:17.444 - [任务 59][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-12 15:20:17.444 - [任务 59][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-12 15:20:17.501 - [任务 59][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-12 15:20:17.501 - [任务 59][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 15:20:17.501 - [任务 59][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-12 15:20:17.501 - [任务 59][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-12 15:20:17.501 - [任务 59][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-12 15:20:17.546 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-12 15:20:17.546 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 1 - Check connection ShareMongoCDC-Test enable share cdc: true 
[INFO ] 2024-07-12 15:20:17.565 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 59 enable share cdc: true 
[INFO ] 2024-07-12 15:20:17.566 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自ShareMongoCDC-Test的共享挖掘任务 
[INFO ] 2024-07-12 15:20:17.581 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-12 15:20:17.581 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728309, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1026742106, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 15:20:17.724 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务_CUSTOMER_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1026742106', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 15:20:17.724 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728308, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1618843798, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 15:20:17.737 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务__tapdata_heartbeat_table_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1618843798', head seq: 0, tail seq: 2 
[INFO ] 2024-07-12 15:20:17.737 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-12 15:20:17.738 - [任务 59][CUSTOMER] - Init share cdc reader completed 
[INFO ] 2024-07-12 15:20:17.738 - [任务 59][CUSTOMER] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-12 15:20:17.738 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-12 15:20:17.738 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-12 15:20:17.743 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728309, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1026742106, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 15:20:17.743 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务_CUSTOMER_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1026742106', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 15:20:17.746 - [任务 59][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务_CUSTOMER_任务 59, external storage name: ExternalStorage_SHARE_CDC_1026742106 
[INFO ] 2024-07-12 15:20:17.747 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-07-12 15:20:17.747 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728308, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1618843798, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 15:20:17.749 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务__tapdata_heartbeat_table_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1618843798', head seq: 0, tail seq: 2 
[INFO ] 2024-07-12 15:20:17.750 - [任务 59][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务__tapdata_heartbeat_table_任务 59, external storage name: ExternalStorage_SHARE_CDC_1618843798 
[INFO ] 2024-07-12 15:20:17.750 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-12 15:20:17.751 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-07-12T07:20:17.228Z): 1 
[INFO ] 2024-07-12 15:20:17.751 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 1 
[INFO ] 2024-07-12 15:20:17.751 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Find sequence in construct(_tapdata_heartbeat_table) by timestamp(2024-07-12T07:20:17.228Z): 3 
[INFO ] 2024-07-12 15:20:17.752 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=1} 
[INFO ] 2024-07-12 15:20:17.757 - [任务 59][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 15:20:17.757 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 3 
[INFO ] 2024-07-12 15:20:17.958 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=3} 
[INFO ] 2024-07-12 15:20:19.383 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1720768817000, date=Fri Jul 12 15:20:17 CST 2024, before=Document{{}}, after=Document{{_id=[B@5e990e95, id=6690d7e969bf00020103c9f0, ts=Fri Jul 12 15:20:15 CST 2024}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2OTBEOTMxMDAwMDAwMUMyQjAyMkMwMTAwMjk2RTVBMTAwNDEx
NDM0QkVCRDBCMzRDMUNCOTBEQTM2QjQ5MEYxQ0U1NDY2NDVGNjk2NDAwNjQ2NjkwRDg1MTY2QUI1
RURFOEE3MjM3NzEwMDA0In2o
, type=DATA, connectionId=6690d8be457e901dd0b0ef12, isReplaceEvent=false, _ts=1720768818}} 
[INFO ] 2024-07-12 16:20:01.916 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] running status set to false 
[INFO ] 2024-07-12 16:20:01.963 - [任务 59][CUSTOMER] - Incremental sync completed 
[WARN ] 2024-07-12 16:20:02.009 - [任务 59][CUSTOMER] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-04ec7338-5d20-4822-a122-308cdb7a6a77 
[INFO ] 2024-07-12 18:32:55.790 - [任务 59] - Task initialization... 
[INFO ] 2024-07-12 18:32:55.797 - [任务 59] - Start task milestones: 6690d8eb457e901dd0b0ef60(任务 59) 
[INFO ] 2024-07-12 18:32:57.132 - [任务 59] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 18:32:57.488 - [任务 59] - The engine receives 任务 59 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 18:32:57.961 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] start preload schema,table counts: 1 
[INFO ] 2024-07-12 18:32:57.962 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] preload schema finished, cost 1 ms 
[INFO ] 2024-07-12 18:32:58.030 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] start preload schema,table counts: 1 
[INFO ] 2024-07-12 18:32:58.041 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 18:32:59.368 - [任务 59][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 18:32:59.396 - [任务 59][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-12 18:32:59.397 - [任务 59][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-12 18:32:59.423 - [任务 59][CUSTOMER] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-12 18:32:59.433 - [任务 59][CUSTOMER] - batch offset found: {"CUSTOMER":{"batch_read_connector_offset":{"sortKey":"_id","value":"66865a45e85c02b03ee82080","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"CUSTOMER":1,"_tapdata_heartbeat_table":4213},"streamOffset":{"_data":{"value":"826690E726000000072B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646690D93166AB5EDE8A72876E0004","bsonType":"STRING","double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"array":false,"null":false,"number":false}}} 
[INFO ] 2024-07-12 18:32:59.611 - [任务 59][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-12 18:32:59.611 - [任务 59][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-12 18:32:59.697 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-12 18:32:59.697 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 1 - Check connection ShareMongoCDC-Test enable share cdc: true 
[INFO ] 2024-07-12 18:32:59.698 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 59 enable share cdc: true 
[INFO ] 2024-07-12 18:32:59.737 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自ShareMongoCDC-Test的共享挖掘任务 
[INFO ] 2024-07-12 18:32:59.866 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-12 18:32:59.868 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728309, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1026742106, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 18:33:00.028 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务_CUSTOMER_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1026742106', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 18:33:00.126 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728308, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1618843798, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 18:33:00.126 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务__tapdata_heartbeat_table_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1618843798', head seq: 0, tail seq: 4223 
[INFO ] 2024-07-12 18:33:00.128 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-12 18:33:00.129 - [任务 59][CUSTOMER] - Init share cdc reader completed 
[INFO ] 2024-07-12 18:33:00.130 - [任务 59][CUSTOMER] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-12 18:33:00.130 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-12 18:33:00.170 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-12 18:33:00.170 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728309, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1026742106, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 18:33:00.186 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务_CUSTOMER_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1026742106', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 18:33:00.186 - [任务 59][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务_CUSTOMER_任务 59, external storage name: ExternalStorage_SHARE_CDC_1026742106 
[INFO ] 2024-07-12 18:33:00.192 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-07-12 18:33:00.194 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 1 
[INFO ] 2024-07-12 18:33:00.194 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=1} 
[INFO ] 2024-07-12 18:33:00.227 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728308, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1618843798, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-12 18:33:00.241 - [任务 59][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务__tapdata_heartbeat_table_任务 59', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1618843798', head seq: 0, tail seq: 4223 
[INFO ] 2024-07-12 18:33:00.243 - [任务 59][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务__tapdata_heartbeat_table_任务 59, external storage name: ExternalStorage_SHARE_CDC_1618843798 
[INFO ] 2024-07-12 18:33:00.246 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-12 18:33:00.246 - [任务 59][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 18:33:00.248 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 4213 
[INFO ] 2024-07-12 18:33:00.248 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=4213} 
[INFO ] 2024-07-12 18:33:00.451 - [任务 59][CUSTOMER] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1720772391000, date=Fri Jul 12 16:19:51 CST 2024, before=Document{{}}, after=Document{{_id=[B@5cb07a8e, id=6690d8be457e901dd0b0ef12, ts=Fri Jul 12 16:19:49 CST 2024}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2OTBFNzI3MDAwMDAwMDIyQjAyMkMwMTAwMjk2RTVBMTAwNDEx
NDM0QkVCRDBCMzRDMUNCOTBEQTM2QjQ5MEYxQ0U1NDY2NDVGNjk2NDAwNjQ2NjkwRDkzMTY2QUI1
RURFOEE3Mjg3NkUwMDA0In2o
, type=DATA, connectionId=6690d8be457e901dd0b0ef12, isReplaceEvent=false, _ts=1720772392}} 
[INFO ] 2024-07-12 19:05:35.090 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] running status set to false 
[INFO ] 2024-07-12 19:05:35.091 - [任务 59][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-12 19:05:35.107 - [任务 59][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-04ec7338-5d20-4822-a122-308cdb7a6a77 
[INFO ] 2024-07-12 19:05:35.108 - [任务 59][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-04ec7338-5d20-4822-a122-308cdb7a6a77 
[INFO ] 2024-07-12 19:05:35.108 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] schema data cleaned 
[INFO ] 2024-07-12 19:05:35.109 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] monitor closed 
[INFO ] 2024-07-12 19:05:35.110 - [任务 59][CUSTOMER] - Node CUSTOMER[04ec7338-5d20-4822-a122-308cdb7a6a77] close complete, cost 35 ms 
[INFO ] 2024-07-12 19:05:35.130 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] running status set to false 
[INFO ] 2024-07-12 19:05:35.130 - [任务 59][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-b773c7e1-9afb-4f2d-be4c-321e2a83ae16 
[INFO ] 2024-07-12 19:05:35.130 - [任务 59][test] - PDK connector node released: HazelcastTargetPdkDataNode-b773c7e1-9afb-4f2d-be4c-321e2a83ae16 
[INFO ] 2024-07-12 19:05:35.131 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] schema data cleaned 
[INFO ] 2024-07-12 19:05:35.133 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] monitor closed 
[INFO ] 2024-07-12 19:05:35.133 - [任务 59][test] - Node test[b773c7e1-9afb-4f2d-be4c-321e2a83ae16] close complete, cost 22 ms 
[INFO ] 2024-07-12 19:05:37.405 - [任务 59] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 19:05:37.406 - [任务 59] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6a4788c7 
[INFO ] 2024-07-12 19:05:37.542 - [任务 59] - Stop task milestones: 6690d8eb457e901dd0b0ef60(任务 59)  
[INFO ] 2024-07-12 19:05:37.542 - [任务 59] - Stopped task aspect(s) 
[INFO ] 2024-07-12 19:05:37.542 - [任务 59] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 19:05:37.568 - [任务 59] - Remove memory task client succeed, task: 任务 59[6690d8eb457e901dd0b0ef60] 
[INFO ] 2024-07-12 19:05:37.570 - [任务 59] - Destroy memory task client cache succeed, task: 任务 59[6690d8eb457e901dd0b0ef60] 
