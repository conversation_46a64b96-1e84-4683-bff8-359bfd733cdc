[INFO ] 2024-07-18 10:43:57.140 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Task initialization... 
[INFO ] 2024-07-18 10:43:57.340 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Start task milestones: 669881628315b25db9f537b3(t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625) 
[INFO ] 2024-07-18 10:43:57.395 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:43:57.571 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - The engine receives t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:43:57.627 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[a600939f-6aa8-4a45-b26d-afa4eba026cd] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:43:57.628 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[67495fbc-e4c2-4455-8111-56de8b317d95] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:43:57.628 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[a600939f-6aa8-4a45-b26d-afa4eba026cd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:43:57.628 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[67495fbc-e4c2-4455-8111-56de8b317d95] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:43:57.973 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:43:57.986 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Source node "qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:43:57.986 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Source node "qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:43:57.986 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:43:58.138 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-18 10:43:58.138 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:43:58.144 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Starting batch read, table name: mock_1000w, offset: null 
[INFO ] 2024-07-18 10:43:58.144 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Table mock_1000w is going to be initial synced 
[INFO ] 2024-07-18 10:43:58.152 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Start mock_1000w batch read 
[INFO ] 2024-07-18 10:43:58.152 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Query table 'mock_1000w' counts: 10000000 
[INFO ] 2024-07-18 10:44:36.974 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Compile mock_1000w batch read 
[INFO ] 2024-07-18 10:44:36.977 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Table [mock_1000w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:44:36.977 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:44:43.062 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[67495fbc-e4c2-4455-8111-56de8b317d95] running status set to false 
[INFO ] 2024-07-18 10:44:43.073 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[a600939f-6aa8-4a45-b26d-afa4eba026cd] running status set to false 
[INFO ] 2024-07-18 10:44:43.074 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 10:44:43.081 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-67495fbc-e4c2-4455-8111-56de8b317d95 
[INFO ] 2024-07-18 10:44:43.081 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-67495fbc-e4c2-4455-8111-56de8b317d95 
[INFO ] 2024-07-18 10:44:43.081 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[67495fbc-e4c2-4455-8111-56de8b317d95] schema data cleaned 
[INFO ] 2024-07-18 10:44:43.082 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[67495fbc-e4c2-4455-8111-56de8b317d95] monitor closed 
[INFO ] 2024-07-18 10:44:43.090 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[67495fbc-e4c2-4455-8111-56de8b317d95] close complete, cost 30 ms 
[INFO ] 2024-07-18 10:44:43.090 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Stop connector: first 1721270638148 85ms, last 1721270676951 3005ms, counts: 10000000/41808ms, min: 7, max: 3031, QPS: 243902/s 
[INFO ] 2024-07-18 10:44:43.096 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-a600939f-6aa8-4a45-b26d-afa4eba026cd 
[INFO ] 2024-07-18 10:44:43.096 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-a600939f-6aa8-4a45-b26d-afa4eba026cd 
[INFO ] 2024-07-18 10:44:43.096 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[a600939f-6aa8-4a45-b26d-afa4eba026cd] schema data cleaned 
[INFO ] 2024-07-18 10:44:43.096 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[a600939f-6aa8-4a45-b26d-afa4eba026cd] monitor closed 
[INFO ] 2024-07-18 10:44:43.298 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[a600939f-6aa8-4a45-b26d-afa4eba026cd] close complete, cost 40 ms 
[INFO ] 2024-07-18 10:44:43.793 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:44:43.794 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7bbc2e3c 
[INFO ] 2024-07-18 10:44:43.925 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Stop task milestones: 669881628315b25db9f537b3(t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625)  
[INFO ] 2024-07-18 10:44:43.966 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:44:43.966 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:44:44.028 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Remove memory task client succeed, task: t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625[669881628315b25db9f537b3] 
[INFO ] 2024-07-18 10:44:44.028 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625] - Destroy memory task client cache succeed, task: t_1.2-mock_to_mock_3fields_1717403468657_3537-1721270625[669881628315b25db9f537b3] 
