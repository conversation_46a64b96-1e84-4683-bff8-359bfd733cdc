[INFO ] 2024-11-14 11:58:27.305 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 11:58:27.306 - [主从合并没有主键(100)][06726f29-57c5-4d57-9f6d-7897faa290e0] - Node 06726f29-57c5-4d57-9f6d-7897faa290e0[06726f29-57c5-4d57-9f6d-7897faa290e0] start preload schema,table counts: 0 
[INFO ] 2024-11-14 11:58:27.306 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 11:58:27.306 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:58:27.306 - [主从合并没有主键(100)][06726f29-57c5-4d57-9f6d-7897faa290e0] - Node 06726f29-57c5-4d57-9f6d-7897faa290e0[06726f29-57c5-4d57-9f6d-7897faa290e0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:58:27.306 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:58:27.511 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 11:58:27.567 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 11:58:27.567 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 11:58:27.578 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556707381 
[INFO ] 2024-11-14 11:58:27.578 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556707381 
[INFO ] 2024-11-14 11:58:27.578 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 11:58:27.579 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 11:58:27.787 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 13 ms 
[INFO ] 2024-11-14 11:58:28.183 - [主从合并没有主键(100)][c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] - Node c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c[c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] start preload schema,table counts: 0 
[INFO ] 2024-11-14 11:58:28.184 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 11:58:28.184 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 11:58:28.184 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:58:28.184 - [主从合并没有主键(100)][c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] - Node c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c[c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:58:28.184 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:58:28.184 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 11:58:28.221 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 11:58:28.222 - [主从合并没有主键(100)][06726f29-57c5-4d57-9f6d-7897faa290e0] - Node 06726f29-57c5-4d57-9f6d-7897faa290e0[06726f29-57c5-4d57-9f6d-7897faa290e0] running status set to false 
[INFO ] 2024-11-14 11:58:28.222 - [主从合并没有主键(100)][06726f29-57c5-4d57-9f6d-7897faa290e0] - Node 06726f29-57c5-4d57-9f6d-7897faa290e0[06726f29-57c5-4d57-9f6d-7897faa290e0] schema data cleaned 
[INFO ] 2024-11-14 11:58:28.235 - [主从合并没有主键(100)][06726f29-57c5-4d57-9f6d-7897faa290e0] - Node 06726f29-57c5-4d57-9f6d-7897faa290e0[06726f29-57c5-4d57-9f6d-7897faa290e0] monitor closed 
[INFO ] 2024-11-14 11:58:28.253 - [主从合并没有主键(100)][06726f29-57c5-4d57-9f6d-7897faa290e0] - Node 06726f29-57c5-4d57-9f6d-7897faa290e0[06726f29-57c5-4d57-9f6d-7897faa290e0] close complete, cost 5 ms 
[INFO ] 2024-11-14 11:58:28.257 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-bc784b1e-1c4f-43fd-85c3-cfe835218036 
[INFO ] 2024-11-14 11:58:28.257 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-bc784b1e-1c4f-43fd-85c3-cfe835218036 
[INFO ] 2024-11-14 11:58:28.257 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 11:58:28.258 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 11:58:28.258 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 11:58:28.260 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 41 ms 
[INFO ] 2024-11-14 11:58:28.264 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 11:58:28.264 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 11:58:28.264 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 11:58:28.437 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 11:58:28.437 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 11:58:28.450 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556708261 
[INFO ] 2024-11-14 11:58:28.450 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556708261 
[INFO ] 2024-11-14 11:58:28.450 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 11:58:28.450 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 11:58:28.451 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 14 ms 
[INFO ] 2024-11-14 11:58:28.753 - [主从合并没有主键(100)][c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] - Node c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c[c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] running status set to false 
[INFO ] 2024-11-14 11:58:28.753 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 11:58:28.753 - [主从合并没有主键(100)][c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] - Node c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c[c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] schema data cleaned 
[INFO ] 2024-11-14 11:58:28.753 - [主从合并没有主键(100)][c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] - Node c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c[c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] monitor closed 
[INFO ] 2024-11-14 11:58:28.753 - [主从合并没有主键(100)][c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] - Node c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c[c17fcd28-3ec9-4b5b-9e10-41f3fe0fee0c] close complete, cost 1 ms 
[INFO ] 2024-11-14 11:58:28.768 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-b431c2af-1bc3-4584-92ab-5fecc8c81d21 
[INFO ] 2024-11-14 11:58:28.769 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-b431c2af-1bc3-4584-92ab-5fecc8c81d21 
[INFO ] 2024-11-14 11:58:28.769 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 11:58:28.771 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 11:58:28.771 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 11:58:28.771 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 21 ms 
[INFO ] 2024-11-14 11:58:28.773 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 11:58:28.773 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 11:58:28.979 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 11:59:16.686 - [主从合并没有主键(100)][c5869121-1bf2-45c9-8360-22e12a409ec2] - Node c5869121-1bf2-45c9-8360-22e12a409ec2[c5869121-1bf2-45c9-8360-22e12a409ec2] start preload schema,table counts: 0 
[INFO ] 2024-11-14 11:59:16.687 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 11:59:16.688 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 11:59:16.688 - [主从合并没有主键(100)][c5869121-1bf2-45c9-8360-22e12a409ec2] - Node c5869121-1bf2-45c9-8360-22e12a409ec2[c5869121-1bf2-45c9-8360-22e12a409ec2] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:59:16.688 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 11:59:16.689 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 2 ms 
[INFO ] 2024-11-14 11:59:16.691 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 11:59:16.981 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 11:59:16.981 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 11:59:16.995 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556756793 
[INFO ] 2024-11-14 11:59:16.995 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556756793 
[INFO ] 2024-11-14 11:59:16.995 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 11:59:16.995 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 11:59:17.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 17 ms 
[INFO ] 2024-11-14 11:59:17.291 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 11:59:17.292 - [主从合并没有主键(100)][c5869121-1bf2-45c9-8360-22e12a409ec2] - Node c5869121-1bf2-45c9-8360-22e12a409ec2[c5869121-1bf2-45c9-8360-22e12a409ec2] running status set to false 
[INFO ] 2024-11-14 11:59:17.301 - [主从合并没有主键(100)][c5869121-1bf2-45c9-8360-22e12a409ec2] - Node c5869121-1bf2-45c9-8360-22e12a409ec2[c5869121-1bf2-45c9-8360-22e12a409ec2] schema data cleaned 
[INFO ] 2024-11-14 11:59:17.304 - [主从合并没有主键(100)][c5869121-1bf2-45c9-8360-22e12a409ec2] - Node c5869121-1bf2-45c9-8360-22e12a409ec2[c5869121-1bf2-45c9-8360-22e12a409ec2] monitor closed 
[INFO ] 2024-11-14 11:59:17.304 - [主从合并没有主键(100)][c5869121-1bf2-45c9-8360-22e12a409ec2] - Node c5869121-1bf2-45c9-8360-22e12a409ec2[c5869121-1bf2-45c9-8360-22e12a409ec2] close complete, cost 1 ms 
[INFO ] 2024-11-14 11:59:17.309 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-5d803b32-6245-4a07-9f24-2f9cb61b2f62 
[INFO ] 2024-11-14 11:59:17.309 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-5d803b32-6245-4a07-9f24-2f9cb61b2f62 
[INFO ] 2024-11-14 11:59:17.309 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 11:59:17.312 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 11:59:17.312 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 11:59:17.313 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 24 ms 
[INFO ] 2024-11-14 11:59:17.315 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 11:59:17.316 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 11:59:17.316 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:12.420 - [主从合并没有主键(100)][7e6422ae-2c25-468e-b1ba-6fc16415c1f3] - Node 7e6422ae-2c25-468e-b1ba-6fc16415c1f3[7e6422ae-2c25-468e-b1ba-6fc16415c1f3] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:12.420 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:12.420 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:12.420 - [主从合并没有主键(100)][7e6422ae-2c25-468e-b1ba-6fc16415c1f3] - Node 7e6422ae-2c25-468e-b1ba-6fc16415c1f3[7e6422ae-2c25-468e-b1ba-6fc16415c1f3] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:12.421 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:12.421 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:12.421 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:12.702 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:12.702 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:12.718 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556872515 
[INFO ] 2024-11-14 12:01:12.718 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556872515 
[INFO ] 2024-11-14 12:01:12.718 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:12.718 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:12.719 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 17 ms 
[INFO ] 2024-11-14 12:01:13.035 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:13.041 - [主从合并没有主键(100)][7e6422ae-2c25-468e-b1ba-6fc16415c1f3] - Node 7e6422ae-2c25-468e-b1ba-6fc16415c1f3[7e6422ae-2c25-468e-b1ba-6fc16415c1f3] running status set to false 
[INFO ] 2024-11-14 12:01:13.049 - [主从合并没有主键(100)][7e6422ae-2c25-468e-b1ba-6fc16415c1f3] - Node 7e6422ae-2c25-468e-b1ba-6fc16415c1f3[7e6422ae-2c25-468e-b1ba-6fc16415c1f3] schema data cleaned 
[INFO ] 2024-11-14 12:01:13.049 - [主从合并没有主键(100)][7e6422ae-2c25-468e-b1ba-6fc16415c1f3] - Node 7e6422ae-2c25-468e-b1ba-6fc16415c1f3[7e6422ae-2c25-468e-b1ba-6fc16415c1f3] monitor closed 
[INFO ] 2024-11-14 12:01:13.049 - [主从合并没有主键(100)][7e6422ae-2c25-468e-b1ba-6fc16415c1f3] - Node 7e6422ae-2c25-468e-b1ba-6fc16415c1f3[7e6422ae-2c25-468e-b1ba-6fc16415c1f3] close complete, cost 1 ms 
[INFO ] 2024-11-14 12:01:13.057 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-d051781c-5918-4c66-a97c-2e240980decb 
[INFO ] 2024-11-14 12:01:13.057 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-d051781c-5918-4c66-a97c-2e240980decb 
[INFO ] 2024-11-14 12:01:13.057 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:13.059 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:13.060 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:13.060 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 31 ms 
[INFO ] 2024-11-14 12:01:13.062 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:13.062 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:13.063 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:26.343 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:26.348 - [主从合并没有主键(100)][a2e8ebab-e010-4baa-b125-f9637ad5cb33] - Node a2e8ebab-e010-4baa-b125-f9637ad5cb33[a2e8ebab-e010-4baa-b125-f9637ad5cb33] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:26.348 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:26.348 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:26.348 - [主从合并没有主键(100)][a2e8ebab-e010-4baa-b125-f9637ad5cb33] - Node a2e8ebab-e010-4baa-b125-f9637ad5cb33[a2e8ebab-e010-4baa-b125-f9637ad5cb33] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:26.348 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:26.348 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:26.717 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:26.739 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:26.740 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556886530 
[INFO ] 2024-11-14 12:01:26.740 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556886530 
[INFO ] 2024-11-14 12:01:26.740 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:26.740 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:26.741 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 20 ms 
[INFO ] 2024-11-14 12:01:27.054 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:27.073 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-a0757047-467b-40c9-bed0-341e811fc66f 
[INFO ] 2024-11-14 12:01:27.073 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-a0757047-467b-40c9-bed0-341e811fc66f 
[INFO ] 2024-11-14 12:01:27.073 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:27.074 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:27.074 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:27.075 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 26 ms 
[INFO ] 2024-11-14 12:01:27.122 - [主从合并没有主键(100)][a2e8ebab-e010-4baa-b125-f9637ad5cb33] - Node a2e8ebab-e010-4baa-b125-f9637ad5cb33[a2e8ebab-e010-4baa-b125-f9637ad5cb33] running status set to false 
[INFO ] 2024-11-14 12:01:27.124 - [主从合并没有主键(100)][a2e8ebab-e010-4baa-b125-f9637ad5cb33] - Node a2e8ebab-e010-4baa-b125-f9637ad5cb33[a2e8ebab-e010-4baa-b125-f9637ad5cb33] schema data cleaned 
[INFO ] 2024-11-14 12:01:27.124 - [主从合并没有主键(100)][a2e8ebab-e010-4baa-b125-f9637ad5cb33] - Node a2e8ebab-e010-4baa-b125-f9637ad5cb33[a2e8ebab-e010-4baa-b125-f9637ad5cb33] monitor closed 
[INFO ] 2024-11-14 12:01:27.124 - [主从合并没有主键(100)][a2e8ebab-e010-4baa-b125-f9637ad5cb33] - Node a2e8ebab-e010-4baa-b125-f9637ad5cb33[a2e8ebab-e010-4baa-b125-f9637ad5cb33] close complete, cost 4 ms 
[INFO ] 2024-11-14 12:01:27.126 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:27.126 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:27.336 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:27.503 - [主从合并没有主键(100)][d3d47f51-afc9-444c-a838-862f55c72cd0] - Node d3d47f51-afc9-444c-a838-862f55c72cd0[d3d47f51-afc9-444c-a838-862f55c72cd0] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:27.503 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:27.503 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:27.504 - [主从合并没有主键(100)][d3d47f51-afc9-444c-a838-862f55c72cd0] - Node d3d47f51-afc9-444c-a838-862f55c72cd0[d3d47f51-afc9-444c-a838-862f55c72cd0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:27.504 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:27.504 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:27.504 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:27.713 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:27.713 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:27.724 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556887525 
[INFO ] 2024-11-14 12:01:27.724 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556887525 
[INFO ] 2024-11-14 12:01:27.724 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:27.724 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:27.724 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 13 ms 
[INFO ] 2024-11-14 12:01:28.266 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:28.266 - [主从合并没有主键(100)][d3d47f51-afc9-444c-a838-862f55c72cd0] - Node d3d47f51-afc9-444c-a838-862f55c72cd0[d3d47f51-afc9-444c-a838-862f55c72cd0] running status set to false 
[INFO ] 2024-11-14 12:01:28.267 - [主从合并没有主键(100)][d3d47f51-afc9-444c-a838-862f55c72cd0] - Node d3d47f51-afc9-444c-a838-862f55c72cd0[d3d47f51-afc9-444c-a838-862f55c72cd0] schema data cleaned 
[INFO ] 2024-11-14 12:01:28.267 - [主从合并没有主键(100)][d3d47f51-afc9-444c-a838-862f55c72cd0] - Node d3d47f51-afc9-444c-a838-862f55c72cd0[d3d47f51-afc9-444c-a838-862f55c72cd0] monitor closed 
[INFO ] 2024-11-14 12:01:28.267 - [主从合并没有主键(100)][d3d47f51-afc9-444c-a838-862f55c72cd0] - Node d3d47f51-afc9-444c-a838-862f55c72cd0[d3d47f51-afc9-444c-a838-862f55c72cd0] close complete, cost 4 ms 
[INFO ] 2024-11-14 12:01:28.270 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-f60d444a-3510-4e8f-83e0-42190bed8f52 
[INFO ] 2024-11-14 12:01:28.271 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-f60d444a-3510-4e8f-83e0-42190bed8f52 
[INFO ] 2024-11-14 12:01:28.271 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:28.272 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:28.272 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:28.274 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 25 ms 
[INFO ] 2024-11-14 12:01:28.274 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:28.274 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:28.275 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:28.392 - [主从合并没有主键(100)][a21ce227-467a-4bbf-a098-38f3705e2495] - Node a21ce227-467a-4bbf-a098-38f3705e2495[a21ce227-467a-4bbf-a098-38f3705e2495] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:28.392 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:28.392 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:28.392 - [主从合并没有主键(100)][a21ce227-467a-4bbf-a098-38f3705e2495] - Node a21ce227-467a-4bbf-a098-38f3705e2495[a21ce227-467a-4bbf-a098-38f3705e2495] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:28.392 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:28.392 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 2 ms 
[INFO ] 2024-11-14 12:01:28.394 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:28.669 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:28.697 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:28.698 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556888472 
[INFO ] 2024-11-14 12:01:28.698 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556888472 
[INFO ] 2024-11-14 12:01:28.698 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:28.699 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:28.904 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 29 ms 
[INFO ] 2024-11-14 12:01:29.048 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:29.048 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-72f4c3b4-388c-4ed3-8b3f-a1f3562e963f 
[INFO ] 2024-11-14 12:01:29.048 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-72f4c3b4-388c-4ed3-8b3f-a1f3562e963f 
[INFO ] 2024-11-14 12:01:29.049 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:29.052 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:29.052 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:29.052 - [主从合并没有主键(100)][增强JS] - Node 增强JS[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 31 ms 
[INFO ] 2024-11-14 12:01:29.066 - [主从合并没有主键(100)][a21ce227-467a-4bbf-a098-38f3705e2495] - Node a21ce227-467a-4bbf-a098-38f3705e2495[a21ce227-467a-4bbf-a098-38f3705e2495] running status set to false 
[INFO ] 2024-11-14 12:01:29.066 - [主从合并没有主键(100)][a21ce227-467a-4bbf-a098-38f3705e2495] - Node a21ce227-467a-4bbf-a098-38f3705e2495[a21ce227-467a-4bbf-a098-38f3705e2495] schema data cleaned 
[INFO ] 2024-11-14 12:01:29.066 - [主从合并没有主键(100)][a21ce227-467a-4bbf-a098-38f3705e2495] - Node a21ce227-467a-4bbf-a098-38f3705e2495[a21ce227-467a-4bbf-a098-38f3705e2495] monitor closed 
[INFO ] 2024-11-14 12:01:29.066 - [主从合并没有主键(100)][a21ce227-467a-4bbf-a098-38f3705e2495] - Node a21ce227-467a-4bbf-a098-38f3705e2495[a21ce227-467a-4bbf-a098-38f3705e2495] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:29.068 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:29.068 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:29.069 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:33.749 - [主从合并没有主键(100)][5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] - Node 5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2[5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] - Node 5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2[5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][f08319a8-dce8-4c2f-bad2-5276511b39c2] - Node f08319a8-dce8-4c2f-bad2-5276511b39c2[f08319a8-dce8-4c2f-bad2-5276511b39c2] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][f08319a8-dce8-4c2f-bad2-5276511b39c2] - Node f08319a8-dce8-4c2f-bad2-5276511b39c2[f08319a8-dce8-4c2f-bad2-5276511b39c2] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:33.751 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:33.811 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:33.811 - [主从合并没有主键(100)][a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] - Node a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1[a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:33.811 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:33.811 - [主从合并没有主键(100)][a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] - Node a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1[a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:33.811 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:33.811 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:33.811 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:33.976 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:33.992 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:34.002 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556893790 
[INFO ] 2024-11-14 12:01:34.002 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556893790 
[INFO ] 2024-11-14 12:01:34.002 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.002 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:34.049 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 28 ms 
[INFO ] 2024-11-14 12:01:34.049 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.054 - [主从合并没有主键(100)][8cbe7524-c71f-4432-a16e-84f7c3de20ee] - Node 8cbe7524-c71f-4432-a16e-84f7c3de20ee[8cbe7524-c71f-4432-a16e-84f7c3de20ee] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:34.056 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.065 - [主从合并没有主键(100)][8cbe7524-c71f-4432-a16e-84f7c3de20ee] - Node 8cbe7524-c71f-4432-a16e-84f7c3de20ee[8cbe7524-c71f-4432-a16e-84f7c3de20ee] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.066 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.069 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.138 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:34.138 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.139 - [主从合并没有主键(100)][fe8e174b-f2db-44ec-b0c1-e25872d21977] - Node fe8e174b-f2db-44ec-b0c1-e25872d21977[fe8e174b-f2db-44ec-b0c1-e25872d21977] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:34.139 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.139 - [主从合并没有主键(100)][fe8e174b-f2db-44ec-b0c1-e25872d21977] - Node fe8e174b-f2db-44ec-b0c1-e25872d21977[fe8e174b-f2db-44ec-b0c1-e25872d21977] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.139 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.139 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:34.139 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:34.151 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:34.176 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:34.177 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556893819 
[INFO ] 2024-11-14 12:01:34.177 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556893819 
[INFO ] 2024-11-14 12:01:34.177 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.177 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:34.177 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 20 ms 
[WARN ] 2024-11-14 12:01:34.420 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:34.469 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:34.474 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:34.476 - [主从合并没有主键(100)][f08319a8-dce8-4c2f-bad2-5276511b39c2] - Node f08319a8-dce8-4c2f-bad2-5276511b39c2[f08319a8-dce8-4c2f-bad2-5276511b39c2] running status set to false 
[INFO ] 2024-11-14 12:01:34.476 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556893791 
[INFO ] 2024-11-14 12:01:34.476 - [主从合并没有主键(100)][f08319a8-dce8-4c2f-bad2-5276511b39c2] - Node f08319a8-dce8-4c2f-bad2-5276511b39c2[f08319a8-dce8-4c2f-bad2-5276511b39c2] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.478 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556893791 
[INFO ] 2024-11-14 12:01:34.479 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.479 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:34.479 - [主从合并没有主键(100)][f08319a8-dce8-4c2f-bad2-5276511b39c2] - Node f08319a8-dce8-4c2f-bad2-5276511b39c2[f08319a8-dce8-4c2f-bad2-5276511b39c2] monitor closed 
[INFO ] 2024-11-14 12:01:34.480 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 58 ms 
[INFO ] 2024-11-14 12:01:34.495 - [主从合并没有主键(100)][f08319a8-dce8-4c2f-bad2-5276511b39c2] - Node f08319a8-dce8-4c2f-bad2-5276511b39c2[f08319a8-dce8-4c2f-bad2-5276511b39c2] close complete, cost 28 ms 
[INFO ] 2024-11-14 12:01:34.496 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-b935ad2e-9037-43a5-b36b-3ffac1c444f1 
[INFO ] 2024-11-14 12:01:34.496 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-b935ad2e-9037-43a5-b36b-3ffac1c444f1 
[INFO ] 2024-11-14 12:01:34.496 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.497 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.497 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:34.498 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 56 ms 
[INFO ] 2024-11-14 12:01:34.502 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:34.502 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:34.503 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:34.711 - [主从合并没有主键(100)][e6592c34-4364-475d-8338-e07f4869a1f6] - Node e6592c34-4364-475d-8338-e07f4869a1f6[e6592c34-4364-475d-8338-e07f4869a1f6] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:34.713 - [主从合并没有主键(100)][增强JS] - Node ch[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.713 - [主从合并没有主键(100)][a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] - Node a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1[a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] running status set to false 
[INFO ] 2024-11-14 12:01:34.713 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.714 - [主从合并没有主键(100)][e6592c34-4364-475d-8338-e07f4869a1f6] - Node e6592c34-4364-475d-8338-e07f4869a1f6[e6592c34-4364-475d-8338-e07f4869a1f6] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.714 - [主从合并没有主键(100)][增强JS] - Node ch[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.714 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:34.714 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.714 - [主从合并没有主键(100)][a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] - Node a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1[a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.714 - [主从合并没有主键(100)][a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] - Node a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1[a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] monitor closed 
[INFO ] 2024-11-14 12:01:34.738 - [主从合并没有主键(100)][a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] - Node a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1[a5b6da7f-b9ad-409e-8c91-2cdb8361cbd1] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:01:34.739 - [主从合并没有主键(100)][增强JS] - Node js_processor(ch: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:34.749 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-1ca98a4c-1a6a-4ca1-b8dc-9c869e17714f 
[INFO ] 2024-11-14 12:01:34.749 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-1ca98a4c-1a6a-4ca1-b8dc-9c869e17714f 
[INFO ] 2024-11-14 12:01:34.752 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.752 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.752 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:34.753 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 44 ms 
[INFO ] 2024-11-14 12:01:34.761 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:34.762 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:34.762 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:34.845 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:34.845 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:34.863 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556894147 
[INFO ] 2024-11-14 12:01:34.863 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556894147 
[INFO ] 2024-11-14 12:01:34.863 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:34.863 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:34.863 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 20 ms 
[INFO ] 2024-11-14 12:01:34.886 - [主从合并没有主键(100)][b68394d2-9490-4381-aa82-76111af1d9dc] - Node b68394d2-9490-4381-aa82-76111af1d9dc[b68394d2-9490-4381-aa82-76111af1d9dc] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:34.886 - [主从合并没有主键(100)][增强JS] - Node chi[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.886 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:34.886 - [主从合并没有主键(100)][增强JS] - Node chi[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.886 - [主从合并没有主键(100)][b68394d2-9490-4381-aa82-76111af1d9dc] - Node b68394d2-9490-4381-aa82-76111af1d9dc[b68394d2-9490-4381-aa82-76111af1d9dc] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.886 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:34.888 - [主从合并没有主键(100)][增强JS] - Node js_processor(chi: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:35.062 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:35.075 - [主从合并没有主键(100)][5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] - Node 5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2[5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] running status set to false 
[INFO ] 2024-11-14 12:01:35.075 - [主从合并没有主键(100)][5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] - Node 5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2[5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.075 - [主从合并没有主键(100)][5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] - Node 5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2[5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] monitor closed 
[INFO ] 2024-11-14 12:01:35.076 - [主从合并没有主键(100)][5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] - Node 5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2[5b9bfbb3-3fe5-4950-8f9b-9bc1313774f2] close complete, cost 8 ms 
[INFO ] 2024-11-14 12:01:35.103 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-7816cb02-7dc6-44c8-b4dd-b5e04f82ad97 
[INFO ] 2024-11-14 12:01:35.104 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-7816cb02-7dc6-44c8-b4dd-b5e04f82ad97 
[INFO ] 2024-11-14 12:01:35.105 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.110 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.110 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:35.110 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 91 ms 
[INFO ] 2024-11-14 12:01:35.114 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:35.115 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:35.115 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:35.129 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:35.129 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:35.163 - [主从合并没有主键(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.IllegalStateException: The pool is closedio.tapdata.entity.event.dml.TapInsertRecordEvent@6cf58db3: {"after":{"name":"sample string","id":1.0,"childId":1.0},"containsIllegalDate":false,"tableId":"test_merge_no_primary_key_parent","time":1731556894841,"type":300}
 
[INFO ] 2024-11-14 12:01:35.163 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556894145 
[INFO ] 2024-11-14 12:01:35.163 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556894145 
[INFO ] 2024-11-14 12:01:35.163 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.163 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:35.163 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 36 ms 
[ERROR] 2024-11-14 12:01:35.180 - [主从合并没有主键(100)][增强JS] - java.lang.IllegalStateException: The pool is closedio.tapdata.entity.event.dml.TapInsertRecordEvent@6cf58db3: {"after":{"name":"sample string","id":1.0,"childId":1.0},"containsIllegalDate":false,"tableId":"test_merge_no_primary_key_parent","time":1731556894841,"type":300}
 <-- Error Message -->
java.lang.IllegalStateException: The pool is closedio.tapdata.entity.event.dml.TapInsertRecordEvent@6cf58db3: {"after":{"name":"sample string","id":1.0,"childId":1.0},"containsIllegalDate":false,"tableId":"test_merge_no_primary_key_parent","time":1731556894841,"type":300}


<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: The pool is closed
	com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:137)
	com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:123)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:80)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	...

<-- Full Stack Trace -->
java.lang.IllegalStateException: The pool is closed
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:268)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalStateException: The pool is closed
	at com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:137)
	at com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:123)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:80)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:172)
	at com.mongodb.client.internal.MongoIterableImpl.execute(MongoIterableImpl.java:132)
	at com.mongodb.client.internal.MongoIterableImpl.iterator(MongoIterableImpl.java:86)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:451)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:198)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$null$0(HazelcastJavaScriptProcessorNode.java:174)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:174)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:298)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:275)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:259)
	... 18 more

[WARN ] 2024-11-14 12:01:35.300 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][增强JS] - Node c[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][8116e4fd-adea-4e7c-8d45-3e28245f62ef] - Node 8116e4fd-adea-4e7c-8d45-3e28245f62ef[8116e4fd-adea-4e7c-8d45-3e28245f62ef] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][8116e4fd-adea-4e7c-8d45-3e28245f62ef] - Node 8116e4fd-adea-4e7c-8d45-3e28245f62ef[8116e4fd-adea-4e7c-8d45-3e28245f62ef] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][增强JS] - Node c[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][增强JS] - Node js_processor(c: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:35.302 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:35.332 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556894913 
[INFO ] 2024-11-14 12:01:35.332 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556894913 
[INFO ] 2024-11-14 12:01:35.332 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.332 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:35.333 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 34 ms 
[WARN ] 2024-11-14 12:01:35.471 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:35.471 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:35.492 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556894829 
[INFO ] 2024-11-14 12:01:35.492 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556894829 
[INFO ] 2024-11-14 12:01:35.493 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.493 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:35.493 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 22 ms 
[INFO ] 2024-11-14 12:01:35.585 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.585 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.585 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.586 - [主从合并没有主键(100)][53da7989-8b72-4e42-a62d-d99568800d3b] - Node 53da7989-8b72-4e42-a62d-d99568800d3b[53da7989-8b72-4e42-a62d-d99568800d3b] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:35.586 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.586 - [主从合并没有主键(100)][53da7989-8b72-4e42-a62d-d99568800d3b] - Node 53da7989-8b72-4e42-a62d-d99568800d3b[53da7989-8b72-4e42-a62d-d99568800d3b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.628 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:35.628 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:35.650 - [主从合并没有主键(100)][8cbe7524-c71f-4432-a16e-84f7c3de20ee] - Node 8cbe7524-c71f-4432-a16e-84f7c3de20ee[8cbe7524-c71f-4432-a16e-84f7c3de20ee] running status set to false 
[INFO ] 2024-11-14 12:01:35.656 - [主从合并没有主键(100)][8cbe7524-c71f-4432-a16e-84f7c3de20ee] - Node 8cbe7524-c71f-4432-a16e-84f7c3de20ee[8cbe7524-c71f-4432-a16e-84f7c3de20ee] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.660 - [主从合并没有主键(100)][8cbe7524-c71f-4432-a16e-84f7c3de20ee] - Node 8cbe7524-c71f-4432-a16e-84f7c3de20ee[8cbe7524-c71f-4432-a16e-84f7c3de20ee] monitor closed 
[INFO ] 2024-11-14 12:01:35.670 - [主从合并没有主键(100)][8cbe7524-c71f-4432-a16e-84f7c3de20ee] - Node 8cbe7524-c71f-4432-a16e-84f7c3de20ee[8cbe7524-c71f-4432-a16e-84f7c3de20ee] close complete, cost 41 ms 
[INFO ] 2024-11-14 12:01:35.670 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.674 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.674 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.674 - [主从合并没有主键(100)][2524c16d-1a30-4923-8d81-ee64767048b4] - Node 2524c16d-1a30-4923-8d81-ee64767048b4[2524c16d-1a30-4923-8d81-ee64767048b4] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:35.674 - [主从合并没有主键(100)][2524c16d-1a30-4923-8d81-ee64767048b4] - Node 2524c16d-1a30-4923-8d81-ee64767048b4[2524c16d-1a30-4923-8d81-ee64767048b4] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.674 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.674 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-7c1ebd9c-59ca-487d-a2f5-4ee40a315ef0 
[INFO ] 2024-11-14 12:01:35.674 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-7c1ebd9c-59ca-487d-a2f5-4ee40a315ef0 
[INFO ] 2024-11-14 12:01:35.675 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.675 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:35.678 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.680 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:35.680 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 59 ms 
[INFO ] 2024-11-14 12:01:35.681 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:35.681 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:35.681 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:35.796 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.797 - [主从合并没有主键(100)][e29dc653-9e01-46e3-8c44-69e2e9c9745b] - Node e29dc653-9e01-46e3-8c44-69e2e9c9745b[e29dc653-9e01-46e3-8c44-69e2e9c9745b] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:35.797 - [主从合并没有主键(100)][增强JS] - Node chil[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:35.797 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.797 - [主从合并没有主键(100)][e29dc653-9e01-46e3-8c44-69e2e9c9745b] - Node e29dc653-9e01-46e3-8c44-69e2e9c9745b[e29dc653-9e01-46e3-8c44-69e2e9c9745b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.797 - [主从合并没有主键(100)][增强JS] - Node chil[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:35.797 - [主从合并没有主键(100)][增强JS] - Node js_processor(chil: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:35.918 - [主从合并没有主键(100)][增强JS] - Node chi[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:35.919 - [主从合并没有主键(100)][增强JS] - Node ch[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:35.919 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-fb506f90-2678-479b-89cf-f036c8ec03d3 
[INFO ] 2024-11-14 12:01:35.919 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-62b8122b-7ef5-47ca-a01a-c1fd52b79391 
[INFO ] 2024-11-14 12:01:35.919 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-fb506f90-2678-479b-89cf-f036c8ec03d3 
[INFO ] 2024-11-14 12:01:35.919 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-62b8122b-7ef5-47ca-a01a-c1fd52b79391 
[INFO ] 2024-11-14 12:01:35.919 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.919 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.922 - [主从合并没有主键(100)][增强JS] - Node chi[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.922 - [主从合并没有主键(100)][增强JS] - Node chi[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:35.922 - [主从合并没有主键(100)][增强JS] - Node ch[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.922 - [主从合并没有主键(100)][增强JS] - Node ch[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:35.927 - [主从合并没有主键(100)][增强JS] - Node chi[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 40 ms 
[INFO ] 2024-11-14 12:01:35.927 - [主从合并没有主键(100)][增强JS] - Node ch[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 41 ms 
[INFO ] 2024-11-14 12:01:35.962 - [主从合并没有主键(100)][b68394d2-9490-4381-aa82-76111af1d9dc] - Node b68394d2-9490-4381-aa82-76111af1d9dc[b68394d2-9490-4381-aa82-76111af1d9dc] running status set to false 
[INFO ] 2024-11-14 12:01:35.963 - [主从合并没有主键(100)][e6592c34-4364-475d-8338-e07f4869a1f6] - Node e6592c34-4364-475d-8338-e07f4869a1f6[e6592c34-4364-475d-8338-e07f4869a1f6] running status set to false 
[INFO ] 2024-11-14 12:01:35.963 - [主从合并没有主键(100)][b68394d2-9490-4381-aa82-76111af1d9dc] - Node b68394d2-9490-4381-aa82-76111af1d9dc[b68394d2-9490-4381-aa82-76111af1d9dc] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.963 - [主从合并没有主键(100)][e6592c34-4364-475d-8338-e07f4869a1f6] - Node e6592c34-4364-475d-8338-e07f4869a1f6[e6592c34-4364-475d-8338-e07f4869a1f6] schema data cleaned 
[INFO ] 2024-11-14 12:01:35.963 - [主从合并没有主键(100)][e6592c34-4364-475d-8338-e07f4869a1f6] - Node e6592c34-4364-475d-8338-e07f4869a1f6[e6592c34-4364-475d-8338-e07f4869a1f6] monitor closed 
[INFO ] 2024-11-14 12:01:35.963 - [主从合并没有主键(100)][b68394d2-9490-4381-aa82-76111af1d9dc] - Node b68394d2-9490-4381-aa82-76111af1d9dc[b68394d2-9490-4381-aa82-76111af1d9dc] monitor closed 
[INFO ] 2024-11-14 12:01:35.963 - [主从合并没有主键(100)][e6592c34-4364-475d-8338-e07f4869a1f6] - Node e6592c34-4364-475d-8338-e07f4869a1f6[e6592c34-4364-475d-8338-e07f4869a1f6] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:35.963 - [主从合并没有主键(100)][b68394d2-9490-4381-aa82-76111af1d9dc] - Node b68394d2-9490-4381-aa82-76111af1d9dc[b68394d2-9490-4381-aa82-76111af1d9dc] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:35.965 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:35.965 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:35.966 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:35.966 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:35.966 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:35.966 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:36.082 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[WARN ] 2024-11-14 12:01:36.082 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:36.123 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:36.123 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:36.123 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556895697 
[INFO ] 2024-11-14 12:01:36.123 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556895697 
[INFO ] 2024-11-14 12:01:36.126 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.126 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:36.126 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 40 ms 
[INFO ] 2024-11-14 12:01:36.128 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556895400 
[INFO ] 2024-11-14 12:01:36.131 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556895400 
[INFO ] 2024-11-14 12:01:36.131 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.131 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:36.131 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 42 ms 
[WARN ] 2024-11-14 12:01:36.163 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:36.163 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:36.181 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556895850 
[INFO ] 2024-11-14 12:01:36.181 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556895850 
[INFO ] 2024-11-14 12:01:36.181 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.181 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:36.181 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 23 ms 
[WARN ] 2024-11-14 12:01:36.387 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:36.413 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:36.413 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556895678 
[INFO ] 2024-11-14 12:01:36.413 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556895678 
[INFO ] 2024-11-14 12:01:36.413 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.413 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:36.413 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 20 ms 
[INFO ] 2024-11-14 12:01:36.581 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:36.593 - [主从合并没有主键(100)][2524c16d-1a30-4923-8d81-ee64767048b4] - Node 2524c16d-1a30-4923-8d81-ee64767048b4[2524c16d-1a30-4923-8d81-ee64767048b4] running status set to false 
[INFO ] 2024-11-14 12:01:36.594 - [主从合并没有主键(100)][2524c16d-1a30-4923-8d81-ee64767048b4] - Node 2524c16d-1a30-4923-8d81-ee64767048b4[2524c16d-1a30-4923-8d81-ee64767048b4] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.594 - [主从合并没有主键(100)][2524c16d-1a30-4923-8d81-ee64767048b4] - Node 2524c16d-1a30-4923-8d81-ee64767048b4[2524c16d-1a30-4923-8d81-ee64767048b4] monitor closed 
[INFO ] 2024-11-14 12:01:36.594 - [主从合并没有主键(100)][2524c16d-1a30-4923-8d81-ee64767048b4] - Node 2524c16d-1a30-4923-8d81-ee64767048b4[2524c16d-1a30-4923-8d81-ee64767048b4] close complete, cost 1 ms 
[INFO ] 2024-11-14 12:01:36.602 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-c49bee2f-7a94-41da-bb3a-347cbc76a4b9 
[INFO ] 2024-11-14 12:01:36.603 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-c49bee2f-7a94-41da-bb3a-347cbc76a4b9 
[INFO ] 2024-11-14 12:01:36.603 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.604 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.604 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:36.606 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 34 ms 
[INFO ] 2024-11-14 12:01:36.606 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:36.606 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:36.684 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:36.684 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:36.684 - [主从合并没有主键(100)][fcff19c7-c893-43c9-9244-d29991e1710c] - Node fcff19c7-c893-43c9-9244-d29991e1710c[fcff19c7-c893-43c9-9244-d29991e1710c] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:36.684 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:36.684 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:36.684 - [主从合并没有主键(100)][fcff19c7-c893-43c9-9244-d29991e1710c] - Node fcff19c7-c893-43c9-9244-d29991e1710c[fcff19c7-c893-43c9-9244-d29991e1710c] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:36.684 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:36.685 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:36.753 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:36.753 - [主从合并没有主键(100)][53da7989-8b72-4e42-a62d-d99568800d3b] - Node 53da7989-8b72-4e42-a62d-d99568800d3b[53da7989-8b72-4e42-a62d-d99568800d3b] running status set to false 
[INFO ] 2024-11-14 12:01:36.753 - [主从合并没有主键(100)][53da7989-8b72-4e42-a62d-d99568800d3b] - Node 53da7989-8b72-4e42-a62d-d99568800d3b[53da7989-8b72-4e42-a62d-d99568800d3b] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.753 - [主从合并没有主键(100)][53da7989-8b72-4e42-a62d-d99568800d3b] - Node 53da7989-8b72-4e42-a62d-d99568800d3b[53da7989-8b72-4e42-a62d-d99568800d3b] monitor closed 
[INFO ] 2024-11-14 12:01:36.754 - [主从合并没有主键(100)][53da7989-8b72-4e42-a62d-d99568800d3b] - Node 53da7989-8b72-4e42-a62d-d99568800d3b[53da7989-8b72-4e42-a62d-d99568800d3b] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:01:36.776 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-2725d3af-2fd5-4484-b01a-445525f807b7 
[INFO ] 2024-11-14 12:01:36.776 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-2725d3af-2fd5-4484-b01a-445525f807b7 
[INFO ] 2024-11-14 12:01:36.776 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.781 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:36.783 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:36.786 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 32 ms 
[INFO ] 2024-11-14 12:01:36.786 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:36.786 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:36.786 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:36.878 - [主从合并没有主键(100)][b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] - Node b38213d0-63f7-4d7d-acbb-f5c8a84fecc9[b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:36.878 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:36.878 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:36.878 - [主从合并没有主键(100)][b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] - Node b38213d0-63f7-4d7d-acbb-f5c8a84fecc9[b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:36.878 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:36.879 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 2 ms 
[INFO ] 2024-11-14 12:01:36.880 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:36.976 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:36.976 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:37.031 - [主从合并没有主键(100)][增强JS] - Node c[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:37.032 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556896702 
[INFO ] 2024-11-14 12:01:37.032 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556896702 
[INFO ] 2024-11-14 12:01:37.033 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.033 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:37.033 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-2974ebb4-35c4-4275-a8a2-71e0202d1a6d 
[INFO ] 2024-11-14 12:01:37.034 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 68 ms 
[INFO ] 2024-11-14 12:01:37.034 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-2974ebb4-35c4-4275-a8a2-71e0202d1a6d 
[INFO ] 2024-11-14 12:01:37.034 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.036 - [主从合并没有主键(100)][增强JS] - Node c[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.037 - [主从合并没有主键(100)][增强JS] - Node c[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:37.067 - [主从合并没有主键(100)][增强JS] - Node c[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 63 ms 
[INFO ] 2024-11-14 12:01:37.067 - [主从合并没有主键(100)][8116e4fd-adea-4e7c-8d45-3e28245f62ef] - Node 8116e4fd-adea-4e7c-8d45-3e28245f62ef[8116e4fd-adea-4e7c-8d45-3e28245f62ef] running status set to false 
[INFO ] 2024-11-14 12:01:37.067 - [主从合并没有主键(100)][8116e4fd-adea-4e7c-8d45-3e28245f62ef] - Node 8116e4fd-adea-4e7c-8d45-3e28245f62ef[8116e4fd-adea-4e7c-8d45-3e28245f62ef] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.067 - [主从合并没有主键(100)][8116e4fd-adea-4e7c-8d45-3e28245f62ef] - Node 8116e4fd-adea-4e7c-8d45-3e28245f62ef[8116e4fd-adea-4e7c-8d45-3e28245f62ef] monitor closed 
[INFO ] 2024-11-14 12:01:37.067 - [主从合并没有主键(100)][8116e4fd-adea-4e7c-8d45-3e28245f62ef] - Node 8116e4fd-adea-4e7c-8d45-3e28245f62ef[8116e4fd-adea-4e7c-8d45-3e28245f62ef] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:37.069 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:37.069 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:37.241 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:37.246 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:37.271 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:37.272 - [主从合并没有主键(100)][增强JS] - Node chil[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:37.314 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556896901 
[INFO ] 2024-11-14 12:01:37.314 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556896901 
[INFO ] 2024-11-14 12:01:37.314 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.314 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:37.314 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 68 ms 
[INFO ] 2024-11-14 12:01:37.325 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-b90eb46c-b137-49f0-9254-b44097c76513 
[INFO ] 2024-11-14 12:01:37.326 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-b90eb46c-b137-49f0-9254-b44097c76513 
[INFO ] 2024-11-14 12:01:37.327 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.331 - [主从合并没有主键(100)][增强JS] - Node chil[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.331 - [主从合并没有主键(100)][增强JS] - Node chil[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:37.331 - [主从合并没有主键(100)][增强JS] - Node chil[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 76 ms 
[INFO ] 2024-11-14 12:01:37.357 - [主从合并没有主键(100)][e29dc653-9e01-46e3-8c44-69e2e9c9745b] - Node e29dc653-9e01-46e3-8c44-69e2e9c9745b[e29dc653-9e01-46e3-8c44-69e2e9c9745b] running status set to false 
[INFO ] 2024-11-14 12:01:37.358 - [主从合并没有主键(100)][e29dc653-9e01-46e3-8c44-69e2e9c9745b] - Node e29dc653-9e01-46e3-8c44-69e2e9c9745b[e29dc653-9e01-46e3-8c44-69e2e9c9745b] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.358 - [主从合并没有主键(100)][e29dc653-9e01-46e3-8c44-69e2e9c9745b] - Node e29dc653-9e01-46e3-8c44-69e2e9c9745b[e29dc653-9e01-46e3-8c44-69e2e9c9745b] monitor closed 
[INFO ] 2024-11-14 12:01:37.358 - [主从合并没有主键(100)][e29dc653-9e01-46e3-8c44-69e2e9c9745b] - Node e29dc653-9e01-46e3-8c44-69e2e9c9745b[e29dc653-9e01-46e3-8c44-69e2e9c9745b] close complete, cost 1 ms 
[INFO ] 2024-11-14 12:01:37.360 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:37.360 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:37.372 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:37.372 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.372 - [主从合并没有主键(100)][08008add-8b43-4323-80ce-bfca0d0e64c9] - Node 08008add-8b43-4323-80ce-bfca0d0e64c9[08008add-8b43-4323-80ce-bfca0d0e64c9] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:37.372 - [主从合并没有主键(100)][08008add-8b43-4323-80ce-bfca0d0e64c9] - Node 08008add-8b43-4323-80ce-bfca0d0e64c9[08008add-8b43-4323-80ce-bfca0d0e64c9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.372 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.372 - [主从合并没有主键(100)][增强JS] - Node p[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.373 - [主从合并没有主键(100)][增强JS] - Node p[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.373 - [主从合并没有主键(100)][增强JS] - Node js_processor(p: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:37.375 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.375 - [主从合并没有主键(100)][5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] - Node 5b9a21dc-ba75-47cb-937a-cb86b26ce1b0[5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:37.375 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.375 - [主从合并没有主键(100)][5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] - Node 5b9a21dc-ba75-47cb-937a-cb86b26ce1b0[5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.375 - [主从合并没有主键(100)][增强JS] - Node pa[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.375 - [主从合并没有主键(100)][增强JS] - Node pa[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.376 - [主从合并没有主键(100)][增强JS] - Node js_processor(pa: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:37.468 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:37.469 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-2096c55a-2292-4440-9582-1d7658c8f8b5 
[INFO ] 2024-11-14 12:01:37.470 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-2096c55a-2292-4440-9582-1d7658c8f8b5 
[INFO ] 2024-11-14 12:01:37.471 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.472 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.472 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:37.472 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 41 ms 
[INFO ] 2024-11-14 12:01:37.494 - [主从合并没有主键(100)][fcff19c7-c893-43c9-9244-d29991e1710c] - Node fcff19c7-c893-43c9-9244-d29991e1710c[fcff19c7-c893-43c9-9244-d29991e1710c] running status set to false 
[INFO ] 2024-11-14 12:01:37.496 - [主从合并没有主键(100)][fcff19c7-c893-43c9-9244-d29991e1710c] - Node fcff19c7-c893-43c9-9244-d29991e1710c[fcff19c7-c893-43c9-9244-d29991e1710c] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.496 - [主从合并没有主键(100)][fcff19c7-c893-43c9-9244-d29991e1710c] - Node fcff19c7-c893-43c9-9244-d29991e1710c[fcff19c7-c893-43c9-9244-d29991e1710c] monitor closed 
[INFO ] 2024-11-14 12:01:37.500 - [主从合并没有主键(100)][fcff19c7-c893-43c9-9244-d29991e1710c] - Node fcff19c7-c893-43c9-9244-d29991e1710c[fcff19c7-c893-43c9-9244-d29991e1710c] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:01:37.500 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:37.500 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:37.500 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:37.625 - [主从合并没有主键(100)][增强JS] - Node par[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.626 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.626 - [主从合并没有主键(100)][048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] - Node 048d33ae-0c0b-4622-9e6b-7ae394b0d4e2[048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:37.626 - [主从合并没有主键(100)][048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] - Node 048d33ae-0c0b-4622-9e6b-7ae394b0d4e2[048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.626 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.626 - [主从合并没有主键(100)][增强JS] - Node par[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 2 ms 
[INFO ] 2024-11-14 12:01:37.626 - [主从合并没有主键(100)][增强JS] - Node js_processor(par: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:37.714 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:37.726 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-eae49237-9234-4775-8218-602cdbd4bd1b 
[INFO ] 2024-11-14 12:01:37.727 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-eae49237-9234-4775-8218-602cdbd4bd1b 
[INFO ] 2024-11-14 12:01:37.727 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.728 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.728 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:37.728 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 23 ms 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][fe8e174b-f2db-44ec-b0c1-e25872d21977] - Node fe8e174b-f2db-44ec-b0c1-e25872d21977[fe8e174b-f2db-44ec-b0c1-e25872d21977] running status set to false 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][fe8e174b-f2db-44ec-b0c1-e25872d21977] - Node fe8e174b-f2db-44ec-b0c1-e25872d21977[fe8e174b-f2db-44ec-b0c1-e25872d21977] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][fe8e174b-f2db-44ec-b0c1-e25872d21977] - Node fe8e174b-f2db-44ec-b0c1-e25872d21977[fe8e174b-f2db-44ec-b0c1-e25872d21977] monitor closed 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][fe8e174b-f2db-44ec-b0c1-e25872d21977] - Node fe8e174b-f2db-44ec-b0c1-e25872d21977[fe8e174b-f2db-44ec-b0c1-e25872d21977] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:37.740 - [主从合并没有主键(100)][增强JS] - Node 增强JS[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:01:37.744 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:37.744 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:37.767 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:37.767 - [主从合并没有主键(100)][b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] - Node b38213d0-63f7-4d7d-acbb-f5c8a84fecc9[b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] running status set to false 
[INFO ] 2024-11-14 12:01:37.767 - [主从合并没有主键(100)][b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] - Node b38213d0-63f7-4d7d-acbb-f5c8a84fecc9[b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.767 - [主从合并没有主键(100)][b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] - Node b38213d0-63f7-4d7d-acbb-f5c8a84fecc9[b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] monitor closed 
[INFO ] 2024-11-14 12:01:37.767 - [主从合并没有主键(100)][b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] - Node b38213d0-63f7-4d7d-acbb-f5c8a84fecc9[b38213d0-63f7-4d7d-acbb-f5c8a84fecc9] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.767 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:37.767 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:37.768 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:37.828 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.829 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:37.829 - [主从合并没有主键(100)][5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] - Node 5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f[5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:37.829 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.829 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.829 - [主从合并没有主键(100)][5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] - Node 5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f[5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:37.829 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:37.876 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:37.876 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:37.885 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556897652 
[INFO ] 2024-11-14 12:01:37.885 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556897652 
[INFO ] 2024-11-14 12:01:37.885 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:37.886 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:37.886 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 11 ms 
[INFO ] 2024-11-14 12:01:38.038 - [主从合并没有主键(100)][增强JS] - Node pare[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.038 - [主从合并没有主键(100)][c5f7f450-1694-49a8-85dc-fe5b4076fb07] - Node c5f7f450-1694-49a8-85dc-fe5b4076fb07[c5f7f450-1694-49a8-85dc-fe5b4076fb07] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:38.038 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.038 - [主从合并没有主键(100)][c5f7f450-1694-49a8-85dc-fe5b4076fb07] - Node c5f7f450-1694-49a8-85dc-fe5b4076fb07[c5f7f450-1694-49a8-85dc-fe5b4076fb07] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.047 - [主从合并没有主键(100)][增强JS] - Node pare[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.047 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.047 - [主从合并没有主键(100)][增强JS] - Node js_processor(pare: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:38.072 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:38.072 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:38.081 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556897475 
[INFO ] 2024-11-14 12:01:38.081 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556897475 
[INFO ] 2024-11-14 12:01:38.081 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.081 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:38.081 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 10 ms 
[WARN ] 2024-11-14 12:01:38.148 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:38.157 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:38.165 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556897926 
[INFO ] 2024-11-14 12:01:38.165 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556897926 
[INFO ] 2024-11-14 12:01:38.165 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.165 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:38.165 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 19 ms 
[INFO ] 2024-11-14 12:01:38.245 - [主从合并没有主键(100)][f0ff80ae-5747-4542-9b13-8497b766a4ea] - Node f0ff80ae-5747-4542-9b13-8497b766a4ea[f0ff80ae-5747-4542-9b13-8497b766a4ea] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:38.245 - [主从合并没有主键(100)][增强JS] - Node paren[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.245 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.245 - [主从合并没有主键(100)][f0ff80ae-5747-4542-9b13-8497b766a4ea] - Node f0ff80ae-5747-4542-9b13-8497b766a4ea[f0ff80ae-5747-4542-9b13-8497b766a4ea] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.245 - [主从合并没有主键(100)][增强JS] - Node paren[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:38.246 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:38.247 - [主从合并没有主键(100)][增强JS] - Node js_processor(paren: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:38.310 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:38.310 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:38.323 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556897473 
[INFO ] 2024-11-14 12:01:38.326 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556897473 
[INFO ] 2024-11-14 12:01:38.326 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.326 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:38.326 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 21 ms 
[INFO ] 2024-11-14 12:01:38.385 - [主从合并没有主键(100)][增强JS] - Node p[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:38.385 - [主从合并没有主键(100)][08008add-8b43-4323-80ce-bfca0d0e64c9] - Node 08008add-8b43-4323-80ce-bfca0d0e64c9[08008add-8b43-4323-80ce-bfca0d0e64c9] running status set to false 
[INFO ] 2024-11-14 12:01:38.385 - [主从合并没有主键(100)][08008add-8b43-4323-80ce-bfca0d0e64c9] - Node 08008add-8b43-4323-80ce-bfca0d0e64c9[08008add-8b43-4323-80ce-bfca0d0e64c9] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.385 - [主从合并没有主键(100)][08008add-8b43-4323-80ce-bfca0d0e64c9] - Node 08008add-8b43-4323-80ce-bfca0d0e64c9[08008add-8b43-4323-80ce-bfca0d0e64c9] monitor closed 
[INFO ] 2024-11-14 12:01:38.385 - [主从合并没有主键(100)][08008add-8b43-4323-80ce-bfca0d0e64c9] - Node 08008add-8b43-4323-80ce-bfca0d0e64c9[08008add-8b43-4323-80ce-bfca0d0e64c9] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:01:38.391 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-aa2e1147-6511-4449-9a59-37f436668810 
[INFO ] 2024-11-14 12:01:38.391 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-aa2e1147-6511-4449-9a59-37f436668810 
[INFO ] 2024-11-14 12:01:38.392 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.393 - [主从合并没有主键(100)][增强JS] - Node p[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.393 - [主从合并没有主键(100)][增强JS] - Node p[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:38.396 - [主从合并没有主键(100)][增强JS] - Node p[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 17 ms 
[INFO ] 2024-11-14 12:01:38.396 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:38.396 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:38.455 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:38.456 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.456 - [主从合并没有主键(100)][7df17064-a029-494d-9835-06fc2814b4e1] - Node 7df17064-a029-494d-9835-06fc2814b4e1[7df17064-a029-494d-9835-06fc2814b4e1] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:38.456 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.456 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:38.456 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:38.456 - [主从合并没有主键(100)][7df17064-a029-494d-9835-06fc2814b4e1] - Node 7df17064-a029-494d-9835-06fc2814b4e1[7df17064-a029-494d-9835-06fc2814b4e1] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:38.456 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:01:38.465 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.465 - [主从合并没有主键(100)][f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] - Node f7ea9a8c-90a4-4378-9d1d-7c8911c6b048[f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:38.465 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.465 - [主从合并没有主键(100)][f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] - Node f7ea9a8c-90a4-4378-9d1d-7c8911c6b048[f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.465 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.465 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.465 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:38.584 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[WARN ] 2024-11-14 12:01:38.584 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:38.585 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:38.585 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:38.588 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556898319 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556898063 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556898319 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556898063 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 8 ms 
[INFO ] 2024-11-14 12:01:38.589 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 10 ms 
[INFO ] 2024-11-14 12:01:38.758 - [主从合并没有主键(100)][增强JS] - Node pa[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:38.758 - [主从合并没有主键(100)][增强JS] - Node par[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:38.758 - [主从合并没有主键(100)][048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] - Node 048d33ae-0c0b-4622-9e6b-7ae394b0d4e2[048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] running status set to false 
[INFO ] 2024-11-14 12:01:38.759 - [主从合并没有主键(100)][5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] - Node 5b9a21dc-ba75-47cb-937a-cb86b26ce1b0[5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] running status set to false 
[INFO ] 2024-11-14 12:01:38.759 - [主从合并没有主键(100)][048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] - Node 048d33ae-0c0b-4622-9e6b-7ae394b0d4e2[048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.759 - [主从合并没有主键(100)][5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] - Node 5b9a21dc-ba75-47cb-937a-cb86b26ce1b0[5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.759 - [主从合并没有主键(100)][048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] - Node 048d33ae-0c0b-4622-9e6b-7ae394b0d4e2[048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] monitor closed 
[INFO ] 2024-11-14 12:01:38.759 - [主从合并没有主键(100)][5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] - Node 5b9a21dc-ba75-47cb-937a-cb86b26ce1b0[5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] monitor closed 
[INFO ] 2024-11-14 12:01:38.759 - [主从合并没有主键(100)][048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] - Node 048d33ae-0c0b-4622-9e6b-7ae394b0d4e2[048d33ae-0c0b-4622-9e6b-7ae394b0d4e2] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.759 - [主从合并没有主键(100)][5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] - Node 5b9a21dc-ba75-47cb-937a-cb86b26ce1b0[5b9a21dc-ba75-47cb-937a-cb86b26ce1b0] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.771 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-74024165-82e5-4011-90c5-b0bb8f0d8aa6 
[INFO ] 2024-11-14 12:01:38.771 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-129b9bc1-7dab-472b-977a-400107119dc6 
[INFO ] 2024-11-14 12:01:38.771 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-74024165-82e5-4011-90c5-b0bb8f0d8aa6 
[INFO ] 2024-11-14 12:01:38.771 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-129b9bc1-7dab-472b-977a-400107119dc6 
[INFO ] 2024-11-14 12:01:38.771 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.771 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.771 - [主从合并没有主键(100)][增强JS] - Node pa[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.772 - [主从合并没有主键(100)][增强JS] - Node par[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.772 - [主从合并没有主键(100)][增强JS] - Node pa[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:38.772 - [主从合并没有主键(100)][增强JS] - Node par[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:38.772 - [主从合并没有主键(100)][增强JS] - Node pa[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 14 ms 
[INFO ] 2024-11-14 12:01:38.772 - [主从合并没有主键(100)][增强JS] - Node par[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 14 ms 
[INFO ] 2024-11-14 12:01:38.773 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:38.773 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:38.773 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:38.773 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:38.774 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:38.774 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:38.850 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][d11c62a1-aa97-4d07-ae53-12f453b8c4b6] - Node d11c62a1-aa97-4d07-ae53-12f453b8c4b6[d11c62a1-aa97-4d07-ae53-12f453b8c4b6] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][bd7fade4-2529-46e3-8bbe-6f630248e428] - Node bd7fade4-2529-46e3-8bbe-6f630248e428[bd7fade4-2529-46e3-8bbe-6f630248e428] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][d11c62a1-aa97-4d07-ae53-12f453b8c4b6] - Node d11c62a1-aa97-4d07-ae53-12f453b8c4b6[d11c62a1-aa97-4d07-ae53-12f453b8c4b6] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][bd7fade4-2529-46e3-8bbe-6f630248e428] - Node bd7fade4-2529-46e3-8bbe-6f630248e428[bd7fade4-2529-46e3-8bbe-6f630248e428] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:38.851 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:38.883 - [主从合并没有主键(100)][增强JS] - Node pare[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:38.883 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-c5ad106f-5c41-46bc-93fd-d7e826431149 
[INFO ] 2024-11-14 12:01:38.883 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-c5ad106f-5c41-46bc-93fd-d7e826431149 
[INFO ] 2024-11-14 12:01:38.883 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.885 - [主从合并没有主键(100)][增强JS] - Node pare[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.885 - [主从合并没有主键(100)][增强JS] - Node pare[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:38.885 - [主从合并没有主键(100)][增强JS] - Node pare[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:01:38.886 - [主从合并没有主键(100)][c5f7f450-1694-49a8-85dc-fe5b4076fb07] - Node c5f7f450-1694-49a8-85dc-fe5b4076fb07[c5f7f450-1694-49a8-85dc-fe5b4076fb07] running status set to false 
[INFO ] 2024-11-14 12:01:38.886 - [主从合并没有主键(100)][c5f7f450-1694-49a8-85dc-fe5b4076fb07] - Node c5f7f450-1694-49a8-85dc-fe5b4076fb07[c5f7f450-1694-49a8-85dc-fe5b4076fb07] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.886 - [主从合并没有主键(100)][c5f7f450-1694-49a8-85dc-fe5b4076fb07] - Node c5f7f450-1694-49a8-85dc-fe5b4076fb07[c5f7f450-1694-49a8-85dc-fe5b4076fb07] monitor closed 
[INFO ] 2024-11-14 12:01:38.886 - [主从合并没有主键(100)][c5f7f450-1694-49a8-85dc-fe5b4076fb07] - Node c5f7f450-1694-49a8-85dc-fe5b4076fb07[c5f7f450-1694-49a8-85dc-fe5b4076fb07] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.887 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:38.887 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:38.887 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:38.928 - [主从合并没有主键(100)][7a6f0570-6327-4c35-b515-15f82ded28ed] - Node 7a6f0570-6327-4c35-b515-15f82ded28ed[7a6f0570-6327-4c35-b515-15f82ded28ed] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:38.928 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.928 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:38.928 - [主从合并没有主键(100)][7a6f0570-6327-4c35-b515-15f82ded28ed] - Node 7a6f0570-6327-4c35-b515-15f82ded28ed[7a6f0570-6327-4c35-b515-15f82ded28ed] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.928 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.928 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:38.937 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:38.945 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:38.948 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:38.948 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556898498 
[INFO ] 2024-11-14 12:01:38.948 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556898498 
[INFO ] 2024-11-14 12:01:38.948 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:38.948 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:39.116 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 4 ms 
[WARN ] 2024-11-14 12:01:39.121 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:39.133 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:39.133 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898862 
[INFO ] 2024-11-14 12:01:39.133 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898862 
[INFO ] 2024-11-14 12:01:39.133 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.133 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:39.133 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 14 ms 
[WARN ] 2024-11-14 12:01:39.173 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[WARN ] 2024-11-14 12:01:39.174 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:39.188 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:39.189 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898936 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898498 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898936 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898498 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 8 ms 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 10 ms 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:39.200 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:39.201 - [主从合并没有主键(100)][f991da51-4c1d-4b61-b5c4-392ffdf68d49] - Node f991da51-4c1d-4b61-b5c4-392ffdf68d49[f991da51-4c1d-4b61-b5c4-392ffdf68d49] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:39.201 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.201 - [主从合并没有主键(100)][f991da51-4c1d-4b61-b5c4-392ffdf68d49] - Node f991da51-4c1d-4b61-b5c4-392ffdf68d49[f991da51-4c1d-4b61-b5c4-392ffdf68d49] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.201 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.387 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:39.387 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:39.394 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:39.395 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898861 
[INFO ] 2024-11-14 12:01:39.395 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556898861 
[INFO ] 2024-11-14 12:01:39.395 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.395 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:39.434 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 8 ms 
[INFO ] 2024-11-14 12:01:39.434 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:39.434 - [主从合并没有主键(100)][7df17064-a029-494d-9835-06fc2814b4e1] - Node 7df17064-a029-494d-9835-06fc2814b4e1[7df17064-a029-494d-9835-06fc2814b4e1] running status set to false 
[INFO ] 2024-11-14 12:01:39.435 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:39.435 - [主从合并没有主键(100)][7df17064-a029-494d-9835-06fc2814b4e1] - Node 7df17064-a029-494d-9835-06fc2814b4e1[7df17064-a029-494d-9835-06fc2814b4e1] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.435 - [主从合并没有主键(100)][7df17064-a029-494d-9835-06fc2814b4e1] - Node 7df17064-a029-494d-9835-06fc2814b4e1[7df17064-a029-494d-9835-06fc2814b4e1] monitor closed 
[INFO ] 2024-11-14 12:01:39.435 - [主从合并没有主键(100)][7df17064-a029-494d-9835-06fc2814b4e1] - Node 7df17064-a029-494d-9835-06fc2814b4e1[7df17064-a029-494d-9835-06fc2814b4e1] close complete, cost 1 ms 
[INFO ] 2024-11-14 12:01:39.458 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-89f5a213-344d-4a0f-8cd3-5b33b2814bc0 
[INFO ] 2024-11-14 12:01:39.458 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-55083844-4514-4b01-b27d-6ee9232fa0e1 
[INFO ] 2024-11-14 12:01:39.458 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-89f5a213-344d-4a0f-8cd3-5b33b2814bc0 
[INFO ] 2024-11-14 12:01:39.458 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-55083844-4514-4b01-b27d-6ee9232fa0e1 
[INFO ] 2024-11-14 12:01:39.459 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.459 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.461 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.461 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.461 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:39.461 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:39.461 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 31 ms 
[INFO ] 2024-11-14 12:01:39.461 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 32 ms 
[INFO ] 2024-11-14 12:01:39.462 - [主从合并没有主键(100)][5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] - Node 5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f[5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] running status set to false 
[INFO ] 2024-11-14 12:01:39.462 - [主从合并没有主键(100)][5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] - Node 5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f[5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.462 - [主从合并没有主键(100)][5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] - Node 5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f[5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] monitor closed 
[INFO ] 2024-11-14 12:01:39.464 - [主从合并没有主键(100)][5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] - Node 5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f[5e26f52e-9b1d-4f2f-b0a8-f6c57d894b1f] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.464 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:39.464 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:39.464 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:39.464 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:39.465 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:39.520 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:39.521 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:39.527 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:39.527 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556899232 
[INFO ] 2024-11-14 12:01:39.527 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556899232 
[INFO ] 2024-11-14 12:01:39.527 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.527 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:39.603 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:01:39.603 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:39.603 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:39.604 - [主从合并没有主键(100)][fcb97488-f2ca-459b-88cb-93a3fda2077c] - Node fcb97488-f2ca-459b-88cb-93a3fda2077c[fcb97488-f2ca-459b-88cb-93a3fda2077c] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:39.604 - [主从合并没有主键(100)][fcb97488-f2ca-459b-88cb-93a3fda2077c] - Node fcb97488-f2ca-459b-88cb-93a3fda2077c[fcb97488-f2ca-459b-88cb-93a3fda2077c] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.604 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.604 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:39.604 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:39.644 - [主从合并没有主键(100)][增强JS] - Node paren[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:39.644 - [主从合并没有主键(100)][f0ff80ae-5747-4542-9b13-8497b766a4ea] - Node f0ff80ae-5747-4542-9b13-8497b766a4ea[f0ff80ae-5747-4542-9b13-8497b766a4ea] running status set to false 
[INFO ] 2024-11-14 12:01:39.644 - [主从合并没有主键(100)][f0ff80ae-5747-4542-9b13-8497b766a4ea] - Node f0ff80ae-5747-4542-9b13-8497b766a4ea[f0ff80ae-5747-4542-9b13-8497b766a4ea] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.644 - [主从合并没有主键(100)][f0ff80ae-5747-4542-9b13-8497b766a4ea] - Node f0ff80ae-5747-4542-9b13-8497b766a4ea[f0ff80ae-5747-4542-9b13-8497b766a4ea] monitor closed 
[INFO ] 2024-11-14 12:01:39.675 - [主从合并没有主键(100)][f0ff80ae-5747-4542-9b13-8497b766a4ea] - Node f0ff80ae-5747-4542-9b13-8497b766a4ea[f0ff80ae-5747-4542-9b13-8497b766a4ea] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.675 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-0a004f5b-1c2f-4769-8a6f-4b898a4e7491 
[INFO ] 2024-11-14 12:01:39.675 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-0a004f5b-1c2f-4769-8a6f-4b898a4e7491 
[INFO ] 2024-11-14 12:01:39.675 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.687 - [主从合并没有主键(100)][增强JS] - Node paren[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.687 - [主从合并没有主键(100)][增强JS] - Node paren[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:39.687 - [主从合并没有主键(100)][增强JS] - Node paren[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 47 ms 
[INFO ] 2024-11-14 12:01:39.688 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:39.688 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:39.690 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:39.697 - [主从合并没有主键(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.IllegalStateException: state should be: server session pool is openio.tapdata.entity.event.dml.TapInsertRecordEvent@c019f52: {"after":{"name":"sample string","id":1.0,"childId":1.0},"containsIllegalDate":false,"tableId":"test_merge_no_primary_key_parent","time":1731556899520,"type":300}
 
[ERROR] 2024-11-14 12:01:39.728 - [主从合并没有主键(100)][增强JS] - java.lang.IllegalStateException: state should be: server session pool is openio.tapdata.entity.event.dml.TapInsertRecordEvent@c019f52: {"after":{"name":"sample string","id":1.0,"childId":1.0},"containsIllegalDate":false,"tableId":"test_merge_no_primary_key_parent","time":1731556899520,"type":300}
 <-- Error Message -->
java.lang.IllegalStateException: state should be: server session pool is openio.tapdata.entity.event.dml.TapInsertRecordEvent@c019f52: {"after":{"name":"sample string","id":1.0,"childId":1.0},"containsIllegalDate":false,"tableId":"test_merge_no_primary_key_parent","time":1731556899520,"type":300}


<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:268)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:172)
	at com.mongodb.client.internal.MongoIterableImpl.execute(MongoIterableImpl.java:132)
	at com.mongodb.client.internal.MongoIterableImpl.iterator(MongoIterableImpl.java:86)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:451)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:198)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$null$0(HazelcastJavaScriptProcessorNode.java:174)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:174)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:298)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:275)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:259)
	... 18 more

[INFO ] 2024-11-14 12:01:39.729 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:39.732 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-6233e9ed-4cf9-4951-8204-115802fa15d1 
[INFO ] 2024-11-14 12:01:39.733 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-6233e9ed-4cf9-4951-8204-115802fa15d1 
[INFO ] 2024-11-14 12:01:39.733 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.736 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.736 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:39.737 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 15 ms 
[INFO ] 2024-11-14 12:01:39.749 - [主从合并没有主键(100)][7a6f0570-6327-4c35-b515-15f82ded28ed] - Node 7a6f0570-6327-4c35-b515-15f82ded28ed[7a6f0570-6327-4c35-b515-15f82ded28ed] running status set to false 
[INFO ] 2024-11-14 12:01:39.749 - [主从合并没有主键(100)][7a6f0570-6327-4c35-b515-15f82ded28ed] - Node 7a6f0570-6327-4c35-b515-15f82ded28ed[7a6f0570-6327-4c35-b515-15f82ded28ed] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.749 - [主从合并没有主键(100)][7a6f0570-6327-4c35-b515-15f82ded28ed] - Node 7a6f0570-6327-4c35-b515-15f82ded28ed[7a6f0570-6327-4c35-b515-15f82ded28ed] monitor closed 
[INFO ] 2024-11-14 12:01:39.750 - [主从合并没有主键(100)][7a6f0570-6327-4c35-b515-15f82ded28ed] - Node 7a6f0570-6327-4c35-b515-15f82ded28ed[7a6f0570-6327-4c35-b515-15f82ded28ed] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:39.751 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:39.751 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:39.810 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:39.811 - [主从合并没有主键(100)][0b34c997-8c0f-4c2c-925a-e66b266651b4] - Node 0b34c997-8c0f-4c2c-925a-e66b266651b4[0b34c997-8c0f-4c2c-925a-e66b266651b4] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:39.811 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:39.811 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:39.811 - [主从合并没有主键(100)][0b34c997-8c0f-4c2c-925a-e66b266651b4] - Node 0b34c997-8c0f-4c2c-925a-e66b266651b4[0b34c997-8c0f-4c2c-925a-e66b266651b4] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.811 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:39.811 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:39.811 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:39.883 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:39.886 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-40fe5aeb-5a50-4a68-980e-a6407c1115d3 
[INFO ] 2024-11-14 12:01:39.886 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-40fe5aeb-5a50-4a68-980e-a6407c1115d3 
[INFO ] 2024-11-14 12:01:39.887 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.888 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.888 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:39.888 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 13 ms 
[INFO ] 2024-11-14 12:01:39.909 - [主从合并没有主键(100)][d11c62a1-aa97-4d07-ae53-12f453b8c4b6] - Node d11c62a1-aa97-4d07-ae53-12f453b8c4b6[d11c62a1-aa97-4d07-ae53-12f453b8c4b6] running status set to false 
[INFO ] 2024-11-14 12:01:39.911 - [主从合并没有主键(100)][d11c62a1-aa97-4d07-ae53-12f453b8c4b6] - Node d11c62a1-aa97-4d07-ae53-12f453b8c4b6[d11c62a1-aa97-4d07-ae53-12f453b8c4b6] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.911 - [主从合并没有主键(100)][d11c62a1-aa97-4d07-ae53-12f453b8c4b6] - Node d11c62a1-aa97-4d07-ae53-12f453b8c4b6[d11c62a1-aa97-4d07-ae53-12f453b8c4b6] monitor closed 
[INFO ] 2024-11-14 12:01:39.911 - [主从合并没有主键(100)][d11c62a1-aa97-4d07-ae53-12f453b8c4b6] - Node d11c62a1-aa97-4d07-ae53-12f453b8c4b6[d11c62a1-aa97-4d07-ae53-12f453b8c4b6] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:39.914 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:39.914 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:39.914 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:39.959 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:39.961 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-348c0555-32d9-4a88-8a61-c446aedeea64 
[INFO ] 2024-11-14 12:01:39.961 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-348c0555-32d9-4a88-8a61-c446aedeea64 
[INFO ] 2024-11-14 12:01:39.961 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.962 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.962 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:39.962 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 8 ms 
[INFO ] 2024-11-14 12:01:39.968 - [主从合并没有主键(100)][f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] - Node f7ea9a8c-90a4-4378-9d1d-7c8911c6b048[f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] running status set to false 
[INFO ] 2024-11-14 12:01:39.968 - [主从合并没有主键(100)][f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] - Node f7ea9a8c-90a4-4378-9d1d-7c8911c6b048[f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] schema data cleaned 
[INFO ] 2024-11-14 12:01:39.968 - [主从合并没有主键(100)][f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] - Node f7ea9a8c-90a4-4378-9d1d-7c8911c6b048[f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] monitor closed 
[INFO ] 2024-11-14 12:01:39.968 - [主从合并没有主键(100)][f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] - Node f7ea9a8c-90a4-4378-9d1d-7c8911c6b048[f7ea9a8c-90a4-4378-9d1d-7c8911c6b048] close complete, cost 1 ms 
[INFO ] 2024-11-14 12:01:39.971 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:39.971 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:39.971 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:40.087 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:40.087 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:40.103 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556899842 
[INFO ] 2024-11-14 12:01:40.103 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556899842 
[INFO ] 2024-11-14 12:01:40.103 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.104 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:40.104 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 17 ms 
[INFO ] 2024-11-14 12:01:40.179 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:40.179 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-7249d4d5-8f1d-4d9e-97f9-3856314725d3 
[INFO ] 2024-11-14 12:01:40.179 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-7249d4d5-8f1d-4d9e-97f9-3856314725d3 
[INFO ] 2024-11-14 12:01:40.179 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.180 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.181 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:40.189 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 31 ms 
[INFO ] 2024-11-14 12:01:40.190 - [主从合并没有主键(100)][bd7fade4-2529-46e3-8bbe-6f630248e428] - Node bd7fade4-2529-46e3-8bbe-6f630248e428[bd7fade4-2529-46e3-8bbe-6f630248e428] running status set to false 
[INFO ] 2024-11-14 12:01:40.190 - [主从合并没有主键(100)][bd7fade4-2529-46e3-8bbe-6f630248e428] - Node bd7fade4-2529-46e3-8bbe-6f630248e428[bd7fade4-2529-46e3-8bbe-6f630248e428] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.190 - [主从合并没有主键(100)][bd7fade4-2529-46e3-8bbe-6f630248e428] - Node bd7fade4-2529-46e3-8bbe-6f630248e428[bd7fade4-2529-46e3-8bbe-6f630248e428] monitor closed 
[INFO ] 2024-11-14 12:01:40.190 - [主从合并没有主键(100)][bd7fade4-2529-46e3-8bbe-6f630248e428] - Node bd7fade4-2529-46e3-8bbe-6f630248e428[bd7fade4-2529-46e3-8bbe-6f630248e428] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:40.191 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:40.191 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:40.254 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:40.254 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:40.255 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:40.255 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556899700 
[INFO ] 2024-11-14 12:01:40.255 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556899700 
[INFO ] 2024-11-14 12:01:40.255 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.256 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:40.256 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 1 ms 
[INFO ] 2024-11-14 12:01:40.458 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:40.461 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-c5930b09-33be-493b-a169-87771a6b19eb 
[INFO ] 2024-11-14 12:01:40.462 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-c5930b09-33be-493b-a169-87771a6b19eb 
[INFO ] 2024-11-14 12:01:40.462 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.462 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.463 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:40.475 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 5 ms 
[INFO ] 2024-11-14 12:01:40.475 - [主从合并没有主键(100)][0b34c997-8c0f-4c2c-925a-e66b266651b4] - Node 0b34c997-8c0f-4c2c-925a-e66b266651b4[0b34c997-8c0f-4c2c-925a-e66b266651b4] running status set to false 
[INFO ] 2024-11-14 12:01:40.475 - [主从合并没有主键(100)][0b34c997-8c0f-4c2c-925a-e66b266651b4] - Node 0b34c997-8c0f-4c2c-925a-e66b266651b4[0b34c997-8c0f-4c2c-925a-e66b266651b4] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.475 - [主从合并没有主键(100)][0b34c997-8c0f-4c2c-925a-e66b266651b4] - Node 0b34c997-8c0f-4c2c-925a-e66b266651b4[0b34c997-8c0f-4c2c-925a-e66b266651b4] monitor closed 
[INFO ] 2024-11-14 12:01:40.476 - [主从合并没有主键(100)][0b34c997-8c0f-4c2c-925a-e66b266651b4] - Node 0b34c997-8c0f-4c2c-925a-e66b266651b4[0b34c997-8c0f-4c2c-925a-e66b266651b4] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:40.476 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:40.476 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:40.621 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:40.621 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:40.622 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-6148b761-e191-47b5-947d-466e66f60b8c 
[INFO ] 2024-11-14 12:01:40.622 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-6148b761-e191-47b5-947d-466e66f60b8c 
[INFO ] 2024-11-14 12:01:40.622 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.626 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.626 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:40.626 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:01:40.638 - [主从合并没有主键(100)][fcb97488-f2ca-459b-88cb-93a3fda2077c] - Node fcb97488-f2ca-459b-88cb-93a3fda2077c[fcb97488-f2ca-459b-88cb-93a3fda2077c] running status set to false 
[INFO ] 2024-11-14 12:01:40.638 - [主从合并没有主键(100)][fcb97488-f2ca-459b-88cb-93a3fda2077c] - Node fcb97488-f2ca-459b-88cb-93a3fda2077c[fcb97488-f2ca-459b-88cb-93a3fda2077c] schema data cleaned 
[INFO ] 2024-11-14 12:01:40.638 - [主从合并没有主键(100)][fcb97488-f2ca-459b-88cb-93a3fda2077c] - Node fcb97488-f2ca-459b-88cb-93a3fda2077c[fcb97488-f2ca-459b-88cb-93a3fda2077c] monitor closed 
[INFO ] 2024-11-14 12:01:40.638 - [主从合并没有主键(100)][fcb97488-f2ca-459b-88cb-93a3fda2077c] - Node fcb97488-f2ca-459b-88cb-93a3fda2077c[fcb97488-f2ca-459b-88cb-93a3fda2077c] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:40.638 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:40.638 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:40.638 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:41.133 - [主从合并没有主键(100)][9cbccfdf-24e7-460f-93c4-d16cd132086d] - Node 9cbccfdf-24e7-460f-93c4-d16cd132086d[9cbccfdf-24e7-460f-93c4-d16cd132086d] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:41.133 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:41.133 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:41.133 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:41.133 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:41.134 - [主从合并没有主键(100)][9cbccfdf-24e7-460f-93c4-d16cd132086d] - Node 9cbccfdf-24e7-460f-93c4-d16cd132086d[9cbccfdf-24e7-460f-93c4-d16cd132086d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:41.316 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:41.316 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:41.318 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:41.318 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556901147 
[INFO ] 2024-11-14 12:01:41.318 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556901147 
[INFO ] 2024-11-14 12:01:41.318 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:41.318 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:41.318 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:41.599 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:41.603 - [主从合并没有主键(100)][9cbccfdf-24e7-460f-93c4-d16cd132086d] - Node 9cbccfdf-24e7-460f-93c4-d16cd132086d[9cbccfdf-24e7-460f-93c4-d16cd132086d] running status set to false 
[INFO ] 2024-11-14 12:01:41.603 - [主从合并没有主键(100)][9cbccfdf-24e7-460f-93c4-d16cd132086d] - Node 9cbccfdf-24e7-460f-93c4-d16cd132086d[9cbccfdf-24e7-460f-93c4-d16cd132086d] schema data cleaned 
[INFO ] 2024-11-14 12:01:41.603 - [主从合并没有主键(100)][9cbccfdf-24e7-460f-93c4-d16cd132086d] - Node 9cbccfdf-24e7-460f-93c4-d16cd132086d[9cbccfdf-24e7-460f-93c4-d16cd132086d] monitor closed 
[INFO ] 2024-11-14 12:01:41.603 - [主从合并没有主键(100)][9cbccfdf-24e7-460f-93c4-d16cd132086d] - Node 9cbccfdf-24e7-460f-93c4-d16cd132086d[9cbccfdf-24e7-460f-93c4-d16cd132086d] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:41.606 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-4ed4f3b4-b454-4d14-972d-88e07673a625 
[INFO ] 2024-11-14 12:01:41.606 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-4ed4f3b4-b454-4d14-972d-88e07673a625 
[INFO ] 2024-11-14 12:01:41.607 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:41.607 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:41.607 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:41.608 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 11 ms 
[INFO ] 2024-11-14 12:01:41.609 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:41.609 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:41.730 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:41.731 - [主从合并没有主键(100)][1460023f-4c07-42f3-8150-6d1d648cd864] - Node 1460023f-4c07-42f3-8150-6d1d648cd864[1460023f-4c07-42f3-8150-6d1d648cd864] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:41.731 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:41.731 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:41.731 - [主从合并没有主键(100)][1460023f-4c07-42f3-8150-6d1d648cd864] - Node 1460023f-4c07-42f3-8150-6d1d648cd864[1460023f-4c07-42f3-8150-6d1d648cd864] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:41.731 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:41.731 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:41.731 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:42.051 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:42.060 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:42.060 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556901857 
[INFO ] 2024-11-14 12:01:42.060 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556901857 
[INFO ] 2024-11-14 12:01:42.060 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.061 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:42.061 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 10 ms 
[INFO ] 2024-11-14 12:01:42.264 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:42.264 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.264 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:42.265 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:42.265 - [主从合并没有主键(100)][f991da51-4c1d-4b61-b5c4-392ffdf68d49] - Node f991da51-4c1d-4b61-b5c4-392ffdf68d49[f991da51-4c1d-4b61-b5c4-392ffdf68d49] running status set to false 
[INFO ] 2024-11-14 12:01:42.265 - [主从合并没有主键(100)][f991da51-4c1d-4b61-b5c4-392ffdf68d49] - Node f991da51-4c1d-4b61-b5c4-392ffdf68d49[f991da51-4c1d-4b61-b5c4-392ffdf68d49] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.265 - [主从合并没有主键(100)][f991da51-4c1d-4b61-b5c4-392ffdf68d49] - Node f991da51-4c1d-4b61-b5c4-392ffdf68d49[f991da51-4c1d-4b61-b5c4-392ffdf68d49] monitor closed 
[INFO ] 2024-11-14 12:01:42.267 - [主从合并没有主键(100)][f991da51-4c1d-4b61-b5c4-392ffdf68d49] - Node f991da51-4c1d-4b61-b5c4-392ffdf68d49[f991da51-4c1d-4b61-b5c4-392ffdf68d49] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:42.267 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:42.267 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:42.346 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:42.346 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:42.347 - [主从合并没有主键(100)][f0cd10d9-2c84-406b-b049-e4ae9cf9c933] - Node f0cd10d9-2c84-406b-b049-e4ae9cf9c933[f0cd10d9-2c84-406b-b049-e4ae9cf9c933] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:42.347 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:42.347 - [主从合并没有主键(100)][f0cd10d9-2c84-406b-b049-e4ae9cf9c933] - Node f0cd10d9-2c84-406b-b049-e4ae9cf9c933[f0cd10d9-2c84-406b-b049-e4ae9cf9c933] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:42.347 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:42.348 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:42.349 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:01:42.408 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:42.408 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-424c57b4-ed7f-4e10-8030-5db37f755bb1 
[INFO ] 2024-11-14 12:01:42.408 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-424c57b4-ed7f-4e10-8030-5db37f755bb1 
[INFO ] 2024-11-14 12:01:42.408 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.409 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.409 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:42.409 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:01:42.415 - [主从合并没有主键(100)][1460023f-4c07-42f3-8150-6d1d648cd864] - Node 1460023f-4c07-42f3-8150-6d1d648cd864[1460023f-4c07-42f3-8150-6d1d648cd864] running status set to false 
[INFO ] 2024-11-14 12:01:42.415 - [主从合并没有主键(100)][1460023f-4c07-42f3-8150-6d1d648cd864] - Node 1460023f-4c07-42f3-8150-6d1d648cd864[1460023f-4c07-42f3-8150-6d1d648cd864] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.415 - [主从合并没有主键(100)][1460023f-4c07-42f3-8150-6d1d648cd864] - Node 1460023f-4c07-42f3-8150-6d1d648cd864[1460023f-4c07-42f3-8150-6d1d648cd864] monitor closed 
[INFO ] 2024-11-14 12:01:42.416 - [主从合并没有主键(100)][1460023f-4c07-42f3-8150-6d1d648cd864] - Node 1460023f-4c07-42f3-8150-6d1d648cd864[1460023f-4c07-42f3-8150-6d1d648cd864] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:42.416 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:42.416 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:42.546 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[WARN ] 2024-11-14 12:01:42.546 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:42.549 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:42.549 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556902366 
[INFO ] 2024-11-14 12:01:42.549 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556902366 
[INFO ] 2024-11-14 12:01:42.549 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.549 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:42.549 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:01:42.817 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:42.821 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-7d467402-afc7-4d03-aae0-a7028a37529d 
[INFO ] 2024-11-14 12:01:42.821 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-7d467402-afc7-4d03-aae0-a7028a37529d 
[INFO ] 2024-11-14 12:01:42.821 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.822 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.823 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:42.828 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:01:42.828 - [主从合并没有主键(100)][f0cd10d9-2c84-406b-b049-e4ae9cf9c933] - Node f0cd10d9-2c84-406b-b049-e4ae9cf9c933[f0cd10d9-2c84-406b-b049-e4ae9cf9c933] running status set to false 
[INFO ] 2024-11-14 12:01:42.828 - [主从合并没有主键(100)][f0cd10d9-2c84-406b-b049-e4ae9cf9c933] - Node f0cd10d9-2c84-406b-b049-e4ae9cf9c933[f0cd10d9-2c84-406b-b049-e4ae9cf9c933] schema data cleaned 
[INFO ] 2024-11-14 12:01:42.828 - [主从合并没有主键(100)][f0cd10d9-2c84-406b-b049-e4ae9cf9c933] - Node f0cd10d9-2c84-406b-b049-e4ae9cf9c933[f0cd10d9-2c84-406b-b049-e4ae9cf9c933] monitor closed 
[INFO ] 2024-11-14 12:01:42.829 - [主从合并没有主键(100)][f0cd10d9-2c84-406b-b049-e4ae9cf9c933] - Node f0cd10d9-2c84-406b-b049-e4ae9cf9c933[f0cd10d9-2c84-406b-b049-e4ae9cf9c933] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:42.829 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:42.829 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:43.034 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:48.730 - [主从合并没有主键(100)][608b7048-1eaa-4ab6-ad25-16418ae3041f] - Node 608b7048-1eaa-4ab6-ad25-16418ae3041f[608b7048-1eaa-4ab6-ad25-16418ae3041f] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:48.730 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:48.730 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:48.730 - [主从合并没有主键(100)][608b7048-1eaa-4ab6-ad25-16418ae3041f] - Node 608b7048-1eaa-4ab6-ad25-16418ae3041f[608b7048-1eaa-4ab6-ad25-16418ae3041f] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:48.730 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:48.731 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:48.733 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:49.000 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:49.010 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:49.011 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556908785 
[INFO ] 2024-11-14 12:01:49.011 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556908785 
[INFO ] 2024-11-14 12:01:49.011 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.012 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:49.012 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 12 ms 
[INFO ] 2024-11-14 12:01:49.338 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:49.344 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-59c2ee7c-c6a3-4d8f-b9a3-07a96a638aae 
[INFO ] 2024-11-14 12:01:49.344 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-59c2ee7c-c6a3-4d8f-b9a3-07a96a638aae 
[INFO ] 2024-11-14 12:01:49.344 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.345 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.345 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:49.354 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 7 ms 
[INFO ] 2024-11-14 12:01:49.354 - [主从合并没有主键(100)][608b7048-1eaa-4ab6-ad25-16418ae3041f] - Node 608b7048-1eaa-4ab6-ad25-16418ae3041f[608b7048-1eaa-4ab6-ad25-16418ae3041f] running status set to false 
[INFO ] 2024-11-14 12:01:49.354 - [主从合并没有主键(100)][608b7048-1eaa-4ab6-ad25-16418ae3041f] - Node 608b7048-1eaa-4ab6-ad25-16418ae3041f[608b7048-1eaa-4ab6-ad25-16418ae3041f] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.354 - [主从合并没有主键(100)][608b7048-1eaa-4ab6-ad25-16418ae3041f] - Node 608b7048-1eaa-4ab6-ad25-16418ae3041f[608b7048-1eaa-4ab6-ad25-16418ae3041f] monitor closed 
[INFO ] 2024-11-14 12:01:49.354 - [主从合并没有主键(100)][608b7048-1eaa-4ab6-ad25-16418ae3041f] - Node 608b7048-1eaa-4ab6-ad25-16418ae3041f[608b7048-1eaa-4ab6-ad25-16418ae3041f] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:49.356 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:49.356 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:49.425 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:49.426 - [主从合并没有主键(100)][70cd69b2-4318-40cd-9cc3-73f344962089] - Node 70cd69b2-4318-40cd-9cc3-73f344962089[70cd69b2-4318-40cd-9cc3-73f344962089] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:49.426 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:49.426 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:49.426 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:49.426 - [主从合并没有主键(100)][70cd69b2-4318-40cd-9cc3-73f344962089] - Node 70cd69b2-4318-40cd-9cc3-73f344962089[70cd69b2-4318-40cd-9cc3-73f344962089] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:49.426 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:49.427 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:49.648 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:49.650 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:49.650 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556909488 
[INFO ] 2024-11-14 12:01:49.650 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556909488 
[INFO ] 2024-11-14 12:01:49.650 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.650 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:49.651 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:49.899 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:49.905 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-eb342902-383d-4cde-b912-5d84f93f466e 
[INFO ] 2024-11-14 12:01:49.905 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-eb342902-383d-4cde-b912-5d84f93f466e 
[INFO ] 2024-11-14 12:01:49.906 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.907 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.907 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:49.907 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 11 ms 
[INFO ] 2024-11-14 12:01:49.926 - [主从合并没有主键(100)][70cd69b2-4318-40cd-9cc3-73f344962089] - Node 70cd69b2-4318-40cd-9cc3-73f344962089[70cd69b2-4318-40cd-9cc3-73f344962089] running status set to false 
[INFO ] 2024-11-14 12:01:49.926 - [主从合并没有主键(100)][70cd69b2-4318-40cd-9cc3-73f344962089] - Node 70cd69b2-4318-40cd-9cc3-73f344962089[70cd69b2-4318-40cd-9cc3-73f344962089] schema data cleaned 
[INFO ] 2024-11-14 12:01:49.926 - [主从合并没有主键(100)][70cd69b2-4318-40cd-9cc3-73f344962089] - Node 70cd69b2-4318-40cd-9cc3-73f344962089[70cd69b2-4318-40cd-9cc3-73f344962089] monitor closed 
[INFO ] 2024-11-14 12:01:49.926 - [主从合并没有主键(100)][70cd69b2-4318-40cd-9cc3-73f344962089] - Node 70cd69b2-4318-40cd-9cc3-73f344962089[70cd69b2-4318-40cd-9cc3-73f344962089] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:01:49.928 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:49.928 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:50.142 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:52.074 - [主从合并没有主键(100)][40a42579-1c94-4e0e-9fa0-91882247ba62] - Node 40a42579-1c94-4e0e-9fa0-91882247ba62[40a42579-1c94-4e0e-9fa0-91882247ba62] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:52.074 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:52.074 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:52.074 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:52.074 - [主从合并没有主键(100)][40a42579-1c94-4e0e-9fa0-91882247ba62] - Node 40a42579-1c94-4e0e-9fa0-91882247ba62[40a42579-1c94-4e0e-9fa0-91882247ba62] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:52.074 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:01:52.074 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:01:52.312 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:52.312 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:01:52.319 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556912112 
[INFO ] 2024-11-14 12:01:52.319 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556912112 
[INFO ] 2024-11-14 12:01:52.319 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:01:52.319 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:01:52.524 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 7 ms 
[INFO ] 2024-11-14 12:01:52.617 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:01:52.617 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-03bcd67a-cf10-47b6-a43a-8f2657b0d6c5 
[INFO ] 2024-11-14 12:01:52.618 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-03bcd67a-cf10-47b6-a43a-8f2657b0d6c5 
[INFO ] 2024-11-14 12:01:52.620 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:52.620 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:01:52.620 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:01:52.620 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:01:52.627 - [主从合并没有主键(100)][40a42579-1c94-4e0e-9fa0-91882247ba62] - Node 40a42579-1c94-4e0e-9fa0-91882247ba62[40a42579-1c94-4e0e-9fa0-91882247ba62] running status set to false 
[INFO ] 2024-11-14 12:01:52.627 - [主从合并没有主键(100)][40a42579-1c94-4e0e-9fa0-91882247ba62] - Node 40a42579-1c94-4e0e-9fa0-91882247ba62[40a42579-1c94-4e0e-9fa0-91882247ba62] schema data cleaned 
[INFO ] 2024-11-14 12:01:52.627 - [主从合并没有主键(100)][40a42579-1c94-4e0e-9fa0-91882247ba62] - Node 40a42579-1c94-4e0e-9fa0-91882247ba62[40a42579-1c94-4e0e-9fa0-91882247ba62] monitor closed 
[INFO ] 2024-11-14 12:01:52.627 - [主从合并没有主键(100)][40a42579-1c94-4e0e-9fa0-91882247ba62] - Node 40a42579-1c94-4e0e-9fa0-91882247ba62[40a42579-1c94-4e0e-9fa0-91882247ba62] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:52.629 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:52.629 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:52.629 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:01:52.687 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:52.687 - [主从合并没有主键(100)][1d32e320-f4e8-4950-bd1d-459b40a22b33] - Node 1d32e320-f4e8-4950-bd1d-459b40a22b33[1d32e320-f4e8-4950-bd1d-459b40a22b33] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:01:52.687 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:01:52.687 - [主从合并没有主键(100)][1d32e320-f4e8-4950-bd1d-459b40a22b33] - Node 1d32e320-f4e8-4950-bd1d-459b40a22b33[1d32e320-f4e8-4950-bd1d-459b40a22b33] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:52.687 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:52.687 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:01:52.893 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:01:52.912 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:01:52.912 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:01:52.914 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556912747 
[INFO ] 2024-11-14 12:01:52.915 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556912747 
[INFO ] 2024-11-14 12:01:52.915 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:01:52.915 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:01:53.120 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:01:53.183 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:01:53.183 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-4a8ab56e-0aa5-409e-9efe-f311a09df89c 
[INFO ] 2024-11-14 12:01:53.183 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-4a8ab56e-0aa5-409e-9efe-f311a09df89c 
[INFO ] 2024-11-14 12:01:53.183 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:01:53.184 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:01:53.184 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:01:53.192 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 5 ms 
[INFO ] 2024-11-14 12:01:53.192 - [主从合并没有主键(100)][1d32e320-f4e8-4950-bd1d-459b40a22b33] - Node 1d32e320-f4e8-4950-bd1d-459b40a22b33[1d32e320-f4e8-4950-bd1d-459b40a22b33] running status set to false 
[INFO ] 2024-11-14 12:01:53.192 - [主从合并没有主键(100)][1d32e320-f4e8-4950-bd1d-459b40a22b33] - Node 1d32e320-f4e8-4950-bd1d-459b40a22b33[1d32e320-f4e8-4950-bd1d-459b40a22b33] schema data cleaned 
[INFO ] 2024-11-14 12:01:53.192 - [主从合并没有主键(100)][1d32e320-f4e8-4950-bd1d-459b40a22b33] - Node 1d32e320-f4e8-4950-bd1d-459b40a22b33[1d32e320-f4e8-4950-bd1d-459b40a22b33] monitor closed 
[INFO ] 2024-11-14 12:01:53.193 - [主从合并没有主键(100)][1d32e320-f4e8-4950-bd1d-459b40a22b33] - Node 1d32e320-f4e8-4950-bd1d-459b40a22b33[1d32e320-f4e8-4950-bd1d-459b40a22b33] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:01:53.193 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:01:53.193 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:01:53.395 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:03.274 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:03.274 - [主从合并没有主键(100)][bba7642e-54c0-4ab5-9a0e-277b5b709629] - Node bba7642e-54c0-4ab5-9a0e-277b5b709629[bba7642e-54c0-4ab5-9a0e-277b5b709629] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:03.274 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:03.275 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:03.275 - [主从合并没有主键(100)][bba7642e-54c0-4ab5-9a0e-277b5b709629] - Node bba7642e-54c0-4ab5-9a0e-277b5b709629[bba7642e-54c0-4ab5-9a0e-277b5b709629] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:03.275 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:03.317 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[INFO ] 2024-11-14 12:02:03.317 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:03.317 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:03.317 - [主从合并没有主键(100)][53c108bc-24d4-409e-b397-15b2d0539cc3] - Node 53c108bc-24d4-409e-b397-15b2d0539cc3[53c108bc-24d4-409e-b397-15b2d0539cc3] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:03.318 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:03.318 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:03.318 - [主从合并没有主键(100)][53c108bc-24d4-409e-b397-15b2d0539cc3] - Node 53c108bc-24d4-409e-b397-15b2d0539cc3[53c108bc-24d4-409e-b397-15b2d0539cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:03.460 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:02:03.460 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:03.462 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:02:03.462 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556923294 
[INFO ] 2024-11-14 12:02:03.462 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556923294 
[INFO ] 2024-11-14 12:02:03.462 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:02:03.463 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:02:03.463 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 2 ms 
[WARN ] 2024-11-14 12:02:03.655 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:03.655 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:02:03.660 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556923326 
[INFO ] 2024-11-14 12:02:03.660 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556923326 
[INFO ] 2024-11-14 12:02:03.660 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:02:03.660 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:02:03.660 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 5 ms 
[INFO ] 2024-11-14 12:02:03.815 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:02:03.815 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-75bc395a-1804-4186-99ef-c6e8f1383c08 
[INFO ] 2024-11-14 12:02:03.815 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-75bc395a-1804-4186-99ef-c6e8f1383c08 
[INFO ] 2024-11-14 12:02:03.815 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:03.816 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:02:03.816 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:02:03.823 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 25 ms 
[INFO ] 2024-11-14 12:02:03.824 - [主从合并没有主键(100)][bba7642e-54c0-4ab5-9a0e-277b5b709629] - Node bba7642e-54c0-4ab5-9a0e-277b5b709629[bba7642e-54c0-4ab5-9a0e-277b5b709629] running status set to false 
[INFO ] 2024-11-14 12:02:03.824 - [主从合并没有主键(100)][bba7642e-54c0-4ab5-9a0e-277b5b709629] - Node bba7642e-54c0-4ab5-9a0e-277b5b709629[bba7642e-54c0-4ab5-9a0e-277b5b709629] schema data cleaned 
[INFO ] 2024-11-14 12:02:03.824 - [主从合并没有主键(100)][bba7642e-54c0-4ab5-9a0e-277b5b709629] - Node bba7642e-54c0-4ab5-9a0e-277b5b709629[bba7642e-54c0-4ab5-9a0e-277b5b709629] monitor closed 
[INFO ] 2024-11-14 12:02:03.824 - [主从合并没有主键(100)][bba7642e-54c0-4ab5-9a0e-277b5b709629] - Node bba7642e-54c0-4ab5-9a0e-277b5b709629[bba7642e-54c0-4ab5-9a0e-277b5b709629] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:03.826 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:03.826 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:03.827 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:03.998 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:03.998 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:03.999 - [主从合并没有主键(100)][89fee648-efd8-4212-9952-d763ab9ec3a4] - Node 89fee648-efd8-4212-9952-d763ab9ec3a4[89fee648-efd8-4212-9952-d763ab9ec3a4] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:03.999 - [主从合并没有主键(100)][89fee648-efd8-4212-9952-d763ab9ec3a4] - Node 89fee648-efd8-4212-9952-d763ab9ec3a4[89fee648-efd8-4212-9952-d763ab9ec3a4] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:02:03.999 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 3 ms 
[INFO ] 2024-11-14 12:02:03.999 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 4 ms 
[INFO ] 2024-11-14 12:02:04.063 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[INFO ] 2024-11-14 12:02:04.063 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:02:04.075 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-aa6d2f38-d155-4bae-9461-656ba1f11318 
[INFO ] 2024-11-14 12:02:04.075 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-aa6d2f38-d155-4bae-9461-656ba1f11318 
[INFO ] 2024-11-14 12:02:04.075 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.090 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.090 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:02:04.090 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 37 ms 
[INFO ] 2024-11-14 12:02:04.111 - [主从合并没有主键(100)][53c108bc-24d4-409e-b397-15b2d0539cc3] - Node 53c108bc-24d4-409e-b397-15b2d0539cc3[53c108bc-24d4-409e-b397-15b2d0539cc3] running status set to false 
[INFO ] 2024-11-14 12:02:04.111 - [主从合并没有主键(100)][53c108bc-24d4-409e-b397-15b2d0539cc3] - Node 53c108bc-24d4-409e-b397-15b2d0539cc3[53c108bc-24d4-409e-b397-15b2d0539cc3] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.111 - [主从合并没有主键(100)][53c108bc-24d4-409e-b397-15b2d0539cc3] - Node 53c108bc-24d4-409e-b397-15b2d0539cc3[53c108bc-24d4-409e-b397-15b2d0539cc3] monitor closed 
[INFO ] 2024-11-14 12:02:04.111 - [主从合并没有主键(100)][53c108bc-24d4-409e-b397-15b2d0539cc3] - Node 53c108bc-24d4-409e-b397-15b2d0539cc3[53c108bc-24d4-409e-b397-15b2d0539cc3] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.113 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:04.113 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:04.114 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:04.211 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:04.211 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:04.211 - [主从合并没有主键(100)][8f29269b-da79-4736-bae1-4ea59b039e33] - Node 8f29269b-da79-4736-bae1-4ea59b039e33[8f29269b-da79-4736-bae1-4ea59b039e33] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:04.211 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.211 - [主从合并没有主键(100)][8f29269b-da79-4736-bae1-4ea59b039e33] - Node 8f29269b-da79-4736-bae1-4ea59b039e33[8f29269b-da79-4736-bae1-4ea59b039e33] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.211 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.323 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:02:04.323 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:04.328 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:02:04.328 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556924151 
[INFO ] 2024-11-14 12:02:04.328 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556924151 
[INFO ] 2024-11-14 12:02:04.328 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.329 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:02:04.329 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 4 ms 
[WARN ] 2024-11-14 12:02:04.494 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:04.494 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:02:04.497 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556924223 
[INFO ] 2024-11-14 12:02:04.497 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556924223 
[INFO ] 2024-11-14 12:02:04.497 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.497 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:02:04.497 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:02:04.627 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:02:04.627 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-c6bf56a5-1fb5-4f1f-9e65-bd5298f7c702 
[INFO ] 2024-11-14 12:02:04.627 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-c6bf56a5-1fb5-4f1f-9e65-bd5298f7c702 
[INFO ] 2024-11-14 12:02:04.628 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.628 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.629 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:02:04.629 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:02:04.633 - [主从合并没有主键(100)][89fee648-efd8-4212-9952-d763ab9ec3a4] - Node 89fee648-efd8-4212-9952-d763ab9ec3a4[89fee648-efd8-4212-9952-d763ab9ec3a4] running status set to false 
[INFO ] 2024-11-14 12:02:04.633 - [主从合并没有主键(100)][89fee648-efd8-4212-9952-d763ab9ec3a4] - Node 89fee648-efd8-4212-9952-d763ab9ec3a4[89fee648-efd8-4212-9952-d763ab9ec3a4] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.633 - [主从合并没有主键(100)][89fee648-efd8-4212-9952-d763ab9ec3a4] - Node 89fee648-efd8-4212-9952-d763ab9ec3a4[89fee648-efd8-4212-9952-d763ab9ec3a4] monitor closed 
[INFO ] 2024-11-14 12:02:04.633 - [主从合并没有主键(100)][89fee648-efd8-4212-9952-d763ab9ec3a4] - Node 89fee648-efd8-4212-9952-d763ab9ec3a4[89fee648-efd8-4212-9952-d763ab9ec3a4] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.633 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:04.633 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:04.634 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:04.787 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:02:04.789 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-61bc8426-8553-416f-b51d-128ec593b79a 
[INFO ] 2024-11-14 12:02:04.789 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-61bc8426-8553-416f-b51d-128ec593b79a 
[INFO ] 2024-11-14 12:02:04.789 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.789 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.789 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:02:04.789 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 5 ms 
[INFO ] 2024-11-14 12:02:04.794 - [主从合并没有主键(100)][8f29269b-da79-4736-bae1-4ea59b039e33] - Node 8f29269b-da79-4736-bae1-4ea59b039e33[8f29269b-da79-4736-bae1-4ea59b039e33] running status set to false 
[INFO ] 2024-11-14 12:02:04.795 - [主从合并没有主键(100)][8f29269b-da79-4736-bae1-4ea59b039e33] - Node 8f29269b-da79-4736-bae1-4ea59b039e33[8f29269b-da79-4736-bae1-4ea59b039e33] schema data cleaned 
[INFO ] 2024-11-14 12:02:04.795 - [主从合并没有主键(100)][8f29269b-da79-4736-bae1-4ea59b039e33] - Node 8f29269b-da79-4736-bae1-4ea59b039e33[8f29269b-da79-4736-bae1-4ea59b039e33] monitor closed 
[INFO ] 2024-11-14 12:02:04.795 - [主从合并没有主键(100)][8f29269b-da79-4736-bae1-4ea59b039e33] - Node 8f29269b-da79-4736-bae1-4ea59b039e33[8f29269b-da79-4736-bae1-4ea59b039e33] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.795 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:04.795 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:04.865 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:04.865 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:04.865 - [主从合并没有主键(100)][7ad683d8-2823-474e-b55d-b74ca091a02a] - Node 7ad683d8-2823-474e-b55d-b74ca091a02a[7ad683d8-2823-474e-b55d-b74ca091a02a] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:04.865 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:04.865 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.865 - [主从合并没有主键(100)][7ad683d8-2823-474e-b55d-b74ca091a02a] - Node 7ad683d8-2823-474e-b55d-b74ca091a02a[7ad683d8-2823-474e-b55d-b74ca091a02a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.865 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:04.866 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:02:05.049 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:05.049 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:02:05.051 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556924883 
[INFO ] 2024-11-14 12:02:05.051 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556924883 
[INFO ] 2024-11-14 12:02:05.051 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.051 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:02:05.257 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:02:05.311 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:02:05.311 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-176db923-d0a6-4471-9368-6d7a1ccd1219 
[INFO ] 2024-11-14 12:02:05.312 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-176db923-d0a6-4471-9368-6d7a1ccd1219 
[INFO ] 2024-11-14 12:02:05.313 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.313 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.313 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:02:05.317 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 7 ms 
[INFO ] 2024-11-14 12:02:05.317 - [主从合并没有主键(100)][7ad683d8-2823-474e-b55d-b74ca091a02a] - Node 7ad683d8-2823-474e-b55d-b74ca091a02a[7ad683d8-2823-474e-b55d-b74ca091a02a] running status set to false 
[INFO ] 2024-11-14 12:02:05.317 - [主从合并没有主键(100)][7ad683d8-2823-474e-b55d-b74ca091a02a] - Node 7ad683d8-2823-474e-b55d-b74ca091a02a[7ad683d8-2823-474e-b55d-b74ca091a02a] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.317 - [主从合并没有主键(100)][7ad683d8-2823-474e-b55d-b74ca091a02a] - Node 7ad683d8-2823-474e-b55d-b74ca091a02a[7ad683d8-2823-474e-b55d-b74ca091a02a] monitor closed 
[INFO ] 2024-11-14 12:02:05.317 - [主从合并没有主键(100)][7ad683d8-2823-474e-b55d-b74ca091a02a] - Node 7ad683d8-2823-474e-b55d-b74ca091a02a[7ad683d8-2823-474e-b55d-b74ca091a02a] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:05.318 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:05.318 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:05.380 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:05.380 - [主从合并没有主键(100)][05186bfe-3c94-4766-aabb-5d37087f70f8] - Node 05186bfe-3c94-4766-aabb-5d37087f70f8[05186bfe-3c94-4766-aabb-5d37087f70f8] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:05.380 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:05.380 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:05.380 - [主从合并没有主键(100)][05186bfe-3c94-4766-aabb-5d37087f70f8] - Node 05186bfe-3c94-4766-aabb-5d37087f70f8[05186bfe-3c94-4766-aabb-5d37087f70f8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:05.380 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:05.380 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:05.586 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:02:05.608 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:05.608 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:02:05.611 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556925437 
[INFO ] 2024-11-14 12:02:05.611 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556925437 
[INFO ] 2024-11-14 12:02:05.611 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.611 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:02:05.812 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:02:05.887 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:02:05.888 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-8de2b9b6-62de-4688-96c8-60b80a5b8ccd 
[INFO ] 2024-11-14 12:02:05.888 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-8de2b9b6-62de-4688-96c8-60b80a5b8ccd 
[INFO ] 2024-11-14 12:02:05.888 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.889 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.889 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:02:05.900 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:02:05.900 - [主从合并没有主键(100)][05186bfe-3c94-4766-aabb-5d37087f70f8] - Node 05186bfe-3c94-4766-aabb-5d37087f70f8[05186bfe-3c94-4766-aabb-5d37087f70f8] running status set to false 
[INFO ] 2024-11-14 12:02:05.900 - [主从合并没有主键(100)][05186bfe-3c94-4766-aabb-5d37087f70f8] - Node 05186bfe-3c94-4766-aabb-5d37087f70f8[05186bfe-3c94-4766-aabb-5d37087f70f8] schema data cleaned 
[INFO ] 2024-11-14 12:02:05.900 - [主从合并没有主键(100)][05186bfe-3c94-4766-aabb-5d37087f70f8] - Node 05186bfe-3c94-4766-aabb-5d37087f70f8[05186bfe-3c94-4766-aabb-5d37087f70f8] monitor closed 
[INFO ] 2024-11-14 12:02:05.901 - [主从合并没有主键(100)][05186bfe-3c94-4766-aabb-5d37087f70f8] - Node 05186bfe-3c94-4766-aabb-5d37087f70f8[05186bfe-3c94-4766-aabb-5d37087f70f8] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:05.901 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:05.901 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:06.106 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:06.487 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:06.487 - [主从合并没有主键(100)][180937f9-a762-41c9-a494-22656e0f1624] - Node 180937f9-a762-41c9-a494-22656e0f1624[180937f9-a762-41c9-a494-22656e0f1624] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:06.487 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:06.487 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:06.487 - [主从合并没有主键(100)][180937f9-a762-41c9-a494-22656e0f1624] - Node 180937f9-a762-41c9-a494-22656e0f1624[180937f9-a762-41c9-a494-22656e0f1624] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:06.487 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:06.487 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:02:06.670 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:06.673 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:02:06.673 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556926502 
[INFO ] 2024-11-14 12:02:06.673 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556926502 
[INFO ] 2024-11-14 12:02:06.673 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:02:06.673 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:02:06.673 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 3 ms 
[INFO ] 2024-11-14 12:02:06.927 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:02:06.935 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-78cdcbd4-e834-4abe-ae3e-a6585ada636b 
[INFO ] 2024-11-14 12:02:06.935 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-78cdcbd4-e834-4abe-ae3e-a6585ada636b 
[INFO ] 2024-11-14 12:02:06.936 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:06.937 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:02:06.938 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:02:06.942 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 11 ms 
[INFO ] 2024-11-14 12:02:06.942 - [主从合并没有主键(100)][180937f9-a762-41c9-a494-22656e0f1624] - Node 180937f9-a762-41c9-a494-22656e0f1624[180937f9-a762-41c9-a494-22656e0f1624] running status set to false 
[INFO ] 2024-11-14 12:02:06.943 - [主从合并没有主键(100)][180937f9-a762-41c9-a494-22656e0f1624] - Node 180937f9-a762-41c9-a494-22656e0f1624[180937f9-a762-41c9-a494-22656e0f1624] schema data cleaned 
[INFO ] 2024-11-14 12:02:06.943 - [主从合并没有主键(100)][180937f9-a762-41c9-a494-22656e0f1624] - Node 180937f9-a762-41c9-a494-22656e0f1624[180937f9-a762-41c9-a494-22656e0f1624] monitor closed 
[INFO ] 2024-11-14 12:02:06.944 - [主从合并没有主键(100)][180937f9-a762-41c9-a494-22656e0f1624] - Node 180937f9-a762-41c9-a494-22656e0f1624[180937f9-a762-41c9-a494-22656e0f1624] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:02:06.944 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:06.944 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:06.945 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:07.052 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:07.053 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:07.054 - [主从合并没有主键(100)][486714f5-6634-402f-b9e1-d210dcac8154] - Node 486714f5-6634-402f-b9e1-d210dcac8154[486714f5-6634-402f-b9e1-d210dcac8154] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:07.054 - [主从合并没有主键(100)][486714f5-6634-402f-b9e1-d210dcac8154] - Node 486714f5-6634-402f-b9e1-d210dcac8154[486714f5-6634-402f-b9e1-d210dcac8154] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:07.054 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:07.054 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:07.263 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:02:07.363 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:07.363 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:02:07.372 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556927158 
[INFO ] 2024-11-14 12:02:07.372 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556927158 
[INFO ] 2024-11-14 12:02:07.372 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:02:07.372 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:02:07.372 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 9 ms 
[INFO ] 2024-11-14 12:02:07.663 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:02:07.666 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-838e2d8a-f62f-461b-b7b3-8d21e07bed7f 
[INFO ] 2024-11-14 12:02:07.666 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-838e2d8a-f62f-461b-b7b3-8d21e07bed7f 
[INFO ] 2024-11-14 12:02:07.667 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:07.668 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:02:07.668 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:02:07.679 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 6 ms 
[INFO ] 2024-11-14 12:02:07.679 - [主从合并没有主键(100)][486714f5-6634-402f-b9e1-d210dcac8154] - Node 486714f5-6634-402f-b9e1-d210dcac8154[486714f5-6634-402f-b9e1-d210dcac8154] running status set to false 
[INFO ] 2024-11-14 12:02:07.679 - [主从合并没有主键(100)][486714f5-6634-402f-b9e1-d210dcac8154] - Node 486714f5-6634-402f-b9e1-d210dcac8154[486714f5-6634-402f-b9e1-d210dcac8154] schema data cleaned 
[INFO ] 2024-11-14 12:02:07.679 - [主从合并没有主键(100)][486714f5-6634-402f-b9e1-d210dcac8154] - Node 486714f5-6634-402f-b9e1-d210dcac8154[486714f5-6634-402f-b9e1-d210dcac8154] monitor closed 
[INFO ] 2024-11-14 12:02:07.681 - [主从合并没有主键(100)][486714f5-6634-402f-b9e1-d210dcac8154] - Node 486714f5-6634-402f-b9e1-d210dcac8154[486714f5-6634-402f-b9e1-d210dcac8154] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:07.681 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:07.681 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:07.682 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:09.346 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:09.346 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:09.346 - [主从合并没有主键(100)][bb3e1697-55d9-4560-b675-90d96a1dc081] - Node bb3e1697-55d9-4560-b675-90d96a1dc081[bb3e1697-55d9-4560-b675-90d96a1dc081] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:09.346 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:09.346 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:09.347 - [主从合并没有主键(100)][bb3e1697-55d9-4560-b675-90d96a1dc081] - Node bb3e1697-55d9-4560-b675-90d96a1dc081[bb3e1697-55d9-4560-b675-90d96a1dc081] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:09.526 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:02:09.526 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:09.530 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:02:09.530 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556929358 
[INFO ] 2024-11-14 12:02:09.530 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731556929358 
[INFO ] 2024-11-14 12:02:09.530 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:02:09.531 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:02:09.531 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 4 ms 
[INFO ] 2024-11-14 12:02:09.782 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:02:09.785 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-754013ab-a3b4-4e73-878a-04553c0901ef 
[INFO ] 2024-11-14 12:02:09.785 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-754013ab-a3b4-4e73-878a-04553c0901ef 
[INFO ] 2024-11-14 12:02:09.785 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:09.786 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:02:09.786 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:02:09.786 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 5 ms 
[INFO ] 2024-11-14 12:02:09.794 - [主从合并没有主键(100)][bb3e1697-55d9-4560-b675-90d96a1dc081] - Node bb3e1697-55d9-4560-b675-90d96a1dc081[bb3e1697-55d9-4560-b675-90d96a1dc081] running status set to false 
[INFO ] 2024-11-14 12:02:09.794 - [主从合并没有主键(100)][bb3e1697-55d9-4560-b675-90d96a1dc081] - Node bb3e1697-55d9-4560-b675-90d96a1dc081[bb3e1697-55d9-4560-b675-90d96a1dc081] schema data cleaned 
[INFO ] 2024-11-14 12:02:09.794 - [主从合并没有主键(100)][bb3e1697-55d9-4560-b675-90d96a1dc081] - Node bb3e1697-55d9-4560-b675-90d96a1dc081[bb3e1697-55d9-4560-b675-90d96a1dc081] monitor closed 
[INFO ] 2024-11-14 12:02:09.795 - [主从合并没有主键(100)][bb3e1697-55d9-4560-b675-90d96a1dc081] - Node bb3e1697-55d9-4560-b675-90d96a1dc081[bb3e1697-55d9-4560-b675-90d96a1dc081] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:09.795 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:09.795 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:09.835 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:02:09.835 - [主从合并没有主键(100)][1a3c2161-4bfd-4737-a925-e323a8ff4dca] - Node 1a3c2161-4bfd-4737-a925-e323a8ff4dca[1a3c2161-4bfd-4737-a925-e323a8ff4dca] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:02:09.835 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:09.835 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:02:09.835 - [主从合并没有主键(100)][1a3c2161-4bfd-4737-a925-e323a8ff4dca] - Node 1a3c2161-4bfd-4737-a925-e323a8ff4dca[1a3c2161-4bfd-4737-a925-e323a8ff4dca] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:09.835 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:09.835 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:02:10.040 - [主从合并没有主键(100)][增强JS] - Node js_processor(child: f1dc23b3-db3d-47f9-9d5f-e14c514f2d66) enable batch process 
[WARN ] 2024-11-14 12:02:10.063 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:02:10.063 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] running status set to false 
[INFO ] 2024-11-14 12:02:10.066 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556929898 
[INFO ] 2024-11-14 12:02:10.066 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - PDK connector node released: HazelcastSampleSourcePdkDataNode_48e06b0f-7e21-43f5-9b48-01b52c25b3f1_1731556929898 
[INFO ] 2024-11-14 12:02:10.066 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] schema data cleaned 
[INFO ] 2024-11-14 12:02:10.066 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] monitor closed 
[INFO ] 2024-11-14 12:02:10.270 - [主从合并没有主键(100)][test_merge_no_primary_key_child] - Node test_merge_no_primary_key_child[48e06b0f-7e21-43f5-9b48-01b52c25b3f1] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:02:10.318 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] running status set to false 
[INFO ] 2024-11-14 12:02:10.318 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-bf5d24b2-36ef-4cae-92f4-902ae3382952 
[INFO ] 2024-11-14 12:02:10.318 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-bf5d24b2-36ef-4cae-92f4-902ae3382952 
[INFO ] 2024-11-14 12:02:10.319 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-f1dc23b3-db3d-47f9-9d5f-e14c514f2d66-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:02:10.319 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] schema data cleaned 
[INFO ] 2024-11-14 12:02:10.319 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] monitor closed 
[INFO ] 2024-11-14 12:02:10.319 - [主从合并没有主键(100)][增强JS] - Node child[f1dc23b3-db3d-47f9-9d5f-e14c514f2d66] close complete, cost 4 ms 
[INFO ] 2024-11-14 12:02:10.327 - [主从合并没有主键(100)][1a3c2161-4bfd-4737-a925-e323a8ff4dca] - Node 1a3c2161-4bfd-4737-a925-e323a8ff4dca[1a3c2161-4bfd-4737-a925-e323a8ff4dca] running status set to false 
[INFO ] 2024-11-14 12:02:10.327 - [主从合并没有主键(100)][1a3c2161-4bfd-4737-a925-e323a8ff4dca] - Node 1a3c2161-4bfd-4737-a925-e323a8ff4dca[1a3c2161-4bfd-4737-a925-e323a8ff4dca] schema data cleaned 
[INFO ] 2024-11-14 12:02:10.327 - [主从合并没有主键(100)][1a3c2161-4bfd-4737-a925-e323a8ff4dca] - Node 1a3c2161-4bfd-4737-a925-e323a8ff4dca[1a3c2161-4bfd-4737-a925-e323a8ff4dca] monitor closed 
[INFO ] 2024-11-14 12:02:10.327 - [主从合并没有主键(100)][1a3c2161-4bfd-4737-a925-e323a8ff4dca] - Node 1a3c2161-4bfd-4737-a925-e323a8ff4dca[1a3c2161-4bfd-4737-a925-e323a8ff4dca] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:02:10.328 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:02:10.328 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:02:10.328 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:18:45.913 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:18:45.915 - [主从合并没有主键(100)][09579c60-9cd1-4311-94cd-5fd5e2be805d] - Node 09579c60-9cd1-4311-94cd-5fd5e2be805d[09579c60-9cd1-4311-94cd-5fd5e2be805d] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:18:45.916 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:18:45.916 - [主从合并没有主键(100)][09579c60-9cd1-4311-94cd-5fd5e2be805d] - Node 09579c60-9cd1-4311-94cd-5fd5e2be805d[09579c60-9cd1-4311-94cd-5fd5e2be805d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:18:45.916 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 12:18:45.916 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:18:45.916 - [主从合并没有主键(100)][增强JS] - Node js_processor(parent: b96f35a8-fb5d-4abc-95e3-5e714d9ccf58) enable batch process 
[WARN ] 2024-11-14 12:18:46.199 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:18:46.219 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:18:46.219 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731557925967 
[INFO ] 2024-11-14 12:18:46.219 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731557925967 
[INFO ] 2024-11-14 12:18:46.220 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:18:46.220 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:18:46.221 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 20 ms 
[INFO ] 2024-11-14 12:18:46.583 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] running status set to false 
[INFO ] 2024-11-14 12:18:46.593 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-1e96f08f-baab-421a-807e-84394dd6c743 
[INFO ] 2024-11-14 12:18:46.593 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-1e96f08f-baab-421a-807e-84394dd6c743 
[INFO ] 2024-11-14 12:18:46.593 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-b96f35a8-fb5d-4abc-95e3-5e714d9ccf58-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:18:46.596 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] schema data cleaned 
[INFO ] 2024-11-14 12:18:46.596 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] monitor closed 
[INFO ] 2024-11-14 12:18:46.616 - [主从合并没有主键(100)][增强JS] - Node parent[b96f35a8-fb5d-4abc-95e3-5e714d9ccf58] close complete, cost 22 ms 
[INFO ] 2024-11-14 12:18:46.616 - [主从合并没有主键(100)][09579c60-9cd1-4311-94cd-5fd5e2be805d] - Node 09579c60-9cd1-4311-94cd-5fd5e2be805d[09579c60-9cd1-4311-94cd-5fd5e2be805d] running status set to false 
[INFO ] 2024-11-14 12:18:46.616 - [主从合并没有主键(100)][09579c60-9cd1-4311-94cd-5fd5e2be805d] - Node 09579c60-9cd1-4311-94cd-5fd5e2be805d[09579c60-9cd1-4311-94cd-5fd5e2be805d] schema data cleaned 
[INFO ] 2024-11-14 12:18:46.616 - [主从合并没有主键(100)][09579c60-9cd1-4311-94cd-5fd5e2be805d] - Node 09579c60-9cd1-4311-94cd-5fd5e2be805d[09579c60-9cd1-4311-94cd-5fd5e2be805d] monitor closed 
[INFO ] 2024-11-14 12:18:46.620 - [主从合并没有主键(100)][09579c60-9cd1-4311-94cd-5fd5e2be805d] - Node 09579c60-9cd1-4311-94cd-5fd5e2be805d[09579c60-9cd1-4311-94cd-5fd5e2be805d] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:18:46.620 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:18:46.620 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:18:46.621 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:36:33.937 - [主从合并没有主键(100)][3386cecb-2072-4895-9d9b-172fd2d93ef5] - Node 3386cecb-2072-4895-9d9b-172fd2d93ef5[3386cecb-2072-4895-9d9b-172fd2d93ef5] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:36:33.938 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:36:33.938 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:36:33.938 - [主从合并没有主键(100)][3386cecb-2072-4895-9d9b-172fd2d93ef5] - Node 3386cecb-2072-4895-9d9b-172fd2d93ef5[3386cecb-2072-4895-9d9b-172fd2d93ef5] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:36:33.938 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 2 ms 
[INFO ] 2024-11-14 12:36:33.938 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] preload schema finished, cost 2 ms 
[INFO ] 2024-11-14 12:36:33.943 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: 5663c86f-ac13-4171-81c8-3931b1d914c2) enable batch process 
[WARN ] 2024-11-14 12:36:34.161 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:36:34.168 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:36:34.169 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731558993971 
[INFO ] 2024-11-14 12:36:34.169 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731558993971 
[INFO ] 2024-11-14 12:36:34.169 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:36:34.169 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:36:34.170 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 8 ms 
[INFO ] 2024-11-14 12:36:34.730 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] running status set to false 
[INFO ] 2024-11-14 12:36:34.731 - [主从合并没有主键(100)][3386cecb-2072-4895-9d9b-172fd2d93ef5] - Node 3386cecb-2072-4895-9d9b-172fd2d93ef5[3386cecb-2072-4895-9d9b-172fd2d93ef5] running status set to false 
[INFO ] 2024-11-14 12:36:34.731 - [主从合并没有主键(100)][3386cecb-2072-4895-9d9b-172fd2d93ef5] - Node 3386cecb-2072-4895-9d9b-172fd2d93ef5[3386cecb-2072-4895-9d9b-172fd2d93ef5] schema data cleaned 
[INFO ] 2024-11-14 12:36:34.731 - [主从合并没有主键(100)][3386cecb-2072-4895-9d9b-172fd2d93ef5] - Node 3386cecb-2072-4895-9d9b-172fd2d93ef5[3386cecb-2072-4895-9d9b-172fd2d93ef5] monitor closed 
[INFO ] 2024-11-14 12:36:34.731 - [主从合并没有主键(100)][3386cecb-2072-4895-9d9b-172fd2d93ef5] - Node 3386cecb-2072-4895-9d9b-172fd2d93ef5[3386cecb-2072-4895-9d9b-172fd2d93ef5] close complete, cost 1 ms 
[INFO ] 2024-11-14 12:36:34.737 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-37b216e6-f0c0-4267-8a2e-6d8633e8d919 
[INFO ] 2024-11-14 12:36:34.737 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-37b216e6-f0c0-4267-8a2e-6d8633e8d919 
[INFO ] 2024-11-14 12:36:34.738 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-5663c86f-ac13-4171-81c8-3931b1d914c2-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:36:34.739 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] schema data cleaned 
[INFO ] 2024-11-14 12:36:34.739 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] monitor closed 
[INFO ] 2024-11-14 12:36:34.741 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] close complete, cost 14 ms 
[INFO ] 2024-11-14 12:36:34.741 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:36:34.741 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:36:34.943 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 12:36:35.007 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:36:35.007 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] start preload schema,table counts: 1 
[INFO ] 2024-11-14 12:36:35.007 - [主从合并没有主键(100)][7a921bb9-2043-4d89-a5ea-09c34f39622f] - Node 7a921bb9-2043-4d89-a5ea-09c34f39622f[7a921bb9-2043-4d89-a5ea-09c34f39622f] start preload schema,table counts: 0 
[INFO ] 2024-11-14 12:36:35.007 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:36:35.007 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:36:35.007 - [主从合并没有主键(100)][7a921bb9-2043-4d89-a5ea-09c34f39622f] - Node 7a921bb9-2043-4d89-a5ea-09c34f39622f[7a921bb9-2043-4d89-a5ea-09c34f39622f] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 12:36:35.008 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: 5663c86f-ac13-4171-81c8-3931b1d914c2) enable batch process 
[WARN ] 2024-11-14 12:36:35.198 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 12:36:35.198 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 12:36:35.199 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731558995031 
[INFO ] 2024-11-14 12:36:35.199 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731558995031 
[INFO ] 2024-11-14 12:36:35.199 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 12:36:35.199 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 12:36:35.401 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 2 ms 
[INFO ] 2024-11-14 12:36:35.506 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] running status set to false 
[INFO ] 2024-11-14 12:36:35.506 - [主从合并没有主键(100)][7a921bb9-2043-4d89-a5ea-09c34f39622f] - Node 7a921bb9-2043-4d89-a5ea-09c34f39622f[7a921bb9-2043-4d89-a5ea-09c34f39622f] running status set to false 
[INFO ] 2024-11-14 12:36:35.506 - [主从合并没有主键(100)][7a921bb9-2043-4d89-a5ea-09c34f39622f] - Node 7a921bb9-2043-4d89-a5ea-09c34f39622f[7a921bb9-2043-4d89-a5ea-09c34f39622f] schema data cleaned 
[INFO ] 2024-11-14 12:36:35.506 - [主从合并没有主键(100)][7a921bb9-2043-4d89-a5ea-09c34f39622f] - Node 7a921bb9-2043-4d89-a5ea-09c34f39622f[7a921bb9-2043-4d89-a5ea-09c34f39622f] monitor closed 
[INFO ] 2024-11-14 12:36:35.510 - [主从合并没有主键(100)][7a921bb9-2043-4d89-a5ea-09c34f39622f] - Node 7a921bb9-2043-4d89-a5ea-09c34f39622f[7a921bb9-2043-4d89-a5ea-09c34f39622f] close complete, cost 0 ms 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-c1b243fb-60fa-4a44-a8f8-8da0dc50e93e 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-c1b243fb-60fa-4a44-a8f8-8da0dc50e93e 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-5663c86f-ac13-4171-81c8-3931b1d914c2-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] schema data cleaned 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] monitor closed 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] close complete, cost 7 ms 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 12:36:35.512 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 12:36:35.713 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 13:05:19.679 - [主从合并没有主键(100)][ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] - Node ff46cbf6-89ce-429e-82c9-23a2ff30f8ad[ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] start preload schema,table counts: 0 
[INFO ] 2024-11-14 13:05:19.679 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 13:05:19.681 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] start preload schema,table counts: 1 
[INFO ] 2024-11-14 13:05:19.681 - [主从合并没有主键(100)][ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] - Node ff46cbf6-89ce-429e-82c9-23a2ff30f8ad[ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 13:05:19.682 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 13:05:19.682 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 0 ms 
[INFO ] 2024-11-14 13:05:19.682 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: 5663c86f-ac13-4171-81c8-3931b1d914c2) enable batch process 
[WARN ] 2024-11-14 13:05:19.927 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 13:05:19.935 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 13:05:19.935 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731560719722 
[INFO ] 2024-11-14 13:05:19.935 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731560719722 
[INFO ] 2024-11-14 13:05:19.935 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 13:05:19.936 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 13:05:19.936 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 9 ms 
[INFO ] 2024-11-14 13:05:20.319 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] running status set to false 
[INFO ] 2024-11-14 13:05:20.320 - [主从合并没有主键(100)][ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] - Node ff46cbf6-89ce-429e-82c9-23a2ff30f8ad[ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] running status set to false 
[INFO ] 2024-11-14 13:05:20.320 - [主从合并没有主键(100)][ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] - Node ff46cbf6-89ce-429e-82c9-23a2ff30f8ad[ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] schema data cleaned 
[INFO ] 2024-11-14 13:05:20.320 - [主从合并没有主键(100)][ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] - Node ff46cbf6-89ce-429e-82c9-23a2ff30f8ad[ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] monitor closed 
[INFO ] 2024-11-14 13:05:20.320 - [主从合并没有主键(100)][ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] - Node ff46cbf6-89ce-429e-82c9-23a2ff30f8ad[ff46cbf6-89ce-429e-82c9-23a2ff30f8ad] close complete, cost 1 ms 
[INFO ] 2024-11-14 13:05:20.325 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-8d481673-ed59-44bd-8bd7-8393b732d546 
[INFO ] 2024-11-14 13:05:20.325 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-8d481673-ed59-44bd-8bd7-8393b732d546 
[INFO ] 2024-11-14 13:05:20.325 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-5663c86f-ac13-4171-81c8-3931b1d914c2-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 13:05:20.328 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] schema data cleaned 
[INFO ] 2024-11-14 13:05:20.328 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] monitor closed 
[INFO ] 2024-11-14 13:05:20.328 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] close complete, cost 14 ms 
[INFO ] 2024-11-14 13:05:20.330 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 13:05:20.330 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 13:05:20.532 - [主从合并没有主键(100)] - Stopped task aspect(s) 
[INFO ] 2024-11-14 14:04:44.959 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] start preload schema,table counts: 1 
[INFO ] 2024-11-14 14:04:44.961 - [主从合并没有主键(100)][c4e26810-d9ec-4be7-97f6-9cf93efb02c6] - Node c4e26810-d9ec-4be7-97f6-9cf93efb02c6[c4e26810-d9ec-4be7-97f6-9cf93efb02c6] start preload schema,table counts: 0 
[INFO ] 2024-11-14 14:04:44.961 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] start preload schema,table counts: 1 
[INFO ] 2024-11-14 14:04:44.962 - [主从合并没有主键(100)][c4e26810-d9ec-4be7-97f6-9cf93efb02c6] - Node c4e26810-d9ec-4be7-97f6-9cf93efb02c6[c4e26810-d9ec-4be7-97f6-9cf93efb02c6] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 14:04:44.962 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] preload schema finished, cost 1 ms 
[INFO ] 2024-11-14 14:04:44.962 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] preload schema finished, cost 2 ms 
[INFO ] 2024-11-14 14:04:44.962 - [主从合并没有主键(100)][增强JS] - Node js_processor(增强JS: 5663c86f-ac13-4171-81c8-3931b1d914c2) enable batch process 
[WARN ] 2024-11-14 14:04:45.210 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Source table is empty, trying to mock data 
[INFO ] 2024-11-14 14:04:45.216 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] running status set to false 
[INFO ] 2024-11-14 14:04:45.216 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731564285012 
[INFO ] 2024-11-14 14:04:45.217 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5895cf6b-78d3-4e36-a05f-e00ff86a5243_1731564285012 
[INFO ] 2024-11-14 14:04:45.217 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] schema data cleaned 
[INFO ] 2024-11-14 14:04:45.217 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] monitor closed 
[INFO ] 2024-11-14 14:04:45.422 - [主从合并没有主键(100)][test_merge_no_primary_key_parent] - Node test_merge_no_primary_key_parent[5895cf6b-78d3-4e36-a05f-e00ff86a5243] close complete, cost 5 ms 
[INFO ] 2024-11-14 14:04:45.532 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] running status set to false 
[INFO ] 2024-11-14 14:04:45.532 - [主从合并没有主键(100)][c4e26810-d9ec-4be7-97f6-9cf93efb02c6] - Node c4e26810-d9ec-4be7-97f6-9cf93efb02c6[c4e26810-d9ec-4be7-97f6-9cf93efb02c6] running status set to false 
[INFO ] 2024-11-14 14:04:45.532 - [主从合并没有主键(100)][c4e26810-d9ec-4be7-97f6-9cf93efb02c6] - Node c4e26810-d9ec-4be7-97f6-9cf93efb02c6[c4e26810-d9ec-4be7-97f6-9cf93efb02c6] schema data cleaned 
[INFO ] 2024-11-14 14:04:45.532 - [主从合并没有主键(100)][c4e26810-d9ec-4be7-97f6-9cf93efb02c6] - Node c4e26810-d9ec-4be7-97f6-9cf93efb02c6[c4e26810-d9ec-4be7-97f6-9cf93efb02c6] monitor closed 
[INFO ] 2024-11-14 14:04:45.532 - [主从合并没有主键(100)][c4e26810-d9ec-4be7-97f6-9cf93efb02c6] - Node c4e26810-d9ec-4be7-97f6-9cf93efb02c6[c4e26810-d9ec-4be7-97f6-9cf93efb02c6] close complete, cost 0 ms 
[INFO ] 2024-11-14 14:04:45.534 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Mysql3306-394a9336-d494-44c4-af82-a44704bd8076 
[INFO ] 2024-11-14 14:04:45.534 - [主从合并没有主键(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Mysql3306-394a9336-d494-44c4-af82-a44704bd8076 
[INFO ] 2024-11-14 14:04:45.534 - [主从合并没有主键(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67357457bb2de53147e9e537-5663c86f-ac13-4171-81c8-3931b1d914c2-6720599e584d3301f1b298c8] schema data cleaned 
[INFO ] 2024-11-14 14:04:45.535 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] schema data cleaned 
[INFO ] 2024-11-14 14:04:45.535 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] monitor closed 
[INFO ] 2024-11-14 14:04:45.535 - [主从合并没有主键(100)][增强JS] - Node 增强JS[5663c86f-ac13-4171-81c8-3931b1d914c2] close complete, cost 6 ms 
[INFO ] 2024-11-14 14:04:45.537 - [主从合并没有主键(100)] - Closed task monitor(s)
null 
[INFO ] 2024-11-14 14:04:45.537 - [主从合并没有主键(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-11-14 14:04:45.738 - [主从合并没有主键(100)] - Stopped task aspect(s) 
