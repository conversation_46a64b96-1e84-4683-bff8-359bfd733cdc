[INFO ] 2024-12-18 03:30:23.171 - [任务 1] - Task initialization... 
[INFO ] 2024-12-18 03:30:23.171 - [任务 1] - Start task milestones: 6761d13caa4ad53b67c6f1af(任务 1) 
[INFO ] 2024-12-18 03:30:23.428 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-12-18 03:30:23.493 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 03:30:23.571 - [任务 1][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 1 
[INFO ] 2024-12-18 03:30:23.573 - [任务 1][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 1 
[INFO ] 2024-12-18 03:30:23.573 - [任务 1][Dummy] - <PERSON><PERSON> Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 03:30:23.578 - [任务 1][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 03:30:24.551 - [任务 1][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 03:30:24.552 - [任务 1][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 03:30:24.552 - [任务 1][MongoTarget] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 03:30:24.629 - [任务 1][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 03:30:24.629 - [任务 1][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 03:30:24.781 - [任务 1][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 03:30:24.817 - [任务 1][MongoTarget] - Initial sync started 
[INFO ] 2024-12-18 03:30:24.818 - [任务 1][MongoTarget] - Starting batch read, table name: CAR.CLAIM 
[INFO ] 2024-12-18 03:30:24.818 - [任务 1][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 03:30:24.818 - [任务 1][MongoTarget] - Table CAR.CLAIM is going to be initial synced 
[INFO ] 2024-12-18 03:30:24.891 - [任务 1][MongoTarget] - Table [CAR.CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 03:30:24.892 - [任务 1][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 03:30:24.892 - [任务 1][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 03:30:24.892 - [任务 1][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 03:30:24.973 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 03:30:24.974 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 03:30:24.974 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-12-18 03:30:25.007 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 03:30:25.025 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 03:30:25.025 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 03:30:25.025 - [任务 1][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 03:30:25.025 - [任务 1][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 03:30:25.026 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 03:30:25.026 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-12-18 03:30:25.063 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 03:30:25.063 - [任务 1][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_任务 1, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 03:30:25.063 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 03:30:25.068 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 03:30:25.068 - [任务 1][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 03:30:25.068 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 03:30:25.071 - [任务 1][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 03:32:11.782 - [任务 1][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 03:32:11.782 - [任务 1][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 03:32:11.793 - [任务 1][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734463824425 
[INFO ] 2024-12-18 03:32:11.793 - [任务 1][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734463824425 
[INFO ] 2024-12-18 03:32:11.793 - [任务 1][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 03:32:11.794 - [任务 1][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 03:32:11.794 - [任务 1][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 32 ms 
[INFO ] 2024-12-18 03:32:11.795 - [任务 1][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 03:32:11.820 - [任务 1][Dummy] - Stop connector 
[INFO ] 2024-12-18 03:32:11.821 - [任务 1][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734463824533 
[INFO ] 2024-12-18 03:32:11.821 - [任务 1][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734463824533 
[INFO ] 2024-12-18 03:32:11.822 - [任务 1][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 03:32:11.825 - [任务 1][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 03:32:11.826 - [任务 1][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 29 ms 
[INFO ] 2024-12-18 03:32:12.562 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 03:32:12.563 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5a49af1f 
[INFO ] 2024-12-18 03:32:12.726 - [任务 1] - Stop task milestones: 6761d13caa4ad53b67c6f1af(任务 1)  
[INFO ] 2024-12-18 03:32:12.729 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-12-18 03:32:12.729 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 03:32:12.819 - [任务 1] - Remove memory task client succeed, task: 任务 1[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 03:32:12.820 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 03:34:14.869 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 03:34:14.872 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 03:34:15.165 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 03:34:15.167 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 03:34:15.218 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 2 
[INFO ] 2024-12-18 03:34:15.218 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 2 
[INFO ] 2024-12-18 03:34:15.218 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 03:34:15.218 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 03:34:15.392 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 03:34:15.397 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 03:34:15.412 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 03:34:15.412 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 03:34:15.423 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 03:34:15.428 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CLAIM":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 03:34:15.511 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM.2 
[INFO ] 2024-12-18 03:34:15.511 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM.2 is going to be initial synced 
[INFO ] 2024-12-18 03:34:15.597 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 03:34:15.597 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM.2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 03:34:15.597 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 03:34:15.598 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 03:34:15.598 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 03:34:15.644 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 03:34:15.644 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 03:34:15.644 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 03:34:15.693 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 03:34:15.693 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 03:34:15.693 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 03:34:15.693 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 03:34:15.693 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 03:34:15.693 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 03:34:15.694 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-12-18 03:34:15.720 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 03:34:15.727 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 03:34:15.727 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 03:34:15.730 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 03:34:15.732 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 03:34:15.739 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 03:34:15.739 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 03:34:15.987 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 03:34:15.988 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 03:34:15.990 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 03:34:15.990 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CLAIM.2, CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 03:34:15.990 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 0 
[INFO ] 2024-12-18 03:34:15.991 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=0} 
[INFO ] 2024-12-18 10:16:22.920 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 10:16:22.928 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 10:16:22.929 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734464055275 
[INFO ] 2024-12-18 10:16:22.929 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734464055275 
[INFO ] 2024-12-18 10:16:22.929 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 10:16:22.929 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 10:16:22.936 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 75 ms 
[INFO ] 2024-12-18 10:16:22.952 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 10:16:22.953 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 10:16:22.955 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734464055275 
[INFO ] 2024-12-18 10:16:22.955 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734464055275 
[INFO ] 2024-12-18 10:16:22.955 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 10:16:22.958 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 10:16:22.960 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 20 ms 
[INFO ] 2024-12-18 10:16:23.310 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 10:16:23.330 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5e1340c 
[INFO ] 2024-12-18 10:16:23.331 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 10:16:23.465 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 10:16:23.465 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 10:16:23.513 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 10:16:23.516 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 12:09:49.923 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 12:09:50.131 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 12:09:51.143 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 12:09:51.144 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:09:51.631 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 3 
[INFO ] 2024-12-18 12:09:51.633 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 3 
[INFO ] 2024-12-18 12:09:51.633 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:09:51.634 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 12:09:52.408 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 12:09:52.410 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 12:09:52.602 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 12:09:52.603 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 12:09:52.605 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 12:09:52.612 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 12:09:52.721 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM.3 
[INFO ] 2024-12-18 12:09:52.723 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM.3 is going to be initial synced 
[INFO ] 2024-12-18 12:09:52.777 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 12:09:52.891 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM.3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:09:52.892 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 12:09:52.894 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 12:09:52.895 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 12:09:52.990 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 12:09:52.991 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 12:09:52.992 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 12:09:53.070 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 12:09:53.070 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 12:09:53.071 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 12:09:53.071 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 12:09:53.078 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 12:09:53.078 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 12:09:53.081 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 3, partition size: 1, read thread number: 3 
[INFO ] 2024-12-18 12:09:53.212 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:09:53.212 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 12:09:53.215 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 12:09:53.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 12:09:53.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:09:53.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 12:09:53.235 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 12:09:53.282 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 12:09:53.283 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 12:09:53.285 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:09:53.285 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 0 
[INFO ] 2024-12-18 12:09:53.290 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=0} 
[INFO ] 2024-12-18 12:09:53.291 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:09:53.512 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 12:09:53.520 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 12:09:53.522 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:09:53.523 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CLAIM.2, CAR.CLAIM, CAR.CLAIM.3], data change syncing 
[INFO ] 2024-12-18 12:09:53.524 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 0 
[INFO ] 2024-12-18 12:09:53.529 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=0} 
[INFO ] 2024-12-18 12:10:11.754 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 12:10:11.763 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 12:10:11.787 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734494992092 
[INFO ] 2024-12-18 12:10:11.790 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734494992092 
[INFO ] 2024-12-18 12:10:11.791 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 12:10:11.792 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 12:10:11.801 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 50 ms 
[INFO ] 2024-12-18 12:10:11.805 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 12:10:11.828 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 12:10:11.833 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734494992092 
[INFO ] 2024-12-18 12:10:11.835 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734494992092 
[INFO ] 2024-12-18 12:10:11.836 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 12:10:11.836 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 12:10:11.836 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 33 ms 
[INFO ] 2024-12-18 12:10:12.402 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 12:10:12.411 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@541fdd61 
[INFO ] 2024-12-18 12:10:12.524 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 12:10:12.536 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 12:10:12.536 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 12:10:12.620 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 12:10:12.621 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 12:45:00.342 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 12:45:00.543 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 12:45:00.839 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 12:45:00.989 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:45:01.129 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 4 
[INFO ] 2024-12-18 12:45:01.129 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 4 
[INFO ] 2024-12-18 12:45:01.129 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:45:01.130 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:45:01.324 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 12:45:01.324 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 12:45:01.339 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 12:45:01.343 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 12:45:01.344 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 12:45:01.558 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 12:45:01.600 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.COLLECTION 
[INFO ] 2024-12-18 12:45:01.600 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.COLLECTION is going to be initial synced 
[INFO ] 2024-12-18 12:45:01.638 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 12:45:01.652 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.COLLECTION] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:45:01.663 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 12:45:01.664 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 12:45:01.665 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 12:45:01.720 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 12:45:01.720 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 12:45:01.720 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 12:45:01.782 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 12:45:01.782 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 12:45:01.782 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 12:45:01.782 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 12:45:01.783 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 12:45:01.783 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 12:45:01.783 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 4, partition size: 1, read thread number: 4 
[INFO ] 2024-12-18 12:45:01.834 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:45:01.834 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 12:45:01.834 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 12:45:01.840 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 12:45:01.840 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 12:45:01.846 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 12:45:01.846 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:45:01.853 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 12:45:01.853 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 12:45:01.856 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:45:01.856 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 0 
[INFO ] 2024-12-18 12:45:01.864 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=0} 
[INFO ] 2024-12-18 12:45:01.864 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:45:01.880 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 12:45:01.880 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 12:45:01.888 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:45:01.889 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 0 
[INFO ] 2024-12-18 12:45:01.891 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:45:01.891 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=0} 
[INFO ] 2024-12-18 12:45:01.901 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 12:45:01.904 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 12:45:01.904 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 12:45:01.904 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CLAIM.2, CAR.CLAIM, CAR.CLAIM.3, CAR.COLLECTION], data change syncing 
[INFO ] 2024-12-18 12:45:01.904 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 12:45:02.108 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 12:45:15.806 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 12:45:15.851 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 12:45:15.852 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734497101199 
[INFO ] 2024-12-18 12:45:15.853 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734497101199 
[INFO ] 2024-12-18 12:45:15.853 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 12:45:15.853 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 12:45:15.855 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 64 ms 
[INFO ] 2024-12-18 12:45:15.866 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 12:45:15.866 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 12:45:15.877 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734497101199 
[INFO ] 2024-12-18 12:45:15.877 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734497101199 
[INFO ] 2024-12-18 12:45:15.878 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 12:45:15.878 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 12:45:16.084 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 23 ms 
[INFO ] 2024-12-18 12:45:19.909 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 12:45:19.909 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@13cee570 
[INFO ] 2024-12-18 12:45:20.028 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 12:45:20.054 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 12:45:20.055 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 12:45:20.102 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 12:45:20.103 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 12:56:23.292 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 12:56:23.497 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 12:56:23.690 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 12:56:23.752 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:56:23.821 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 6 
[INFO ] 2024-12-18 12:56:23.824 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 6 
[INFO ] 2024-12-18 12:56:23.825 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:56:23.825 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:56:23.982 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 12:56:23.982 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 12:56:23.982 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 12:56:23.983 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 12:56:23.984 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 12:56:24.184 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 12:56:24.211 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CUSTOMER.BACK1 
[INFO ] 2024-12-18 12:56:24.260 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CUSTOMER.BACK1 is going to be initial synced 
[INFO ] 2024-12-18 12:56:24.261 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 12:56:24.365 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CUSTOMER.BACK1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:56:24.366 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY 
[INFO ] 2024-12-18 12:56:24.366 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY is going to be initial synced 
[INFO ] 2024-12-18 12:56:24.375 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:56:24.382 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 12:56:24.382 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 12:56:24.383 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 12:56:24.452 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 12:56:24.452 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 12:56:24.452 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 12:56:24.514 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 12:56:24.530 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 12:56:24.530 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 12:56:24.531 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 12:56:24.531 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 12:56:24.531 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 12:56:24.581 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 6, partition size: 1, read thread number: 6 
[INFO ] 2024-12-18 12:56:24.581 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:56:24.590 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 12:56:24.590 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 12:56:24.593 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 12:56:24.593 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 12:56:24.598 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 12:56:24.598 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:56:24.606 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 12:56:24.606 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 12:56:24.610 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:56:24.610 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 0 
[INFO ] 2024-12-18 12:56:24.616 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=0} 
[INFO ] 2024-12-18 12:56:24.616 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:56:24.624 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 12:56:24.624 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 12:56:24.630 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:56:24.630 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:56:24.631 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 0 
[INFO ] 2024-12-18 12:56:24.631 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=0} 
[INFO ] 2024-12-18 12:56:24.644 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 12:56:24.645 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 12:56:24.651 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 12:56:24.651 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 12:56:24.660 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 12:56:24.660 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:56:24.864 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 12:56:24.870 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 12:56:24.870 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:56:24.875 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 0 
[INFO ] 2024-12-18 12:56:24.876 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=0} 
[INFO ] 2024-12-18 12:56:25.086 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 12:56:25.107 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 12:56:25.111 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 12:56:25.114 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 12:56:25.115 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY, CAR.CLAIM, CAR.COLLECTION, CAR.CLAIM.2, CAR.CLAIM.3], data change syncing 
[INFO ] 2024-12-18 12:56:25.115 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 0 
[INFO ] 2024-12-18 12:56:25.322 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=0} 
[INFO ] 2024-12-18 13:05:43.882 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:07:35.127 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:07:35.331 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:07:36.718 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:07:36.718 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:07:37.167 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 7 
[INFO ] 2024-12-18 13:07:37.169 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 7 
[INFO ] 2024-12-18 13:07:37.169 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:07:37.170 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:07:37.807 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:07:37.811 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:07:38.054 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:07:38.057 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:07:38.057 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:07:38.195 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:07:38.202 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.1 
[INFO ] 2024-12-18 13:07:38.203 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.1 is going to be initial synced 
[INFO ] 2024-12-18 13:07:38.256 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:07:38.395 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:07:38.397 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:07:38.398 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:07:38.491 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:07:38.492 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:07:38.498 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:07:38.503 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:07:38.593 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:07:38.593 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:07:38.594 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:07:38.595 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:07:38.596 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:07:38.596 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:07:38.604 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 7, partition size: 1, read thread number: 7 
[INFO ] 2024-12-18 13:07:38.620 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:07:38.710 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:07:38.711 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 13:07:38.718 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 13:07:38.719 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:07:38.735 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:07:38.737 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:07:38.804 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:07:38.804 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:07:38.807 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:07:38.807 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 0 
[INFO ] 2024-12-18 13:07:38.808 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:07:38.808 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=0} 
[INFO ] 2024-12-18 13:07:38.824 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:07:38.830 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 13:07:38.830 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:07:38.830 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 0 
[INFO ] 2024-12-18 13:07:38.836 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=0} 
[INFO ] 2024-12-18 13:07:38.839 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:07:38.913 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:07:38.915 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 13:07:38.917 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 13:07:38.923 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:07:38.923 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:07:38.953 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:07:38.954 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:07:38.955 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 13:07:38.958 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:07:38.959 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 0 
[INFO ] 2024-12-18 13:07:38.959 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:07:38.980 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:07:39.053 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:07:39.054 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 13:07:39.058 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:07:39.059 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 0 
[INFO ] 2024-12-18 13:07:39.062 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=0} 
[INFO ] 2024-12-18 13:07:39.266 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:07:39.311 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:07:39.311 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1] 
[INFO ] 2024-12-18 13:07:39.321 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:07:39.321 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY, CAR.CLAIM, CAR.COLLECTION, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1], data change syncing 
[INFO ] 2024-12-18 13:07:39.322 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 0 
[INFO ] 2024-12-18 13:07:39.524 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:08:42.733 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:08:42.773 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:08:42.782 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734498457515 
[INFO ] 2024-12-18 13:08:42.782 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734498457515 
[INFO ] 2024-12-18 13:08:42.782 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:08:42.783 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:08:42.786 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 62 ms 
[INFO ] 2024-12-18 13:08:42.786 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:08:42.816 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:08:42.817 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734498457515 
[INFO ] 2024-12-18 13:08:42.817 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734498457515 
[INFO ] 2024-12-18 13:08:42.818 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:08:42.818 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:08:43.028 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 34 ms 
[INFO ] 2024-12-18 13:08:43.431 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:08:43.443 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7eb6db11 
[INFO ] 2024-12-18 13:08:43.581 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:08:43.581 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:08:43.619 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:08:43.619 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:08:43.619 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:08:58.988 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:08:58.991 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:08:59.383 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:08:59.511 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:08:59.512 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 8 
[INFO ] 2024-12-18 13:08:59.513 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 8 
[INFO ] 2024-12-18 13:08:59.513 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 13:08:59.514 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:08:59.823 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:08:59.825 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:08:59.843 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:08:59.843 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:08:59.851 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:08:59.963 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.1":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:08:59.963 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.3 
[INFO ] 2024-12-18 13:08:59.963 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.3 is going to be initial synced 
[INFO ] 2024-12-18 13:09:00.012 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:09:00.013 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:09:00.013 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:09:00.013 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:09:00.100 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:09:00.100 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:09:00.100 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:09:00.102 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:09:00.125 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:09:00.125 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:09:00.126 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:09:00.126 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:09:00.128 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:09:00.128 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:09:00.143 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 8, partition size: 1, read thread number: 8 
[INFO ] 2024-12-18 13:09:00.144 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.228 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:09:00.228 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 13:09:00.233 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 13:09:00.233 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:09:00.244 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:09:00.245 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.353 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:09:00.356 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:09:00.369 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:09:00.370 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 0 
[INFO ] 2024-12-18 13:09:00.370 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=0} 
[INFO ] 2024-12-18 13:09:00.409 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.409 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:09:00.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 13:09:00.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:09:00.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 0 
[INFO ] 2024-12-18 13:09:00.413 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=0} 
[INFO ] 2024-12-18 13:09:00.475 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.476 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:09:00.478 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 13:09:00.478 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 13:09:00.478 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:09:00.479 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:09:00.493 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.493 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:09:00.497 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 13:09:00.497 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:09:00.497 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 0 
[INFO ] 2024-12-18 13:09:00.498 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:09:00.570 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.571 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:09:00.571 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 13:09:00.581 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:09:00.582 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 0 
[INFO ] 2024-12-18 13:09:00.582 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=0} 
[INFO ] 2024-12-18 13:09:00.709 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.710 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:09:00.710 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1] 
[INFO ] 2024-12-18 13:09:00.711 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:09:00.711 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 0 
[INFO ] 2024-12-18 13:09:00.715 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:09:00.920 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:09:00.987 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:09:00.989 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.3] 
[INFO ] 2024-12-18 13:09:00.999 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:09:00.999 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY.3, CAR.POLICY, CAR.CLAIM, CAR.COLLECTION, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1], data change syncing 
[INFO ] 2024-12-18 13:09:00.999 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 0 
[INFO ] 2024-12-18 13:09:01.201 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=0} 
[INFO ] 2024-12-18 13:10:07.115 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:10:07.150 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:10:07.151 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734498539688 
[INFO ] 2024-12-18 13:10:07.151 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734498539688 
[INFO ] 2024-12-18 13:10:07.151 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:10:07.155 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:10:07.155 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 59 ms 
[INFO ] 2024-12-18 13:10:07.167 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:10:07.167 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:10:07.177 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734498539688 
[INFO ] 2024-12-18 13:10:07.184 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734498539688 
[INFO ] 2024-12-18 13:10:07.184 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:10:07.186 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:10:07.187 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 23 ms 
[INFO ] 2024-12-18 13:10:08.734 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:10:08.742 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@757fb0c5 
[INFO ] 2024-12-18 13:10:08.860 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:10:08.901 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:10:08.902 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:10:08.938 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:10:08.938 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:10:20.805 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:10:21.014 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:10:21.138 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:10:21.261 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:10:21.264 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 9 
[INFO ] 2024-12-18 13:10:21.264 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 9 
[INFO ] 2024-12-18 13:10:21.268 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:10:21.268 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:10:21.536 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:10:21.536 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:10:21.547 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:10:21.553 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:10:21.553 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:10:21.559 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.POLICY.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.1":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734463824,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:10:21.691 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.BACK.1 
[INFO ] 2024-12-18 13:10:21.692 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.BACK.1 is going to be initial synced 
[INFO ] 2024-12-18 13:10:21.763 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:10:21.765 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.BACK.1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:10:21.765 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:10:21.765 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:10:21.765 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:10:21.828 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:10:21.828 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:10:21.829 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:10:21.853 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:10:21.854 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:10:21.854 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:10:21.854 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:10:21.854 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:10:21.855 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:10:21.855 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 9, partition size: 1, read thread number: 9 
[INFO ] 2024-12-18 13:10:21.876 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:21.876 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:10:21.880 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 13:10:21.880 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 13:10:21.887 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:10:21.888 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:21.914 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:10:21.931 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:10:21.942 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 0 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=0} 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 0 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=0} 
[INFO ] 2024-12-18 13:10:21.943 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:21.946 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:10:21.949 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 13:10:21.949 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-17T19:30:24.551Z): 1 
[INFO ] 2024-12-18 13:10:21.953 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:10:21.955 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:10:21.960 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:21.960 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:10:21.962 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 13:10:21.962 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:10:21.962 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 0 
[INFO ] 2024-12-18 13:10:21.963 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:10:22.032 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:22.032 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:10:22.032 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 13:10:22.034 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:10:22.034 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 0 
[INFO ] 2024-12-18 13:10:22.035 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=0} 
[INFO ] 2024-12-18 13:10:22.046 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:22.046 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:10:22.048 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1] 
[INFO ] 2024-12-18 13:10:22.048 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:10:22.048 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 0 
[INFO ] 2024-12-18 13:10:22.049 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:10:22.122 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:22.122 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:10:22.129 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.3] 
[INFO ] 2024-12-18 13:10:22.130 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:10:22.130 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 0 
[INFO ] 2024-12-18 13:10:22.130 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=0} 
[INFO ] 2024-12-18 13:10:22.328 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:10:22.329 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:10:22.329 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1] 
[INFO ] 2024-12-18 13:10:22.330 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-17T19:30:24.551Z): 0 
[INFO ] 2024-12-18 13:10:22.332 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, CAR.CLAIM, CAR.COLLECTION, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1], data change syncing 
[INFO ] 2024-12-18 13:10:22.332 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 0 
[INFO ] 2024-12-18 13:10:22.535 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:14:15.867 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:14:15.869 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:14:15.883 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734498621416 
[INFO ] 2024-12-18 13:14:15.883 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734498621416 
[INFO ] 2024-12-18 13:14:15.883 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:14:15.888 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:14:15.889 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 69 ms 
[INFO ] 2024-12-18 13:14:15.889 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:14:15.904 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:14:15.906 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734498621416 
[INFO ] 2024-12-18 13:14:15.906 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734498621416 
[INFO ] 2024-12-18 13:14:15.906 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:14:15.906 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:14:16.109 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 18 ms 
[INFO ] 2024-12-18 13:14:19.403 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:14:19.540 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d3f6614 
[INFO ] 2024-12-18 13:14:19.540 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:14:19.569 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:14:19.569 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:14:19.592 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:14:19.595 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:18:58.753 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:18:58.960 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:19:00.508 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:19:00.711 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:19:00.883 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 10 
[INFO ] 2024-12-18 13:19:00.888 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 10 
[INFO ] 2024-12-18 13:19:00.888 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 2 ms 
[INFO ] 2024-12-18 13:19:00.889 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 13:19:01.600 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:19:01.605 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:19:02.064 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:19:02.072 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:19:02.073 - [测试子用户先有共享挖掘任务][MongoTarget] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 13:19:02.277 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734499142,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:19:02.338 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync started 
[INFO ] 2024-12-18 13:19:02.339 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CUSTOMER.BACK1 
[INFO ] 2024-12-18 13:19:02.344 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CUSTOMER.BACK1 is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.350 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:19:02.445 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CUSTOMER.BACK1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.446 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.3 
[INFO ] 2024-12-18 13:19:02.446 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.3 is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.461 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.462 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.BACK.1 
[INFO ] 2024-12-18 13:19:02.462 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.BACK.1 is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.535 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.BACK.1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.538 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY 
[INFO ] 2024-12-18 13:19:02.538 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.567 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.571 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM 
[INFO ] 2024-12-18 13:19:02.574 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.619 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.620 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.COLLECTION 
[INFO ] 2024-12-18 13:19:02.620 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.COLLECTION is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.629 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.COLLECTION] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.631 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM.2 
[INFO ] 2024-12-18 13:19:02.631 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM.2 is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.656 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM.2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.657 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.ORACLE 
[INFO ] 2024-12-18 13:19:02.657 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.ORACLE is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.675 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.ORACLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.679 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM.3 
[INFO ] 2024-12-18 13:19:02.680 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM.3 is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.698 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM.3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.698 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.1 
[INFO ] 2024-12-18 13:19:02.698 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.1 is going to be initial synced 
[INFO ] 2024-12-18 13:19:02.721 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:19:02.722 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:19:02.723 - [测试子用户先有共享挖掘任务][MongoTarget] - Skip table [CAR.POLICY.ORACLE] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-12-18 13:19:02.729 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:19:02.730 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:19:02.731 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:19:02.739 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:19:02.793 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:19:02.795 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:19:02.796 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:19:02.846 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:19:02.847 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:19:02.847 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:19:02.849 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:19:02.851 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:19:02.852 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:19:02.852 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 10, partition size: 1, read thread number: 10 
[INFO ] 2024-12-18 13:19:02.960 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:02.961 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:19:02.976 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 13:19:02.976 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:02.976 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:02.989 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:19:02.993 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.046 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:19:03.046 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:19:03.047 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:03.048 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 1 
[INFO ] 2024-12-18 13:19:03.060 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.061 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.072 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:19:03.073 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 13:19:03.076 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:03.076 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 1 
[INFO ] 2024-12-18 13:19:03.079 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.163 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.173 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:19:03.175 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 13:19:03.183 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:03.183 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:19:03.192 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.193 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.200 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:19:03.200 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 13:19:03.205 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:03.207 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 1 
[INFO ] 2024-12-18 13:19:03.207 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.210 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.274 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:19:03.275 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 13:19:03.279 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:03.284 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 1 
[INFO ] 2024-12-18 13:19:03.285 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.287 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.341 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:19:03.341 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1] 
[INFO ] 2024-12-18 13:19:03.345 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:03.345 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 1 
[INFO ] 2024-12-18 13:19:03.350 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.350 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:19:03.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.3] 
[INFO ] 2024-12-18 13:19:03.420 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-18T05:19:02.068Z): 1 
[INFO ] 2024-12-18 13:19:03.422 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 1 
[INFO ] 2024-12-18 13:19:03.422 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:19:03.422 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.436 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:19:03.438 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1] 
[INFO ] 2024-12-18 13:19:03.442 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-18T05:19:02.068Z): 0 
[INFO ] 2024-12-18 13:19:03.446 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 0 
[INFO ] 2024-12-18 13:19:03.446 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:19:03.460 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:19:03.680 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1062771021 
[INFO ] 2024-12-18 13:19:03.684 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.ORACLE] 
[INFO ] 2024-12-18 13:19:03.684 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.ORACLE) by timestamp(2024-12-18T05:19:02.068Z): 0 
[INFO ] 2024-12-18 13:19:03.691 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, CAR.CLAIM, CAR.COLLECTION, CAR.CLAIM.2, CAR.POLICY.ORACLE, CAR.CLAIM.3, CAR.POLICY.1], data change syncing 
[INFO ] 2024-12-18 13:19:03.691 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.ORACLE' log, sequence: 0 
[INFO ] 2024-12-18 13:19:03.892 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.ORACLE filter: {sequence=0} 
[INFO ] 2024-12-18 13:19:16.649 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:19:16.702 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:19:16.706 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734499141654 
[INFO ] 2024-12-18 13:19:16.709 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734499141654 
[INFO ] 2024-12-18 13:19:16.709 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:19:16.726 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:19:16.727 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 89 ms 
[INFO ] 2024-12-18 13:19:16.727 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:19:16.748 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:19:16.763 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734499141469 
[INFO ] 2024-12-18 13:19:16.764 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734499141469 
[INFO ] 2024-12-18 13:19:16.764 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:19:16.764 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:19:16.983 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 38 ms 
[INFO ] 2024-12-18 13:19:19.744 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:19:19.744 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@f8846a0 
[INFO ] 2024-12-18 13:19:19.860 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:19:19.869 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:19:19.869 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:19:19.905 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:19:19.905 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:23:13.168 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:23:13.216 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:23:13.852 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:23:13.952 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:23:13.952 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 12 
[INFO ] 2024-12-18 13:23:13.952 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 12 
[INFO ] 2024-12-18 13:23:13.952 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:23:13.952 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 13:23:14.503 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:23:14.503 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:23:14.503 - [测试子用户先有共享挖掘任务][MongoTarget] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 13:23:14.597 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:23:14.620 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:23:14.620 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {},stream offset found: {"cdcOffset":1734499394,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:23:14.771 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync started 
[INFO ] 2024-12-18 13:23:14.772 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CUSTOMER.BACK1 
[INFO ] 2024-12-18 13:23:14.772 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CUSTOMER.BACK1 is going to be initial synced 
[INFO ] 2024-12-18 13:23:14.977 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:23:15.076 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CUSTOMER.BACK1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.076 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM 
[INFO ] 2024-12-18 13:23:15.077 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.113 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.113 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM.2 
[INFO ] 2024-12-18 13:23:15.114 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM.2 is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.129 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM.2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.129 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.CLAIM.3 
[INFO ] 2024-12-18 13:23:15.142 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.CLAIM.3 is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.142 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.CLAIM.3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.144 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.1 
[INFO ] 2024-12-18 13:23:15.144 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.1 is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.154 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.154 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.3 
[INFO ] 2024-12-18 13:23:15.154 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.3 is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.158 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.158 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.BACK.1 
[INFO ] 2024-12-18 13:23:15.169 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.BACK.1 is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.169 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.BACK.1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.169 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY 
[INFO ] 2024-12-18 13:23:15.169 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.177 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.178 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.COLLECTION 
[INFO ] 2024-12-18 13:23:15.180 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.COLLECTION is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.181 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.COLLECTION] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.182 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CUSTOMER 
[INFO ] 2024-12-18 13:23:15.182 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.190 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.191 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CLAIMTYPE 
[INFO ] 2024-12-18 13:23:15.191 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CLAIMTYPE is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.206 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CLAIMTYPE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.207 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: CAR.POLICY.ORACLE 
[INFO ] 2024-12-18 13:23:15.207 - [测试子用户先有共享挖掘任务][MongoTarget] - Table CAR.POLICY.ORACLE is going to be initial synced 
[INFO ] 2024-12-18 13:23:15.217 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [CAR.POLICY.ORACLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:23:15.217 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:23:15.218 - [测试子用户先有共享挖掘任务][MongoTarget] - Skip table [CLAIMTYPE] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-12-18 13:23:15.219 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:23:15.219 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:23:15.220 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:23:15.220 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:23:15.261 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:23:15.261 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:23:15.261 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:23:15.292 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:23:15.292 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:23:15.297 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:23:15.297 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:23:15.302 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:23:15.302 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:23:15.313 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 12, partition size: 1, read thread number: 12 
[INFO ] 2024-12-18 13:23:15.314 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.326 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:23:15.327 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 13:23:15.331 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.331 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.332 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.344 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.344 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:23:15.350 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:23:15.350 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.351 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.351 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.373 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.377 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:23:15.377 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 13:23:15.379 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.379 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.384 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.384 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.391 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:23:15.391 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 13:23:15.393 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.393 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.402 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.402 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.409 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:23:15.410 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 13:23:15.417 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.417 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.422 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.422 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.429 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:23:15.429 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 13:23:15.435 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.443 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.443 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.451 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.451 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:23:15.460 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1] 
[INFO ] 2024-12-18 13:23:15.460 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.460 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.460 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.541 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.542 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:23:15.542 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.3] 
[INFO ] 2024-12-18 13:23:15.547 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.548 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.554 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.587 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.587 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:23:15.646 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1] 
[INFO ] 2024-12-18 13:23:15.647 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.658 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-18T05:23:14.504Z): 2 
[INFO ] 2024-12-18 13:23:15.666 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1062771021 
[INFO ] 2024-12-18 13:23:15.666 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 2 
[INFO ] 2024-12-18 13:23:15.666 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.ORACLE] 
[INFO ] 2024-12-18 13:23:15.666 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=2} 
[INFO ] 2024-12-18 13:23:15.671 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.ORACLE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.672 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.ORACLE' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.672 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.ORACLE filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.673 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.682 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_2116397731 
[INFO ] 2024-12-18 13:23:15.682 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CLAIMTYPE] 
[INFO ] 2024-12-18 13:23:15.684 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CLAIMTYPE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:23:15.684 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CLAIMTYPE' log, sequence: 1 
[INFO ] 2024-12-18 13:23:15.691 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CLAIMTYPE filter: {sequence=1} 
[INFO ] 2024-12-18 13:23:15.900 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:23:15.948 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1792520433 
[INFO ] 2024-12-18 13:23:15.948 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-12-18 13:23:15.953 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:23:15.958 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, CAR.POLICY.ORACLE], data change syncing 
[INFO ] 2024-12-18 13:23:15.958 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 0 
[INFO ] 2024-12-18 13:23:16.159 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=0} 
[INFO ] 2024-12-18 13:23:40.693 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:23:40.695 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:23:40.701 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734499394393 
[INFO ] 2024-12-18 13:23:40.701 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734499394393 
[INFO ] 2024-12-18 13:23:40.701 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:23:40.701 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:23:40.702 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 52 ms 
[INFO ] 2024-12-18 13:23:40.702 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:23:40.713 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:23:40.713 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734499394492 
[INFO ] 2024-12-18 13:23:40.714 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734499394492 
[INFO ] 2024-12-18 13:23:40.714 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:23:40.717 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:23:40.717 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 14 ms 
[INFO ] 2024-12-18 13:23:45.140 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:23:45.142 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@f756143 
[INFO ] 2024-12-18 13:23:45.258 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:23:45.269 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:23:45.269 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:23:45.296 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:23:45.296 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:24:01.227 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:24:01.427 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:24:01.649 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:24:01.738 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:24:01.739 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 13 
[INFO ] 2024-12-18 13:24:01.740 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 13 
[INFO ] 2024-12-18 13:24:01.740 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:24:01.742 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:24:01.880 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:24:01.880 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:24:01.881 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:24:01.887 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:24:01.891 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.POLICY.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.BACK.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CUSTOMER":{"batch_read_connector_status":"OVER"},"CLAIMTYPE":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.POLICY.ORACLE":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.1":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734499394,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:24:01.892 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:24:01.976 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: TARGET.CAR.CLAIM 
[INFO ] 2024-12-18 13:24:01.976 - [测试子用户先有共享挖掘任务][MongoTarget] - Table TARGET.CAR.CLAIM is going to be initial synced 
[INFO ] 2024-12-18 13:24:02.031 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:24:02.031 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [TARGET.CAR.CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:24:02.032 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:24:02.032 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:24:02.069 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:24:02.070 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:24:02.070 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:24:02.070 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:24:02.090 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:24:02.090 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:24:02.091 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:24:02.091 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:24:02.091 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:24:02.092 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:24:02.099 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 13, partition size: 1, read thread number: 13 
[INFO ] 2024-12-18 13:24:02.099 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.118 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:24:02.119 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 13:24:02.119 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.126 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:24:02.129 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:24:02.129 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.131 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.131 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.131 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.134 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.136 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.136 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.145 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:24:02.145 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 13:24:02.153 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.153 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.153 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.154 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.161 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:24:02.161 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 13:24:02.167 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.167 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.171 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.171 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.179 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:24:02.180 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 13:24:02.184 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.184 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.185 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.201 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.201 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:24:02.205 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 13:24:02.206 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.206 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.211 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.211 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.220 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:24:02.221 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1] 
[INFO ] 2024-12-18 13:24:02.223 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.223 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.224 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.233 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.326 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:24:02.326 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.3] 
[INFO ] 2024-12-18 13:24:02.333 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.333 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.345 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.347 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.372 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:24:02.373 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1] 
[INFO ] 2024-12-18 13:24:02.375 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-18T05:23:14.504Z): 2 
[INFO ] 2024-12-18 13:24:02.376 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 2 
[INFO ] 2024-12-18 13:24:02.379 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=2} 
[INFO ] 2024-12-18 13:24:02.391 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.391 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1062771021 
[INFO ] 2024-12-18 13:24:02.395 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.ORACLE] 
[INFO ] 2024-12-18 13:24:02.395 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.ORACLE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.395 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.ORACLE' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.395 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.ORACLE filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_2116397731 
[INFO ] 2024-12-18 13:24:02.412 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CLAIMTYPE] 
[INFO ] 2024-12-18 13:24:02.416 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CLAIMTYPE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:24:02.416 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CLAIMTYPE' log, sequence: 1 
[INFO ] 2024-12-18 13:24:02.421 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CLAIMTYPE filter: {sequence=1} 
[INFO ] 2024-12-18 13:24:02.421 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.438 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1792520433 
[INFO ] 2024-12-18 13:24:02.438 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-12-18 13:24:02.443 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:24:02.443 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 0 
[INFO ] 2024-12-18 13:24:02.449 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=0} 
[INFO ] 2024-12-18 13:24:02.450 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:24:02.669 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_42811224 
[INFO ] 2024-12-18 13:24:02.669 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TARGET.CAR.CLAIM] 
[INFO ] 2024-12-18 13:24:02.677 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TARGET.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:24:02.678 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, CAR.POLICY.ORACLE, TARGET.CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 13:24:02.679 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TARGET.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:24:02.679 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TARGET.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:24:27.699 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:24:27.717 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:24:27.720 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734499441781 
[INFO ] 2024-12-18 13:24:27.720 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734499441781 
[INFO ] 2024-12-18 13:24:27.720 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:24:27.720 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:24:27.725 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 42 ms 
[INFO ] 2024-12-18 13:24:27.727 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:24:27.738 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:24:27.739 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734499441781 
[INFO ] 2024-12-18 13:24:27.739 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734499441781 
[INFO ] 2024-12-18 13:24:27.739 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:24:27.739 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:24:27.943 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 15 ms 
[INFO ] 2024-12-18 13:24:30.378 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:24:30.498 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@653237e4 
[INFO ] 2024-12-18 13:24:30.498 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:24:30.536 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:24:30.536 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:24:30.563 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:24:30.565 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:35:14.752 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:35:14.753 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:35:15.242 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:35:15.242 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:35:15.304 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 15 
[INFO ] 2024-12-18 13:35:15.304 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 15 
[INFO ] 2024-12-18 13:35:15.304 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:35:15.304 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:35:15.452 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:35:15.457 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:35:15.457 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:35:15.461 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:35:15.461 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:35:15.612 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.BACK.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CUSTOMER":{"batch_read_connector_status":"OVER"},"CLAIMTYPE":{"batch_read_connector_status":"OVER"},"CAR.POLICY.ORACLE":{"batch_read_connector_status":"OVER"},"TARGET.CAR.CLAIM":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734499394,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:35:15.613 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: TEST.CAR.CLAIM 
[INFO ] 2024-12-18 13:35:15.613 - [测试子用户先有共享挖掘任务][MongoTarget] - Table TEST.CAR.CLAIM is going to be initial synced 
[INFO ] 2024-12-18 13:35:15.680 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:35:15.682 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [TEST.CAR.CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:35:15.682 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: TEST.CAR.CLAIM.1 
[INFO ] 2024-12-18 13:35:15.683 - [测试子用户先有共享挖掘任务][MongoTarget] - Table TEST.CAR.CLAIM.1 is going to be initial synced 
[INFO ] 2024-12-18 13:35:15.699 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [TEST.CAR.CLAIM.1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:35:15.700 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:35:15.700 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:35:15.700 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:35:15.779 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:35:15.781 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:35:15.781 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:35:15.803 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:35:15.819 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:35:15.819 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:35:15.821 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:35:15.822 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:35:15.823 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:35:15.823 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 15, partition size: 1, read thread number: 15 
[INFO ] 2024-12-18 13:35:15.827 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.843 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:35:15.843 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM] 
[INFO ] 2024-12-18 13:35:15.849 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.849 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.852 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:35:15.853 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:15.859 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:35:15.860 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:35:15.861 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.861 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 1 
[INFO ] 2024-12-18 13:35:15.868 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:15.871 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.871 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:35:15.873 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3] 
[INFO ] 2024-12-18 13:35:15.873 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.874 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 1 
[INFO ] 2024-12-18 13:35:15.879 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:15.883 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.883 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:35:15.886 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.COLLECTION] 
[INFO ] 2024-12-18 13:35:15.886 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.886 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:35:15.890 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:15.894 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.894 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:35:15.895 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1] 
[INFO ] 2024-12-18 13:35:15.895 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.896 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 1 
[INFO ] 2024-12-18 13:35:15.896 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:15.903 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.903 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:35:15.905 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY] 
[INFO ] 2024-12-18 13:35:15.905 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.905 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 1 
[INFO ] 2024-12-18 13:35:15.905 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:15.914 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.914 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:35:15.916 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1] 
[INFO ] 2024-12-18 13:35:15.916 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.916 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 1 
[INFO ] 2024-12-18 13:35:15.916 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:15.993 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:15.993 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:35:15.993 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.3] 
[INFO ] 2024-12-18 13:35:15.994 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:15.995 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 1 
[INFO ] 2024-12-18 13:35:16.000 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:16.004 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:16.004 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:35:16.006 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1] 
[INFO ] 2024-12-18 13:35:16.006 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-18T05:23:14.504Z): 2 
[INFO ] 2024-12-18 13:35:16.006 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 2 
[INFO ] 2024-12-18 13:35:16.006 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=2} 
[INFO ] 2024-12-18 13:35:16.015 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:16.015 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1062771021 
[INFO ] 2024-12-18 13:35:16.017 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.ORACLE] 
[INFO ] 2024-12-18 13:35:16.017 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.ORACLE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:16.017 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.ORACLE' log, sequence: 1 
[INFO ] 2024-12-18 13:35:16.017 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.ORACLE filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:16.029 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:16.029 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_2116397731 
[INFO ] 2024-12-18 13:35:16.031 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CLAIMTYPE] 
[INFO ] 2024-12-18 13:35:16.031 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CLAIMTYPE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:35:16.031 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CLAIMTYPE' log, sequence: 1 
[INFO ] 2024-12-18 13:35:16.032 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CLAIMTYPE filter: {sequence=1} 
[INFO ] 2024-12-18 13:35:16.046 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:16.046 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1792520433 
[INFO ] 2024-12-18 13:35:16.052 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-12-18 13:35:16.053 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:35:16.054 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 0 
[INFO ] 2024-12-18 13:35:16.060 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=0} 
[INFO ] 2024-12-18 13:35:16.078 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:16.078 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_42811224 
[INFO ] 2024-12-18 13:35:16.085 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TARGET.CAR.CLAIM] 
[INFO ] 2024-12-18 13:35:16.086 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TARGET.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:35:16.086 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TARGET.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:35:16.092 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TARGET.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:35:16.095 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:16.269 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1162179033 
[INFO ] 2024-12-18 13:35:16.269 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TEST.CAR.CLAIM] 
[INFO ] 2024-12-18 13:35:16.270 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:35:16.270 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:35:16.273 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:35:16.482 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:35:16.525 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_162555228 
[INFO ] 2024-12-18 13:35:16.525 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TEST.CAR.CLAIM.1] 
[INFO ] 2024-12-18 13:35:16.529 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.1) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:35:16.530 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM, TEST.CAR.CLAIM.1, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, CAR.POLICY.ORACLE, TARGET.CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 13:35:16.530 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.1' log, sequence: 0 
[INFO ] 2024-12-18 13:35:16.731 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:35:28.296 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:35:28.334 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:35:28.335 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734500115347 
[INFO ] 2024-12-18 13:35:28.335 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734500115347 
[INFO ] 2024-12-18 13:35:28.336 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:35:28.336 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:35:28.341 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 48 ms 
[INFO ] 2024-12-18 13:35:28.341 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:35:28.364 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:35:28.365 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734500115347 
[INFO ] 2024-12-18 13:35:28.365 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734500115347 
[INFO ] 2024-12-18 13:35:28.366 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:35:28.370 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:35:28.370 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 29 ms 
[INFO ] 2024-12-18 13:35:32.467 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:35:32.468 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57462f11 
[INFO ] 2024-12-18 13:35:32.596 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:35:32.611 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:35:32.612 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:35:32.635 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:35:32.636 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:36:50.287 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:36:50.492 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:36:50.690 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:36:50.731 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:36:50.790 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 16 
[INFO ] 2024-12-18 13:36:50.790 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 16 
[INFO ] 2024-12-18 13:36:50.790 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:36:50.790 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:36:50.930 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:36:50.938 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:36:50.938 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:36:50.954 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:36:50.954 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:36:51.060 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.BACK.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM.1":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CUSTOMER":{"batch_read_connector_status":"OVER"},"CLAIMTYPE":{"batch_read_connector_status":"OVER"},"CAR.POLICY.ORACLE":{"batch_read_connector_status":"OVER"},"TARGET.CAR.CLAIM":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734499394,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:36:51.060 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: TEST.CAR.CLAIM.BACK1 
[INFO ] 2024-12-18 13:36:51.060 - [测试子用户先有共享挖掘任务][MongoTarget] - Table TEST.CAR.CLAIM.BACK1 is going to be initial synced 
[INFO ] 2024-12-18 13:36:51.127 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:36:51.127 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [TEST.CAR.CLAIM.BACK1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:36:51.127 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:36:51.127 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:36:51.127 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:36:51.165 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:36:51.166 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:36:51.166 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:36:51.187 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:36:51.187 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:36:51.188 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:36:51.188 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:36:51.188 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:36:51.188 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:36:51.193 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 16, partition size: 2, read thread number: 8 
[INFO ] 2024-12-18 13:36:51.193 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.204 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:36:51.204 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.209 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:36:51.209 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM, CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:36:51.212 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.212 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.218 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.220 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.220 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.224 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.224 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:36:51.237 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.237 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:36:51.239 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3, CAR.COLLECTION] 
[INFO ] 2024-12-18 13:36:51.239 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.239 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.239 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.247 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.247 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.247 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.247 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.250 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:36:51.260 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.260 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:36:51.262 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY] 
[INFO ] 2024-12-18 13:36:51.263 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.263 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.263 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.270 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.271 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.271 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.272 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.289 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:36:51.289 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.363 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:36:51.363 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1, CAR.POLICY.3] 
[INFO ] 2024-12-18 13:36:51.366 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.367 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.373 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.373 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.373 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.374 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM, TEST.CAR.CLAIM.1, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, CAR.POLICY.ORACLE, TARGET.CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 13:36:51.374 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.382 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.382 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:36:51.393 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.393 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1062771021 
[INFO ] 2024-12-18 13:36:51.400 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1, CAR.POLICY.ORACLE] 
[INFO ] 2024-12-18 13:36:51.401 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-18T05:23:14.504Z): 2 
[INFO ] 2024-12-18 13:36:51.403 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 2 
[INFO ] 2024-12-18 13:36:51.403 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=2} 
[INFO ] 2024-12-18 13:36:51.403 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.411 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.ORACLE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.411 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.ORACLE' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.411 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.ORACLE filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.411 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_2116397731 
[INFO ] 2024-12-18 13:36:51.419 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.426 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1792520433 
[INFO ] 2024-12-18 13:36:51.427 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CLAIMTYPE, CUSTOMER] 
[INFO ] 2024-12-18 13:36:51.430 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CLAIMTYPE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:36:51.430 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CLAIMTYPE' log, sequence: 1 
[INFO ] 2024-12-18 13:36:51.440 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CLAIMTYPE filter: {sequence=1} 
[INFO ] 2024-12-18 13:36:51.440 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:36:51.440 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.440 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 0 
[INFO ] 2024-12-18 13:36:51.455 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=0} 
[INFO ] 2024-12-18 13:36:51.455 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_42811224 
[INFO ] 2024-12-18 13:36:51.455 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.464 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1162179033 
[INFO ] 2024-12-18 13:36:51.464 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TARGET.CAR.CLAIM, TEST.CAR.CLAIM] 
[INFO ] 2024-12-18 13:36:51.465 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TARGET.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:36:51.465 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TARGET.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:36:51.472 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TARGET.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:36:51.473 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:36:51.473 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:36:51.473 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:36:51.484 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.484 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_162555228 
[INFO ] 2024-12-18 13:36:51.695 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:36:51.727 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1192523733 
[INFO ] 2024-12-18 13:36:51.727 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TEST.CAR.CLAIM.1, TEST.CAR.CLAIM.BACK1] 
[INFO ] 2024-12-18 13:36:51.731 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.1) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:36:51.731 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.1' log, sequence: 0 
[INFO ] 2024-12-18 13:36:51.735 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:36:51.739 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:36:51.739 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.BACK1' log, sequence: 0 
[INFO ] 2024-12-18 13:36:51.940 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.BACK1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:49:15.509 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:49:15.510 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:49:15.522 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734500210828 
[INFO ] 2024-12-18 13:49:15.522 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734500210828 
[INFO ] 2024-12-18 13:49:15.522 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:49:15.522 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:49:15.523 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 43 ms 
[INFO ] 2024-12-18 13:49:15.523 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:49:15.534 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:49:15.534 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734500210828 
[INFO ] 2024-12-18 13:49:15.534 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734500210828 
[INFO ] 2024-12-18 13:49:15.534 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:49:15.534 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:49:15.535 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 11 ms 
[INFO ] 2024-12-18 13:49:18.600 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:49:18.601 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6adda13f 
[INFO ] 2024-12-18 13:49:18.740 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:49:18.740 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:49:18.741 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:49:18.776 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:49:18.778 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:50:48.869 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:50:48.870 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:50:49.273 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:50:49.395 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:50:49.395 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 16 
[INFO ] 2024-12-18 13:50:49.395 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 16 
[INFO ] 2024-12-18 13:50:49.395 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 13:50:49.395 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 13:50:49.560 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:50:49.560 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:50:49.578 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:50:49.578 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:50:49.587 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:50:49.588 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM.BACK1":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.BACK.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM.1":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CUSTOMER":{"batch_read_connector_status":"OVER"},"CLAIMTYPE":{"batch_read_connector_status":"OVER"},"CAR.POLICY.ORACLE":{"batch_read_connector_status":"OVER"},"TARGET.CAR.CLAIM":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734499394,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:50:49.721 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:50:49.723 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:50:49.796 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:50:49.796 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:50:49.797 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:50:49.867 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:50:49.880 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:50:49.880 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:50:49.880 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:50:49.880 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:50:49.881 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:50:49.906 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 16, partition size: 2, read thread number: 8 
[INFO ] 2024-12-18 13:50:49.906 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:49.923 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:50:49.923 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:49.927 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:50:49.927 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM, CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:50:49.929 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:49.930 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:50:49.937 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:49.937 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:49.945 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:49.945 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:50:49.945 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 1 
[INFO ] 2024-12-18 13:50:49.945 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:49.958 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:49.970 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:50:49.971 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3, CAR.COLLECTION] 
[INFO ] 2024-12-18 13:50:49.974 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:49.974 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 1 
[INFO ] 2024-12-18 13:50:49.974 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:49.981 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:49.981 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:49.981 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:50:49.985 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:49.985 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:50:50.024 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.024 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:50:50.024 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY] 
[INFO ] 2024-12-18 13:50:50.028 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:50.028 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 1 
[INFO ] 2024-12-18 13:50:50.030 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:50.051 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.051 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:50.051 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 1 
[INFO ] 2024-12-18 13:50:50.051 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:50.058 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:50:50.136 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.137 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:50:50.137 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1, CAR.POLICY.3] 
[INFO ] 2024-12-18 13:50:50.141 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:50.141 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 1 
[INFO ] 2024-12-18 13:50:50.141 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:50.149 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.149 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:50.150 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM, TEST.CAR.CLAIM.1, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, CAR.POLICY.ORACLE, TARGET.CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 13:50:50.150 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 1 
[INFO ] 2024-12-18 13:50:50.150 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:50.152 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:50:50.162 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.162 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1062771021 
[INFO ] 2024-12-18 13:50:50.162 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1, CAR.POLICY.ORACLE] 
[INFO ] 2024-12-18 13:50:50.165 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-18T05:23:14.504Z): 2 
[INFO ] 2024-12-18 13:50:50.166 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 2 
[INFO ] 2024-12-18 13:50:50.167 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=2} 
[INFO ] 2024-12-18 13:50:50.175 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.175 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.ORACLE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:50.176 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.ORACLE' log, sequence: 1 
[INFO ] 2024-12-18 13:50:50.176 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_2116397731 
[INFO ] 2024-12-18 13:50:50.182 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.ORACLE filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:50.182 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.185 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1792520433 
[INFO ] 2024-12-18 13:50:50.185 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CLAIMTYPE, CUSTOMER] 
[INFO ] 2024-12-18 13:50:50.188 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CLAIMTYPE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:50:50.188 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CLAIMTYPE' log, sequence: 1 
[INFO ] 2024-12-18 13:50:50.197 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CLAIMTYPE filter: {sequence=1} 
[INFO ] 2024-12-18 13:50:50.197 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.197 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:50:50.197 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 0 
[INFO ] 2024-12-18 13:50:50.197 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=0} 
[INFO ] 2024-12-18 13:50:50.209 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_42811224 
[INFO ] 2024-12-18 13:50:50.209 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.212 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1162179033 
[INFO ] 2024-12-18 13:50:50.212 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TARGET.CAR.CLAIM, TEST.CAR.CLAIM] 
[INFO ] 2024-12-18 13:50:50.213 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TARGET.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:50:50.213 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TARGET.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:50:50.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TARGET.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:50:50.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:50:50.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:50:50.219 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:50:50.233 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_162555228 
[INFO ] 2024-12-18 13:50:50.233 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:50:50.241 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1192523733 
[INFO ] 2024-12-18 13:50:50.241 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TEST.CAR.CLAIM.1, TEST.CAR.CLAIM.BACK1] 
[INFO ] 2024-12-18 13:50:50.242 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.1) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:50:50.243 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.1' log, sequence: 0 
[INFO ] 2024-12-18 13:50:50.243 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:50:50.246 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:50:50.246 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.BACK1' log, sequence: 0 
[INFO ] 2024-12-18 13:50:50.247 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.BACK1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:51:41.110 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 13:51:41.110 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 13:51:41.115 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734501049439 
[INFO ] 2024-12-18 13:51:41.115 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734501049439 
[INFO ] 2024-12-18 13:51:41.115 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 13:51:41.115 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 13:51:41.118 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 54 ms 
[INFO ] 2024-12-18 13:51:41.120 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 13:51:41.120 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 13:51:41.122 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734501049439 
[INFO ] 2024-12-18 13:51:41.123 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734501049439 
[INFO ] 2024-12-18 13:51:41.123 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 13:51:41.123 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 13:51:41.326 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 4 ms 
[INFO ] 2024-12-18 13:51:43.936 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 13:51:43.936 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7b7bcbd9 
[INFO ] 2024-12-18 13:51:44.087 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 13:51:44.087 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 13:51:44.087 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 13:51:44.124 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:51:44.124 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 13:58:16.477 - [测试子用户先有共享挖掘任务] - Task initialization... 
[INFO ] 2024-12-18 13:58:16.522 - [测试子用户先有共享挖掘任务] - Start task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务) 
[INFO ] 2024-12-18 13:58:16.891 - [测试子用户先有共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 13:58:16.892 - [测试子用户先有共享挖掘任务] - The engine receives 测试子用户先有共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 13:58:16.957 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] start preload schema,table counts: 17 
[INFO ] 2024-12-18 13:58:16.957 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] start preload schema,table counts: 17 
[INFO ] 2024-12-18 13:58:16.957 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:58:16.957 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 13:58:17.117 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" read batch size: 100 
[INFO ] 2024-12-18 13:58:17.117 - [测试子用户先有共享挖掘任务][MongoTarget] - Source node "MongoTarget" event queue capacity: 200 
[INFO ] 2024-12-18 13:58:17.118 - [测试子用户先有共享挖掘任务][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-18 13:58:17.118 - [测试子用户先有共享挖掘任务][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 13:58:17.125 - [测试子用户先有共享挖掘任务][MongoTarget] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-18 13:58:17.245 - [测试子用户先有共享挖掘任务][MongoTarget] - batch offset found: {"CAR.CUSTOMER.BACK1":{"batch_read_connector_status":"OVER"},"CAR.CLAIM":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM.BACK1":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.2":{"batch_read_connector_status":"OVER"},"CAR.CLAIM.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY.3":{"batch_read_connector_status":"OVER"},"CAR.POLICY.BACK.1":{"batch_read_connector_status":"OVER"},"CAR.POLICY":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM":{"batch_read_connector_status":"OVER"},"TEST.CAR.CLAIM.1":{"batch_read_connector_status":"OVER"},"CAR.COLLECTION":{"batch_read_connector_status":"OVER"},"CUSTOMER":{"batch_read_connector_status":"OVER"},"CLAIMTYPE":{"batch_read_connector_status":"OVER"},"CAR.POLICY.ORACLE":{"batch_read_connector_status":"OVER"},"TARGET.CAR.CLAIM":{"batch_read_connector_status":"OVER"}},stream offset found: {"cdcOffset":1734499394,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-18 13:58:17.245 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting batch read, table name: TEST.CAR.CLAIM.BACK2 
[INFO ] 2024-12-18 13:58:17.245 - [测试子用户先有共享挖掘任务][MongoTarget] - Table TEST.CAR.CLAIM.BACK2 is going to be initial synced 
[INFO ] 2024-12-18 13:58:17.288 - [测试子用户先有共享挖掘任务][MongoTarget] - Query snapshot row size completed: MongoTarget(cbbcc3ad-017f-4480-adf9-5ceb81d87b80) 
[INFO ] 2024-12-18 13:58:17.305 - [测试子用户先有共享挖掘任务][MongoTarget] - Table [TEST.CAR.CLAIM.BACK2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 13:58:17.305 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:58:17.306 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync starting... 
[INFO ] 2024-12-18 13:58:17.306 - [测试子用户先有共享挖掘任务][MongoTarget] - Initial sync completed 
[INFO ] 2024-12-18 13:58:17.342 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-18 13:58:17.342 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 1 - Check connection MongoTarget enable share cdc: true 
[INFO ] 2024-12-18 13:58:17.342 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试子用户先有共享挖掘任务 enable share cdc: true 
[INFO ] 2024-12-18 13:58:17.401 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from MongoTarget 
[INFO ] 2024-12-18 13:58:17.411 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav317?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-18 13:58:17.411 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-18 13:58:17.411 - [测试子用户先有共享挖掘任务][MongoTarget] - Init share cdc reader completed 
[INFO ] 2024-12-18 13:58:17.411 - [测试子用户先有共享挖掘任务][MongoTarget] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-18 13:58:17.411 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-18 13:58:17.428 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Read table count: 17, partition size: 2, read thread number: 9 
[INFO ] 2024-12-18 13:58:17.428 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d14e9ed70a1ee0ef81b6, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM, version=v2, tableName=CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1545530031, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.442 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1545530031 
[INFO ] 2024-12-18 13:58:17.442 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761d1d29ed70a1ee0ef9c68, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.2, version=v2, tableName=CAR.CLAIM.2, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323149, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.447 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323149 
[INFO ] 2024-12-18 13:58:17.447 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM, CAR.CLAIM.2] 
[INFO ] 2024-12-18 13:58:17.449 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.449 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.454 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.454 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67624ae49ed70a1ee0f4e7c0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CLAIM.3, version=v2, tableName=CAR.CLAIM.3, externalStorageTableName=ExternalStorage_SHARE_CDC_-804323148, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.457 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.2) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.457 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.2' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.463 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.2 filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.463 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CLAIM.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-804323148 
[INFO ] 2024-12-18 13:58:17.470 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6761cf999ed70a1ee0ef27e7, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.COLLECTION, version=v2, tableName=CAR.COLLECTION, externalStorageTableName=ExternalStorage_SHARE_CDC_-945749013, shareCdcTaskId=6761cf99a355981f6f70a1e5, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.470 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.COLLECTION_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-945749013 
[INFO ] 2024-12-18 13:58:17.472 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CLAIM.3, CAR.COLLECTION] 
[INFO ] 2024-12-18 13:58:17.472 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CLAIM.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.472 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CLAIM.3' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.472 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CLAIM.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.479 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762536a9ed70a1ee0f621f9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.CUSTOMER.BACK1, version=v2, tableName=CAR.CUSTOMER.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_-1660872473, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.479 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.COLLECTION) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.479 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.COLLECTION' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.479 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.COLLECTION filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.485 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.CUSTOMER.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_-1660872473 
[INFO ] 2024-12-18 13:58:17.485 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676255f69ed70a1ee0f67cd8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY, version=v2, tableName=CAR.POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1042067711, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.491 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1042067711 
[INFO ] 2024-12-18 13:58:17.491 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.CUSTOMER.BACK1, CAR.POLICY] 
[INFO ] 2024-12-18 13:58:17.492 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.CUSTOMER.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.492 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.CUSTOMER.BACK1' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.496 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.CUSTOMER.BACK1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.496 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258969ed70a1ee0f6f510, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.1, version=v2, tableName=CAR.POLICY.1, externalStorageTableName=ExternalStorage_SHARE_CDC_699691778, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.498 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.498 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.499 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.499 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691778 
[INFO ] 2024-12-18 13:58:17.574 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676258ea9ed70a1ee0f70548, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.3, version=v2, tableName=CAR.POLICY.3, externalStorageTableName=ExternalStorage_SHARE_CDC_699691780, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.575 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.3_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_699691780 
[INFO ] 2024-12-18 13:58:17.575 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.1, CAR.POLICY.3] 
[INFO ] 2024-12-18 13:58:17.576 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.1) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.577 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.1' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.586 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.1 filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.587 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.3) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.587 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6762593c9ed70a1ee0f7193d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.BACK.1, version=v2, tableName=CAR.POLICY.BACK.1, externalStorageTableName=ExternalStorage_SHARE_CDC_674958521, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.587 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.3' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.587 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.3 filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.608 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.BACK.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_674958521 
[INFO ] 2024-12-18 13:58:17.609 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625a3b9ed70a1ee0f75edd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CAR.POLICY.ORACLE, version=v2, tableName=CAR.POLICY.ORACLE, externalStorageTableName=ExternalStorage_SHARE_CDC_1062771021, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.615 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CAR.POLICY.ORACLE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1062771021 
[INFO ] 2024-12-18 13:58:17.615 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR.POLICY.BACK.1, CAR.POLICY.ORACLE] 
[INFO ] 2024-12-18 13:58:17.618 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.BACK.1) by timestamp(2024-12-18T05:23:14.504Z): 2 
[INFO ] 2024-12-18 13:58:17.623 - [测试子用户先有共享挖掘任务][MongoTarget] - Connector MongoDB incremental start succeed, tables: [CAR.CUSTOMER.BACK1, CAR.CLAIM, TEST.CAR.CLAIM.BACK1, TEST.CAR.CLAIM.BACK2, CAR.CLAIM.2, CAR.CLAIM.3, CAR.POLICY.1, CAR.POLICY.3, CAR.POLICY.BACK.1, CAR.POLICY, TEST.CAR.CLAIM, TEST.CAR.CLAIM.1, CAR.COLLECTION, CUSTOMER, CLAIMTYPE, CAR.POLICY.ORACLE, TARGET.CAR.CLAIM], data change syncing 
[INFO ] 2024-12-18 13:58:17.623 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.BACK.1' log, sequence: 2 
[INFO ] 2024-12-18 13:58:17.623 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.BACK.1 filter: {sequence=2} 
[INFO ] 2024-12-18 13:58:17.625 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625b679ed70a1ee0f78cd9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CLAIMTYPE, version=v2, tableName=CLAIMTYPE, externalStorageTableName=ExternalStorage_SHARE_CDC_2116397731, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.626 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CAR.POLICY.ORACLE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.626 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CAR.POLICY.ORACLE' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.632 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CAR.POLICY.ORACLE filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.632 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CLAIMTYPE_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_2116397731 
[INFO ] 2024-12-18 13:58:17.648 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c3c9ed70a1ee0f7aa33, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1792520433, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.648 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_CUSTOMER_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1792520433 
[INFO ] 2024-12-18 13:58:17.649 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CLAIMTYPE, CUSTOMER] 
[INFO ] 2024-12-18 13:58:17.657 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CLAIMTYPE) by timestamp(2024-12-18T05:23:14.504Z): 1 
[INFO ] 2024-12-18 13:58:17.657 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CLAIMTYPE' log, sequence: 1 
[INFO ] 2024-12-18 13:58:17.664 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CLAIMTYPE filter: {sequence=1} 
[INFO ] 2024-12-18 13:58:17.664 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:58:17.664 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 0 
[INFO ] 2024-12-18 13:58:17.664 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=0} 
[INFO ] 2024-12-18 13:58:17.670 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625c719ed70a1ee0f7b8fb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TARGET.CAR.CLAIM, version=v2, tableName=TARGET.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_42811224, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.670 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TARGET.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_42811224 
[INFO ] 2024-12-18 13:58:17.681 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625ca09ed70a1ee0f7c6c5, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM, version=v2, tableName=TEST.CAR.CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1162179033, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.684 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1162179033 
[INFO ] 2024-12-18 13:58:17.684 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TARGET.CAR.CLAIM, TEST.CAR.CLAIM] 
[INFO ] 2024-12-18 13:58:17.685 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TARGET.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:58:17.685 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TARGET.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:58:17.691 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TARGET.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:58:17.691 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:58:17.691 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM' log, sequence: 0 
[INFO ] 2024-12-18 13:58:17.691 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM filter: {sequence=0} 
[INFO ] 2024-12-18 13:58:17.692 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f079ed70a1ee0f820ae, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.1, version=v2, tableName=TEST.CAR.CLAIM.1, externalStorageTableName=ExternalStorage_SHARE_CDC_162555228, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.704 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_162555228 
[INFO ] 2024-12-18 13:58:17.704 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67625f379ed70a1ee0f82db9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK1, version=v2, tableName=TEST.CAR.CLAIM.BACK1, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523733, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.709 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK1_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1192523733 
[INFO ] 2024-12-18 13:58:17.709 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TEST.CAR.CLAIM.1, TEST.CAR.CLAIM.BACK1] 
[INFO ] 2024-12-18 13:58:17.711 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.1) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:58:17.719 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.1' log, sequence: 0 
[INFO ] 2024-12-18 13:58:17.719 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:58:17.721 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=676263ff9ed70a1ee0f8d768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6761cefda355981f6f70a1a2_TEST.CAR.CLAIM.BACK2, version=v2, tableName=TEST.CAR.CLAIM.BACK2, externalStorageTableName=ExternalStorage_SHARE_CDC_1192523734, shareCdcTaskId=6761d14eaa4ad53b67c6f1eb, connectionId=6761cefda355981f6f70a1a2) 
[INFO ] 2024-12-18 13:58:17.721 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.BACK1) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:58:17.721 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.BACK1' log, sequence: 0 
[INFO ] 2024-12-18 13:58:17.722 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.BACK1 filter: {sequence=0} 
[INFO ] 2024-12-18 13:58:17.958 - [测试子用户先有共享挖掘任务][MongoTarget] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from MongoTarget_TEST.CAR.CLAIM.BACK2_测试子用户先有共享挖掘任务, external storage name: ExternalStorage_SHARE_CDC_1192523734 
[INFO ] 2024-12-18 13:58:17.958 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TEST.CAR.CLAIM.BACK2] 
[INFO ] 2024-12-18 13:58:17.961 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find sequence in construct(TEST.CAR.CLAIM.BACK2) by timestamp(2024-12-18T05:23:14.504Z): 0 
[INFO ] 2024-12-18 13:58:17.961 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Starting read 'TEST.CAR.CLAIM.BACK2' log, sequence: 0 
[INFO ] 2024-12-18 13:58:17.964 - [测试子用户先有共享挖掘任务][MongoTarget] - [Share CDC Task HZ Reader] - Find by TEST.CAR.CLAIM.BACK2 filter: {sequence=0} 
[INFO ] 2024-12-18 14:17:20.357 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] running status set to false 
[INFO ] 2024-12-18 14:17:20.358 - [测试子用户先有共享挖掘任务][MongoTarget] - Incremental sync completed 
[INFO ] 2024-12-18 14:17:20.368 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node stopped: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734501496997 
[INFO ] 2024-12-18 14:17:20.368 - [测试子用户先有共享挖掘任务][MongoTarget] - PDK connector node released: HazelcastSourcePdkDataNode_cbbcc3ad-017f-4480-adf9-5ceb81d87b80_1734501496997 
[INFO ] 2024-12-18 14:17:20.369 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] schema data cleaned 
[INFO ] 2024-12-18 14:17:20.369 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] monitor closed 
[INFO ] 2024-12-18 14:17:20.372 - [测试子用户先有共享挖掘任务][MongoTarget] - Node MongoTarget[cbbcc3ad-017f-4480-adf9-5ceb81d87b80] close complete, cost 44 ms 
[INFO ] 2024-12-18 14:17:20.372 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] running status set to false 
[INFO ] 2024-12-18 14:17:20.463 - [测试子用户先有共享挖掘任务][Dummy] - Stop connector 
[INFO ] 2024-12-18 14:17:20.466 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734501496997 
[INFO ] 2024-12-18 14:17:20.466 - [测试子用户先有共享挖掘任务][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_50631aa3-dad8-46aa-a7a8-67016015e636_1734501496997 
[INFO ] 2024-12-18 14:17:20.466 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] schema data cleaned 
[INFO ] 2024-12-18 14:17:20.466 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] monitor closed 
[INFO ] 2024-12-18 14:17:20.867 - [测试子用户先有共享挖掘任务][Dummy] - Node Dummy[50631aa3-dad8-46aa-a7a8-67016015e636] close complete, cost 390 ms 
[INFO ] 2024-12-18 14:17:22.564 - [测试子用户先有共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:17:22.567 - [测试子用户先有共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5e9f4b56 
[INFO ] 2024-12-18 14:17:22.741 - [测试子用户先有共享挖掘任务] - Stop task milestones: 6761d13caa4ad53b67c6f1af(测试子用户先有共享挖掘任务)  
[INFO ] 2024-12-18 14:17:22.743 - [测试子用户先有共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:17:22.743 - [测试子用户先有共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:17:22.743 - [测试子用户先有共享挖掘任务] - Remove memory task client succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
[INFO ] 2024-12-18 14:17:22.946 - [测试子用户先有共享挖掘任务] - Destroy memory task client cache succeed, task: 测试子用户先有共享挖掘任务[6761d13caa4ad53b67c6f1af] 
