[TRACE] 2025-05-13 14:33:24.158 - [任务 52] - Task initialization... 
[TRACE] 2025-05-13 14:33:24.158 - [任务 52] - Start task milestones: 6822e717995bd0727260dd45(任务 52) 
[INFO ] 2025-05-13 14:33:24.760 - [任务 52] - Loading table structure completed 
[TRACE] 2025-05-13 14:33:25.484 - [任务 52] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-13 14:33:25.484 - [任务 52] - The engine receives 任务 52 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-13 14:33:25.893 - [任务 52] - Task started 
[TRACE] 2025-05-13 14:33:25.945 - [任务 52][PG - Copy] - Node PG - Copy[eb1271cb-5011-4cd5-9b35-fe0f6722740c] start preload schema,table counts: 1 
[TRACE] 2025-05-13 14:33:25.952 - [任务 52][SybaseL] - Node SybaseL[8c6f8fad-5665-4831-8743-ffa877b3b72e] start preload schema,table counts: 1 
[TRACE] 2025-05-13 14:33:25.957 - [任务 52][PG - Copy] - Node PG - Copy[eb1271cb-5011-4cd5-9b35-fe0f6722740c] preload schema finished, cost 0 ms 
[TRACE] 2025-05-13 14:33:25.957 - [任务 52][SybaseL] - Node SybaseL[8c6f8fad-5665-4831-8743-ffa877b3b72e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-13 14:33:26.749 - [任务 52][SybaseL] - Source connector(SybaseL) initialization completed 
[TRACE] 2025-05-13 14:33:26.764 - [任务 52][SybaseL] - Source node "SybaseL" read batch size: 100 
[TRACE] 2025-05-13 14:33:26.767 - [任务 52][SybaseL] - Source node "SybaseL" event queue capacity: 200 
[TRACE] 2025-05-13 14:33:26.770 - [任务 52][SybaseL] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-13 14:33:26.770 - [任务 52][SybaseL] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-13 14:33:26.834 - [任务 52][SybaseL] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-13 14:33:26.835 - [任务 52][SybaseL] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-13 14:33:26.880 - [任务 52][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-13 14:33:26.883 - [任务 52][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-13 14:33:26.883 - [任务 52][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-13 14:33:26.883 - [任务 52][SybaseL] - Starting batch read from 1 tables 
[INFO ] 2025-05-13 14:33:26.902 - [任务 52][PG - Copy] - Apply table structure to target database 
[TRACE] 2025-05-13 14:33:26.902 - [任务 52][SybaseL] - Initial sync started 
[INFO ] 2025-05-13 14:33:26.906 - [任务 52][SybaseL] - Starting batch read from table: test_timestamp 
[TRACE] 2025-05-13 14:33:26.906 - [任务 52][SybaseL] - Table test_timestamp is going to be initial synced 
[TRACE] 2025-05-13 14:33:26.948 - [任务 52][SybaseL] - Query snapshot row size completed: SybaseL(8c6f8fad-5665-4831-8743-ffa877b3b72e) 
[INFO ] 2025-05-13 14:33:26.949 - [任务 52][SybaseL] - Table test_timestamp has been completed batch read 
[TRACE] 2025-05-13 14:33:26.951 - [任务 52][SybaseL] - Initial sync completed 
[INFO ] 2025-05-13 14:33:26.958 - [任务 52][SybaseL] - Batch read completed. 
[TRACE] 2025-05-13 14:33:26.958 - [任务 52][SybaseL] - Incremental sync starting... 
[TRACE] 2025-05-13 14:33:26.961 - [任务 52][SybaseL] - Initial sync completed 
[TRACE] 2025-05-13 14:33:26.961 - [任务 52][SybaseL] - Starting stream read, table list: [test_timestamp], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-13 14:33:26.978 - [任务 52][SybaseL] - Starting incremental sync using database log parser 
[INFO ] 2025-05-13 14:33:26.979 - [任务 52][SybaseL] - startRid: 525104, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-13 14:33:26.979 - [任务 52][SybaseL] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-13 14:33:26.979 - [任务 52][SybaseL] - sybase offset in database is: startRid: 525104, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-13 14:33:27.003 - [任务 52][SybaseL] - we will use offset in database, how ever, this is safe: startRid: 525104, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-13 14:33:27.004 - [任务 52][SybaseL] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-05-13 14:33:27.042 - [任务 52][SybaseL] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-13 14:33:27.042 - [任务 52][SybaseL] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-13 14:33:27.057 - [任务 52][SybaseL] - opened cdc for tables: {dbo=[test_timestamp]} 
[INFO ] 2025-05-13 14:33:27.057 - [任务 52][SybaseL] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-13 14:33:27.100 - [任务 52][SybaseL] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-13 14:33:27.100 - [任务 52][SybaseL] - trans timestamp offset: 0 
[INFO ] 2025-05-13 14:33:27.101 - [任务 52][SybaseL] - sybase cdc debug log is disabled 
[INFO ] 2025-05-13 14:33:27.102 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525104, rowId: 0 
[WARN ] 2025-05-13 14:33:27.410 - [任务 52][PG - Copy] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6822e7b6f49a6a0b6bfa0c85, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[8c6f8fad-5665-4831-8743-ffa877b3b72e], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-05-13 14:33:27.436 - [任务 52][PG - Copy] - Process after table "test_timestamp" initial sync finished, cost: 22 ms 
[INFO ] 2025-05-13 14:33:27.436 - [任务 52][PG - Copy] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-05-13 14:33:30.109 - [任务 52][SybaseL] - rebuild statement with 525104, 0 
[INFO ] 2025-05-13 14:33:30.161 - [任务 52][SybaseL] - uncommit trans size: 0 
[INFO ] 2025-05-13 14:33:30.162 - [任务 52][SybaseL] - uncommit trans: {} 
[INFO ] 2025-05-13 14:33:30.163 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:33:30.173 - [任务 52][SybaseL] - column: 2,columnName:, value: 525103 
[INFO ] 2025-05-13 14:33:30.182 - [任务 52][SybaseL] - column: 3,columnName:, value: 15 
[INFO ] 2025-05-13 14:33:30.184 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.187 - [任务 52][SybaseL] - column: 5,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.187 - [任务 52][SybaseL] - column: 6,columnName:, value: 6 
[INFO ] 2025-05-13 14:33:30.188 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.188 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 32918 
[INFO ] 2025-05-13 14:33:30.195 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T11:48:10.713+0800 
[INFO ] 2025-05-13 14:33:30.198 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.199 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.199 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.201 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T11:49:33.810+0800 
[INFO ] 2025-05-13 14:33:30.201 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.201 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 32918 
[INFO ] 2025-05-13 14:33:30.202 - [任务 52][SybaseL] - column: 7,columnName:, value: _upd 
[INFO ] 2025-05-13 14:33:30.202 - [任务 52][SybaseL] - column: 8,columnName:, value: null 
[INFO ] 2025-05-13 14:33:30.202 - [任务 52][SybaseL] - column: 9,columnName:, value: null 
[INFO ] 2025-05-13 14:33:30.205 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.206 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.206 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.207 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.210 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.212 - [任务 52][SybaseL] - column: 6,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.212 - [任务 52][SybaseL] - column: 7,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.215 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.216 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 32918 
[INFO ] 2025-05-13 14:33:30.217 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.219 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.221 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.222 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.222 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.223 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.224 - [任务 52][SybaseL] - column: 6,columnName:, value: 525105 
[INFO ] 2025-05-13 14:33:30.226 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.226 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.226 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33009 
[INFO ] 2025-05-13 14:33:30.227 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.228 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.232 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 6,columnName:, value: 525105 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33009 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.233 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.234 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.234 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.234 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.234 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.234 - [任务 52][SybaseL] - column: 6,columnName:, value: 525105 
[INFO ] 2025-05-13 14:33:30.234 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.234 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.235 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33009 
[INFO ] 2025-05-13 14:33:30.235 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.235 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.236 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.237 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.239 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.242 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.243 - [任务 52][SybaseL] - column: 6,columnName:, value: 525106 
[INFO ] 2025-05-13 14:33:30.244 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.248 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.249 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33010 
[INFO ] 2025-05-13 14:33:30.249 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.250 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.256 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.256 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.256 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.265 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.267 - [任务 52][SybaseL] - column: 6,columnName:, value: 525106 
[INFO ] 2025-05-13 14:33:30.267 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.271 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.274 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33010 
[INFO ] 2025-05-13 14:33:30.276 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.276 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.276 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.276 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.276 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.276 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.276 - [任务 52][SybaseL] - column: 6,columnName:, value: 525106 
[INFO ] 2025-05-13 14:33:30.277 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.277 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.277 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33010 
[INFO ] 2025-05-13 14:33:30.277 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.277 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.277 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.277 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.279 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.281 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 6,columnName:, value: 525107 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33011 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 6,columnName:, value: 525107 
[INFO ] 2025-05-13 14:33:30.284 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33011 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.285 - [任务 52][SybaseL] - column: 6,columnName:, value: 525107 
[INFO ] 2025-05-13 14:33:30.286 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.286 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.286 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33011 
[INFO ] 2025-05-13 14:33:30.286 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.286 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.286 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.287 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.287 - [任务 52][SybaseL] - column: 4,columnName:, value: 504 
[INFO ] 2025-05-13 14:33:30.287 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.287 - [任务 52][SybaseL] - column: 6,columnName:, value: 525108 
[INFO ] 2025-05-13 14:33:30.290 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.291 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.292 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33012 
[INFO ] 2025-05-13 14:33:30.292 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.292 - [任务 52][SybaseL] - column: 1,columnName:, value: 32 
[INFO ] 2025-05-13 14:33:30.292 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.292 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.293 - [任务 52][SybaseL] - column: 4,columnName:, value: 208 
[INFO ] 2025-05-13 14:33:30.293 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.293 - [任务 52][SybaseL] - column: 6,columnName:, value: 525108 
[INFO ] 2025-05-13 14:33:30.293 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.293 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.293 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33012 
[INFO ] 2025-05-13 14:33:30.294 - [任务 52][SybaseL] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 525104-7, startRid: 525104, rowId: 7 
[INFO ] 2025-05-13 14:33:30.294 - [任务 52][SybaseL] - column: 1,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.296 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.301 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.310 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.311 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.311 - [任务 52][SybaseL] - column: 6,columnName:, value: 525108 
[INFO ] 2025-05-13 14:33:30.311 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.311 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.311 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33012 
[INFO ] 2025-05-13 14:33:30.311 - [任务 52][SybaseL] - column: 1,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.312 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.312 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.312 - [任务 52][SybaseL] - column: 4,columnName:, value: 2060 
[INFO ] 2025-05-13 14:33:30.312 - [任务 52][SybaseL] - column: 5,columnName:, value: [0, 0, 0, 0, -17, -128, 0, 0] 
[INFO ] 2025-05-13 14:33:30.312 - [任务 52][SybaseL] - column: 6,columnName:, value: [0, 0, 0, 0, -16, -128, 0, 0] 
[INFO ] 2025-05-13 14:33:30.313 - [任务 52][SybaseL] - column: 7,columnName:, value: 525108 
[INFO ] 2025-05-13 14:33:30.313 - [任务 52][SybaseL] - column: 8,columnName:, value: 4 
[INFO ] 2025-05-13 14:33:30.316 - [任务 52][SybaseL] - column: 9,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.317 - [任务 52][SybaseL] - column: 10,columnName:log_ts_low, value: 33012 
[INFO ] 2025-05-13 14:33:30.317 - [任务 52][SybaseL] - column: 11,columnName:, value: testrslt 
[INFO ] 2025-05-13 14:33:30.317 - [任务 52][SybaseL] - column: 12,columnName:, value: dbo 
[INFO ] 2025-05-13 14:33:30.318 - [任务 52][SybaseL] - column: 1,columnName:, value: 11 
[INFO ] 2025-05-13 14:33:30.318 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 4,columnName:, value: 432 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 5,columnName:, value: 136 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.319 - [任务 52][SybaseL] - column: 1,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.320 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.320 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.320 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.320 - [任务 52][SybaseL] - column: 5,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.321 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.321 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.324 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.324 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.325 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:33:30.325 - [任务 52][SybaseL] - column: 2,columnName:, value: 525104 
[INFO ] 2025-05-13 14:33:30.325 - [任务 52][SybaseL] - column: 3,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.325 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.326 - [任务 52][SybaseL] - column: 5,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.327 - [任务 52][SybaseL] - column: 6,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.327 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.327 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.328 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T11:49:33.810+0800 
[INFO ] 2025-05-13 14:33:30.329 - [任务 52][SybaseL] - transBroken find, will rescan: 525104, 7 
[WARN ] 2025-05-13 14:33:30.424 - [任务 52][SybaseL] - parse event failed, eventStr: 3003fa7f0180010001008601323559303031313032380303000002000002dfdbfe428b030000000000000000000049770000ffe1ff00000101000000000000313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435ba1305ffffff7f0100008000007f242d00ff818b013116534b4b4b4b4b4b4b4b4b4b4b4a4a4945414140404040403e3e3e3e3e3f3f3d3d39393837333232323222221c1a1817160c, table: testrslt, dbccLogFormat: null, error: Cannot invoke "java.util.Map.get(Object)" because "dbccLogFormat" is null 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - rescan archive: 525104, 7, size: 2, cost: 93 ms 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - column: 3,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T14:25:08.783+0800 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.427 - [任务 52][SybaseL] - column: 7,columnName:, value: _CREATE_TABLE 
[INFO ] 2025-05-13 14:33:30.428 - [任务 52][SybaseL] - column: 8,columnName:, value: sa 
[INFO ] 2025-05-13 14:33:30.429 - [任务 52][SybaseL] - column: 9,columnName:, value: sybase 
[INFO ] 2025-05-13 14:33:30.430 - [任务 52][SybaseL] - column: 1,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.430 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.430 - [任务 52][SybaseL] - column: 3,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.430 - [任务 52][SybaseL] - column: 4,columnName:, value: 24 
[INFO ] 2025-05-13 14:33:30.430 - [任务 52][SybaseL] - column: 5,columnName:, value: 320 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 7,columnName:, value: 4 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 3,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.431 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 7,columnName:, value: 6 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 3,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 4,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.432 - [任务 52][SybaseL] - column: 5,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.433 - [任务 52][SybaseL] - column: 6,columnName:, value: 9 
[INFO ] 2025-05-13 14:33:30.433 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.433 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.433 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T14:25:08.783+0800 
[INFO ] 2025-05-13 14:33:30.437 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.445 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.445 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T14:31:28.263+0800 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 7,columnName:, value: _CREATE_TABLE 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 8,columnName:, value: sa 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 9,columnName:, value: sybase 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 1,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 4,columnName:, value: 24 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 5,columnName:, value: 320 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 7,columnName:, value: 11 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.446 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.447 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.447 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.447 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.448 - [任务 52][SybaseL] - column: 7,columnName:, value: 13 
[INFO ] 2025-05-13 14:33:30.448 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.448 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.448 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.448 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.449 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 7,columnName:, value: 15 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.450 - [任务 52][SybaseL] - column: 6,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.451 - [任务 52][SybaseL] - column: 7,columnName:, value: 17 
[INFO ] 2025-05-13 14:33:30.451 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.451 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 33013 
[INFO ] 2025-05-13 14:33:30.452 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.452 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.452 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.452 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.453 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.453 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.453 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.453 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.453 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 4,columnName:, value: 96 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.454 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.455 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.455 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.455 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.455 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.455 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.455 - [任务 52][SybaseL] - column: 7,columnName:, value: 4 
[INFO ] 2025-05-13 14:33:30.455 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.456 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.456 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.456 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.457 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.457 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.457 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.457 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.457 - [任务 52][SybaseL] - column: 7,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.459 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.459 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.459 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.459 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.459 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.459 - [任务 52][SybaseL] - column: 4,columnName:, value: 104 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 7,columnName:, value: 6 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.460 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 7,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.461 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.462 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.462 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.462 - [任务 52][SybaseL] - column: 7,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.462 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.463 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.463 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:33:30.466 - [任务 52][SybaseL] - column: 2,columnName:, value: 525109 
[INFO ] 2025-05-13 14:33:30.466 - [任务 52][SybaseL] - column: 3,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.473 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.473 - [任务 52][SybaseL] - column: 5,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.476 - [任务 52][SybaseL] - column: 6,columnName:, value: 11 
[INFO ] 2025-05-13 14:33:30.476 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.476 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.476 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T14:31:28.263+0800 
[INFO ] 2025-05-13 14:33:30.477 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.477 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.477 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.477 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T14:31:28.263+0800 
[INFO ] 2025-05-13 14:33:30.477 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.480 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.481 - [任务 52][SybaseL] - column: 7,columnName:, value: _DROP_TABLE 
[INFO ] 2025-05-13 14:33:30.481 - [任务 52][SybaseL] - column: 8,columnName:, value: sa 
[INFO ] 2025-05-13 14:33:30.481 - [任务 52][SybaseL] - column: 9,columnName:, value: sybase 
[INFO ] 2025-05-13 14:33:30.481 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.481 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 7,columnName:, value: 13 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 6,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 7,columnName:, value: 15 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37355 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.482 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.483 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.483 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.483 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.483 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 4,columnName:, value: 96 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.484 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.485 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.485 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.485 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.485 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.485 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.485 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 7,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.486 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 7,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 7,columnName:, value: 6 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.487 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 4,columnName:, value: 104 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 7,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.488 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 7,columnName:, value: 9 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 7,columnName:, value: 11 
[INFO ] 2025-05-13 14:33:30.489 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 2,columnName:, value: 525110 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 3,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 5,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 6,columnName:, value: 13 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.490 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T14:31:28.263+0800 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T14:31:28.263+0800 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 7,columnName:, value: _CREATE_TABLE 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 8,columnName:, value: sa 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 9,columnName:, value: sybase 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 1,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.491 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.492 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.495 - [任务 52][SybaseL] - column: 4,columnName:, value: 24 
[INFO ] 2025-05-13 14:33:30.495 - [任务 52][SybaseL] - column: 5,columnName:, value: 320 
[INFO ] 2025-05-13 14:33:30.503 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.505 - [任务 52][SybaseL] - column: 7,columnName:, value: 15 
[INFO ] 2025-05-13 14:33:30.505 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.505 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.505 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.505 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.505 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.505 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 6,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 7,columnName:, value: 17 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37374 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 7,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.506 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 7,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 4,columnName:, value: 96 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 7,columnName:, value: 6 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.507 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 7,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 7,columnName:, value: 9 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.508 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 4,columnName:, value: 104 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 7,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 7,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.509 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 7,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 1,columnName:, value: 41 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 4,columnName:, value: 128 
[INFO ] 2025-05-13 14:33:30.510 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 7,columnName:, value: 15 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 1,columnName:, value: 39 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 4,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 5,columnName:, value: 320 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 7,columnName:, value: 16 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.511 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 1,columnName:, value: 13 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 4,columnName:, value: 128 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 5,columnName:, value: 512 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 6,columnName:, value: 525112 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 7,columnName:, value: 17 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37394 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 1,columnName:, value: 51 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 4,columnName:, value: 144 
[INFO ] 2025-05-13 14:33:30.512 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.513 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.513 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.517 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.517 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.528 - [任务 52][SybaseL] - column: 1,columnName:, value: 52 
[INFO ] 2025-05-13 14:33:30.528 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.528 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.528 - [任务 52][SybaseL] - column: 4,columnName:, value: 120 
[INFO ] 2025-05-13 14:33:30.528 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 7,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 7,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.529 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 4,columnName:, value: 96 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 7,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 1,columnName:, value: 41 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 4,columnName:, value: 128 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 7,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.530 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 1,columnName:, value: 39 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 4,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 5,columnName:, value: 320 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 7,columnName:, value: 9 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 1,columnName:, value: 13 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.531 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 4,columnName:, value: 128 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 5,columnName:, value: 512 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 7,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 1,columnName:, value: 77 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.532 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.533 - [任务 52][SybaseL] - column: 4,columnName:, value: 152 
[INFO ] 2025-05-13 14:33:30.533 - [任务 52][SybaseL] - column: 5,columnName:, value: 512 
[INFO ] 2025-05-13 14:33:30.534 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.534 - [任务 52][SybaseL] - column: 7,columnName:, value: 11 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 1,columnName:, value: 41 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 4,columnName:, value: 128 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 7,columnName:, value: 12 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.535 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 1,columnName:, value: 39 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 4,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 5,columnName:, value: 320 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 7,columnName:, value: 13 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 1,columnName:, value: 13 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 4,columnName:, value: 128 
[INFO ] 2025-05-13 14:33:30.536 - [任务 52][SybaseL] - column: 5,columnName:, value: 512 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 7,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 1,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 4,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 6,columnName:, value: 525113 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 7,columnName:, value: 15 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37401 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.537 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 6,columnName:, value: 525114 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37422 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 6,columnName:, value: 525114 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 7,columnName:, value: 4 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37422 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 1,columnName:, value: 51 
[INFO ] 2025-05-13 14:33:30.538 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 4,columnName:, value: 144 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 6,columnName:, value: 525114 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 7,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37422 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 1,columnName:, value: 52 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 4,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 6,columnName:, value: 525114 
[INFO ] 2025-05-13 14:33:30.539 - [任务 52][SybaseL] - column: 7,columnName:, value: 6 
[INFO ] 2025-05-13 14:33:30.541 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.543 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37422 
[INFO ] 2025-05-13 14:33:30.543 - [任务 52][SybaseL] - column: 1,columnName:, value: 52 
[INFO ] 2025-05-13 14:33:30.547 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 4,columnName:, value: 120 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 6,columnName:, value: 525114 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 7,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37422 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 1,columnName:, value: 52 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 4,columnName:, value: 120 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 6,columnName:, value: 525114 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 7,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.556 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37422 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 6,columnName:, value: 525114 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 7,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37422 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 4,columnName:, value: 104 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.557 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 7,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 4,columnName:, value: 88 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 7,columnName:, value: 7 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 7,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 4,columnName:, value: 120 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 7,columnName:, value: 9 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 1,columnName:, value: 51 
[INFO ] 2025-05-13 14:33:30.558 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 4,columnName:, value: 144 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 7,columnName:, value: 10 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 1,columnName:, value: 52 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 4,columnName:, value: 120 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 5,columnName:, value: 64 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 6,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 7,columnName:, value: 11 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 2,columnName:, value: 525111 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 3,columnName:, value: 14 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 5,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 6,columnName:, value: 17 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T14:31:28.263+0800 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 1,columnName:, value: 17 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 2,columnName:, value: 8256 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 3,columnName:, value: 2025-05-13T14:32:26.356+0800 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 4,columnName:, value: [0, 0, 0, 0, 77, -110, 0, 0] 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 6,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.559 - [任务 52][SybaseL] - column: 8,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 9,columnName:, value: 525115 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 10,columnName:, value: 18 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 11,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 12,columnName:log_ts_low, value: 37444 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T14:33:26.366+0800 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 7,columnName:, value: rs_logexec 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 8,columnName:, value: tester 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 9,columnName:, value: guest1234 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 1,columnName:, value: 58 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.560 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 4,columnName:, value: 344 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 6,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 7,columnName:, value: 1 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 1,columnName:, value: 59 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 4,columnName:, value: 328 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 6,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 7,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.561 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 1,columnName:, value: 59 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 4,columnName:, value: 328 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 6,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 7,columnName:, value: 3 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 1,columnName:, value: 59 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 4,columnName:, value: 328 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 6,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 7,columnName:, value: 4 
[INFO ] 2025-05-13 14:33:30.562 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 4,columnName:, value: 96 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 6,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 7,columnName:, value: 5 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 1,columnName:, value: 72 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.563 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 6,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 7,columnName:, value: 6 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 4,columnName:, value: 96 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 6,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 7,columnName:, value: 8 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37456 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 1,columnName:, value: 71 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.564 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.565 - [任务 52][SybaseL] - column: 4,columnName:, value: 80 
[INFO ] 2025-05-13 14:33:30.565 - [任务 52][SybaseL] - column: 5,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.565 - [任务 52][SybaseL] - column: 6,columnName:, value: 525117 
[INFO ] 2025-05-13 14:33:30.565 - [任务 52][SybaseL] - column: 7,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.565 - [任务 52][SybaseL] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.565 - [任务 52][SybaseL] - column: 9,columnName:log_ts_low, value: 37463 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 2,columnName:, value: 525116 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 3,columnName:, value: 0 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 5,columnName:, value: 525117 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 6,columnName:, value: 2 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 37463 
[INFO ] 2025-05-13 14:33:30.567 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T14:33:26.366+0800 
[INFO ] 2025-05-13 14:33:30.769 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:33.607 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:33.815 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:36.668 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:36.757 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:39.813 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:40.018 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:42.845 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:42.883 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:45.888 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:45.971 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:48.977 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:49.180 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:52.104 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:52.105 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:55.112 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:55.321 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:33:58.237 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:33:58.237 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:01.243 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:01.448 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:04.381 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:04.381 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:07.386 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:07.592 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:10.517 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:10.518 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:13.524 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:13.729 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:16.661 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:16.661 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:19.667 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:19.873 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:22.722 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:22.768 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:25.774 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:25.850 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:28.895 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:29.098 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:31.920 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:31.947 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:34.987 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:35.192 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:38.027 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:38.232 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:41.057 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:41.258 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:44.102 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:44.173 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:47.177 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:47.383 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:50.281 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:50.281 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:53.305 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:53.508 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:56.410 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:56.410 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:34:59.414 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:34:59.620 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:02.549 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:02.550 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:05.553 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:05.757 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:08.680 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:08.680 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:11.681 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:11.882 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:14.719 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:14.771 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:17.776 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:17.838 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:20.879 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:21.085 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:23.926 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:24.129 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:26.944 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:26.992 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:29.998 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:30.056 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:33.085 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:33.289 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:36.232 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:36.233 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:39.237 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:39.442 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:42.395 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:42.396 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:45.396 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:45.597 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:48.440 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:48.505 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:51.540 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:51.540 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:54.570 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:54.570 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:35:57.596 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:35:57.801 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:00.618 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:00.680 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:03.718 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:03.719 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:06.723 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:06.929 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:09.769 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:09.975 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:12.818 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:12.868 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:15.871 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:16.075 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:18.910 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:18.983 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:22.006 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:22.210 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:25.030 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:25.091 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:28.137 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:28.342 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 2 
[INFO ] 2025-05-13 14:36:31.152 - [任务 52][SybaseL] - rebuild statement with 525117, 2 
[INFO ] 2025-05-13 14:36:31.230 - [任务 52][SybaseL] - uncommit trans size: 0 
[INFO ] 2025-05-13 14:36:31.231 - [任务 52][SybaseL] - uncommit trans: {} 
[INFO ] 2025-05-13 14:36:31.232 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:36:31.232 - [任务 52][SybaseL] - column: 2,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:31.232 - [任务 52][SybaseL] - column: 3,columnName:, value: 3 
[INFO ] 2025-05-13 14:36:31.234 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T14:36:27.650+0800 
[INFO ] 2025-05-13 14:36:31.234 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:36:31.234 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 37463 
[INFO ] 2025-05-13 14:36:31.234 - [任务 52][SybaseL] - column: 7,columnName:, value: _ins 
[INFO ] 2025-05-13 14:36:31.234 - [任务 52][SybaseL] - column: 8,columnName:, value: null 
[INFO ] 2025-05-13 14:36:31.235 - [任务 52][SybaseL] - column: 9,columnName:, value: null 
[INFO ] 2025-05-13 14:36:31.235 - [任务 52][SybaseL] - column: 1,columnName:, value: 4 
[INFO ] 2025-05-13 14:36:31.236 - [任务 52][SybaseL] - column: 2,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:31.236 - [任务 52][SybaseL] - column: 3,columnName:, value: 3 
[INFO ] 2025-05-13 14:36:31.237 - [任务 52][SybaseL] - column: 4,columnName:, value: 2048 
[INFO ] 2025-05-13 14:36:31.237 - [任务 52][SybaseL] - column: 5,columnName:, value: [0, 0, 0, 0, 38, -110, 0, 0] 
[INFO ] 2025-05-13 14:36:31.238 - [任务 52][SybaseL] - column: 6,columnName:, value: [0, 0, 0, 0, 14, -109, 0, 0] 
[INFO ] 2025-05-13 14:36:31.240 - [任务 52][SybaseL] - column: 7,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:31.240 - [任务 52][SybaseL] - column: 8,columnName:, value: 4 
[INFO ] 2025-05-13 14:36:31.242 - [任务 52][SybaseL] - column: 9,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:36:31.242 - [任务 52][SybaseL] - column: 10,columnName:log_ts_low, value: 37463 
[INFO ] 2025-05-13 14:36:31.242 - [任务 52][SybaseL] - column: 11,columnName:, value: test_timestamp 
[INFO ] 2025-05-13 14:36:31.249 - [任务 52][SybaseL] - column: 12,columnName:, value: dbo 
[INFO ] 2025-05-13 14:36:31.249 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:36:31.249 - [任务 52][SybaseL] - column: 2,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:31.249 - [任务 52][SybaseL] - column: 3,columnName:, value: 3 
[INFO ] 2025-05-13 14:36:31.250 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:36:31.250 - [任务 52][SybaseL] - column: 5,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:31.250 - [任务 52][SybaseL] - column: 6,columnName:, value: 5 
[INFO ] 2025-05-13 14:36:31.250 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:36:31.251 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 37463 
[INFO ] 2025-05-13 14:36:31.251 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T14:36:27.650+0800 
[INFO ] 2025-05-13 14:36:31.456 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 5 
[INFO ] 2025-05-13 14:36:34.284 - [任务 52][SybaseL] - rebuild statement with 525117, 5 
[INFO ] 2025-05-13 14:36:34.490 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 5 
[INFO ] 2025-05-13 14:36:37.295 - [任务 52][SybaseL] - rebuild statement with 525117, 5 
[INFO ] 2025-05-13 14:36:37.336 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 5 
[INFO ] 2025-05-13 14:36:40.363 - [任务 52][SybaseL] - rebuild statement with 525117, 5 
[INFO ] 2025-05-13 14:36:40.377 - [任务 52][SybaseL] - column: 1,columnName:, value: 0 
[INFO ] 2025-05-13 14:36:40.377 - [任务 52][SybaseL] - column: 2,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:40.377 - [任务 52][SybaseL] - column: 3,columnName:, value: 6 
[INFO ] 2025-05-13 14:36:40.377 - [任务 52][SybaseL] - column: 4,columnName:, value: 2025-05-13T14:36:39.650+0800 
[INFO ] 2025-05-13 14:36:40.377 - [任务 52][SybaseL] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:36:40.377 - [任务 52][SybaseL] - column: 6,columnName:log_ts_low, value: 37463 
[INFO ] 2025-05-13 14:36:40.377 - [任务 52][SybaseL] - column: 7,columnName:, value: _systsflush 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 8,columnName:, value: null 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 9,columnName:, value: null 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 1,columnName:, value: 30 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 2,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 3,columnName:, value: 6 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 4,columnName:, value: 2 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 5,columnName:, value: 525117 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 6,columnName:, value: 8 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-05-13 14:36:40.378 - [任务 52][SybaseL] - column: 8,columnName:log_ts_low, value: 37463 
[INFO ] 2025-05-13 14:36:40.379 - [任务 52][SybaseL] - column: 9,columnName:, value: 2025-05-13T14:36:39.650+0800 
[INFO ] 2025-05-13 14:36:40.379 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:36:43.386 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:36:43.438 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:36:46.478 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:36:46.478 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:36:49.530 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:36:49.735 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:36:52.579 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:36:52.782 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:36:55.612 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:36:55.816 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:36:58.747 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:36:58.748 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:01.752 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:01.954 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:04.848 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:04.849 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:07.885 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:08.088 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:10.920 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:10.968 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:14.006 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:14.212 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:17.056 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:17.257 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:20.074 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:20.142 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:23.148 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:23.351 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:26.250 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:26.251 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:29.252 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:29.453 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:32.297 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:32.350 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:35.377 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:35.583 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:38.482 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:38.482 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:41.524 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:41.732 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:44.578 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:44.640 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:47.646 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:47.709 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:50.713 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:50.771 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:53.825 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:54.030 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:56.950 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:37:56.951 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:37:59.953 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:38:00.159 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:38:03.088 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:38:03.089 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:38:06.095 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:38:06.297 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:38:09.193 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:38:09.194 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:38:12.259 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:38:12.464 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[INFO ] 2025-05-13 14:38:15.272 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:38:15.322 - [任务 52][SybaseL] - normal rescan, will sleep 3s, and scan from startRid: 525117, rowId: 8 
[TRACE] 2025-05-13 14:38:17.328 - [任务 52][SybaseL] - Node SybaseL[8c6f8fad-5665-4831-8743-ffa877b3b72e] running status set to false 
[INFO ] 2025-05-13 14:38:17.328 - [任务 52][SybaseL] - rebuild statement with 525117, 8 
[INFO ] 2025-05-13 14:38:17.328 - [任务 52][SybaseL] - Log Miner is shutting down... 
[TRACE] 2025-05-13 14:38:17.407 - [任务 52][SybaseL] - Incremental sync completed 
[TRACE] 2025-05-13 14:38:17.407 - [任务 52][SybaseL] - PDK connector node stopped: HazelcastSourcePdkDataNode_8c6f8fad-5665-4831-8743-ffa877b3b72e_1747118006658 
[TRACE] 2025-05-13 14:38:17.407 - [任务 52][SybaseL] - PDK connector node released: HazelcastSourcePdkDataNode_8c6f8fad-5665-4831-8743-ffa877b3b72e_1747118006658 
[TRACE] 2025-05-13 14:38:17.408 - [任务 52][SybaseL] - Node SybaseL[8c6f8fad-5665-4831-8743-ffa877b3b72e] schema data cleaned 
[TRACE] 2025-05-13 14:38:17.410 - [任务 52][SybaseL] - Node SybaseL[8c6f8fad-5665-4831-8743-ffa877b3b72e] monitor closed 
[TRACE] 2025-05-13 14:38:17.411 - [任务 52][SybaseL] - Node SybaseL[8c6f8fad-5665-4831-8743-ffa877b3b72e] close complete, cost 87 ms 
[TRACE] 2025-05-13 14:38:17.449 - [任务 52][PG - Copy] - Node PG - Copy[eb1271cb-5011-4cd5-9b35-fe0f6722740c] running status set to false 
[TRACE] 2025-05-13 14:38:17.449 - [任务 52][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_eb1271cb-5011-4cd5-9b35-fe0f6722740c_1747118006730 
[TRACE] 2025-05-13 14:38:17.449 - [任务 52][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_eb1271cb-5011-4cd5-9b35-fe0f6722740c_1747118006730 
[TRACE] 2025-05-13 14:38:17.450 - [任务 52][PG - Copy] - Node PG - Copy[eb1271cb-5011-4cd5-9b35-fe0f6722740c] schema data cleaned 
[TRACE] 2025-05-13 14:38:17.450 - [任务 52][PG - Copy] - Node PG - Copy[eb1271cb-5011-4cd5-9b35-fe0f6722740c] monitor closed 
[TRACE] 2025-05-13 14:38:17.450 - [任务 52][PG - Copy] - Node PG - Copy[eb1271cb-5011-4cd5-9b35-fe0f6722740c] close complete, cost 39 ms 
[TRACE] 2025-05-13 14:38:21.514 - [任务 52] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-13 14:38:21.515 - [任务 52] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ff6868a 
[TRACE] 2025-05-13 14:38:21.640 - [任务 52] - Stop task milestones: 6822e717995bd0727260dd45(任务 52)  
[TRACE] 2025-05-13 14:38:21.640 - [任务 52] - Stopped task aspect(s) 
[TRACE] 2025-05-13 14:38:21.646 - [任务 52] - Snapshot order controller have been removed 
[INFO ] 2025-05-13 14:38:21.647 - [任务 52] - Task stopped. 
[TRACE] 2025-05-13 14:38:26.673 - [任务 52] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-13 14:38:26.674 - [任务 52] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ff6868a 
[TRACE] 2025-05-13 14:38:26.674 - [任务 52] - Stopped task aspect(s) 
[INFO ] 2025-05-13 14:38:26.674 - [任务 52] - Task stopped. 
[TRACE] 2025-05-13 14:38:26.843 - [任务 52] - Remove memory task client succeed, task: 任务 52[6822e717995bd0727260dd45] 
[TRACE] 2025-05-13 14:38:26.843 - [任务 52] - Destroy memory task client cache succeed, task: 任务 52[6822e717995bd0727260dd45] 
