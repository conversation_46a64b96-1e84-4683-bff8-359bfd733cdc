[INFO ] 2024-07-15 12:04:02.666 - [Heart<PERSON>-<PERSON><PERSON>] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-<PERSON><PERSON>) 
[INFO ] 2024-07-15 12:04:02.767 - [Heartbeat-<PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:04:02.802 - [Heartbeat-Mon<PERSON>] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:04:02.903 - [Heartbeat-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:04:02.903 - [Heartbeat-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:04:02.903 - [<PERSON><PERSON>-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:04:02.903 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:04:04.145 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-15 12:04:04.355 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721016244, "i": 7}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721016244, "i": 8}}, "signature": {"hash": {"$binary": {"base64": "2cm+Qp0YxtnVJ2lvm+cdVvBBEB0=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-15 12:04:04.800 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 12:04:04.801 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 12:04:04.801 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:04:04.853 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721016244799,"lastTimes":1721016244799,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-15 12:04:04.933 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-15 12:04:04.936 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-15 12:04:04.961 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-15 12:04:04.961 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-15 12:04:04.963 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 12:04:04.963 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721016244799,"lastTimes":1721016244799,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-15 12:04:04.965 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 12:04:04.965 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 12:04:35.620 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 12:04:35.622 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 12:04:35.628 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 12:04:35.629 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 12:04:35.629 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 12:04:35.629 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 12:04:35.630 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 49 ms 
[INFO ] 2024-07-15 12:04:35.630 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 12:04:35.646 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 12:04:35.647 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 12:04:35.647 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 12:04:35.647 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 12:04:35.650 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 16 ms 
[INFO ] 2024-07-15 12:04:36.630 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:04:36.630 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d88b64c 
[INFO ] 2024-07-15 12:04:36.746 - [Heartbeat-Mongo] - Stop task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo)  
[INFO ] 2024-07-15 12:04:36.767 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:04:36.767 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:04:36.797 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 12:04:36.800 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 17:59:31.872 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 17:59:32.098 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 17:59:32.141 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 17:59:32.207 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:59:32.207 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 17:59:32.207 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:59:32.207 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 17:59:32.494 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 17:59:32.494 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 17:59:32.497 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 17:59:32.557 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721016244933,"lastTimes":1721016244937,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721016244799,"lastTimes":1721016275073,"lastTN":32,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":31,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 17:59:32.560 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 17:59:32.655 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721016244799,"lastTimes":1721016275073,"lastTN":32,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":31,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 17:59:32.658 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 17:59:32.658 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:02:43.827 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:02:43.828 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-15 19:02:43.846 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:02:43.848 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:02:43.852 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 19:02:43.852 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 19:02:43.859 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 54 ms 
[INFO ] 2024-07-15 19:02:43.860 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[WARN ] 2024-07-15 19:02:43.920 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:02:43.920 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:02:43.920 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 19:02:43.925 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 19:02:43.925 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 64 ms 
[INFO ] 2024-07-15 19:05:41.423 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 19:05:41.739 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:05:41.856 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:05:41.858 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:05:41.858 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:05:41.861 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:05:41.862 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:05:42.078 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 19:05:42.079 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 19:05:42.114 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:05:42.151 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:05:42.152 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721016244799,"lastTimes":1721041363017,"lastTN":3490,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":3521,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:05:42.259 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721016244799,"lastTimes":1721041363017,"lastTN":3490,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":3521,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:05:42.261 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 19:05:42.462 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:08:36.771 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:08:36.774 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 19:08:36.779 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:08:36.780 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:08:36.780 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 19:08:36.781 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 19:08:36.782 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 29 ms 
[INFO ] 2024-07-15 19:08:36.785 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 19:08:36.831 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:08:36.831 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:08:36.831 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 19:08:36.831 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 19:08:36.835 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 51 ms 
[INFO ] 2024-07-15 19:08:41.134 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:08:41.140 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@e01b483 
[INFO ] 2024-07-15 19:08:41.141 - [Heartbeat-Mongo] - Stop task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo)  
[INFO ] 2024-07-15 19:08:41.251 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:08:41.251 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:08:41.273 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:08:41.276 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:09:05.426 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 19:09:05.587 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:09:05.694 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:09:05.694 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:09:05.694 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:09:05.696 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 19:09:05.696 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:09:05.755 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 19:09:05.755 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 19:09:05.756 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:09:05.823 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721016244799,"lastTimes":1721041716749,"lastTN":175,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":3696,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:09:05.824 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721016244799,"lastTimes":1721041716749,"lastTN":175,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":3696,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:09:05.825 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 19:09:05.825 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:09:06.029 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:09:55.529 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:09:55.531 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 19:09:55.537 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:09:55.538 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:09:55.538 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 19:09:55.539 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 19:09:55.543 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 44 ms 
[INFO ] 2024-07-15 19:09:55.543 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 19:09:55.568 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:09:55.568 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:09:55.568 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 19:09:55.571 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 19:09:55.571 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 27 ms 
[INFO ] 2024-07-15 19:09:56.373 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:09:56.373 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2e0c3c0a 
[INFO ] 2024-07-15 19:09:56.373 - [Heartbeat-Mongo] - Stop task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo)  
[INFO ] 2024-07-15 19:09:56.515 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:09:56.515 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:09:56.557 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:09:56.559 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:17:56.844 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 19:17:56.925 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:17:56.927 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:17:56.971 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:17:56.971 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:17:56.971 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:17:56.971 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:17:57.783 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 19:17:57.784 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 19:17:57.784 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 19:17:57.792 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721042277784,"lastTimes":1721042277784,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-15 19:17:57.860 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:17:57.864 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-15 19:17:57.900 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-15 19:17:57.900 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-15 19:17:57.918 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-15 19:17:57.925 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 19:17:57.926 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721042277784,"lastTimes":1721042277784,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-15 19:17:57.929 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 19:17:57.929 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[WARN ] 2024-07-15 19:17:58.137 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721042277, "i": 42}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721042277, "i": 42}}, "signature": {"hash": {"$binary": {"base64": "30RW63aZ+F84IgftV0j/DeH8oAE=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-15 19:18:38.625 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:18:38.630 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 19:18:38.641 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:18:38.641 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:18:38.641 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 19:18:38.642 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 19:18:38.644 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 73 ms 
[INFO ] 2024-07-15 19:18:38.664 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 19:18:38.665 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:18:38.665 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:18:38.667 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 19:18:38.668 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 19:18:38.873 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 24 ms 
[INFO ] 2024-07-15 19:18:42.090 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:18:42.090 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@358bc5f3 
[INFO ] 2024-07-15 19:18:42.090 - [Heartbeat-Mongo] - Stop task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo)  
[INFO ] 2024-07-15 19:18:42.234 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:18:42.234 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:18:42.284 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:18:42.286 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:29:01.495 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 19:29:01.534 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:29:01.534 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:29:01.589 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:29:01.589 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:29:01.589 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:29:01.589 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:29:01.663 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 19:29:01.663 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 19:29:01.664 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:29:01.755 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721042277898,"lastTimes":1721042277901,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721042318057,"lastTN":42,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":41,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:29:01.758 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721042318057,"lastTN":42,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":41,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:29:01.759 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 19:29:01.759 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:29:01.866 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:29:08.815 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:29:08.817 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-15 19:29:08.828 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:29:08.829 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:29:08.834 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 19:29:08.839 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 19:34:03.611 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 44 ms 
[INFO ] 2024-07-15 19:34:03.615 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 19:34:03.743 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 19:34:04.674 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:34:04.876 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:34:05.401 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:34:05.402 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:34:05.402 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:34:05.402 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:34:05.813 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 19:34:05.825 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 19:34:05.836 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:34:05.958 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721042277898,"lastTimes":1721042277901,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721042318057,"lastTN":42,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":41,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:34:05.962 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721042318057,"lastTN":42,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":41,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:34:05.980 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 19:34:05.981 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:34:06.389 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:38:17.801 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:38:17.810 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 19:38:17.829 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:38:17.830 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:38:17.832 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 19:38:17.834 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 19:38:17.841 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 46 ms 
[INFO ] 2024-07-15 19:38:17.841 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 19:38:17.894 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:38:17.894 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:38:17.897 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 19:38:17.899 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 19:38:17.900 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 56 ms 
[INFO ] 2024-07-15 19:38:19.217 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:38:19.219 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@de2ba3c 
[INFO ] 2024-07-15 19:38:19.343 - [Heartbeat-Mongo] - Stop task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo)  
[INFO ] 2024-07-15 19:38:19.344 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:38:19.398 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:38:19.398 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:38:19.604 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:42:21.255 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 19:42:21.559 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:42:21.724 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:42:21.786 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:42:21.787 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:42:21.791 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:42:21.791 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:42:21.925 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 19:42:21.926 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 19:42:21.929 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:42:21.930 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721043497785,"lastTN":252,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":293,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:42:22.044 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721043497785,"lastTN":252,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":293,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:42:22.044 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 19:42:22.057 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:42:22.057 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:43:50.837 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:43:50.838 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 19:43:50.847 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:43:50.849 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 19:43:50.849 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 19:43:50.850 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 19:43:50.856 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 35 ms 
[INFO ] 2024-07-15 19:43:50.856 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 19:43:50.957 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:43:50.960 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 19:43:50.960 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 19:43:50.961 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 19:43:50.963 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 108 ms 
[INFO ] 2024-07-15 19:43:51.827 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:43:51.934 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@40948c39 
[INFO ] 2024-07-15 19:43:51.934 - [Heartbeat-Mongo] - Stop task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo)  
[INFO ] 2024-07-15 19:43:51.964 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:43:51.964 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:43:51.995 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:43:51.995 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[66949fb21df4b966216a4f31] 
[INFO ] 2024-07-15 19:44:10.821 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 19:44:10.903 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:44:11.008 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:44:11.008 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:44:11.008 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:44:11.008 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:44:11.008 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:44:11.059 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 19:44:11.059 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 19:44:11.059 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:44:11.062 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721043830304,"lastTN":89,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":382,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:44:11.132 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721043830304,"lastTN":89,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":382,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 19:44:11.133 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 19:44:11.224 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:44:11.224 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:53:10.511 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 19:53:10.511 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 20:01:36.319 - [Heartbeat-Mongo] - Start task milestones: 66949fb21df4b966216a4f31(Heartbeat-Mongo) 
[INFO ] 2024-07-15 20:01:36.321 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:01:36.365 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 20:01:36.418 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 20:01:36.418 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] start preload schema,table counts: 1 
[INFO ] 2024-07-15 20:01:36.419 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:01:36.420 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:01:36.569 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-15 20:01:36.569 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-15 20:01:36.583 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 20:01:36.584 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721044383657,"lastTN":532,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":914,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 20:01:36.667 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 20:01:36.673 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721042277784,"lastTimes":1721044383657,"lastTN":532,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":914,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-15 20:01:36.674 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-15 20:01:36.880 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 20:22:55.694 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] running status set to false 
[INFO ] 2024-07-15 20:22:55.694 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-15 20:22:55.725 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 20:22:55.726 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-86150066-df81-4a0d-b9e0-faee35eb6548 
[INFO ] 2024-07-15 20:22:55.727 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] schema data cleaned 
[INFO ] 2024-07-15 20:22:55.728 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] monitor closed 
[INFO ] 2024-07-15 20:22:55.731 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[86150066-df81-4a0d-b9e0-faee35eb6548] close complete, cost 80 ms 
[INFO ] 2024-07-15 20:22:55.840 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] running status set to false 
[INFO ] 2024-07-15 20:22:55.892 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 20:22:55.893 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-85a6bc63-f8fa-4f22-9425-53b99f76d7f1 
[INFO ] 2024-07-15 20:22:55.893 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] schema data cleaned 
[INFO ] 2024-07-15 20:22:55.894 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] monitor closed 
[INFO ] 2024-07-15 20:22:56.096 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[85a6bc63-f8fa-4f22-9425-53b99f76d7f1] close complete, cost 162 ms 
