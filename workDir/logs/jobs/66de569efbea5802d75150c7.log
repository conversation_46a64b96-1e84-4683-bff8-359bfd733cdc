[INFO ] 2024-09-18 01:48:33.263 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 01:48:33.296 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 01:48:33.455 - [本地测试主从Mysql-3306的mysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-18 01:48:33.455 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 01:48:33.512 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 01:48:33.512 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 01:48:33.522 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 01:48:33.525 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 01:48:55.686 - [本地测试主从Mysql-3306的mysql][localmaster] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@101ababf failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 
[ERROR] 2024-09-18 01:48:55.835 - [本地测试主从Mysql-3306的mysql][localmaster] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@101ababf failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@101ababf failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more


<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@101ababf failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@101ababf failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[INFO ] 2024-09-18 01:48:55.894 - [本地测试主从Mysql-3306的mysql][localmaster] - Job suspend in error handle 
[INFO ] 2024-09-18 01:48:55.895 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 01:48:55.899 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: null 
[INFO ] 2024-09-18 01:48:55.899 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: null 
[INFO ] 2024-09-18 01:48:55.900 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 01:48:55.907 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 01:48:55.908 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 12 ms 
[INFO ] 2024-09-18 01:48:55.943 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 01:48:55.944 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 01:48:55.944 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 01:48:55.944 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 01:48:55.944 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 01:48:55.990 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 34 ms 
[INFO ] 2024-09-18 01:48:55.990 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 01:48:55.997 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 01:48:56.003 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@6d4d72ad failed, java.lang.RuntimeException: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2063)
	at java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:830)
	at java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:326)
	at java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:597)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:178)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 
[ERROR] 2024-09-18 01:48:56.206 - [本地测试主从Mysql-3306的mysql][DummyTarget] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@6d4d72ad failed, java.lang.RuntimeException: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2063)
	at java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:830)
	at java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:326)
	at java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:597)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:178)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@6d4d72ad failed, java.lang.RuntimeException: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2063)
	at java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:830)
	at java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:326)
	at java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:597)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:178)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more


<-- Simple Stack Trace -->
Caused by: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2063)
	java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:830)
	java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:326)
	java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:597)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:178)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@6d4d72ad failed, java.lang.RuntimeException: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2063)
	at java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:830)
	at java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:326)
	at java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:597)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:178)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:109)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@6d4d72ad failed, java.lang.RuntimeException: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2063)
	at java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:830)
	at java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:326)
	at java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:597)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:178)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@5e44483d rejected from java.util.concurrent.ScheduledThreadPoolExecutor@5716ec16[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0]
	at java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2063)
	at java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:830)
	at java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:326)
	at java.util.concurrent.ScheduledThreadPoolExecutor.scheduleWithFixedDelay(ScheduledThreadPoolExecutor.java:597)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:178)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

[INFO ] 2024-09-18 01:48:57.921 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 01:48:57.922 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 01:48:57.954 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 01:48:58.015 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 01:48:58.017 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 01:48:58.017 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 01:50:40.312 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 01:50:40.313 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 01:50:40.518 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 01:50:40.592 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 01:50:40.592 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 01:50:40.597 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 01:50:40.597 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 01:50:40.803 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 01:55:40.873 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@689b15f7 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[INFO ] 2024-09-18 01:55:40.878 - [本地测试主从Mysql-3306的mysql][localmaster] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@4ebcc986 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[ERROR] 2024-09-18 01:55:40.881 - [本地测试主从Mysql-3306的mysql][localmaster] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@4ebcc986 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@4ebcc986 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@4ebcc986 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3009/1445318790@4ebcc986 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	... 15 more

[ERROR] 2024-09-18 01:55:40.882 - [本地测试主从Mysql-3306的mysql][DummyTarget] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@689b15f7 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@689b15f7 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@689b15f7 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:109)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3007/663867489@689b15f7 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:70)
	... 15 more

[INFO ] 2024-09-18 01:55:40.951 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 01:55:40.952 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: null 
[INFO ] 2024-09-18 01:55:40.952 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: null 
[INFO ] 2024-09-18 01:55:40.952 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 01:55:40.952 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 01:55:40.954 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 5 ms 
[INFO ] 2024-09-18 01:55:40.954 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 01:55:40.955 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 01:55:40.955 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 01:55:40.956 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 01:55:40.956 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 01:55:40.996 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 2 ms 
[INFO ] 2024-09-18 01:58:50.727 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 01:58:50.745 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 01:58:52.540 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 01:58:52.890 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 01:58:53.705 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 01:58:53.733 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 01:58:53.734 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 01:58:53.744 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 01:59:08.780 - [本地测试主从Mysql-3306的mysql][localmaster] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3099/1076194725@19582b21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "JGzqSE66gQ21fSpnyhE0hn0CcjY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 
[INFO ] 2024-09-18 01:59:08.814 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b 
[ERROR] 2024-09-18 01:59:08.818 - [本地测试主从Mysql-3306的mysql][localmaster] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3099/1076194725@19582b21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "JGzqSE66gQ21fSpnyhE0hn0CcjY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3099/1076194725@19582b21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "JGzqSE66gQ21fSpnyhE0hn0CcjY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "JGzqSE66gQ21fSpnyhE0hn0CcjY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3099/1076194725@19582b21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "JGzqSE66gQ21fSpnyhE0hn0CcjY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3099/1076194725@19582b21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "JGzqSE66gQ21fSpnyhE0hn0CcjY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "JGzqSE66gQ21fSpnyhE0hn0CcjY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726595948, "i" : 41 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

[ERROR] 2024-09-18 01:59:08.820 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b <-- Error Message -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more

[INFO ] 2024-09-18 01:59:08.829 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Job suspend in error handle 
[INFO ] 2024-09-18 01:59:08.832 - [本地测试主从Mysql-3306的mysql][localmaster] - Job suspend in error handle 
[INFO ] 2024-09-18 01:59:08.855 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 01:59:08.860 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: null 
[INFO ] 2024-09-18 01:59:08.860 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: null 
[INFO ] 2024-09-18 01:59:08.865 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 01:59:08.866 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 01:59:08.867 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 17 ms 
[INFO ] 2024-09-18 01:59:08.879 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 01:59:08.889 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 01:59:08.916 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 01:59:08.917 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 01:59:08.919 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 01:59:08.920 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 49 ms 
[INFO ] 2024-09-18 01:59:10.222 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 01:59:10.222 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 01:59:10.239 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 01:59:10.239 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 01:59:10.259 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 01:59:10.261 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:10:11.520 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 02:10:11.664 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 02:10:12.751 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:10:12.825 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:10:13.378 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:10:13.379 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:10:13.383 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:10:13.383 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:10:15.400 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 02:10:15.412 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-09-18 02:10:16.034 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726596615991} and {hostPort=localhost:43306, time=1726567815985} 
[INFO ] 2024-09-18 02:10:16.049 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 02:10:16.052 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 02:10:16.053 - [本地测试主从Mysql-3306的mysql][localmaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-18 02:10:16.081 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000023","position":455,"gtidSet":""} 
[INFO ] 2024-09-18 02:10:16.082 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 02:10:16.162 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync started 
[INFO ] 2024-09-18 02:10:16.167 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting batch read, table name: t2, offset: null 
[INFO ] 2024-09-18 02:10:16.183 - [本地测试主从Mysql-3306的mysql][localmaster] - Table t2 is going to be initial synced 
[INFO ] 2024-09-18 02:10:16.237 - [本地测试主从Mysql-3306的mysql][localmaster] - Query table 't2' counts: 506000 
[INFO ] 2024-09-18 02:10:24.052 - [本地测试主从Mysql-3306的mysql][localmaster] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-18 02:10:24.058 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 02:10:24.059 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 02:10:24.060 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 02:10:24.148 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"filename":"mysql-bin.000023","position":455,"gtidSet":""} 
[INFO ] 2024-09-18 02:10:24.148 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 02:10:24.201 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 119237228
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 02:10:24.300 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[WARN ] 2024-09-18 02:16:15.901 - [本地测试主从Mysql-3306的mysql] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=1a9d7bc956c847b58c169d84b80de6bdbdc313ab1be84dcaafecc46f34ff2021&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266de569efbea5802d75150c7%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@5a8bcf19, CDC=io.tapdata.milestone.entity.MilestoneEntity@46a9337d, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@6b9b715f, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@3e53d382, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@3aa43056, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@3599cea4}, attrs.nodeMilestones={8eec7d28-ce15-4c24-a832-d88310700e5b={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@5b840c1, NODE=io.tapdata.milestone.entity.MilestoneEntity@25575c37, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@3eeaee4b, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@457debdc}, e653b64c-e17c-46a2-ae89-45d1d1817223={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@28415fa, NODE=io.tapdata.milestone.entity.MilestoneEntity@1d2d4779, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@74dc6a21, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5e554c6d}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=b26658dc-02cd-4954-be11-ffeaaf79b59a}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[WARN ] 2024-09-18 02:16:15.902 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Save to snapshot failed, collection: Task/syncProgress/66de569efbea5802d75150c7, object: {e653b64c-e17c-46a2-ae89-45d1d1817223,8eec7d28-ce15-4c24-a832-d88310700e5b=SyncProgress{eventSerialNo=516, syncStage='CDC', batchOffset='{t2={batch_read_connector_offset={}, batch_read_connector_status=RUNNING}}', streamOffset='MysqlStreamOffset{name='91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d', offset={{"server":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d"}={"ts_sec":1726596624,"file":"mysql-bin.000023","pos":455,"server_id":1}}}'}}, errors: RestException{uri='http://localhost:3000/api/Task/syncProgress/66de569efbea5802d75150c7?access_token=1a9d7bc956c847b58c169d84b80de6bdbdc313ab1be84dcaafecc46f34ff2021', method='POST', param={["e653b64c-e17c-46a2-ae89-45d1d1817223","8eec7d28-ce15-4c24-a832-d88310700e5b"]={"offset":null,"eventTime":1726596939450,"eventSerialNo":516,"sourceTime":1726596939450,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcAEUAAJ0MgFkABFqYXZhLnV0aWwuSGFzaE1hcAEUABtiYXRj\r\naF9yZWFkX2Nvbm5lY3Rvcl9vZmZzZXQBZAARamF2YS51dGlsLkhhc2hNYXCoARQAG2JhdGNoX3Jl\r\nYWRfY29ubmVjdG9yX3N0YXR1cwEUAAdSVU5OSU5HqKg=\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-QKo£0\u0010¾#ñ_\f)+qèa\u0000f±S\b\r¶o<`ÀI[ðøõe³Ã4ï¥9ÃÖùÙ!p\u0018Á£_ål!Î³´\u0015}Êêù&}TÒº\u001D\u0019J×êËKúUÝ=cýRåSVQÏsãC×\u0012`©¼Î\u001BV\u00106g\fÐà\u0004VSæU&\"òM¾·\u0007[Ø ¨Rý],7\u0002 \u001E/<\u0002°®½.:è\u0018\u0019MåÝÞª0Èë&Ñ.d3ÑQ\u0013ßh@ÝÞ¼õU%o|Zêù=Q[$B:ë\u001A8þ7g¢/\u0007Täã}\u001FøK±]Yá34ÐóÂ\bnz¿e`7b+gò®\u0003¥×J\u001D\"°\u0007\u00116£®}¬\u001Eÿ¿q\u0013ÿkN\r0\u001DçhM\u0011a¹Ø\"97EV-ÄE¦ðj\u0019giK³îÆ\u0015DH×v\u0001ùL\"\u001A\u001A$òÄ\u0012H7¬CzªÌCÛëD\u0017l®(¶\u0018Ån¤|ê×½,_\u0018W¶þ\u001E·°z#\u001EÌdÆ\u0003¾8jÆ¨÷i¬§èTäÜµ¾}÷w]û\u0005üÀÓ¿\u0002\u0002\u0000\u0000","type":"NORMAL"}}, code='SystemError', data=null, reqId=60a7171e-52e1-4f19-a246-a7b78c25df81}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:16:16.289 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 02:16:16.324 - [本地测试主从Mysql-3306的mysql][localmaster] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-18 02:16:16.327 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 02:16:16.328 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 02:16:16.328 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 02:16:16.329 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 02:16:16.333 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 78 ms 
[INFO ] 2024-09-18 02:16:16.334 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[WARN ] 2024-09-18 02:16:16.388 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Save to snapshot failed, collection: Task/syncProgress/66de569efbea5802d75150c7, object: {e653b64c-e17c-46a2-ae89-45d1d1817223,8eec7d28-ce15-4c24-a832-d88310700e5b=SyncProgress{eventSerialNo=516, syncStage='CDC', batchOffset='{t2={batch_read_connector_offset={}, batch_read_connector_status=RUNNING}}', streamOffset='MysqlStreamOffset{name='91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d', offset={{"server":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d"}={"ts_sec":1726596624,"file":"mysql-bin.000023","pos":455,"server_id":1}}}'}}, errors: RestException{uri='http://localhost:3000/api/Task/syncProgress/66de569efbea5802d75150c7?access_token=1a9d7bc956c847b58c169d84b80de6bdbdc313ab1be84dcaafecc46f34ff2021', method='POST', param={["e653b64c-e17c-46a2-ae89-45d1d1817223","8eec7d28-ce15-4c24-a832-d88310700e5b"]={"offset":null,"eventTime":1726596939450,"eventSerialNo":516,"sourceTime":1726596939450,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcAEUAAJ0MgFkABFqYXZhLnV0aWwuSGFzaE1hcAEUABtiYXRj\r\naF9yZWFkX2Nvbm5lY3Rvcl9vZmZzZXQBZAARamF2YS51dGlsLkhhc2hNYXCoARQAG2JhdGNoX3Jl\r\nYWRfY29ubmVjdG9yX3N0YXR1cwEUAAdSVU5OSU5HqKg=\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-QKo£0\u0010¾#ñ_\f)+qèa\u0000f±S\b\r¶o<`ÀI[ðøõe³Ã4ï¥9ÃÖùÙ!p\u0018Á£_ål!Î³´\u0015}Êêù&}TÒº\u001D\u0019J×êËKúUÝ=cýRåSVQÏsãC×\u0012`©¼Î\u001BV\u00106g\fÐà\u0004VSæU&\"òM¾·\u0007[Ø ¨Rý],7\u0002 \u001E/<\u0002°®½.:è\u0018\u0019MåÝÞª0Èë&Ñ.d3ÑQ\u0013ßh@ÝÞ¼õU%o|Zêù=Q[$B:ë\u001A8þ7g¢/\u0007Täã}\u001FøK±]Yá34ÐóÂ\bnz¿e`7b+gò®\u0003¥×J\u001D\"°\u0007\u00116£®}¬\u001Eÿ¿q\u0013ÿkN\r0\u001DçhM\u0011a¹Ø\"97EV-ÄE¦ðj\u0019giK³îÆ\u0015DH×v\u0001ùL\"\u001A\u001A$òÄ\u0012H7¬CzªÌCÛëD\u0017l®(¶\u0018Ån¤|ê×½,_\u0018W¶þ\u001E·°z#\u001EÌdÆ\u0003¾8jÆ¨÷i¬§èTäÜµ¾}÷w]û\u0005üÀÓ¿\u0002\u0002\u0000\u0000","type":"NORMAL"}}, code='SystemError', data=null, reqId=185694be-a26e-4ff4-b9ac-e31aa56d6629}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:16:16.389 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 02:16:16.397 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 02:16:16.399 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 02:16:16.405 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 02:16:16.405 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 02:16:16.411 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 76 ms 
[INFO ] 2024-09-18 02:16:20.880 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:16:20.880 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[WARN ] 2024-09-18 02:16:20.927 - [本地测试主从Mysql-3306的mysql] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=1a9d7bc956c847b58c169d84b80de6bdbdc313ab1be84dcaafecc46f34ff2021&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266de569efbea5802d75150c7%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@5a8bcf19, CDC=io.tapdata.milestone.entity.MilestoneEntity@46a9337d, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@6b9b715f, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@3e53d382, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@3aa43056, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@3599cea4}, attrs.nodeMilestones={8eec7d28-ce15-4c24-a832-d88310700e5b={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@5b840c1, NODE=io.tapdata.milestone.entity.MilestoneEntity@25575c37, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@3eeaee4b, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@457debdc}, e653b64c-e17c-46a2-ae89-45d1d1817223={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@28415fa, NODE=io.tapdata.milestone.entity.MilestoneEntity@1d2d4779, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@74dc6a21, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5e554c6d}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=21d529ab-7548-45ea-8a06-9050812cdc21}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:16:20.928 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:16:20.928 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:16:20.929 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:16:20.929 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:16:20.951 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:19:56.625 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 02:19:56.625 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 02:19:56.740 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:19:56.740 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:19:56.776 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:19:56.777 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:19:56.777 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:19:56.777 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:20:06.321 - [本地测试主从Mysql-3306的mysql][localmaster] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 
[WARN ] 2024-09-18 02:20:06.322 - [本地测试主从Mysql-3306的mysql] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266de569efbea5802d75150c7%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@1ce805b, CDC=io.tapdata.milestone.entity.MilestoneEntity@5f104ba, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@40f38a60, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@440ca8b, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@71f94e00, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@7ca05051}, attrs.nodeMilestones={8eec7d28-ce15-4c24-a832-d88310700e5b={NODE=io.tapdata.milestone.entity.MilestoneEntity@20dc752f}, e653b64c-e17c-46a2-ae89-45d1d1817223={NODE=io.tapdata.milestone.entity.MilestoneEntity@224ae72d}}, syncStatus=DATA_NODE_INIT}}}}, code='SystemError', data=null, reqId=79c13b60-d721-4e70-87e1-f012c99b34b8}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[WARN ] 2024-09-18 02:20:06.358 - [本地测试主从Mysql-3306的mysql][localmaster] - Save error event failed: RestException{uri='http://localhost:3000/api/Task/errorEvents/66de569efbea5802d75150c7?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18', method='POST', param=[ErrorEvent(id=66e9c856a1c26e62b12551ab, message=code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
, code=11001, stacks=code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
, skip=false)], code='SystemError', data=null, reqId=7dd188c9-dcc1-46d0-b855-f9a3b870e087}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[ERROR] 2024-09-18 02:20:06.455 - [本地测试主从Mysql-3306的mysql][localmaster] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more


<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3006/1188052768@1f0d43fc failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[INFO ] 2024-09-18 02:20:06.456 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more
 
[WARN ] 2024-09-18 02:20:06.577 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Save error event failed: RestException{uri='http://localhost:3000/api/Task/errorEvents/66de569efbea5802d75150c7?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18', method='POST', param=[ErrorEvent(id=66e9c856a1c26e62b12551ac, message=code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more
, code=15001, stacks=code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:109)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more
, skip=false)], code='SystemError', data=null, reqId=76f5b5f1-14e3-494b-af4a-ee140dbd5392}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[ERROR] 2024-09-18 02:20:06.579 - [本地测试主从Mysql-3306的mysql][DummyTarget] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more


<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:109)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$3008/1646476791@ba5ea21 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:169)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: dummy-io.tapdata-1.0-SNAPSHOT-public, message: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:357)
	at io.tapdata.flow.engine.V2.util.PdkUtil.reDownloadIfNeed(PdkUtil.java:111)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:99)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:58)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:197)
	... 10 more
Caused by: RestException{uri='http://localhost:3000/api/pdk/checkMd5/v2?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&pdkHash=f45ed8744261175abce5f902473472866aa1ffc40cccec0a0d2abceef4a338c2&pdkBuildNumber=17', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=be0e9222-410e-42d6-8fdf-5f041d6c64e4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}
	at com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	at com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	at com.tapdata.mongo.HttpClientMongoOperator.findOne(HttpClientMongoOperator.java:355)
	... 14 more

[INFO ] 2024-09-18 02:20:06.581 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 02:20:06.581 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:20:06.581 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: null 
[INFO ] 2024-09-18 02:20:06.581 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 02:20:06.583 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 02:20:06.603 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 16 ms 
[INFO ] 2024-09-18 02:20:06.606 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 02:20:06.623 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:20:06.627 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 02:20:06.627 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 02:20:06.627 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 02:20:06.641 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 33 ms 
[INFO ] 2024-09-18 02:20:11.183 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:20:11.184 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[WARN ] 2024-09-18 02:20:11.229 - [本地测试主从Mysql-3306的mysql] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=668df508fafb456d8bf080030d325f9c9cde0c2b80834a078dacae992cd80d18&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266de569efbea5802d75150c7%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@1ce805b, CDC=io.tapdata.milestone.entity.MilestoneEntity@5f104ba, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@40f38a60, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@440ca8b, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@71f94e00, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@7ca05051}, attrs.nodeMilestones={8eec7d28-ce15-4c24-a832-d88310700e5b={NODE=io.tapdata.milestone.entity.MilestoneEntity@20dc752f}, e653b64c-e17c-46a2-ae89-45d1d1817223={NODE=io.tapdata.milestone.entity.MilestoneEntity@224ae72d}}, syncStatus=DATA_NODE_INIT}}}}, code='SystemError', data=null, reqId=5369f887-7121-4626-8512-679b4c7b6cef}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:20:11.230 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:20:11.230 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:20:11.288 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:20:11.289 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:20:11.289 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:20:11.290 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:25:00.390 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 02:25:04.543 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 02:25:04.708 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:25:04.710 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:25:04.782 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:25:04.783 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:25:04.783 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:25:04.783 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:25:56.872 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b 
[ERROR] 2024-09-18 02:25:56.943 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b <-- Error Message -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597521, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "Mm2LbezJvRtKGMLyRNcIB0eQ9IY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597521, "i" : 2 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.internal.session.ServerSessionPool.endClosedSessions(ServerSessionPool.java:141)
	at com.mongodb.internal.session.ServerSessionPool.close(ServerSessionPool.java:97)
	at com.mongodb.client.internal.MongoClientDelegate.close(MongoClientDelegate.java:123)
	at com.mongodb.client.internal.MongoClientImpl.close(MongoClientImpl.java:128)
	at com.hazelcast.persistence.resource.impl.MongoDBGlobalResource$MongoClientHolder.close(MongoDBGlobalResource.java:326)
	at com.hazelcast.persistence.resource.impl.MongoDBGlobalResource$MongoClientPartition.getMongoClientWithPartition(MongoDBGlobalResource.java:169)
	at com.hazelcast.persistence.resource.impl.MongoDBGlobalResource.getMongoClient(MongoDBGlobalResource.java:57)
	at com.hazelcast.persistence.resource.impl.MongoDBResource.doInit(MongoDBResource.java:45)
	at com.hazelcast.persistence.resource.impl.MongoDBResource.doInit(MongoDBResource.java:19)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:279)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597521, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "Mm2LbezJvRtKGMLyRNcIB0eQ9IY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597521, "i" : 2 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 41 more

[INFO ] 2024-09-18 02:25:57.032 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Job suspend in error handle 
[WARN ] 2024-09-18 02:26:08.620 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726597567998} and {hostPort=localhost:43306, time=1726568767996} 
[INFO ] 2024-09-18 02:26:08.652 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 02:26:08.652 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 02:26:08.653 - [本地测试主从Mysql-3306的mysql][localmaster] - Sync progress not exists, will run task as first time 
[INFO ] 2024-09-18 02:26:08.654 - [本地测试主从Mysql-3306的mysql][localmaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-18 02:26:08.659 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000023","position":455,"gtidSet":""} 
[INFO ] 2024-09-18 02:26:08.659 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 02:26:08.707 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 02:26:08.707 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync completed 
[INFO ] 2024-09-18 02:26:08.809 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 02:26:08.809 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 02:26:08.809 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 02:26:08.809 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 02:26:08.809 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 02:26:08.814 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 106 ms 
[INFO ] 2024-09-18 02:26:08.814 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 02:26:08.820 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:26:08.820 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 02:26:08.821 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 02:26:08.821 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 02:26:08.930 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 8 ms 
[INFO ] 2024-09-18 02:26:14.947 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:26:14.952 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 02:26:15.202 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:26:15.203 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:26:15.263 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:26:15.276 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:31:01.498 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 02:31:01.498 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 02:31:02.735 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:31:02.887 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:31:03.872 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:03.872 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:03.874 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:31:03.874 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:31:04.320 - [本地测试主从Mysql-3306的mysql][localmaster] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@5068845e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } }, "signature" : { "hash" : { "$binary" : "Y8EthSxEYQ2dj4xIj+yXjvXrbM0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 
[ERROR] 2024-09-18 02:31:04.356 - [本地测试主从Mysql-3306的mysql][localmaster] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@5068845e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } }, "signature" : { "hash" : { "$binary" : "Y8EthSxEYQ2dj4xIj+yXjvXrbM0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@5068845e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } }, "signature" : { "hash" : { "$binary" : "Y8EthSxEYQ2dj4xIj+yXjvXrbM0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } }, "signature" : { "hash" : { "$binary" : "Y8EthSxEYQ2dj4xIj+yXjvXrbM0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@5068845e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } }, "signature" : { "hash" : { "$binary" : "Y8EthSxEYQ2dj4xIj+yXjvXrbM0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@5068845e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } }, "signature" : { "hash" : { "$binary" : "Y8EthSxEYQ2dj4xIj+yXjvXrbM0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } }, "signature" : { "hash" : { "$binary" : "Y8EthSxEYQ2dj4xIj+yXjvXrbM0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597863, "i" : 14 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

[INFO ] 2024-09-18 02:31:04.381 - [本地测试主从Mysql-3306的mysql][localmaster] - Job suspend in error handle 
[INFO ] 2024-09-18 02:31:04.432 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 02:31:04.432 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:31:04.432 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: null 
[INFO ] 2024-09-18 02:31:04.433 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 02:31:04.452 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 02:31:04.492 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 43 ms 
[INFO ] 2024-09-18 02:31:04.493 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 02:31:04.495 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:31:04.495 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 02:31:04.497 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 02:31:04.516 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 02:31:04.517 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 16 ms 
[INFO ] 2024-09-18 02:31:04.611 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b 
[ERROR] 2024-09-18 02:31:04.612 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b <-- Error Message -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b

<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireSharedInterruptibly(AbstractQueuedSynchronizer.java:1306)
	java.util.concurrent.Semaphore.acquire(Semaphore.java:312)
	com.mongodb.internal.connection.ConcurrentPool.acquirePermit(ConcurrentPool.java:199)
	com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:140)
	com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:123)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoInterruptedException: Interrupted acquiring a permit to retrieve an item from the pool 
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoInterruptedException: Interrupted acquiring a permit to retrieve an item from the pool 
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoInterruptedException: Interrupted acquiring a permit to retrieve an item from the pool 
	at com.mongodb.internal.connection.ConcurrentPool.acquirePermit(ConcurrentPool.java:203)
	at com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:140)
	at com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:123)
	at com.mongodb.internal.connection.PowerOfTwoBufferPool.getBuffer(PowerOfTwoBufferPool.java:78)
	at com.mongodb.internal.connection.SocketChannelStream.getBuffer(SocketChannelStream.java:86)
	at com.mongodb.internal.connection.InternalStreamConnection.getBuffer(InternalStreamConnection.java:606)
	at com.mongodb.connection.ByteBufferBsonOutput.getByteBufferAtIndex(ByteBufferBsonOutput.java:93)
	at com.mongodb.connection.ByteBufferBsonOutput.getCurrentByteBuffer(ByteBufferBsonOutput.java:82)
	at com.mongodb.connection.ByteBufferBsonOutput.writeByte(ByteBufferBsonOutput.java:77)
	at org.bson.io.OutputBuffer.write(OutputBuffer.java:150)
	at org.bson.io.OutputBuffer.writeInt32(OutputBuffer.java:56)
	at com.mongodb.internal.connection.RequestMessage.writeMessagePrologue(RequestMessage.java:158)
	at com.mongodb.internal.connection.RequestMessage.encode(RequestMessage.java:137)
	at com.mongodb.internal.connection.CommandMessage.encode(CommandMessage.java:57)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:244)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireSharedInterruptibly(AbstractQueuedSynchronizer.java:1306)
	at java.util.concurrent.Semaphore.acquire(Semaphore.java:312)
	at com.mongodb.internal.connection.ConcurrentPool.acquirePermit(ConcurrentPool.java:199)
	... 65 more

[INFO ] 2024-09-18 02:31:06.268 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:31:06.269 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 02:31:06.281 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:31:06.282 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:31:06.302 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:31:06.306 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:31:34.744 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 02:31:34.748 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 02:31:34.926 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:31:34.979 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:31:35.130 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:35.132 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:35.132 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:31:35.132 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:31:41.260 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 02:31:41.260 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 02:31:41.458 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Sync progress not exists, will run task as first time 
[WARN ] 2024-09-18 02:31:42.510 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726597901996} and {hostPort=localhost:43306, time=1726569101993} 
[INFO ] 2024-09-18 02:31:42.539 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 02:31:42.540 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 02:31:42.540 - [本地测试主从Mysql-3306的mysql][localmaster] - Sync progress not exists, will run task as first time 
[INFO ] 2024-09-18 02:31:42.540 - [本地测试主从Mysql-3306的mysql][localmaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-18 02:31:42.549 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000023","position":455,"gtidSet":""} 
[INFO ] 2024-09-18 02:31:42.551 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 02:31:42.651 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync started 
[INFO ] 2024-09-18 02:31:42.655 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting batch read, table name: t2, offset: null 
[INFO ] 2024-09-18 02:31:42.655 - [本地测试主从Mysql-3306的mysql][localmaster] - Table t2 is going to be initial synced 
[INFO ] 2024-09-18 02:31:42.754 - [本地测试主从Mysql-3306的mysql][localmaster] - Query table 't2' counts: 506000 
[INFO ] 2024-09-18 02:31:54.237 - [本地测试主从Mysql-3306的mysql][localmaster] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-18 02:31:54.237 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 02:31:54.238 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 02:31:54.238 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 02:31:54.267 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"filename":"mysql-bin.000023","position":455,"gtidSet":""} 
[INFO ] 2024-09-18 02:31:54.268 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 02:31:54.330 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2080705206
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 02:31:54.330 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[WARN ] 2024-09-18 02:36:44.145 - [本地测试主从Mysql-3306的mysql] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=4e9057d366d94697b1dc6049f4d9baef7138c099c4a5462ab5b1d2affa8f6be3&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266de569efbea5802d75150c7%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@40e071ae, CDC=io.tapdata.milestone.entity.MilestoneEntity@1b01e700, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@716a3626, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@6361fe22, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@20cf8ee1, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@34e2541f}, attrs.nodeMilestones={8eec7d28-ce15-4c24-a832-d88310700e5b={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@14198fa3, NODE=io.tapdata.milestone.entity.MilestoneEntity@7865f3ec, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@2481d287, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@2e528a4}, e653b64c-e17c-46a2-ae89-45d1d1817223={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5a59cbcc, NODE=io.tapdata.milestone.entity.MilestoneEntity@d74a9f8, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@27cb473b, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@cc0eb6e}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=b2d2b7d4-aebe-4c95-a25c-4c4abecad507}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:36:44.487 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 02:36:44.620 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 02:36:44.638 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 02:36:44.647 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 02:36:44.647 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 02:36:44.653 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 286 ms 
[INFO ] 2024-09-18 02:36:44.654 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[WARN ] 2024-09-18 02:36:44.736 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Save to snapshot failed, collection: Task/syncProgress/66de569efbea5802d75150c7, object: {e653b64c-e17c-46a2-ae89-45d1d1817223,8eec7d28-ce15-4c24-a832-d88310700e5b=SyncProgress{eventSerialNo=514, syncStage='CDC', batchOffset='{t2={batch_read_connector_offset={}, batch_read_connector_status=RUNNING}}', streamOffset='MysqlStreamOffset{name='91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d', offset={{"server":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d"}={"file":"mysql-bin.000023","pos":455,"server_id":1}}}'}}, errors: RestException{uri='http://localhost:3000/api/Task/syncProgress/66de569efbea5802d75150c7?access_token=4e9057d366d94697b1dc6049f4d9baef7138c099c4a5462ab5b1d2affa8f6be3', method='POST', param={["e653b64c-e17c-46a2-ae89-45d1d1817223","8eec7d28-ce15-4c24-a832-d88310700e5b"]={"offset":null,"eventTime":1726598154380,"eventSerialNo":514,"sourceTime":1726598154380,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcAEUAAJ0MgFkABFqYXZhLnV0aWwuSGFzaE1hcAEUABtiYXRj\r\naF9yZWFkX2Nvbm5lY3Rvcl9vZmZzZXQBZAARamF2YS51dGlsLkhhc2hNYXCoARQAG2JhdGNoX3Jl\r\nYWRfY29ubmVjdG9yX3N0YXR1cwEUAAdSVU5OSU5HqKg=\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-I0\u0010ïTñ_\u0004TùC\u0003f\u000B\u0012Ã\u0012tc1\u0002d;\u0001å×\u000FL\u001Fº/ïõ{ß\u0015.Öß\u0018EÉ\u0006\u000EyÔ\u0018µ\u0006FÓ.äUKsdFúj9¿S²Ö¶9Ö·ôÑúÃ3§ãÞkÞúáÈJíMU¸ä;§©¸o\u0006­°Nºk\u00000,`yfWÅ\u0011\u0013âJº:ËÎ\u0013+Í[¤\u0011ÔÈñÉ÷\u0019\u0003ð×·\u001F\u0001±ª|+µÖ\u001B§\bi]ãÌ§ÆO t÷|$;^¢\u000FD\u000Ffâa-Î4ÁzWFyrb{Ø\u0013Ç\u001D±¼ îMUÀrÿ0Ê»èV ª\\çîÕåø\n¿}}ò\u001DW:),`§WøU{ç_x÷E>\f`ÓôÞÈb¯¼óÄýnQ·£ãÿq\rûë\u0006ÿÕ\u0002¬ï[xP:§E`âé<ovl#;­ò´'ù03\u0019¬\\ ¤*±7¶'+ÏÃxø \t`c\u0012\\\u0013Ï\u001FÔÌÆp§¨\f\u001Fµ8-Ø\u0005÷XDv¸Ô\u0006\u0016qh$[D£\u0017\u0007i+êã\u0010ô÷õ^ªÊ'!ßªñæ\u0001\u0000\u0000","type":"NORMAL"}}, code='SystemError', data=null, reqId=5e0bab60-17ef-4974-bc08-1c6a82db5d21}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:36:44.737 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 02:36:44.739 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 02:36:44.740 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 02:36:44.740 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 02:36:44.748 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 02:36:44.748 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 92 ms 
[INFO ] 2024-09-18 02:36:49.078 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:36:49.085 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[WARN ] 2024-09-18 02:36:49.164 - [本地测试主从Mysql-3306的mysql] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=4e9057d366d94697b1dc6049f4d9baef7138c099c4a5462ab5b1d2affa8f6be3&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266de569efbea5802d75150c7%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@40e071ae, CDC=io.tapdata.milestone.entity.MilestoneEntity@1b01e700, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@716a3626, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@6361fe22, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@20cf8ee1, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@34e2541f}, attrs.nodeMilestones={8eec7d28-ce15-4c24-a832-d88310700e5b={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@14198fa3, NODE=io.tapdata.milestone.entity.MilestoneEntity@7865f3ec, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@2481d287, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@2e528a4}, e653b64c-e17c-46a2-ae89-45d1d1817223={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5a59cbcc, NODE=io.tapdata.milestone.entity.MilestoneEntity@d74a9f8, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@27cb473b, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@cc0eb6e}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=c581fef2-06f1-47d1-a5b3-b1df0f8cfd1a}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:36:49.167 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:36:49.168 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:36:49.215 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:36:49.277 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:36:49.278 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 02:36:49.278 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 10:33:24.845 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 10:33:25.203 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 10:33:27.420 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:33:27.786 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:33:28.882 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:33:28.883 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:33:28.886 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:33:28.887 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:33:30.409 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:33:30.410 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-09-18 10:33:32.952 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726626811997} and {hostPort=localhost:43306, time=1726598011994} 
[INFO ] 2024-09-18 10:33:32.953 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 10:33:32.958 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 10:33:32.961 - [本地测试主从Mysql-3306的mysql][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:33:32.964 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {"t2":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:33:32.964 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:33:33.039 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 10:33:33.039 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 10:33:33.039 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:33:33.122 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 10:33:33.157 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1394499657
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:33:33.363 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-18 10:37:42.348 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[WARN ] 2024-09-18 10:37:42.359 - [本地测试主从Mysql-3306的mysql][localmaster] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 10:37:42.360 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 10:37:42.368 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 10:42:46.610 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 10:42:47.202 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 10:42:48.910 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:42:49.316 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:42:50.448 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:42:50.448 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:42:50.449 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:42:50.449 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:42:51.652 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:42:51.652 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-09-18 10:42:59.610 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726627378996} and {hostPort=localhost:43306, time=1726598578984} 
[INFO ] 2024-09-18 10:42:59.610 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 10:42:59.610 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 10:42:59.616 - [本地测试主从Mysql-3306的mysql][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:42:59.617 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:42:59.619 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:42:59.704 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 10:42:59.707 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 10:42:59.715 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:42:59.774 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 10:42:59.838 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1113243126
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:43:00.782 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-18 10:47:38.819 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[WARN ] 2024-09-18 10:47:38.829 - [本地测试主从Mysql-3306的mysql][localmaster] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 10:47:38.829 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 10:47:38.830 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 10:47:38.831 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 10:47:38.838 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 50 ms 
[INFO ] 2024-09-18 10:47:38.845 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 10:52:37.383 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 10:52:37.487 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 10:52:37.489 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 10:52:40.771 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:52:41.287 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:52:42.372 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:52:42.377 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 1 ms 
[INFO ] 2024-09-18 10:52:42.548 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:52:42.551 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:52:44.405 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:52:44.409 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-09-18 10:52:45.913 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726627964979} and {hostPort=localhost:43306, time=1726599164967} 
[INFO ] 2024-09-18 10:52:45.926 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 10:52:45.926 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 10:52:45.928 - [本地测试主从Mysql-3306的mysql][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:52:45.944 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:52:45.944 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:52:46.017 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 10:52:46.017 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 10:52:46.020 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:52:46.210 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 10:52:46.224 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1790093057
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:52:47.049 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-18 10:53:37.579 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[WARN ] 2024-09-18 10:53:37.581 - [本地测试主从Mysql-3306的mysql][localmaster] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 10:53:37.582 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 10:53:37.584 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 10:53:37.586 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 10:58:38.498 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 10:58:38.518 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 10:58:39.957 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:58:40.142 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:58:41.319 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:58:41.320 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:58:41.324 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 2 ms 
[INFO ] 2024-09-18 10:58:41.325 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[WARN ] 2024-09-18 10:58:43.369 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726628322987} and {hostPort=localhost:43306, time=1726599522983} 
[INFO ] 2024-09-18 10:58:43.433 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 10:58:43.434 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 10:58:43.434 - [本地测试主从Mysql-3306的mysql][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:58:43.434 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:58:43.434 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:58:43.481 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:58:43.496 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 10:58:43.548 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 10:58:43.582 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 10:58:43.603 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:58:43.648 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 10:58:43.730 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1363101815
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:58:45.599 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-18 11:16:38.793 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 11:16:38.798 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 11:16:40.687 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 11:16:40.954 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 11:16:41.979 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:16:41.985 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:16:41.998 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:16:42.000 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:16:44.080 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 11:16:44.080 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-09-18 11:16:44.478 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726629403993} and {hostPort=localhost:43306, time=1726600603989} 
[INFO ] 2024-09-18 11:16:44.497 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 11:16:44.497 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 11:16:44.500 - [本地测试主从Mysql-3306的mysql][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 11:16:44.502 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 11:16:44.502 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 11:16:44.579 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 11:16:44.579 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 11:16:44.581 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 11:16:44.694 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 11:16:44.695 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 156095106
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 11:16:45.941 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-18 11:18:58.583 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 11:18:58.765 - [本地测试主从Mysql-3306的mysql][localmaster] - Mysql binlog reader stopped 
[INFO ] 2024-09-18 11:18:58.769 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync completed 
[INFO ] 2024-09-18 11:18:58.832 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 11:18:58.832 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 11:18:58.848 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 11:18:58.865 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 11:18:58.877 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 303 ms 
[INFO ] 2024-09-18 11:18:58.877 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[WARN ] 2024-09-18 11:18:58.976 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Save to snapshot failed, collection: Task/syncProgress/66de569efbea5802d75150c7, object: {e653b64c-e17c-46a2-ae89-45d1d1817223,8eec7d28-ce15-4c24-a832-d88310700e5b=SyncProgress{eventSerialNo=4, syncStage='CDC', batchOffset='{}', streamOffset='MysqlStreamOffset{name='91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d', offset={{"server":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d"}={"file":"mysql-bin.000023","pos":455,"server_id":1}}}'}}, errors: RestException{uri='http://localhost:3000/api/Task/syncProgress/66de569efbea5802d75150c7?access_token=1129302f8a914f148e5cce80b62d947b2d81f08746c74d53aa41c2d927ee5178', method='POST', param={["e653b64c-e17c-46a2-ae89-45d1d1817223","8eec7d28-ce15-4c24-a832-d88310700e5b"]={"offset":null,"eventTime":1726629501967,"eventSerialNo":4,"sourceTime":1726629501967,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcKg=\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-I0\u0010ïTñ_\u0004TùC\u0003f\u000B\u0012Ã\u0012tc1\u0002d;\u0001å×\u000FL\u001Fº/ïõ{ß\u0015.Öß\u0018EÉ\u0006\u000EyÔ\u0018µ\u0006FÓ.äUKsdFúj9¿S²Ö¶9Ö·ôÑúÃ3§ãÞkÞúáÈJíMU¸ä;§©¸o\u0006­°Nºk\u00000,`yfWÅ\u0011\u0013âJº:ËÎ\u0013+Í[¤\u0011ÔÈñÉ÷\u0019\u0003ð×·\u001F\u0001±ª|+µÖ\u001B§\bi]ãÌ§ÆO t÷|$;^¢\u000FD\u000Ffâa-Î4ÁzWFyrb{Ø\u0013Ç\u001D±¼ îMUÀrÿ0Ê»èV ª\\çîÕåø\n¿}}ò\u001DW:),`§WøU{ç_x÷E>\f`ÓôÞÈb¯¼óÄýnQ·£ãÿq\rûë\u0006ÿÕ\u0002¬ï[xP:§E`âé<ovl#;­ò´'ù03\u0019¬\\ ¤*±7¶'+ÏÃxø \t`c\u0012\\\u0013Ï\u001FÔÌÆp§¨\f\u001Fµ8-Ø\u0005÷XDv¸Ô\u0006\u0016qh$[D£\u0017\u0007i+êã\u0010ô÷õ^ªÊ'!ßªñæ\u0001\u0000\u0000","type":"NORMAL"}}, code='SystemError', data=null, reqId=85e9acb6-b3c3-42e2-a1fc-8f79afe9b8d4}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 11:18:58.992 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 11:18:59.063 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 11:18:59.064 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 11:18:59.065 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 11:18:59.073 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 11:18:59.078 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 196 ms 
[INFO ] 2024-09-18 11:19:01.888 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 11:19:01.888 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[WARN ] 2024-09-18 11:19:01.944 - [本地测试主从Mysql-3306的mysql] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=1129302f8a914f148e5cce80b62d947b2d81f08746c74d53aa41c2d927ee5178&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266de569efbea5802d75150c7%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@2e137803, CDC=io.tapdata.milestone.entity.MilestoneEntity@79362d60, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@1c70c993, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@78c63a5a, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@4da09621, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@535efd8}, attrs.nodeMilestones={8eec7d28-ce15-4c24-a832-d88310700e5b={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@1f9392bc, NODE=io.tapdata.milestone.entity.MilestoneEntity@564e6ffe, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@1f16fa88, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@35778d9d}, e653b64c-e17c-46a2-ae89-45d1d1817223={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@36f2effd, NODE=io.tapdata.milestone.entity.MilestoneEntity@13664742, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@3b8c2d1c, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@329e4495}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=b4bf03cd-594e-4596-be09-1820aa6f3d86}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 11:19:01.951 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 11:19:01.951 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 11:19:01.952 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:19:01.960 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:23:57.761 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 11:23:57.765 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 11:23:57.939 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 11:23:57.987 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 11:23:58.102 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:23:58.102 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:23:58.103 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:23:58.103 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:23:58.403 - [本地测试主从Mysql-3306的mysql][localmaster] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@7adfb53a failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "drYKtFXk4UsHci/uSmExXFahFrw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 
[INFO ] 2024-09-18 11:23:58.409 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b 
[ERROR] 2024-09-18 11:23:58.421 - [本地测试主从Mysql-3306的mysql][localmaster] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@7adfb53a failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "drYKtFXk4UsHci/uSmExXFahFrw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@7adfb53a failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "drYKtFXk4UsHci/uSmExXFahFrw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "drYKtFXk4UsHci/uSmExXFahFrw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@7adfb53a failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "drYKtFXk4UsHci/uSmExXFahFrw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@7adfb53a failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "drYKtFXk4UsHci/uSmExXFahFrw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "drYKtFXk4UsHci/uSmExXFahFrw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629838, "i" : 5 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

[INFO ] 2024-09-18 11:23:58.429 - [本地测试主从Mysql-3306的mysql][localmaster] - Job suspend in error handle 
[ERROR] 2024-09-18 11:23:58.429 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b <-- Error Message -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_8eec7d28-ce15-4c24-a832-d88310700e5b
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more

[INFO ] 2024-09-18 11:23:58.436 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 11:23:58.440 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: null 
[INFO ] 2024-09-18 11:23:58.440 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: null 
[INFO ] 2024-09-18 11:23:58.440 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 11:23:58.444 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 11:23:58.444 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 5 ms 
[INFO ] 2024-09-18 11:23:58.452 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 11:23:58.452 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 11:23:58.452 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 11:23:58.452 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 11:23:58.452 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 11:23:58.657 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 5 ms 
[INFO ] 2024-09-18 11:24:02.898 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 11:24:02.904 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 11:24:02.918 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 11:24:02.927 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 11:24:02.927 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:24:02.927 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:24:42.100 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 11:24:42.100 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 11:24:42.285 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 11:24:42.360 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 11:24:42.579 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:24:42.579 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:24:42.580 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:24:42.580 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:24:43.112 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 11:24:43.112 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-09-18 11:24:43.535 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726629882997} and {hostPort=localhost:43306, time=1726601082995} 
[INFO ] 2024-09-18 11:24:43.535 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 11:24:43.535 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 11:24:43.535 - [本地测试主从Mysql-3306的mysql][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 11:24:43.536 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 11:24:43.572 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 11:24:43.572 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 11:24:43.572 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 11:24:43.575 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}} 
[INFO ] 2024-09-18 11:24:43.620 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d 
[INFO ] 2024-09-18 11:24:43.620 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 104234936
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.port: 33306
  threadName: Debezium-Mysql-Connector-91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  database.hostname: localhost
  database.password: ********
  name: 91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d
  pdk.offset.string: {"name":"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d","offset":{"{\"server\":\"91f6e730-6d7b-4c59-bb1f-48c2c41e2a4d\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 11:24:43.726 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-18 11:26:29.459 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 11:26:29.860 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 11:26:29.917 - [本地测试主从Mysql-3306的mysql][localmaster] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-18 11:26:29.917 - [本地测试主从Mysql-3306的mysql][localmaster] - Mysql binlog reader stopped 
[INFO ] 2024-09-18 11:26:30.002 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 11:26:30.002 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 11:26:30.003 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 11:26:30.003 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 11:26:30.003 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 157 ms 
[INFO ] 2024-09-18 11:26:30.016 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 11:26:30.016 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 11:26:30.018 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 11:26:30.018 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 11:26:30.018 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 11:26:30.018 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 11:26:30.224 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 15 ms 
[INFO ] 2024-09-18 11:26:32.989 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 11:26:32.989 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 11:26:32.989 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 11:26:33.009 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:26:33.009 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:26:45.771 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 11:26:45.772 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 11:26:46.147 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 11:26:46.351 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 11:26:46.460 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:26:46.460 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:26:46.525 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:26:46.525 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:26:46.725 - [本地测试主从Mysql-3306的mysql][localmaster] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@5257b3c3 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } }, "signature" : { "hash" : { "$binary" : "vm9RBlDQeJdXT4ioxmiAajNDQdw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 
[INFO ] 2024-09-18 11:26:46.737 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 11:26:46.737 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[ERROR] 2024-09-18 11:26:46.755 - [本地测试主从Mysql-3306的mysql][localmaster] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@5257b3c3 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } }, "signature" : { "hash" : { "$binary" : "vm9RBlDQeJdXT4ioxmiAajNDQdw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@5257b3c3 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } }, "signature" : { "hash" : { "$binary" : "vm9RBlDQeJdXT4ioxmiAajNDQdw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } }, "signature" : { "hash" : { "$binary" : "vm9RBlDQeJdXT4ioxmiAajNDQdw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@5257b3c3 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } }, "signature" : { "hash" : { "$binary" : "vm9RBlDQeJdXT4ioxmiAajNDQdw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@5257b3c3 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } }, "signature" : { "hash" : { "$binary" : "vm9RBlDQeJdXT4ioxmiAajNDQdw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_e653b64c-e17c-46a2-ae89-45d1d1817223
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } }, "signature" : { "hash" : { "$binary" : "vm9RBlDQeJdXT4ioxmiAajNDQdw=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726630006, "i" : 27 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 45 more

[INFO ] 2024-09-18 11:26:46.776 - [本地测试主从Mysql-3306的mysql][localmaster] - Job suspend in error handle 
[INFO ] 2024-09-18 11:26:46.776 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 11:26:46.776 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: null 
[INFO ] 2024-09-18 11:26:46.776 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: null 
[INFO ] 2024-09-18 11:26:46.777 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 11:26:46.777 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 11:26:46.784 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 3 ms 
[INFO ] 2024-09-18 11:26:46.784 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 11:26:46.788 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 11:26:46.788 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 11:26:46.788 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 11:26:46.788 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 11:26:46.788 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 11:26:46.788 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 6 ms 
[INFO ] 2024-09-18 11:26:48.169 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 11:26:48.194 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 11:26:48.194 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 11:26:48.194 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 11:26:48.210 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:26:48.296 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 11:29:41.016 - [本地测试主从Mysql-3306的mysql] - Task initialization... 
[INFO ] 2024-09-18 11:29:41.016 - [本地测试主从Mysql-3306的mysql] - Start task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql) 
[INFO ] 2024-09-18 11:29:41.104 - [本地测试主从Mysql-3306的mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 11:29:41.312 - [本地测试主从Mysql-3306的mysql] - The engine receives 本地测试主从Mysql-3306的mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 11:29:41.322 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:29:41.322 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:29:41.322 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:29:41.322 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:29:42.004 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 11:29:42.005 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-09-18 11:29:42.398 - [本地测试主从Mysql-3306的mysql][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726630181998} and {hostPort=localhost:43306, time=1726601381994} 
[INFO ] 2024-09-18 11:29:42.400 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-18 11:29:42.400 - [本地测试主从Mysql-3306的mysql][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-18 11:29:42.400 - [本地测试主从Mysql-3306的mysql][localmaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-18 11:29:42.443 - [本地测试主从Mysql-3306的mysql][localmaster] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000023","position":455,"gtidSet":""} 
[INFO ] 2024-09-18 11:29:42.447 - [本地测试主从Mysql-3306的mysql][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 11:29:42.520 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync started 
[INFO ] 2024-09-18 11:29:42.521 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting batch read, table name: t2, offset: null 
[INFO ] 2024-09-18 11:29:42.658 - [本地测试主从Mysql-3306的mysql][localmaster] - Table t2 is going to be initial synced 
[INFO ] 2024-09-18 11:29:42.658 - [本地测试主从Mysql-3306的mysql][localmaster] - Query table 't2' counts: 506000 
[INFO ] 2024-09-18 11:29:48.642 - [本地测试主从Mysql-3306的mysql][localmaster] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-18 11:29:48.644 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 11:29:48.644 - [本地测试主从Mysql-3306的mysql][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-18 11:29:48.644 - [本地测试主从Mysql-3306的mysql][localmaster] - Initial sync completed 
[INFO ] 2024-09-18 11:29:48.645 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting stream read, table list: [t2], offset: {"filename":"mysql-bin.000023","position":455,"gtidSet":""} 
[INFO ] 2024-09-18 11:29:48.734 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting mysql cdc, server name: 32eacc21-fa86-4736-906d-6ccb42e59e19 
[INFO ] 2024-09-18 11:29:48.735 - [本地测试主从Mysql-3306的mysql][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 533077603
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 32eacc21-fa86-4736-906d-6ccb42e59e19
  database.port: 33306
  threadName: Debezium-Mysql-Connector-32eacc21-fa86-4736-906d-6ccb42e59e19
  database.hostname: localhost
  database.password: ********
  name: 32eacc21-fa86-4736-906d-6ccb42e59e19
  pdk.offset.string: {"name":"32eacc21-fa86-4736-906d-6ccb42e59e19","offset":{"{\"server\":\"32eacc21-fa86-4736-906d-6ccb42e59e19\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":455,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 11:29:48.943 - [本地测试主从Mysql-3306的mysql][localmaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-18 12:15:55.954 - [本地测试主从Mysql-3306的mysql] - Stop task milestones: 66de569efbea5802d75150c7(本地测试主从Mysql-3306的mysql)  
[INFO ] 2024-09-18 12:15:56.477 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] running status set to false 
[INFO ] 2024-09-18 12:15:56.477 - [本地测试主从Mysql-3306的mysql][localmaster] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-18 12:15:56.477 - [本地测试主从Mysql-3306的mysql][localmaster] - Mysql binlog reader stopped 
[INFO ] 2024-09-18 12:15:56.486 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 12:15:56.486 - [本地测试主从Mysql-3306的mysql][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-e653b64c-e17c-46a2-ae89-45d1d1817223 
[INFO ] 2024-09-18 12:15:56.486 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] schema data cleaned 
[INFO ] 2024-09-18 12:15:56.486 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] monitor closed 
[INFO ] 2024-09-18 12:15:56.486 - [本地测试主从Mysql-3306的mysql][localmaster] - Node localmaster[e653b64c-e17c-46a2-ae89-45d1d1817223] close complete, cost 115 ms 
[INFO ] 2024-09-18 12:15:56.486 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] running status set to false 
[INFO ] 2024-09-18 12:15:56.492 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 12:15:56.492 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 12:15:56.493 - [本地测试主从Mysql-3306的mysql][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-8eec7d28-ce15-4c24-a832-d88310700e5b 
[INFO ] 2024-09-18 12:15:56.493 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] schema data cleaned 
[INFO ] 2024-09-18 12:15:56.493 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] monitor closed 
[INFO ] 2024-09-18 12:15:56.493 - [本地测试主从Mysql-3306的mysql][DummyTarget] - Node DummyTarget[8eec7d28-ce15-4c24-a832-d88310700e5b] close complete, cost 6 ms 
[INFO ] 2024-09-18 12:16:00.209 - [本地测试主从Mysql-3306的mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 12:16:00.209 - [本地测试主从Mysql-3306的mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-18 12:16:00.209 - [本地测试主从Mysql-3306的mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 12:16:00.225 - [本地测试主从Mysql-3306的mysql] - Remove memory task client succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
[INFO ] 2024-09-18 12:16:00.228 - [本地测试主从Mysql-3306的mysql] - Destroy memory task client cache succeed, task: 本地测试主从Mysql-3306的mysql[66de569efbea5802d75150c7] 
