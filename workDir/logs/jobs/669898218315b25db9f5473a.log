[INFO ] 2024-07-18 12:21:00.445 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Task initialization... 
[INFO ] 2024-07-18 12:21:00.656 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Start task milestones: 669898218315b25db9f5473a(t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418) 
[INFO ] 2024-07-18 12:21:00.829 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:21:00.830 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - The engine receives t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:21:00.911 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[0735818b-ee06-4828-ace7-ec23103960b8] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:21:00.911 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - Node qa_pg_5432_1717403468657_3537[e8c5109c-c0fd-47a3-aafe-fc16bb362229] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:21:00.912 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[0735818b-ee06-4828-ace7-ec23103960b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:21:00.912 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - Node qa_pg_5432_1717403468657_3537[e8c5109c-c0fd-47a3-aafe-fc16bb362229] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:21:01.296 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:21:01.296 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:21:01.297 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:21:01.348 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:21:01.544 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":1721276461,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:21:01.693 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:21:01.693 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Starting batch read, table name: t_4_2511_mongodb, offset: null 
[INFO ] 2024-07-18 12:21:01.704 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Table t_4_2511_mongodb is going to be initial synced 
[INFO ] 2024-07-18 12:21:01.704 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Table [t_4_2511_mongodb] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:21:01.709 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Query table 't_4_2511_mongodb' counts: 1 
[INFO ] 2024-07-18 12:21:01.709 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:21:01.709 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 12:21:01.709 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:21:01.718 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Starting stream read, table list: [t_4_2511_mongodb, _tapdata_heartbeat_table], offset: {"cdcOffset":1721276461,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:21:01.718 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t_4_2511_mongodb, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:21:17.904 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[0735818b-ee06-4828-ace7-ec23103960b8] running status set to false 
[INFO ] 2024-07-18 12:21:17.932 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-0735818b-ee06-4828-ace7-ec23103960b8 
[INFO ] 2024-07-18 12:21:17.933 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-0735818b-ee06-4828-ace7-ec23103960b8 
[INFO ] 2024-07-18 12:21:17.933 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[0735818b-ee06-4828-ace7-ec23103960b8] schema data cleaned 
[INFO ] 2024-07-18 12:21:17.933 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[0735818b-ee06-4828-ace7-ec23103960b8] monitor closed 
[INFO ] 2024-07-18 12:21:17.933 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[0735818b-ee06-4828-ace7-ec23103960b8] close complete, cost 32 ms 
[INFO ] 2024-07-18 12:21:17.934 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:21:17.934 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - Node qa_pg_5432_1717403468657_3537[e8c5109c-c0fd-47a3-aafe-fc16bb362229] running status set to false 
[INFO ] 2024-07-18 12:21:17.964 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-e8c5109c-c0fd-47a3-aafe-fc16bb362229 
[INFO ] 2024-07-18 12:21:17.964 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-e8c5109c-c0fd-47a3-aafe-fc16bb362229 
[INFO ] 2024-07-18 12:21:17.964 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - Node qa_pg_5432_1717403468657_3537[e8c5109c-c0fd-47a3-aafe-fc16bb362229] schema data cleaned 
[INFO ] 2024-07-18 12:21:17.964 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - Node qa_pg_5432_1717403468657_3537[e8c5109c-c0fd-47a3-aafe-fc16bb362229] monitor closed 
[INFO ] 2024-07-18 12:21:18.170 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418][qa_pg_5432_1717403468657_3537] - Node qa_pg_5432_1717403468657_3537[e8c5109c-c0fd-47a3-aafe-fc16bb362229] close complete, cost 31 ms 
[INFO ] 2024-07-18 12:21:19.512 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:21:19.513 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4b2a6337 
[INFO ] 2024-07-18 12:21:19.672 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Stop task milestones: 669898218315b25db9f5473a(t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418)  
[INFO ] 2024-07-18 12:21:19.673 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:21:19.673 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:21:19.715 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Remove memory task client succeed, task: t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418[669898218315b25db9f5473a] 
[INFO ] 2024-07-18 12:21:19.916 - [t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418] - Destroy memory task client cache succeed, task: t_4.2511-mdb_to_mysql_embedded_document_1717403468657_3537-1721276418[669898218315b25db9f5473a] 
