[INFO ] 2024-04-01 18:06:48.558 - [任务 39] - Start task milestones: 660a870d38ef7c7c95b82f51(任务 39) 
[INFO ] 2024-04-01 18:06:48.559 - [任务 39] - Task initialization... 
[INFO ] 2024-04-01 18:06:48.574 - [任务 39] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-01 18:06:48.727 - [任务 39] - The engine receives 任务 39 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-01 18:06:48.728 - [任务 39][test10] - Node test10[66a7d237-a459-4f7a-8824-27554a5f57c2] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:06:48.728 - [任务 39][CLAIM] - Node CLAIM[1ea469b8-1d96-4eda-bcee-6493b0d91b31] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:06:48.755 - [任务 39][CLAIM] - Node CLAIM[1ea469b8-1d96-4eda-bcee-6493b0d91b31] preload schema finished, cost 25 ms 
[INFO ] 2024-04-01 18:06:48.758 - [任务 39][test10] - Node test10[66a7d237-a459-4f7a-8824-27554a5f57c2] preload schema finished, cost 25 ms 
[INFO ] 2024-04-01 18:06:49.655 - [任务 39][test10] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-01 18:06:49.763 - [任务 39][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-01 18:06:49.764 - [任务 39][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-01 18:06:49.764 - [任务 39][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-01 18:06:49.765 - [任务 39][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145948132,"gtidSet":""} 
[INFO ] 2024-04-01 18:06:49.765 - [任务 39][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-04-01 18:06:49.812 - [任务 39][CLAIM] - Initial sync started 
[INFO ] 2024-04-01 18:06:49.824 - [任务 39][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-01 18:06:49.824 - [任务 39][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-01 18:06:49.933 - [任务 39][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-01 18:06:49.935 - [任务 39][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:06:49.935 - [任务 39][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-01 18:06:49.935 - [任务 39][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:06:49.935 - [任务 39][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145948132,"gtidSet":""} 
[INFO ] 2024-04-01 18:06:49.969 - [任务 39][CLAIM] - Starting mysql cdc, server name: 2ee63e15-76fc-4590-a6e9-2956fcb3a1f1 
[INFO ] 2024-04-01 18:06:50.043 - [任务 39][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1774380718
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2ee63e15-76fc-4590-a6e9-2956fcb3a1f1
  database.port: 3306
  threadName: Debezium-Mysql-Connector-2ee63e15-76fc-4590-a6e9-2956fcb3a1f1
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2ee63e15-76fc-4590-a6e9-2956fcb3a1f1
  pdk.offset.string: {"name":"2ee63e15-76fc-4590-a6e9-2956fcb3a1f1","offset":{"{\"server\":\"2ee63e15-76fc-4590-a6e9-2956fcb3a1f1\"}":"{\"file\":\"binlog.000020\",\"pos\":145948132,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-01 18:06:50.044 - [任务 39][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-01 18:09:58.613 - [任务 39][CLAIM] - Read DDL: alter table CLAIM rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-04-01 18:09:58.616 - [任务 39][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='2ee63e15-76fc-4590-a6e9-2956fcb3a1f1', offset={{"server":"2ee63e15-76fc-4590-a6e9-2956fcb3a1f1"}={"ts_sec":1711966198,"file":"binlog.000020","pos":145948832,"server_id":1}}} 
[INFO ] 2024-04-01 18:09:58.619 - [任务 39][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@2f2077c} 
[INFO ] 2024-04-01 18:10:14.664 - [任务 39][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660a870d38ef7c7c95b82f51 
[INFO ] 2024-04-01 18:18:17.105 - [任务 39][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-04-01 18:18:47.070 - [任务 39][CLAIM] - Node CLAIM[1ea469b8-1d96-4eda-bcee-6493b0d91b31] running status set to false 
