[INFO ] 2024-03-29 14:13:19.058 - [orders] - Task initialization... 
[INFO ] 2024-03-29 14:13:19.265 - [orders] - Start task milestones: 66065af52bfc1e544b39a61f(orders) 
[INFO ] 2024-03-29 14:13:19.478 - [orders] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 14:13:19.538 - [orders] - The engine receives orders task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 14:13:19.793 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] start preload schema,table counts: 4 
[INFO ] 2024-03-29 14:13:19.819 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:19.821 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:19.821 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:19.821 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:19.821 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:19.821 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:19.821 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:20.013 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:20.017 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] preload schema finished, cost 192 ms 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] preload schema finished, cost 188 ms 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] preload schema finished, cost 221 ms 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] preload schema finished, cost 222 ms 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] preload schema finished, cost 221 ms 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] preload schema finished, cost 188 ms 
[INFO ] 2024-03-29 14:13:20.017 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] preload schema finished, cost 221 ms 
[INFO ] 2024-03-29 14:13:20.029 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] preload schema finished, cost 238 ms 
[INFO ] 2024-03-29 14:13:20.029 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] preload schema finished, cost 206 ms 
[INFO ] 2024-03-29 14:13:20.029 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] preload schema finished, cost 238 ms 
[INFO ] 2024-03-29 14:13:20.135 - [orders][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:13:20.205 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] preload schema finished, cost 399 ms 
[INFO ] 2024-03-29 14:13:20.209 - [orders][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 14:13:20.209 - [orders][merge] - 
Merge lookup relation{
  Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
    ->Products(84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded)
} 
[INFO ] 2024-03-29 14:13:20.209 - [orders][merge] - 
Merge lookup relation{
  Orders(691d4b02-d308-4463-bb87-8bf921ca11bf)
    ->Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
} 
[INFO ] 2024-03-29 14:13:20.411 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:13:20.925 - [orders][Order Details] - Source node "Order Details" read batch size: 500 
[INFO ] 2024-03-29 14:13:20.926 - [orders][Order Details] - Source node "Order Details" event queue capacity: 1000 
[INFO ] 2024-03-29 14:13:20.926 - [orders][Order Details] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:13:21.097 - [orders][Order Details] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:13:21.097 - [orders][Orders] - Source node "Orders" read batch size: 500 
[INFO ] 2024-03-29 14:13:21.097 - [orders][Orders] - Source node "Orders" event queue capacity: 1000 
[INFO ] 2024-03-29 14:13:21.097 - [orders][Orders] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:13:21.187 - [orders][Orders] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:13:21.202 - [orders] - Node[Order Details] is waiting for running 
[INFO ] 2024-03-29 14:13:21.203 - [orders][Orders] - Initial sync started 
[INFO ] 2024-03-29 14:13:21.203 - [orders][Orders] - Starting batch read, table name: Orders, offset: null 
[INFO ] 2024-03-29 14:13:21.230 - [orders][Orders] - Table Orders is going to be initial synced 
[INFO ] 2024-03-29 14:13:21.230 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:13:21.332 - [orders][Orders] - Query table 'Orders' counts: 1 
[INFO ] 2024-03-29 14:13:21.332 - [orders][Orders] - Initial sync completed 
[INFO ] 2024-03-29 14:13:21.351 - [orders][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 14:13:21.352 - [orders][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 14:13:21.610 - [orders][orders] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 14:13:22.007 - [orders][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 14:13:22.007 - [orders][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 14:13:22.007 - [orders][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:13:22.063 - [orders][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:13:22.063 - [orders] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 14:13:22.837 - [orders] - Node[Orders] finish, notify next layer to run 
[INFO ] 2024-03-29 14:13:22.846 - [orders][Order Details] - Initial sync started 
[INFO ] 2024-03-29 14:13:22.846 - [orders] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 14:13:22.846 - [orders][Order Details] - Starting batch read, table name: Order Details, offset: null 
[INFO ] 2024-03-29 14:13:22.860 - [orders][Order Details] - Table Order Details is going to be initial synced 
[INFO ] 2024-03-29 14:13:22.898 - [orders][Order Details] - Query table 'Order Details' counts: 1 
[INFO ] 2024-03-29 14:13:22.898 - [orders][Order Details] - Initial sync completed 
[ERROR] 2024-03-29 14:13:23.309 - [orders][merge] - - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0} <-- Error Message -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 14:13:23.515 - [orders][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 14:13:23.626 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] running status set to false 
[INFO ] 2024-03-29 14:13:23.627 - [orders][Orders] - Incremental sync starting... 
[INFO ] 2024-03-29 14:13:23.627 - [orders][Orders] - Incremental sync completed 
[INFO ] 2024-03-29 14:13:23.641 - [orders][Orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:13:23.642 - [orders][Orders] - PDK connector node released: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:13:23.642 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] schema data cleaned 
[INFO ] 2024-03-29 14:13:23.662 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] monitor closed 
[INFO ] 2024-03-29 14:13:23.665 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] close complete, cost 70 ms 
[INFO ] 2024-03-29 14:13:23.665 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] running status set to false 
[INFO ] 2024-03-29 14:13:23.916 - [orders][Order Details] - Incremental sync starting... 
[INFO ] 2024-03-29 14:13:23.917 - [orders][Order Details] - Incremental sync completed 
[INFO ] 2024-03-29 14:13:23.920 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] schema data cleaned 
[INFO ] 2024-03-29 14:13:23.929 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] monitor closed 
[INFO ] 2024-03-29 14:13:23.937 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] close complete, cost 272 ms 
[INFO ] 2024-03-29 14:13:23.937 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] running status set to false 
[INFO ] 2024-03-29 14:13:23.974 - [orders][Order Details] - PDK connector node stopped: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:13:23.974 - [orders][Order Details] - PDK connector node released: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:13:23.974 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] schema data cleaned 
[INFO ] 2024-03-29 14:13:23.974 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] monitor closed 
[INFO ] 2024-03-29 14:13:23.975 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] close complete, cost 38 ms 
[INFO ] 2024-03-29 14:13:23.975 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] running status set to false 
[INFO ] 2024-03-29 14:13:23.990 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TargetMysql-49792b96-809b-43d2-9e02-f22956080901 
[INFO ] 2024-03-29 14:13:23.990 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TargetMysql-49792b96-809b-43d2-9e02-f22956080901 
[INFO ] 2024-03-29 14:13:23.996 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 14:13:23.996 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TestMongo-87fc3c02-748f-4f37-ae8b-778a4f289a08 
[INFO ] 2024-03-29 14:13:23.996 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TestMongo-87fc3c02-748f-4f37-ae8b-778a4f289a08 
[INFO ] 2024-03-29 14:13:23.996 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 14:13:23.998 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.000 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] monitor closed 
[INFO ] 2024-03-29 14:13:24.000 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] close complete, cost 24 ms 
[INFO ] 2024-03-29 14:13:24.011 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] running status set to false 
[INFO ] 2024-03-29 14:13:24.011 - [orders][Products] - Initial sync started 
[INFO ] 2024-03-29 14:13:24.015 - [orders][Products] - Initial sync completed 
[INFO ] 2024-03-29 14:13:24.015 - [orders][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 14:13:24.017 - [orders][Products] - Incremental sync completed 
[INFO ] 2024-03-29 14:13:24.018 - [orders][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:13:24.018 - [orders][Products] - PDK connector node released: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:13:24.018 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.019 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] monitor closed 
[INFO ] 2024-03-29 14:13:24.019 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] close complete, cost 18 ms 
[INFO ] 2024-03-29 14:13:24.020 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] running status set to false 
[INFO ] 2024-03-29 14:13:24.096 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.097 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] monitor closed 
[INFO ] 2024-03-29 14:13:24.100 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] close complete, cost 79 ms 
[INFO ] 2024-03-29 14:13:24.100 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] running status set to false 
[INFO ] 2024-03-29 14:13:24.147 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.150 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] monitor closed 
[INFO ] 2024-03-29 14:13:24.152 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] close complete, cost 50 ms 
[INFO ] 2024-03-29 14:13:24.152 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] running status set to false 
[INFO ] 2024-03-29 14:13:24.211 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.211 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] monitor closed 
[INFO ] 2024-03-29 14:13:24.212 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] close complete, cost 60 ms 
[INFO ] 2024-03-29 14:13:24.250 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] running status set to false 
[INFO ] 2024-03-29 14:13:24.250 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.250 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] monitor closed 
[INFO ] 2024-03-29 14:13:24.251 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] close complete, cost 39 ms 
[INFO ] 2024-03-29 14:13:24.251 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] running status set to false 
[INFO ] 2024-03-29 14:13:24.251 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG 
[INFO ] 2024-03-29 14:13:24.282 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG 
[INFO ] 2024-03-29 14:13:24.282 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.282 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] monitor closed 
[INFO ] 2024-03-29 14:13:24.283 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] close complete, cost 32 ms 
[INFO ] 2024-03-29 14:13:24.331 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] running status set to false 
[INFO ] 2024-03-29 14:13:24.331 - [orders][orders] - PDK connector node stopped: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:13:24.331 - [orders][orders] - PDK connector node released: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:13:24.331 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] schema data cleaned 
[INFO ] 2024-03-29 14:13:24.331 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] monitor closed 
[INFO ] 2024-03-29 14:13:24.537 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] close complete, cost 48 ms 
[INFO ] 2024-03-29 14:13:25.029 - [orders] - Task [orders] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 14:13:25.030 - [orders] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 14:13:25.050 - [orders] - Stop task milestones: 66065af52bfc1e544b39a61f(orders)  
[INFO ] 2024-03-29 14:13:25.051 - [orders] - Stopped task aspect(s) 
[INFO ] 2024-03-29 14:13:25.051 - [orders] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 14:13:25.074 - [orders] - Remove memory task client succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:13:25.075 - [orders] - Destroy memory task client cache succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:14:43.730 - [orders] - Task initialization... 
[INFO ] 2024-03-29 14:14:43.733 - [orders] - Start task milestones: 66065af52bfc1e544b39a61f(orders) 
[INFO ] 2024-03-29 14:14:43.934 - [orders] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 14:14:44.137 - [orders] - The engine receives orders task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 14:14:44.240 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.241 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] start preload schema,table counts: 4 
[INFO ] 2024-03-29 14:14:44.241 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.254 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.255 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.257 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.257 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.257 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.258 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.258 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.258 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:14:44.335 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] preload schema finished, cost 90 ms 
[INFO ] 2024-03-29 14:14:44.335 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] preload schema finished, cost 69 ms 
[INFO ] 2024-03-29 14:14:44.336 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] preload schema finished, cost 69 ms 
[INFO ] 2024-03-29 14:14:44.336 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] preload schema finished, cost 92 ms 
[INFO ] 2024-03-29 14:14:44.336 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] preload schema finished, cost 73 ms 
[INFO ] 2024-03-29 14:14:44.336 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] preload schema finished, cost 99 ms 
[INFO ] 2024-03-29 14:14:44.337 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] preload schema finished, cost 80 ms 
[INFO ] 2024-03-29 14:14:44.346 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] preload schema finished, cost 88 ms 
[INFO ] 2024-03-29 14:14:44.346 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] preload schema finished, cost 88 ms 
[INFO ] 2024-03-29 14:14:44.356 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] preload schema finished, cost 96 ms 
[INFO ] 2024-03-29 14:14:44.434 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] preload schema finished, cost 190 ms 
[INFO ] 2024-03-29 14:14:44.435 - [orders][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 14:14:44.436 - [orders][merge] - 
Merge lookup relation{
  Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
    ->Products(84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded)
} 
[INFO ] 2024-03-29 14:14:44.436 - [orders][merge] - 
Merge lookup relation{
  Orders(691d4b02-d308-4463-bb87-8bf921ca11bf)
    ->Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
} 
[INFO ] 2024-03-29 14:14:44.469 - [orders][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:14:44.489 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:14:44.489 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:14:44.559 - [orders][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 14:14:44.559 - [orders][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 14:14:44.698 - [orders][orders] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 14:14:44.699 - [orders][Order Details] - Source node "Order Details" read batch size: 500 
[INFO ] 2024-03-29 14:14:44.699 - [orders][Order Details] - Source node "Order Details" event queue capacity: 1000 
[INFO ] 2024-03-29 14:14:44.699 - [orders][Order Details] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:14:44.802 - [orders][Order Details] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:14:44.802 - [orders][Order Details] - Initial sync started 
[INFO ] 2024-03-29 14:14:44.807 - [orders][Order Details] - Starting batch read, table name: Order Details, offset: null 
[INFO ] 2024-03-29 14:14:44.807 - [orders][Order Details] - Table Order Details is going to be initial synced 
[INFO ] 2024-03-29 14:14:44.846 - [orders][Order Details] - Query table 'Order Details' counts: 1 
[INFO ] 2024-03-29 14:14:44.846 - [orders][Order Details] - Initial sync completed 
[INFO ] 2024-03-29 14:14:45.067 - [orders][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 14:14:45.067 - [orders][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 14:14:45.067 - [orders][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:14:45.127 - [orders][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:14:45.127 - [orders] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 14:14:45.185 - [orders][Orders] - Source node "Orders" read batch size: 500 
[INFO ] 2024-03-29 14:14:45.185 - [orders][Orders] - Source node "Orders" event queue capacity: 1000 
[INFO ] 2024-03-29 14:14:45.194 - [orders][Orders] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 14:14:45.194 - [orders][Orders] - batch offset found: {"Orders":{}},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:14:45.246 - [orders][Orders] - Initial sync started 
[INFO ] 2024-03-29 14:14:45.246 - [orders][Orders] - Starting batch read, table name: Orders, offset: {} 
[INFO ] 2024-03-29 14:14:45.275 - [orders][Orders] - Table Orders is going to be initial synced 
[INFO ] 2024-03-29 14:14:45.275 - [orders][Orders] - Query table 'Orders' counts: 1 
[INFO ] 2024-03-29 14:14:45.275 - [orders][Orders] - Initial sync completed 
[ERROR] 2024-03-29 14:14:45.447 - [orders][merge] - - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0, ProductID=1} <-- Error Message -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0, ProductID=1}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0, ProductID=1}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, orderId=1, subtotal=36.0, discount=0.0, ProductID=1}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 14:14:45.649 - [orders][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 14:14:45.871 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] running status set to false 
[INFO ] 2024-03-29 14:14:45.881 - [orders][Products] - Initial sync started 
[INFO ] 2024-03-29 14:14:45.901 - [orders][Products] - Initial sync completed 
[INFO ] 2024-03-29 14:14:45.901 - [orders][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 14:14:45.911 - [orders][Products] - Incremental sync completed 
[INFO ] 2024-03-29 14:14:45.911 - [orders][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:14:45.911 - [orders][Products] - PDK connector node released: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:14:45.911 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] schema data cleaned 
[INFO ] 2024-03-29 14:14:45.913 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] monitor closed 
[INFO ] 2024-03-29 14:14:45.913 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] close complete, cost 46 ms 
[INFO ] 2024-03-29 14:14:45.913 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] running status set to false 
[INFO ] 2024-03-29 14:14:45.916 - [orders][Order Details] - Incremental sync starting... 
[INFO ] 2024-03-29 14:14:45.916 - [orders][Order Details] - Incremental sync completed 
[INFO ] 2024-03-29 14:14:45.925 - [orders][Order Details] - PDK connector node stopped: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:14:45.925 - [orders][Order Details] - PDK connector node released: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:14:45.926 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] schema data cleaned 
[INFO ] 2024-03-29 14:14:45.926 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] monitor closed 
[INFO ] 2024-03-29 14:14:45.928 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] close complete, cost 12 ms 
[INFO ] 2024-03-29 14:14:45.928 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] running status set to false 
[INFO ] 2024-03-29 14:14:45.940 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TargetMysql-2f5137e2-df39-45e5-944e-73001a73d7d9 
[INFO ] 2024-03-29 14:14:45.946 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TargetMysql-2f5137e2-df39-45e5-944e-73001a73d7d9 
[INFO ] 2024-03-29 14:14:45.946 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 14:14:45.949 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TestMongo-9c4526cd-7302-460a-95dd-d9729dba1b40 
[INFO ] 2024-03-29 14:14:45.949 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TestMongo-9c4526cd-7302-460a-95dd-d9729dba1b40 
[INFO ] 2024-03-29 14:14:45.949 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 14:14:45.950 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] schema data cleaned 
[INFO ] 2024-03-29 14:14:45.950 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] monitor closed 
[INFO ] 2024-03-29 14:14:45.950 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] close complete, cost 23 ms 
[INFO ] 2024-03-29 14:14:46.011 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] running status set to false 
[INFO ] 2024-03-29 14:14:46.011 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.012 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] monitor closed 
[INFO ] 2024-03-29 14:14:46.012 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] close complete, cost 61 ms 
[INFO ] 2024-03-29 14:14:46.052 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] running status set to false 
[INFO ] 2024-03-29 14:14:46.053 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.053 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] monitor closed 
[INFO ] 2024-03-29 14:14:46.053 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] close complete, cost 38 ms 
[INFO ] 2024-03-29 14:14:46.053 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] running status set to false 
[INFO ] 2024-03-29 14:14:46.101 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.102 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] monitor closed 
[INFO ] 2024-03-29 14:14:46.103 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] close complete, cost 49 ms 
[INFO ] 2024-03-29 14:14:46.154 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] running status set to false 
[INFO ] 2024-03-29 14:14:46.154 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.154 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] monitor closed 
[INFO ] 2024-03-29 14:14:46.155 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] close complete, cost 52 ms 
[INFO ] 2024-03-29 14:14:46.160 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] running status set to false 
[INFO ] 2024-03-29 14:14:46.160 - [orders][Orders] - Incremental sync starting... 
[INFO ] 2024-03-29 14:14:46.177 - [orders][Orders] - Incremental sync completed 
[INFO ] 2024-03-29 14:14:46.178 - [orders][Orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:14:46.178 - [orders][Orders] - PDK connector node released: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:14:46.178 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.178 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] monitor closed 
[INFO ] 2024-03-29 14:14:46.179 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] close complete, cost 23 ms 
[INFO ] 2024-03-29 14:14:46.179 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] running status set to false 
[INFO ] 2024-03-29 14:14:46.217 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.217 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] monitor closed 
[INFO ] 2024-03-29 14:14:46.267 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] close complete, cost 82 ms 
[INFO ] 2024-03-29 14:14:46.269 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] running status set to false 
[INFO ] 2024-03-29 14:14:46.269 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG 
[INFO ] 2024-03-29 14:14:46.283 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG 
[INFO ] 2024-03-29 14:14:46.283 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.283 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] monitor closed 
[INFO ] 2024-03-29 14:14:46.283 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] close complete, cost 22 ms 
[INFO ] 2024-03-29 14:14:46.289 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] running status set to false 
[INFO ] 2024-03-29 14:14:46.289 - [orders][orders] - PDK connector node stopped: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:14:46.289 - [orders][orders] - PDK connector node released: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:14:46.289 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] schema data cleaned 
[INFO ] 2024-03-29 14:14:46.289 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] monitor closed 
[INFO ] 2024-03-29 14:14:46.290 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] close complete, cost 5 ms 
[INFO ] 2024-03-29 14:14:50.186 - [orders] - Task [orders] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 14:14:50.186 - [orders] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 14:14:50.245 - [orders] - Stop task milestones: 66065af52bfc1e544b39a61f(orders)  
[INFO ] 2024-03-29 14:14:50.245 - [orders] - Stopped task aspect(s) 
[INFO ] 2024-03-29 14:14:50.245 - [orders] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 14:14:50.278 - [orders] - Remove memory task client succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:14:50.278 - [orders] - Destroy memory task client cache succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:18:50.261 - [orders] - Task initialization... 
[INFO ] 2024-03-29 14:18:50.261 - [orders] - Start task milestones: 66065af52bfc1e544b39a61f(orders) 
[INFO ] 2024-03-29 14:18:50.302 - [orders] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 14:18:50.619 - [orders] - The engine receives orders task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 14:18:50.619 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.647 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.647 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.647 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.647 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.647 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.647 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.649 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.649 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] start preload schema,table counts: 4 
[INFO ] 2024-03-29 14:18:50.664 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.664 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:18:50.725 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] preload schema finished, cost 88 ms 
[INFO ] 2024-03-29 14:18:50.726 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] preload schema finished, cost 75 ms 
[INFO ] 2024-03-29 14:18:50.726 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] preload schema finished, cost 78 ms 
[INFO ] 2024-03-29 14:18:50.726 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] preload schema finished, cost 79 ms 
[INFO ] 2024-03-29 14:18:50.726 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] preload schema finished, cost 78 ms 
[INFO ] 2024-03-29 14:18:50.733 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] preload schema finished, cost 62 ms 
[INFO ] 2024-03-29 14:18:50.745 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] preload schema finished, cost 108 ms 
[INFO ] 2024-03-29 14:18:50.745 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] preload schema finished, cost 81 ms 
[INFO ] 2024-03-29 14:18:50.774 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] preload schema finished, cost 68 ms 
[INFO ] 2024-03-29 14:18:50.774 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] preload schema finished, cost 127 ms 
[INFO ] 2024-03-29 14:18:50.868 - [orders][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:18:50.871 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] preload schema finished, cost 217 ms 
[INFO ] 2024-03-29 14:18:50.871 - [orders][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 14:18:50.871 - [orders][merge] - 
Merge lookup relation{
  Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
    ->Products(84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded)
} 
[INFO ] 2024-03-29 14:18:50.871 - [orders][merge] - 
Merge lookup relation{
  Orders(691d4b02-d308-4463-bb87-8bf921ca11bf)
    ->Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
} 
[INFO ] 2024-03-29 14:18:51.072 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:18:51.346 - [orders][Order Details] - Source node "Order Details" read batch size: 500 
[INFO ] 2024-03-29 14:18:51.348 - [orders][Order Details] - Source node "Order Details" event queue capacity: 1000 
[INFO ] 2024-03-29 14:18:51.348 - [orders][Order Details] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:18:51.456 - [orders][Order Details] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:18:51.459 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:18:51.490 - [orders] - Node[Order Details] is waiting for running 
[INFO ] 2024-03-29 14:18:51.490 - [orders][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 14:18:51.494 - [orders][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 14:18:51.633 - [orders][Orders] - Source node "Orders" read batch size: 500 
[INFO ] 2024-03-29 14:18:51.635 - [orders][Orders] - Source node "Orders" event queue capacity: 1000 
[INFO ] 2024-03-29 14:18:51.635 - [orders][Orders] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:18:51.635 - [orders][Orders] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:18:51.695 - [orders][Orders] - Initial sync started 
[INFO ] 2024-03-29 14:18:51.708 - [orders][Orders] - Starting batch read, table name: Orders, offset: null 
[INFO ] 2024-03-29 14:18:51.716 - [orders][Orders] - Table Orders is going to be initial synced 
[INFO ] 2024-03-29 14:18:51.747 - [orders][Orders] - Query table 'Orders' counts: 1 
[INFO ] 2024-03-29 14:18:51.757 - [orders][Orders] - Initial sync completed 
[INFO ] 2024-03-29 14:18:51.930 - [orders][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 14:18:51.930 - [orders][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 14:18:51.930 - [orders][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:18:51.930 - [orders][orders] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 14:18:51.935 - [orders][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:18:52.137 - [orders] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 14:18:52.460 - [orders] - Node[Orders] finish, notify next layer to run 
[INFO ] 2024-03-29 14:18:52.460 - [orders] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 14:18:52.460 - [orders][Order Details] - Initial sync started 
[INFO ] 2024-03-29 14:18:52.460 - [orders][Order Details] - Starting batch read, table name: Order Details, offset: null 
[INFO ] 2024-03-29 14:18:52.510 - [orders][Order Details] - Table Order Details is going to be initial synced 
[INFO ] 2024-03-29 14:18:52.511 - [orders][Order Details] - Query table 'Order Details' counts: 1 
[INFO ] 2024-03-29 14:18:52.713 - [orders][Order Details] - Initial sync completed 
[ERROR] 2024-03-29 14:18:52.927 - [orders][merge] - - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0} <-- Error Message -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 14:18:52.928 - [orders][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 14:18:53.312 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] running status set to false 
[INFO ] 2024-03-29 14:18:53.324 - [orders][Orders] - Incremental sync starting... 
[INFO ] 2024-03-29 14:18:53.324 - [orders][Orders] - Incremental sync completed 
[INFO ] 2024-03-29 14:18:53.353 - [orders][Orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:18:53.353 - [orders][Orders] - PDK connector node released: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:18:53.353 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.354 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] monitor closed 
[INFO ] 2024-03-29 14:18:53.361 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] close complete, cost 87 ms 
[INFO ] 2024-03-29 14:18:53.361 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] running status set to false 
[INFO ] 2024-03-29 14:18:53.378 - [orders][Products] - Initial sync started 
[INFO ] 2024-03-29 14:18:53.378 - [orders][Products] - Initial sync completed 
[INFO ] 2024-03-29 14:18:53.378 - [orders][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 14:18:53.378 - [orders][Products] - Incremental sync completed 
[INFO ] 2024-03-29 14:18:53.379 - [orders][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:18:53.379 - [orders][Products] - PDK connector node released: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:18:53.379 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.379 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] monitor closed 
[INFO ] 2024-03-29 14:18:53.380 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] close complete, cost 19 ms 
[INFO ] 2024-03-29 14:18:53.380 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] running status set to false 
[INFO ] 2024-03-29 14:18:53.458 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.458 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] monitor closed 
[INFO ] 2024-03-29 14:18:53.459 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] close complete, cost 79 ms 
[INFO ] 2024-03-29 14:18:53.459 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] running status set to false 
[INFO ] 2024-03-29 14:18:53.495 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.495 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] monitor closed 
[INFO ] 2024-03-29 14:18:53.497 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] close complete, cost 37 ms 
[INFO ] 2024-03-29 14:18:53.497 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] running status set to false 
[INFO ] 2024-03-29 14:18:53.534 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.534 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] monitor closed 
[INFO ] 2024-03-29 14:18:53.534 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] close complete, cost 37 ms 
[INFO ] 2024-03-29 14:18:53.534 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] running status set to false 
[INFO ] 2024-03-29 14:18:53.538 - [orders][Order Details] - Incremental sync starting... 
[INFO ] 2024-03-29 14:18:53.538 - [orders][Order Details] - Incremental sync completed 
[INFO ] 2024-03-29 14:18:53.543 - [orders][Order Details] - PDK connector node stopped: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:18:53.543 - [orders][Order Details] - PDK connector node released: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:18:53.543 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.543 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] monitor closed 
[INFO ] 2024-03-29 14:18:53.543 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] close complete, cost 8 ms 
[INFO ] 2024-03-29 14:18:53.553 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] running status set to false 
[INFO ] 2024-03-29 14:18:53.553 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TargetMysql-479282cd-d629-4a18-b668-e671d2aed86e 
[INFO ] 2024-03-29 14:18:53.554 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TargetMysql-479282cd-d629-4a18-b668-e671d2aed86e 
[INFO ] 2024-03-29 14:18:53.554 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.558 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TestMongo-d042bdaf-7dbd-4f7e-a4e7-c0e7a0594c46 
[INFO ] 2024-03-29 14:18:53.558 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TestMongo-d042bdaf-7dbd-4f7e-a4e7-c0e7a0594c46 
[INFO ] 2024-03-29 14:18:53.558 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.559 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.559 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] monitor closed 
[INFO ] 2024-03-29 14:18:53.559 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] close complete, cost 15 ms 
[INFO ] 2024-03-29 14:18:53.559 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] running status set to false 
[INFO ] 2024-03-29 14:18:53.597 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.597 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] monitor closed 
[INFO ] 2024-03-29 14:18:53.597 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] close complete, cost 37 ms 
[INFO ] 2024-03-29 14:18:53.597 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] running status set to false 
[INFO ] 2024-03-29 14:18:53.633 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.633 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] monitor closed 
[INFO ] 2024-03-29 14:18:53.633 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] close complete, cost 36 ms 
[INFO ] 2024-03-29 14:18:53.633 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] running status set to false 
[INFO ] 2024-03-29 14:18:53.633 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG 
[INFO ] 2024-03-29 14:18:53.634 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG 
[INFO ] 2024-03-29 14:18:53.638 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.638 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] monitor closed 
[INFO ] 2024-03-29 14:18:53.638 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] close complete, cost 5 ms 
[INFO ] 2024-03-29 14:18:53.660 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] running status set to false 
[INFO ] 2024-03-29 14:18:53.660 - [orders][orders] - PDK connector node stopped: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:18:53.661 - [orders][orders] - PDK connector node released: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:18:53.661 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] schema data cleaned 
[INFO ] 2024-03-29 14:18:53.662 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] monitor closed 
[INFO ] 2024-03-29 14:18:53.662 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] close complete, cost 22 ms 
[INFO ] 2024-03-29 14:18:55.551 - [orders] - Task [orders] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 14:18:55.551 - [orders] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 14:18:55.578 - [orders] - Stop task milestones: 66065af52bfc1e544b39a61f(orders)  
[INFO ] 2024-03-29 14:18:55.578 - [orders] - Stopped task aspect(s) 
[INFO ] 2024-03-29 14:18:55.578 - [orders] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 14:18:55.610 - [orders] - Remove memory task client succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:18:55.611 - [orders] - Destroy memory task client cache succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:20:38.147 - [orders] - Task initialization... 
[INFO ] 2024-03-29 14:20:38.191 - [orders] - Start task milestones: 66065af52bfc1e544b39a61f(orders) 
[INFO ] 2024-03-29 14:20:38.195 - [orders] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 14:20:38.375 - [orders] - The engine receives orders task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 14:20:38.518 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.519 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.519 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.520 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.520 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.520 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.525 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] start preload schema,table counts: 4 
[INFO ] 2024-03-29 14:20:38.528 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.528 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.528 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.528 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] preload schema finished, cost 82 ms 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] preload schema finished, cost 76 ms 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] preload schema finished, cost 86 ms 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] preload schema finished, cost 85 ms 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] preload schema finished, cost 78 ms 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] preload schema finished, cost 87 ms 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] preload schema finished, cost 81 ms 
[INFO ] 2024-03-29 14:20:38.607 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] preload schema finished, cost 89 ms 
[INFO ] 2024-03-29 14:20:38.609 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] preload schema finished, cost 79 ms 
[INFO ] 2024-03-29 14:20:38.609 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] preload schema finished, cost 90 ms 
[INFO ] 2024-03-29 14:20:38.681 - [orders][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:20:38.685 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] preload schema finished, cost 156 ms 
[INFO ] 2024-03-29 14:20:38.685 - [orders][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 14:20:38.685 - [orders][merge] - 
Merge lookup relation{
  Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
    ->Products(84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded)
} 
[INFO ] 2024-03-29 14:20:38.685 - [orders][merge] - 
Merge lookup relation{
  Orders(691d4b02-d308-4463-bb87-8bf921ca11bf)
    ->Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
} 
[INFO ] 2024-03-29 14:20:38.889 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:20:39.111 - [orders][Order Details] - Source node "Order Details" read batch size: 500 
[INFO ] 2024-03-29 14:20:39.115 - [orders][Order Details] - Source node "Order Details" event queue capacity: 1000 
[INFO ] 2024-03-29 14:20:39.116 - [orders][Order Details] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:20:39.241 - [orders][Order Details] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:20:39.242 - [orders] - Node[Order Details] is waiting for running 
[INFO ] 2024-03-29 14:20:39.297 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:20:39.300 - [orders][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 14:20:39.300 - [orders][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 14:20:39.475 - [orders][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 14:20:39.475 - [orders][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 14:20:39.475 - [orders][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:20:39.542 - [orders][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:20:39.542 - [orders] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 14:20:39.640 - [orders][Orders] - Source node "Orders" read batch size: 500 
[INFO ] 2024-03-29 14:20:39.640 - [orders][Orders] - Source node "Orders" event queue capacity: 1000 
[INFO ] 2024-03-29 14:20:39.641 - [orders][Orders] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:20:39.652 - [orders][Orders] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:20:39.653 - [orders][orders] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 14:20:39.702 - [orders][Orders] - Initial sync started 
[INFO ] 2024-03-29 14:20:39.707 - [orders][Orders] - Starting batch read, table name: Orders, offset: null 
[INFO ] 2024-03-29 14:20:39.723 - [orders][Orders] - Table Orders is going to be initial synced 
[INFO ] 2024-03-29 14:20:39.763 - [orders][Orders] - Query table 'Orders' counts: 1 
[INFO ] 2024-03-29 14:20:39.764 - [orders][Orders] - Initial sync completed 
[INFO ] 2024-03-29 14:20:41.183 - [orders] - Node[Orders] finish, notify next layer to run 
[INFO ] 2024-03-29 14:20:41.187 - [orders] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 14:20:41.187 - [orders][Order Details] - Initial sync started 
[INFO ] 2024-03-29 14:20:41.187 - [orders][Order Details] - Starting batch read, table name: Order Details, offset: null 
[INFO ] 2024-03-29 14:20:41.238 - [orders][Order Details] - Table Order Details is going to be initial synced 
[INFO ] 2024-03-29 14:20:41.238 - [orders][Order Details] - Query table 'Order Details' counts: 1 
[INFO ] 2024-03-29 14:20:41.238 - [orders][Order Details] - Initial sync completed 
[ERROR] 2024-03-29 14:22:48.534 - [orders][merge] - - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0} <-- Error Message -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG
- Pk or unique field: [orderId, productId]
- Data: {unitPrice=18.00, quantity=2, productID=1, orderId=1, subtotal=36.0, discount=0.0}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1366)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getPkOrUniqueValueKey(HazelcastMergeNode.java:1354)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1215)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 14:22:48.534 - [orders][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 14:22:48.534 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] running status set to false 
[INFO ] 2024-03-29 14:22:48.534 - [orders][Products] - Initial sync started 
[INFO ] 2024-03-29 14:22:48.534 - [orders][Products] - Initial sync completed 
[INFO ] 2024-03-29 14:22:48.534 - [orders][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 14:22:48.534 - [orders][Products] - Incremental sync completed 
[INFO ] 2024-03-29 14:22:48.534 - [orders][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:22:48.535 - [orders][Products] - PDK connector node released: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:22:48.535 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.536 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] monitor closed 
[INFO ] 2024-03-29 14:22:48.537 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] close complete, cost 72 ms 
[INFO ] 2024-03-29 14:22:48.537 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] running status set to false 
[INFO ] 2024-03-29 14:22:48.632 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.632 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] monitor closed 
[INFO ] 2024-03-29 14:22:48.633 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] close complete, cost 96 ms 
[INFO ] 2024-03-29 14:22:48.633 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] running status set to false 
[INFO ] 2024-03-29 14:22:48.709 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.709 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] monitor closed 
[INFO ] 2024-03-29 14:22:48.729 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] close complete, cost 95 ms 
[INFO ] 2024-03-29 14:22:48.729 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] running status set to false 
[INFO ] 2024-03-29 14:22:48.730 - [orders][Order Details] - Incremental sync starting... 
[INFO ] 2024-03-29 14:22:48.730 - [orders][Order Details] - Incremental sync completed 
[INFO ] 2024-03-29 14:22:48.737 - [orders][Order Details] - PDK connector node stopped: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:22:48.737 - [orders][Order Details] - PDK connector node released: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:22:48.737 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.737 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] monitor closed 
[INFO ] 2024-03-29 14:22:48.738 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] close complete, cost 9 ms 
[INFO ] 2024-03-29 14:22:48.747 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] running status set to false 
[INFO ] 2024-03-29 14:22:48.757 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TargetMysql-977b8660-5149-4500-8aca-4298a7f6bea8 
[INFO ] 2024-03-29 14:22:48.758 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TargetMysql-977b8660-5149-4500-8aca-4298a7f6bea8 
[INFO ] 2024-03-29 14:22:48.758 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.761 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TestMongo-655e6d03-6618-47b6-b917-832b06413077 
[INFO ] 2024-03-29 14:22:48.761 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TestMongo-655e6d03-6618-47b6-b917-832b06413077 
[INFO ] 2024-03-29 14:22:48.761 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.761 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.762 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] monitor closed 
[INFO ] 2024-03-29 14:22:48.762 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] close complete, cost 23 ms 
[INFO ] 2024-03-29 14:22:48.808 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] running status set to false 
[INFO ] 2024-03-29 14:22:48.809 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.809 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] monitor closed 
[INFO ] 2024-03-29 14:22:48.811 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] close complete, cost 49 ms 
[INFO ] 2024-03-29 14:22:48.854 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] running status set to false 
[INFO ] 2024-03-29 14:22:48.855 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.855 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] monitor closed 
[INFO ] 2024-03-29 14:22:48.855 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] close complete, cost 43 ms 
[INFO ] 2024-03-29 14:22:48.855 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] running status set to false 
[INFO ] 2024-03-29 14:22:48.855 - [orders][Orders] - Incremental sync starting... 
[INFO ] 2024-03-29 14:22:48.857 - [orders][Orders] - Incremental sync completed 
[INFO ] 2024-03-29 14:22:48.858 - [orders][Orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:22:48.858 - [orders][Orders] - PDK connector node released: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:22:48.858 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.870 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] monitor closed 
[INFO ] 2024-03-29 14:22:48.871 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] close complete, cost 15 ms 
[INFO ] 2024-03-29 14:22:48.871 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] running status set to false 
[INFO ] 2024-03-29 14:22:48.913 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.916 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] monitor closed 
[INFO ] 2024-03-29 14:22:48.916 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] close complete, cost 45 ms 
[INFO ] 2024-03-29 14:22:48.916 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] running status set to false 
[INFO ] 2024-03-29 14:22:48.916 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG 
[INFO ] 2024-03-29 14:22:48.922 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG 
[INFO ] 2024-03-29 14:22:48.922 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.922 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] monitor closed 
[INFO ] 2024-03-29 14:22:48.922 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] close complete, cost 5 ms 
[INFO ] 2024-03-29 14:22:48.936 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] running status set to false 
[INFO ] 2024-03-29 14:22:48.936 - [orders][orders] - PDK connector node stopped: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:22:48.936 - [orders][orders] - PDK connector node released: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:22:48.937 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] schema data cleaned 
[INFO ] 2024-03-29 14:22:48.937 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] monitor closed 
[INFO ] 2024-03-29 14:22:49.140 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] close complete, cost 14 ms 
[INFO ] 2024-03-29 14:22:53.005 - [orders] - Task [orders] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 14:22:53.029 - [orders] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 14:22:53.029 - [orders] - Stop task milestones: 66065af52bfc1e544b39a61f(orders)  
[INFO ] 2024-03-29 14:22:53.056 - [orders] - Stopped task aspect(s) 
[INFO ] 2024-03-29 14:22:53.057 - [orders] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 14:22:53.081 - [orders] - Remove memory task client succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:22:53.084 - [orders] - Destroy memory task client cache succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:23:38.607 - [orders] - Task initialization... 
[INFO ] 2024-03-29 14:23:38.720 - [orders] - Start task milestones: 66065af52bfc1e544b39a61f(orders) 
[INFO ] 2024-03-29 14:23:38.721 - [orders] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 14:23:38.879 - [orders] - The engine receives orders task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.197 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] start preload schema,table counts: 4 
[INFO ] 2024-03-29 14:23:39.197 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] preload schema finished, cost 66 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] preload schema finished, cost 67 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] preload schema finished, cost 70 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] preload schema finished, cost 66 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] preload schema finished, cost 72 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] preload schema finished, cost 65 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] preload schema finished, cost 67 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] preload schema finished, cost 81 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] preload schema finished, cost 82 ms 
[INFO ] 2024-03-29 14:23:39.278 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] preload schema finished, cost 82 ms 
[INFO ] 2024-03-29 14:23:39.402 - [orders][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:23:39.405 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] preload schema finished, cost 198 ms 
[INFO ] 2024-03-29 14:23:39.405 - [orders][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 14:23:39.405 - [orders][merge] - 
Merge lookup relation{
  Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
    ->Products(84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded)
} 
[INFO ] 2024-03-29 14:23:39.405 - [orders][merge] - 
Merge lookup relation{
  Orders(691d4b02-d308-4463-bb87-8bf921ca11bf)
    ->Order Details(857431f1-77d7-44dc-9ac9-be0aa3eddd2d)
} 
[INFO ] 2024-03-29 14:23:39.463 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:23:39.463 - [orders][merge] - Create merge cache imap name: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 14:23:39.571 - [orders][orders] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 14:23:39.571 - [orders][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 14:23:39.571 - [orders][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 14:23:39.641 - [orders][Orders] - Source node "Orders" read batch size: 500 
[INFO ] 2024-03-29 14:23:39.641 - [orders][Orders] - Source node "Orders" event queue capacity: 1000 
[INFO ] 2024-03-29 14:23:39.642 - [orders][Orders] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 14:23:39.729 - [orders][Orders] - batch offset found: {"Orders":{}},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:23:39.729 - [orders][Orders] - Initial sync started 
[INFO ] 2024-03-29 14:23:39.736 - [orders][Orders] - Starting batch read, table name: Orders, offset: {} 
[INFO ] 2024-03-29 14:23:39.750 - [orders][Orders] - Table Orders is going to be initial synced 
[INFO ] 2024-03-29 14:23:39.791 - [orders][Orders] - Query table 'Orders' counts: 1 
[INFO ] 2024-03-29 14:23:39.791 - [orders][Orders] - Initial sync completed 
[INFO ] 2024-03-29 14:23:40.035 - [orders][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 14:23:40.036 - [orders][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 14:23:40.036 - [orders][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:23:40.040 - [orders][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:23:40.158 - [orders] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 14:23:40.158 - [orders][Order Details] - Source node "Order Details" read batch size: 500 
[INFO ] 2024-03-29 14:23:40.158 - [orders][Order Details] - Source node "Order Details" event queue capacity: 1000 
[INFO ] 2024-03-29 14:23:40.160 - [orders][Order Details] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 14:23:40.160 - [orders][Order Details] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:23:40.217 - [orders][Order Details] - Initial sync started 
[INFO ] 2024-03-29 14:23:40.217 - [orders][Order Details] - Starting batch read, table name: Order Details, offset: null 
[INFO ] 2024-03-29 14:23:40.267 - [orders][Order Details] - Table Order Details is going to be initial synced 
[INFO ] 2024-03-29 14:23:40.268 - [orders][Order Details] - Query table 'Order Details' counts: 1 
[INFO ] 2024-03-29 14:23:40.476 - [orders][Order Details] - Initial sync completed 
[INFO ] 2024-03-29 14:23:40.615 - [orders] - Node[Order Details] finish, notify next layer to run 
[INFO ] 2024-03-29 14:23:40.615 - [orders] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 14:23:40.615 - [orders][Products] - Initial sync started 
[INFO ] 2024-03-29 14:23:40.616 - [orders][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 14:23:40.616 - [orders][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 14:23:40.653 - [orders][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 14:23:40.860 - [orders][Products] - Initial sync completed 
[INFO ] 2024-03-29 14:23:41.819 - [orders][Orders] - Incremental sync starting... 
[INFO ] 2024-03-29 14:23:41.823 - [orders][Orders] - Initial sync completed 
[INFO ] 2024-03-29 14:23:41.889 - [orders][Orders] - Starting stream read, table list: [Orders], offset: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:23:41.889 - [orders][Orders] - Starting mysql cdc, server name: 15715b36-e55c-4227-ac70-548e00c8f69b 
[INFO ] 2024-03-29 14:23:42.094 - [orders][Orders] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 284071157
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 15715b36-e55c-4227-ac70-548e00c8f69b
  database.port: 3307
  threadName: Debezium-Mysql-Connector-15715b36-e55c-4227-ac70-548e00c8f69b
  database.hostname: 127.0.0.1
  database.password: ********
  name: 15715b36-e55c-4227-ac70-548e00c8f69b
  pdk.offset.string: {"name":"15715b36-e55c-4227-ac70-548e00c8f69b","offset":{"{\"server\":\"15715b36-e55c-4227-ac70-548e00c8f69b\"}":"{\"file\":\"binlog.000008\",\"pos\":6369457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Orders
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 14:23:42.287 - [orders][Orders] - Connector Mysql incremental start succeed, tables: [Orders], data change syncing 
[INFO ] 2024-03-29 14:23:42.287 - [orders][Order Details] - Incremental sync starting... 
[INFO ] 2024-03-29 14:23:42.287 - [orders][Order Details] - Initial sync completed 
[INFO ] 2024-03-29 14:23:42.288 - [orders][Order Details] - Starting stream read, table list: [Order Details], offset: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:23:42.338 - [orders][Order Details] - Starting mysql cdc, server name: 747f0093-e25e-4fbb-b4f3-62de510b051d 
[INFO ] 2024-03-29 14:23:42.339 - [orders][Order Details] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 82224091
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 747f0093-e25e-4fbb-b4f3-62de510b051d
  database.port: 3307
  threadName: Debezium-Mysql-Connector-747f0093-e25e-4fbb-b4f3-62de510b051d
  database.hostname: 127.0.0.1
  database.password: ********
  name: 747f0093-e25e-4fbb-b4f3-62de510b051d
  pdk.offset.string: {"name":"747f0093-e25e-4fbb-b4f3-62de510b051d","offset":{"{\"server\":\"747f0093-e25e-4fbb-b4f3-62de510b051d\"}":"{\"file\":\"binlog.000008\",\"pos\":6369457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Order Details
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 14:23:42.551 - [orders][Order Details] - Connector Mysql incremental start succeed, tables: [Order Details], data change syncing 
[INFO ] 2024-03-29 14:23:42.718 - [orders][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 14:23:42.742 - [orders][Products] - Initial sync completed 
[INFO ] 2024-03-29 14:23:42.764 - [orders][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 14:23:42.926 - [orders][Products] - Starting mysql cdc, server name: b8894ec5-200b-4214-8198-f2fbac7a3561 
[INFO ] 2024-03-29 14:23:42.933 - [orders][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 227524938
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b8894ec5-200b-4214-8198-f2fbac7a3561
  database.port: 3307
  threadName: Debezium-Mysql-Connector-b8894ec5-200b-4214-8198-f2fbac7a3561
  database.hostname: 127.0.0.1
  database.password: ********
  name: b8894ec5-200b-4214-8198-f2fbac7a3561
  pdk.offset.string: {"name":"b8894ec5-200b-4214-8198-f2fbac7a3561","offset":{"{\"server\":\"b8894ec5-200b-4214-8198-f2fbac7a3561\"}":"{\"file\":\"binlog.000008\",\"pos\":6369457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 14:23:43.150 - [orders][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 14:23:49.866 - [orders] - Stop task milestones: 66065af52bfc1e544b39a61f(orders)  
[INFO ] 2024-03-29 14:23:50.240 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] running status set to false 
[INFO ] 2024-03-29 14:23:50.316 - [orders][Order Details] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 14:23:50.321 - [orders][Order Details] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 14:23:50.327 - [orders][Order Details] - PDK connector node stopped: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:23:50.328 - [orders][Order Details] - PDK connector node released: HazelcastSourcePdkDataNode-a2bd4368-0fdc-4e25-9585-c1f9246e51ca 
[INFO ] 2024-03-29 14:23:50.328 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.328 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] monitor closed 
[INFO ] 2024-03-29 14:23:50.328 - [orders][Order Details] - Node Order Details[a2bd4368-0fdc-4e25-9585-c1f9246e51ca] close complete, cost 100 ms 
[INFO ] 2024-03-29 14:23:50.349 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] running status set to false 
[INFO ] 2024-03-29 14:23:50.349 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TargetMysql-8d6eeaf2-6b28-4381-a206-5fc35028e920 
[INFO ] 2024-03-29 14:23:50.350 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TargetMysql-8d6eeaf2-6b28-4381-a206-5fc35028e920 
[INFO ] 2024-03-29 14:23:50.353 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.353 - [orders][Order Details] - PDK connector node stopped: ScriptExecutor-TestMongo-69fe7e8b-ce07-4287-917f-19c722d7ebc3 
[INFO ] 2024-03-29 14:23:50.353 - [orders][Order Details] - PDK connector node released: ScriptExecutor-TestMongo-69fe7e8b-ce07-4287-917f-19c722d7ebc3 
[INFO ] 2024-03-29 14:23:50.353 - [orders][Order Details] - [ScriptExecutorsManager-66065af52bfc1e544b39a61f-54235be1-b7ab-4dfb-a936-44edb60b5eee-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.353 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.353 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] monitor closed 
[INFO ] 2024-03-29 14:23:50.353 - [orders][Order Details] - Node Order Details[54235be1-b7ab-4dfb-a936-44edb60b5eee] close complete, cost 24 ms 
[INFO ] 2024-03-29 14:23:50.437 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] running status set to false 
[INFO ] 2024-03-29 14:23:50.437 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.437 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] monitor closed 
[INFO ] 2024-03-29 14:23:50.437 - [orders][Rename Order Details] - Node Rename Order Details[6d4acd46-de71-4913-8bb1-58dfa9d35a0b] close complete, cost 83 ms 
[INFO ] 2024-03-29 14:23:50.481 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] running status set to false 
[INFO ] 2024-03-29 14:23:50.481 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.482 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] monitor closed 
[INFO ] 2024-03-29 14:23:50.486 - [orders][Delete Order Details] - Node Delete Order Details[857431f1-77d7-44dc-9ac9-be0aa3eddd2d] close complete, cost 44 ms 
[INFO ] 2024-03-29 14:23:50.486 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] running status set to false 
[INFO ] 2024-03-29 14:23:50.525 - [orders][Orders] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 14:23:50.525 - [orders][Orders] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 14:23:50.529 - [orders][Orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:23:50.529 - [orders][Orders] - PDK connector node released: HazelcastSourcePdkDataNode-85125c33-6534-44e5-b9d0-74f61b2b2f84 
[INFO ] 2024-03-29 14:23:50.530 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.530 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] monitor closed 
[INFO ] 2024-03-29 14:23:50.531 - [orders][Orders] - Node Orders[85125c33-6534-44e5-b9d0-74f61b2b2f84] close complete, cost 47 ms 
[INFO ] 2024-03-29 14:23:50.531 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] running status set to false 
[INFO ] 2024-03-29 14:23:50.572 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.572 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] monitor closed 
[INFO ] 2024-03-29 14:23:50.572 - [orders][Rename Orders] - Node Rename Orders[691d4b02-d308-4463-bb87-8bf921ca11bf] close complete, cost 41 ms 
[INFO ] 2024-03-29 14:23:50.572 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] running status set to false 
[INFO ] 2024-03-29 14:23:50.614 - [orders][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 14:23:50.620 - [orders][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 14:23:50.627 - [orders][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:23:50.627 - [orders][Products] - PDK connector node released: HazelcastSourcePdkDataNode-ee57034e-0832-4775-81d0-5a786d6f5178 
[INFO ] 2024-03-29 14:23:50.627 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.627 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] monitor closed 
[INFO ] 2024-03-29 14:23:50.627 - [orders][Products] - Node Products[ee57034e-0832-4775-81d0-5a786d6f5178] close complete, cost 55 ms 
[INFO ] 2024-03-29 14:23:50.627 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] running status set to false 
[INFO ] 2024-03-29 14:23:50.665 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.665 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] monitor closed 
[INFO ] 2024-03-29 14:23:50.665 - [orders][Rename Products] - Node Rename Products[6a77f696-4c58-4aec-8862-7d1bc0dc321a] close complete, cost 37 ms 
[INFO ] 2024-03-29 14:23:50.665 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] running status set to false 
[INFO ] 2024-03-29 14:23:50.701 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.701 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] monitor closed 
[INFO ] 2024-03-29 14:23:50.702 - [orders][Delete Products] - Node Delete Products[84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded] close complete, cost 36 ms 
[INFO ] 2024-03-29 14:23:50.702 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] running status set to false 
[INFO ] 2024-03-29 14:23:50.705 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_84cdc6a6-2c5f-4a4c-9a11-c55db57d8ded__TPORIG 
[INFO ] 2024-03-29 14:23:50.706 - [orders][merge] - Destroy merge cache resource: HazelcastMergeNode_Order Details_857431f1-77d7-44dc-9ac9-be0aa3eddd2d__TPORIG 
[INFO ] 2024-03-29 14:23:50.709 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.709 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] monitor closed 
[INFO ] 2024-03-29 14:23:50.709 - [orders][merge] - Node merge[e814f5be-1c7e-479f-a51c-0be972161449] close complete, cost 7 ms 
[INFO ] 2024-03-29 14:23:50.709 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] running status set to false 
[INFO ] 2024-03-29 14:23:50.726 - [orders][orders] - PDK connector node stopped: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:23:50.726 - [orders][orders] - PDK connector node released: HazelcastTargetPdkDataNode-428ddd60-f644-4f0d-b742-20827ecf2136 
[INFO ] 2024-03-29 14:23:50.726 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] schema data cleaned 
[INFO ] 2024-03-29 14:23:50.726 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] monitor closed 
[INFO ] 2024-03-29 14:23:50.928 - [orders][orders] - Node orders[428ddd60-f644-4f0d-b742-20827ecf2136] close complete, cost 16 ms 
[INFO ] 2024-03-29 14:23:53.173 - [orders] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 14:23:53.173 - [orders] - Stopped task aspect(s) 
[INFO ] 2024-03-29 14:23:53.173 - [orders] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 14:23:53.211 - [orders] - Remove memory task client succeed, task: orders[66065af52bfc1e544b39a61f] 
[INFO ] 2024-03-29 14:23:53.212 - [orders] - Destroy memory task client cache succeed, task: orders[66065af52bfc1e544b39a61f] 
