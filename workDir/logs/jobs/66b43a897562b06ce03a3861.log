[INFO ] 2024-08-08 11:27:06.097 - [任务 1] - Task initialization... 
[INFO ] 2024-08-08 11:27:06.098 - [任务 1] - Start task milestones: 66b43a897562b06ce03a3861(任务 1) 
[INFO ] 2024-08-08 11:27:06.145 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-08 11:27:06.351 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-08 11:27:06.754 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:27:06.756 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:27:06.848 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] preload schema finished, cost 95 ms 
[INFO ] 2024-08-08 11:27:07.050 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] preload schema finished, cost 96 ms 
[INFO ] 2024-08-08 11:27:08.274 - [任务 1][TestSpace] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-08 11:27:08.521 - [任务 1][TESTSPACE] - Source node "TESTSPACE" read batch size: 100 
[INFO ] 2024-08-08 11:27:08.524 - [任务 1][TESTSPACE] - Source node "TESTSPACE" event queue capacity: 200 
[INFO ] 2024-08-08 11:27:08.524 - [任务 1][TESTSPACE] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-08 11:27:09.180 - [任务 1][TESTSPACE] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 92163492 
[INFO ] 2024-08-08 11:27:09.567 - [任务 1][TESTSPACE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:27:09.725 - [任务 1][TESTSPACE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-08 11:27:09.725 - [任务 1][TESTSPACE] - Initial sync started 
[INFO ] 2024-08-08 11:27:09.740 - [任务 1][TESTSPACE] - Starting batch read, table name: TESTSPACE, offset: null 
[INFO ] 2024-08-08 11:27:09.744 - [任务 1][TESTSPACE] - Table TESTSPACE is going to be initial synced 
[INFO ] 2024-08-08 11:27:09.839 - [任务 1][TESTSPACE] - Query table 'TESTSPACE' counts: 0 
[INFO ] 2024-08-08 11:27:09.839 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:27:09.841 - [任务 1][TESTSPACE] - Incremental sync starting... 
[INFO ] 2024-08-08 11:27:09.842 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:27:10.008 - [任务 1][TESTSPACE] - Starting stream read, table list: [TESTSPACE], offset: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:27:10.010 - [任务 1][TESTSPACE] - total start mining scn: 92163491 
[INFO ] 2024-08-08 11:27:11.761 - [任务 1][TESTSPACE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-08 11:27:51.492 - [任务 1] - Stop task milestones: 66b43a897562b06ce03a3861(任务 1)  
[INFO ] 2024-08-08 11:27:51.576 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] running status set to false 
[INFO ] 2024-08-08 11:27:51.576 - [任务 1][TESTSPACE] - Log Miner is shutting down... 
[INFO ] 2024-08-08 11:27:51.659 - [任务 1][TESTSPACE] - Log Miner has been closed! 
[ERROR] 2024-08-08 11:27:51.665 - [任务 1][TESTSPACE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:714)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:735)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-08-08 11:27:51.843 - [任务 1][TESTSPACE] - PDK connector node stopped: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:27:51.846 - [任务 1][TESTSPACE] - PDK connector node released: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:27:51.846 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] schema data cleaned 
[INFO ] 2024-08-08 11:27:51.851 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] monitor closed 
[INFO ] 2024-08-08 11:27:51.851 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] close complete, cost 295 ms 
[INFO ] 2024-08-08 11:27:51.894 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] running status set to false 
[INFO ] 2024-08-08 11:27:51.894 - [任务 1][TestSpace] - PDK connector node stopped: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:27:51.896 - [任务 1][TestSpace] - PDK connector node released: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:27:51.896 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] schema data cleaned 
[INFO ] 2024-08-08 11:27:51.897 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] monitor closed 
[INFO ] 2024-08-08 11:27:51.897 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] close complete, cost 45 ms 
[INFO ] 2024-08-08 11:27:55.654 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-08 11:27:55.654 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-08 11:27:55.712 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-08 11:27:55.712 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b43a897562b06ce03a3861] 
[INFO ] 2024-08-08 11:27:55.712 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b43a897562b06ce03a3861] 
[INFO ] 2024-08-08 11:28:43.160 - [任务 1] - Task initialization... 
[INFO ] 2024-08-08 11:28:43.161 - [任务 1] - Start task milestones: 66b43a897562b06ce03a3861(任务 1) 
[INFO ] 2024-08-08 11:28:43.227 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-08 11:28:43.227 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-08 11:28:43.265 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:28:43.265 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:28:43.294 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] preload schema finished, cost 30 ms 
[INFO ] 2024-08-08 11:28:43.294 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] preload schema finished, cost 28 ms 
[INFO ] 2024-08-08 11:28:44.337 - [任务 1][TestSpace] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-08 11:28:44.539 - [任务 1][TestSpace] - The table TestSpace has already exist. 
[INFO ] 2024-08-08 11:28:44.888 - [任务 1][TESTSPACE] - Source node "TESTSPACE" read batch size: 100 
[INFO ] 2024-08-08 11:28:44.891 - [任务 1][TESTSPACE] - Source node "TESTSPACE" event queue capacity: 200 
[INFO ] 2024-08-08 11:28:44.891 - [任务 1][TESTSPACE] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-08 11:28:45.696 - [任务 1][TESTSPACE] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 92163492 
[INFO ] 2024-08-08 11:28:46.070 - [任务 1][TESTSPACE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:28:46.071 - [任务 1][TESTSPACE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-08 11:28:46.142 - [任务 1][TESTSPACE] - Initial sync started 
[INFO ] 2024-08-08 11:28:46.149 - [任务 1][TESTSPACE] - Starting batch read, table name: TESTSPACE, offset: null 
[INFO ] 2024-08-08 11:28:46.149 - [任务 1][TESTSPACE] - Table TESTSPACE is going to be initial synced 
[INFO ] 2024-08-08 11:28:46.334 - [任务 1][TESTSPACE] - Query table 'TESTSPACE' counts: 0 
[INFO ] 2024-08-08 11:28:46.334 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:28:46.335 - [任务 1][TESTSPACE] - Incremental sync starting... 
[INFO ] 2024-08-08 11:28:46.335 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:28:46.459 - [任务 1][TESTSPACE] - Starting stream read, table list: [TESTSPACE], offset: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:28:46.460 - [任务 1][TESTSPACE] - total start mining scn: 92163491 
[INFO ] 2024-08-08 11:28:47.854 - [任务 1][TESTSPACE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-08 11:29:26.428 - [任务 1] - Stop task milestones: 66b43a897562b06ce03a3861(任务 1)  
[INFO ] 2024-08-08 11:29:26.482 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] running status set to false 
[INFO ] 2024-08-08 11:29:26.502 - [任务 1][TESTSPACE] - Log Miner is shutting down... 
[INFO ] 2024-08-08 11:29:26.502 - [任务 1][TESTSPACE] - Log Miner has been closed! 
[ERROR] 2024-08-08 11:29:26.531 - [任务 1][TESTSPACE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:714)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:735)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-08-08 11:29:26.738 - [任务 1][TESTSPACE] - PDK connector node stopped: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:29:26.738 - [任务 1][TESTSPACE] - PDK connector node released: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:29:26.741 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] schema data cleaned 
[INFO ] 2024-08-08 11:29:26.741 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] monitor closed 
[INFO ] 2024-08-08 11:29:26.765 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] close complete, cost 262 ms 
[INFO ] 2024-08-08 11:29:26.766 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] running status set to false 
[INFO ] 2024-08-08 11:29:26.768 - [任务 1][TestSpace] - PDK connector node stopped: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:29:26.768 - [任务 1][TestSpace] - PDK connector node released: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:29:26.770 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] schema data cleaned 
[INFO ] 2024-08-08 11:29:26.770 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] monitor closed 
[INFO ] 2024-08-08 11:29:26.771 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] close complete, cost 26 ms 
[INFO ] 2024-08-08 11:29:30.814 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-08 11:29:30.814 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-08 11:29:30.814 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-08 11:29:30.848 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b43a897562b06ce03a3861] 
[INFO ] 2024-08-08 11:29:30.850 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b43a897562b06ce03a3861] 
[INFO ] 2024-08-08 11:29:35.864 - [任务 1] - Task initialization... 
[INFO ] 2024-08-08 11:29:35.865 - [任务 1] - Start task milestones: 66b43a897562b06ce03a3861(任务 1) 
[INFO ] 2024-08-08 11:29:35.909 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-08 11:29:36.269 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-08 11:29:36.271 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:29:36.271 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:29:36.320 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] preload schema finished, cost 40 ms 
[INFO ] 2024-08-08 11:29:36.321 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] preload schema finished, cost 40 ms 
[INFO ] 2024-08-08 11:29:37.047 - [任务 1][TestSpace] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-08 11:29:37.048 - [任务 1][TestSpace] - The table TestSpace has already exist. 
[INFO ] 2024-08-08 11:29:37.919 - [任务 1][TESTSPACE] - Source node "TESTSPACE" read batch size: 100 
[INFO ] 2024-08-08 11:29:37.919 - [任务 1][TESTSPACE] - Source node "TESTSPACE" event queue capacity: 200 
[INFO ] 2024-08-08 11:29:37.920 - [任务 1][TESTSPACE] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-08 11:29:38.405 - [任务 1][TESTSPACE] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 92163492 
[INFO ] 2024-08-08 11:29:38.933 - [任务 1][TESTSPACE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:29:38.933 - [任务 1][TESTSPACE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-08 11:29:38.989 - [任务 1][TESTSPACE] - Initial sync started 
[INFO ] 2024-08-08 11:29:38.994 - [任务 1][TESTSPACE] - Starting batch read, table name: TESTSPACE, offset: null 
[INFO ] 2024-08-08 11:29:38.994 - [任务 1][TESTSPACE] - Table TESTSPACE is going to be initial synced 
[INFO ] 2024-08-08 11:29:39.047 - [任务 1][TESTSPACE] - Query table 'TESTSPACE' counts: 0 
[INFO ] 2024-08-08 11:29:39.047 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:29:39.052 - [任务 1][TESTSPACE] - Incremental sync starting... 
[INFO ] 2024-08-08 11:29:39.052 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:29:39.255 - [任务 1][TESTSPACE] - Starting stream read, table list: [TESTSPACE], offset: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:29:39.334 - [任务 1][TESTSPACE] - total start mining scn: 92163491 
[INFO ] 2024-08-08 11:29:40.753 - [任务 1][TESTSPACE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-08 11:30:07.558 - [任务 1] - Stop task milestones: 66b43a897562b06ce03a3861(任务 1)  
[INFO ] 2024-08-08 11:30:08.111 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] running status set to false 
[INFO ] 2024-08-08 11:30:08.112 - [任务 1][TESTSPACE] - Log Miner is shutting down... 
[INFO ] 2024-08-08 11:30:08.127 - [任务 1][TESTSPACE] - Log Miner has been closed! 
[ERROR] 2024-08-08 11:30:08.129 - [任务 1][TESTSPACE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:714)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:735)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-08-08 11:30:08.196 - [任务 1][TESTSPACE] - PDK connector node stopped: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:30:08.200 - [任务 1][TESTSPACE] - PDK connector node released: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:30:08.201 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] schema data cleaned 
[INFO ] 2024-08-08 11:30:08.203 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] monitor closed 
[INFO ] 2024-08-08 11:30:08.206 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] close complete, cost 202 ms 
[INFO ] 2024-08-08 11:30:08.207 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] running status set to false 
[INFO ] 2024-08-08 11:30:08.216 - [任务 1][TestSpace] - PDK connector node stopped: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:30:08.216 - [任务 1][TestSpace] - PDK connector node released: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:30:08.216 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] schema data cleaned 
[INFO ] 2024-08-08 11:30:08.217 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] monitor closed 
[INFO ] 2024-08-08 11:30:08.218 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] close complete, cost 11 ms 
[INFO ] 2024-08-08 11:30:10.899 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-08 11:30:10.899 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-08 11:30:10.899 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-08 11:30:10.935 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b43a897562b06ce03a3861] 
[INFO ] 2024-08-08 11:30:10.936 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b43a897562b06ce03a3861] 
[INFO ] 2024-08-08 11:30:27.501 - [任务 1] - Task initialization... 
[INFO ] 2024-08-08 11:30:27.502 - [任务 1] - Start task milestones: 66b43a897562b06ce03a3861(任务 1) 
[INFO ] 2024-08-08 11:30:27.509 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-08 11:30:27.602 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-08 11:30:27.603 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:30:27.603 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] start preload schema,table counts: 1 
[INFO ] 2024-08-08 11:30:27.649 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] preload schema finished, cost 41 ms 
[INFO ] 2024-08-08 11:30:27.649 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] preload schema finished, cost 50 ms 
[INFO ] 2024-08-08 11:30:28.703 - [任务 1][TestSpace] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-08 11:30:28.704 - [任务 1][TestSpace] - The table TestSpace has already exist. 
[INFO ] 2024-08-08 11:30:29.265 - [任务 1][TESTSPACE] - Source node "TESTSPACE" read batch size: 100 
[INFO ] 2024-08-08 11:30:29.265 - [任务 1][TESTSPACE] - Source node "TESTSPACE" event queue capacity: 200 
[INFO ] 2024-08-08 11:30:29.265 - [任务 1][TESTSPACE] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-08 11:30:29.860 - [任务 1][TESTSPACE] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 92163492 
[INFO ] 2024-08-08 11:30:30.348 - [任务 1][TESTSPACE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:30:30.428 - [任务 1][TESTSPACE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-08 11:30:30.428 - [任务 1][TESTSPACE] - Initial sync started 
[INFO ] 2024-08-08 11:30:30.436 - [任务 1][TESTSPACE] - Starting batch read, table name: TESTSPACE, offset: null 
[INFO ] 2024-08-08 11:30:30.436 - [任务 1][TESTSPACE] - Table TESTSPACE is going to be initial synced 
[INFO ] 2024-08-08 11:31:09.740 - [任务 1][TESTSPACE] - Query table 'TESTSPACE' counts: 1 
[INFO ] 2024-08-08 11:31:09.742 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:31:09.742 - [任务 1][TESTSPACE] - Incremental sync starting... 
[INFO ] 2024-08-08 11:31:09.742 - [任务 1][TESTSPACE] - Initial sync completed 
[INFO ] 2024-08-08 11:31:09.742 - [任务 1][TESTSPACE] - Starting stream read, table list: [TESTSPACE], offset: {"sortString":null,"offsetValue":null,"lastScn":92163491,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 11:31:10.348 - [任务 1][TESTSPACE] - total start mining scn: 92163491 
[INFO ] 2024-08-08 11:31:11.524 - [任务 1][TESTSPACE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-08 11:36:02.835 - [任务 1] - Stop task milestones: 66b43a897562b06ce03a3861(任务 1)  
[INFO ] 2024-08-08 11:36:02.966 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] running status set to false 
[INFO ] 2024-08-08 11:36:03.153 - [任务 1][TESTSPACE] - Log Miner is shutting down... 
[INFO ] 2024-08-08 11:36:03.320 - [任务 1][TESTSPACE] - Log Miner has been closed! 
[ERROR] 2024-08-08 11:36:03.325 - [任务 1][TESTSPACE] - Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:714)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:735)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 92165962 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTSPACE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-08-08 11:36:03.582 - [任务 1][TESTSPACE] - PDK connector node stopped: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:36:03.582 - [任务 1][TESTSPACE] - PDK connector node released: HazelcastSourcePdkDataNode-597c6218-c44b-4e50-b873-2c57a220a1ed 
[INFO ] 2024-08-08 11:36:03.582 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] schema data cleaned 
[INFO ] 2024-08-08 11:36:03.582 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] monitor closed 
[INFO ] 2024-08-08 11:36:03.585 - [任务 1][TESTSPACE] - Node TESTSPACE[597c6218-c44b-4e50-b873-2c57a220a1ed] close complete, cost 621 ms 
[INFO ] 2024-08-08 11:36:03.585 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] running status set to false 
[INFO ] 2024-08-08 11:36:03.608 - [任务 1][TestSpace] - PDK connector node stopped: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:36:03.608 - [任务 1][TestSpace] - PDK connector node released: HazelcastTargetPdkDataNode-52abbc88-3ba2-436e-935d-ba18e9161441 
[INFO ] 2024-08-08 11:36:03.609 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] schema data cleaned 
[INFO ] 2024-08-08 11:36:03.609 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] monitor closed 
[INFO ] 2024-08-08 11:36:03.814 - [任务 1][TestSpace] - Node TestSpace[52abbc88-3ba2-436e-935d-ba18e9161441] close complete, cost 25 ms 
[INFO ] 2024-08-08 11:36:06.797 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-08 11:36:06.799 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-08 11:36:06.800 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-08 11:36:06.834 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b43a897562b06ce03a3861] 
[INFO ] 2024-08-08 11:36:06.838 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b43a897562b06ce03a3861] 
