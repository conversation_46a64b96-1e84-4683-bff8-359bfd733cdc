[INFO ] 2024-07-18 12:00:20.151 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:20.151 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:20.151 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][a10a586a-4de7-484b-845b-9df18904eb8a] - Node a10a586a-4de7-484b-845b-9df18904eb8a[a10a586a-4de7-484b-845b-9df18904eb8a] start preload schema,table counts: 0 
[INFO ] 2024-07-18 12:00:20.151 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:00:20.151 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][a10a586a-4de7-484b-845b-9df18904eb8a] - Node a10a586a-4de7-484b-845b-9df18904eb8a[a10a586a-4de7-484b-845b-9df18904eb8a] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:00:20.151 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:00:20.343 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:20.343 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][3e487d51-a29a-486d-bfa2-b86782b30137] - Node 3e487d51-a29a-486d-bfa2-b86782b30137[3e487d51-a29a-486d-bfa2-b86782b30137] start preload schema,table counts: 0 
[INFO ] 2024-07-18 12:00:20.343 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:20.343 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][3e487d51-a29a-486d-bfa2-b86782b30137] - Node 3e487d51-a29a-486d-bfa2-b86782b30137[3e487d51-a29a-486d-bfa2-b86782b30137] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:00:20.343 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:00:20.343 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:00:20.896 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 
[ERROR] 2024-07-18 12:00:20.897 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - start source runner failed: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-07-18 12:00:21.052 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] running status set to false 
[INFO ] 2024-07-18 12:00:21.052 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 
[INFO ] 2024-07-18 12:00:21.052 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 
[INFO ] 2024-07-18 12:00:21.052 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] schema data cleaned 
[INFO ] 2024-07-18 12:00:21.052 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] monitor closed 
[INFO ] 2024-07-18 12:00:21.257 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] close complete, cost 41 ms 
[INFO ] 2024-07-18 12:00:21.411 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] running status set to false 
[INFO ] 2024-07-18 12:00:21.411 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][a10a586a-4de7-484b-845b-9df18904eb8a] - Node a10a586a-4de7-484b-845b-9df18904eb8a[a10a586a-4de7-484b-845b-9df18904eb8a] running status set to false 
[INFO ] 2024-07-18 12:00:21.411 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][a10a586a-4de7-484b-845b-9df18904eb8a] - Node a10a586a-4de7-484b-845b-9df18904eb8a[a10a586a-4de7-484b-845b-9df18904eb8a] schema data cleaned 
[INFO ] 2024-07-18 12:00:21.412 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][a10a586a-4de7-484b-845b-9df18904eb8a] - Node a10a586a-4de7-484b-845b-9df18904eb8a[a10a586a-4de7-484b-845b-9df18904eb8a] monitor closed 
[INFO ] 2024-07-18 12:00:21.412 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][a10a586a-4de7-484b-845b-9df18904eb8a] - Node a10a586a-4de7-484b-845b-9df18904eb8a[a10a586a-4de7-484b-845b-9df18904eb8a] close complete, cost 2 ms 
[INFO ] 2024-07-18 12:00:21.414 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-43bc25d0-db0b-4d97-9c2b-fa237d11217c 
[INFO ] 2024-07-18 12:00:21.414 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - PDK connector node released: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-43bc25d0-db0b-4d97-9c2b-fa237d11217c 
[INFO ] 2024-07-18 12:00:21.419 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - [ScriptExecutorsManager-669893538315b25db9f54457-1168de21-27e5-42a3-a5b8-c29383206f39-6697a5dfb92eda1a86f5248f] schema data cleaned 
[INFO ] 2024-07-18 12:00:21.419 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] schema data cleaned 
[INFO ] 2024-07-18 12:00:21.419 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] monitor closed 
[INFO ] 2024-07-18 12:00:21.419 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] close complete, cost 25 ms 
[INFO ] 2024-07-18 12:00:21.432 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-18 12:00:21.432 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-18 12:00:21.433 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:00:23.446 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] running status set to false 
[INFO ] 2024-07-18 12:00:23.446 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] running status set to false 
[INFO ] 2024-07-18 12:00:23.448 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: null 
[INFO ] 2024-07-18 12:00:23.467 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: null 
[INFO ] 2024-07-18 12:00:23.467 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][3e487d51-a29a-486d-bfa2-b86782b30137] - Node 3e487d51-a29a-486d-bfa2-b86782b30137[3e487d51-a29a-486d-bfa2-b86782b30137] running status set to false 
[INFO ] 2024-07-18 12:00:23.467 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] schema data cleaned 
[INFO ] 2024-07-18 12:00:23.467 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-2e1f8b47-1bc4-46a7-a568-dc492565b239 
[INFO ] 2024-07-18 12:00:23.467 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] monitor closed 
[INFO ] 2024-07-18 12:00:23.467 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][3e487d51-a29a-486d-bfa2-b86782b30137] - Node 3e487d51-a29a-486d-bfa2-b86782b30137[3e487d51-a29a-486d-bfa2-b86782b30137] schema data cleaned 
[INFO ] 2024-07-18 12:00:23.467 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - PDK connector node released: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-2e1f8b47-1bc4-46a7-a568-dc492565b239 
[INFO ] 2024-07-18 12:00:23.468 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - [ScriptExecutorsManager-669893538315b25db9f54457-1168de21-27e5-42a3-a5b8-c29383206f39-6697a5dfb92eda1a86f5248f] schema data cleaned 
[INFO ] 2024-07-18 12:00:23.468 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][3e487d51-a29a-486d-bfa2-b86782b30137] - Node 3e487d51-a29a-486d-bfa2-b86782b30137[3e487d51-a29a-486d-bfa2-b86782b30137] monitor closed 
[INFO ] 2024-07-18 12:00:23.468 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] close complete, cost 23 ms 
[INFO ] 2024-07-18 12:00:23.468 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][3e487d51-a29a-486d-bfa2-b86782b30137] - Node 3e487d51-a29a-486d-bfa2-b86782b30137[3e487d51-a29a-486d-bfa2-b86782b30137] close complete, cost 14 ms 
[INFO ] 2024-07-18 12:00:23.470 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] schema data cleaned 
[INFO ] 2024-07-18 12:00:23.470 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] monitor closed 
[INFO ] 2024-07-18 12:00:23.470 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] close complete, cost 23 ms 
[INFO ] 2024-07-18 12:00:23.471 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-18 12:00:23.471 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-18 12:00:23.673 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:00:31.162 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:31.162 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:31.163 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][946ccad3-b829-435b-bcda-2074cc299a0e] - Node 946ccad3-b829-435b-bcda-2074cc299a0e[946ccad3-b829-435b-bcda-2074cc299a0e] start preload schema,table counts: 0 
[INFO ] 2024-07-18 12:00:31.163 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][946ccad3-b829-435b-bcda-2074cc299a0e] - Node 946ccad3-b829-435b-bcda-2074cc299a0e[946ccad3-b829-435b-bcda-2074cc299a0e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:00:31.163 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:00:31.163 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:00:31.627 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] running status set to false 
[INFO ] 2024-07-18 12:00:31.627 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 
[INFO ] 2024-07-18 12:00:31.627 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastSampleSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 
[INFO ] 2024-07-18 12:00:31.627 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] schema data cleaned 
[INFO ] 2024-07-18 12:00:31.627 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] monitor closed 
[INFO ] 2024-07-18 12:00:31.723 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] close complete, cost 28 ms 
[INFO ] 2024-07-18 12:00:31.725 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] running status set to false 
[INFO ] 2024-07-18 12:00:31.727 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][946ccad3-b829-435b-bcda-2074cc299a0e] - Node 946ccad3-b829-435b-bcda-2074cc299a0e[946ccad3-b829-435b-bcda-2074cc299a0e] running status set to false 
[INFO ] 2024-07-18 12:00:31.727 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][946ccad3-b829-435b-bcda-2074cc299a0e] - Node 946ccad3-b829-435b-bcda-2074cc299a0e[946ccad3-b829-435b-bcda-2074cc299a0e] schema data cleaned 
[INFO ] 2024-07-18 12:00:31.727 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][946ccad3-b829-435b-bcda-2074cc299a0e] - Node 946ccad3-b829-435b-bcda-2074cc299a0e[946ccad3-b829-435b-bcda-2074cc299a0e] monitor closed 
[INFO ] 2024-07-18 12:00:31.728 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][946ccad3-b829-435b-bcda-2074cc299a0e] - Node 946ccad3-b829-435b-bcda-2074cc299a0e[946ccad3-b829-435b-bcda-2074cc299a0e] close complete, cost 2 ms 
[INFO ] 2024-07-18 12:00:31.729 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-01a9f398-289e-4100-babb-e6ab3474682a 
[INFO ] 2024-07-18 12:00:31.729 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - PDK connector node released: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-01a9f398-289e-4100-babb-e6ab3474682a 
[INFO ] 2024-07-18 12:00:31.730 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - [ScriptExecutorsManager-669893538315b25db9f54457-1168de21-27e5-42a3-a5b8-c29383206f39-6697a5dfb92eda1a86f5248f] schema data cleaned 
[INFO ] 2024-07-18 12:00:31.732 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] schema data cleaned 
[INFO ] 2024-07-18 12:00:31.733 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] monitor closed 
[INFO ] 2024-07-18 12:00:31.733 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] close complete, cost 20 ms 
[INFO ] 2024-07-18 12:00:31.735 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-18 12:00:31.735 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-18 12:00:31.952 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188(100)] - Stopped task aspect(s) 
