[INFO ] 2024-06-19 11:42:10.022 - [任务 73] - Start task milestones: 65ee24a8b69548427b2fd175(任务 73) 
[INFO ] 2024-06-19 11:42:10.023 - [任务 73] - Task initialization... 
[INFO ] 2024-06-19 11:42:11.277 - [任务 73] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-19 11:42:11.521 - [任务 73] - The engine receives 任务 73 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-19 11:42:12.255 - [任务 73][Unwind] - Node Unwind[29274a15-1f44-427c-a371-af7223f331ae] start preload schema,table counts: 1 
[INFO ] 2024-06-19 11:42:12.267 - [任务 73][Unwind] - Node Unwind[29274a15-1f44-427c-a371-af7223f331ae] preload schema finished, cost 0 ms 
[INFO ] 2024-06-19 11:42:12.293 - [任务 73][CLAIM] - Node CLAIM[68fff686-b924-4e1a-b1b8-47af5307b554] start preload schema,table counts: 1 
[INFO ] 2024-06-19 11:42:12.294 - [任务 73][test12] - Node test12[9903eb2c-3599-45e4-983b-a75b74583c86] start preload schema,table counts: 1 
[INFO ] 2024-06-19 11:42:12.294 - [任务 73][test12] - Node test12[9903eb2c-3599-45e4-983b-a75b74583c86] preload schema finished, cost 0 ms 
[INFO ] 2024-06-19 11:42:12.295 - [任务 73][CLAIM] - Node CLAIM[68fff686-b924-4e1a-b1b8-47af5307b554] preload schema finished, cost 0 ms 
[INFO ] 2024-06-19 11:42:12.506 - [任务 73][test12] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_9903eb2c-3599-45e4-983b-a75b74583c86 
[INFO ] 2024-06-19 11:42:12.510 - [任务 73][CLAIM] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2822/1059273955@212578c7 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 
[ERROR] 2024-06-19 11:42:12.572 - [任务 73][CLAIM] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2822/1059273955@212578c7 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2822/1059273955@212578c7 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2822/1059273955@212578c7 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2822/1059273955@212578c7 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_68fff686-b924-4e1a-b1b8-47af5307b554
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[ERROR] 2024-06-19 11:42:12.574 - [任务 73][test12] - Map name: PdkStateMap_9903eb2c-3599-45e4-983b-a75b74583c86 <-- Error Message -->
Map name: PdkStateMap_9903eb2c-3599-45e4-983b-a75b74583c86

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_9903eb2c-3599-45e4-983b-a75b74583c86
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718768531, "i" : 3 } }, "signature" : { "hash" : { "$binary" : "Q0x5hPeOaaG3GiA5FZIqABe9fM8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more

[INFO ] 2024-06-19 11:42:12.615 - [任务 73][test12] - Job suspend in error handle 
[INFO ] 2024-06-19 11:42:12.615 - [任务 73][CLAIM] - Job suspend in error handle 
[INFO ] 2024-06-19 11:42:12.660 - [任务 73][CLAIM] - Node CLAIM[68fff686-b924-4e1a-b1b8-47af5307b554] running status set to false 
[INFO ] 2024-06-19 11:42:12.662 - [任务 73][CLAIM] - PDK connector node stopped: null 
[INFO ] 2024-06-19 11:42:12.663 - [任务 73][CLAIM] - PDK connector node released: null 
[INFO ] 2024-06-19 11:42:12.663 - [任务 73][CLAIM] - Node CLAIM[68fff686-b924-4e1a-b1b8-47af5307b554] schema data cleaned 
[INFO ] 2024-06-19 11:42:12.665 - [任务 73][CLAIM] - Node CLAIM[68fff686-b924-4e1a-b1b8-47af5307b554] monitor closed 
[INFO ] 2024-06-19 11:42:12.682 - [任务 73][CLAIM] - Node CLAIM[68fff686-b924-4e1a-b1b8-47af5307b554] close complete, cost 21 ms 
[INFO ] 2024-06-19 11:42:12.687 - [任务 73][Unwind] - Node Unwind[29274a15-1f44-427c-a371-af7223f331ae] running status set to false 
[INFO ] 2024-06-19 11:42:12.690 - [任务 73][Unwind] - Node Unwind[29274a15-1f44-427c-a371-af7223f331ae] schema data cleaned 
[INFO ] 2024-06-19 11:42:12.690 - [任务 73][Unwind] - Node Unwind[29274a15-1f44-427c-a371-af7223f331ae] monitor closed 
[INFO ] 2024-06-19 11:42:12.693 - [任务 73][Unwind] - Node Unwind[29274a15-1f44-427c-a371-af7223f331ae] close complete, cost 7 ms 
[INFO ] 2024-06-19 11:42:12.694 - [任务 73][test12] - Node test12[9903eb2c-3599-45e4-983b-a75b74583c86] running status set to false 
[INFO ] 2024-06-19 11:42:12.696 - [任务 73][test12] - PDK connector node stopped: null 
[INFO ] 2024-06-19 11:42:12.697 - [任务 73][test12] - PDK connector node released: null 
[INFO ] 2024-06-19 11:42:12.698 - [任务 73][test12] - Node test12[9903eb2c-3599-45e4-983b-a75b74583c86] schema data cleaned 
[INFO ] 2024-06-19 11:42:12.699 - [任务 73][test12] - Node test12[9903eb2c-3599-45e4-983b-a75b74583c86] monitor closed 
[INFO ] 2024-06-19 11:42:12.699 - [任务 73][test12] - Node test12[9903eb2c-3599-45e4-983b-a75b74583c86] close complete, cost 8 ms 
[INFO ] 2024-06-19 11:42:17.616 - [任务 73] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-19 11:42:17.619 - [任务 73] - Stop task milestones: 65ee24a8b69548427b2fd175(任务 73)  
[INFO ] 2024-06-19 11:42:17.643 - [任务 73] - Stopped task aspect(s) 
[INFO ] 2024-06-19 11:42:17.644 - [任务 73] - Snapshot order controller have been removed 
[INFO ] 2024-06-19 11:42:17.681 - [任务 73] - Remove memory task client succeed, task: 任务 73[65ee24a8b69548427b2fd175] 
[INFO ] 2024-06-19 11:42:17.681 - [任务 73] - Destroy memory task client cache succeed, task: 任务 73[65ee24a8b69548427b2fd175] 
