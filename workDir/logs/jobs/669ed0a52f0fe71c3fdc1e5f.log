[INFO ] 2024-07-23 05:36:09.827 - [任务 12] - Task initialization... 
[INFO ] 2024-07-23 05:36:09.829 - [任务 12] - Start task milestones: 669ed0a52f0fe71c3fdc1e5f(任务 12) 
[INFO ] 2024-07-23 05:36:10.022 - [任务 12] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 05:36:10.023 - [任务 12] - The engine receives 任务 12 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 05:36:10.079 - [任务 12][POLICY] - Node POLICY[11665532-7d49-44da-83c6-46483bd512b0] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:36:10.079 - [任务 12][Tes] - Node Tes[1c6cd9c9-eed4-4fdb-9347-644723e36e57] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:36:10.079 - [任务 12][POLICY] - Node POLICY[11665532-7d49-44da-83c6-46483bd512b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:36:10.079 - [任务 12][Tes] - Node Tes[1c6cd9c9-eed4-4fdb-9347-644723e36e57] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:36:10.889 - [任务 12][Tes] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-23 05:36:11.364 - [任务 12][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-23 05:36:11.364 - [任务 12][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-23 05:36:11.364 - [任务 12][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 05:36:11.520 - [任务 12][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721684171,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-23 05:36:11.578 - [任务 12][POLICY] - Initial sync started 
[INFO ] 2024-07-23 05:36:11.579 - [任务 12][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-23 05:36:11.579 - [任务 12][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-23 05:36:11.663 - [任务 12][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 05:36:11.663 - [任务 12][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-23 05:36:11.664 - [任务 12][POLICY] - Initial sync completed 
[INFO ] 2024-07-23 05:36:11.664 - [任务 12][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-23 05:36:11.664 - [任务 12][POLICY] - Initial sync completed 
[INFO ] 2024-07-23 05:36:11.669 - [任务 12][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721684171,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-23 05:36:11.669 - [任务 12][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 05:36:26.784 - [任务 12][POLICY] - Node POLICY[11665532-7d49-44da-83c6-46483bd512b0] running status set to false 
[INFO ] 2024-07-23 05:36:26.816 - [任务 12][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-11665532-7d49-44da-83c6-46483bd512b0 
[INFO ] 2024-07-23 05:36:26.816 - [任务 12][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-11665532-7d49-44da-83c6-46483bd512b0 
[INFO ] 2024-07-23 05:36:26.816 - [任务 12][POLICY] - Node POLICY[11665532-7d49-44da-83c6-46483bd512b0] schema data cleaned 
[INFO ] 2024-07-23 05:36:26.816 - [任务 12][POLICY] - Node POLICY[11665532-7d49-44da-83c6-46483bd512b0] monitor closed 
[INFO ] 2024-07-23 05:36:26.817 - [任务 12][POLICY] - Node POLICY[11665532-7d49-44da-83c6-46483bd512b0] close complete, cost 34 ms 
[INFO ] 2024-07-23 05:36:26.820 - [任务 12][Tes] - Node Tes[1c6cd9c9-eed4-4fdb-9347-644723e36e57] running status set to false 
[INFO ] 2024-07-23 05:36:26.833 - [任务 12][Tes] - PDK connector node stopped: HazelcastTargetPdkDataNode-1c6cd9c9-eed4-4fdb-9347-644723e36e57 
[INFO ] 2024-07-23 05:36:26.833 - [任务 12][Tes] - PDK connector node released: HazelcastTargetPdkDataNode-1c6cd9c9-eed4-4fdb-9347-644723e36e57 
[INFO ] 2024-07-23 05:36:26.833 - [任务 12][Tes] - Node Tes[1c6cd9c9-eed4-4fdb-9347-644723e36e57] schema data cleaned 
[INFO ] 2024-07-23 05:36:26.833 - [任务 12][Tes] - Node Tes[1c6cd9c9-eed4-4fdb-9347-644723e36e57] monitor closed 
[INFO ] 2024-07-23 05:36:26.834 - [任务 12][Tes] - Node Tes[1c6cd9c9-eed4-4fdb-9347-644723e36e57] close complete, cost 16 ms 
[INFO ] 2024-07-23 05:36:27.306 - [任务 12][POLICY] - Incremental sync completed 
[INFO ] 2024-07-23 05:36:31.980 - [任务 12] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 05:36:31.980 - [任务 12] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3fa78fc7 
[INFO ] 2024-07-23 05:36:32.114 - [任务 12] - Stop task milestones: 669ed0a52f0fe71c3fdc1e5f(任务 12)  
[INFO ] 2024-07-23 05:36:32.115 - [任务 12] - Stopped task aspect(s) 
[INFO ] 2024-07-23 05:36:32.115 - [任务 12] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 05:36:32.137 - [任务 12] - Remove memory task client succeed, task: 任务 12[669ed0a52f0fe71c3fdc1e5f] 
[INFO ] 2024-07-23 05:36:32.140 - [任务 12] - Destroy memory task client cache succeed, task: 任务 12[669ed0a52f0fe71c3fdc1e5f] 
