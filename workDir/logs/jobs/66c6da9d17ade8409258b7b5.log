[INFO ] 2024-08-22 14:29:34.971 - [任务 15] - Start task milestones: 66c6da9d17ade8409258b7b5(任务 15) 
[INFO ] 2024-08-22 14:29:35.004 - [任务 15] - Task initialization... 
[INFO ] 2024-08-22 14:29:35.004 - [任务 15] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-08-22 14:29:35.112 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 14:29:35.113 - [任务 15][TestDummy] - Node TestDummy[32840223-74ee-4557-a8d2-405cc8a9c790] start preload schema,table counts: 1 
[INFO ] 2024-08-22 14:29:35.113 - [任务 15][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d] start preload schema,table counts: 1 
[INFO ] 2024-08-22 14:29:35.145 - [任务 15][TestDummy] - Node TestDummy[32840223-74ee-4557-a8d2-405cc8a9c790] preload schema finished, cost 33 ms 
[INFO ] 2024-08-22 14:29:35.148 - [任务 15][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d] preload schema finished, cost 32 ms 
[INFO ] 2024-08-22 14:29:35.719 - [任务 15][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 14:29:35.727 - [任务 15][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 14:29:35.916 - [任务 15][JARADDB2WIMTEST132] - Source node "JARADDB2WIMTEST132" read batch size: 100 
[INFO ] 2024-08-22 14:29:35.918 - [任务 15][JARADDB2WIMTEST132] - Source node "JARADDB2WIMTEST132" event queue capacity: 200 
[INFO ] 2024-08-22 14:29:35.918 - [任务 15][JARADDB2WIMTEST132] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-22 14:29:36.046 - [任务 15][JARADDB2WIMTEST132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724308176045} 
[INFO ] 2024-08-22 14:29:36.046 - [任务 15][JARADDB2WIMTEST132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 14:29:36.091 - [任务 15][JARADDB2WIMTEST132] - Initial sync started 
[INFO ] 2024-08-22 14:29:36.091 - [任务 15][JARADDB2WIMTEST132] - Starting batch read, table name: test111, offset: null 
[INFO ] 2024-08-22 14:29:36.139 - [任务 15][JARADDB2WIMTEST132] - Table test111 is going to be initial synced 
[INFO ] 2024-08-22 14:29:36.139 - [任务 15][JARADDB2WIMTEST132] - Table [test111] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 14:29:36.150 - [任务 15][JARADDB2WIMTEST132] - Query table 'test111' counts: 3 
[INFO ] 2024-08-22 14:29:36.150 - [任务 15][JARADDB2WIMTEST132] - Initial sync completed 
[INFO ] 2024-08-22 14:29:36.150 - [任务 15][JARADDB2WIMTEST132] - Incremental sync starting... 
[INFO ] 2024-08-22 14:29:36.150 - [任务 15][JARADDB2WIMTEST132] - Initial sync completed 
[INFO ] 2024-08-22 14:29:36.370 - [任务 15][JARADDB2WIMTEST132] - Starting stream read, table list: [test111], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724308176045} 
[INFO ] 2024-08-22 14:45:21.944 - [任务 15] - Stop task milestones: 66c6da9d17ade8409258b7b5(任务 15)  
[INFO ] 2024-08-22 14:45:22.072 - [任务 15][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d] running status set to false 
[INFO ] 2024-08-22 14:45:22.072 - [任务 15][JARADDB2WIMTEST132] - Log Miner is shutting down... 
[ERROR] 2024-08-22 14:45:22.274 - [任务 15][JARADDB2WIMTEST132] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-22 14:45:24.128 - [任务 15][JARADDB2WIMTEST132] - PDK connector node stopped: HazelcastSourcePdkDataNode-2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d 
[INFO ] 2024-08-22 14:45:24.128 - [任务 15][JARADDB2WIMTEST132] - PDK connector node released: HazelcastSourcePdkDataNode-2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d 
[INFO ] 2024-08-22 14:45:24.129 - [任务 15][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d] schema data cleaned 
[INFO ] 2024-08-22 14:45:24.129 - [任务 15][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d] monitor closed 
[INFO ] 2024-08-22 14:45:24.130 - [任务 15][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[2acb4f90-ab17-4bdf-a7ba-8d78fa3fbe0d] close complete, cost 2060 ms 
[INFO ] 2024-08-22 14:45:24.131 - [任务 15][TestDummy] - Node TestDummy[32840223-74ee-4557-a8d2-405cc8a9c790] running status set to false 
[INFO ] 2024-08-22 14:45:24.140 - [任务 15][TestDummy] - Stop connector 
[INFO ] 2024-08-22 14:45:24.141 - [任务 15][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-32840223-74ee-4557-a8d2-405cc8a9c790 
[INFO ] 2024-08-22 14:45:24.141 - [任务 15][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-32840223-74ee-4557-a8d2-405cc8a9c790 
[INFO ] 2024-08-22 14:45:24.142 - [任务 15][TestDummy] - Node TestDummy[32840223-74ee-4557-a8d2-405cc8a9c790] schema data cleaned 
[INFO ] 2024-08-22 14:45:24.142 - [任务 15][TestDummy] - Node TestDummy[32840223-74ee-4557-a8d2-405cc8a9c790] monitor closed 
[INFO ] 2024-08-22 14:45:24.142 - [任务 15][TestDummy] - Node TestDummy[32840223-74ee-4557-a8d2-405cc8a9c790] close complete, cost 12 ms 
[INFO ] 2024-08-22 14:45:27.533 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 14:45:27.534 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-08-22 14:45:27.534 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 14:45:27.574 - [任务 15] - Remove memory task client succeed, task: 任务 15[66c6da9d17ade8409258b7b5] 
[INFO ] 2024-08-22 14:45:27.574 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[66c6da9d17ade8409258b7b5] 
