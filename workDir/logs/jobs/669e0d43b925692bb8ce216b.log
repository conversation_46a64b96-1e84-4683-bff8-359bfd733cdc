[INFO ] 2024-07-22 15:43:00.725 - [任务 7] - Start task milestones: 669e0d43b925692bb8ce216b(任务 7) 
[INFO ] 2024-07-22 15:43:00.816 - [任务 7] - Task initialization... 
[INFO ] 2024-07-22 15:43:00.816 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-22 15:43:00.891 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-22 15:43:00.891 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] start preload schema,table counts: 1 
[INFO ] 2024-07-22 15:43:00.891 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] start preload schema,table counts: 1 
[INFO ] 2024-07-22 15:43:00.892 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 15:43:01.093 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 15:43:01.497 - [任务 7][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-22 15:43:01.752 - [任务 7][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-22 15:43:01.752 - [任务 7][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-22 15:43:01.752 - [任务 7][POLICY] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-07-22 15:43:01.753 - [任务 7][POLICY] - Pdk connector does not support timestamp to stream offset function, will stop task after snapshot: clickhouse-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-07-22 15:43:01.753 - [任务 7][POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-22 15:43:01.794 - [任务 7][POLICY] - Initial sync started 
[INFO ] 2024-07-22 15:43:01.795 - [任务 7][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-22 15:43:01.795 - [任务 7][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-22 15:43:02.300 - [任务 7][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-22 15:43:02.301 - [任务 7][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-22 15:43:02.302 - [任务 7][POLICY] - Initial sync completed 
[INFO ] 2024-07-22 15:43:02.302 - [任务 7][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-22 15:43:02.303 - [任务 7][POLICY] - Initial sync completed 
[INFO ] 2024-07-22 15:43:02.303 - [任务 7][POLICY] - Incremental sync completed 
[INFO ] 2024-07-22 15:43:02.334 - [任务 7][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: Starting stream read failed, errors: start point offset is null 
[ERROR] 2024-07-22 15:43:02.335 - [任务 7][POLICY] - Starting stream read failed, errors: start point offset is null <-- Error Message -->
Starting stream read failed, errors: start point offset is null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Starting stream read failed, errors: start point offset is null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.enterCDCStage(HazelcastSourcePdkDataNode.java:648)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:596)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	...

<-- Full Stack Trace -->
io.tapdata.exception.NodeException: Starting stream read failed, errors: start point offset is null
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Starting stream read failed, errors: start point offset is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.enterCDCStage(HazelcastSourcePdkDataNode.java:648)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:596)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more

[INFO ] 2024-07-22 15:43:02.536 - [任务 7][POLICY] - Job suspend in error handle 
[INFO ] 2024-07-22 15:43:02.810 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] running status set to false 
[INFO ] 2024-07-22 15:43:03.011 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:43:03.078 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[INFO ] 2024-07-22 15:43:03.109 - [任务 7][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad 
[INFO ] 2024-07-22 15:43:03.109 - [任务 7][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad 
[INFO ] 2024-07-22 15:43:03.110 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] schema data cleaned 
[INFO ] 2024-07-22 15:43:03.110 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] monitor closed 
[INFO ] 2024-07-22 15:43:03.111 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] close complete, cost 303 ms 
[INFO ] 2024-07-22 15:43:03.111 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] running status set to false 
[INFO ] 2024-07-22 15:43:03.132 - [任务 7][dummy_test] - Stop connector: first 1721634182267 24ms, last 1721634182299 502ms, counts: 695/534ms, min: 1, max: 502, QPS: 695/s 
[INFO ] 2024-07-22 15:43:03.132 - [任务 7][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-71333e40-15d3-432f-8dce-9395b9660bde 
[INFO ] 2024-07-22 15:43:03.132 - [任务 7][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-71333e40-15d3-432f-8dce-9395b9660bde 
[INFO ] 2024-07-22 15:43:03.133 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] schema data cleaned 
[INFO ] 2024-07-22 15:43:03.134 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] monitor closed 
[INFO ] 2024-07-22 15:43:03.134 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] close complete, cost 22 ms 
[INFO ] 2024-07-22 15:43:05.824 - [任务 7] - Task [任务 7] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-22 15:43:05.825 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-22 15:43:05.825 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@16053212 
[INFO ] 2024-07-22 15:43:05.825 - [任务 7] - Stop task milestones: 669e0d43b925692bb8ce216b(任务 7)  
[INFO ] 2024-07-22 15:43:06.008 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-07-22 15:43:06.009 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-07-22 15:43:06.020 - [任务 7] - Remove memory task client succeed, task: 任务 7[669e0d43b925692bb8ce216b] 
[INFO ] 2024-07-22 15:43:06.021 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[669e0d43b925692bb8ce216b] 
[INFO ] 2024-07-22 15:43:41.392 - [任务 7] - Start task milestones: 669e0d43b925692bb8ce216b(任务 7) 
[INFO ] 2024-07-22 15:43:41.580 - [任务 7] - Task initialization... 
[INFO ] 2024-07-22 15:43:41.580 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-22 15:43:41.674 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-22 15:43:41.674 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] start preload schema,table counts: 1 
[INFO ] 2024-07-22 15:43:41.674 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] start preload schema,table counts: 1 
[INFO ] 2024-07-22 15:43:41.674 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 15:43:41.674 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 15:43:42.278 - [任务 7][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-22 15:43:42.673 - [任务 7][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-22 15:43:42.673 - [任务 7][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-22 15:43:42.673 - [任务 7][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-22 15:43:42.725 - [任务 7][POLICY] - batch offset found: {},stream offset found: {} 
[INFO ] 2024-07-22 15:43:42.725 - [任务 7][POLICY] - Initial sync started 
[INFO ] 2024-07-22 15:43:42.733 - [任务 7][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-22 15:43:42.733 - [任务 7][POLICY] - Table POLICY is going to be initial synced 
[WARN ] 2024-07-22 15:43:43.150 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:43:43.150 - [任务 7][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-22 15:44:43.168 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:44:43.370 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:44:43.574 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:45:43.714 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:45:43.778 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:45:44.168 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:46:44.282 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:46:44.427 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:46:44.835 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:47:44.853 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:47:45.259 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:47:45.426 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:48:45.601 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:48:45.809 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:48:46.011 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:49:45.868 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:49:46.073 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:49:46.894 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:50:46.857 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:50:47.000 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:50:47.810 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:51:47.830 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:51:48.045 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[WARN ] 2024-07-22 15:51:48.248 - [任务 7][POLICY] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `LAST_CHANGE` is not under aggregate function and not in GROUP BY (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 15:51:56.383 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] running status set to false 
[INFO ] 2024-07-22 15:51:56.384 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:51:56.384 - [任务 7][POLICY] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-22 15:51:56.618 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[INFO ] 2024-07-22 15:51:56.618 - [任务 7][POLICY] - Clickhouse Optimize Table end 
[INFO ] 2024-07-22 15:51:56.651 - [任务 7][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad 
[INFO ] 2024-07-22 15:51:56.653 - [任务 7][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad 
[INFO ] 2024-07-22 15:51:56.653 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] schema data cleaned 
[INFO ] 2024-07-22 15:51:56.655 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] monitor closed 
[INFO ] 2024-07-22 15:51:56.657 - [任务 7][POLICY] - Node POLICY[39a0b7f8-4fc4-41ba-a78a-9069c6ee28ad] close complete, cost 275 ms 
[INFO ] 2024-07-22 15:51:56.657 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] running status set to false 
[INFO ] 2024-07-22 15:51:56.665 - [任务 7][POLICY] - Cancel query 'POLICY' snapshot row size with task stopped. 
[INFO ] 2024-07-22 15:51:56.668 - [任务 7][dummy_test] - Stop connector: first 1721634223132 15ms, last 1721634223132 521ms, counts: 695/521ms, min: 15, max: 521, QPS: 695/s 
[INFO ] 2024-07-22 15:51:56.668 - [任务 7][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-22 15:51:56.669 - [任务 7][POLICY] - Incremental sync completed 
[INFO ] 2024-07-22 15:51:56.670 - [任务 7][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-71333e40-15d3-432f-8dce-9395b9660bde 
[INFO ] 2024-07-22 15:51:56.671 - [任务 7][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-71333e40-15d3-432f-8dce-9395b9660bde 
[INFO ] 2024-07-22 15:51:56.671 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] schema data cleaned 
[INFO ] 2024-07-22 15:51:56.671 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] monitor closed 
[INFO ] 2024-07-22 15:51:56.671 - [任务 7][dummy_test] - Node dummy_test[71333e40-15d3-432f-8dce-9395b9660bde] close complete, cost 13 ms 
[INFO ] 2024-07-22 15:52:01.522 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-22 15:52:01.524 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@9628d2b 
[INFO ] 2024-07-22 15:52:01.654 - [任务 7] - Stop task milestones: 669e0d43b925692bb8ce216b(任务 7)  
[INFO ] 2024-07-22 15:52:01.655 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-07-22 15:52:01.655 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-07-22 15:52:01.677 - [任务 7] - Remove memory task client succeed, task: 任务 7[669e0d43b925692bb8ce216b] 
[INFO ] 2024-07-22 15:52:01.679 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[669e0d43b925692bb8ce216b] 
