[INFO ] 2024-03-29 18:15:41.175 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:41.181 - [orders(100)][850a7a8b-608f-4ca3-ac7a-de77aed512c9] - Node 850a7a8b-608f-4ca3-ac7a-de77aed512c9[850a7a8b-608f-4ca3-ac7a-de77aed512c9] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:41.182 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:41.185 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:41.186 - [orders(100)][850a7a8b-608f-4ca3-ac7a-de77aed512c9] - Node 850a7a8b-608f-4ca3-ac7a-de77aed512c9[850a7a8b-608f-4ca3-ac7a-de77aed512c9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:41.186 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:41.896 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:42.440 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72f770fe error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72f770fe error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72f770fe error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:44.878 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:44.965 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:44.966 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:44.975 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:44.975 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:44.980 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 202 ms 
[INFO ] 2024-03-29 18:15:45.014 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:45.014 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:45.014 - [orders(100)][850a7a8b-608f-4ca3-ac7a-de77aed512c9] - Node 850a7a8b-608f-4ca3-ac7a-de77aed512c9[850a7a8b-608f-4ca3-ac7a-de77aed512c9] running status set to false 
[INFO ] 2024-03-29 18:15:45.016 - [orders(100)][850a7a8b-608f-4ca3-ac7a-de77aed512c9] - Node 850a7a8b-608f-4ca3-ac7a-de77aed512c9[850a7a8b-608f-4ca3-ac7a-de77aed512c9] schema data cleaned 
[INFO ] 2024-03-29 18:15:45.016 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:45.018 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:15:45.018 - [orders(100)][850a7a8b-608f-4ca3-ac7a-de77aed512c9] - Node 850a7a8b-608f-4ca3-ac7a-de77aed512c9[850a7a8b-608f-4ca3-ac7a-de77aed512c9] monitor closed 
[INFO ] 2024-03-29 18:15:45.024 - [orders(100)][850a7a8b-608f-4ca3-ac7a-de77aed512c9] - Node 850a7a8b-608f-4ca3-ac7a-de77aed512c9[850a7a8b-608f-4ca3-ac7a-de77aed512c9] close complete, cost 12 ms 
[INFO ] 2024-03-29 18:15:45.027 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-850a7a8b-608f-4ca3-ac7a-de77aed512c9 complete, cost 5698ms 
[INFO ] 2024-03-29 18:15:52.815 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:52.835 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:52.839 - [orders(100)][ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] - Node ad4429d5-398f-4df7-b6dd-0b391b9b5bd3[ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:52.845 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.847 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.849 - [orders(100)][ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] - Node ad4429d5-398f-4df7-b6dd-0b391b9b5bd3[ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.891 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:52.892 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:52.894 - [orders(100)][abf4024e-8541-46c3-8d5c-dbb48c25152a] - Node abf4024e-8541-46c3-8d5c-dbb48c25152a[abf4024e-8541-46c3-8d5c-dbb48c25152a] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:52.897 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.897 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.908 - [orders(100)][abf4024e-8541-46c3-8d5c-dbb48c25152a] - Node abf4024e-8541-46c3-8d5c-dbb48c25152a[abf4024e-8541-46c3-8d5c-dbb48c25152a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.944 - [orders(100)][7f3be461-c380-4c82-b6b0-85d08edfe116] - Node 7f3be461-c380-4c82-b6b0-85d08edfe116[7f3be461-c380-4c82-b6b0-85d08edfe116] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:52.945 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:52.948 - [orders(100)][7f3be461-c380-4c82-b6b0-85d08edfe116] - Node 7f3be461-c380-4c82-b6b0-85d08edfe116[7f3be461-c380-4c82-b6b0-85d08edfe116] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.948 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:52.952 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.954 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:52.957 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:15:52.957 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:15:53.072 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:53.077 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f343679 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f343679 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f343679 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:15:53.080 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15a97551 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15a97551 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15a97551 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:15:53.088 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d35bb03 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d35bb03 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d35bb03 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:53.213 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:53.213 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.214 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.215 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:53.215 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:53.391 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 51 ms 
[INFO ] 2024-03-29 18:15:53.395 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:53.403 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.403 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.403 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:53.405 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:53.513 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 33 ms 
[INFO ] 2024-03-29 18:15:53.517 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:53.519 - [orders(100)][5232e124-7c49-48ba-a4c7-9117c73f6176] - Node 5232e124-7c49-48ba-a4c7-9117c73f6176[5232e124-7c49-48ba-a4c7-9117c73f6176] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:53.522 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.526 - [orders(100)][5232e124-7c49-48ba-a4c7-9117c73f6176] - Node 5232e124-7c49-48ba-a4c7-9117c73f6176[5232e124-7c49-48ba-a4c7-9117c73f6176] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.529 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:53.529 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.569 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:53.569 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.569 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.569 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:53.569 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:53.570 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 30 ms 
[INFO ] 2024-03-29 18:15:53.625 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:53.694 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6a347418 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6a347418 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6a347418 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:53.694 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:53.694 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:53.694 - [orders(100)][22770dff-5497-4bff-993a-3f669b95e9fb] - Node 22770dff-5497-4bff-993a-3f669b95e9fb[22770dff-5497-4bff-993a-3f669b95e9fb] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:53.695 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.695 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.696 - [orders(100)][22770dff-5497-4bff-993a-3f669b95e9fb] - Node 22770dff-5497-4bff-993a-3f669b95e9fb[22770dff-5497-4bff-993a-3f669b95e9fb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.708 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:15:53.726 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:53.726 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:53.726 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.727 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:53.727 - [orders(100)][6e44dd76-6f00-4b72-be3b-ce589a99f7d3] - Node 6e44dd76-6f00-4b72-be3b-ce589a99f7d3[6e44dd76-6f00-4b72-be3b-ce589a99f7d3] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:53.737 - [orders(100)][6e44dd76-6f00-4b72-be3b-ce589a99f7d3] - Node 6e44dd76-6f00-4b72-be3b-ce589a99f7d3[6e44dd76-6f00-4b72-be3b-ce589a99f7d3] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 18:15:53.739 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14c0e6e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14c0e6e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14c0e6e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:53.775 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:53.775 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@296145f2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@296145f2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@296145f2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:53.839 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:53.839 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.840 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:53.840 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:53.842 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:53.842 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 31 ms 
[INFO ] 2024-03-29 18:15:54.037 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:54.037 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:54.039 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:54.040 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:54.040 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:54.040 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 33 ms 
[INFO ] 2024-03-29 18:15:54.174 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:54.175 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:54.176 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:54.176 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:54.365 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:54.365 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 23 ms 
[INFO ] 2024-03-29 18:15:54.365 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:54.365 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:54.366 - [orders(100)][96adf325-236d-4d89-ac19-c8d069ea1d60] - Node 96adf325-236d-4d89-ac19-c8d069ea1d60[96adf325-236d-4d89-ac19-c8d069ea1d60] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:54.367 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:54.367 - [orders(100)][96adf325-236d-4d89-ac19-c8d069ea1d60] - Node 96adf325-236d-4d89-ac19-c8d069ea1d60[96adf325-236d-4d89-ac19-c8d069ea1d60] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:54.419 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:54.419 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:54.597 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7944a101 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7944a101 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7944a101 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:54.610 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:54.649 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:54.649 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:54.655 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:54.655 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:54.656 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 60 ms 
[INFO ] 2024-03-29 18:15:55.604 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:55.607 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:55.607 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:55.745 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:55.747 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:55.776 - [orders(100)][7f3be461-c380-4c82-b6b0-85d08edfe116] - Node 7f3be461-c380-4c82-b6b0-85d08edfe116[7f3be461-c380-4c82-b6b0-85d08edfe116] running status set to false 
[INFO ] 2024-03-29 18:15:55.899 - [orders(100)][ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] - Node ad4429d5-398f-4df7-b6dd-0b391b9b5bd3[ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] running status set to false 
[INFO ] 2024-03-29 18:15:55.900 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:15:55.901 - [orders(100)][ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] - Node ad4429d5-398f-4df7-b6dd-0b391b9b5bd3[ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] schema data cleaned 
[INFO ] 2024-03-29 18:15:55.901 - [orders(100)][ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] - Node ad4429d5-398f-4df7-b6dd-0b391b9b5bd3[ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] monitor closed 
[INFO ] 2024-03-29 18:15:55.901 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:55.901 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:55.901 - [orders(100)][7f3be461-c380-4c82-b6b0-85d08edfe116] - Node 7f3be461-c380-4c82-b6b0-85d08edfe116[7f3be461-c380-4c82-b6b0-85d08edfe116] schema data cleaned 
[INFO ] 2024-03-29 18:15:55.902 - [orders(100)][ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] - Node ad4429d5-398f-4df7-b6dd-0b391b9b5bd3[ad4429d5-398f-4df7-b6dd-0b391b9b5bd3] close complete, cost 144 ms 
[INFO ] 2024-03-29 18:15:55.902 - [orders(100)][abf4024e-8541-46c3-8d5c-dbb48c25152a] - Node abf4024e-8541-46c3-8d5c-dbb48c25152a[abf4024e-8541-46c3-8d5c-dbb48c25152a] running status set to false 
[INFO ] 2024-03-29 18:15:55.902 - [orders(100)][abf4024e-8541-46c3-8d5c-dbb48c25152a] - Node abf4024e-8541-46c3-8d5c-dbb48c25152a[abf4024e-8541-46c3-8d5c-dbb48c25152a] schema data cleaned 
[INFO ] 2024-03-29 18:15:55.903 - [orders(100)][abf4024e-8541-46c3-8d5c-dbb48c25152a] - Node abf4024e-8541-46c3-8d5c-dbb48c25152a[abf4024e-8541-46c3-8d5c-dbb48c25152a] monitor closed 
[INFO ] 2024-03-29 18:15:55.903 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:55.904 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 240 ms 
[INFO ] 2024-03-29 18:15:55.904 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:55.904 - [orders(100)][abf4024e-8541-46c3-8d5c-dbb48c25152a] - Node abf4024e-8541-46c3-8d5c-dbb48c25152a[abf4024e-8541-46c3-8d5c-dbb48c25152a] close complete, cost 245 ms 
[INFO ] 2024-03-29 18:15:55.916 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 264 ms 
[INFO ] 2024-03-29 18:15:55.972 - [orders(100)][7f3be461-c380-4c82-b6b0-85d08edfe116] - Node 7f3be461-c380-4c82-b6b0-85d08edfe116[7f3be461-c380-4c82-b6b0-85d08edfe116] monitor closed 
[INFO ] 2024-03-29 18:15:55.973 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-abf4024e-8541-46c3-8d5c-dbb48c25152a complete, cost 3109ms 
[INFO ] 2024-03-29 18:15:55.973 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-ad4429d5-398f-4df7-b6dd-0b391b9b5bd3 complete, cost 3113ms 
[INFO ] 2024-03-29 18:15:55.975 - [orders(100)][7f3be461-c380-4c82-b6b0-85d08edfe116] - Node 7f3be461-c380-4c82-b6b0-85d08edfe116[7f3be461-c380-4c82-b6b0-85d08edfe116] close complete, cost 336 ms 
[INFO ] 2024-03-29 18:15:55.980 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-7f3be461-c380-4c82-b6b0-85d08edfe116 complete, cost 3171ms 
[INFO ] 2024-03-29 18:15:56.248 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:56.251 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.252 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:56.252 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:15:56.253 - [orders(100)][5232e124-7c49-48ba-a4c7-9117c73f6176] - Node 5232e124-7c49-48ba-a4c7-9117c73f6176[5232e124-7c49-48ba-a4c7-9117c73f6176] running status set to false 
[INFO ] 2024-03-29 18:15:56.255 - [orders(100)][5232e124-7c49-48ba-a4c7-9117c73f6176] - Node 5232e124-7c49-48ba-a4c7-9117c73f6176[5232e124-7c49-48ba-a4c7-9117c73f6176] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.255 - [orders(100)][5232e124-7c49-48ba-a4c7-9117c73f6176] - Node 5232e124-7c49-48ba-a4c7-9117c73f6176[5232e124-7c49-48ba-a4c7-9117c73f6176] monitor closed 
[INFO ] 2024-03-29 18:15:56.256 - [orders(100)][5232e124-7c49-48ba-a4c7-9117c73f6176] - Node 5232e124-7c49-48ba-a4c7-9117c73f6176[5232e124-7c49-48ba-a4c7-9117c73f6176] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:15:56.260 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-5232e124-7c49-48ba-a4c7-9117c73f6176 complete, cost 2812ms 
[INFO ] 2024-03-29 18:15:56.262 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.263 - [orders(100)][256118fd-ef00-4903-8ae0-c707c9c4bb37] - Node 256118fd-ef00-4903-8ae0-c707c9c4bb37[256118fd-ef00-4903-8ae0-c707c9c4bb37] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:56.263 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.263 - [orders(100)][256118fd-ef00-4903-8ae0-c707c9c4bb37] - Node 256118fd-ef00-4903-8ae0-c707c9c4bb37[256118fd-ef00-4903-8ae0-c707c9c4bb37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.263 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.263 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.324 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:56.324 - [orders(100)][22770dff-5497-4bff-993a-3f669b95e9fb] - Node 22770dff-5497-4bff-993a-3f669b95e9fb[22770dff-5497-4bff-993a-3f669b95e9fb] running status set to false 
[INFO ] 2024-03-29 18:15:56.324 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:56.324 - [orders(100)][6e44dd76-6f00-4b72-be3b-ce589a99f7d3] - Node 6e44dd76-6f00-4b72-be3b-ce589a99f7d3[6e44dd76-6f00-4b72-be3b-ce589a99f7d3] running status set to false 
[INFO ] 2024-03-29 18:15:56.324 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:15:56.324 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.325 - [orders(100)][22770dff-5497-4bff-993a-3f669b95e9fb] - Node 22770dff-5497-4bff-993a-3f669b95e9fb[22770dff-5497-4bff-993a-3f669b95e9fb] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.325 - [orders(100)][6e44dd76-6f00-4b72-be3b-ce589a99f7d3] - Node 6e44dd76-6f00-4b72-be3b-ce589a99f7d3[6e44dd76-6f00-4b72-be3b-ce589a99f7d3] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.325 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.325 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:56.326 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:56.326 - [orders(100)][6e44dd76-6f00-4b72-be3b-ce589a99f7d3] - Node 6e44dd76-6f00-4b72-be3b-ce589a99f7d3[6e44dd76-6f00-4b72-be3b-ce589a99f7d3] monitor closed 
[INFO ] 2024-03-29 18:15:56.326 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:15:56.326 - [orders(100)][6e44dd76-6f00-4b72-be3b-ce589a99f7d3] - Node 6e44dd76-6f00-4b72-be3b-ce589a99f7d3[6e44dd76-6f00-4b72-be3b-ce589a99f7d3] close complete, cost 12 ms 
[INFO ] 2024-03-29 18:15:56.327 - [orders(100)][22770dff-5497-4bff-993a-3f669b95e9fb] - Node 22770dff-5497-4bff-993a-3f669b95e9fb[22770dff-5497-4bff-993a-3f669b95e9fb] monitor closed 
[INFO ] 2024-03-29 18:15:56.327 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 16 ms 
[INFO ] 2024-03-29 18:15:56.328 - [orders(100)][22770dff-5497-4bff-993a-3f669b95e9fb] - Node 22770dff-5497-4bff-993a-3f669b95e9fb[22770dff-5497-4bff-993a-3f669b95e9fb] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:15:56.329 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-6e44dd76-6f00-4b72-be3b-ce589a99f7d3 complete, cost 2643ms 
[INFO ] 2024-03-29 18:15:56.330 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-22770dff-5497-4bff-993a-3f669b95e9fb complete, cost 2689ms 
[ERROR] 2024-03-29 18:15:56.525 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6f0e8ec5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6f0e8ec5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6f0e8ec5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:56.549 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:56.576 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:56.576 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.577 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.577 - [orders(100)][4ee786bf-c26b-4a79-9ff5-e163360b6712] - Node 4ee786bf-c26b-4a79-9ff5-e163360b6712[4ee786bf-c26b-4a79-9ff5-e163360b6712] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:56.577 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.577 - [orders(100)][4ee786bf-c26b-4a79-9ff5-e163360b6712] - Node 4ee786bf-c26b-4a79-9ff5-e163360b6712[4ee786bf-c26b-4a79-9ff5-e163360b6712] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.578 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:56.579 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.579 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.580 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:56.580 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 68 ms 
[INFO ] 2024-03-29 18:15:56.620 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:56.621 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@256b1f2d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@256b1f2d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@256b1f2d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:56.764 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.766 - [orders(100)][30c18598-aa45-4c4f-9cba-f0e77efa2df7] - Node 30c18598-aa45-4c4f-9cba-f0e77efa2df7[30c18598-aa45-4c4f-9cba-f0e77efa2df7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:56.767 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.767 - [orders(100)][30c18598-aa45-4c4f-9cba-f0e77efa2df7] - Node 30c18598-aa45-4c4f-9cba-f0e77efa2df7[30c18598-aa45-4c4f-9cba-f0e77efa2df7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.767 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.767 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.820 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:56.854 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:56.855 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:56.855 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.855 - [orders(100)][584294d7-e1dd-443f-92f0-8329efaaa138] - Node 584294d7-e1dd-443f-92f0-8329efaaa138[584294d7-e1dd-443f-92f0-8329efaaa138] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:56.855 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.855 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:56.855 - [orders(100)][584294d7-e1dd-443f-92f0-8329efaaa138] - Node 584294d7-e1dd-443f-92f0-8329efaaa138[584294d7-e1dd-443f-92f0-8329efaaa138] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.856 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.856 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:56.856 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:56.856 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 42 ms 
[INFO ] 2024-03-29 18:15:56.857 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:15:56.930 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:56.930 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@30a6f71f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@30a6f71f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@30a6f71f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:15:56.932 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2efe2d5b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2efe2d5b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2efe2d5b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:56.978 - [orders(100)][96adf325-236d-4d89-ac19-c8d069ea1d60] - Node 96adf325-236d-4d89-ac19-c8d069ea1d60[96adf325-236d-4d89-ac19-c8d069ea1d60] running status set to false 
[INFO ] 2024-03-29 18:15:56.978 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:56.978 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.978 - [orders(100)][96adf325-236d-4d89-ac19-c8d069ea1d60] - Node 96adf325-236d-4d89-ac19-c8d069ea1d60[96adf325-236d-4d89-ac19-c8d069ea1d60] schema data cleaned 
[INFO ] 2024-03-29 18:15:56.979 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:56.979 - [orders(100)][96adf325-236d-4d89-ac19-c8d069ea1d60] - Node 96adf325-236d-4d89-ac19-c8d069ea1d60[96adf325-236d-4d89-ac19-c8d069ea1d60] monitor closed 
[INFO ] 2024-03-29 18:15:56.980 - [orders(100)][96adf325-236d-4d89-ac19-c8d069ea1d60] - Node 96adf325-236d-4d89-ac19-c8d069ea1d60[96adf325-236d-4d89-ac19-c8d069ea1d60] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:15:56.982 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:15:56.982 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-96adf325-236d-4d89-ac19-c8d069ea1d60 complete, cost 2716ms 
[INFO ] 2024-03-29 18:15:57.048 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:57.048 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.050 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.050 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:57.050 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:57.050 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 23 ms 
[INFO ] 2024-03-29 18:15:57.235 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:57.256 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.259 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.259 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:57.260 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:57.260 - [orders(100)][5b4ce989-5eaf-4545-bec9-52065b191b11] - Node 5b4ce989-5eaf-4545-bec9-52065b191b11[5b4ce989-5eaf-4545-bec9-52065b191b11] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:57.260 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:57.260 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:57.260 - [orders(100)][5b4ce989-5eaf-4545-bec9-52065b191b11] - Node 5b4ce989-5eaf-4545-bec9-52065b191b11[5b4ce989-5eaf-4545-bec9-52065b191b11] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:57.260 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:57.260 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:57.261 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 46 ms 
[INFO ] 2024-03-29 18:15:57.360 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:57.361 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@56fd9a38 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@56fd9a38 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@56fd9a38 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:57.516 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:57.516 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.517 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.517 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:57.518 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:57.519 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:15:57.586 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:57.592 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:57.594 - [orders(100)][03527580-3b70-4828-87dc-8da5c739c0c7] - Node 03527580-3b70-4828-87dc-8da5c739c0c7[03527580-3b70-4828-87dc-8da5c739c0c7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:57.594 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:57.594 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:57.594 - [orders(100)][03527580-3b70-4828-87dc-8da5c739c0c7] - Node 03527580-3b70-4828-87dc-8da5c739c0c7[03527580-3b70-4828-87dc-8da5c739c0c7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:57.635 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:57.636 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53852f08 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53852f08 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53852f08 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:57.804 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:57.804 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.805 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:57.805 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:57.806 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:57.807 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:15:58.956 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:58.958 - [orders(100)][256118fd-ef00-4903-8ae0-c707c9c4bb37] - Node 256118fd-ef00-4903-8ae0-c707c9c4bb37[256118fd-ef00-4903-8ae0-c707c9c4bb37] running status set to false 
[INFO ] 2024-03-29 18:15:58.958 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:58.958 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:58.958 - [orders(100)][256118fd-ef00-4903-8ae0-c707c9c4bb37] - Node 256118fd-ef00-4903-8ae0-c707c9c4bb37[256118fd-ef00-4903-8ae0-c707c9c4bb37] schema data cleaned 
[INFO ] 2024-03-29 18:15:58.958 - [orders(100)][256118fd-ef00-4903-8ae0-c707c9c4bb37] - Node 256118fd-ef00-4903-8ae0-c707c9c4bb37[256118fd-ef00-4903-8ae0-c707c9c4bb37] monitor closed 
[INFO ] 2024-03-29 18:15:58.958 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:15:58.958 - [orders(100)][256118fd-ef00-4903-8ae0-c707c9c4bb37] - Node 256118fd-ef00-4903-8ae0-c707c9c4bb37[256118fd-ef00-4903-8ae0-c707c9c4bb37] close complete, cost 19 ms 
[INFO ] 2024-03-29 18:15:58.964 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-256118fd-ef00-4903-8ae0-c707c9c4bb37 complete, cost 2768ms 
[INFO ] 2024-03-29 18:15:58.964 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:58.964 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:15:58.964 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:58.965 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:58.967 - [orders(100)][6b92a727-20c5-4ca3-b91d-6dc65c6cce43] - Node 6b92a727-20c5-4ca3-b91d-6dc65c6cce43[6b92a727-20c5-4ca3-b91d-6dc65c6cce43] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:15:58.967 - [orders(100)][6b92a727-20c5-4ca3-b91d-6dc65c6cce43] - Node 6b92a727-20c5-4ca3-b91d-6dc65c6cce43[6b92a727-20c5-4ca3-b91d-6dc65c6cce43] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:15:59.077 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:15:59.152 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ca406d7 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ca406d7 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ca406d7 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:15:59.153 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:59.153 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.153 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:59.157 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:15:59.157 - [orders(100)][4ee786bf-c26b-4a79-9ff5-e163360b6712] - Node 4ee786bf-c26b-4a79-9ff5-e163360b6712[4ee786bf-c26b-4a79-9ff5-e163360b6712] running status set to false 
[INFO ] 2024-03-29 18:15:59.158 - [orders(100)][4ee786bf-c26b-4a79-9ff5-e163360b6712] - Node 4ee786bf-c26b-4a79-9ff5-e163360b6712[4ee786bf-c26b-4a79-9ff5-e163360b6712] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.158 - [orders(100)][4ee786bf-c26b-4a79-9ff5-e163360b6712] - Node 4ee786bf-c26b-4a79-9ff5-e163360b6712[4ee786bf-c26b-4a79-9ff5-e163360b6712] monitor closed 
[INFO ] 2024-03-29 18:15:59.159 - [orders(100)][4ee786bf-c26b-4a79-9ff5-e163360b6712] - Node 4ee786bf-c26b-4a79-9ff5-e163360b6712[4ee786bf-c26b-4a79-9ff5-e163360b6712] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:15:59.159 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-4ee786bf-c26b-4a79-9ff5-e163360b6712 complete, cost 2674ms 
[INFO ] 2024-03-29 18:15:59.273 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:15:59.273 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:59.274 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:15:59.274 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.275 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:15:59.275 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 19 ms 
[INFO ] 2024-03-29 18:15:59.458 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:59.458 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.460 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:59.460 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:59.461 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][30c18598-aa45-4c4f-9cba-f0e77efa2df7] - Node 30c18598-aa45-4c4f-9cba-f0e77efa2df7[30c18598-aa45-4c4f-9cba-f0e77efa2df7] running status set to false 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][30c18598-aa45-4c4f-9cba-f0e77efa2df7] - Node 30c18598-aa45-4c4f-9cba-f0e77efa2df7[30c18598-aa45-4c4f-9cba-f0e77efa2df7] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][584294d7-e1dd-443f-92f0-8329efaaa138] - Node 584294d7-e1dd-443f-92f0-8329efaaa138[584294d7-e1dd-443f-92f0-8329efaaa138] running status set to false 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][30c18598-aa45-4c4f-9cba-f0e77efa2df7] - Node 30c18598-aa45-4c4f-9cba-f0e77efa2df7[30c18598-aa45-4c4f-9cba-f0e77efa2df7] monitor closed 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][584294d7-e1dd-443f-92f0-8329efaaa138] - Node 584294d7-e1dd-443f-92f0-8329efaaa138[584294d7-e1dd-443f-92f0-8329efaaa138] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][30c18598-aa45-4c4f-9cba-f0e77efa2df7] - Node 30c18598-aa45-4c4f-9cba-f0e77efa2df7[30c18598-aa45-4c4f-9cba-f0e77efa2df7] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][584294d7-e1dd-443f-92f0-8329efaaa138] - Node 584294d7-e1dd-443f-92f0-8329efaaa138[584294d7-e1dd-443f-92f0-8329efaaa138] monitor closed 
[INFO ] 2024-03-29 18:15:59.463 - [orders(100)][584294d7-e1dd-443f-92f0-8329efaaa138] - Node 584294d7-e1dd-443f-92f0-8329efaaa138[584294d7-e1dd-443f-92f0-8329efaaa138] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:15:59.464 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-30c18598-aa45-4c4f-9cba-f0e77efa2df7 complete, cost 2817ms 
[INFO ] 2024-03-29 18:15:59.465 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-584294d7-e1dd-443f-92f0-8329efaaa138 complete, cost 2691ms 
[INFO ] 2024-03-29 18:15:59.897 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:15:59.897 - [orders(100)][5b4ce989-5eaf-4545-bec9-52065b191b11] - Node 5b4ce989-5eaf-4545-bec9-52065b191b11[5b4ce989-5eaf-4545-bec9-52065b191b11] running status set to false 
[INFO ] 2024-03-29 18:15:59.897 - [orders(100)][5b4ce989-5eaf-4545-bec9-52065b191b11] - Node 5b4ce989-5eaf-4545-bec9-52065b191b11[5b4ce989-5eaf-4545-bec9-52065b191b11] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.898 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:15:59.899 - [orders(100)][5b4ce989-5eaf-4545-bec9-52065b191b11] - Node 5b4ce989-5eaf-4545-bec9-52065b191b11[5b4ce989-5eaf-4545-bec9-52065b191b11] monitor closed 
[INFO ] 2024-03-29 18:15:59.899 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:15:59.899 - [orders(100)][5b4ce989-5eaf-4545-bec9-52065b191b11] - Node 5b4ce989-5eaf-4545-bec9-52065b191b11[5b4ce989-5eaf-4545-bec9-52065b191b11] close complete, cost 7 ms 
[INFO ] 2024-03-29 18:15:59.899 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:16:00.057 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-5b4ce989-5eaf-4545-bec9-52065b191b11 complete, cost 2779ms 
[INFO ] 2024-03-29 18:16:00.084 - [orders(100)][5ee17566-3c8f-491c-9551-ab324430accc] - Node 5ee17566-3c8f-491c-9551-ab324430accc[5ee17566-3c8f-491c-9551-ab324430accc] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:16:00.085 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:00.085 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:00.092 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:16:00.092 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:00.093 - [orders(100)][5ee17566-3c8f-491c-9551-ab324430accc] - Node 5ee17566-3c8f-491c-9551-ab324430accc[5ee17566-3c8f-491c-9551-ab324430accc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:00.125 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:16:00.162 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a014163 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a014163 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a014163 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][03527580-3b70-4828-87dc-8da5c739c0c7] - Node 03527580-3b70-4828-87dc-8da5c739c0c7[03527580-3b70-4828-87dc-8da5c739c0c7] running status set to false 
[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][03527580-3b70-4828-87dc-8da5c739c0c7] - Node 03527580-3b70-4828-87dc-8da5c739c0c7[03527580-3b70-4828-87dc-8da5c739c0c7] schema data cleaned 
[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][03527580-3b70-4828-87dc-8da5c739c0c7] - Node 03527580-3b70-4828-87dc-8da5c739c0c7[03527580-3b70-4828-87dc-8da5c739c0c7] monitor closed 
[INFO ] 2024-03-29 18:16:00.165 - [orders(100)][03527580-3b70-4828-87dc-8da5c739c0c7] - Node 03527580-3b70-4828-87dc-8da5c739c0c7[03527580-3b70-4828-87dc-8da5c739c0c7] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:16:00.355 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-03527580-3b70-4828-87dc-8da5c739c0c7 complete, cost 2625ms 
[INFO ] 2024-03-29 18:16:00.355 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:16:00.375 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:00.376 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:00.376 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:16:00.378 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:16:00.379 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 36 ms 
[INFO ] 2024-03-29 18:16:01.241 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:01.241 - [orders(100)][e4beb17b-489f-4d4f-a581-c8d495f4f867] - Node e4beb17b-489f-4d4f-a581-c8d495f4f867[e4beb17b-489f-4d4f-a581-c8d495f4f867] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:16:01.241 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:01.241 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:01.242 - [orders(100)][e4beb17b-489f-4d4f-a581-c8d495f4f867] - Node e4beb17b-489f-4d4f-a581-c8d495f4f867[e4beb17b-489f-4d4f-a581-c8d495f4f867] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:01.242 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:01.320 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:16:01.321 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@42ba3a21 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@42ba3a21 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@42ba3a21 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:16:01.362 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:01.362 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:01.363 - [orders(100)][81f56388-3788-4fd7-98f1-3e1e91056f03] - Node 81f56388-3788-4fd7-98f1-3e1e91056f03[81f56388-3788-4fd7-98f1-3e1e91056f03] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:16:01.363 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:01.363 - [orders(100)][81f56388-3788-4fd7-98f1-3e1e91056f03] - Node 81f56388-3788-4fd7-98f1-3e1e91056f03[81f56388-3788-4fd7-98f1-3e1e91056f03] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:01.363 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:01.406 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:16:01.407 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62840f2e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62840f2e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62840f2e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:16:01.496 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:16:01.496 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:01.496 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:01.496 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:16:01.497 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:16:01.497 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 16 ms 
[INFO ] 2024-03-29 18:16:01.642 - [orders(100)][6b92a727-20c5-4ca3-b91d-6dc65c6cce43] - Node 6b92a727-20c5-4ca3-b91d-6dc65c6cce43[6b92a727-20c5-4ca3-b91d-6dc65c6cce43] running status set to false 
[INFO ] 2024-03-29 18:16:01.643 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:01.643 - [orders(100)][6b92a727-20c5-4ca3-b91d-6dc65c6cce43] - Node 6b92a727-20c5-4ca3-b91d-6dc65c6cce43[6b92a727-20c5-4ca3-b91d-6dc65c6cce43] schema data cleaned 
[INFO ] 2024-03-29 18:16:01.643 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:01.643 - [orders(100)][6b92a727-20c5-4ca3-b91d-6dc65c6cce43] - Node 6b92a727-20c5-4ca3-b91d-6dc65c6cce43[6b92a727-20c5-4ca3-b91d-6dc65c6cce43] monitor closed 
[INFO ] 2024-03-29 18:16:01.643 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:01.643 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:16:01.643 - [orders(100)][6b92a727-20c5-4ca3-b91d-6dc65c6cce43] - Node 6b92a727-20c5-4ca3-b91d-6dc65c6cce43[6b92a727-20c5-4ca3-b91d-6dc65c6cce43] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:16:01.657 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:16:01.657 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-6b92a727-20c5-4ca3-b91d-6dc65c6cce43 complete, cost 2769ms 
[INFO ] 2024-03-29 18:16:01.662 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:01.663 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:01.663 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:16:01.663 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:16:01.663 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 19 ms 
[INFO ] 2024-03-29 18:16:02.682 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:02.683 - [orders(100)][5ee17566-3c8f-491c-9551-ab324430accc] - Node 5ee17566-3c8f-491c-9551-ab324430accc[5ee17566-3c8f-491c-9551-ab324430accc] running status set to false 
[INFO ] 2024-03-29 18:16:02.683 - [orders(100)][5ee17566-3c8f-491c-9551-ab324430accc] - Node 5ee17566-3c8f-491c-9551-ab324430accc[5ee17566-3c8f-491c-9551-ab324430accc] schema data cleaned 
[INFO ] 2024-03-29 18:16:02.683 - [orders(100)][5ee17566-3c8f-491c-9551-ab324430accc] - Node 5ee17566-3c8f-491c-9551-ab324430accc[5ee17566-3c8f-491c-9551-ab324430accc] monitor closed 
[INFO ] 2024-03-29 18:16:02.683 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:02.683 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:02.683 - [orders(100)][5ee17566-3c8f-491c-9551-ab324430accc] - Node 5ee17566-3c8f-491c-9551-ab324430accc[5ee17566-3c8f-491c-9551-ab324430accc] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:16:02.683 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:16:02.884 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-5ee17566-3c8f-491c-9551-ab324430accc complete, cost 2688ms 
[INFO ] 2024-03-29 18:16:03.845 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:03.845 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:03.846 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:03.846 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:16:03.846 - [orders(100)][e4beb17b-489f-4d4f-a581-c8d495f4f867] - Node e4beb17b-489f-4d4f-a581-c8d495f4f867[e4beb17b-489f-4d4f-a581-c8d495f4f867] running status set to false 
[INFO ] 2024-03-29 18:16:03.847 - [orders(100)][e4beb17b-489f-4d4f-a581-c8d495f4f867] - Node e4beb17b-489f-4d4f-a581-c8d495f4f867[e4beb17b-489f-4d4f-a581-c8d495f4f867] schema data cleaned 
[INFO ] 2024-03-29 18:16:03.847 - [orders(100)][e4beb17b-489f-4d4f-a581-c8d495f4f867] - Node e4beb17b-489f-4d4f-a581-c8d495f4f867[e4beb17b-489f-4d4f-a581-c8d495f4f867] monitor closed 
[INFO ] 2024-03-29 18:16:03.847 - [orders(100)][e4beb17b-489f-4d4f-a581-c8d495f4f867] - Node e4beb17b-489f-4d4f-a581-c8d495f4f867[e4beb17b-489f-4d4f-a581-c8d495f4f867] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:16:03.938 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-e4beb17b-489f-4d4f-a581-c8d495f4f867 complete, cost 2652ms 
[INFO ] 2024-03-29 18:16:03.938 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:03.938 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:03.938 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:03.938 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:16:03.943 - [orders(100)][81f56388-3788-4fd7-98f1-3e1e91056f03] - Node 81f56388-3788-4fd7-98f1-3e1e91056f03[81f56388-3788-4fd7-98f1-3e1e91056f03] running status set to false 
[INFO ] 2024-03-29 18:16:03.944 - [orders(100)][81f56388-3788-4fd7-98f1-3e1e91056f03] - Node 81f56388-3788-4fd7-98f1-3e1e91056f03[81f56388-3788-4fd7-98f1-3e1e91056f03] schema data cleaned 
[INFO ] 2024-03-29 18:16:03.944 - [orders(100)][81f56388-3788-4fd7-98f1-3e1e91056f03] - Node 81f56388-3788-4fd7-98f1-3e1e91056f03[81f56388-3788-4fd7-98f1-3e1e91056f03] monitor closed 
[INFO ] 2024-03-29 18:16:03.944 - [orders(100)][81f56388-3788-4fd7-98f1-3e1e91056f03] - Node 81f56388-3788-4fd7-98f1-3e1e91056f03[81f56388-3788-4fd7-98f1-3e1e91056f03] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:16:04.149 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-81f56388-3788-4fd7-98f1-3e1e91056f03 complete, cost 2629ms 
[INFO ] 2024-03-29 18:16:04.881 - [orders(100)][68664780-b70c-4d78-bde4-5f421281c014] - Node 68664780-b70c-4d78-bde4-5f421281c014[68664780-b70c-4d78-bde4-5f421281c014] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:16:04.881 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:04.881 - [orders(100)][68664780-b70c-4d78-bde4-5f421281c014] - Node 68664780-b70c-4d78-bde4-5f421281c014[68664780-b70c-4d78-bde4-5f421281c014] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:04.881 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:04.881 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:04.933 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:04.933 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:16:05.002 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@208121c1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@208121c1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@208121c1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:16:05.004 - [orders(100)][0d8b054b-92ce-4ad4-8ede-0089f7f58459] - Node 0d8b054b-92ce-4ad4-8ede-0089f7f58459[0d8b054b-92ce-4ad4-8ede-0089f7f58459] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:16:05.004 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:05.004 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:05.004 - [orders(100)][0d8b054b-92ce-4ad4-8ede-0089f7f58459] - Node 0d8b054b-92ce-4ad4-8ede-0089f7f58459[0d8b054b-92ce-4ad4-8ede-0089f7f58459] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:05.004 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:05.004 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:16:05.052 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:16:05.053 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d368609 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d368609 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d368609 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:16:05.178 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:16:05.178 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:05.178 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:05.178 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:16:05.179 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:16:05.180 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 40 ms 
[INFO ] 2024-03-29 18:16:05.219 - [orders(100)][d8fb2b31-81f4-4057-9ca1-82123f377642] - Node d8fb2b31-81f4-4057-9ca1-82123f377642[d8fb2b31-81f4-4057-9ca1-82123f377642] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:16:05.220 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:05.220 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:16:05.221 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:16:05.221 - [orders(100)][d8fb2b31-81f4-4057-9ca1-82123f377642] - Node d8fb2b31-81f4-4057-9ca1-82123f377642[d8fb2b31-81f4-4057-9ca1-82123f377642] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:05.221 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:16:05.314 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:16:05.314 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d677728 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d677728 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d677728 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:16:05.336 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:16:05.337 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:05.344 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:05.344 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:16:05.346 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:16:05.346 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 26 ms 
[INFO ] 2024-03-29 18:16:05.474 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] running status set to false 
[INFO ] 2024-03-29 18:16:05.474 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:05.474 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1 
[INFO ] 2024-03-29 18:16:05.474 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] schema data cleaned 
[INFO ] 2024-03-29 18:16:05.475 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] monitor closed 
[INFO ] 2024-03-29 18:16:05.475 - [orders(100)][Order Details] - Node Order Details[bbac64d5-e71d-4ad1-b008-e4a7bb17a5c1] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:16:07.513 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:07.518 - [orders(100)][68664780-b70c-4d78-bde4-5f421281c014] - Node 68664780-b70c-4d78-bde4-5f421281c014[68664780-b70c-4d78-bde4-5f421281c014] running status set to false 
[INFO ] 2024-03-29 18:16:07.518 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:07.518 - [orders(100)][68664780-b70c-4d78-bde4-5f421281c014] - Node 68664780-b70c-4d78-bde4-5f421281c014[68664780-b70c-4d78-bde4-5f421281c014] schema data cleaned 
[INFO ] 2024-03-29 18:16:07.518 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:07.518 - [orders(100)][68664780-b70c-4d78-bde4-5f421281c014] - Node 68664780-b70c-4d78-bde4-5f421281c014[68664780-b70c-4d78-bde4-5f421281c014] monitor closed 
[INFO ] 2024-03-29 18:16:07.518 - [orders(100)][68664780-b70c-4d78-bde4-5f421281c014] - Node 68664780-b70c-4d78-bde4-5f421281c014[68664780-b70c-4d78-bde4-5f421281c014] close complete, cost 11 ms 
[INFO ] 2024-03-29 18:16:07.518 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 15 ms 
[INFO ] 2024-03-29 18:16:07.577 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-68664780-b70c-4d78-bde4-5f421281c014 complete, cost 2695ms 
[INFO ] 2024-03-29 18:16:07.577 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:07.577 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:07.577 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:07.582 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:16:07.582 - [orders(100)][0d8b054b-92ce-4ad4-8ede-0089f7f58459] - Node 0d8b054b-92ce-4ad4-8ede-0089f7f58459[0d8b054b-92ce-4ad4-8ede-0089f7f58459] running status set to false 
[INFO ] 2024-03-29 18:16:07.585 - [orders(100)][0d8b054b-92ce-4ad4-8ede-0089f7f58459] - Node 0d8b054b-92ce-4ad4-8ede-0089f7f58459[0d8b054b-92ce-4ad4-8ede-0089f7f58459] schema data cleaned 
[INFO ] 2024-03-29 18:16:07.585 - [orders(100)][0d8b054b-92ce-4ad4-8ede-0089f7f58459] - Node 0d8b054b-92ce-4ad4-8ede-0089f7f58459[0d8b054b-92ce-4ad4-8ede-0089f7f58459] monitor closed 
[INFO ] 2024-03-29 18:16:07.592 - [orders(100)][0d8b054b-92ce-4ad4-8ede-0089f7f58459] - Node 0d8b054b-92ce-4ad4-8ede-0089f7f58459[0d8b054b-92ce-4ad4-8ede-0089f7f58459] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:16:07.593 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-0d8b054b-92ce-4ad4-8ede-0089f7f58459 complete, cost 2659ms 
[INFO ] 2024-03-29 18:16:07.841 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] running status set to false 
[INFO ] 2024-03-29 18:16:07.841 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] schema data cleaned 
[INFO ] 2024-03-29 18:16:07.841 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] monitor closed 
[INFO ] 2024-03-29 18:16:07.841 - [orders(100)][Order Details] - Node Order Details[b8a98d69-3854-4130-b1e1-2b4ad141b821] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:16:07.841 - [orders(100)][d8fb2b31-81f4-4057-9ca1-82123f377642] - Node d8fb2b31-81f4-4057-9ca1-82123f377642[d8fb2b31-81f4-4057-9ca1-82123f377642] running status set to false 
[INFO ] 2024-03-29 18:16:07.841 - [orders(100)][d8fb2b31-81f4-4057-9ca1-82123f377642] - Node d8fb2b31-81f4-4057-9ca1-82123f377642[d8fb2b31-81f4-4057-9ca1-82123f377642] schema data cleaned 
[INFO ] 2024-03-29 18:16:07.842 - [orders(100)][d8fb2b31-81f4-4057-9ca1-82123f377642] - Node d8fb2b31-81f4-4057-9ca1-82123f377642[d8fb2b31-81f4-4057-9ca1-82123f377642] monitor closed 
[INFO ] 2024-03-29 18:16:07.842 - [orders(100)][d8fb2b31-81f4-4057-9ca1-82123f377642] - Node d8fb2b31-81f4-4057-9ca1-82123f377642[d8fb2b31-81f4-4057-9ca1-82123f377642] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:16:07.842 - [orders(100)] - load tapTable task 660694c839a4e7320f0e7317-d8fb2b31-81f4-4057-9ca1-82123f377642 complete, cost 2729ms 
