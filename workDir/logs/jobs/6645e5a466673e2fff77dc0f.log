[INFO ] 2024-05-17 10:08:51.098 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 10:08:51.119 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 10:08:51.119 - [任务 7][my_table] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-17 10:08:51.119 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[WARN ] 2024-05-17 10:08:51.127 - [任务 7][my_table] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: The client connection was terminated by the sqlserver server
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-17 10:09:51.744 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 10:09:52.052 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 10:09:52.052 - [任务 7][my_table] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-17 10:09:52.064 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[WARN ] 2024-05-17 10:09:52.671 - [任务 7][testTimestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: testTimestamp
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-17 10:10:52.754 - [任务 7][testTimestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: testTimestamp
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-17 10:11:52.878 - [任务 7][testTimestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: testTimestamp
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-17 10:12:52.904 - [任务 7][testTimestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: testTimestamp
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-17 10:13:52.919 - [任务 7][testTimestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: testTimestamp
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-17 10:14:53.154 - [任务 7][testTimestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: testTimestamp
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-17 10:15:53.063 - [任务 7][testTimestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: testTimestamp
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-17 10:16:18.406 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 10:22:05.538 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 10:22:05.539 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 10:22:05.539 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 10:22:05.917 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 10:22:06.521 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 10:22:06.522 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] start preload schema,table counts: 1 
[INFO ] 2024-05-17 10:22:06.754 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 231 ms 
[INFO ] 2024-05-17 10:22:06.755 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] preload schema finished, cost 228 ms 
[INFO ] 2024-05-17 10:22:07.815 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 10:22:07.819 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 10:22:07.843 - [任务 7][my_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-17 10:22:07.844 - [任务 7][my_table] - batch offset found: {"my_table":{}},stream offset found: "{\"currentStartLSN\":\"000032F40001FF880003\",\"ddlOffset\":\"AAAy9AAB9+gBcw==\",\"tablesOffset\":{\"my_table\":\"000032F40001FF880003\"}}" 
[INFO ] 2024-05-17 10:22:07.971 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[ERROR] 2024-05-17 10:22:07.972 - [任务 7][testTimestamp] - java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive] <-- Error Message -->
java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...

<-- Full Stack Trace -->
java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:128)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:128)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:112)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:106)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:178)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:372)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.connector.doris.DorisJdbcContext.queryTimeZone(DorisJdbcContext.java:43)
	at io.tapdata.connector.doris.DorisConnector.onStart(DorisConnector.java:67)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:268)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:178)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	... 16 more

[INFO ] 2024-05-17 10:22:08.119 - [任务 7][testTimestamp] - Job suspend in error handle 
[INFO ] 2024-05-17 10:22:08.124 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 10:22:08.124 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 10:22:08.125 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: "{\"currentStartLSN\":\"000032F40001FF880003\",\"ddlOffset\":\"AAAy9AAB9+gBcw==\",\"tablesOffset\":{\"my_table\":\"000032F40001FF880003\"}}" 
[INFO ] 2024-05-17 10:22:08.126 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 10:22:08.181 - [任务 7][my_table] - Incremental sync completed 
[ERROR] 2024-05-17 10:22:08.183 - [任务 7][my_table] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed. <-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:33)
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.init(MssqlCdcRunner.java:137)
	at io.tapdata.connector.mssql.cdc.MssqlCdcRunner.startCdcRunner(MssqlCdcRunner.java:209)
	at io.tapdata.connector.mssql.MssqlConnector.streamRead(MssqlConnector.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	... 16 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 22 more

[INFO ] 2024-05-17 10:22:08.189 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 10:22:08.195 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 10:22:08.195 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 10:22:08.208 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 10:22:08.209 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 78 ms 
[INFO ] 2024-05-17 10:22:08.209 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] running status set to false 
[INFO ] 2024-05-17 10:22:08.268 - [任务 7][testTimestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-2b0f7f3e-30f7-4db5-a950-d64cccb20c4e 
[INFO ] 2024-05-17 10:22:08.273 - [任务 7][testTimestamp] - PDK connector node released: HazelcastTargetPdkDataNode-2b0f7f3e-30f7-4db5-a950-d64cccb20c4e 
[INFO ] 2024-05-17 10:22:08.273 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] schema data cleaned 
[INFO ] 2024-05-17 10:22:08.274 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] monitor closed 
[INFO ] 2024-05-17 10:22:08.275 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] close complete, cost 66 ms 
[INFO ] 2024-05-17 10:22:09.110 - [任务 7] - Task [任务 7] cannot retry, reason: Task retry service not start 
[INFO ] 2024-05-17 10:22:09.142 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 10:22:09.143 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 10:22:09.165 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 10:22:09.165 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 10:22:09.211 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 10:22:09.212 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 10:23:05.772 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 10:23:05.784 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 10:23:05.828 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 10:23:05.944 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 10:23:05.944 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] start preload schema,table counts: 1 
[INFO ] 2024-05-17 10:23:05.969 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 10:23:05.969 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 14 ms 
[INFO ] 2024-05-17 10:23:05.970 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] preload schema finished, cost 16 ms 
[INFO ] 2024-05-17 10:23:06.722 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 10:23:06.724 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 10:23:06.802 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 10:23:06.803 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FA0000CB080003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 10:23:06.803 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 10:23:06.892 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 10:23:06.900 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 10:23:06.987 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 10:23:06.987 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 10:23:06.989 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 10:23:06.989 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 10:23:06.993 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 10:23:06.993 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FA0000CB080003","tablesOffset":{},"ddlOffset":null} 
[ERROR] 2024-05-17 10:23:07.084 - [任务 7][testTimestamp] - java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive] <-- Error Message -->
java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...

<-- Full Stack Trace -->
java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:128)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:128)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:112)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:106)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:178)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:372)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: errCode = 2, detailMessage = There is no scanNode Backend available.[10007: not alive]
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.connector.doris.DorisJdbcContext.queryTimeZone(DorisJdbcContext.java:43)
	at io.tapdata.connector.doris.DorisConnector.onStart(DorisConnector.java:67)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:268)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:178)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	... 16 more

[INFO ] 2024-05-17 10:23:07.084 - [任务 7][testTimestamp] - Job suspend in error handle 
[INFO ] 2024-05-17 10:23:07.290 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 10:23:07.321 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 10:23:07.322 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 10:23:07.322 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 10:23:07.529 - [任务 7][my_table] - Incremental sync completed 
[INFO ] 2024-05-17 10:23:09.279 - [任务 7] - Task [任务 7] cannot retry, reason: Task retry service not start 
[INFO ] 2024-05-17 10:23:10.372 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 10:23:10.372 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 10:23:10.376 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 10:23:10.376 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 10:23:10.387 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3065 ms 
[INFO ] 2024-05-17 10:23:10.387 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] running status set to false 
[INFO ] 2024-05-17 10:23:10.419 - [任务 7][testTimestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-2b0f7f3e-30f7-4db5-a950-d64cccb20c4e 
[INFO ] 2024-05-17 10:23:10.419 - [任务 7][testTimestamp] - PDK connector node released: HazelcastTargetPdkDataNode-2b0f7f3e-30f7-4db5-a950-d64cccb20c4e 
[INFO ] 2024-05-17 10:23:10.420 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] schema data cleaned 
[INFO ] 2024-05-17 10:23:10.420 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] monitor closed 
[INFO ] 2024-05-17 10:23:10.629 - [任务 7][testTimestamp] - Node testTimestamp[2b0f7f3e-30f7-4db5-a950-d64cccb20c4e] close complete, cost 34 ms 
[INFO ] 2024-05-17 10:23:14.321 - [任务 7] - Task [任务 7] cannot retry, reason: Task retry service not start 
[INFO ] 2024-05-17 10:23:14.321 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 10:23:14.369 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 10:23:14.371 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 10:23:14.372 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 10:23:14.448 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 10:23:14.452 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 11:58:49.487 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 11:58:49.603 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 11:58:49.604 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 11:58:49.749 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 11:58:49.753 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 11:58:49.753 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 11:58:49.776 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 26 ms 
[INFO ] 2024-05-17 11:58:50.009 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 40 ms 
[INFO ] 2024-05-17 11:58:50.113 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 11:58:50.113 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 11:58:50.115 - [任务 7][my_table] - Sync progress not exists, will run task as first time 
[INFO ] 2024-05-17 11:58:50.116 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 11:58:50.147 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FA0001EE480003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 11:58:50.147 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 11:58:50.228 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 11:58:50.236 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 11:58:50.236 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 11:58:50.400 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 11:58:50.400 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 11:58:50.401 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 11:58:50.403 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 11:58:50.605 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FA0001EE480003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 11:58:50.611 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 11:58:50.699 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 11:58:50.699 - [任务 7][test_timestamp] - Sync progress not exists, will run task as first time 
[INFO ] 2024-05-17 11:58:50.710 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 11:58:50.710 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 11:58:52.229 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 11:58:52.230 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 11:58:55.290 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 11:58:55.292 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 11:58:55.293 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 11:58:55.293 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 11:58:55.296 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3069 ms 
[INFO ] 2024-05-17 11:58:55.344 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 11:58:55.344 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 11:58:55.345 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 11:58:55.345 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 11:58:55.345 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 11:58:55.345 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 49 ms 
[INFO ] 2024-05-17 11:58:58.305 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 11:58:58.306 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 11:58:58.306 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 11:58:58.424 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 11:58:58.424 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 12:00:20.835 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 12:00:20.836 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 12:00:20.928 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 12:00:20.928 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 12:00:21.012 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:00:21.015 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:00:21.131 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 101 ms 
[INFO ] 2024-05-17 12:00:21.131 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 103 ms 
[INFO ] 2024-05-17 12:00:22.126 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 12:00:22.127 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 12:00:22.127 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 12:00:22.159 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FA0001F3100003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:00:22.159 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 12:00:22.242 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 12:00:22.242 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 12:00:22.248 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 12:00:22.350 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 12:00:22.350 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:00:22.352 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 12:00:22.354 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:00:22.555 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FA0001F3100003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:00:22.587 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 12:00:22.695 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 12:00:22.695 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 12:00:28.542 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 12:00:28.760 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 12:01:09.288 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 12:01:09.696 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 12:01:12.626 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 12:01:12.627 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 12:01:12.627 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 12:01:12.627 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 12:01:12.631 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3074 ms 
[INFO ] 2024-05-17 12:01:12.633 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 12:01:12.690 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 12:01:12.690 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 12:01:12.691 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 12:01:12.691 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 12:01:12.693 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 62 ms 
[INFO ] 2024-05-17 12:01:13.548 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 12:01:13.549 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 12:01:13.579 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 12:01:13.579 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 12:01:13.579 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 12:02:10.129 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 12:02:10.131 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 12:02:10.185 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 12:02:10.363 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 12:02:10.363 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:02:10.363 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:02:10.385 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 19 ms 
[INFO ] 2024-05-17 12:02:10.385 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 19 ms 
[INFO ] 2024-05-17 12:02:11.437 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 12:02:11.446 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 12:02:11.447 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 12:02:11.462 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FA0001F8600003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:02:11.524 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 12:02:11.524 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 12:02:11.524 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 12:02:11.532 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 12:02:11.612 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 12:02:11.621 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:02:11.621 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 12:02:11.621 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:02:11.621 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FA0001F8600003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:02:11.940 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 12:02:11.940 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 12:02:12.140 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 12:02:18.143 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 12:02:18.144 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 12:02:52.264 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 12:02:52.647 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 12:02:55.700 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 12:02:55.701 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 12:02:55.701 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 12:02:55.702 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 12:02:55.704 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3057 ms 
[INFO ] 2024-05-17 12:02:55.763 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 12:02:55.763 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 12:02:55.763 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 12:02:55.764 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 12:02:55.764 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 12:02:55.967 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 61 ms 
[INFO ] 2024-05-17 12:02:58.711 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 12:02:58.711 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 12:02:58.711 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 12:02:58.754 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 12:02:58.755 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 12:03:44.527 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 12:03:44.528 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 12:03:44.581 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 12:03:44.753 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 12:03:44.758 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:03:44.758 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:03:44.787 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 28 ms 
[INFO ] 2024-05-17 12:03:44.788 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 29 ms 
[INFO ] 2024-05-17 12:03:45.639 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 12:03:45.654 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 12:03:45.654 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 12:03:45.797 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FA0001FD200003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:03:45.798 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 12:03:45.798 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 12:03:45.798 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 12:03:45.806 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 12:03:45.882 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 12:03:45.889 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:03:45.889 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 12:03:45.889 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:03:45.892 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FA0001FD200003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:03:46.093 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 12:03:46.204 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 12:03:50.213 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 12:03:56.704 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 12:03:56.711 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 12:08:07.009 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 12:08:07.146 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 12:08:10.201 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 12:08:10.201 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 12:08:10.202 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 12:08:10.202 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 12:08:10.207 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3068 ms 
[INFO ] 2024-05-17 12:08:10.211 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 12:08:10.259 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 12:08:10.259 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 12:08:10.260 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 12:08:10.260 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 12:08:10.467 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 56 ms 
[INFO ] 2024-05-17 12:08:10.691 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 12:08:10.693 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 12:08:10.693 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 12:08:10.709 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 12:08:10.709 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 12:16:19.406 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 12:16:19.409 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 12:16:19.409 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 12:16:19.409 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 12:16:19.409 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:16:19.409 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 12:16:19.409 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 33 ms 
[INFO ] 2024-05-17 12:16:19.409 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 33 ms 
[INFO ] 2024-05-17 12:16:19.957 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 12:16:19.961 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 12:16:19.961 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 12:16:20.166 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FB00002358001D","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:16:20.167 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 12:16:20.260 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 12:16:20.265 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 12:16:20.273 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 12:16:20.396 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 12:16:20.400 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:16:20.401 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 12:16:20.401 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 12:16:20.608 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FB00002358001D","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 12:16:20.640 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 12:16:20.743 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 12:16:20.744 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 12:16:26.803 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 12:16:27.015 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 13:26:49.778 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 13:26:50.188 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 13:26:53.091 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:26:53.092 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:26:53.092 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 13:26:53.092 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 13:26:53.094 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3044 ms 
[INFO ] 2024-05-17 13:26:53.094 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 13:26:53.152 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:26:53.152 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:26:53.152 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 13:26:53.152 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 13:26:53.152 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 57 ms 
[INFO ] 2024-05-17 13:26:53.341 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 13:26:53.341 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 13:26:53.341 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 13:26:53.378 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:26:53.583 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:28:51.914 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 13:28:51.915 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 13:28:52.001 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 13:28:52.211 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 13:28:52.268 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:28:52.268 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:28:52.268 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 47 ms 
[INFO ] 2024-05-17 13:28:52.268 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 46 ms 
[INFO ] 2024-05-17 13:28:53.083 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 13:28:53.084 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 13:28:53.084 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 13:28:53.121 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FB0000BC10001D","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:28:53.122 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 13:28:53.258 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 13:28:53.267 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 13:28:53.268 - [任务 7][my_table] - Table my_table is going to be initial synced 
[WARN ] 2024-05-17 13:34:06.987 - [任务 7][my_table] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLTransientConnectionException: HikariPool-81 - Connection is not available, request timed out after 74665ms.
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-05-17 13:34:07.502 - [任务 7][test_timestamp] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@5fcf3b09 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@5fcf3b09 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more


<-- Simple Stack Trace -->
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	java.io.FilterInputStream.read(FilterInputStream.java:107)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@5fcf3b09 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@5fcf3b09 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:85)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 5018427
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more

[INFO ] 2024-05-17 13:34:07.512 - [任务 7][test_timestamp] - Job suspend in error handle 
[INFO ] 2024-05-17 13:34:07.651 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 13:34:07.652 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:34:07.652 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:34:07.653 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 13:34:07.656 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 13:34:07.659 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 34 ms 
[INFO ] 2024-05-17 13:34:07.661 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 13:34:07.661 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:34:07.662 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 13:34:07.662 - [任务 7][my_table] - Incremental sync completed 
[INFO ] 2024-05-17 13:34:07.669 - [任务 7][test_timestamp] - PDK connector node stopped: null 
[INFO ] 2024-05-17 13:34:07.669 - [任务 7][test_timestamp] - PDK connector node released: null 
[INFO ] 2024-05-17 13:34:07.670 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 13:34:07.670 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 13:34:07.880 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 15 ms 
[INFO ] 2024-05-17 13:34:11.638 - [任务 7] - Task [任务 7] cannot retry, reason: Sync progress is empty 
[INFO ] 2024-05-17 13:34:11.659 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 13:34:11.659 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 13:34:11.679 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 13:34:11.679 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 13:34:11.698 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:34:11.701 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:35:15.759 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 13:35:15.760 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 13:35:15.880 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 13:35:15.882 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 13:35:15.989 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:35:15.992 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:35:15.993 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 33 ms 
[INFO ] 2024-05-17 13:35:15.993 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 33 ms 
[INFO ] 2024-05-17 13:35:16.950 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 13:35:16.950 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 13:35:16.950 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 13:35:16.974 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FB0000CF800003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:35:16.974 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 13:35:17.032 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 13:35:17.038 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 13:35:17.038 - [任务 7][my_table] - Table my_table is going to be initial synced 
[WARN ] 2024-05-17 13:39:04.821 - [任务 7][my_table] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLTransientConnectionException: HikariPool-84 - Connection is not available, request timed out after 33654ms.
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-17 13:39:10.391 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 13:39:10.597 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 13:41:50.030 - [任务 7][my_table] - [Auto Retry] Method (source_batch_count) retry succeed 
[INFO ] 2024-05-17 13:41:50.031 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 13:41:50.031 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:41:50.032 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 13:41:50.032 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:41:50.040 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FB0000CF800003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:41:50.312 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 13:41:50.452 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 13:41:50.452 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 13:43:58.384 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 13:43:58.791 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 13:44:01.748 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:44:01.748 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:44:01.751 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 13:44:01.751 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 13:44:01.753 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3061 ms 
[INFO ] 2024-05-17 13:44:01.753 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 13:44:01.796 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:44:01.796 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:44:01.796 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 13:44:01.796 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 13:44:01.998 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 44 ms 
[INFO ] 2024-05-17 13:44:04.916 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 13:44:04.917 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 13:44:04.979 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 13:44:04.980 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:44:04.980 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:44:16.401 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 13:44:16.402 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 13:44:16.535 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 13:44:16.539 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 13:44:16.692 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:44:16.692 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:44:16.692 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 75 ms 
[INFO ] 2024-05-17 13:44:16.692 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 75 ms 
[INFO ] 2024-05-17 13:44:17.794 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 13:44:17.794 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 13:44:17.794 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 13:44:17.815 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FB0000EBA00005","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:44:17.815 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 13:44:17.866 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 13:44:17.866 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 13:44:17.949 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 13:44:17.949 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 13:44:17.949 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:44:17.950 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 13:44:17.966 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:44:17.966 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FB0000EBA00005","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:44:18.152 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 13:44:18.240 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 13:44:18.240 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 13:44:23.754 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 13:44:23.957 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 13:48:04.154 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 13:48:04.770 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 13:48:07.641 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:48:07.641 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:48:07.643 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 13:48:07.643 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 13:48:07.645 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3067 ms 
[INFO ] 2024-05-17 13:48:07.645 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 13:48:07.697 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:48:07.697 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:48:07.697 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 13:48:07.697 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 13:48:07.909 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 54 ms 
[INFO ] 2024-05-17 13:48:08.663 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 13:48:08.663 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 13:48:08.663 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 13:48:08.682 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:48:08.684 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:49:20.544 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 13:49:20.546 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 13:49:20.594 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 13:49:20.685 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 13:49:20.685 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:49:20.686 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:49:20.716 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 31 ms 
[INFO ] 2024-05-17 13:49:20.716 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 31 ms 
[INFO ] 2024-05-17 13:49:21.678 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 13:49:21.678 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 13:49:21.678 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 13:49:21.696 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FB0000FBB80003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:49:21.696 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 13:49:21.741 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 13:49:21.750 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 13:49:21.750 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 13:49:21.868 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 13:49:21.871 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:49:21.871 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 13:49:21.871 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:49:22.074 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FB0000FBB80003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:49:22.075 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 13:49:22.153 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 13:49:22.356 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 13:49:27.819 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 13:49:28.036 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 13:52:14.983 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 13:52:15.168 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 13:52:18.245 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:52:18.246 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:52:18.246 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 13:52:18.246 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 13:52:18.247 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3087 ms 
[INFO ] 2024-05-17 13:52:18.247 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 13:52:18.289 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:52:18.289 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:52:18.290 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 13:52:18.290 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 13:52:18.499 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 43 ms 
[INFO ] 2024-05-17 13:52:23.260 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 13:52:23.261 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 13:52:23.261 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 13:52:23.301 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:52:23.301 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:52:33.387 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 13:52:33.388 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 13:52:33.454 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 13:52:33.454 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 13:52:33.490 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:52:33.491 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:52:33.529 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 38 ms 
[INFO ] 2024-05-17 13:52:33.529 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 38 ms 
[INFO ] 2024-05-17 13:52:34.567 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 13:52:34.567 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 13:52:34.567 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 13:52:34.590 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FB000105C8002B","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:52:34.590 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 13:52:34.665 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 13:52:34.665 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 13:52:34.745 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 13:52:34.745 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 13:52:34.746 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:52:34.746 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 13:52:34.750 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 13:52:34.750 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FB000105C8002B","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:52:34.954 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 13:52:35.017 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 13:52:35.017 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 13:52:40.797 - [任务 7][test_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 13:52:41.004 - [任务 7][test_timestamp] - The table test_timestamp has already exist. 
[INFO ] 2024-05-17 13:53:37.917 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 13:53:37.917 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 13:53:40.949 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:53:40.952 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 13:53:40.952 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 13:53:40.952 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 13:53:40.954 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3041 ms 
[INFO ] 2024-05-17 13:53:40.954 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 13:53:41.151 - [任务 7][test_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:53:41.151 - [任务 7][test_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 13:53:41.152 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 13:53:41.152 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 13:53:41.359 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 199 ms 
[INFO ] 2024-05-17 13:53:41.526 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 13:53:41.526 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 13:53:41.526 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 13:53:41.550 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:53:41.552 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 13:55:00.263 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 13:55:00.263 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 13:55:00.310 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 13:55:00.400 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 13:55:00.400 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:55:00.400 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 13:55:00.432 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 31 ms 
[INFO ] 2024-05-17 13:55:00.433 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 30 ms 
[INFO ] 2024-05-17 13:55:01.423 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 13:55:01.424 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 13:55:01.424 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 13:55:01.447 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FB00010D580005","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 13:55:01.448 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 13:55:01.549 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 13:55:01.556 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 13:55:01.556 - [任务 7][my_table] - Table my_table is going to be initial synced 
[WARN ] 2024-05-17 14:05:36.989 - [任务 7][my_table] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLTransientConnectionException: HikariPool-92 - Connection is not available, request timed out after 55257ms.
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-05-17 14:05:36.992 - [任务 7][test_timestamp] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@2adce4c1 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@2adce4c1 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more


<-- Simple Stack Trace -->
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	java.io.FilterInputStream.read(FilterInputStream.java:107)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@2adce4c1 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2446/800793632@2adce4c1 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:85)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:374)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: doris-io.tapdata.connector.doris-1.0-SNAPSHOT-public, message: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	... 8 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467.
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.downloadFile(RestTemplateOperator.java:544)
	at com.tapdata.mongo.HttpClientMongoOperator.downloadFile(HttpClientMongoOperator.java:622)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:85)
	at io.tapdata.flow.engine.V2.util.PdkUtil.downloadPdkFileIfNeed(PdkUtil.java:59)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:206)
	... 10 more
Caused by: org.springframework.web.client.RestClientException: Error while extracting response for type [interface org.springframework.core.io.Resource] and content type [application/octet-stream]; nested exception is org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:115)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:1008)
	at org.springframework.web.client.RestTemplate$ResponseEntityResponseExtractor.extractData(RestTemplate.java:991)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:732)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$downloadFile$15(RestTemplateOperator.java:564)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 15 more
Caused by: org.apache.http.ConnectionClosedException: Premature end of Content-Length delimited message body (expected: 51372420; received: 18345467
	at org.apache.http.impl.io.ContentLengthInputStream.read(ContentLengthInputStream.java:178)
	at org.apache.http.conn.EofSensorInputStream.read(EofSensorInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at java.io.PushbackInputStream.read(PushbackInputStream.java:186)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:139)
	at org.springframework.util.StreamUtils.copyToByteArray(StreamUtils.java:66)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:90)
	at org.springframework.http.converter.ResourceHttpMessageConverter.readInternal(ResourceHttpMessageConverter.java:45)
	at org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:196)
	at org.springframework.web.client.HttpMessageConverterExtractor.extractData(HttpMessageConverterExtractor.java:109)
	... 22 more

[INFO ] 2024-05-17 14:05:36.993 - [任务 7][test_timestamp] - Job suspend in error handle 
[INFO ] 2024-05-17 14:05:37.163 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 14:05:37.191 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 14:05:37.191 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 14:05:37.192 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 14:05:37.192 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 14:05:37.195 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 32 ms 
[INFO ] 2024-05-17 14:05:37.195 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 14:05:37.206 - [任务 7][test_timestamp] - PDK connector node stopped: null 
[INFO ] 2024-05-17 14:05:37.211 - [任务 7][test_timestamp] - PDK connector node released: null 
[INFO ] 2024-05-17 14:05:37.211 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 14:05:37.211 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 14:05:37.211 - [任务 7][test_timestamp] - Node test_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 11 ms 
[INFO ] 2024-05-17 14:05:41.201 - [任务 7] - Task [任务 7] cannot retry, reason: Sync progress is empty 
[INFO ] 2024-05-17 14:05:41.214 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 14:05:41.214 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 14:05:41.246 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 14:05:41.246 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 14:05:41.283 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 14:05:41.284 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 14:06:07.318 - [任务 7][my_table] - Cancel query 'my_table' snapshot row size with task stopped. 
[INFO ] 2024-05-17 15:18:50.534 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 15:18:50.559 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 15:18:50.561 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:18:50.769 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:18:50.843 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:18:50.844 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:18:50.872 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 62 ms 
[INFO ] 2024-05-17 15:18:50.877 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 22 ms 
[INFO ] 2024-05-17 15:18:51.726 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:18:51.726 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:18:51.726 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:18:51.803 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC00000DF00003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:18:51.910 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:18:51.910 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:18:51.936 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:18:51.937 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:20:11.508 - [任务 7][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:20:11.512 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 15:20:11.513 - [任务 7][test2_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:20:11.513 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:20:11.514 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:20:11.514 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:20:11.526 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC00000DF00003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:20:11.742 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:20:11.883 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:20:11.883 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[WARN ] 2024-05-17 15:20:38.998 - [任务 7][test2_timestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-17 15:21:12.110 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 15:21:12.111 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 15:21:15.186 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 15:21:15.187 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 15:21:15.187 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 15:21:15.190 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 15:21:15.190 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3087 ms 
[INFO ] 2024-05-17 15:21:15.220 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[INFO ] 2024-05-17 15:21:15.220 - [任务 7][test2_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 15:21:15.225 - [任务 7][test2_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 15:21:15.225 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 15:21:15.225 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 15:21:15.281 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 35 ms 
[ERROR] 2024-05-17 15:21:15.285 - [任务 7][test2_timestamp] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: Connection pool shut down
	org.apache.http.util.Asserts.check(Asserts.java:34)
	org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:531)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.connector.doris.streamload.exception.DorisRuntimeException: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:224)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	at io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 26 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:179)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	... 29 more
Caused by: java.lang.IllegalStateException: Connection pool shut down
	at org.apache.http.util.Asserts.check(Asserts.java:34)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	... 30 more

[INFO ] 2024-05-17 15:21:18.689 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:21:18.690 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:21:18.691 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:21:18.753 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 15:21:18.755 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 15:21:50.484 - [任务 7] - Start task milestones: 6645e5a466673e2fff77dc0f(任务 7) 
[INFO ] 2024-05-17 15:21:50.505 - [任务 7] - Task initialization... 
[INFO ] 2024-05-17 15:21:50.595 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:21:50.596 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:21:50.648 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:21:50.648 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:21:50.676 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] preload schema finished, cost 22 ms 
[INFO ] 2024-05-17 15:21:50.678 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] preload schema finished, cost 20 ms 
[INFO ] 2024-05-17 15:21:51.541 - [任务 7][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:21:51.542 - [任务 7][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:21:51.543 - [任务 7][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:21:51.563 - [任务 7][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC000017080003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:21:51.619 - [任务 7][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:21:51.619 - [任务 7][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:21:51.622 - [任务 7][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:21:51.623 - [任务 7][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:21:51.719 - [任务 7][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:21:51.720 - [任务 7][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 15:21:51.720 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:21:51.721 - [任务 7][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:21:51.722 - [任务 7][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:21:51.722 - [任务 7][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC000017080003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:21:51.913 - [任务 7][test2_timestamp] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:21:51.914 - [任务 7][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:21:52.009 - [任务 7][test2_timestamp] - The table test2_timestamp has already exist. 
[INFO ] 2024-05-17 15:21:52.010 - [任务 7][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:21:52.211 - [任务 7][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[WARN ] 2024-05-17 15:21:52.582 - [任务 7][test2_timestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-17 15:22:52.881 - [任务 7][test2_timestamp] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-17 15:23:02.335 - [任务 7] - Stop task milestones: 6645e5a466673e2fff77dc0f(任务 7)  
[INFO ] 2024-05-17 15:23:02.950 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] running status set to false 
[INFO ] 2024-05-17 15:23:05.890 - [任务 7][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 15:23:05.893 - [任务 7][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-45d55324-c828-43ab-b021-5af5a7f9be08 
[INFO ] 2024-05-17 15:23:05.894 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] schema data cleaned 
[INFO ] 2024-05-17 15:23:05.895 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] monitor closed 
[INFO ] 2024-05-17 15:23:05.897 - [任务 7][my_table] - Node my_table[45d55324-c828-43ab-b021-5af5a7f9be08] close complete, cost 3092 ms 
[INFO ] 2024-05-17 15:23:05.897 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] running status set to false 
[ERROR] 2024-05-17 15:23:05.925 - [任务 7][test2_timestamp] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: Connection pool shut down
	org.apache.http.util.Asserts.check(Asserts.java:34)
	org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2_timestamp
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:531)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.connector.doris.streamload.exception.DorisRuntimeException: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:224)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	at io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 26 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:179)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	... 29 more
Caused by: java.lang.IllegalStateException: Connection pool shut down
	at org.apache.http.util.Asserts.check(Asserts.java:34)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	... 30 more

[INFO ] 2024-05-17 15:23:05.941 - [任务 7][test2_timestamp] - PDK connector node stopped: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 15:23:05.941 - [任务 7][test2_timestamp] - PDK connector node released: HazelcastTargetPdkDataNode-11536612-9ba3-4d24-87fd-51b69100eccb 
[INFO ] 2024-05-17 15:23:05.941 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] schema data cleaned 
[INFO ] 2024-05-17 15:23:05.941 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] monitor closed 
[INFO ] 2024-05-17 15:23:05.943 - [任务 7][test2_timestamp] - Node test2_timestamp[11536612-9ba3-4d24-87fd-51b69100eccb] close complete, cost 45 ms 
[INFO ] 2024-05-17 15:23:08.904 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:23:08.905 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:23:08.905 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:23:08.953 - [任务 7] - Remove memory task client succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
[INFO ] 2024-05-17 15:23:08.954 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6645e5a466673e2fff77dc0f] 
