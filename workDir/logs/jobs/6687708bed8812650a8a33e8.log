[INFO ] 2024-07-05 12:03:24.201 - [来自TAPDATA2Oracle的共享挖掘任务] - Start task milestones: 6687708bed8812650a8a33e8(来自TAPDATA2Oracle的共享挖掘任务) 
[INFO ] 2024-07-05 12:03:24.292 - [来自TAPDATA2Oracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 12:03:24.293 - [来自TAPDATA2Oracle的共享挖掘任务] - The engine receives 来自TAPDATA2Oracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 12:03:24.372 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] start preload schema,table counts: 2 
[INFO ] 2024-07-05 12:03:24.372 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 12:03:24.386 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 12:03:24.386 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 12:03:24.404 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c769, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1700608932, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:03:24.408 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:03:25.125 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: -1 
[INFO ] 2024-07-05 12:03:25.316 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: -1 
[INFO ] 2024-07-05 12:03:25.519 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 12:03:26.880 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" read batch size: 2000 
[INFO ] 2024-07-05 12:03:26.882 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" event queue capacity: 4000 
[INFO ] 2024-07-05 12:03:26.884 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 12:03:27.364 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68913979,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:03:27.418 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 12:03:27.529 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Starting stream read, table list: [C##TAPDATA2.POLICY, C##TAPDATA2._tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":68913979,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:03:27.699 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - total start mining scn: 68913979 
[INFO ] 2024-07-05 12:03:29.123 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 12:04:58.094 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] running status set to false 
[INFO ] 2024-07-05 12:04:58.180 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 12:04:58.180 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 12:04:58.203 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 
[ERROR] 2024-07-05 12:04:58.204 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamReadMultiConnection(OracleConnector.java:424)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$25(HazelcastSourcePdkDataNode.java:701)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 68916542 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND (SEG_OWNER = 'C##TAPDATA2' AND TABLE_NAME IN ('POLICY','_tapdata_heartbeat_table')))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 19 more

[INFO ] 2024-07-05 12:04:58.290 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-2dec3dcde8194dd48807198d5c68dd17 
[INFO ] 2024-07-05 12:04:58.290 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - PDK connector node released: HazelcastSourcePdkShareCDCNode-2dec3dcde8194dd48807198d5c68dd17 
[INFO ] 2024-07-05 12:04:58.290 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] schema data cleaned 
[INFO ] 2024-07-05 12:04:58.290 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] monitor closed 
[INFO ] 2024-07-05 12:04:58.291 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] close complete, cost 215 ms 
[INFO ] 2024-07-05 12:04:58.291 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] running status set to false 
[INFO ] 2024-07-05 12:04:58.307 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-05 12:04:58.316 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-05 12:04:58.316 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] schema data cleaned 
[INFO ] 2024-07-05 12:04:58.316 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] monitor closed 
[INFO ] 2024-07-05 12:04:58.316 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] close complete, cost 16 ms 
[INFO ] 2024-07-05 12:04:58.751 - [来自TAPDATA2Oracle的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:04:58.751 - [来自TAPDATA2Oracle的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1734747e 
[INFO ] 2024-07-05 12:04:58.751 - [来自TAPDATA2Oracle的共享挖掘任务] - Stop task milestones: 6687708bed8812650a8a33e8(来自TAPDATA2Oracle的共享挖掘任务)  
[INFO ] 2024-07-05 12:04:58.881 - [来自TAPDATA2Oracle的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:04:58.881 - [来自TAPDATA2Oracle的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:04:58.923 - [来自TAPDATA2Oracle的共享挖掘任务] - Remove memory task client succeed, task: 来自TAPDATA2Oracle的共享挖掘任务[6687708bed8812650a8a33e8] 
[INFO ] 2024-07-05 12:04:58.926 - [来自TAPDATA2Oracle的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自TAPDATA2Oracle的共享挖掘任务[6687708bed8812650a8a33e8] 
[INFO ] 2024-07-05 12:05:07.293 - [来自TAPDATA2Oracle的共享挖掘任务] - Start task milestones: 6687708bed8812650a8a33e8(来自TAPDATA2Oracle的共享挖掘任务) 
[INFO ] 2024-07-05 12:05:07.496 - [来自TAPDATA2Oracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 12:05:07.595 - [来自TAPDATA2Oracle的共享挖掘任务] - The engine receives 来自TAPDATA2Oracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 12:05:07.595 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] start preload schema,table counts: 2 
[INFO ] 2024-07-05 12:05:07.595 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 12:05:07.595 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 12:05:07.607 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 12:05:07.607 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:05:07.607 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c769, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1700608932, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-05 12:05:07.625 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 12:05:07.625 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 91 
[INFO ] 2024-07-05 12:05:07.828 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 12:05:08.600 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" read batch size: 2000 
[INFO ] 2024-07-05 12:05:08.600 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Source node "TAPDATA2Oracle" event queue capacity: 4000 
[INFO ] 2024-07-05 12:05:08.602 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-05 12:05:08.605 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68916542,"pendingScn":68916543,"timestamp":1720152297000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:05:08.608 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 12:05:08.809 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Starting stream read, table list: [C##TAPDATA2.POLICY, C##TAPDATA2._tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":68916542,"pendingScn":68916543,"timestamp":1720152297000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:05:09.209 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - total start mining scn: 68916542 
[INFO ] 2024-07-05 12:05:10.332 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 12:07:55.578 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] running status set to false 
[INFO ] 2024-07-05 12:07:55.598 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 12:07:55.605 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 12:07:55.616 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-07-05 12:07:55.619 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamReadMultiConnection(OracleConnector.java:424)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$25(HazelcastSourcePdkDataNode.java:701)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 19 more

[INFO ] 2024-07-05 12:07:55.661 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-2dec3dcde8194dd48807198d5c68dd17 
[INFO ] 2024-07-05 12:07:55.661 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - PDK connector node released: HazelcastSourcePdkShareCDCNode-2dec3dcde8194dd48807198d5c68dd17 
[INFO ] 2024-07-05 12:07:55.662 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] schema data cleaned 
[INFO ] 2024-07-05 12:07:55.662 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] monitor closed 
[INFO ] 2024-07-05 12:07:55.665 - [来自TAPDATA2Oracle的共享挖掘任务][TAPDATA2Oracle] - Node TAPDATA2Oracle[2dec3dcde8194dd48807198d5c68dd17] close complete, cost 87 ms 
[INFO ] 2024-07-05 12:07:55.684 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] running status set to false 
[INFO ] 2024-07-05 12:07:55.691 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-05 12:07:55.691 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-05 12:07:55.691 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] schema data cleaned 
[INFO ] 2024-07-05 12:07:55.691 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] monitor closed 
[INFO ] 2024-07-05 12:07:55.691 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[06bc4eb34c784bc6934f1d3c4cbb0ed7] close complete, cost 20 ms 
[INFO ] 2024-07-05 12:08:00.307 - [来自TAPDATA2Oracle的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:08:00.312 - [来自TAPDATA2Oracle的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@324907b1 
[INFO ] 2024-07-05 12:08:00.312 - [来自TAPDATA2Oracle的共享挖掘任务] - Stop task milestones: 6687708bed8812650a8a33e8(来自TAPDATA2Oracle的共享挖掘任务)  
[INFO ] 2024-07-05 12:08:00.433 - [来自TAPDATA2Oracle的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:08:00.464 - [来自TAPDATA2Oracle的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:08:00.468 - [来自TAPDATA2Oracle的共享挖掘任务] - Remove memory task client succeed, task: 来自TAPDATA2Oracle的共享挖掘任务[6687708bed8812650a8a33e8] 
[INFO ] 2024-07-05 12:08:00.468 - [来自TAPDATA2Oracle的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自TAPDATA2Oracle的共享挖掘任务[6687708bed8812650a8a33e8] 
[INFO ] 2024-07-05 12:14:28.185 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 265 
[INFO ] 2024-07-05 12:14:28.210 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 12:27:04.054 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1700608932', head seq: 0, tail seq: 1016 
[INFO ] 2024-07-05 12:27:04.057 - [来自TAPDATA2Oracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
