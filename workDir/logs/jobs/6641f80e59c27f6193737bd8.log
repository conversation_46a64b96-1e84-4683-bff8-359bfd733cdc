[INFO ] 2024-05-22 12:49:42.816 - [任务 3] - Start task milestones: 6641f80e59c27f6193737bd8(任务 3) 
[INFO ] 2024-05-22 12:49:42.827 - [任务 3] - Task initialization... 
[INFO ] 2024-05-22 12:49:42.828 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-22 12:49:42.828 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-22 12:49:42.828 - [任务 3][TEST2] - Node TEST2[e4c055d8-1409-467d-9803-f4ea90ae179b] start preload schema,table counts: 1 
[INFO ] 2024-05-22 12:49:42.828 - [任务 3][test2] - Node test2[5d23483f-e0d7-4a60-bb1e-86175f486eaf] start preload schema,table counts: 1 
[INFO ] 2024-05-22 12:49:42.875 - [任务 3][TEST2] - Node TEST2[e4c055d8-1409-467d-9803-f4ea90ae179b] preload schema finished, cost 58 ms 
[INFO ] 2024-05-22 12:49:42.875 - [任务 3][test2] - Node test2[5d23483f-e0d7-4a60-bb1e-86175f486eaf] preload schema finished, cost 58 ms 
[INFO ] 2024-05-22 12:49:44.164 - [任务 3][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-22 12:49:44.166 - [任务 3][TEST2] - Source node "TEST2" read batch size: 100 
[INFO ] 2024-05-22 12:49:44.166 - [任务 3][TEST2] - Source node "TEST2" event queue capacity: 200 
[INFO ] 2024-05-22 12:49:44.166 - [任务 3][TEST2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-22 12:49:44.352 - [任务 3][TEST2] - batch offset found: {},stream offset found: {"cdcOffset":1716353384,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-22 12:49:44.431 - [任务 3][TEST2] - Initial sync started 
[INFO ] 2024-05-22 12:49:44.432 - [任务 3][TEST2] - Starting batch read, table name: TEST2, offset: null 
[INFO ] 2024-05-22 12:49:44.466 - [任务 3][TEST2] - Table TEST2 is going to be initial synced 
[INFO ] 2024-05-22 12:49:44.469 - [任务 3][TEST2] - Query table 'TEST2' counts: 1076 
[INFO ] 2024-05-22 12:49:48.809 - [任务 3][TEST2] - Table [TEST2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-22 12:49:48.820 - [任务 3][TEST2] - Initial sync completed 
[INFO ] 2024-05-22 12:49:48.820 - [任务 3][TEST2] - Incremental sync starting... 
[INFO ] 2024-05-22 12:49:48.820 - [任务 3][TEST2] - Initial sync completed 
[INFO ] 2024-05-22 12:49:48.820 - [任务 3][TEST2] - Starting stream read, table list: [TEST2], offset: {"cdcOffset":1716353384,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-22 12:49:48.878 - [任务 3][TEST2] - Connector MongoDB incremental start succeed, tables: [TEST2], data change syncing 
[WARN ] 2024-05-22 12:50:34.190 - [任务 3][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-22 12:51:35.422 - [任务 3][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-22 12:51:51.912 - [任务 3][test2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLException: Connection is closed
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-22 12:52:07.361 - [任务 3][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-22 12:53:09.307 - [任务 3][test2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLException: Connection is closed
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-22 12:53:09.307 - [任务 3][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: time out
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-22 12:54:25.711 - [任务 3][test2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLException: Connection is closed
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-22 12:54:26.829 - [任务 3][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-22 12:55:37.707 - [任务 3][test2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLException: Connection is closed
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-22 12:56:40.602 - [任务 3][test2] - [Auto Retry] Method (target_write_record) retry succeed 
[INFO ] 2024-05-22 12:57:18.024 - [任务 3] - Stop task milestones: 6641f80e59c27f6193737bd8(任务 3)  
[INFO ] 2024-05-22 12:57:18.179 - [任务 3][TEST2] - Node TEST2[e4c055d8-1409-467d-9803-f4ea90ae179b] running status set to false 
[INFO ] 2024-05-22 12:57:18.214 - [任务 3][TEST2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e4c055d8-1409-467d-9803-f4ea90ae179b 
[INFO ] 2024-05-22 12:57:18.215 - [任务 3][TEST2] - PDK connector node released: HazelcastSourcePdkDataNode-e4c055d8-1409-467d-9803-f4ea90ae179b 
[INFO ] 2024-05-22 12:57:18.216 - [任务 3][TEST2] - Node TEST2[e4c055d8-1409-467d-9803-f4ea90ae179b] schema data cleaned 
[INFO ] 2024-05-22 12:57:18.216 - [任务 3][TEST2] - Node TEST2[e4c055d8-1409-467d-9803-f4ea90ae179b] monitor closed 
[INFO ] 2024-05-22 12:57:18.216 - [任务 3][TEST2] - Node TEST2[e4c055d8-1409-467d-9803-f4ea90ae179b] close complete, cost 43 ms 
[INFO ] 2024-05-22 12:57:18.216 - [任务 3][test2] - Node test2[5d23483f-e0d7-4a60-bb1e-86175f486eaf] running status set to false 
[INFO ] 2024-05-22 12:57:18.248 - [任务 3][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-5d23483f-e0d7-4a60-bb1e-86175f486eaf 
[INFO ] 2024-05-22 12:57:18.248 - [任务 3][test2] - PDK connector node released: HazelcastTargetPdkDataNode-5d23483f-e0d7-4a60-bb1e-86175f486eaf 
[INFO ] 2024-05-22 12:57:18.248 - [任务 3][test2] - Node test2[5d23483f-e0d7-4a60-bb1e-86175f486eaf] schema data cleaned 
[INFO ] 2024-05-22 12:57:18.249 - [任务 3][test2] - Node test2[5d23483f-e0d7-4a60-bb1e-86175f486eaf] monitor closed 
[INFO ] 2024-05-22 12:57:18.249 - [任务 3][test2] - Node test2[5d23483f-e0d7-4a60-bb1e-86175f486eaf] close complete, cost 32 ms 
[INFO ] 2024-05-22 12:57:22.483 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-22 12:57:22.483 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-05-22 12:57:22.483 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-05-22 12:57:22.506 - [任务 3] - Remove memory task client succeed, task: 任务 3[6641f80e59c27f6193737bd8] 
[INFO ] 2024-05-22 12:57:22.506 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[6641f80e59c27f6193737bd8] 
