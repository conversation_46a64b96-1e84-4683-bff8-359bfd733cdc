[INFO ] 2024-07-18 11:51:49.881 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Task initialization... 
[INFO ] 2024-07-18 11:51:49.881 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Start task milestones: 6698914a8315b25db9f542b7(t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672) 
[INFO ] 2024-07-18 11:51:50.084 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:51:50.197 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - The engine receives t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:51:50.198 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[199cbd90-f6c2-4582-a96d-576793ac5cfe] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:51:50.198 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[b9f7d1f7-b745-4bcf-8c8d-57916e92738c] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:51:50.198 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[199cbd90-f6c2-4582-a96d-576793ac5cfe] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:51:50.198 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[b9f7d1f7-b745-4bcf-8c8d-57916e92738c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:51:50.588 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:51:50.692 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Source node "qa_mongodb_repl_36230_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:51:50.694 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Source node "qa_mongodb_repl_36230_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:51:50.694 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:51:50.910 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":{"rs0":{"seconds":1721274710,"inc":0}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:51:51.019 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:51:51.019 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Starting batch read, table name: t_414, offset: null 
[INFO ] 2024-07-18 11:51:51.060 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Table t_414 is going to be initial synced 
[INFO ] 2024-07-18 11:51:51.060 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Table [t_414] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:51:51.095 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Query table 't_414' counts: 1000 
[INFO ] 2024-07-18 11:51:51.095 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:51:51.095 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:51:51.095 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:51:51.096 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Starting stream read, table list: [t_414, _tapdata_heartbeat_table], offset: {"cdcOffset":{"rs0":{"seconds":1721274710,"inc":0}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:51:51.371 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t_414, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:53:58.843 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[b9f7d1f7-b745-4bcf-8c8d-57916e92738c] running status set to false 
[INFO ] 2024-07-18 11:53:58.844 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:53:59.386 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-b9f7d1f7-b745-4bcf-8c8d-57916e92738c 
[INFO ] 2024-07-18 11:53:59.387 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-b9f7d1f7-b745-4bcf-8c8d-57916e92738c 
[INFO ] 2024-07-18 11:53:59.387 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[b9f7d1f7-b745-4bcf-8c8d-57916e92738c] schema data cleaned 
[INFO ] 2024-07-18 11:53:59.387 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[b9f7d1f7-b745-4bcf-8c8d-57916e92738c] monitor closed 
[INFO ] 2024-07-18 11:53:59.389 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[b9f7d1f7-b745-4bcf-8c8d-57916e92738c] close complete, cost 553 ms 
[INFO ] 2024-07-18 11:53:59.389 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[199cbd90-f6c2-4582-a96d-576793ac5cfe] running status set to false 
[INFO ] 2024-07-18 11:53:59.421 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-199cbd90-f6c2-4582-a96d-576793ac5cfe 
[INFO ] 2024-07-18 11:53:59.421 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-199cbd90-f6c2-4582-a96d-576793ac5cfe 
[INFO ] 2024-07-18 11:53:59.421 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[199cbd90-f6c2-4582-a96d-576793ac5cfe] schema data cleaned 
[INFO ] 2024-07-18 11:53:59.421 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[199cbd90-f6c2-4582-a96d-576793ac5cfe] monitor closed 
[INFO ] 2024-07-18 11:53:59.424 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[199cbd90-f6c2-4582-a96d-576793ac5cfe] close complete, cost 33 ms 
[INFO ] 2024-07-18 11:54:01.324 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:54:01.450 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@12a8173 
[INFO ] 2024-07-18 11:54:01.450 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Stop task milestones: 6698914a8315b25db9f542b7(t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672)  
[INFO ] 2024-07-18 11:54:01.481 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:54:01.481 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:54:01.525 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Remove memory task client succeed, task: t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672[6698914a8315b25db9f542b7] 
[INFO ] 2024-07-18 11:54:01.525 - [t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672] - Destroy memory task client cache succeed, task: t_4.1.4-mdb-v3.6.23_to_mdb-v3.6.23_with_check_data_1717403468657_3537-1721274672[6698914a8315b25db9f542b7] 
