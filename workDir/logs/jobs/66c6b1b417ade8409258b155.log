[INFO ] 2024-08-22 11:34:33.064 - [任务 13] - Start task milestones: 66c6b1b417ade8409258b155(任务 13) 
[INFO ] 2024-08-22 11:34:33.065 - [任务 13] - Task initialization... 
[INFO ] 2024-08-22 11:34:33.085 - [任务 13] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 11:34:33.168 - [任务 13] - The engine receives 任务 13 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 11:34:33.169 - [任务 13][DB2TEST] - Node DB2TEST[a8f0baa4-c3bb-42a0-bdfa-832f435582ab] start preload schema,table counts: 2 
[INFO ] 2024-08-22 11:34:33.214 - [任务 13][TestDummy] - Node TestDummy[71feefb4-8a7a-4cfe-ad7b-c9774561b5c1] start preload schema,table counts: 2 
[INFO ] 2024-08-22 11:34:33.215 - [任务 13][DB2TEST] - Node DB2TEST[a8f0baa4-c3bb-42a0-bdfa-832f435582ab] preload schema finished, cost 46 ms 
[INFO ] 2024-08-22 11:34:33.215 - [任务 13][TestDummy] - Node TestDummy[71feefb4-8a7a-4cfe-ad7b-c9774561b5c1] preload schema finished, cost 44 ms 
[INFO ] 2024-08-22 11:34:33.796 - [任务 13][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 11:34:33.796 - [任务 13][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 11:34:33.965 - [任务 13][DB2TEST] - Source node "DB2TEST" read batch size: 100 
[INFO ] 2024-08-22 11:34:33.967 - [任务 13][DB2TEST] - Source node "DB2TEST" event queue capacity: 200 
[INFO ] 2024-08-22 11:34:33.967 - [任务 13][DB2TEST] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 11:34:34.221 - [任务 13][DB2TEST] - Table [CUSTOMERS1, CUSTOMERS] not open CDC 
[INFO ] 2024-08-22 11:34:34.222 - [任务 13][DB2TEST] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724297674220} 
[INFO ] 2024-08-22 11:34:34.222 - [任务 13][DB2TEST] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 11:34:34.317 - [任务 13][DB2TEST] - Initial sync started 
[INFO ] 2024-08-22 11:34:34.321 - [任务 13][DB2TEST] - Starting batch read, table name: CUSTOMERS1, offset: null 
[INFO ] 2024-08-22 11:34:34.322 - [任务 13][DB2TEST] - Table CUSTOMERS1 is going to be initial synced 
[INFO ] 2024-08-22 11:34:34.390 - [任务 13][DB2TEST] - Query table 'CUSTOMERS1' counts: 2 
[INFO ] 2024-08-22 11:34:34.390 - [任务 13][DB2TEST] - Table [CUSTOMERS1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 11:34:34.390 - [任务 13][DB2TEST] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-22 11:34:34.391 - [任务 13][DB2TEST] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-22 11:34:34.409 - [任务 13][DB2TEST] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 11:34:34.411 - [任务 13][DB2TEST] - Query table 'CUSTOMERS' counts: 3 
[INFO ] 2024-08-22 11:34:34.411 - [任务 13][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-22 11:34:34.411 - [任务 13][DB2TEST] - Incremental sync starting... 
[INFO ] 2024-08-22 11:34:34.411 - [任务 13][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-22 11:34:34.613 - [任务 13][DB2TEST] - Starting stream read, table list: [CUSTOMERS1, CUSTOMERS], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724297674220} 
[INFO ] 2024-08-22 12:09:32.491 - [任务 13] - Stop task milestones: 66c6b1b417ade8409258b155(任务 13)  
[INFO ] 2024-08-22 12:09:32.579 - [任务 13][DB2TEST] - Node DB2TEST[a8f0baa4-c3bb-42a0-bdfa-832f435582ab] running status set to false 
[INFO ] 2024-08-22 12:09:32.580 - [任务 13][DB2TEST] - Log Miner is shutting down... 
[ERROR] 2024-08-22 12:09:32.782 - [任务 13][DB2TEST] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-22 12:09:34.754 - [任务 13][DB2TEST] - PDK connector node stopped: HazelcastSourcePdkDataNode-a8f0baa4-c3bb-42a0-bdfa-832f435582ab 
[INFO ] 2024-08-22 12:09:34.754 - [任务 13][DB2TEST] - PDK connector node released: HazelcastSourcePdkDataNode-a8f0baa4-c3bb-42a0-bdfa-832f435582ab 
[INFO ] 2024-08-22 12:09:34.755 - [任务 13][DB2TEST] - Node DB2TEST[a8f0baa4-c3bb-42a0-bdfa-832f435582ab] schema data cleaned 
[INFO ] 2024-08-22 12:09:34.755 - [任务 13][DB2TEST] - Node DB2TEST[a8f0baa4-c3bb-42a0-bdfa-832f435582ab] monitor closed 
[INFO ] 2024-08-22 12:09:34.756 - [任务 13][DB2TEST] - Node DB2TEST[a8f0baa4-c3bb-42a0-bdfa-832f435582ab] close complete, cost 2184 ms 
[INFO ] 2024-08-22 12:09:34.757 - [任务 13][TestDummy] - Node TestDummy[71feefb4-8a7a-4cfe-ad7b-c9774561b5c1] running status set to false 
[INFO ] 2024-08-22 12:09:34.771 - [任务 13][TestDummy] - Stop connector 
[INFO ] 2024-08-22 12:09:34.773 - [任务 13][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-71feefb4-8a7a-4cfe-ad7b-c9774561b5c1 
[INFO ] 2024-08-22 12:09:34.773 - [任务 13][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-71feefb4-8a7a-4cfe-ad7b-c9774561b5c1 
[INFO ] 2024-08-22 12:09:34.773 - [任务 13][TestDummy] - Node TestDummy[71feefb4-8a7a-4cfe-ad7b-c9774561b5c1] schema data cleaned 
[INFO ] 2024-08-22 12:09:34.773 - [任务 13][TestDummy] - Node TestDummy[71feefb4-8a7a-4cfe-ad7b-c9774561b5c1] monitor closed 
[INFO ] 2024-08-22 12:09:34.773 - [任务 13][TestDummy] - Node TestDummy[71feefb4-8a7a-4cfe-ad7b-c9774561b5c1] close complete, cost 17 ms 
[INFO ] 2024-08-22 12:09:38.934 - [任务 13] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 12:09:38.935 - [任务 13] - Stopped task aspect(s) 
[INFO ] 2024-08-22 12:09:38.935 - [任务 13] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 12:09:38.976 - [任务 13] - Remove memory task client succeed, task: 任务 13[66c6b1b417ade8409258b155] 
[INFO ] 2024-08-22 12:09:38.977 - [任务 13] - Destroy memory task client cache succeed, task: 任务 13[66c6b1b417ade8409258b155] 
