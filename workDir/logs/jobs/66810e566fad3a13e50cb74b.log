[INFO ] 2024-06-30 15:51:15.981 - [任务 34] - Task initialization... 
[INFO ] 2024-06-30 15:51:16.190 - [任务 34] - Start task milestones: 66810e566fad3a13e50cb74b(任务 34) 
[INFO ] 2024-06-30 15:51:16.252 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-30 15:51:16.399 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-30 15:51:16.399 - [任务 34][SourceMongo] - Node SourceMongo[5a699828-3856-46c6-bb42-143ccfcbf811] start preload schema,table counts: 2 
[INFO ] 2024-06-30 15:51:16.400 - [任务 34][SourceMongo] - Node SourceMongo[5a699828-3856-46c6-bb42-143ccfcbf811] preload schema finished, cost 0 ms 
[INFO ] 2024-06-30 15:51:16.403 - [任务 34][SouceMysql] - Node SouceMysql[17fbc5c3-3949-4a83-9cdb-743adb866224] start preload schema,table counts: 2 
[INFO ] 2024-06-30 15:51:16.612 - [任务 34][SouceMysql] - Node SouceMysql[17fbc5c3-3949-4a83-9cdb-743adb866224] preload schema finished, cost 0 ms 
[INFO ] 2024-06-30 15:51:17.197 - [任务 34][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-30 15:51:17.204 - [任务 34][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-30 15:51:17.468 - [任务 34][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-30 15:51:17.468 - [任务 34][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-30 15:51:17.473 - [任务 34][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-30 15:51:17.543 - [任务 34][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":157,"gtidSet":""} 
[INFO ] 2024-06-30 15:51:17.543 - [任务 34][SouceMysql] - Initial sync started 
[INFO ] 2024-06-30 15:51:17.560 - [任务 34][SouceMysql] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-06-30 15:51:17.560 - [任务 34][SouceMysql] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-06-30 15:51:17.753 - [任务 34][SouceMysql] - Query table 'CUSTOMER' counts: 674 
[INFO ] 2024-06-30 15:51:17.756 - [任务 34][SouceMysql] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-30 15:51:17.756 - [任务 34][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-30 15:51:17.760 - [任务 34][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-30 15:51:17.770 - [任务 34][SouceMysql] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-30 15:51:17.988 - [任务 34][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-30 15:51:17.988 - [任务 34][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-30 15:51:17.989 - [任务 34][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-30 15:51:17.993 - [任务 34][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-30 15:51:17.993 - [任务 34][SouceMysql] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"filename":"binlog.000032","position":157,"gtidSet":""} 
[INFO ] 2024-06-30 15:51:18.044 - [任务 34][SouceMysql] - Starting mysql cdc, server name: 94a9d34c-a2b5-46c3-9a36-bf0ba6f4678c 
[INFO ] 2024-06-30 15:51:18.044 - [任务 34][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1252419353
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 94a9d34c-a2b5-46c3-9a36-bf0ba6f4678c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-94a9d34c-a2b5-46c3-9a36-bf0ba6f4678c
  database.hostname: localhost
  database.password: ********
  name: 94a9d34c-a2b5-46c3-9a36-bf0ba6f4678c
  pdk.offset.string: {"name":"94a9d34c-a2b5-46c3-9a36-bf0ba6f4678c","offset":{"{\"server\":\"94a9d34c-a2b5-46c3-9a36-bf0ba6f4678c\"}":"{\"file\":\"binlog.000032\",\"pos\":157,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-30 15:51:18.248 - [任务 34][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-06-30 15:52:45.365 - [任务 34] - Stop task milestones: 66810e566fad3a13e50cb74b(任务 34)  
[INFO ] 2024-06-30 15:52:45.366 - [任务 34][SouceMysql] - Node SouceMysql[17fbc5c3-3949-4a83-9cdb-743adb866224] running status set to false 
[INFO ] 2024-06-30 15:52:45.420 - [任务 34][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-30 15:52:45.420 - [任务 34][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-30 15:52:45.459 - [任务 34][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-17fbc5c3-3949-4a83-9cdb-743adb866224 
[INFO ] 2024-06-30 15:52:45.460 - [任务 34][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-17fbc5c3-3949-4a83-9cdb-743adb866224 
[INFO ] 2024-06-30 15:52:45.460 - [任务 34][SouceMysql] - Node SouceMysql[17fbc5c3-3949-4a83-9cdb-743adb866224] schema data cleaned 
[INFO ] 2024-06-30 15:52:45.460 - [任务 34][SouceMysql] - Node SouceMysql[17fbc5c3-3949-4a83-9cdb-743adb866224] monitor closed 
[INFO ] 2024-06-30 15:52:45.461 - [任务 34][SouceMysql] - Node SouceMysql[17fbc5c3-3949-4a83-9cdb-743adb866224] close complete, cost 102 ms 
[INFO ] 2024-06-30 15:52:45.461 - [任务 34][SourceMongo] - Node SourceMongo[5a699828-3856-46c6-bb42-143ccfcbf811] running status set to false 
[INFO ] 2024-06-30 15:52:45.549 - [任务 34][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5a699828-3856-46c6-bb42-143ccfcbf811 
[INFO ] 2024-06-30 15:52:45.549 - [任务 34][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-5a699828-3856-46c6-bb42-143ccfcbf811 
[INFO ] 2024-06-30 15:52:45.551 - [任务 34][SourceMongo] - Node SourceMongo[5a699828-3856-46c6-bb42-143ccfcbf811] schema data cleaned 
[INFO ] 2024-06-30 15:52:45.551 - [任务 34][SourceMongo] - Node SourceMongo[5a699828-3856-46c6-bb42-143ccfcbf811] monitor closed 
[INFO ] 2024-06-30 15:52:45.758 - [任务 34][SourceMongo] - Node SourceMongo[5a699828-3856-46c6-bb42-143ccfcbf811] close complete, cost 90 ms 
[INFO ] 2024-06-30 15:52:46.027 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-30 15:52:46.027 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49d3d658 
[INFO ] 2024-06-30 15:52:46.028 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-06-30 15:52:46.028 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-06-30 15:52:46.073 - [任务 34] - Remove memory task client succeed, task: 任务 34[66810e566fad3a13e50cb74b] 
[INFO ] 2024-06-30 15:52:46.073 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66810e566fad3a13e50cb74b] 
