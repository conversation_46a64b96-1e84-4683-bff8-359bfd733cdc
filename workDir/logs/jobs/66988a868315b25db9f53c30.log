[INFO ] 2024-07-18 11:22:48.036 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Start task milestones: 66988a868315b25db9f53c30(Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537) 
[INFO ] 2024-07-18 11:22:48.444 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:22:48.675 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - The engine receives Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:22:48.697 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b4d583d3-063d-489f-9b06-bb5a515fc13a] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:22:48.697 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[19421b50-707a-46e3-8c25-1d55980d804f] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:22:48.697 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[19421b50-707a-46e3-8c25-1d55980d804f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:22:48.698 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b4d583d3-063d-489f-9b06-bb5a515fc13a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:22:49.002 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 11:22:49.002 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 11:22:49.002 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:22:49.136 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721272969002,"lastTimes":1721272969002,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 11:22:49.201 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-18 11:22:49.202 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 11:22:49.212 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 11:22:49.216 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:22:49.216 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-18 11:22:49.217 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721272969002,"lastTimes":1721272969002,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 11:22:49.217 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 11:22:49.421 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:22:49.624 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:22:49.829 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Table "tpcc_331._tapdata_heartbeat_table" exists, skip auto create table 
[INFO ] 2024-07-18 12:33:19.485 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[19421b50-707a-46e3-8c25-1d55980d804f] running status set to false 
[INFO ] 2024-07-18 12:33:19.486 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-18 12:33:19.486 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-19421b50-707a-46e3-8c25-1d55980d804f 
[INFO ] 2024-07-18 12:33:19.486 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-19421b50-707a-46e3-8c25-1d55980d804f 
[INFO ] 2024-07-18 12:33:19.486 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[19421b50-707a-46e3-8c25-1d55980d804f] schema data cleaned 
[INFO ] 2024-07-18 12:33:19.486 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[19421b50-707a-46e3-8c25-1d55980d804f] monitor closed 
[INFO ] 2024-07-18 12:33:19.487 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[19421b50-707a-46e3-8c25-1d55980d804f] close complete, cost 8 ms 
[INFO ] 2024-07-18 12:33:19.487 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b4d583d3-063d-489f-9b06-bb5a515fc13a] running status set to false 
[INFO ] 2024-07-18 12:33:19.515 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-b4d583d3-063d-489f-9b06-bb5a515fc13a 
[INFO ] 2024-07-18 12:33:19.515 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-b4d583d3-063d-489f-9b06-bb5a515fc13a 
[INFO ] 2024-07-18 12:33:19.515 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b4d583d3-063d-489f-9b06-bb5a515fc13a] schema data cleaned 
[INFO ] 2024-07-18 12:33:19.516 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b4d583d3-063d-489f-9b06-bb5a515fc13a] monitor closed 
[INFO ] 2024-07-18 12:33:19.516 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b4d583d3-063d-489f-9b06-bb5a515fc13a] close complete, cost 28 ms 
[INFO ] 2024-07-18 12:33:23.636 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:33:23.636 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@e1db1ec 
[INFO ] 2024-07-18 12:33:23.743 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Stop task milestones: 66988a868315b25db9f53c30(Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537)  
[INFO ] 2024-07-18 12:33:23.765 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:33:23.765 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:33:23.804 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Remove memory task client succeed, task: Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537[66988a868315b25db9f53c30] 
[INFO ] 2024-07-18 12:33:23.804 - [Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537] - Destroy memory task client cache succeed, task: Heartbeat-qa_mysql_repl_33306_331_1717403468657_3537[66988a868315b25db9f53c30] 
