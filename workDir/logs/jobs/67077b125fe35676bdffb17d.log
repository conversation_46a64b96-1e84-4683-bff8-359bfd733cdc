[INFO ] 2024-10-14 03:06:15.501 - [测试主从合并节点没有主键] - Start task milestones: 67077b125fe35676bdffb17d(测试主从合并节点没有主键) 
[INFO ] 2024-10-14 03:06:15.515 - [测试主从合并节点没有主键] - Task initialization... 
[INFO ] 2024-10-14 03:06:15.799 - [测试主从合并节点没有主键] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-10-14 03:06:17.043 - [测试主从合并节点没有主键] - The engine receives 测试主从合并节点没有主键 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 03:06:17.748 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:06:17.751 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:06:17.751 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:06:17.752 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] preload schema finished, cost 1 ms 
[INFO ] 2024-10-14 03:06:17.796 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] start preload schema,table counts: 3 
[INFO ] 2024-10-14 03:06:17.802 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:06:17.803 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:06:17.803 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:06:17.804 - [测试主从合并节点没有主键][主从合并] - Node merge_table_processor(主从合并: 853aab53-0067-493a-a6dc-1c89f83a41aa) enable batch process 
[INFO ] 2024-10-14 03:06:17.809 - [测试主从合并节点没有主键][主从合并] - 
Merge lookup relation{
  mysql3306(accf3599-6c88-49d6-ab67-465a478413aa)
    ->mysql3307(e118c590-a725-463d-96e9-627ec6c11073)
} 
[INFO ] 2024-10-14 03:06:18.654 - [测试主从合并节点没有主键][主从合并] - Create merge cache imap name: 1166063210, external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 03:07:01.412 - [测试主从合并节点没有主键][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: - Table name: master
- Node name: master
- Merge operation: updateOrInsert 
[ERROR] 2024-10-14 03:07:01.507 - [测试主从合并节点没有主键][主从合并] - - Table name: master
- Node name: master
- Merge operation: updateOrInsert <-- Error Message -->
- Table name: master
- Node name: master
- Merge operation: updateOrInsert

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Table name: master
- Node name: master
- Merge operation: updateOrInsert
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:253)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:170)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	...

<-- Full Stack Trace -->
- Table name: master
- Node name: master
- Merge operation: updateOrInsert
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:170)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-10-14 03:07:01.518 - [测试主从合并节点没有主键][主从合并] - Job suspend in error handle 
[INFO ] 2024-10-14 03:07:03.964 - [测试主从合并节点没有主键][merge] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 03:07:07.373 - [测试主从合并节点没有主键][slave] - Source node "slave" read batch size: 100 
[INFO ] 2024-10-14 03:07:07.373 - [测试主从合并节点没有主键][slave] - Source node "slave" event queue capacity: 200 
[INFO ] 2024-10-14 03:07:07.377 - [测试主从合并节点没有主键][slave] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 03:07:07.397 - [测试主从合并节点没有主键][slave] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":287955678,"gtidSet":""} 
[INFO ] 2024-10-14 03:07:07.495 - [测试主从合并节点没有主键][slave] - Incremental sync starting... 
[INFO ] 2024-10-14 03:07:07.495 - [测试主从合并节点没有主键][slave] - Incremental sync completed 
[INFO ] 2024-10-14 03:07:11.944 - [测试主从合并节点没有主键][master] - Source node "master" read batch size: 100 
[INFO ] 2024-10-14 03:07:11.946 - [测试主从合并节点没有主键][master] - Source node "master" event queue capacity: 200 
[INFO ] 2024-10-14 03:07:11.947 - [测试主从合并节点没有主键][master] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 03:07:11.958 - [测试主从合并节点没有主键][master] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":8731,"gtidSet":""} 
[INFO ] 2024-10-14 03:07:12.055 - [测试主从合并节点没有主键][master] - Incremental sync starting... 
[INFO ] 2024-10-14 03:07:12.059 - [测试主从合并节点没有主键][master] - Incremental sync completed 
[INFO ] 2024-10-14 03:07:12.063 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] running status set to false 
[INFO ] 2024-10-14 03:07:12.093 - [测试主从合并节点没有主键][master] - PDK connector node stopped: HazelcastSourcePdkDataNode-accf3599-6c88-49d6-ab67-465a478413aa 
[INFO ] 2024-10-14 03:07:12.094 - [测试主从合并节点没有主键][master] - PDK connector node released: HazelcastSourcePdkDataNode-accf3599-6c88-49d6-ab67-465a478413aa 
[INFO ] 2024-10-14 03:07:12.097 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] schema data cleaned 
[INFO ] 2024-10-14 03:07:12.098 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] monitor closed 
[INFO ] 2024-10-14 03:07:12.113 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] close complete, cost 55 ms 
[INFO ] 2024-10-14 03:07:12.116 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] running status set to false 
[INFO ] 2024-10-14 03:07:12.125 - [测试主从合并节点没有主键][slave] - PDK connector node stopped: HazelcastSourcePdkDataNode-e118c590-a725-463d-96e9-627ec6c11073 
[INFO ] 2024-10-14 03:07:12.126 - [测试主从合并节点没有主键][slave] - PDK connector node released: HazelcastSourcePdkDataNode-e118c590-a725-463d-96e9-627ec6c11073 
[INFO ] 2024-10-14 03:07:12.126 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] schema data cleaned 
[INFO ] 2024-10-14 03:07:12.127 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] monitor closed 
[INFO ] 2024-10-14 03:07:12.128 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] close complete, cost 14 ms 
[INFO ] 2024-10-14 03:07:12.130 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] running status set to false 
[INFO ] 2024-10-14 03:07:12.131 - [测试主从合并节点没有主键][主从合并] - Destroy merge cache resource: 1166063210 
[INFO ] 2024-10-14 03:07:12.136 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] schema data cleaned 
[INFO ] 2024-10-14 03:07:12.139 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] monitor closed 
[INFO ] 2024-10-14 03:07:12.140 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] close complete, cost 11 ms 
[INFO ] 2024-10-14 03:07:12.140 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] running status set to false 
[INFO ] 2024-10-14 03:07:12.148 - [测试主从合并节点没有主键][merge] - PDK connector node stopped: HazelcastTargetPdkDataNode-441049ce-ae03-4edb-ad9d-0d4f1e285062 
[INFO ] 2024-10-14 03:07:12.148 - [测试主从合并节点没有主键][merge] - PDK connector node released: HazelcastTargetPdkDataNode-441049ce-ae03-4edb-ad9d-0d4f1e285062 
[INFO ] 2024-10-14 03:07:12.149 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] schema data cleaned 
[INFO ] 2024-10-14 03:07:12.149 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] monitor closed 
[INFO ] 2024-10-14 03:07:12.354 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] close complete, cost 9 ms 
[INFO ] 2024-10-14 03:07:16.578 - [测试主从合并节点没有主键] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 03:07:16.726 - [测试主从合并节点没有主键] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@70dc120f 
[INFO ] 2024-10-14 03:07:16.728 - [测试主从合并节点没有主键] - Stop task milestones: 67077b125fe35676bdffb17d(测试主从合并节点没有主键)  
[INFO ] 2024-10-14 03:07:16.757 - [测试主从合并节点没有主键] - Stopped task aspect(s) 
[INFO ] 2024-10-14 03:07:16.758 - [测试主从合并节点没有主键] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 03:07:16.815 - [测试主从合并节点没有主键] - Remove memory task client succeed, task: 测试主从合并节点没有主键[67077b125fe35676bdffb17d] 
[INFO ] 2024-10-14 03:07:16.817 - [测试主从合并节点没有主键] - Destroy memory task client cache succeed, task: 测试主从合并节点没有主键[67077b125fe35676bdffb17d] 
[INFO ] 2024-10-14 03:08:43.965 - [测试主从合并节点没有主键] - Start task milestones: 67077b125fe35676bdffb17d(测试主从合并节点没有主键) 
[INFO ] 2024-10-14 03:08:44.122 - [测试主从合并节点没有主键] - Task initialization... 
[INFO ] 2024-10-14 03:08:44.123 - [测试主从合并节点没有主键] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-10-14 03:08:45.226 - [测试主从合并节点没有主键] - The engine receives 测试主从合并节点没有主键 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 03:08:45.287 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:08:45.287 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] start preload schema,table counts: 3 
[INFO ] 2024-10-14 03:08:45.296 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:08:45.296 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:08:45.296 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:08:45.297 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:08:45.300 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:08:45.306 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:08:45.313 - [测试主从合并节点没有主键][主从合并] - Node merge_table_processor(主从合并: 853aab53-0067-493a-a6dc-1c89f83a41aa) enable batch process 
[INFO ] 2024-10-14 03:08:45.515 - [测试主从合并节点没有主键][主从合并] - 
Merge lookup relation{
  mysql3306(accf3599-6c88-49d6-ab67-465a478413aa)
    ->mysql3307(e118c590-a725-463d-96e9-627ec6c11073)
} 
[INFO ] 2024-10-14 03:08:46.445 - [测试主从合并节点没有主键][主从合并] - Create merge cache imap name: 1166063210, external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 03:08:48.628 - [测试主从合并节点没有主键][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: - Table name: master
- Node name: master
- Merge operation: updateOrInsert 
[ERROR] 2024-10-14 03:08:48.665 - [测试主从合并节点没有主键][主从合并] - - Table name: master
- Node name: master
- Merge operation: updateOrInsert <-- Error Message -->
- Table name: master
- Node name: master
- Merge operation: updateOrInsert

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Table name: master
- Node name: master
- Merge operation: updateOrInsert
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:253)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:170)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	...

<-- Full Stack Trace -->
- Table name: master
- Node name: master
- Merge operation: updateOrInsert
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:170)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-10-14 03:08:48.675 - [测试主从合并节点没有主键][主从合并] - Job suspend in error handle 
[INFO ] 2024-10-14 03:08:50.506 - [测试主从合并节点没有主键][merge] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 03:08:54.001 - [测试主从合并节点没有主键][slave] - Source node "slave" read batch size: 100 
[INFO ] 2024-10-14 03:08:54.004 - [测试主从合并节点没有主键][slave] - Source node "slave" event queue capacity: 200 
[INFO ] 2024-10-14 03:08:54.006 - [测试主从合并节点没有主键][slave] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 03:08:54.032 - [测试主从合并节点没有主键][slave] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":287955678,"gtidSet":""} 
[INFO ] 2024-10-14 03:08:54.097 - [测试主从合并节点没有主键][slave] - Incremental sync starting... 
[INFO ] 2024-10-14 03:08:54.097 - [测试主从合并节点没有主键][slave] - Incremental sync completed 
[INFO ] 2024-10-14 03:08:58.918 - [测试主从合并节点没有主键][master] - Source node "master" read batch size: 100 
[INFO ] 2024-10-14 03:08:58.918 - [测试主从合并节点没有主键][master] - Source node "master" event queue capacity: 200 
[INFO ] 2024-10-14 03:08:58.925 - [测试主从合并节点没有主键][master] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 03:08:58.925 - [测试主从合并节点没有主键][master] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":8731,"gtidSet":""} 
[INFO ] 2024-10-14 03:08:58.995 - [测试主从合并节点没有主键][master] - Incremental sync starting... 
[INFO ] 2024-10-14 03:08:58.995 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] running status set to false 
[INFO ] 2024-10-14 03:08:59.023 - [测试主从合并节点没有主键][master] - Incremental sync completed 
[INFO ] 2024-10-14 03:08:59.024 - [测试主从合并节点没有主键][slave] - PDK connector node stopped: HazelcastSourcePdkDataNode-e118c590-a725-463d-96e9-627ec6c11073 
[INFO ] 2024-10-14 03:08:59.024 - [测试主从合并节点没有主键][slave] - PDK connector node released: HazelcastSourcePdkDataNode-e118c590-a725-463d-96e9-627ec6c11073 
[INFO ] 2024-10-14 03:08:59.025 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] schema data cleaned 
[INFO ] 2024-10-14 03:08:59.028 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] monitor closed 
[INFO ] 2024-10-14 03:08:59.033 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] close complete, cost 34 ms 
[INFO ] 2024-10-14 03:08:59.037 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] running status set to false 
[INFO ] 2024-10-14 03:08:59.041 - [测试主从合并节点没有主键][master] - PDK connector node stopped: HazelcastSourcePdkDataNode-accf3599-6c88-49d6-ab67-465a478413aa 
[INFO ] 2024-10-14 03:08:59.042 - [测试主从合并节点没有主键][master] - PDK connector node released: HazelcastSourcePdkDataNode-accf3599-6c88-49d6-ab67-465a478413aa 
[INFO ] 2024-10-14 03:08:59.042 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] schema data cleaned 
[INFO ] 2024-10-14 03:08:59.043 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] monitor closed 
[INFO ] 2024-10-14 03:08:59.043 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] close complete, cost 14 ms 
[INFO ] 2024-10-14 03:08:59.044 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] running status set to false 
[INFO ] 2024-10-14 03:08:59.044 - [测试主从合并节点没有主键][主从合并] - Destroy merge cache resource: 1166063210 
[INFO ] 2024-10-14 03:08:59.045 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] schema data cleaned 
[INFO ] 2024-10-14 03:08:59.046 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] monitor closed 
[INFO ] 2024-10-14 03:08:59.047 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] close complete, cost 2 ms 
[INFO ] 2024-10-14 03:08:59.047 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] running status set to false 
[INFO ] 2024-10-14 03:08:59.058 - [测试主从合并节点没有主键][merge] - PDK connector node stopped: HazelcastTargetPdkDataNode-441049ce-ae03-4edb-ad9d-0d4f1e285062 
[INFO ] 2024-10-14 03:08:59.058 - [测试主从合并节点没有主键][merge] - PDK connector node released: HazelcastTargetPdkDataNode-441049ce-ae03-4edb-ad9d-0d4f1e285062 
[INFO ] 2024-10-14 03:08:59.059 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] schema data cleaned 
[INFO ] 2024-10-14 03:08:59.059 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] monitor closed 
[INFO ] 2024-10-14 03:08:59.269 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] close complete, cost 11 ms 
[INFO ] 2024-10-14 03:09:03.639 - [测试主从合并节点没有主键] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 03:09:03.757 - [测试主从合并节点没有主键] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@406383c9 
[INFO ] 2024-10-14 03:09:03.757 - [测试主从合并节点没有主键] - Stop task milestones: 67077b125fe35676bdffb17d(测试主从合并节点没有主键)  
[INFO ] 2024-10-14 03:09:03.776 - [测试主从合并节点没有主键] - Stopped task aspect(s) 
[INFO ] 2024-10-14 03:09:03.776 - [测试主从合并节点没有主键] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 03:09:03.799 - [测试主从合并节点没有主键] - Remove memory task client succeed, task: 测试主从合并节点没有主键[67077b125fe35676bdffb17d] 
[INFO ] 2024-10-14 03:09:03.803 - [测试主从合并节点没有主键] - Destroy memory task client cache succeed, task: 测试主从合并节点没有主键[67077b125fe35676bdffb17d] 
[INFO ] 2024-10-14 03:09:40.341 - [测试主从合并节点没有主键] - Start task milestones: 67077b125fe35676bdffb17d(测试主从合并节点没有主键) 
[INFO ] 2024-10-14 03:09:40.524 - [测试主从合并节点没有主键] - Task initialization... 
[INFO ] 2024-10-14 03:09:40.525 - [测试主从合并节点没有主键] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-10-14 03:09:41.634 - [测试主从合并节点没有主键] - The engine receives 测试主从合并节点没有主键 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-14 03:09:41.706 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:09:41.707 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] start preload schema,table counts: 3 
[INFO ] 2024-10-14 03:09:41.718 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:09:41.719 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] start preload schema,table counts: 1 
[INFO ] 2024-10-14 03:09:41.719 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:09:41.719 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:09:41.719 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:09:41.719 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] preload schema finished, cost 0 ms 
[INFO ] 2024-10-14 03:09:41.719 - [测试主从合并节点没有主键][主从合并] - Node merge_table_processor(主从合并: 853aab53-0067-493a-a6dc-1c89f83a41aa) enable batch process 
[INFO ] 2024-10-14 03:09:41.719 - [测试主从合并节点没有主键][主从合并] - 
Merge lookup relation{
  mysql3306(accf3599-6c88-49d6-ab67-465a478413aa)
    ->mysql3307(e118c590-a725-463d-96e9-627ec6c11073)
} 
[INFO ] 2024-10-14 03:09:52.040 - [测试主从合并节点没有主键][主从合并] - Create merge cache imap name: 1166063210, external storage: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-14 03:09:52.173 - [测试主从合并节点没有主键][主从合并] - Exception skipping - The current exception does not match the skip exception strategy, message: - Table name: master
- Node name: master
- Merge operation: updateOrInsert 
[ERROR] 2024-10-14 03:09:52.173 - [测试主从合并节点没有主键][主从合并] - - Table name: master
- Node name: master
- Merge operation: updateOrInsert <-- Error Message -->
- Table name: master
- Node name: master
- Merge operation: updateOrInsert

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Table name: master
- Node name: master
- Merge operation: updateOrInsert
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:253)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:170)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	...

<-- Full Stack Trace -->
- Table name: master
- Node name: master
- Merge operation: updateOrInsert
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initSourcePkOrUniqueFieldMap(HazelcastMergeNode.java:919)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.initRuntimeParameters(HazelcastMergeNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doInit(HazelcastMergeNode.java:170)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-10-14 03:09:52.384 - [测试主从合并节点没有主键][主从合并] - Job suspend in error handle 
[INFO ] 2024-10-14 03:09:54.383 - [测试主从合并节点没有主键][merge] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-14 03:09:57.241 - [测试主从合并节点没有主键][master] - Source node "master" read batch size: 100 
[INFO ] 2024-10-14 03:09:57.244 - [测试主从合并节点没有主键][master] - Source node "master" event queue capacity: 200 
[INFO ] 2024-10-14 03:09:57.246 - [测试主从合并节点没有主键][master] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 03:09:57.264 - [测试主从合并节点没有主键][master] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":8731,"gtidSet":""} 
[INFO ] 2024-10-14 03:09:57.326 - [测试主从合并节点没有主键][master] - Incremental sync starting... 
[INFO ] 2024-10-14 03:09:57.326 - [测试主从合并节点没有主键][master] - Incremental sync completed 
[INFO ] 2024-10-14 03:10:01.796 - [测试主从合并节点没有主键][slave] - Source node "slave" read batch size: 100 
[INFO ] 2024-10-14 03:10:01.797 - [测试主从合并节点没有主键][slave] - Source node "slave" event queue capacity: 200 
[INFO ] 2024-10-14 03:10:01.797 - [测试主从合并节点没有主键][slave] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-14 03:10:01.871 - [测试主从合并节点没有主键][slave] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":287955678,"gtidSet":""} 
[INFO ] 2024-10-14 03:10:01.871 - [测试主从合并节点没有主键][slave] - Incremental sync starting... 
[INFO ] 2024-10-14 03:10:01.871 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] running status set to false 
[INFO ] 2024-10-14 03:10:01.871 - [测试主从合并节点没有主键][slave] - Incremental sync completed 
[INFO ] 2024-10-14 03:10:01.895 - [测试主从合并节点没有主键][master] - PDK connector node stopped: HazelcastSourcePdkDataNode-accf3599-6c88-49d6-ab67-465a478413aa 
[INFO ] 2024-10-14 03:10:01.895 - [测试主从合并节点没有主键][master] - PDK connector node released: HazelcastSourcePdkDataNode-accf3599-6c88-49d6-ab67-465a478413aa 
[INFO ] 2024-10-14 03:10:01.895 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] schema data cleaned 
[INFO ] 2024-10-14 03:10:01.902 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] monitor closed 
[INFO ] 2024-10-14 03:10:01.904 - [测试主从合并节点没有主键][master] - Node master[accf3599-6c88-49d6-ab67-465a478413aa] close complete, cost 28 ms 
[INFO ] 2024-10-14 03:10:01.906 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] running status set to false 
[INFO ] 2024-10-14 03:10:01.922 - [测试主从合并节点没有主键][slave] - PDK connector node stopped: HazelcastSourcePdkDataNode-e118c590-a725-463d-96e9-627ec6c11073 
[INFO ] 2024-10-14 03:10:01.924 - [测试主从合并节点没有主键][slave] - PDK connector node released: HazelcastSourcePdkDataNode-e118c590-a725-463d-96e9-627ec6c11073 
[INFO ] 2024-10-14 03:10:01.924 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] schema data cleaned 
[INFO ] 2024-10-14 03:10:01.925 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] monitor closed 
[INFO ] 2024-10-14 03:10:01.925 - [测试主从合并节点没有主键][slave] - Node slave[e118c590-a725-463d-96e9-627ec6c11073] close complete, cost 23 ms 
[INFO ] 2024-10-14 03:10:01.926 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] running status set to false 
[INFO ] 2024-10-14 03:10:01.926 - [测试主从合并节点没有主键][主从合并] - Destroy merge cache resource: 1166063210 
[INFO ] 2024-10-14 03:10:01.927 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] schema data cleaned 
[INFO ] 2024-10-14 03:10:01.928 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] monitor closed 
[INFO ] 2024-10-14 03:10:01.928 - [测试主从合并节点没有主键][主从合并] - Node 主从合并[853aab53-0067-493a-a6dc-1c89f83a41aa] close complete, cost 2 ms 
[INFO ] 2024-10-14 03:10:01.935 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] running status set to false 
[INFO ] 2024-10-14 03:10:01.935 - [测试主从合并节点没有主键][merge] - PDK connector node stopped: HazelcastTargetPdkDataNode-441049ce-ae03-4edb-ad9d-0d4f1e285062 
[INFO ] 2024-10-14 03:10:01.935 - [测试主从合并节点没有主键][merge] - PDK connector node released: HazelcastTargetPdkDataNode-441049ce-ae03-4edb-ad9d-0d4f1e285062 
[INFO ] 2024-10-14 03:10:01.936 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] schema data cleaned 
[INFO ] 2024-10-14 03:10:01.937 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] monitor closed 
[INFO ] 2024-10-14 03:10:01.937 - [测试主从合并节点没有主键][merge] - Node merge[441049ce-ae03-4edb-ad9d-0d4f1e285062] close complete, cost 8 ms 
[INFO ] 2024-10-14 03:10:02.120 - [测试主从合并节点没有主键] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-14 03:10:02.122 - [测试主从合并节点没有主键] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@646a32e8 
[INFO ] 2024-10-14 03:10:02.240 - [测试主从合并节点没有主键] - Stop task milestones: 67077b125fe35676bdffb17d(测试主从合并节点没有主键)  
[INFO ] 2024-10-14 03:10:02.249 - [测试主从合并节点没有主键] - Stopped task aspect(s) 
[INFO ] 2024-10-14 03:10:02.249 - [测试主从合并节点没有主键] - Snapshot order controller have been removed 
[INFO ] 2024-10-14 03:10:02.267 - [测试主从合并节点没有主键] - Remove memory task client succeed, task: 测试主从合并节点没有主键[67077b125fe35676bdffb17d] 
[INFO ] 2024-10-14 03:10:02.468 - [测试主从合并节点没有主键] - Destroy memory task client cache succeed, task: 测试主从合并节点没有主键[67077b125fe35676bdffb17d] 
