[INFO ] 2024-07-26 19:11:54.597 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Start task milestones: 66a3847917f31912ab592950(Heartbeat-TestMongoHeartBeat - Copy - Copy) 
[INFO ] 2024-07-26 19:11:58.768 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:11:58.866 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - The engine receives Heartbeat-TestMongoHeartBeat - Copy - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:11:59.436 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4e51500d-a909-45d8-9085-2abd0bf3c930] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:11:59.451 - [Heartbeat-TestMongoHeartBeat - Copy - Co<PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2b36eb1c-395a-4d44-b180-07bc4fe9af7e] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:11:59.452 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4e51500d-a909-45d8-9085-2abd0bf3c930] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:11:59.465 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2b36eb1c-395a-4d44-b180-07bc4fe9af7e] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 19:12:00.840 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-26 19:12:00.942 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721992320, "i": 52}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721992320, "i": 52}}, "signature": {"hash": {"$binary": {"base64": "UdD6LhdoHEvUYCz+kIUaJx3igkI=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-26 19:12:01.039 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 19:12:01.039 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 19:12:01.039 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:12:01.057 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721992321039,"lastTimes":1721992321039,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 19:12:01.143 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 19:12:01.144 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 19:12:01.178 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 19:12:01.178 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 19:12:01.180 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:12:01.181 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721992321039,"lastTimes":1721992321039,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 19:12:01.181 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 19:12:01.184 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 19:12:12.227 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2b36eb1c-395a-4d44-b180-07bc4fe9af7e] running status set to false 
[INFO ] 2024-07-26 19:12:12.229 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 19:12:12.244 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-2b36eb1c-395a-4d44-b180-07bc4fe9af7e 
[INFO ] 2024-07-26 19:12:12.244 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-2b36eb1c-395a-4d44-b180-07bc4fe9af7e 
[INFO ] 2024-07-26 19:12:12.244 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2b36eb1c-395a-4d44-b180-07bc4fe9af7e] schema data cleaned 
[INFO ] 2024-07-26 19:12:12.251 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2b36eb1c-395a-4d44-b180-07bc4fe9af7e] monitor closed 
[INFO ] 2024-07-26 19:12:12.252 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[2b36eb1c-395a-4d44-b180-07bc4fe9af7e] close complete, cost 33 ms 
[INFO ] 2024-07-26 19:12:12.253 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4e51500d-a909-45d8-9085-2abd0bf3c930] running status set to false 
[INFO ] 2024-07-26 19:12:12.285 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-4e51500d-a909-45d8-9085-2abd0bf3c930 
[INFO ] 2024-07-26 19:12:12.286 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-4e51500d-a909-45d8-9085-2abd0bf3c930 
[INFO ] 2024-07-26 19:12:12.286 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4e51500d-a909-45d8-9085-2abd0bf3c930] schema data cleaned 
[INFO ] 2024-07-26 19:12:12.286 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4e51500d-a909-45d8-9085-2abd0bf3c930] monitor closed 
[INFO ] 2024-07-26 19:12:12.289 - [Heartbeat-TestMongoHeartBeat - Copy - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4e51500d-a909-45d8-9085-2abd0bf3c930] close complete, cost 34 ms 
[INFO ] 2024-07-26 19:12:13.039 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:12:13.039 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6af97fc3 
[INFO ] 2024-07-26 19:12:13.197 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Stop task milestones: 66a3847917f31912ab592950(Heartbeat-TestMongoHeartBeat - Copy - Copy)  
[INFO ] 2024-07-26 19:12:13.200 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:12:13.212 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:12:13.267 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Remove memory task client succeed, task: Heartbeat-TestMongoHeartBeat - Copy - Copy[66a3847917f31912ab592950] 
[INFO ] 2024-07-26 19:12:13.471 - [Heartbeat-TestMongoHeartBeat - Copy - Copy] - Destroy memory task client cache succeed, task: Heartbeat-TestMongoHeartBeat - Copy - Copy[66a3847917f31912ab592950] 
