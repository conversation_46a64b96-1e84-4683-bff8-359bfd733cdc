[INFO ] 2024-07-26 19:40:18.098 - [任务 1] - Task initialization... 
[INFO ] 2024-07-26 19:40:18.100 - [任务 1] - Start task milestones: 66a38af3183a6022d03ba9b7(任务 1) 
[INFO ] 2024-07-26 19:40:22.144 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:40:22.145 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:40:22.508 - [任务 1][CUSTOMER] - Node CUSTOMER[007289f2-9903-41d9-8831-031b08a0d1dd] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:40:22.516 - [任务 1][CUSTOMER] - Node CUSTOMER[007289f2-9903-41d9-8831-031b08a0d1dd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:40:22.517 - [任务 1][Test1] - Node Test1[74ff9699-3ce5-456d-b27c-9d19a3899726] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:40:22.554 - [任务 1][Test1] - Node Test1[74ff9699-3ce5-456d-b27c-9d19a3899726] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:40:23.350 - [任务 1][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 19:40:23.351 - [任务 1][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 19:40:23.460 - [任务 1][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:40:23.460 - [任务 1][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721994023,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:40:23.538 - [任务 1][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 19:40:23.538 - [任务 1][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 19:40:23.573 - [任务 1][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 19:40:23.573 - [任务 1][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 19:40:23.684 - [任务 1][Test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 19:40:23.691 - [任务 1][Test1] - Table "test.Test1" exists, skip auto create table 
[INFO ] 2024-07-26 19:40:23.691 - [任务 1][Test1] - The table Test1 has already exist. 
[WARN ] 2024-07-26 19:40:23.893 - [任务 1][Test1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 1054, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-26 19:41:23.988 - [任务 1][Test1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 1054, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
