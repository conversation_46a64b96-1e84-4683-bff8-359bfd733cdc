[INFO ] 2024-07-15 14:31:52.594 - [任务 6(100)][01e3607c-a8b2-4e5f-894e-918508ab10a6] - Node 01e3607c-a8b2-4e5f-894e-918508ab10a6[01e3607c-a8b2-4e5f-894e-918508ab10a6] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:31:52.596 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:31:52.596 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:31:52.596 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:31:52.596 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:31:52.596 - [任务 6(100)][01e3607c-a8b2-4e5f-894e-918508ab10a6] - Node 01e3607c-a8b2-4e5f-894e-918508ab10a6[01e3607c-a8b2-4e5f-894e-918508ab10a6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:31:53.289 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:31:53.290 - [任务 6(100)][c0916e8e-3605-466c-8312-ec3cb9879734] - Node c0916e8e-3605-466c-8312-ec3cb9879734[c0916e8e-3605-466c-8312-ec3cb9879734] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:31:53.290 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:31:53.291 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:31:53.291 - [任务 6(100)][c0916e8e-3605-466c-8312-ec3cb9879734] - Node c0916e8e-3605-466c-8312-ec3cb9879734[c0916e8e-3605-466c-8312-ec3cb9879734] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:31:53.291 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:31:53.350 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:31:53.350 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:31:53.350 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:31:53.350 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.350 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:31:53.459 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 29 ms 
[INFO ] 2024-07-15 14:31:53.461 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:31:53.463 - [任务 6(100)][01e3607c-a8b2-4e5f-894e-918508ab10a6] - Node 01e3607c-a8b2-4e5f-894e-918508ab10a6[01e3607c-a8b2-4e5f-894e-918508ab10a6] running status set to false 
[INFO ] 2024-07-15 14:31:53.468 - [任务 6(100)][01e3607c-a8b2-4e5f-894e-918508ab10a6] - Node 01e3607c-a8b2-4e5f-894e-918508ab10a6[01e3607c-a8b2-4e5f-894e-918508ab10a6] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.469 - [任务 6(100)][01e3607c-a8b2-4e5f-894e-918508ab10a6] - Node 01e3607c-a8b2-4e5f-894e-918508ab10a6[01e3607c-a8b2-4e5f-894e-918508ab10a6] monitor closed 
[INFO ] 2024-07-15 14:31:53.469 - [任务 6(100)][01e3607c-a8b2-4e5f-894e-918508ab10a6] - Node 01e3607c-a8b2-4e5f-894e-918508ab10a6[01e3607c-a8b2-4e5f-894e-918508ab10a6] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:31:53.472 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-5bd7e700-e8aa-4828-b56b-7a5050ab9775 
[INFO ] 2024-07-15 14:31:53.472 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-5bd7e700-e8aa-4828-b56b-7a5050ab9775 
[INFO ] 2024-07-15 14:31:53.478 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.478 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.479 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:31:53.479 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 23 ms 
[INFO ] 2024-07-15 14:31:53.484 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:31:53.496 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:31:53.496 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:31:53.529 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:31:53.531 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:31:53.531 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:31:53.531 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.531 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:31:53.532 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:31:53.711 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:31:53.714 - [任务 6(100)][c0916e8e-3605-466c-8312-ec3cb9879734] - Node c0916e8e-3605-466c-8312-ec3cb9879734[c0916e8e-3605-466c-8312-ec3cb9879734] running status set to false 
[INFO ] 2024-07-15 14:31:53.715 - [任务 6(100)][c0916e8e-3605-466c-8312-ec3cb9879734] - Node c0916e8e-3605-466c-8312-ec3cb9879734[c0916e8e-3605-466c-8312-ec3cb9879734] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.716 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-a44d221c-1bbe-456b-afc2-46364648d71a 
[INFO ] 2024-07-15 14:31:53.716 - [任务 6(100)][c0916e8e-3605-466c-8312-ec3cb9879734] - Node c0916e8e-3605-466c-8312-ec3cb9879734[c0916e8e-3605-466c-8312-ec3cb9879734] monitor closed 
[INFO ] 2024-07-15 14:31:53.716 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-a44d221c-1bbe-456b-afc2-46364648d71a 
[INFO ] 2024-07-15 14:31:53.716 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.717 - [任务 6(100)][c0916e8e-3605-466c-8312-ec3cb9879734] - Node c0916e8e-3605-466c-8312-ec3cb9879734[c0916e8e-3605-466c-8312-ec3cb9879734] close complete, cost 20 ms 
[INFO ] 2024-07-15 14:31:53.717 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:31:53.718 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:31:53.718 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 26 ms 
[INFO ] 2024-07-15 14:31:53.721 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:31:53.721 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:31:53.721 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:05.253 - [任务 6(100)][8360a82b-bc4e-4941-b10b-7fca48104739] - Node 8360a82b-bc4e-4941-b10b-7fca48104739[8360a82b-bc4e-4941-b10b-7fca48104739] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:05.253 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:05.253 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:05.255 - [任务 6(100)][8360a82b-bc4e-4941-b10b-7fca48104739] - Node 8360a82b-bc4e-4941-b10b-7fca48104739[8360a82b-bc4e-4941-b10b-7fca48104739] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:05.265 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:32:05.271 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 14:32:05.510 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:05.510 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:05.510 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:05.510 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:05.511 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:05.568 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 16 ms 
[INFO ] 2024-07-15 14:32:05.575 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:05.575 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:05.584 - [任务 6(100)][8360a82b-bc4e-4941-b10b-7fca48104739] - Node 8360a82b-bc4e-4941-b10b-7fca48104739[8360a82b-bc4e-4941-b10b-7fca48104739] running status set to false 
[INFO ] 2024-07-15 14:32:05.584 - [任务 6(100)][8360a82b-bc4e-4941-b10b-7fca48104739] - Node 8360a82b-bc4e-4941-b10b-7fca48104739[8360a82b-bc4e-4941-b10b-7fca48104739] schema data cleaned 
[INFO ] 2024-07-15 14:32:05.585 - [任务 6(100)][8360a82b-bc4e-4941-b10b-7fca48104739] - Node 8360a82b-bc4e-4941-b10b-7fca48104739[8360a82b-bc4e-4941-b10b-7fca48104739] monitor closed 
[INFO ] 2024-07-15 14:32:05.585 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-4c20125f-8872-4e2f-9ef2-3dc67746a7fd 
[INFO ] 2024-07-15 14:32:05.585 - [任务 6(100)][8360a82b-bc4e-4941-b10b-7fca48104739] - Node 8360a82b-bc4e-4941-b10b-7fca48104739[8360a82b-bc4e-4941-b10b-7fca48104739] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:32:05.585 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-4c20125f-8872-4e2f-9ef2-3dc67746a7fd 
[INFO ] 2024-07-15 14:32:05.587 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:05.587 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:05.588 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:05.588 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 15 ms 
[INFO ] 2024-07-15 14:32:05.590 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:05.590 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:05.591 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:05.812 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:05.813 - [任务 6(100)][56e41082-6d61-4afa-be92-66944f63622b] - Node 56e41082-6d61-4afa-be92-66944f63622b[56e41082-6d61-4afa-be92-66944f63622b] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:05.813 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:05.813 - [任务 6(100)][56e41082-6d61-4afa-be92-66944f63622b] - Node 56e41082-6d61-4afa-be92-66944f63622b[56e41082-6d61-4afa-be92-66944f63622b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:05.813 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:05.813 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:06.020 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:06.024 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:06.024 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:06.024 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:06.024 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:06.069 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:32:06.069 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:06.076 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:06.076 - [任务 6(100)][56e41082-6d61-4afa-be92-66944f63622b] - Node 56e41082-6d61-4afa-be92-66944f63622b[56e41082-6d61-4afa-be92-66944f63622b] running status set to false 
[INFO ] 2024-07-15 14:32:06.077 - [任务 6(100)][56e41082-6d61-4afa-be92-66944f63622b] - Node 56e41082-6d61-4afa-be92-66944f63622b[56e41082-6d61-4afa-be92-66944f63622b] schema data cleaned 
[INFO ] 2024-07-15 14:32:06.077 - [任务 6(100)][56e41082-6d61-4afa-be92-66944f63622b] - Node 56e41082-6d61-4afa-be92-66944f63622b[56e41082-6d61-4afa-be92-66944f63622b] monitor closed 
[INFO ] 2024-07-15 14:32:06.079 - [任务 6(100)][56e41082-6d61-4afa-be92-66944f63622b] - Node 56e41082-6d61-4afa-be92-66944f63622b[56e41082-6d61-4afa-be92-66944f63622b] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:32:06.079 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-e679323d-b874-471b-9f8b-9e2bd420f664 
[INFO ] 2024-07-15 14:32:06.080 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-e679323d-b874-471b-9f8b-9e2bd420f664 
[INFO ] 2024-07-15 14:32:06.080 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:06.081 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:06.082 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:06.083 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 11 ms 
[INFO ] 2024-07-15 14:32:06.084 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:06.084 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:06.084 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:10.076 - [任务 6(100)][0ab2ea24-61cf-482b-a190-d528cf7c1610] - Node 0ab2ea24-61cf-482b-a190-d528cf7c1610[0ab2ea24-61cf-482b-a190-d528cf7c1610] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:10.076 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:10.076 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:10.076 - [任务 6(100)][0ab2ea24-61cf-482b-a190-d528cf7c1610] - Node 0ab2ea24-61cf-482b-a190-d528cf7c1610[0ab2ea24-61cf-482b-a190-d528cf7c1610] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:10.076 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:10.076 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:10.474 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:10.490 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:10.495 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:10.499 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:10.499 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:10.501 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 30 ms 
[INFO ] 2024-07-15 14:32:10.501 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:10.507 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:10.509 - [任务 6(100)][0ab2ea24-61cf-482b-a190-d528cf7c1610] - Node 0ab2ea24-61cf-482b-a190-d528cf7c1610[0ab2ea24-61cf-482b-a190-d528cf7c1610] running status set to false 
[INFO ] 2024-07-15 14:32:10.509 - [任务 6(100)][0ab2ea24-61cf-482b-a190-d528cf7c1610] - Node 0ab2ea24-61cf-482b-a190-d528cf7c1610[0ab2ea24-61cf-482b-a190-d528cf7c1610] schema data cleaned 
[INFO ] 2024-07-15 14:32:10.510 - [任务 6(100)][0ab2ea24-61cf-482b-a190-d528cf7c1610] - Node 0ab2ea24-61cf-482b-a190-d528cf7c1610[0ab2ea24-61cf-482b-a190-d528cf7c1610] monitor closed 
[INFO ] 2024-07-15 14:32:10.511 - [任务 6(100)][0ab2ea24-61cf-482b-a190-d528cf7c1610] - Node 0ab2ea24-61cf-482b-a190-d528cf7c1610[0ab2ea24-61cf-482b-a190-d528cf7c1610] close complete, cost 6 ms 
[INFO ] 2024-07-15 14:32:10.511 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-969b13ce-7e69-46a6-8938-c849b69ff5d0 
[INFO ] 2024-07-15 14:32:10.512 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-969b13ce-7e69-46a6-8938-c849b69ff5d0 
[INFO ] 2024-07-15 14:32:10.512 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:10.514 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:10.515 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:10.516 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 11 ms 
[INFO ] 2024-07-15 14:32:10.517 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:10.517 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:10.672 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:10.683 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:10.684 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:10.684 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:10.684 - [任务 6(100)][7eab4b97-a6b1-4118-8984-b7d311776eec] - Node 7eab4b97-a6b1-4118-8984-b7d311776eec[7eab4b97-a6b1-4118-8984-b7d311776eec] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:10.684 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:10.685 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:10.685 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:10.686 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:10.686 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:10.686 - [任务 6(100)][7eab4b97-a6b1-4118-8984-b7d311776eec] - Node 7eab4b97-a6b1-4118-8984-b7d311776eec[7eab4b97-a6b1-4118-8984-b7d311776eec] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.006 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:11.032 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:11.034 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:11.037 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:11.037 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:11.042 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 48 ms 
[INFO ] 2024-07-15 14:32:11.047 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:11.124 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 14:32:11.127 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 14:32:11.211 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:11.211 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:11.211 - [任务 6(100)][18dbb193-807d-4c3e-9190-33d16bdf0761] - Node 18dbb193-807d-4c3e-9190-33d16bdf0761[18dbb193-807d-4c3e-9190-33d16bdf0761] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:11.211 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.211 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.212 - [任务 6(100)][18dbb193-807d-4c3e-9190-33d16bdf0761] - Node 18dbb193-807d-4c3e-9190-33d16bdf0761[18dbb193-807d-4c3e-9190-33d16bdf0761] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.513 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:11.521 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:11.522 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:11.522 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:11.522 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:11.522 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:11.525 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 37 ms 
[INFO ] 2024-07-15 14:32:11.527 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:11.528 - [任务 6(100)][18dbb193-807d-4c3e-9190-33d16bdf0761] - Node 18dbb193-807d-4c3e-9190-33d16bdf0761[18dbb193-807d-4c3e-9190-33d16bdf0761] running status set to false 
[INFO ] 2024-07-15 14:32:11.528 - [任务 6(100)][18dbb193-807d-4c3e-9190-33d16bdf0761] - Node 18dbb193-807d-4c3e-9190-33d16bdf0761[18dbb193-807d-4c3e-9190-33d16bdf0761] schema data cleaned 
[INFO ] 2024-07-15 14:32:11.528 - [任务 6(100)][18dbb193-807d-4c3e-9190-33d16bdf0761] - Node 18dbb193-807d-4c3e-9190-33d16bdf0761[18dbb193-807d-4c3e-9190-33d16bdf0761] monitor closed 
[INFO ] 2024-07-15 14:32:11.528 - [任务 6(100)][18dbb193-807d-4c3e-9190-33d16bdf0761] - Node 18dbb193-807d-4c3e-9190-33d16bdf0761[18dbb193-807d-4c3e-9190-33d16bdf0761] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.529 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-ab361c40-4b3e-48d2-8041-a010d4edfecb 
[INFO ] 2024-07-15 14:32:11.529 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-ab361c40-4b3e-48d2-8041-a010d4edfecb 
[INFO ] 2024-07-15 14:32:11.530 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:11.530 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:11.531 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:11.532 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:32:11.533 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:11.533 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:11.533 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:11.663 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:11.664 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:11.671 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:11.671 - [任务 6(100)][d615b864-13b2-419d-a96b-0d64d39070c3] - Node d615b864-13b2-419d-a96b-0d64d39070c3[d615b864-13b2-419d-a96b-0d64d39070c3] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:11.671 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.671 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:11.671 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.672 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.672 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.672 - [任务 6(100)][d615b864-13b2-419d-a96b-0d64d39070c3] - Node d615b864-13b2-419d-a96b-0d64d39070c3[d615b864-13b2-419d-a96b-0d64d39070c3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:11.863 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:11.867 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:11.867 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:11.867 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:11.867 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:11.868 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 21 ms 
[INFO ] 2024-07-15 14:32:12.039 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:12.065 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 14:32:12.067 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 14:32:13.670 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:13.671 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:32:13.671 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:32:13.673 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:32:13.691 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:32:13.693 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:32:13.693 - [任务 6(100)][7eab4b97-a6b1-4118-8984-b7d311776eec] - Node 7eab4b97-a6b1-4118-8984-b7d311776eec[7eab4b97-a6b1-4118-8984-b7d311776eec] running status set to false 
[INFO ] 2024-07-15 14:32:13.693 - [任务 6(100)][7eab4b97-a6b1-4118-8984-b7d311776eec] - Node 7eab4b97-a6b1-4118-8984-b7d311776eec[7eab4b97-a6b1-4118-8984-b7d311776eec] schema data cleaned 
[INFO ] 2024-07-15 14:32:13.693 - [任务 6(100)][7eab4b97-a6b1-4118-8984-b7d311776eec] - Node 7eab4b97-a6b1-4118-8984-b7d311776eec[7eab4b97-a6b1-4118-8984-b7d311776eec] monitor closed 
[INFO ] 2024-07-15 14:32:13.700 - [任务 6(100)][7eab4b97-a6b1-4118-8984-b7d311776eec] - Node 7eab4b97-a6b1-4118-8984-b7d311776eec[7eab4b97-a6b1-4118-8984-b7d311776eec] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:32:13.700 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-30ef2802-b225-439c-b544-910216953eea 
[INFO ] 2024-07-15 14:32:13.700 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-d6b69b1c-b568-4163-98ac-07844da073ae 
[INFO ] 2024-07-15 14:32:13.700 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-30ef2802-b225-439c-b544-910216953eea 
[INFO ] 2024-07-15 14:32:13.700 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-d6b69b1c-b568-4163-98ac-07844da073ae 
[INFO ] 2024-07-15 14:32:13.700 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:13.702 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:13.702 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:13.702 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:13.702 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 35 ms 
[INFO ] 2024-07-15 14:32:13.705 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:32:13.705 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:32:13.706 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 31 ms 
[INFO ] 2024-07-15 14:32:13.707 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:13.707 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:13.707 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:14.633 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:14.633 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:32:14.633 - [任务 6(100)][d615b864-13b2-419d-a96b-0d64d39070c3] - Node d615b864-13b2-419d-a96b-0d64d39070c3[d615b864-13b2-419d-a96b-0d64d39070c3] running status set to false 
[INFO ] 2024-07-15 14:32:14.634 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:32:14.634 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:32:14.634 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:32:14.634 - [任务 6(100)][d615b864-13b2-419d-a96b-0d64d39070c3] - Node d615b864-13b2-419d-a96b-0d64d39070c3[d615b864-13b2-419d-a96b-0d64d39070c3] schema data cleaned 
[INFO ] 2024-07-15 14:32:14.637 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 10 ms 
[INFO ] 2024-07-15 14:32:14.637 - [任务 6(100)][d615b864-13b2-419d-a96b-0d64d39070c3] - Node d615b864-13b2-419d-a96b-0d64d39070c3[d615b864-13b2-419d-a96b-0d64d39070c3] monitor closed 
[INFO ] 2024-07-15 14:32:14.637 - [任务 6(100)][d615b864-13b2-419d-a96b-0d64d39070c3] - Node d615b864-13b2-419d-a96b-0d64d39070c3[d615b864-13b2-419d-a96b-0d64d39070c3] close complete, cost 9 ms 
[INFO ] 2024-07-15 14:32:14.641 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-202b8f03-690b-4542-8726-1575114323e5 
[INFO ] 2024-07-15 14:32:14.641 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-f0eaa160-4fb1-4760-a49a-3aed36c58e7f 
[INFO ] 2024-07-15 14:32:14.642 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-202b8f03-690b-4542-8726-1575114323e5 
[INFO ] 2024-07-15 14:32:14.642 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-f0eaa160-4fb1-4760-a49a-3aed36c58e7f 
[INFO ] 2024-07-15 14:32:14.642 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:14.643 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:14.646 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:14.646 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:32:14.646 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:14.647 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:32:14.647 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 25 ms 
[INFO ] 2024-07-15 14:32:14.650 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 21 ms 
[INFO ] 2024-07-15 14:32:14.651 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:14.651 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:14.856 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:20.654 - [任务 6(100)][38c04ca0-0bf0-4e78-a67b-bcfd2098116c] - Node 38c04ca0-0bf0-4e78-a67b-bcfd2098116c[38c04ca0-0bf0-4e78-a67b-bcfd2098116c] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:20.654 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:20.654 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:20.654 - [任务 6(100)][38c04ca0-0bf0-4e78-a67b-bcfd2098116c] - Node 38c04ca0-0bf0-4e78-a67b-bcfd2098116c[38c04ca0-0bf0-4e78-a67b-bcfd2098116c] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:32:20.654 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:32:20.655 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 14:32:20.906 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:20.909 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:20.909 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:20.910 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:20.910 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:20.921 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 8 ms 
[INFO ] 2024-07-15 14:32:20.921 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:20.926 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:20.926 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-72ffce7a-e605-4482-8a75-6b0458126f15 
[INFO ] 2024-07-15 14:32:20.926 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-72ffce7a-e605-4482-8a75-6b0458126f15 
[INFO ] 2024-07-15 14:32:20.926 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:20.928 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:20.928 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:20.928 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:32:20.930 - [任务 6(100)][38c04ca0-0bf0-4e78-a67b-bcfd2098116c] - Node 38c04ca0-0bf0-4e78-a67b-bcfd2098116c[38c04ca0-0bf0-4e78-a67b-bcfd2098116c] running status set to false 
[INFO ] 2024-07-15 14:32:20.930 - [任务 6(100)][38c04ca0-0bf0-4e78-a67b-bcfd2098116c] - Node 38c04ca0-0bf0-4e78-a67b-bcfd2098116c[38c04ca0-0bf0-4e78-a67b-bcfd2098116c] schema data cleaned 
[INFO ] 2024-07-15 14:32:20.930 - [任务 6(100)][38c04ca0-0bf0-4e78-a67b-bcfd2098116c] - Node 38c04ca0-0bf0-4e78-a67b-bcfd2098116c[38c04ca0-0bf0-4e78-a67b-bcfd2098116c] monitor closed 
[INFO ] 2024-07-15 14:32:20.930 - [任务 6(100)][38c04ca0-0bf0-4e78-a67b-bcfd2098116c] - Node 38c04ca0-0bf0-4e78-a67b-bcfd2098116c[38c04ca0-0bf0-4e78-a67b-bcfd2098116c] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:32:20.932 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:20.932 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:20.932 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] - Node 2e7b22af-aba1-4cc1-a820-5b9d3523a1d8[2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:20.997 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:20.998 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.170 - [任务 6(100)][2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] - Node 2e7b22af-aba1-4cc1-a820-5b9d3523a1d8[2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.170 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:21.173 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:21.173 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:21.174 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:21.174 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:21.174 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 8 ms 
[INFO ] 2024-07-15 14:32:21.262 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:21.262 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:21.262 - [任务 6(100)][dcbdeefd-5507-4ed9-8611-c9e93b13f719] - Node dcbdeefd-5507-4ed9-8611-c9e93b13f719[dcbdeefd-5507-4ed9-8611-c9e93b13f719] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:21.262 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.262 - [任务 6(100)][dcbdeefd-5507-4ed9-8611-c9e93b13f719] - Node dcbdeefd-5507-4ed9-8611-c9e93b13f719[dcbdeefd-5507-4ed9-8611-c9e93b13f719] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.262 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.464 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:21.470 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:21.480 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[INFO ] 2024-07-15 14:32:21.480 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:21.481 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:21.481 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:21.483 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:21.484 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 14 ms 
[ERROR] 2024-07-15 14:32:21.544 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 14:32:21.546 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:21.548 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:21.549 - [任务 6(100)][dcbdeefd-5507-4ed9-8611-c9e93b13f719] - Node dcbdeefd-5507-4ed9-8611-c9e93b13f719[dcbdeefd-5507-4ed9-8611-c9e93b13f719] running status set to false 
[INFO ] 2024-07-15 14:32:21.549 - [任务 6(100)][dcbdeefd-5507-4ed9-8611-c9e93b13f719] - Node dcbdeefd-5507-4ed9-8611-c9e93b13f719[dcbdeefd-5507-4ed9-8611-c9e93b13f719] schema data cleaned 
[INFO ] 2024-07-15 14:32:21.549 - [任务 6(100)][dcbdeefd-5507-4ed9-8611-c9e93b13f719] - Node dcbdeefd-5507-4ed9-8611-c9e93b13f719[dcbdeefd-5507-4ed9-8611-c9e93b13f719] monitor closed 
[INFO ] 2024-07-15 14:32:21.552 - [任务 6(100)][dcbdeefd-5507-4ed9-8611-c9e93b13f719] - Node dcbdeefd-5507-4ed9-8611-c9e93b13f719[dcbdeefd-5507-4ed9-8611-c9e93b13f719] close complete, cost 6 ms 
[INFO ] 2024-07-15 14:32:21.552 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-2ca367e8-abb5-4530-b1d0-dd927c1be332 
[INFO ] 2024-07-15 14:32:21.552 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-2ca367e8-abb5-4530-b1d0-dd927c1be332 
[INFO ] 2024-07-15 14:32:21.552 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:21.554 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:21.554 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:21.555 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 16 ms 
[INFO ] 2024-07-15 14:32:21.555 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:21.555 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:21.629 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:21.629 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:21.629 - [任务 6(100)][8366c8b8-4687-4f17-b365-b1bc716f2731] - Node 8366c8b8-4687-4f17-b365-b1bc716f2731[8366c8b8-4687-4f17-b365-b1bc716f2731] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:21.629 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:21.629 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:21.629 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:21.630 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.630 - [任务 6(100)][8366c8b8-4687-4f17-b365-b1bc716f2731] - Node 8366c8b8-4687-4f17-b365-b1bc716f2731[8366c8b8-4687-4f17-b365-b1bc716f2731] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.630 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.630 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.760 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:21.761 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:21.770 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:21.770 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:21.771 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:21.771 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:21.771 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 12 ms 
[INFO ] 2024-07-15 14:32:21.867 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:21.890 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 14:32:21.890 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 14:32:24.048 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:24.048 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:32:24.050 - [任务 6(100)][2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] - Node 2e7b22af-aba1-4cc1-a820-5b9d3523a1d8[2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] running status set to false 
[INFO ] 2024-07-15 14:32:24.050 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:32:24.050 - [任务 6(100)][2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] - Node 2e7b22af-aba1-4cc1-a820-5b9d3523a1d8[2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.050 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.050 - [任务 6(100)][2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] - Node 2e7b22af-aba1-4cc1-a820-5b9d3523a1d8[2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] monitor closed 
[INFO ] 2024-07-15 14:32:24.050 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:32:24.051 - [任务 6(100)][2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] - Node 2e7b22af-aba1-4cc1-a820-5b9d3523a1d8[2e7b22af-aba1-4cc1-a820-5b9d3523a1d8] close complete, cost 10 ms 
[INFO ] 2024-07-15 14:32:24.052 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 16 ms 
[INFO ] 2024-07-15 14:32:24.052 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-6931b5da-16ff-4d4b-8fa3-3d4d93db585c 
[INFO ] 2024-07-15 14:32:24.052 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-7677f500-63cf-4172-9673-429f695f8c84 
[INFO ] 2024-07-15 14:32:24.052 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-6931b5da-16ff-4d4b-8fa3-3d4d93db585c 
[INFO ] 2024-07-15 14:32:24.052 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-7677f500-63cf-4172-9673-429f695f8c84 
[INFO ] 2024-07-15 14:32:24.053 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.053 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.060 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.060 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:24.060 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 28 ms 
[INFO ] 2024-07-15 14:32:24.061 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.061 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:32:24.062 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 23 ms 
[INFO ] 2024-07-15 14:32:24.063 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:24.063 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:24.264 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:24.425 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 7 ms 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][8366c8b8-4687-4f17-b365-b1bc716f2731] - Node 8366c8b8-4687-4f17-b365-b1bc716f2731[8366c8b8-4687-4f17-b365-b1bc716f2731] running status set to false 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][8366c8b8-4687-4f17-b365-b1bc716f2731] - Node 8366c8b8-4687-4f17-b365-b1bc716f2731[8366c8b8-4687-4f17-b365-b1bc716f2731] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][8366c8b8-4687-4f17-b365-b1bc716f2731] - Node 8366c8b8-4687-4f17-b365-b1bc716f2731[8366c8b8-4687-4f17-b365-b1bc716f2731] monitor closed 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][8366c8b8-4687-4f17-b365-b1bc716f2731] - Node 8366c8b8-4687-4f17-b365-b1bc716f2731[8366c8b8-4687-4f17-b365-b1bc716f2731] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-bdda4dbb-a820-4aa4-989b-5ae21df40a27 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-dea8c3ca-efd9-4b03-aba3-4937fc6dafb6 
[INFO ] 2024-07-15 14:32:24.428 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-bdda4dbb-a820-4aa4-989b-5ae21df40a27 
[INFO ] 2024-07-15 14:32:24.429 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.429 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-dea8c3ca-efd9-4b03-aba3-4937fc6dafb6 
[INFO ] 2024-07-15 14:32:24.429 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.433 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.433 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:24.433 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:32:24.433 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 14 ms 
[INFO ] 2024-07-15 14:32:24.433 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:32:24.433 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 13 ms 
[INFO ] 2024-07-15 14:32:24.435 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:24.436 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:24.436 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:27.243 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:27.243 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:27.244 - [任务 6(100)][2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] - Node 2734cbca-d0e5-4d31-b1a9-16b7cfe6527c[2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:27.244 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.244 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.244 - [任务 6(100)][2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] - Node 2734cbca-d0e5-4d31-b1a9-16b7cfe6527c[2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.734 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:27.737 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:27.737 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:27.737 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:27.737 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:27.738 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:32:27.759 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:27.760 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:27.762 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-d0b3befd-d7c9-46ed-bad6-ac46f3ddb320 
[INFO ] 2024-07-15 14:32:27.762 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-d0b3befd-d7c9-46ed-bad6-ac46f3ddb320 
[INFO ] 2024-07-15 14:32:27.762 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:27.762 - [任务 6(100)][2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] - Node 2734cbca-d0e5-4d31-b1a9-16b7cfe6527c[2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] running status set to false 
[INFO ] 2024-07-15 14:32:27.762 - [任务 6(100)][2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] - Node 2734cbca-d0e5-4d31-b1a9-16b7cfe6527c[2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] schema data cleaned 
[INFO ] 2024-07-15 14:32:27.763 - [任务 6(100)][2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] - Node 2734cbca-d0e5-4d31-b1a9-16b7cfe6527c[2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] monitor closed 
[INFO ] 2024-07-15 14:32:27.764 - [任务 6(100)][2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] - Node 2734cbca-d0e5-4d31-b1a9-16b7cfe6527c[2734cbca-d0e5-4d31-b1a9-16b7cfe6527c] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.764 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:27.764 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:27.765 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 6 ms 
[INFO ] 2024-07-15 14:32:27.766 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:27.766 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:27.767 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:27.820 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:27.820 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:27.820 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:27.821 - [任务 6(100)][6267e641-5f34-4f6f-b652-94787763bbf9] - Node 6267e641-5f34-4f6f-b652-94787763bbf9[6267e641-5f34-4f6f-b652-94787763bbf9] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:27.821 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.821 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.821 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.821 - [任务 6(100)][6267e641-5f34-4f6f-b652-94787763bbf9] - Node 6267e641-5f34-4f6f-b652-94787763bbf9[6267e641-5f34-4f6f-b652-94787763bbf9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.823 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:27.823 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:27.972 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:27.972 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:28.002 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:28.002 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:28.070 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:28.070 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 45 ms 
[INFO ] 2024-07-15 14:32:28.070 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:28.084 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 14:32:28.085 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 14:32:30.666 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:32:30.669 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:32:30.669 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:30.671 - [任务 6(100)][6267e641-5f34-4f6f-b652-94787763bbf9] - Node 6267e641-5f34-4f6f-b652-94787763bbf9[6267e641-5f34-4f6f-b652-94787763bbf9] running status set to false 
[INFO ] 2024-07-15 14:32:30.671 - [任务 6(100)][6267e641-5f34-4f6f-b652-94787763bbf9] - Node 6267e641-5f34-4f6f-b652-94787763bbf9[6267e641-5f34-4f6f-b652-94787763bbf9] schema data cleaned 
[INFO ] 2024-07-15 14:32:30.671 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:32:30.671 - [任务 6(100)][6267e641-5f34-4f6f-b652-94787763bbf9] - Node 6267e641-5f34-4f6f-b652-94787763bbf9[6267e641-5f34-4f6f-b652-94787763bbf9] monitor closed 
[INFO ] 2024-07-15 14:32:30.671 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:32:30.671 - [任务 6(100)][6267e641-5f34-4f6f-b652-94787763bbf9] - Node 6267e641-5f34-4f6f-b652-94787763bbf9[6267e641-5f34-4f6f-b652-94787763bbf9] close complete, cost 25 ms 
[INFO ] 2024-07-15 14:32:30.673 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 34 ms 
[INFO ] 2024-07-15 14:32:30.676 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-d28f65b1-5322-459e-af8d-5f450bcaad44 
[INFO ] 2024-07-15 14:32:30.676 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-bd5f2ef6-89c6-40db-b6b0-16779d23e61b 
[INFO ] 2024-07-15 14:32:30.676 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-d28f65b1-5322-459e-af8d-5f450bcaad44 
[INFO ] 2024-07-15 14:32:30.676 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:30.677 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-bd5f2ef6-89c6-40db-b6b0-16779d23e61b 
[INFO ] 2024-07-15 14:32:30.677 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:30.691 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:32:30.691 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:30.691 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:32:30.691 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:30.691 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 64 ms 
[INFO ] 2024-07-15 14:32:30.692 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 69 ms 
[INFO ] 2024-07-15 14:32:30.692 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:30.692 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:30.693 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:33.485 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:33.486 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:33.486 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:33.486 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:33.486 - [任务 6(100)][9d4f3922-18c3-4154-aac0-202a98006085] - Node 9d4f3922-18c3-4154-aac0-202a98006085[9d4f3922-18c3-4154-aac0-202a98006085] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:33.486 - [任务 6(100)][9d4f3922-18c3-4154-aac0-202a98006085] - Node 9d4f3922-18c3-4154-aac0-202a98006085[9d4f3922-18c3-4154-aac0-202a98006085] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:33.678 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:33.678 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:33.678 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:33.678 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:33.686 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:33.798 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 13 ms 
[INFO ] 2024-07-15 14:32:33.798 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:33.806 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:33.807 - [任务 6(100)][9d4f3922-18c3-4154-aac0-202a98006085] - Node 9d4f3922-18c3-4154-aac0-202a98006085[9d4f3922-18c3-4154-aac0-202a98006085] running status set to false 
[INFO ] 2024-07-15 14:32:33.808 - [任务 6(100)][9d4f3922-18c3-4154-aac0-202a98006085] - Node 9d4f3922-18c3-4154-aac0-202a98006085[9d4f3922-18c3-4154-aac0-202a98006085] schema data cleaned 
[INFO ] 2024-07-15 14:32:33.808 - [任务 6(100)][9d4f3922-18c3-4154-aac0-202a98006085] - Node 9d4f3922-18c3-4154-aac0-202a98006085[9d4f3922-18c3-4154-aac0-202a98006085] monitor closed 
[INFO ] 2024-07-15 14:32:33.808 - [任务 6(100)][9d4f3922-18c3-4154-aac0-202a98006085] - Node 9d4f3922-18c3-4154-aac0-202a98006085[9d4f3922-18c3-4154-aac0-202a98006085] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:32:33.809 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-a6c6a8c6-3b63-41cd-a63e-35cd45c7c270 
[INFO ] 2024-07-15 14:32:33.809 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-a6c6a8c6-3b63-41cd-a63e-35cd45c7c270 
[INFO ] 2024-07-15 14:32:33.809 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:33.811 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:33.811 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:33.812 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 9 ms 
[INFO ] 2024-07-15 14:32:33.813 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:33.813 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:33.965 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:32:33.965 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:33.965 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:33.966 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:33.966 - [任务 6(100)][27a41f8a-e38b-4d7e-8f34-637cca567ceb] - Node 27a41f8a-e38b-4d7e-8f34-637cca567ceb[27a41f8a-e38b-4d7e-8f34-637cca567ceb] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:32:33.966 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:32:33.966 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:33.966 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:33.966 - [任务 6(100)][27a41f8a-e38b-4d7e-8f34-637cca567ceb] - Node 27a41f8a-e38b-4d7e-8f34-637cca567ceb[27a41f8a-e38b-4d7e-8f34-637cca567ceb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:33.966 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:32:33.967 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:32:34.203 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:32:34.206 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:34.207 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:32:34.207 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:32:34.207 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:32:34.208 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 17 ms 
[INFO ] 2024-07-15 14:32:34.229 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:32:34.229 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 14:32:34.434 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 14:32:36.790 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:32:36.791 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:32:36.791 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:32:36.793 - [任务 6(100)][27a41f8a-e38b-4d7e-8f34-637cca567ceb] - Node 27a41f8a-e38b-4d7e-8f34-637cca567ceb[27a41f8a-e38b-4d7e-8f34-637cca567ceb] running status set to false 
[INFO ] 2024-07-15 14:32:36.793 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:32:36.793 - [任务 6(100)][27a41f8a-e38b-4d7e-8f34-637cca567ceb] - Node 27a41f8a-e38b-4d7e-8f34-637cca567ceb[27a41f8a-e38b-4d7e-8f34-637cca567ceb] schema data cleaned 
[INFO ] 2024-07-15 14:32:36.793 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:32:36.793 - [任务 6(100)][27a41f8a-e38b-4d7e-8f34-637cca567ceb] - Node 27a41f8a-e38b-4d7e-8f34-637cca567ceb[27a41f8a-e38b-4d7e-8f34-637cca567ceb] monitor closed 
[INFO ] 2024-07-15 14:32:36.793 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 11 ms 
[INFO ] 2024-07-15 14:32:36.793 - [任务 6(100)][27a41f8a-e38b-4d7e-8f34-637cca567ceb] - Node 27a41f8a-e38b-4d7e-8f34-637cca567ceb[27a41f8a-e38b-4d7e-8f34-637cca567ceb] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:32:36.802 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-92bd2f5b-df94-47d3-87a7-7ca096d7cffe 
[INFO ] 2024-07-15 14:32:36.802 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-41431a04-3eaf-49a2-a93a-7669d6179e01 
[INFO ] 2024-07-15 14:32:36.802 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-92bd2f5b-df94-47d3-87a7-7ca096d7cffe 
[INFO ] 2024-07-15 14:32:36.803 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-41431a04-3eaf-49a2-a93a-7669d6179e01 
[INFO ] 2024-07-15 14:32:36.803 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:36.803 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:32:36.804 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:32:36.804 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:32:36.804 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:32:36.804 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:32:36.805 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 25 ms 
[INFO ] 2024-07-15 14:32:36.805 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 19 ms 
[INFO ] 2024-07-15 14:32:36.807 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:32:36.807 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:32:36.807 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:33:52.031 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:33:52.033 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:33:52.034 - [任务 6(100)][615cc4cf-0d74-436c-a2c8-f8d1c605c276] - Node 615cc4cf-0d74-436c-a2c8-f8d1c605c276[615cc4cf-0d74-436c-a2c8-f8d1c605c276] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:33:52.034 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:33:52.034 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:33:52.034 - [任务 6(100)][615cc4cf-0d74-436c-a2c8-f8d1c605c276] - Node 615cc4cf-0d74-436c-a2c8-f8d1c605c276[615cc4cf-0d74-436c-a2c8-f8d1c605c276] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:33:52.456 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:33:52.459 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:33:52.459 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:33:52.459 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:33:52.463 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:33:52.463 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:33:52.478 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:33:52.485 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:33:52.485 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-90b4c492-74dc-4a63-b7e6-a9a5a63c066c 
[INFO ] 2024-07-15 14:33:52.486 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-90b4c492-74dc-4a63-b7e6-a9a5a63c066c 
[INFO ] 2024-07-15 14:33:52.488 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:33:52.488 - [任务 6(100)][615cc4cf-0d74-436c-a2c8-f8d1c605c276] - Node 615cc4cf-0d74-436c-a2c8-f8d1c605c276[615cc4cf-0d74-436c-a2c8-f8d1c605c276] running status set to false 
[INFO ] 2024-07-15 14:33:52.488 - [任务 6(100)][615cc4cf-0d74-436c-a2c8-f8d1c605c276] - Node 615cc4cf-0d74-436c-a2c8-f8d1c605c276[615cc4cf-0d74-436c-a2c8-f8d1c605c276] schema data cleaned 
[INFO ] 2024-07-15 14:33:52.489 - [任务 6(100)][615cc4cf-0d74-436c-a2c8-f8d1c605c276] - Node 615cc4cf-0d74-436c-a2c8-f8d1c605c276[615cc4cf-0d74-436c-a2c8-f8d1c605c276] monitor closed 
[INFO ] 2024-07-15 14:33:52.490 - [任务 6(100)][615cc4cf-0d74-436c-a2c8-f8d1c605c276] - Node 615cc4cf-0d74-436c-a2c8-f8d1c605c276[615cc4cf-0d74-436c-a2c8-f8d1c605c276] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:33:52.490 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:33:52.490 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:33:52.490 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 8 ms 
[INFO ] 2024-07-15 14:33:52.491 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:33:52.491 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:33:52.623 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:33:52.623 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:33:52.623 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:33:52.623 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:33:52.623 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:33:52.624 - [任务 6(100)][7c054a60-c22e-4987-8c97-3915759abb4d] - Node 7c054a60-c22e-4987-8c97-3915759abb4d[7c054a60-c22e-4987-8c97-3915759abb4d] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:33:52.624 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:33:52.624 - [任务 6(100)][7c054a60-c22e-4987-8c97-3915759abb4d] - Node 7c054a60-c22e-4987-8c97-3915759abb4d[7c054a60-c22e-4987-8c97-3915759abb4d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:33:52.624 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:33:52.624 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:33:52.625 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 14:33:52.855 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:33:52.856 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:33:52.856 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:33:52.856 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:33:52.856 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:33:52.857 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 18 ms 
[INFO ] 2024-07-15 14:33:52.897 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:33:52.898 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 14:33:53.101 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 14:33:55.471 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] running status set to false 
[INFO ] 2024-07-15 14:33:55.474 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:33:55.474 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:33:55.474 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:33:55.474 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 3 ms 
[INFO ] 2024-07-15 14:33:55.475 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:33:55.480 - [任务 6(100)][7c054a60-c22e-4987-8c97-3915759abb4d] - Node 7c054a60-c22e-4987-8c97-3915759abb4d[7c054a60-c22e-4987-8c97-3915759abb4d] running status set to false 
[INFO ] 2024-07-15 14:33:55.480 - [任务 6(100)][7c054a60-c22e-4987-8c97-3915759abb4d] - Node 7c054a60-c22e-4987-8c97-3915759abb4d[7c054a60-c22e-4987-8c97-3915759abb4d] schema data cleaned 
[INFO ] 2024-07-15 14:33:55.480 - [任务 6(100)][7c054a60-c22e-4987-8c97-3915759abb4d] - Node 7c054a60-c22e-4987-8c97-3915759abb4d[7c054a60-c22e-4987-8c97-3915759abb4d] monitor closed 
[INFO ] 2024-07-15 14:33:55.480 - [任务 6(100)][7c054a60-c22e-4987-8c97-3915759abb4d] - Node 7c054a60-c22e-4987-8c97-3915759abb4d[7c054a60-c22e-4987-8c97-3915759abb4d] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:33:55.482 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-eb464497-17c9-40db-b0d4-6b04031ba118 
[INFO ] 2024-07-15 14:33:55.483 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-7cb81293-96e1-4caf-b7ed-108b0022472a 
[INFO ] 2024-07-15 14:33:55.485 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-eb464497-17c9-40db-b0d4-6b04031ba118 
[INFO ] 2024-07-15 14:33:55.485 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-7cb81293-96e1-4caf-b7ed-108b0022472a 
[INFO ] 2024-07-15 14:33:55.486 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:33:55.486 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-e36912f4-a4f2-4c87-ab34-079238777cad-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:33:55.494 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:33:55.494 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] schema data cleaned 
[INFO ] 2024-07-15 14:33:55.494 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:33:55.494 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] monitor closed 
[INFO ] 2024-07-15 14:33:55.494 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 21 ms 
[INFO ] 2024-07-15 14:33:55.495 - [任务 6(100)][增强JS] - Node 增强JS[e36912f4-a4f2-4c87-ab34-079238777cad] close complete, cost 26 ms 
[INFO ] 2024-07-15 14:33:55.497 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:33:55.497 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:33:55.498 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:35:12.734 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:35:12.735 - [任务 6(100)][ec64a947-9d12-4870-95b0-5e85be50a750] - Node ec64a947-9d12-4870-95b0-5e85be50a750[ec64a947-9d12-4870-95b0-5e85be50a750] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:35:12.735 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:35:12.735 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:12.735 - [任务 6(100)][ec64a947-9d12-4870-95b0-5e85be50a750] - Node ec64a947-9d12-4870-95b0-5e85be50a750[ec64a947-9d12-4870-95b0-5e85be50a750] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:12.735 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:12.737 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:35:12.737 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:35:12.737 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:35:12.737 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 0 ms 
[WARN ] 2024-07-15 14:35:12.782 - [任务 6(100)][增强JS] - The source could not build the executor, please check 
[INFO ] 2024-07-15 14:35:12.782 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:35:12.784 - [任务 6(100)][ec64a947-9d12-4870-95b0-5e85be50a750] - Node ec64a947-9d12-4870-95b0-5e85be50a750[ec64a947-9d12-4870-95b0-5e85be50a750] running status set to false 
[INFO ] 2024-07-15 14:35:12.784 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:35:12.784 - [任务 6(100)][ec64a947-9d12-4870-95b0-5e85be50a750] - Node ec64a947-9d12-4870-95b0-5e85be50a750[ec64a947-9d12-4870-95b0-5e85be50a750] schema data cleaned 
[INFO ] 2024-07-15 14:35:12.784 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:35:12.784 - [任务 6(100)][ec64a947-9d12-4870-95b0-5e85be50a750] - Node ec64a947-9d12-4870-95b0-5e85be50a750[ec64a947-9d12-4870-95b0-5e85be50a750] monitor closed 
[INFO ] 2024-07-15 14:35:12.784 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 3 ms 
[INFO ] 2024-07-15 14:35:12.784 - [任务 6(100)][ec64a947-9d12-4870-95b0-5e85be50a750] - Node ec64a947-9d12-4870-95b0-5e85be50a750[ec64a947-9d12-4870-95b0-5e85be50a750] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:35:12.786 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:35:12.786 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:35:12.786 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:35:17.121 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:35:17.122 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:35:17.122 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:35:17.122 - [任务 6(100)][68891df7-1db1-44ae-bfab-ae6c490368dc] - Node 68891df7-1db1-44ae-bfab-ae6c490368dc[68891df7-1db1-44ae-bfab-ae6c490368dc] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:35:17.122 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:17.122 - [任务 6(100)][68891df7-1db1-44ae-bfab-ae6c490368dc] - Node 68891df7-1db1-44ae-bfab-ae6c490368dc[68891df7-1db1-44ae-bfab-ae6c490368dc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:17.122 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:17.122 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:17.337 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:35:17.338 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:35:17.339 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:35:17.339 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:35:17.339 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:35:17.341 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:35:17.341 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:35:17.341 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:35:17.341 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:35:17.341 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 6 ms 
[INFO ] 2024-07-15 14:35:17.359 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:35:17.360 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:35:17.361 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-dca201b7-fdda-4075-ae54-ff4f8148d611 
[INFO ] 2024-07-15 14:35:17.361 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-dca201b7-fdda-4075-ae54-ff4f8148d611 
[INFO ] 2024-07-15 14:35:17.361 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:35:17.362 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:35:17.362 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:35:17.362 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:35:17.365 - [任务 6(100)][68891df7-1db1-44ae-bfab-ae6c490368dc] - Node 68891df7-1db1-44ae-bfab-ae6c490368dc[68891df7-1db1-44ae-bfab-ae6c490368dc] running status set to false 
[INFO ] 2024-07-15 14:35:17.365 - [任务 6(100)][68891df7-1db1-44ae-bfab-ae6c490368dc] - Node 68891df7-1db1-44ae-bfab-ae6c490368dc[68891df7-1db1-44ae-bfab-ae6c490368dc] schema data cleaned 
[INFO ] 2024-07-15 14:35:17.365 - [任务 6(100)][68891df7-1db1-44ae-bfab-ae6c490368dc] - Node 68891df7-1db1-44ae-bfab-ae6c490368dc[68891df7-1db1-44ae-bfab-ae6c490368dc] monitor closed 
[INFO ] 2024-07-15 14:35:17.365 - [任务 6(100)][68891df7-1db1-44ae-bfab-ae6c490368dc] - Node 68891df7-1db1-44ae-bfab-ae6c490368dc[68891df7-1db1-44ae-bfab-ae6c490368dc] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:35:17.366 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:35:17.366 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:35:17.366 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][af32e711-fe22-49c0-8260-152d346d5395] - Node af32e711-fe22-49c0-8260-152d346d5395[af32e711-fe22-49c0-8260-152d346d5395] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][af32e711-fe22-49c0-8260-152d346d5395] - Node af32e711-fe22-49c0-8260-152d346d5395[af32e711-fe22-49c0-8260-152d346d5395] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:18.883 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:35:19.250 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:35:19.252 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:35:19.252 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:35:19.252 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:35:19.252 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 16 ms 
[INFO ] 2024-07-15 14:35:19.289 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:35:19.289 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:35:19.289 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:35:19.289 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:35:19.290 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 69 ms 
[INFO ] 2024-07-15 14:35:19.305 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:35:19.309 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:35:19.309 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-27a1a51e-76b4-434e-bce2-3306de1c55fd 
[INFO ] 2024-07-15 14:35:19.309 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-27a1a51e-76b4-434e-bce2-3306de1c55fd 
[INFO ] 2024-07-15 14:35:19.309 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:35:19.310 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:35:19.310 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:35:19.312 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:35:19.312 - [任务 6(100)][af32e711-fe22-49c0-8260-152d346d5395] - Node af32e711-fe22-49c0-8260-152d346d5395[af32e711-fe22-49c0-8260-152d346d5395] running status set to false 
[INFO ] 2024-07-15 14:35:19.312 - [任务 6(100)][af32e711-fe22-49c0-8260-152d346d5395] - Node af32e711-fe22-49c0-8260-152d346d5395[af32e711-fe22-49c0-8260-152d346d5395] schema data cleaned 
[INFO ] 2024-07-15 14:35:19.312 - [任务 6(100)][af32e711-fe22-49c0-8260-152d346d5395] - Node af32e711-fe22-49c0-8260-152d346d5395[af32e711-fe22-49c0-8260-152d346d5395] monitor closed 
[INFO ] 2024-07-15 14:35:19.315 - [任务 6(100)][af32e711-fe22-49c0-8260-152d346d5395] - Node af32e711-fe22-49c0-8260-152d346d5395[af32e711-fe22-49c0-8260-152d346d5395] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:35:19.315 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:35:19.315 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:35:19.316 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:36:00.336 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:36:00.336 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:36:00.337 - [任务 6(100)][f54b196f-555a-4750-9f3b-22970c83a274] - Node f54b196f-555a-4750-9f3b-22970c83a274[f54b196f-555a-4750-9f3b-22970c83a274] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:36:00.337 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:36:00.337 - [任务 6(100)][f54b196f-555a-4750-9f3b-22970c83a274] - Node f54b196f-555a-4750-9f3b-22970c83a274[f54b196f-555a-4750-9f3b-22970c83a274] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:36:00.337 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:36:00.337 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:36:00.338 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:36:00.763 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:36:00.766 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:36:00.766 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:36:00.767 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:36:00.767 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:36:00.767 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:36:00.767 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:36:00.767 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:36:00.767 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:36:00.767 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 6 ms 
[INFO ] 2024-07-15 14:36:00.782 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:36:00.782 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:36:00.784 - [任务 6(100)][f54b196f-555a-4750-9f3b-22970c83a274] - Node f54b196f-555a-4750-9f3b-22970c83a274[f54b196f-555a-4750-9f3b-22970c83a274] running status set to false 
[INFO ] 2024-07-15 14:36:00.784 - [任务 6(100)][f54b196f-555a-4750-9f3b-22970c83a274] - Node f54b196f-555a-4750-9f3b-22970c83a274[f54b196f-555a-4750-9f3b-22970c83a274] schema data cleaned 
[INFO ] 2024-07-15 14:36:00.784 - [任务 6(100)][f54b196f-555a-4750-9f3b-22970c83a274] - Node f54b196f-555a-4750-9f3b-22970c83a274[f54b196f-555a-4750-9f3b-22970c83a274] monitor closed 
[INFO ] 2024-07-15 14:36:00.784 - [任务 6(100)][f54b196f-555a-4750-9f3b-22970c83a274] - Node f54b196f-555a-4750-9f3b-22970c83a274[f54b196f-555a-4750-9f3b-22970c83a274] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:36:00.787 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-112717ea-7cae-4884-8e5e-98eb78952763 
[INFO ] 2024-07-15 14:36:00.787 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-112717ea-7cae-4884-8e5e-98eb78952763 
[INFO ] 2024-07-15 14:36:00.787 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:36:00.790 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:36:00.790 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:36:00.792 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 8 ms 
[INFO ] 2024-07-15 14:36:00.794 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:36:00.794 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:36:00.998 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:36:06.140 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:36:06.141 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:36:06.142 - [任务 6(100)][44f2ff01-0c03-4076-b5a2-0dd3cd35462f] - Node 44f2ff01-0c03-4076-b5a2-0dd3cd35462f[44f2ff01-0c03-4076-b5a2-0dd3cd35462f] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:36:06.142 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:36:06.142 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 14:36:06.142 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 3 ms 
[INFO ] 2024-07-15 14:36:06.142 - [任务 6(100)][44f2ff01-0c03-4076-b5a2-0dd3cd35462f] - Node 44f2ff01-0c03-4076-b5a2-0dd3cd35462f[44f2ff01-0c03-4076-b5a2-0dd3cd35462f] preload schema finished, cost 3 ms 
[INFO ] 2024-07-15 14:36:06.142 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 3 ms 
[INFO ] 2024-07-15 14:36:30.679 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:37:06.764 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:37:06.777 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:37:06.777 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:37:06.777 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:37:06.777 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:37:06.777 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:37:06.777 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:37:06.779 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 36 ms 
[INFO ] 2024-07-15 14:37:06.779 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 60364 ms 
[INFO ] 2024-07-15 14:37:06.805 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:37:06.814 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:37:06.815 - [任务 6(100)][44f2ff01-0c03-4076-b5a2-0dd3cd35462f] - Node 44f2ff01-0c03-4076-b5a2-0dd3cd35462f[44f2ff01-0c03-4076-b5a2-0dd3cd35462f] running status set to false 
[INFO ] 2024-07-15 14:37:06.819 - [任务 6(100)][44f2ff01-0c03-4076-b5a2-0dd3cd35462f] - Node 44f2ff01-0c03-4076-b5a2-0dd3cd35462f[44f2ff01-0c03-4076-b5a2-0dd3cd35462f] schema data cleaned 
[INFO ] 2024-07-15 14:37:06.819 - [任务 6(100)][44f2ff01-0c03-4076-b5a2-0dd3cd35462f] - Node 44f2ff01-0c03-4076-b5a2-0dd3cd35462f[44f2ff01-0c03-4076-b5a2-0dd3cd35462f] monitor closed 
[INFO ] 2024-07-15 14:37:06.819 - [任务 6(100)][44f2ff01-0c03-4076-b5a2-0dd3cd35462f] - Node 44f2ff01-0c03-4076-b5a2-0dd3cd35462f[44f2ff01-0c03-4076-b5a2-0dd3cd35462f] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:37:06.824 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-4fb670f0-a207-474b-a720-6b1e22296a95 
[INFO ] 2024-07-15 14:37:06.825 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-4fb670f0-a207-474b-a720-6b1e22296a95 
[INFO ] 2024-07-15 14:37:06.825 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:37:06.829 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:37:06.829 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:37:06.829 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 17 ms 
[INFO ] 2024-07-15 14:37:06.833 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:37:06.838 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:37:06.838 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:37:32.878 - [任务 6(100)][c34678cf-e7e3-44d6-ad68-667e13f077d4] - Node c34678cf-e7e3-44d6-ad68-667e13f077d4[c34678cf-e7e3-44d6-ad68-667e13f077d4] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:37:32.879 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:37:32.879 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:37:32.879 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:37:32.879 - [任务 6(100)][c34678cf-e7e3-44d6-ad68-667e13f077d4] - Node c34678cf-e7e3-44d6-ad68-667e13f077d4[c34678cf-e7e3-44d6-ad68-667e13f077d4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:37:32.879 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:37:32.879 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:37:32.879 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:37:33.394 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:38:00.808 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:38:00.825 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:38:00.825 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:38:00.828 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:38:00.829 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:38:00.829 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:38:00.831 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:38:00.832 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 32 ms 
[INFO ] 2024-07-15 14:38:00.834 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 27438 ms 
[INFO ] 2024-07-15 14:38:00.918 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:38:00.918 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:38:00.923 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-40e2635a-a16c-4236-b526-4afc291b0b85 
[INFO ] 2024-07-15 14:38:00.926 - [任务 6(100)][c34678cf-e7e3-44d6-ad68-667e13f077d4] - Node c34678cf-e7e3-44d6-ad68-667e13f077d4[c34678cf-e7e3-44d6-ad68-667e13f077d4] running status set to false 
[INFO ] 2024-07-15 14:38:00.926 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-40e2635a-a16c-4236-b526-4afc291b0b85 
[INFO ] 2024-07-15 14:38:00.926 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:38:00.926 - [任务 6(100)][c34678cf-e7e3-44d6-ad68-667e13f077d4] - Node c34678cf-e7e3-44d6-ad68-667e13f077d4[c34678cf-e7e3-44d6-ad68-667e13f077d4] schema data cleaned 
[INFO ] 2024-07-15 14:38:00.926 - [任务 6(100)][c34678cf-e7e3-44d6-ad68-667e13f077d4] - Node c34678cf-e7e3-44d6-ad68-667e13f077d4[c34678cf-e7e3-44d6-ad68-667e13f077d4] monitor closed 
[INFO ] 2024-07-15 14:38:00.929 - [任务 6(100)][c34678cf-e7e3-44d6-ad68-667e13f077d4] - Node c34678cf-e7e3-44d6-ad68-667e13f077d4[c34678cf-e7e3-44d6-ad68-667e13f077d4] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:38:00.929 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:38:00.929 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:38:00.930 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 7 ms 
[INFO ] 2024-07-15 14:38:00.930 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:38:00.930 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:38:00.930 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:40:15.409 - [任务 6(100)][5207e6ea-109b-4496-9ae6-509804484f4c] - Node 5207e6ea-109b-4496-9ae6-509804484f4c[5207e6ea-109b-4496-9ae6-509804484f4c] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:40:15.409 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:40:15.410 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:40:15.410 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:40:15.411 - [任务 6(100)][5207e6ea-109b-4496-9ae6-509804484f4c] - Node 5207e6ea-109b-4496-9ae6-509804484f4c[5207e6ea-109b-4496-9ae6-509804484f4c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:40:15.411 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:40:15.411 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:40:15.411 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:41:30.607 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:41:30.718 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:41:30.740 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:41:30.740 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:41:30.740 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:41:30.743 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 66299 ms 
[INFO ] 2024-07-15 14:41:30.759 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:41:30.759 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:41:30.759 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:41:30.776 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 10 ms 
[INFO ] 2024-07-15 14:41:30.776 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:41:30.786 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:41:30.790 - [任务 6(100)][5207e6ea-109b-4496-9ae6-509804484f4c] - Node 5207e6ea-109b-4496-9ae6-509804484f4c[5207e6ea-109b-4496-9ae6-509804484f4c] running status set to false 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][5207e6ea-109b-4496-9ae6-509804484f4c] - Node 5207e6ea-109b-4496-9ae6-509804484f4c[5207e6ea-109b-4496-9ae6-509804484f4c] schema data cleaned 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][5207e6ea-109b-4496-9ae6-509804484f4c] - Node 5207e6ea-109b-4496-9ae6-509804484f4c[5207e6ea-109b-4496-9ae6-509804484f4c] monitor closed 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][5207e6ea-109b-4496-9ae6-509804484f4c] - Node 5207e6ea-109b-4496-9ae6-509804484f4c[5207e6ea-109b-4496-9ae6-509804484f4c] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-713f253b-bd2e-4ef5-88bd-cc0bd2bebdcb 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-713f253b-bd2e-4ef5-88bd-cc0bd2bebdcb 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:41:30.794 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 11 ms 
[INFO ] 2024-07-15 14:41:30.892 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:41:30.892 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:41:30.892 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:42:39.735 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:42:39.735 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:42:39.735 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:42:39.737 - [任务 6(100)][ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] - Node ebb18b89-7ef9-415a-8eed-8d8fd11f4c21[ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:42:39.737 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:42:39.737 - [任务 6(100)][ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] - Node ebb18b89-7ef9-415a-8eed-8d8fd11f4c21[ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:42:39.737 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:42:39.737 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:42:43.292 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:42:43.293 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:42:43.293 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:42:43.293 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:42:43.294 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 20 ms 
[INFO ] 2024-07-15 14:42:43.329 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:42:43.329 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:42:43.329 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:42:43.330 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:42:43.330 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:42:43.330 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 3105 ms 
[INFO ] 2024-07-15 14:42:43.332 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:42:43.334 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-4c5128e7-b138-48ee-87c9-f3ac12602e80 
[INFO ] 2024-07-15 14:42:43.334 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-4c5128e7-b138-48ee-87c9-f3ac12602e80 
[INFO ] 2024-07-15 14:42:43.338 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:42:43.338 - [任务 6(100)][ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] - Node ebb18b89-7ef9-415a-8eed-8d8fd11f4c21[ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] running status set to false 
[INFO ] 2024-07-15 14:42:43.338 - [任务 6(100)][ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] - Node ebb18b89-7ef9-415a-8eed-8d8fd11f4c21[ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] schema data cleaned 
[INFO ] 2024-07-15 14:42:43.338 - [任务 6(100)][ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] - Node ebb18b89-7ef9-415a-8eed-8d8fd11f4c21[ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] monitor closed 
[INFO ] 2024-07-15 14:42:43.338 - [任务 6(100)][ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] - Node ebb18b89-7ef9-415a-8eed-8d8fd11f4c21[ebb18b89-7ef9-415a-8eed-8d8fd11f4c21] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:42:43.340 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:42:43.340 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:42:43.342 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 8 ms 
[INFO ] 2024-07-15 14:42:43.342 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:42:43.342 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:42:43.433 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:46:02.048 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:46:02.050 - [任务 6(100)][696f6509-285e-439c-97e0-3d7108fd90cd] - Node 696f6509-285e-439c-97e0-3d7108fd90cd[696f6509-285e-439c-97e0-3d7108fd90cd] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:46:02.051 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:46:02.051 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:46:02.051 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:02.051 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:02.051 - [任务 6(100)][696f6509-285e-439c-97e0-3d7108fd90cd] - Node 696f6509-285e-439c-97e0-3d7108fd90cd[696f6509-285e-439c-97e0-3d7108fd90cd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:02.051 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:02.531 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:46:05.133 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:46:05.142 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:46:05.142 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:46:05.142 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:46:05.143 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:46:05.143 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:46:05.143 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:46:05.143 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 2602 ms 
[INFO ] 2024-07-15 14:46:05.144 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 4 ms 
[INFO ] 2024-07-15 14:46:05.197 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:46:05.202 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:46:05.203 - [任务 6(100)][696f6509-285e-439c-97e0-3d7108fd90cd] - Node 696f6509-285e-439c-97e0-3d7108fd90cd[696f6509-285e-439c-97e0-3d7108fd90cd] running status set to false 
[INFO ] 2024-07-15 14:46:05.203 - [任务 6(100)][696f6509-285e-439c-97e0-3d7108fd90cd] - Node 696f6509-285e-439c-97e0-3d7108fd90cd[696f6509-285e-439c-97e0-3d7108fd90cd] schema data cleaned 
[INFO ] 2024-07-15 14:46:05.203 - [任务 6(100)][696f6509-285e-439c-97e0-3d7108fd90cd] - Node 696f6509-285e-439c-97e0-3d7108fd90cd[696f6509-285e-439c-97e0-3d7108fd90cd] monitor closed 
[INFO ] 2024-07-15 14:46:05.203 - [任务 6(100)][696f6509-285e-439c-97e0-3d7108fd90cd] - Node 696f6509-285e-439c-97e0-3d7108fd90cd[696f6509-285e-439c-97e0-3d7108fd90cd] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:46:05.206 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-fbef03b6-39c8-4faf-afb8-5d9d201285d1 
[INFO ] 2024-07-15 14:46:05.207 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-fbef03b6-39c8-4faf-afb8-5d9d201285d1 
[INFO ] 2024-07-15 14:46:05.208 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:46:05.208 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:46:05.212 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:46:05.212 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 9 ms 
[INFO ] 2024-07-15 14:46:05.212 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:46:05.212 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:46:05.212 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:46:43.745 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:46:43.747 - [任务 6(100)][99f26ec1-04f8-4a69-ab9a-149f855ed5f5] - Node 99f26ec1-04f8-4a69-ab9a-149f855ed5f5[99f26ec1-04f8-4a69-ab9a-149f855ed5f5] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:46:43.747 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:46:43.747 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:46:43.747 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:43.747 - [任务 6(100)][99f26ec1-04f8-4a69-ab9a-149f855ed5f5] - Node 99f26ec1-04f8-4a69-ab9a-149f855ed5f5[99f26ec1-04f8-4a69-ab9a-149f855ed5f5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:43.747 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:43.747 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:46:47.649 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:46:47.651 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:46:47.654 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:46:47.654 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:46:47.654 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 13 ms 
[INFO ] 2024-07-15 14:46:47.667 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:46:47.667 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:46:47.667 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:46:47.667 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:46:47.719 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 3484 ms 
[INFO ] 2024-07-15 14:46:47.720 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:46:47.724 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:46:47.725 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-24528432-21ec-4b57-8fbd-1131ad7e09f4 
[INFO ] 2024-07-15 14:46:47.725 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-24528432-21ec-4b57-8fbd-1131ad7e09f4 
[INFO ] 2024-07-15 14:46:47.725 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:46:47.726 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:46:47.727 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:46:47.727 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 5 ms 
[INFO ] 2024-07-15 14:46:47.728 - [任务 6(100)][99f26ec1-04f8-4a69-ab9a-149f855ed5f5] - Node 99f26ec1-04f8-4a69-ab9a-149f855ed5f5[99f26ec1-04f8-4a69-ab9a-149f855ed5f5] running status set to false 
[INFO ] 2024-07-15 14:46:47.728 - [任务 6(100)][99f26ec1-04f8-4a69-ab9a-149f855ed5f5] - Node 99f26ec1-04f8-4a69-ab9a-149f855ed5f5[99f26ec1-04f8-4a69-ab9a-149f855ed5f5] schema data cleaned 
[INFO ] 2024-07-15 14:46:47.728 - [任务 6(100)][99f26ec1-04f8-4a69-ab9a-149f855ed5f5] - Node 99f26ec1-04f8-4a69-ab9a-149f855ed5f5[99f26ec1-04f8-4a69-ab9a-149f855ed5f5] monitor closed 
[INFO ] 2024-07-15 14:46:47.731 - [任务 6(100)][99f26ec1-04f8-4a69-ab9a-149f855ed5f5] - Node 99f26ec1-04f8-4a69-ab9a-149f855ed5f5[99f26ec1-04f8-4a69-ab9a-149f855ed5f5] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:46:47.731 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:46:47.731 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:46:47.834 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:48:50.471 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:48:50.471 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:48:50.471 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:48:50.471 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:48:50.471 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:48:50.471 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:48:50.472 - [任务 6(100)][ab27e652-5aac-44ff-beff-163c17ca4fec] - Node ab27e652-5aac-44ff-beff-163c17ca4fec[ab27e652-5aac-44ff-beff-163c17ca4fec] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:48:50.472 - [任务 6(100)][ab27e652-5aac-44ff-beff-163c17ca4fec] - Node ab27e652-5aac-44ff-beff-163c17ca4fec[ab27e652-5aac-44ff-beff-163c17ca4fec] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:48:54.021 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:48:54.022 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:48:54.022 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:48:54.022 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:48:54.026 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 17 ms 
[INFO ] 2024-07-15 14:48:54.032 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:48:54.032 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:48:54.035 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:48:54.035 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:48:54.096 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 3123 ms 
[INFO ] 2024-07-15 14:48:54.096 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:48:54.101 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:48:54.103 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-0fc77b60-073c-4114-aa1f-fdee508ce71a 
[INFO ] 2024-07-15 14:48:54.103 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-0fc77b60-073c-4114-aa1f-fdee508ce71a 
[INFO ] 2024-07-15 14:48:54.103 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:48:54.104 - [任务 6(100)][ab27e652-5aac-44ff-beff-163c17ca4fec] - Node ab27e652-5aac-44ff-beff-163c17ca4fec[ab27e652-5aac-44ff-beff-163c17ca4fec] running status set to false 
[INFO ] 2024-07-15 14:48:54.104 - [任务 6(100)][ab27e652-5aac-44ff-beff-163c17ca4fec] - Node ab27e652-5aac-44ff-beff-163c17ca4fec[ab27e652-5aac-44ff-beff-163c17ca4fec] schema data cleaned 
[INFO ] 2024-07-15 14:48:54.104 - [任务 6(100)][ab27e652-5aac-44ff-beff-163c17ca4fec] - Node ab27e652-5aac-44ff-beff-163c17ca4fec[ab27e652-5aac-44ff-beff-163c17ca4fec] monitor closed 
[INFO ] 2024-07-15 14:48:54.104 - [任务 6(100)][ab27e652-5aac-44ff-beff-163c17ca4fec] - Node ab27e652-5aac-44ff-beff-163c17ca4fec[ab27e652-5aac-44ff-beff-163c17ca4fec] close complete, cost 0 ms 
[INFO ] 2024-07-15 14:48:54.104 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:48:54.104 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:48:54.104 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 7 ms 
[INFO ] 2024-07-15 14:48:54.106 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:48:54.106 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:48:54.107 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:51:29.177 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:51:29.180 - [任务 6(100)][e70042c4-d99a-4071-9c2d-22cac2957625] - Node e70042c4-d99a-4071-9c2d-22cac2957625[e70042c4-d99a-4071-9c2d-22cac2957625] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:51:29.180 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:51:29.180 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:51:29.180 - [任务 6(100)][e70042c4-d99a-4071-9c2d-22cac2957625] - Node e70042c4-d99a-4071-9c2d-22cac2957625[e70042c4-d99a-4071-9c2d-22cac2957625] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:51:29.180 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 14:51:29.180 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:51:29.180 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 3 ms 
[INFO ] 2024-07-15 14:51:33.895 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:51:33.922 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 15 ms 
[INFO ] 2024-07-15 14:51:33.926 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 4292 ms 
[INFO ] 2024-07-15 14:51:33.973 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:51:33.975 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:51:33.976 - [任务 6(100)][e70042c4-d99a-4071-9c2d-22cac2957625] - Node e70042c4-d99a-4071-9c2d-22cac2957625[e70042c4-d99a-4071-9c2d-22cac2957625] running status set to false 
[INFO ] 2024-07-15 14:51:33.976 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-f1260a60-b4c4-4b82-9484-9b4dd8f713c4 
[INFO ] 2024-07-15 14:51:33.977 - [任务 6(100)][e70042c4-d99a-4071-9c2d-22cac2957625] - Node e70042c4-d99a-4071-9c2d-22cac2957625[e70042c4-d99a-4071-9c2d-22cac2957625] schema data cleaned 
[INFO ] 2024-07-15 14:51:33.977 - [任务 6(100)][e70042c4-d99a-4071-9c2d-22cac2957625] - Node e70042c4-d99a-4071-9c2d-22cac2957625[e70042c4-d99a-4071-9c2d-22cac2957625] monitor closed 
[INFO ] 2024-07-15 14:51:33.977 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-f1260a60-b4c4-4b82-9484-9b4dd8f713c4 
[INFO ] 2024-07-15 14:51:33.977 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:51:33.978 - [任务 6(100)][e70042c4-d99a-4071-9c2d-22cac2957625] - Node e70042c4-d99a-4071-9c2d-22cac2957625[e70042c4-d99a-4071-9c2d-22cac2957625] close complete, cost 1 ms 
[INFO ] 2024-07-15 14:51:33.978 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:51:33.978 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:51:33.980 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 7 ms 
[INFO ] 2024-07-15 14:51:33.981 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:51:33.981 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:51:34.185 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:51:35.571 - [任务 6(100)][08541d7b-207e-4755-96d4-f79862b07d09] - Node 08541d7b-207e-4755-96d4-f79862b07d09[08541d7b-207e-4755-96d4-f79862b07d09] start preload schema,table counts: 0 
[INFO ] 2024-07-15 14:51:35.571 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:51:35.571 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:51:35.571 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:51:35.572 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:51:35.572 - [任务 6(100)][08541d7b-207e-4755-96d4-f79862b07d09] - Node 08541d7b-207e-4755-96d4-f79862b07d09[08541d7b-207e-4755-96d4-f79862b07d09] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:51:35.572 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:51:35.572 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:51:35.968 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 14:51:38.446 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 14:51:38.446 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:51:38.446 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 14:51:38.446 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 14:51:38.446 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 14:51:38.446 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 14:51:38.446 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 14:51:38.447 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 2479 ms 
[INFO ] 2024-07-15 14:51:38.447 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 6 ms 
[INFO ] 2024-07-15 14:51:38.491 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 14:51:38.497 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 14:51:38.498 - [任务 6(100)][08541d7b-207e-4755-96d4-f79862b07d09] - Node 08541d7b-207e-4755-96d4-f79862b07d09[08541d7b-207e-4755-96d4-f79862b07d09] running status set to false 
[INFO ] 2024-07-15 14:51:38.499 - [任务 6(100)][08541d7b-207e-4755-96d4-f79862b07d09] - Node 08541d7b-207e-4755-96d4-f79862b07d09[08541d7b-207e-4755-96d4-f79862b07d09] schema data cleaned 
[INFO ] 2024-07-15 14:51:38.499 - [任务 6(100)][08541d7b-207e-4755-96d4-f79862b07d09] - Node 08541d7b-207e-4755-96d4-f79862b07d09[08541d7b-207e-4755-96d4-f79862b07d09] monitor closed 
[INFO ] 2024-07-15 14:51:38.499 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-881b7b0d-969c-411b-9810-c7e619d96578 
[INFO ] 2024-07-15 14:51:38.499 - [任务 6(100)][08541d7b-207e-4755-96d4-f79862b07d09] - Node 08541d7b-207e-4755-96d4-f79862b07d09[08541d7b-207e-4755-96d4-f79862b07d09] close complete, cost 2 ms 
[INFO ] 2024-07-15 14:51:38.499 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-881b7b0d-969c-411b-9810-c7e619d96578 
[INFO ] 2024-07-15 14:51:38.499 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 14:51:38.502 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 14:51:38.502 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 14:51:38.505 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 9 ms 
[INFO ] 2024-07-15 14:51:38.505 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 14:51:38.505 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 14:51:38.563 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:02:10.640 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:02:10.641 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:02:10.642 - [任务 6(100)][6dc244cd-7707-4b92-8a80-e35171e091e8] - Node 6dc244cd-7707-4b92-8a80-e35171e091e8[6dc244cd-7707-4b92-8a80-e35171e091e8] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:02:10.642 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:02:10.642 - [任务 6(100)][6dc244cd-7707-4b92-8a80-e35171e091e8] - Node 6dc244cd-7707-4b92-8a80-e35171e091e8[6dc244cd-7707-4b92-8a80-e35171e091e8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:02:10.642 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:02:10.642 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:02:10.642 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:02:18.120 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:02:18.279 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:02:18.288 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:02:18.288 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:02:18.300 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 159 ms 
[INFO ] 2024-07-15 15:02:18.301 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:02:18.301 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:02:18.301 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:02:18.301 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:02:18.333 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 7204 ms 
[INFO ] 2024-07-15 15:02:18.333 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:02:18.336 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:02:18.337 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-066adc17-7faa-4c04-b35d-6e8a39b3db17 
[INFO ] 2024-07-15 15:02:18.337 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-066adc17-7faa-4c04-b35d-6e8a39b3db17 
[INFO ] 2024-07-15 15:02:18.337 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:02:18.340 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:02:18.340 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:02:18.341 - [任务 6(100)][6dc244cd-7707-4b92-8a80-e35171e091e8] - Node 6dc244cd-7707-4b92-8a80-e35171e091e8[6dc244cd-7707-4b92-8a80-e35171e091e8] running status set to false 
[INFO ] 2024-07-15 15:02:18.341 - [任务 6(100)][6dc244cd-7707-4b92-8a80-e35171e091e8] - Node 6dc244cd-7707-4b92-8a80-e35171e091e8[6dc244cd-7707-4b92-8a80-e35171e091e8] schema data cleaned 
[INFO ] 2024-07-15 15:02:18.341 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 6 ms 
[INFO ] 2024-07-15 15:02:18.341 - [任务 6(100)][6dc244cd-7707-4b92-8a80-e35171e091e8] - Node 6dc244cd-7707-4b92-8a80-e35171e091e8[6dc244cd-7707-4b92-8a80-e35171e091e8] monitor closed 
[INFO ] 2024-07-15 15:02:18.345 - [任务 6(100)][6dc244cd-7707-4b92-8a80-e35171e091e8] - Node 6dc244cd-7707-4b92-8a80-e35171e091e8[6dc244cd-7707-4b92-8a80-e35171e091e8] close complete, cost 1 ms 
[INFO ] 2024-07-15 15:02:18.345 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:02:18.345 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:02:18.421 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:12:18.067 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:12:18.067 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:12:18.070 - [任务 6(100)][928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] - Node 928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae[928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:12:18.070 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:12:18.071 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:12:18.071 - [任务 6(100)][928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] - Node 928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae[928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:12:18.071 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:12:18.071 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:12:29.503 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:12:29.523 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:12:29.525 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:12:29.525 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:12:29.535 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 69 ms 
[INFO ] 2024-07-15 15:12:29.559 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:12:29.573 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:12:29.573 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:12:29.573 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:12:29.579 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 10912 ms 
[INFO ] 2024-07-15 15:12:29.595 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:12:29.597 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:12:29.600 - [任务 6(100)][928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] - Node 928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae[928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] running status set to false 
[INFO ] 2024-07-15 15:12:29.600 - [任务 6(100)][928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] - Node 928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae[928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] schema data cleaned 
[INFO ] 2024-07-15 15:12:29.601 - [任务 6(100)][928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] - Node 928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae[928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] monitor closed 
[INFO ] 2024-07-15 15:12:29.601 - [任务 6(100)][928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] - Node 928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae[928d7c62-bc01-4cf2-9f1d-9ea234f3f9ae] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:12:29.601 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-28359360-361f-43d5-92d0-c6d8e01b8b0d 
[INFO ] 2024-07-15 15:12:29.601 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-28359360-361f-43d5-92d0-c6d8e01b8b0d 
[INFO ] 2024-07-15 15:12:29.601 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:12:29.604 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:12:29.604 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:12:29.607 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 9 ms 
[INFO ] 2024-07-15 15:12:29.607 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:12:29.607 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:12:29.698 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:14:36.823 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:14:36.824 - [任务 6(100)][e36c2258-a89c-4e09-af98-5b2196c57e2f] - Node e36c2258-a89c-4e09-af98-5b2196c57e2f[e36c2258-a89c-4e09-af98-5b2196c57e2f] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:14:36.824 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:14:36.824 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:14:36.824 - [任务 6(100)][e36c2258-a89c-4e09-af98-5b2196c57e2f] - Node e36c2258-a89c-4e09-af98-5b2196c57e2f[e36c2258-a89c-4e09-af98-5b2196c57e2f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:14:36.824 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:14:36.824 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:14:36.824 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 15:14:37.329 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:14:37.329 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:14:37.330 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:14:37.330 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:14:37.331 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:14:37.331 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 7 ms 
[INFO ] 2024-07-15 15:14:37.335 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:14:37.335 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:14:37.335 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:14:37.335 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:14:37.356 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:14:37.360 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:14:37.360 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-d25d6ce0-6491-4aa3-ade2-c1650c835681 
[INFO ] 2024-07-15 15:14:37.360 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-d25d6ce0-6491-4aa3-ade2-c1650c835681 
[INFO ] 2024-07-15 15:14:37.360 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:14:37.361 - [任务 6(100)][e36c2258-a89c-4e09-af98-5b2196c57e2f] - Node e36c2258-a89c-4e09-af98-5b2196c57e2f[e36c2258-a89c-4e09-af98-5b2196c57e2f] running status set to false 
[INFO ] 2024-07-15 15:14:37.361 - [任务 6(100)][e36c2258-a89c-4e09-af98-5b2196c57e2f] - Node e36c2258-a89c-4e09-af98-5b2196c57e2f[e36c2258-a89c-4e09-af98-5b2196c57e2f] schema data cleaned 
[INFO ] 2024-07-15 15:14:37.361 - [任务 6(100)][e36c2258-a89c-4e09-af98-5b2196c57e2f] - Node e36c2258-a89c-4e09-af98-5b2196c57e2f[e36c2258-a89c-4e09-af98-5b2196c57e2f] monitor closed 
[INFO ] 2024-07-15 15:14:37.361 - [任务 6(100)][e36c2258-a89c-4e09-af98-5b2196c57e2f] - Node e36c2258-a89c-4e09-af98-5b2196c57e2f[e36c2258-a89c-4e09-af98-5b2196c57e2f] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:14:37.361 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:14:37.361 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:14:37.362 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 4 ms 
[INFO ] 2024-07-15 15:14:37.362 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:14:37.362 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:14:37.394 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:17:42.029 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:17:42.029 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:17:42.030 - [任务 6(100)][9beec416-2a75-4aff-a4fb-7baca29deefa] - Node 9beec416-2a75-4aff-a4fb-7baca29deefa[9beec416-2a75-4aff-a4fb-7baca29deefa] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:17:42.033 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:17:42.033 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:17:42.033 - [任务 6(100)][9beec416-2a75-4aff-a4fb-7baca29deefa] - Node 9beec416-2a75-4aff-a4fb-7baca29deefa[9beec416-2a75-4aff-a4fb-7baca29deefa] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:17:42.033 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:17:42.033 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:18:17.835 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:18:18.782 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:18:18.783 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:18:18.783 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:18:18.786 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 21 ms 
[INFO ] 2024-07-15 15:18:18.786 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:18:18.787 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:18:18.787 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:18:18.787 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:18:18.787 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 1863 ms 
[INFO ] 2024-07-15 15:18:18.867 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:18:18.868 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:18:18.873 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-57a29c52-c053-4162-b692-cbc28f03255a 
[INFO ] 2024-07-15 15:18:18.873 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-57a29c52-c053-4162-b692-cbc28f03255a 
[INFO ] 2024-07-15 15:18:18.873 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:18:18.874 - [任务 6(100)][9beec416-2a75-4aff-a4fb-7baca29deefa] - Node 9beec416-2a75-4aff-a4fb-7baca29deefa[9beec416-2a75-4aff-a4fb-7baca29deefa] running status set to false 
[INFO ] 2024-07-15 15:18:18.874 - [任务 6(100)][9beec416-2a75-4aff-a4fb-7baca29deefa] - Node 9beec416-2a75-4aff-a4fb-7baca29deefa[9beec416-2a75-4aff-a4fb-7baca29deefa] schema data cleaned 
[INFO ] 2024-07-15 15:18:18.874 - [任务 6(100)][9beec416-2a75-4aff-a4fb-7baca29deefa] - Node 9beec416-2a75-4aff-a4fb-7baca29deefa[9beec416-2a75-4aff-a4fb-7baca29deefa] monitor closed 
[INFO ] 2024-07-15 15:18:18.874 - [任务 6(100)][9beec416-2a75-4aff-a4fb-7baca29deefa] - Node 9beec416-2a75-4aff-a4fb-7baca29deefa[9beec416-2a75-4aff-a4fb-7baca29deefa] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:18:18.879 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:18:18.879 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:18:18.879 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 12 ms 
[INFO ] 2024-07-15 15:18:18.882 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:18:18.885 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:18:18.885 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][b3e0890f-d42d-4a49-aa3f-783ba373f3bf] - Node b3e0890f-d42d-4a49-aa3f-783ba373f3bf[b3e0890f-d42d-4a49-aa3f-783ba373f3bf] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][b3e0890f-d42d-4a49-aa3f-783ba373f3bf] - Node b3e0890f-d42d-4a49-aa3f-783ba373f3bf[b3e0890f-d42d-4a49-aa3f-783ba373f3bf] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:20:08.676 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:20:21.405 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:20:21.445 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:20:21.445 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:20:21.445 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:20:21.449 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 19 ms 
[INFO ] 2024-07-15 15:20:21.449 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:20:21.450 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:20:21.453 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:20:21.453 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:20:21.457 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 9943 ms 
[INFO ] 2024-07-15 15:20:21.504 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424476800 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562260422 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:20:21.504 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:20:21.506 - [任务 6(100)][b3e0890f-d42d-4a49-aa3f-783ba373f3bf] - Node b3e0890f-d42d-4a49-aa3f-783ba373f3bf[b3e0890f-d42d-4a49-aa3f-783ba373f3bf] running status set to false 
[INFO ] 2024-07-15 15:20:21.507 - [任务 6(100)][b3e0890f-d42d-4a49-aa3f-783ba373f3bf] - Node b3e0890f-d42d-4a49-aa3f-783ba373f3bf[b3e0890f-d42d-4a49-aa3f-783ba373f3bf] schema data cleaned 
[INFO ] 2024-07-15 15:20:21.507 - [任务 6(100)][b3e0890f-d42d-4a49-aa3f-783ba373f3bf] - Node b3e0890f-d42d-4a49-aa3f-783ba373f3bf[b3e0890f-d42d-4a49-aa3f-783ba373f3bf] monitor closed 
[INFO ] 2024-07-15 15:20:21.507 - [任务 6(100)][b3e0890f-d42d-4a49-aa3f-783ba373f3bf] - Node b3e0890f-d42d-4a49-aa3f-783ba373f3bf[b3e0890f-d42d-4a49-aa3f-783ba373f3bf] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:20:21.508 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-b6c8d40c-6914-440c-8135-869235742dcd 
[INFO ] 2024-07-15 15:20:21.508 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-b6c8d40c-6914-440c-8135-869235742dcd 
[INFO ] 2024-07-15 15:20:21.508 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:20:21.509 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:20:21.509 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:20:21.509 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 6 ms 
[INFO ] 2024-07-15 15:20:21.511 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:20:21.511 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:20:21.601 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:23.752 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:23.753 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:23.753 - [任务 6(100)][77690b0e-04e9-49c3-816d-6ec731357f7b] - Node 77690b0e-04e9-49c3-816d-6ec731357f7b[77690b0e-04e9-49c3-816d-6ec731357f7b] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:23.753 - [任务 6(100)][77690b0e-04e9-49c3-816d-6ec731357f7b] - Node 77690b0e-04e9-49c3-816d-6ec731357f7b[77690b0e-04e9-49c3-816d-6ec731357f7b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:23.754 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:23.754 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:22:24.174 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:24.198 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:24.198 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:24.198 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:24.200 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:24.204 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 26 ms 
[INFO ] 2024-07-15 15:22:24.204 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:24.210 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-40925a3d-86df-41ba-918a-79db31b0cd7f 
[INFO ] 2024-07-15 15:22:24.210 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-40925a3d-86df-41ba-918a-79db31b0cd7f 
[INFO ] 2024-07-15 15:22:24.210 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:24.210 - [任务 6(100)][77690b0e-04e9-49c3-816d-6ec731357f7b] - Node 77690b0e-04e9-49c3-816d-6ec731357f7b[77690b0e-04e9-49c3-816d-6ec731357f7b] running status set to false 
[INFO ] 2024-07-15 15:22:24.210 - [任务 6(100)][77690b0e-04e9-49c3-816d-6ec731357f7b] - Node 77690b0e-04e9-49c3-816d-6ec731357f7b[77690b0e-04e9-49c3-816d-6ec731357f7b] schema data cleaned 
[INFO ] 2024-07-15 15:22:24.210 - [任务 6(100)][77690b0e-04e9-49c3-816d-6ec731357f7b] - Node 77690b0e-04e9-49c3-816d-6ec731357f7b[77690b0e-04e9-49c3-816d-6ec731357f7b] monitor closed 
[INFO ] 2024-07-15 15:22:24.210 - [任务 6(100)][77690b0e-04e9-49c3-816d-6ec731357f7b] - Node 77690b0e-04e9-49c3-816d-6ec731357f7b[77690b0e-04e9-49c3-816d-6ec731357f7b] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:24.211 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:24.211 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:24.211 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 8 ms 
[INFO ] 2024-07-15 15:22:24.212 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:24.212 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:24.263 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:24.263 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][13d66ca3-dc03-417a-940d-1bf96a9989ed] - Node 13d66ca3-dc03-417a-940d-1bf96a9989ed[13d66ca3-dc03-417a-940d-1bf96a9989ed] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:24.264 - [任务 6(100)][13d66ca3-dc03-417a-940d-1bf96a9989ed] - Node 13d66ca3-dc03-417a-940d-1bf96a9989ed[13d66ca3-dc03-417a-940d-1bf96a9989ed] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:24.265 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:24.411 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:24.413 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:24.413 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:24.413 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:24.414 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:24.414 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 2 ms 
[INFO ] 2024-07-15 15:22:24.495 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:22:24.496 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:22:24.722 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:24.723 - [任务 6(100)][43fbccbe-2030-4594-848e-866e8c4dd8a1] - Node 43fbccbe-2030-4594-848e-866e8c4dd8a1[43fbccbe-2030-4594-848e-866e8c4dd8a1] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:24.723 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:24.723 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:22:24.723 - [任务 6(100)][43fbccbe-2030-4594-848e-866e8c4dd8a1] - Node 43fbccbe-2030-4594-848e-866e8c4dd8a1[43fbccbe-2030-4594-848e-866e8c4dd8a1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:24.723 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:22:24.958 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:24.958 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:24.959 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:24.959 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:24.959 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:24.960 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 35 ms 
[INFO ] 2024-07-15 15:22:25.022 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:25.023 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-451ed8a5-2ace-445a-834b-e15ddce05ad3 
[INFO ] 2024-07-15 15:22:25.023 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-451ed8a5-2ace-445a-834b-e15ddce05ad3 
[INFO ] 2024-07-15 15:22:25.023 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:25.027 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:25.027 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:25.027 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 8 ms 
[INFO ] 2024-07-15 15:22:25.027 - [任务 6(100)][43fbccbe-2030-4594-848e-866e8c4dd8a1] - Node 43fbccbe-2030-4594-848e-866e8c4dd8a1[43fbccbe-2030-4594-848e-866e8c4dd8a1] running status set to false 
[INFO ] 2024-07-15 15:22:25.028 - [任务 6(100)][43fbccbe-2030-4594-848e-866e8c4dd8a1] - Node 43fbccbe-2030-4594-848e-866e8c4dd8a1[43fbccbe-2030-4594-848e-866e8c4dd8a1] schema data cleaned 
[INFO ] 2024-07-15 15:22:25.028 - [任务 6(100)][43fbccbe-2030-4594-848e-866e8c4dd8a1] - Node 43fbccbe-2030-4594-848e-866e8c4dd8a1[43fbccbe-2030-4594-848e-866e8c4dd8a1] monitor closed 
[INFO ] 2024-07-15 15:22:25.030 - [任务 6(100)][43fbccbe-2030-4594-848e-866e8c4dd8a1] - Node 43fbccbe-2030-4594-848e-866e8c4dd8a1[43fbccbe-2030-4594-848e-866e8c4dd8a1] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:25.030 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:25.030 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:25.127 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][66369db7-e7ee-4acd-ac5b-ef7e62e4d321] - Node 66369db7-e7ee-4acd-ac5b-ef7e62e4d321[66369db7-e7ee-4acd-ac5b-ef7e62e4d321] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:25.128 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:25.293 - [任务 6(100)][66369db7-e7ee-4acd-ac5b-ef7e62e4d321] - Node 66369db7-e7ee-4acd-ac5b-ef7e62e4d321[66369db7-e7ee-4acd-ac5b-ef7e62e4d321] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:25.293 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:25.295 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:25.295 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:25.295 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:25.295 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:25.366 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 2 ms 
[INFO ] 2024-07-15 15:22:25.366 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:22:25.568 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:22:27.083 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:27.096 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:22:27.098 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.099 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:22:27.099 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:22:27.099 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 11 ms 
[INFO ] 2024-07-15 15:22:27.100 - [任务 6(100)][13d66ca3-dc03-417a-940d-1bf96a9989ed] - Node 13d66ca3-dc03-417a-940d-1bf96a9989ed[13d66ca3-dc03-417a-940d-1bf96a9989ed] running status set to false 
[INFO ] 2024-07-15 15:22:27.101 - [任务 6(100)][13d66ca3-dc03-417a-940d-1bf96a9989ed] - Node 13d66ca3-dc03-417a-940d-1bf96a9989ed[13d66ca3-dc03-417a-940d-1bf96a9989ed] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.102 - [任务 6(100)][13d66ca3-dc03-417a-940d-1bf96a9989ed] - Node 13d66ca3-dc03-417a-940d-1bf96a9989ed[13d66ca3-dc03-417a-940d-1bf96a9989ed] monitor closed 
[INFO ] 2024-07-15 15:22:27.102 - [任务 6(100)][13d66ca3-dc03-417a-940d-1bf96a9989ed] - Node 13d66ca3-dc03-417a-940d-1bf96a9989ed[13d66ca3-dc03-417a-940d-1bf96a9989ed] close complete, cost 1 ms 
[INFO ] 2024-07-15 15:22:27.108 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-b73d63d9-dca3-4a99-ba5b-357763bd1550 
[INFO ] 2024-07-15 15:22:27.108 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-014a5f3f-e04b-4e39-a720-fdf02d171d59 
[INFO ] 2024-07-15 15:22:27.109 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-b73d63d9-dca3-4a99-ba5b-357763bd1550 
[INFO ] 2024-07-15 15:22:27.109 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-014a5f3f-e04b-4e39-a720-fdf02d171d59 
[INFO ] 2024-07-15 15:22:27.109 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.109 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.135 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.139 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:27.139 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 59 ms 
[INFO ] 2024-07-15 15:22:27.139 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.139 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:22:27.139 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 48 ms 
[INFO ] 2024-07-15 15:22:27.140 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:27.140 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:27.141 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:27.940 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:27.940 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:22:27.941 - [任务 6(100)][66369db7-e7ee-4acd-ac5b-ef7e62e4d321] - Node 66369db7-e7ee-4acd-ac5b-ef7e62e4d321[66369db7-e7ee-4acd-ac5b-ef7e62e4d321] running status set to false 
[INFO ] 2024-07-15 15:22:27.941 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:22:27.941 - [任务 6(100)][66369db7-e7ee-4acd-ac5b-ef7e62e4d321] - Node 66369db7-e7ee-4acd-ac5b-ef7e62e4d321[66369db7-e7ee-4acd-ac5b-ef7e62e4d321] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.942 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.942 - [任务 6(100)][66369db7-e7ee-4acd-ac5b-ef7e62e4d321] - Node 66369db7-e7ee-4acd-ac5b-ef7e62e4d321[66369db7-e7ee-4acd-ac5b-ef7e62e4d321] monitor closed 
[INFO ] 2024-07-15 15:22:27.942 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:22:27.942 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 16 ms 
[INFO ] 2024-07-15 15:22:27.942 - [任务 6(100)][66369db7-e7ee-4acd-ac5b-ef7e62e4d321] - Node 66369db7-e7ee-4acd-ac5b-ef7e62e4d321[66369db7-e7ee-4acd-ac5b-ef7e62e4d321] close complete, cost 8 ms 
[INFO ] 2024-07-15 15:22:27.945 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-fcc29964-ef3e-4d76-b7c3-d3463efde05d 
[INFO ] 2024-07-15 15:22:27.945 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-cce830f4-ca04-4ced-b5b5-9a3120540f3f 
[INFO ] 2024-07-15 15:22:27.945 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-fcc29964-ef3e-4d76-b7c3-d3463efde05d 
[INFO ] 2024-07-15 15:22:27.945 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-cce830f4-ca04-4ced-b5b5-9a3120540f3f 
[INFO ] 2024-07-15 15:22:27.945 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.945 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.955 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.955 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:27.956 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 35 ms 
[INFO ] 2024-07-15 15:22:27.958 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:22:27.958 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:22:27.958 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 32 ms 
[INFO ] 2024-07-15 15:22:27.959 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:27.959 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:27.960 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:30.792 - [任务 6(100)][ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] - Node ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b[ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:30.792 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:30.793 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:30.793 - [任务 6(100)][ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] - Node ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b[ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:30.793 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:30.793 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:30.959 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:30.959 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:30.959 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:30.959 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:30.959 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:31.024 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 4 ms 
[INFO ] 2024-07-15 15:22:31.024 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:31.030 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:31.030 - [任务 6(100)][ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] - Node ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b[ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] running status set to false 
[INFO ] 2024-07-15 15:22:31.030 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-8f21eda1-55e5-4d5b-a508-b75816bfa43f 
[INFO ] 2024-07-15 15:22:31.030 - [任务 6(100)][ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] - Node ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b[ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] schema data cleaned 
[INFO ] 2024-07-15 15:22:31.030 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-8f21eda1-55e5-4d5b-a508-b75816bfa43f 
[INFO ] 2024-07-15 15:22:31.030 - [任务 6(100)][ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] - Node ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b[ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] monitor closed 
[INFO ] 2024-07-15 15:22:31.030 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:31.031 - [任务 6(100)][ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] - Node ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b[ab29f048-1cc9-4f3b-91fa-cc5d3dd9c91b] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:31.032 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:31.032 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:31.032 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 5 ms 
[INFO ] 2024-07-15 15:22:31.033 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:31.033 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:31.033 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:31.080 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:31.080 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:31.080 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:31.080 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:31.080 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:31.080 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:31.081 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:31.081 - [任务 6(100)][39f2539a-8e8d-4a73-b704-9277284a2b15] - Node 39f2539a-8e8d-4a73-b704-9277284a2b15[39f2539a-8e8d-4a73-b704-9277284a2b15] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:31.081 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:31.081 - [任务 6(100)][39f2539a-8e8d-4a73-b704-9277284a2b15] - Node 39f2539a-8e8d-4a73-b704-9277284a2b15[39f2539a-8e8d-4a73-b704-9277284a2b15] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:31.281 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:31.285 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:31.286 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:31.286 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:31.286 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:31.287 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 7 ms 
[INFO ] 2024-07-15 15:22:31.335 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:31.337 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:22:31.540 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:22:33.423 - [任务 6(100)][681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] - Node 681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee[681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:33.424 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:33.424 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:33.424 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:22:33.424 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 15:22:33.424 - [任务 6(100)][681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] - Node 681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee[681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 15:22:33.627 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:33.631 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:33.631 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:33.631 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.631 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:33.677 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 6 ms 
[INFO ] 2024-07-15 15:22:33.677 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:33.683 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:33.683 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-e2fbe273-6c73-443b-86c6-07ef2f6bec5c 
[INFO ] 2024-07-15 15:22:33.683 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-e2fbe273-6c73-443b-86c6-07ef2f6bec5c 
[INFO ] 2024-07-15 15:22:33.683 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.685 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.685 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:33.685 - [任务 6(100)][681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] - Node 681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee[681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] running status set to false 
[INFO ] 2024-07-15 15:22:33.685 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 5 ms 
[INFO ] 2024-07-15 15:22:33.685 - [任务 6(100)][681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] - Node 681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee[681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.685 - [任务 6(100)][681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] - Node 681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee[681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] monitor closed 
[INFO ] 2024-07-15 15:22:33.686 - [任务 6(100)][681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] - Node 681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee[681bb5cc-d1ac-4a7c-a33a-b98c8c43e2ee] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:33.686 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:33.686 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:33.749 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:33.749 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:33.749 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:33.750 - [任务 6(100)][88d5c841-5ff6-43b1-a35e-89a1f86759a3] - Node 88d5c841-5ff6-43b1-a35e-89a1f86759a3[88d5c841-5ff6-43b1-a35e-89a1f86759a3] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:33.750 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:33.750 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:33.750 - [任务 6(100)][88d5c841-5ff6-43b1-a35e-89a1f86759a3] - Node 88d5c841-5ff6-43b1-a35e-89a1f86759a3[88d5c841-5ff6-43b1-a35e-89a1f86759a3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:33.750 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:33.750 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:33.750 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:33.900 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:33.900 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:33.900 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:33.900 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:22:33.900 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.900 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:22:33.901 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 1 ms 
[INFO ] 2024-07-15 15:22:33.903 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:33.903 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-c7486b41-9c29-429c-b100-b62d7b3da554 
[INFO ] 2024-07-15 15:22:33.903 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:33.903 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-c7486b41-9c29-429c-b100-b62d7b3da554 
[INFO ] 2024-07-15 15:22:33.903 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.903 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.903 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:33.904 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 7 ms 
[INFO ] 2024-07-15 15:22:33.904 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:22:33.904 - [任务 6(100)][39f2539a-8e8d-4a73-b704-9277284a2b15] - Node 39f2539a-8e8d-4a73-b704-9277284a2b15[39f2539a-8e8d-4a73-b704-9277284a2b15] running status set to false 
[INFO ] 2024-07-15 15:22:33.904 - [任务 6(100)][39f2539a-8e8d-4a73-b704-9277284a2b15] - Node 39f2539a-8e8d-4a73-b704-9277284a2b15[39f2539a-8e8d-4a73-b704-9277284a2b15] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.905 - [任务 6(100)][39f2539a-8e8d-4a73-b704-9277284a2b15] - Node 39f2539a-8e8d-4a73-b704-9277284a2b15[39f2539a-8e8d-4a73-b704-9277284a2b15] monitor closed 
[INFO ] 2024-07-15 15:22:33.905 - [任务 6(100)][39f2539a-8e8d-4a73-b704-9277284a2b15] - Node 39f2539a-8e8d-4a73-b704-9277284a2b15[39f2539a-8e8d-4a73-b704-9277284a2b15] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:33.905 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-52639b72-9c2e-4f2b-bf13-cd34e325e95a 
[INFO ] 2024-07-15 15:22:33.906 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.906 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-52639b72-9c2e-4f2b-bf13-cd34e325e95a 
[INFO ] 2024-07-15 15:22:33.906 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.906 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:33.907 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 8 ms 
[INFO ] 2024-07-15 15:22:33.907 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:22:33.908 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:22:33.908 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 3 ms 
[INFO ] 2024-07-15 15:22:33.908 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:33.908 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:34.114 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:34.134 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:34.134 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:22:34.342 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:22:36.680 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:36.681 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:22:36.681 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:22:36.681 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:22:36.681 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:36.687 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:22:36.690 - [任务 6(100)][88d5c841-5ff6-43b1-a35e-89a1f86759a3] - Node 88d5c841-5ff6-43b1-a35e-89a1f86759a3[88d5c841-5ff6-43b1-a35e-89a1f86759a3] running status set to false 
[INFO ] 2024-07-15 15:22:36.690 - [任务 6(100)][88d5c841-5ff6-43b1-a35e-89a1f86759a3] - Node 88d5c841-5ff6-43b1-a35e-89a1f86759a3[88d5c841-5ff6-43b1-a35e-89a1f86759a3] schema data cleaned 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][88d5c841-5ff6-43b1-a35e-89a1f86759a3] - Node 88d5c841-5ff6-43b1-a35e-89a1f86759a3[88d5c841-5ff6-43b1-a35e-89a1f86759a3] monitor closed 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-fe1e1a6d-4c60-4324-ada1-cf6f76667921 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-fe1e1a6d-4c60-4324-ada1-cf6f76667921 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][88d5c841-5ff6-43b1-a35e-89a1f86759a3] - Node 88d5c841-5ff6-43b1-a35e-89a1f86759a3[88d5c841-5ff6-43b1-a35e-89a1f86759a3] close complete, cost 2 ms 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-91795a80-578d-4078-a31a-db919537b041 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-91795a80-578d-4078-a31a-db919537b041 
[INFO ] 2024-07-15 15:22:36.691 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:36.701 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:36.702 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:36.702 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 25 ms 
[INFO ] 2024-07-15 15:22:36.703 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:22:36.703 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:22:36.707 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 16 ms 
[INFO ] 2024-07-15 15:22:36.707 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:36.707 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:36.708 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:52.139 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:52.139 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:52.139 - [任务 6(100)][0804013a-dbfe-435d-b12b-8dbca2dd0b09] - Node 0804013a-dbfe-435d-b12b-8dbca2dd0b09[0804013a-dbfe-435d-b12b-8dbca2dd0b09] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:52.140 - [任务 6(100)][0804013a-dbfe-435d-b12b-8dbca2dd0b09] - Node 0804013a-dbfe-435d-b12b-8dbca2dd0b09[0804013a-dbfe-435d-b12b-8dbca2dd0b09] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:22:52.140 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:52.140 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 15:22:52.508 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:52.522 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:52.522 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:52.523 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:52.523 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:52.534 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 17 ms 
[INFO ] 2024-07-15 15:22:52.534 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:52.542 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:52.542 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-38c55c79-145c-4aea-b0a1-e61475faf1a0 
[INFO ] 2024-07-15 15:22:52.542 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-38c55c79-145c-4aea-b0a1-e61475faf1a0 
[INFO ] 2024-07-15 15:22:52.542 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:52.542 - [任务 6(100)][0804013a-dbfe-435d-b12b-8dbca2dd0b09] - Node 0804013a-dbfe-435d-b12b-8dbca2dd0b09[0804013a-dbfe-435d-b12b-8dbca2dd0b09] running status set to false 
[INFO ] 2024-07-15 15:22:52.542 - [任务 6(100)][0804013a-dbfe-435d-b12b-8dbca2dd0b09] - Node 0804013a-dbfe-435d-b12b-8dbca2dd0b09[0804013a-dbfe-435d-b12b-8dbca2dd0b09] schema data cleaned 
[INFO ] 2024-07-15 15:22:52.543 - [任务 6(100)][0804013a-dbfe-435d-b12b-8dbca2dd0b09] - Node 0804013a-dbfe-435d-b12b-8dbca2dd0b09[0804013a-dbfe-435d-b12b-8dbca2dd0b09] monitor closed 
[INFO ] 2024-07-15 15:22:52.544 - [任务 6(100)][0804013a-dbfe-435d-b12b-8dbca2dd0b09] - Node 0804013a-dbfe-435d-b12b-8dbca2dd0b09[0804013a-dbfe-435d-b12b-8dbca2dd0b09] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:52.544 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:52.544 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:52.544 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 6 ms 
[INFO ] 2024-07-15 15:22:52.545 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:52.545 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:52.546 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:52.601 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:52.601 - [任务 6(100)][efe37b1e-4372-4d2d-8082-6273ebf836f5] - Node efe37b1e-4372-4d2d-8082-6273ebf836f5[efe37b1e-4372-4d2d-8082-6273ebf836f5] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:52.601 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:52.601 - [任务 6(100)][efe37b1e-4372-4d2d-8082-6273ebf836f5] - Node efe37b1e-4372-4d2d-8082-6273ebf836f5[efe37b1e-4372-4d2d-8082-6273ebf836f5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:52.602 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:52.602 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:52.602 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:52.602 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:52.602 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:52.602 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:52.728 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:52.728 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:52.728 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:52.728 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:52.728 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:52.931 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 3 ms 
[INFO ] 2024-07-15 15:22:53.013 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:53.013 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:22:53.218 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:22:55.574 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][efe37b1e-4372-4d2d-8082-6273ebf836f5] - Node efe37b1e-4372-4d2d-8082-6273ebf836f5[efe37b1e-4372-4d2d-8082-6273ebf836f5] running status set to false 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][efe37b1e-4372-4d2d-8082-6273ebf836f5] - Node efe37b1e-4372-4d2d-8082-6273ebf836f5[efe37b1e-4372-4d2d-8082-6273ebf836f5] schema data cleaned 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][efe37b1e-4372-4d2d-8082-6273ebf836f5] - Node efe37b1e-4372-4d2d-8082-6273ebf836f5[efe37b1e-4372-4d2d-8082-6273ebf836f5] monitor closed 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][efe37b1e-4372-4d2d-8082-6273ebf836f5] - Node efe37b1e-4372-4d2d-8082-6273ebf836f5[efe37b1e-4372-4d2d-8082-6273ebf836f5] close complete, cost 6 ms 
[INFO ] 2024-07-15 15:22:55.577 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 13 ms 
[INFO ] 2024-07-15 15:22:55.585 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-96987f70-9856-4d9e-b55d-e90932a557e2 
[INFO ] 2024-07-15 15:22:55.585 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-33aa9462-7a51-4fb3-8fae-6d3f7a84867b 
[INFO ] 2024-07-15 15:22:55.585 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-96987f70-9856-4d9e-b55d-e90932a557e2 
[INFO ] 2024-07-15 15:22:55.585 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-33aa9462-7a51-4fb3-8fae-6d3f7a84867b 
[INFO ] 2024-07-15 15:22:55.586 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:55.586 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:55.595 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:55.595 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:55.595 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 39 ms 
[INFO ] 2024-07-15 15:22:55.596 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:22:55.597 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:22:55.597 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 27 ms 
[INFO ] 2024-07-15 15:22:55.599 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:55.599 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:55.600 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:58.488 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:58.489 - [任务 6(100)][5e0c9787-93e9-4954-96c7-bd2893917535] - Node 5e0c9787-93e9-4954-96c7-bd2893917535[5e0c9787-93e9-4954-96c7-bd2893917535] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:58.489 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:58.490 - [任务 6(100)][5e0c9787-93e9-4954-96c7-bd2893917535] - Node 5e0c9787-93e9-4954-96c7-bd2893917535[5e0c9787-93e9-4954-96c7-bd2893917535] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.490 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.490 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.772 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:58.782 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:58.782 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:58.783 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:58.783 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:58.806 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 11 ms 
[INFO ] 2024-07-15 15:22:58.806 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:58.814 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:22:58.815 - [任务 6(100)][5e0c9787-93e9-4954-96c7-bd2893917535] - Node 5e0c9787-93e9-4954-96c7-bd2893917535[5e0c9787-93e9-4954-96c7-bd2893917535] running status set to false 
[INFO ] 2024-07-15 15:22:58.815 - [任务 6(100)][5e0c9787-93e9-4954-96c7-bd2893917535] - Node 5e0c9787-93e9-4954-96c7-bd2893917535[5e0c9787-93e9-4954-96c7-bd2893917535] schema data cleaned 
[INFO ] 2024-07-15 15:22:58.815 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-49a14491-0b59-48aa-ba1d-ab3f282809b1 
[INFO ] 2024-07-15 15:22:58.815 - [任务 6(100)][5e0c9787-93e9-4954-96c7-bd2893917535] - Node 5e0c9787-93e9-4954-96c7-bd2893917535[5e0c9787-93e9-4954-96c7-bd2893917535] monitor closed 
[INFO ] 2024-07-15 15:22:58.815 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-49a14491-0b59-48aa-ba1d-ab3f282809b1 
[INFO ] 2024-07-15 15:22:58.815 - [任务 6(100)][5e0c9787-93e9-4954-96c7-bd2893917535] - Node 5e0c9787-93e9-4954-96c7-bd2893917535[5e0c9787-93e9-4954-96c7-bd2893917535] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.815 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:22:58.816 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:22:58.816 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:22:58.818 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 6 ms 
[INFO ] 2024-07-15 15:22:58.818 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:22:58.818 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:22:58.887 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:22:58.887 - [任务 6(100)][91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] - Node 91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae[91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:58.887 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:22:58.887 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:58.887 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:58.888 - [任务 6(100)][91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] - Node 91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae[91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.888 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:22:58.888 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.888 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.888 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:58.888 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:22:59.039 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:22:59.041 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:59.042 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:22:59.042 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:22:59.042 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:22:59.042 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 4 ms 
[INFO ] 2024-07-15 15:22:59.276 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:22:59.284 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:22:59.284 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:23:01.824 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:23:01.825 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:23:01.833 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:23:01.834 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:23:01.844 - [任务 6(100)][91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] - Node 91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae[91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] running status set to false 
[INFO ] 2024-07-15 15:23:01.844 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-479ef762-2693-4ec7-9d9c-02f004e8510a 
[INFO ] 2024-07-15 15:23:01.844 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:23:01.844 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-c0c42ba5-3c35-4b24-b3b3-e37bc865eca0 
[INFO ] 2024-07-15 15:23:01.852 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 21 ms 
[INFO ] 2024-07-15 15:23:01.853 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-479ef762-2693-4ec7-9d9c-02f004e8510a 
[INFO ] 2024-07-15 15:23:01.854 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-c0c42ba5-3c35-4b24-b3b3-e37bc865eca0 
[INFO ] 2024-07-15 15:23:01.856 - [任务 6(100)][91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] - Node 91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae[91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] schema data cleaned 
[INFO ] 2024-07-15 15:23:01.856 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:23:01.856 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:23:01.857 - [任务 6(100)][91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] - Node 91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae[91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] monitor closed 
[INFO ] 2024-07-15 15:23:01.862 - [任务 6(100)][91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] - Node 91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae[91f76fd6-3d61-46d8-bb9f-cf8a699cb7ae] close complete, cost 23 ms 
[INFO ] 2024-07-15 15:23:01.862 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:23:01.862 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:23:01.862 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:23:01.862 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:23:01.863 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 40 ms 
[INFO ] 2024-07-15 15:23:01.863 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 34 ms 
[INFO ] 2024-07-15 15:23:01.865 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:23:01.866 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:23:01.866 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:24:28.974 - [任务 6(100)][6080416a-cdc4-45b6-ba04-e1f869fc5a44] - Node 6080416a-cdc4-45b6-ba04-e1f869fc5a44[6080416a-cdc4-45b6-ba04-e1f869fc5a44] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:24:28.974 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:24:28.975 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:24:28.975 - [任务 6(100)][6080416a-cdc4-45b6-ba04-e1f869fc5a44] - Node 6080416a-cdc4-45b6-ba04-e1f869fc5a44[6080416a-cdc4-45b6-ba04-e1f869fc5a44] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:28.975 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:28.975 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:29.478 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:24:29.478 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:24:29.479 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:24:29.488 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:24:29.490 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:24:29.510 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 22 ms 
[INFO ] 2024-07-15 15:24:29.510 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:24:29.518 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:24:29.519 - [任务 6(100)][6080416a-cdc4-45b6-ba04-e1f869fc5a44] - Node 6080416a-cdc4-45b6-ba04-e1f869fc5a44[6080416a-cdc4-45b6-ba04-e1f869fc5a44] running status set to false 
[INFO ] 2024-07-15 15:24:29.519 - [任务 6(100)][6080416a-cdc4-45b6-ba04-e1f869fc5a44] - Node 6080416a-cdc4-45b6-ba04-e1f869fc5a44[6080416a-cdc4-45b6-ba04-e1f869fc5a44] schema data cleaned 
[INFO ] 2024-07-15 15:24:29.519 - [任务 6(100)][6080416a-cdc4-45b6-ba04-e1f869fc5a44] - Node 6080416a-cdc4-45b6-ba04-e1f869fc5a44[6080416a-cdc4-45b6-ba04-e1f869fc5a44] monitor closed 
[INFO ] 2024-07-15 15:24:29.519 - [任务 6(100)][6080416a-cdc4-45b6-ba04-e1f869fc5a44] - Node 6080416a-cdc4-45b6-ba04-e1f869fc5a44[6080416a-cdc4-45b6-ba04-e1f869fc5a44] close complete, cost 1 ms 
[INFO ] 2024-07-15 15:24:29.522 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-99911034-0bea-48d9-bbc9-37d728a2ff3b 
[INFO ] 2024-07-15 15:24:29.522 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-99911034-0bea-48d9-bbc9-37d728a2ff3b 
[INFO ] 2024-07-15 15:24:29.522 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:24:29.524 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:24:29.524 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:24:29.526 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 7 ms 
[INFO ] 2024-07-15 15:24:29.526 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:24:29.527 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:24:29.527 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:24:29.578 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:24:29.578 - [任务 6(100)][e4ad8ce2-fd47-4391-9f7b-08753f6781ed] - Node e4ad8ce2-fd47-4391-9f7b-08753f6781ed[e4ad8ce2-fd47-4391-9f7b-08753f6781ed] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:24:29.578 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:24:29.578 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:24:29.579 - [任务 6(100)][e4ad8ce2-fd47-4391-9f7b-08753f6781ed] - Node e4ad8ce2-fd47-4391-9f7b-08753f6781ed[e4ad8ce2-fd47-4391-9f7b-08753f6781ed] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:29.579 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:24:29.579 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:29.579 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:29.579 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:29.579 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:24:29.703 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:24:29.703 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:24:29.703 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:24:29.703 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:24:29.703 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:24:29.802 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 3 ms 
[INFO ] 2024-07-15 15:24:29.803 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:24:29.808 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:24:29.809 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:24:32.338 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:24:32.338 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-197064a6-8f06-42f8-8e0d-19a33df4b13f 
[INFO ] 2024-07-15 15:24:32.338 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-197064a6-8f06-42f8-8e0d-19a33df4b13f 
[INFO ] 2024-07-15 15:24:32.338 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:24:32.339 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:24:32.339 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:24:32.341 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 3 ms 
[INFO ] 2024-07-15 15:24:32.341 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:24:32.341 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:24:32.341 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:24:32.341 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:24:32.346 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:24:32.348 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-f48b53fc-0b50-42d2-9a55-47ffc2d0eff4 
[INFO ] 2024-07-15 15:24:32.348 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-f48b53fc-0b50-42d2-9a55-47ffc2d0eff4 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 2 ms 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][e4ad8ce2-fd47-4391-9f7b-08753f6781ed] - Node e4ad8ce2-fd47-4391-9f7b-08753f6781ed[e4ad8ce2-fd47-4391-9f7b-08753f6781ed] running status set to false 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][e4ad8ce2-fd47-4391-9f7b-08753f6781ed] - Node e4ad8ce2-fd47-4391-9f7b-08753f6781ed[e4ad8ce2-fd47-4391-9f7b-08753f6781ed] schema data cleaned 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][e4ad8ce2-fd47-4391-9f7b-08753f6781ed] - Node e4ad8ce2-fd47-4391-9f7b-08753f6781ed[e4ad8ce2-fd47-4391-9f7b-08753f6781ed] monitor closed 
[INFO ] 2024-07-15 15:24:32.349 - [任务 6(100)][e4ad8ce2-fd47-4391-9f7b-08753f6781ed] - Node e4ad8ce2-fd47-4391-9f7b-08753f6781ed[e4ad8ce2-fd47-4391-9f7b-08753f6781ed] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:24:32.350 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:24:32.350 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:24:32.375 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:26:04.545 - [任务 6(100)][0a2612d6-7da6-428b-8bd0-a2b75965a50e] - Node 0a2612d6-7da6-428b-8bd0-a2b75965a50e[0a2612d6-7da6-428b-8bd0-a2b75965a50e] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:26:04.548 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:26:04.548 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:26:04.548 - [任务 6(100)][0a2612d6-7da6-428b-8bd0-a2b75965a50e] - Node 0a2612d6-7da6-428b-8bd0-a2b75965a50e[0a2612d6-7da6-428b-8bd0-a2b75965a50e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:04.548 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:04.548 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:04.942 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:26:04.942 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:26:04.943 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:26:04.943 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:26:04.943 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:26:04.943 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 3 ms 
[INFO ] 2024-07-15 15:26:04.966 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:26:04.966 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:26:04.969 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-b94603a6-661d-483f-954d-2f326152e94f 
[INFO ] 2024-07-15 15:26:04.969 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-b94603a6-661d-483f-954d-2f326152e94f 
[INFO ] 2024-07-15 15:26:04.969 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:26:04.970 - [任务 6(100)][0a2612d6-7da6-428b-8bd0-a2b75965a50e] - Node 0a2612d6-7da6-428b-8bd0-a2b75965a50e[0a2612d6-7da6-428b-8bd0-a2b75965a50e] running status set to false 
[INFO ] 2024-07-15 15:26:04.970 - [任务 6(100)][0a2612d6-7da6-428b-8bd0-a2b75965a50e] - Node 0a2612d6-7da6-428b-8bd0-a2b75965a50e[0a2612d6-7da6-428b-8bd0-a2b75965a50e] schema data cleaned 
[INFO ] 2024-07-15 15:26:04.970 - [任务 6(100)][0a2612d6-7da6-428b-8bd0-a2b75965a50e] - Node 0a2612d6-7da6-428b-8bd0-a2b75965a50e[0a2612d6-7da6-428b-8bd0-a2b75965a50e] monitor closed 
[INFO ] 2024-07-15 15:26:04.970 - [任务 6(100)][0a2612d6-7da6-428b-8bd0-a2b75965a50e] - Node 0a2612d6-7da6-428b-8bd0-a2b75965a50e[0a2612d6-7da6-428b-8bd0-a2b75965a50e] close complete, cost 0 ms 
[INFO ] 2024-07-15 15:26:04.971 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:26:04.971 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:26:04.972 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 5 ms 
[INFO ] 2024-07-15 15:26:04.972 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:26:04.972 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] start preload schema,table counts: 1 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:05.022 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:05.023 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:05.023 - [任务 6(100)][55eb347a-660c-40f5-8982-fab3bbb17de3] - Node 55eb347a-660c-40f5-8982-fab3bbb17de3[55eb347a-660c-40f5-8982-fab3bbb17de3] start preload schema,table counts: 0 
[INFO ] 2024-07-15 15:26:05.219 - [任务 6(100)][55eb347a-660c-40f5-8982-fab3bbb17de3] - Node 55eb347a-660c-40f5-8982-fab3bbb17de3[55eb347a-660c-40f5-8982-fab3bbb17de3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 15:26:05.223 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] running status set to false 
[INFO ] 2024-07-15 15:26:05.223 - [任务 6(100)][AutoTestMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:26:05.223 - [任务 6(100)][AutoTestMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2b45497b-76d1-475c-89aa-334064644dd7 
[INFO ] 2024-07-15 15:26:05.223 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] schema data cleaned 
[INFO ] 2024-07-15 15:26:05.223 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] monitor closed 
[INFO ] 2024-07-15 15:26:05.223 - [任务 6(100)][AutoTestMongo] - Node AutoTestMongo[2b45497b-76d1-475c-89aa-334064644dd7] close complete, cost 4 ms 
[INFO ] 2024-07-15 15:26:05.272 - [任务 6(100)][增强JS] - Document{{_id=65227fbbfb245ac2a2718fec, SETTLED_DATE=DateTime nano 0 seconds 1424448000 timeZone null, CLAIM_ID=CL_000000016, SETTLED_AMOUNT=4063.0, CLAIM_REASON=HAIL, POLICY_ID=PC_000000010, CLAIM_DATE=2014-10-03T16:00:00.000Z, LAST_CHANGE=DateTime nano 997000000 seconds 1562231622 timeZone null, CLAIM_AMOUNT=4063.0}} 
[INFO ] 2024-07-15 15:26:05.273 - [任务 6(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-15 15:26:05.476 - [任务 6(100)][增强JS] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.transformToTapValue(HazelcastBaseNode.java:345)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$null$4(HazelcastProcessorBaseNode.java:247)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 25 more
Caused by: java.lang.RuntimeException: Table name "ReplicationTimeCalculation" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 27 more

[INFO ] 2024-07-15 15:26:07.850 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] running status set to false 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][55eb347a-660c-40f5-8982-fab3bbb17de3] - Node 55eb347a-660c-40f5-8982-fab3bbb17de3[55eb347a-660c-40f5-8982-fab3bbb17de3] running status set to false 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] running status set to false 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] running status set to false 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] schema data cleaned 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][55eb347a-660c-40f5-8982-fab3bbb17de3] - Node 55eb347a-660c-40f5-8982-fab3bbb17de3[55eb347a-660c-40f5-8982-fab3bbb17de3] schema data cleaned 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] monitor closed 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][55eb347a-660c-40f5-8982-fab3bbb17de3] - Node 55eb347a-660c-40f5-8982-fab3bbb17de3[55eb347a-660c-40f5-8982-fab3bbb17de3] monitor closed 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][时间运算] - Node 时间运算[7be1051f-84b8-441b-9d2a-70c67573907f] close complete, cost 12 ms 
[INFO ] 2024-07-15 15:26:07.852 - [任务 6(100)][55eb347a-660c-40f5-8982-fab3bbb17de3] - Node 55eb347a-660c-40f5-8982-fab3bbb17de3[55eb347a-660c-40f5-8982-fab3bbb17de3] close complete, cost 5 ms 
[INFO ] 2024-07-15 15:26:07.857 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-4e1ac726-68d8-4baa-9d95-d65387c64547 
[INFO ] 2024-07-15 15:26:07.857 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-AutoTestMongo-0e048683-3b9b-46f4-b894-e43f334ed68f 
[INFO ] 2024-07-15 15:26:07.857 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-4e1ac726-68d8-4baa-9d95-d65387c64547 
[INFO ] 2024-07-15 15:26:07.857 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-AutoTestMongo-0e048683-3b9b-46f4-b894-e43f334ed68f 
[INFO ] 2024-07-15 15:26:07.857 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-0ddafe94-66f6-458f-889a-92abc4425988-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:26:07.858 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-6694bfd89ead3832b55ffd8f-d375461c-3049-493c-aae2-56ce5379d596-6694a0a91df4b966216a4fff] schema data cleaned 
[INFO ] 2024-07-15 15:26:07.865 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] schema data cleaned 
[INFO ] 2024-07-15 15:26:07.865 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] monitor closed 
[INFO ] 2024-07-15 15:26:07.865 - [任务 6(100)][增强JS] - Node 增强JS[0ddafe94-66f6-458f-889a-92abc4425988] close complete, cost 38 ms 
[INFO ] 2024-07-15 15:26:07.865 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] schema data cleaned 
[INFO ] 2024-07-15 15:26:07.865 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] monitor closed 
[INFO ] 2024-07-15 15:26:07.865 - [任务 6(100)][增强JS] - Node 增强JS[d375461c-3049-493c-aae2-56ce5379d596] close complete, cost 28 ms 
[INFO ] 2024-07-15 15:26:07.867 - [任务 6(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-15 15:26:07.867 - [任务 6(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-15 15:26:07.935 - [任务 6(100)] - Stopped task aspect(s) 
