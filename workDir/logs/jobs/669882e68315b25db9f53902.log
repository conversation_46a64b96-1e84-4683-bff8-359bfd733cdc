[INFO ] 2024-07-18 10:50:16.109 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Start task milestones: 669882e68315b25db9f53902(Heartbeat-qa_mysql_repl_33306_1717403468657_3537) 
[INFO ] 2024-07-18 10:50:16.267 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:50:16.634 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - The engine receives Heartbeat-qa_mysql_repl_33306_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:50:16.634 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[82145821-ca29-4bc0-9c50-276fc7da1a06] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:50:16.634 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de64fc4f-2539-4983-a7f9-e7bfaa3d2207] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:50:16.634 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[82145821-ca29-4bc0-9c50-276fc7da1a06] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:50:16.635 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de64fc4f-2539-4983-a7f9-e7bfaa3d2207] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:50:17.288 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 10:50:17.289 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 10:50:17.290 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:50:17.291 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721271017287,"lastTimes":1721271017287,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:50:17.625 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-18 10:50:17.653 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 10:50:17.653 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 10:50:17.660 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:50:17.660 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-18 10:50:17.662 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721271017287,"lastTimes":1721271017287,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:50:17.662 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 10:50:17.850 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 10:50:17.850 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:50:17.967 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Table "t0._tapdata_heartbeat_table" exists, skip auto create table 
[INFO ] 2024-07-18 12:33:25.249 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[82145821-ca29-4bc0-9c50-276fc7da1a06] running status set to false 
[INFO ] 2024-07-18 12:33:25.249 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-18 12:33:25.258 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-82145821-ca29-4bc0-9c50-276fc7da1a06 
[INFO ] 2024-07-18 12:33:25.258 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-82145821-ca29-4bc0-9c50-276fc7da1a06 
[INFO ] 2024-07-18 12:33:25.258 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[82145821-ca29-4bc0-9c50-276fc7da1a06] schema data cleaned 
[INFO ] 2024-07-18 12:33:25.258 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[82145821-ca29-4bc0-9c50-276fc7da1a06] monitor closed 
[INFO ] 2024-07-18 12:33:25.259 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[82145821-ca29-4bc0-9c50-276fc7da1a06] close complete, cost 14 ms 
[INFO ] 2024-07-18 12:33:25.259 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de64fc4f-2539-4983-a7f9-e7bfaa3d2207] running status set to false 
[INFO ] 2024-07-18 12:33:25.279 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-de64fc4f-2539-4983-a7f9-e7bfaa3d2207 
[INFO ] 2024-07-18 12:33:25.279 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-de64fc4f-2539-4983-a7f9-e7bfaa3d2207 
[INFO ] 2024-07-18 12:33:25.279 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de64fc4f-2539-4983-a7f9-e7bfaa3d2207] schema data cleaned 
[INFO ] 2024-07-18 12:33:25.279 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de64fc4f-2539-4983-a7f9-e7bfaa3d2207] monitor closed 
[INFO ] 2024-07-18 12:33:25.279 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de64fc4f-2539-4983-a7f9-e7bfaa3d2207] close complete, cost 20 ms 
[INFO ] 2024-07-18 12:33:29.634 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:33:29.740 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@236326 
[INFO ] 2024-07-18 12:33:29.740 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Stop task milestones: 669882e68315b25db9f53902(Heartbeat-qa_mysql_repl_33306_1717403468657_3537)  
[INFO ] 2024-07-18 12:33:29.767 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:33:29.767 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:33:29.810 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Remove memory task client succeed, task: Heartbeat-qa_mysql_repl_33306_1717403468657_3537[669882e68315b25db9f53902] 
[INFO ] 2024-07-18 12:33:29.810 - [Heartbeat-qa_mysql_repl_33306_1717403468657_3537] - Destroy memory task client cache succeed, task: Heartbeat-qa_mysql_repl_33306_1717403468657_3537[669882e68315b25db9f53902] 
