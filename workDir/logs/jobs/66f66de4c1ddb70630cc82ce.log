[INFO ] 2024-09-27 16:34:07.085 - [任务 7] - Task initialization... 
[INFO ] 2024-09-27 16:34:07.086 - [任务 7] - Start task milestones: 66f66de4c1ddb70630cc82ce(任务 7) 
[INFO ] 2024-09-27 16:34:07.262 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:34:07.262 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:34:07.314 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:34:07.317 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:34:07.317 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:34:07.317 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:34:08.173 - [任务 7][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:34:08.173 - [任务 7][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 16:34:08.215 - [任务 7][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:34:08.215 - [任务 7][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:34:08.216 - [任务 7][local3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 16:34:08.221 - [任务 7][local3307] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:34:08.277 - [任务 7][local3307] - Initial sync started 
[INFO ] 2024-09-27 16:34:08.278 - [任务 7][local3307] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-09-27 16:34:08.279 - [任务 7][local3307] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-09-27 16:34:08.431 - [任务 7][local3307] - Query table 'BMSQL_CUSTOMER' counts: 100000 
[INFO ] 2024-09-27 16:36:04.676 - [任务 7][local3307] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 16:36:04.677 - [任务 7][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:36:04.677 - [任务 7][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:36:04.677 - [任务 7][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:36:04.732 - [任务 7][local3307] - Starting stream read, table list: [BMSQL_CUSTOMER], offset: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:36:04.733 - [任务 7][local3307] - Starting mysql cdc, server name: a5c48e14-87fa-4fc6-a58d-c9713548a2cd 
[INFO ] 2024-09-27 16:36:04.810 - [任务 7][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a5c48e14-87fa-4fc6-a58d-c9713548a2cd","offset":{"{\"server\":\"a5c48e14-87fa-4fc6-a58d-c9713548a2cd\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1078984881
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a5c48e14-87fa-4fc6-a58d-c9713548a2cd
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-a5c48e14-87fa-4fc6-a58d-c9713548a2cd
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a5c48e14-87fa-4fc6-a58d-c9713548a2cd
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_CUSTOMER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:36:04.810 - [任务 7][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-09-27 17:13:33.036 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] running status set to false 
[INFO ] 2024-09-27 17:13:33.280 - [任务 7][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-6c2eb156-0b9d-4d54-8b8b-7226452382d7 
[INFO ] 2024-09-27 17:13:33.282 - [任务 7][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-6c2eb156-0b9d-4d54-8b8b-7226452382d7 
[INFO ] 2024-09-27 17:13:33.282 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.283 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] monitor closed 
[INFO ] 2024-09-27 17:13:33.286 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] close complete, cost 271 ms 
[INFO ] 2024-09-27 17:13:33.286 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] running status set to false 
[INFO ] 2024-09-27 17:13:33.345 - [任务 7][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-542d0e31-1ea5-46c9-8c14-be9ebe8cfcad 
[INFO ] 2024-09-27 17:13:33.345 - [任务 7][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-542d0e31-1ea5-46c9-8c14-be9ebe8cfcad 
[INFO ] 2024-09-27 17:13:33.345 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.345 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] monitor closed 
[INFO ] 2024-09-27 17:13:33.347 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] close complete, cost 61 ms 
[INFO ] 2024-09-27 17:13:37.102 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 17:13:37.216 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@62c59206 
[INFO ] 2024-09-27 17:13:37.217 - [任务 7] - Stop task milestones: 66f66de4c1ddb70630cc82ce(任务 7)  
[INFO ] 2024-09-27 17:13:37.226 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-09-27 17:13:37.226 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 17:13:37.243 - [任务 7] - Remove memory task client succeed, task: 任务 7[66f66de4c1ddb70630cc82ce] 
[INFO ] 2024-09-27 17:13:37.246 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[66f66de4c1ddb70630cc82ce] 
[INFO ] 2024-09-27 17:16:09.037 - [任务 7] - Task initialization... 
[INFO ] 2024-09-27 17:16:09.136 - [任务 7] - Start task milestones: 66f66de4c1ddb70630cc82ce(任务 7) 
[INFO ] 2024-09-27 17:16:09.401 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:16:09.697 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:16:09.845 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.845 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.845 - [任务 7][SourceMongo] - Node SourceMongo[542d0e31-1ea5-46c9-8c14-be9ebe8cfcad] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:16:09.845 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 17:16:10.327 - [任务 7][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 17:16:10.337 - [任务 7][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 17:16:10.811 - [任务 7][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 17:16:10.812 - [任务 7][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 17:16:10.812 - [任务 7][local3307] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 17:16:10.872 - [任务 7][local3307] - batch offset found: {"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"a5c48e14-87fa-4fc6-a58d-c9713548a2cd","offset":{"{\"server\":\"a5c48e14-87fa-4fc6-a58d-c9713548a2cd\"}":"{\"ts_sec\":1727426164,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:10.919 - [任务 7][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 17:16:10.919 - [任务 7][local3307] - Initial sync completed 
[INFO ] 2024-09-27 17:16:10.922 - [任务 7][local3307] - Starting stream read, table list: [BMSQL_CUSTOMER], offset: {"name":"a5c48e14-87fa-4fc6-a58d-c9713548a2cd","offset":{"{\"server\":\"a5c48e14-87fa-4fc6-a58d-c9713548a2cd\"}":"{\"ts_sec\":1727426164,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:11.008 - [任务 7][local3307] - Starting mysql cdc, server name: a5c48e14-87fa-4fc6-a58d-c9713548a2cd 
[INFO ] 2024-09-27 17:16:11.105 - [任务 7][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a5c48e14-87fa-4fc6-a58d-c9713548a2cd","offset":{"{\"server\":\"a5c48e14-87fa-4fc6-a58d-c9713548a2cd\"}":"{\"ts_sec\":1727426164,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1103745240
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a5c48e14-87fa-4fc6-a58d-c9713548a2cd
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-a5c48e14-87fa-4fc6-a58d-c9713548a2cd
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a5c48e14-87fa-4fc6-a58d-c9713548a2cd
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_CUSTOMER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 17:16:12.843 - [任务 7][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-09-27 18:53:28.741 - [任务 7][local3307] - Node local3307[6c2eb156-0b9d-4d54-8b8b-7226452382d7] running status set to false 
