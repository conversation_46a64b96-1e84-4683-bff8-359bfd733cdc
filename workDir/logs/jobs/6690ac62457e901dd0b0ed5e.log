[INFO ] 2024-07-15 03:35:34.392 - [来自SourceMysqlTestHeartBeat的共享挖掘任务] - Start task milestones: 6690ac62457e901dd0b0ed5e(来自SourceMysqlTestHeartBeat的共享挖掘任务) 
[INFO ] 2024-07-15 03:35:34.520 - [来自SourceMysqlTestHeartBeat的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 03:35:34.557 - [来自SourceMysqlTestHeartBeat的共享挖掘任务] - The engine receives 来自SourceMysqlTestHeartBeat的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 03:35:34.661 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[0701830f0ce24b94a0ca6b3628b0988c] start preload schema,table counts: 2 
[INFO ] 2024-07-15 03:35:34.678 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[0701830f0ce24b94a0ca6b3628b0988c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 03:35:34.688 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 03:35:34.713 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 03:35:34.714 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac6266ab5ede8a624f21, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_933891356, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-15 03:35:34.740 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_933891356', head seq: 0, tail seq: 696 
[INFO ] 2024-07-15 03:35:34.775 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac9266ab5ede8a6260cc, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_2072557188, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-15 03:35:35.166 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_2072557188', head seq: 0, tail seq: 25045 
[INFO ] 2024-07-15 03:35:35.181 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 03:35:36.045 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Source node "SourceMysqlTestHeartBeat" read batch size: 2000 
[INFO ] 2024-07-15 03:35:36.045 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Source node "SourceMysqlTestHeartBeat" event queue capacity: 4000 
[INFO ] 2024-07-15 03:35:36.056 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 03:35:36.073 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - batch offset found: {},stream offset found: {"name":"c2d5c534-4371-4a57-b2bd-7a22990cbeaa","offset":{"{\"server\":\"c2d5c534-4371-4a57-b2bd-7a22990cbeaa\"}":"{\"file\":\"binlog.000032\",\"pos\":111249526,\"server_id\":1}"}} 
[INFO ] 2024-07-15 03:35:36.073 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-15 03:35:36.138 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Starting stream read, table list: [_tapdata_heartbeat_table, POLICY], offset: {"name":"c2d5c534-4371-4a57-b2bd-7a22990cbeaa","offset":{"{\"server\":\"c2d5c534-4371-4a57-b2bd-7a22990cbeaa\"}":"{\"file\":\"binlog.000032\",\"pos\":111249526,\"server_id\":1}"}} 
[INFO ] 2024-07-15 03:35:36.138 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Starting mysql cdc, server name: c2d5c534-4371-4a57-b2bd-7a22990cbeaa 
[INFO ] 2024-07-15 03:35:36.343 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1149596248
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c2d5c534-4371-4a57-b2bd-7a22990cbeaa
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c2d5c534-4371-4a57-b2bd-7a22990cbeaa
  database.hostname: localhost
  database.password: ********
  name: c2d5c534-4371-4a57-b2bd-7a22990cbeaa
  pdk.offset.string: {"name":"c2d5c534-4371-4a57-b2bd-7a22990cbeaa","offset":{"{\"server\":\"c2d5c534-4371-4a57-b2bd-7a22990cbeaa\"}":"{\"file\":\"binlog.000032\",\"pos\":111249526,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test._tapdata_heartbeat_table,test.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-15 03:35:36.894 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Connector Mysql incremental start succeed, tables: [_tapdata_heartbeat_table, POLICY], data change syncing 
[INFO ] 2024-07-15 03:39:13.940 - [来自SourceMysqlTestHeartBeat的共享挖掘任务][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[0701830f0ce24b94a0ca6b3628b0988c] running status set to false 
