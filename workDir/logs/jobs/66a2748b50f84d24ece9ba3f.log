[INFO ] 2024-07-26 18:21:20.273 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 18:21:20.529 - [Heartbeat-test_jack_阿里云] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:21:20.529 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:21:20.640 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2463a2cc 
[INFO ] 2024-07-26 18:21:20.641 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 18:21:20.656 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:21:20.656 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 18:21:20.861 - [Heartbeat-test_jack_阿里云] - Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 18:21:53.716 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 18:22:01.623 - [Heartbeat-test_jack_阿里云] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:22:01.628 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:22:01.630 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57be608b 
[INFO ] 2024-07-26 18:22:01.775 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 18:22:01.775 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:22:01.775 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 18:22:01.786 - [Heartbeat-test_jack_阿里云] - Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 18:24:22.297 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 18:24:22.361 - [Heartbeat-test_jack_阿里云] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:24:22.361 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:24:22.362 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@446ffc75 
[INFO ] 2024-07-26 18:24:22.485 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 18:24:22.485 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:24:22.487 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 18:24:22.488 - [Heartbeat-test_jack_阿里云] - Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 18:24:32.037 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 18:32:36.979 - [Heartbeat-test_jack_阿里云] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:32:36.980 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:32:36.987 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@71a2db48 
[INFO ] 2024-07-26 18:32:37.098 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 18:32:37.108 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:32:37.109 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 18:32:37.314 - [Heartbeat-test_jack_阿里云] - Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 18:33:35.733 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 18:37:20.852 - [Heartbeat-test_jack_阿里云] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:37:20.853 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:37:20.857 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@22310f75 
[INFO ] 2024-07-26 18:37:20.991 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 18:37:20.992 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:37:20.994 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 18:37:20.994 - [Heartbeat-test_jack_阿里云] - Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 18:45:27.034 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 18:45:59.762 - [Heartbeat-test_jack_阿里云] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:45:59.763 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:45:59.766 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6b1d414e 
[INFO ] 2024-07-26 18:45:59.890 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 18:45:59.890 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:45:59.892 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[ERROR] 2024-07-26 18:45:59.893 - [Heartbeat-test_jack_阿里云] - Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id ca84e7e7-ec7a-45ae-8782-28a4e1362925, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-26 19:17:02.909 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 19:17:03.118 - [Heartbeat-test_jack_阿里云] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:17:03.322 - [Heartbeat-test_jack_阿里云] - The engine receives Heartbeat-test_jack_阿里云 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:17:03.412 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:17:03.427 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f 
[INFO ] 2024-07-26 19:17:03.427 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] start preload schema,table counts: 1 
[ERROR] 2024-07-26 19:17:03.444 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:568)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:222)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:214)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-07-26 19:17:03.445 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Job suspend in error handle 
[INFO ] 2024-07-26 19:17:03.446 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f 
[ERROR] 2024-07-26 19:17:03.470 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:568)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:222)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:214)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-07-26 19:17:03.471 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] running status set to false 
[INFO ] 2024-07-26 19:17:03.471 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node stopped: null 
[INFO ] 2024-07-26 19:17:03.471 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node released: null 
[INFO ] 2024-07-26 19:17:03.471 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] schema data cleaned 
[INFO ] 2024-07-26 19:17:03.473 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] monitor closed 
[INFO ] 2024-07-26 19:17:03.476 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] close complete, cost 4 ms 
[INFO ] 2024-07-26 19:17:03.476 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] running status set to false 
[INFO ] 2024-07-26 19:17:03.486 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node stopped: null 
[INFO ] 2024-07-26 19:17:03.487 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node released: null 
[INFO ] 2024-07-26 19:17:03.489 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] schema data cleaned 
[INFO ] 2024-07-26 19:17:03.490 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] monitor closed 
[INFO ] 2024-07-26 19:17:03.693 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] close complete, cost 15 ms 
[INFO ] 2024-07-26 19:17:03.693 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:17:03.694 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7cf81abe 
[INFO ] 2024-07-26 19:17:04.111 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 19:17:04.238 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:17:04.312 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:17:04.313 - [Heartbeat-test_jack_阿里云] - Remove memory task client succeed, task: Heartbeat-test_jack_阿里云[66a2748b50f84d24ece9ba3f] 
[INFO ] 2024-07-26 19:17:04.515 - [Heartbeat-test_jack_阿里云] - Destroy memory task client cache succeed, task: Heartbeat-test_jack_阿里云[66a2748b50f84d24ece9ba3f] 
[INFO ] 2024-07-26 19:18:07.127 - [Heartbeat-test_jack_阿里云] - Start task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云) 
[INFO ] 2024-07-26 19:18:07.259 - [Heartbeat-test_jack_阿里云] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:18:07.259 - [Heartbeat-test_jack_阿里云] - The engine receives Heartbeat-test_jack_阿里云 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:18:07.328 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:18:07.329 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:18:07.340 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f 
[INFO ] 2024-07-26 19:18:07.341 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f 
[ERROR] 2024-07-26 19:18:07.349 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:568)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:222)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:214)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_65bc42f68c5f8e2424291580_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-07-26 19:18:07.349 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Job suspend in error handle 
[ERROR] 2024-07-26 19:18:07.361 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:568)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:222)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:214)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a2747d50f84d24ece9ba38_66a2748b50f84d24ece9ba3f
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-07-26 19:18:07.362 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] running status set to false 
[INFO ] 2024-07-26 19:18:07.362 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node stopped: null 
[INFO ] 2024-07-26 19:18:07.362 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node released: null 
[INFO ] 2024-07-26 19:18:07.363 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] schema data cleaned 
[INFO ] 2024-07-26 19:18:07.365 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] monitor closed 
[INFO ] 2024-07-26 19:18:07.365 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[ca84e7e7-ec7a-45ae-8782-28a4e1362925] close complete, cost 7 ms 
[INFO ] 2024-07-26 19:18:07.368 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] running status set to false 
[INFO ] 2024-07-26 19:18:07.368 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node stopped: null 
[INFO ] 2024-07-26 19:18:07.368 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - PDK connector node released: null 
[INFO ] 2024-07-26 19:18:07.368 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] schema data cleaned 
[INFO ] 2024-07-26 19:18:07.370 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] monitor closed 
[INFO ] 2024-07-26 19:18:07.375 - [Heartbeat-test_jack_阿里云][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[402eb421-c8b1-4880-ad08-c5896b1a777b] close complete, cost 3 ms 
[INFO ] 2024-07-26 19:18:12.328 - [Heartbeat-test_jack_阿里云] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:18:12.335 - [Heartbeat-test_jack_阿里云] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5f3ae35d 
[INFO ] 2024-07-26 19:18:12.482 - [Heartbeat-test_jack_阿里云] - Stop task milestones: 66a2748b50f84d24ece9ba3f(Heartbeat-test_jack_阿里云)  
[INFO ] 2024-07-26 19:18:12.484 - [Heartbeat-test_jack_阿里云] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:18:12.484 - [Heartbeat-test_jack_阿里云] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:18:12.520 - [Heartbeat-test_jack_阿里云] - Remove memory task client succeed, task: Heartbeat-test_jack_阿里云[66a2748b50f84d24ece9ba3f] 
[INFO ] 2024-07-26 19:18:12.522 - [Heartbeat-test_jack_阿里云] - Destroy memory task client cache succeed, task: Heartbeat-test_jack_阿里云[66a2748b50f84d24ece9ba3f] 
