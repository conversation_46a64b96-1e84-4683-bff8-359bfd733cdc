[INFO ] 2024-07-17 18:36:29.995 - [Heartbeat-NewSource] - Start task milestones: 66979eadb92eda1a86f523a3(Heartbeat-NewSource) 
[INFO ] 2024-07-17 18:36:30.043 - [Heartbeat-NewSource] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:36:30.153 - [Heartbeat-NewSource] - The engine receives Heartbeat-NewSource task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 18:36:30.199 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[855ba62a-c1d9-4911-9f83-6f022c14a67b] start preload schema,table counts: 1 
[INFO ] 2024-07-17 18:36:30.199 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d0f04f4c-8c53-4089-a642-70827f232364] start preload schema,table counts: 1 
[INFO ] 2024-07-17 18:36:30.199 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[855ba62a-c1d9-4911-9f83-6f022c14a67b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 18:36:30.199 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d0f04f4c-8c53-4089-a642-70827f232364] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 18:36:30.832 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 18:36:30.948 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-17 18:36:30.948 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-17 18:36:30.948 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 18:36:31.101 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721212590948,"lastTimes":1721212590948,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-17 18:36:31.101 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-17 18:36:31.110 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-17 18:36:31.110 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-17 18:36:31.120 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 18:36:31.120 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-17 18:36:31.121 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721212590948,"lastTimes":1721212590948,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-17 18:36:31.121 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-17 18:36:31.325 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-17 19:08:50.810 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[855ba62a-c1d9-4911-9f83-6f022c14a67b] running status set to false 
[INFO ] 2024-07-17 19:08:50.820 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-17 19:08:50.820 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-855ba62a-c1d9-4911-9f83-6f022c14a67b 
[INFO ] 2024-07-17 19:08:50.821 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-855ba62a-c1d9-4911-9f83-6f022c14a67b 
[INFO ] 2024-07-17 19:08:50.821 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[855ba62a-c1d9-4911-9f83-6f022c14a67b] schema data cleaned 
[INFO ] 2024-07-17 19:08:50.822 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[855ba62a-c1d9-4911-9f83-6f022c14a67b] monitor closed 
[INFO ] 2024-07-17 19:08:50.822 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[855ba62a-c1d9-4911-9f83-6f022c14a67b] close complete, cost 15 ms 
[INFO ] 2024-07-17 19:08:50.847 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d0f04f4c-8c53-4089-a642-70827f232364] running status set to false 
[INFO ] 2024-07-17 19:08:50.847 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-d0f04f4c-8c53-4089-a642-70827f232364 
[INFO ] 2024-07-17 19:08:50.847 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-d0f04f4c-8c53-4089-a642-70827f232364 
[INFO ] 2024-07-17 19:08:50.847 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d0f04f4c-8c53-4089-a642-70827f232364] schema data cleaned 
[INFO ] 2024-07-17 19:08:50.848 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d0f04f4c-8c53-4089-a642-70827f232364] monitor closed 
[INFO ] 2024-07-17 19:08:50.848 - [Heartbeat-NewSource][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d0f04f4c-8c53-4089-a642-70827f232364] close complete, cost 25 ms 
[INFO ] 2024-07-17 19:08:54.120 - [Heartbeat-NewSource] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:08:54.120 - [Heartbeat-NewSource] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2d64d9d9 
[INFO ] 2024-07-17 19:08:54.254 - [Heartbeat-NewSource] - Stop task milestones: 66979eadb92eda1a86f523a3(Heartbeat-NewSource)  
[INFO ] 2024-07-17 19:08:54.254 - [Heartbeat-NewSource] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:08:54.254 - [Heartbeat-NewSource] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:08:54.313 - [Heartbeat-NewSource] - Remove memory task client succeed, task: Heartbeat-NewSource[66979eadb92eda1a86f523a3] 
[INFO ] 2024-07-17 19:08:54.313 - [Heartbeat-NewSource] - Destroy memory task client cache succeed, task: Heartbeat-NewSource[66979eadb92eda1a86f523a3] 
