[INFO ] 2024-08-15 16:43:09.252 - [任务 8] - Start task milestones: 66bdbf88e914743d45ca720c(任务 8) 
[INFO ] 2024-08-15 16:43:09.252 - [任务 8] - Task initialization... 
[INFO ] 2024-08-15 16:43:09.309 - [任务 8] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-15 16:43:09.309 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 16:43:09.354 - [任务 8][TestDummy] - Node TestDummy[b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0] start preload schema,table counts: 1 
[INFO ] 2024-08-15 16:43:09.354 - [任务 8][DB2132] - Node DB2132[85c90c3e-a8e7-4c3f-8786-103ac963f7aa] start preload schema,table counts: 1 
[INFO ] 2024-08-15 16:43:09.380 - [任务 8][DB2132] - Node DB2132[85c90c3e-a8e7-4c3f-8786-103ac963f7aa] preload schema finished, cost 22 ms 
[INFO ] 2024-08-15 16:43:09.380 - [任务 8][TestDummy] - Node TestDummy[b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0] preload schema finished, cost 24 ms 
[INFO ] 2024-08-15 16:43:10.209 - [任务 8][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-15 16:43:10.215 - [任务 8][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 16:43:10.296 - [任务 8][DB2132] - Source node "DB2132" read batch size: 100 
[INFO ] 2024-08-15 16:43:10.296 - [任务 8][DB2132] - Source node "DB2132" event queue capacity: 200 
[INFO ] 2024-08-15 16:43:10.412 - [任务 8][DB2132] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-15 16:43:10.412 - [任务 8][DB2132] - Table [ORDERS] not open CDC 
[INFO ] 2024-08-15 16:43:10.413 - [任务 8][DB2132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723711390411} 
[INFO ] 2024-08-15 16:43:10.413 - [任务 8][DB2132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-15 16:43:10.492 - [任务 8][DB2132] - Initial sync started 
[INFO ] 2024-08-15 16:43:10.492 - [任务 8][DB2132] - Starting batch read, table name: ORDERS, offset: null 
[INFO ] 2024-08-15 16:43:10.492 - [任务 8][DB2132] - Table ORDERS is going to be initial synced 
[INFO ] 2024-08-15 16:43:10.553 - [任务 8][DB2132] - Table [ORDERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 16:43:10.553 - [任务 8][DB2132] - Query table 'ORDERS' counts: 1 
[INFO ] 2024-08-15 16:43:10.553 - [任务 8][DB2132] - Initial sync completed 
[INFO ] 2024-08-15 16:43:10.554 - [任务 8][DB2132] - Incremental sync starting... 
[INFO ] 2024-08-15 16:43:10.554 - [任务 8][DB2132] - Initial sync completed 
[INFO ] 2024-08-15 16:43:10.756 - [任务 8][DB2132] - Starting stream read, table list: [ORDERS], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723711390411} 
[INFO ] 2024-08-15 18:09:55.007 - [任务 8][DB2132] - Incremental sync completed 
[ERROR] 2024-08-15 18:09:55.025 - [任务 8][DB2132] - io.grpc.StatusRuntimeException: UNAVAILABLE: Network closed for unknown reason <-- Error Message -->
io.grpc.StatusRuntimeException: UNAVAILABLE: Network closed for unknown reason

<-- Simple Stack Trace -->
Caused by: io.grpc.StatusRuntimeException: UNAVAILABLE: Network closed for unknown reason
	io.grpc.Status.asRuntimeException(Status.java:535)
	io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	...

<-- Full Stack Trace -->
io.grpc.StatusRuntimeException: UNAVAILABLE: Network closed for unknown reason
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.grpc.StatusRuntimeException: UNAVAILABLE: Network closed for unknown reason
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more

[INFO ] 2024-08-15 18:09:55.044 - [任务 8][DB2132] - Job suspend in error handle 
[INFO ] 2024-08-15 18:09:55.045 - [任务 8][DB2132] - Node DB2132[85c90c3e-a8e7-4c3f-8786-103ac963f7aa] running status set to false 
[INFO ] 2024-08-15 18:09:55.045 - [任务 8][DB2132] - Log Miner is shutting down... 
[INFO ] 2024-08-15 18:09:57.278 - [任务 8][DB2132] - PDK connector node stopped: HazelcastSourcePdkDataNode-85c90c3e-a8e7-4c3f-8786-103ac963f7aa 
[INFO ] 2024-08-15 18:09:57.279 - [任务 8][DB2132] - PDK connector node released: HazelcastSourcePdkDataNode-85c90c3e-a8e7-4c3f-8786-103ac963f7aa 
[INFO ] 2024-08-15 18:09:57.279 - [任务 8][DB2132] - Node DB2132[85c90c3e-a8e7-4c3f-8786-103ac963f7aa] schema data cleaned 
[INFO ] 2024-08-15 18:09:57.279 - [任务 8][DB2132] - Node DB2132[85c90c3e-a8e7-4c3f-8786-103ac963f7aa] monitor closed 
[INFO ] 2024-08-15 18:09:57.279 - [任务 8][DB2132] - Node DB2132[85c90c3e-a8e7-4c3f-8786-103ac963f7aa] close complete, cost 2236 ms 
[INFO ] 2024-08-15 18:09:57.280 - [任务 8][TestDummy] - Node TestDummy[b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0] running status set to false 
[INFO ] 2024-08-15 18:09:57.285 - [任务 8][TestDummy] - Stop connector 
[INFO ] 2024-08-15 18:09:57.293 - [任务 8][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0 
[INFO ] 2024-08-15 18:09:57.297 - [任务 8][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0 
[INFO ] 2024-08-15 18:09:57.297 - [任务 8][TestDummy] - Node TestDummy[b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0] schema data cleaned 
[INFO ] 2024-08-15 18:09:57.297 - [任务 8][TestDummy] - Node TestDummy[b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0] monitor closed 
[INFO ] 2024-08-15 18:09:57.297 - [任务 8][TestDummy] - Node TestDummy[b33ce83b-ea33-49c1-b11a-8e79ae0f4ff0] close complete, cost 7 ms 
[INFO ] 2024-08-15 18:09:57.520 - [任务 8] - Task [任务 8] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-15 18:09:57.520 - [任务 8] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 18:09:57.538 - [任务 8] - Stop task milestones: 66bdbf88e914743d45ca720c(任务 8)  
[INFO ] 2024-08-15 18:09:57.541 - [任务 8] - Stopped task aspect(s) 
[INFO ] 2024-08-15 18:09:57.541 - [任务 8] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 18:09:57.555 - [任务 8] - Remove memory task client succeed, task: 任务 8[66bdbf88e914743d45ca720c] 
[INFO ] 2024-08-15 18:09:57.555 - [任务 8] - Destroy memory task client cache succeed, task: 任务 8[66bdbf88e914743d45ca720c] 
