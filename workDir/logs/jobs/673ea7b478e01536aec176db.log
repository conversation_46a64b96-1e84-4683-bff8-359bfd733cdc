[INFO ] 2024-11-21 11:28:43.140 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:28:43.143 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:28:43.500 - [任务 299] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:28:43.501 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:28:43.640 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:28:43.644 - [任务 299][Mongo] - <PERSON><PERSON>[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:28:43.644 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:28:43.644 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:28:44.385 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:28:44.387 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:28:44.388 - [任务 299][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-21 11:28:44.482 - [任务 299][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:28:44.482 - [任务 299][Mysql3306] - Initial sync started 
[INFO ] 2024-11-21 11:28:44.487 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-21 11:28:44.488 - [任务 299][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-21 11:28:44.608 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:28:44.649 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:28:44.650 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:28:46.023 - [任务 299][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:28:46.024 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:28:46.024 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:28:46.028 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:28:46.070 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:28:46.074 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:28:46.074 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:28:46.105 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:28:46.105 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:28:46.105 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:28:46.105 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:28:46.109 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:28:46.109 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:28:46.109 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-11-21 11:28:46.135 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:28:46.137 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:28:46.145 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:28:46.145 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:28:44.386Z): 1 
[INFO ] 2024-11-21 11:28:46.146 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-21 11:28:46.146 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:28:46.352 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:29:04.888 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:29:04.915 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:29:04.915 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159724175 
[INFO ] 2024-11-21 11:29:04.915 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159724175 
[INFO ] 2024-11-21 11:29:04.918 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:29:04.919 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:29:04.922 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 51 ms 
[INFO ] 2024-11-21 11:29:04.926 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:29:04.950 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159724494 
[INFO ] 2024-11-21 11:29:04.950 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159724494 
[INFO ] 2024-11-21 11:29:04.951 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:29:04.951 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:29:05.159 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 29 ms 
[INFO ] 2024-11-21 11:29:06.223 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:29:06.226 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2c663d3 
[INFO ] 2024-11-21 11:29:06.347 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:29:06.348 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:29:06.348 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:29:06.362 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:29:06.365 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:29:10.876 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:29:10.876 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:29:11.078 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:29:11.153 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:29:11.154 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:29:11.154 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:29:11.154 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 1 ms 
[INFO ] 2024-11-21 11:29:11.360 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:29:11.945 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:29:11.990 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:29:11.990 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:29:11.993 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:29:11.993 - [任务 299][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-21 11:29:12.100 - [任务 299][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:29:12.108 - [任务 299][Mysql3306] - Initial sync started 
[INFO ] 2024-11-21 11:29:12.113 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-21 11:29:12.113 - [任务 299][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-21 11:29:12.251 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:29:12.256 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:29:12.338 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:29:12.426 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:29:12.426 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:29:12.458 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:29:12.458 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:29:12.489 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:29:12.489 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:29:12.520 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:29:12.520 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:29:12.541 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:29:12.541 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:29:13.438 - [任务 299][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:29:13.441 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:29:13.441 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:29:13.442 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:29:13.484 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:29:13.484 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:29:13.500 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:29:13.500 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:29:13.506 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:29:13.506 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:29:13.506 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:29:13.506 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:29:13.506 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:29:13.506 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-11-21 11:29:13.523 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:29:13.524 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:29:13.527 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:29:13.527 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:29:11.993Z): 1 
[INFO ] 2024-11-21 11:29:13.527 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-21 11:29:13.527 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:29:13.733 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:29:33.931 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:29:33.953 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:29:33.954 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159751772 
[INFO ] 2024-11-21 11:29:33.954 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159751772 
[INFO ] 2024-11-21 11:29:33.956 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:29:33.956 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:29:33.959 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 28 ms 
[INFO ] 2024-11-21 11:29:33.959 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:29:34.023 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159751809 
[INFO ] 2024-11-21 11:29:34.026 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159751809 
[INFO ] 2024-11-21 11:29:34.026 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:29:34.028 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:29:34.236 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 71 ms 
[INFO ] 2024-11-21 11:29:36.421 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:29:36.422 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2dd3bfec 
[INFO ] 2024-11-21 11:29:36.538 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:29:36.545 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:29:36.545 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:29:36.616 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:29:36.618 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:29:40.192 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:29:40.192 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:29:40.384 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:29:40.385 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:29:40.440 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:29:40.441 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:29:40.446 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:29:40.446 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:29:41.132 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:29:41.136 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:29:41.321 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:29:41.321 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:29:41.321 - [任务 299][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-21 11:29:41.411 - [任务 299][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:29:41.412 - [任务 299][Mysql3306] - Initial sync started 
[INFO ] 2024-11-21 11:29:41.424 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-21 11:29:41.424 - [任务 299][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-21 11:29:41.512 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:29:41.514 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:29:41.621 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:29:41.623 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:29:41.690 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:29:41.690 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:29:41.728 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:29:41.728 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:29:41.760 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:29:41.760 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:29:41.797 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:29:41.797 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:29:41.998 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:29:42.728 - [任务 299][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:29:42.729 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:29:42.729 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:29:42.729 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:29:42.775 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:29:42.775 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:29:42.775 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:29:42.794 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:29:42.794 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:29:42.795 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:29:42.795 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:29:42.795 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:29:42.795 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:29:42.798 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-11-21 11:29:42.798 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:29:42.821 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:29:42.821 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:29:42.823 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:29:41.321Z): 1 
[INFO ] 2024-11-21 11:29:42.823 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-21 11:29:42.823 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:29:43.024 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:32:22.431 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:32:22.455 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:32:22.456 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159781138 
[INFO ] 2024-11-21 11:32:22.456 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159781138 
[INFO ] 2024-11-21 11:32:22.456 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:32:22.456 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:32:22.457 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 40 ms 
[INFO ] 2024-11-21 11:32:22.460 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:32:22.482 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159780933 
[INFO ] 2024-11-21 11:32:22.482 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159780933 
[INFO ] 2024-11-21 11:32:22.482 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:32:22.485 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:32:22.485 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 27 ms 
[INFO ] 2024-11-21 11:32:26.799 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:32:26.800 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4c2d2f07 
[INFO ] 2024-11-21 11:32:26.924 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:32:26.931 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:32:26.931 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:32:26.954 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:32:26.955 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:32:30.838 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:32:30.949 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:32:30.951 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:32:31.031 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:32:31.031 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:32:31.031 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:32:31.031 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:32:31.031 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 1 ms 
[INFO ] 2024-11-21 11:32:31.641 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:32:31.641 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:32:31.957 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:32:31.957 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:32:31.957 - [任务 299][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-21 11:32:31.964 - [任务 299][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:32:32.032 - [任务 299][Mysql3306] - Initial sync started 
[INFO ] 2024-11-21 11:32:32.041 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-21 11:32:32.044 - [任务 299][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-21 11:32:32.119 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:32:32.119 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:32:32.165 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:32:32.165 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:32:32.196 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:32:32.196 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:32:32.231 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:32:32.231 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:32:32.264 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:32:32.265 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:32:32.297 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:32:32.297 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:32:32.498 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:32:33.181 - [任务 299][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:32:33.181 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:32:33.181 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:32:33.181 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:32:33.275 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:32:33.275 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:32:33.275 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:32:33.291 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:32:33.291 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:32:33.291 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:32:33.292 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:32:33.292 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:32:33.292 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:32:33.296 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-11-21 11:32:33.296 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:32:33.311 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:32:33.311 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:32:33.314 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:32:31.953Z): 1 
[INFO ] 2024-11-21 11:32:33.314 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-21 11:32:33.315 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:32:33.317 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:34:12.937 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:34:12.956 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:34:12.957 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159951757 
[INFO ] 2024-11-21 11:34:12.957 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732159951757 
[INFO ] 2024-11-21 11:34:12.957 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:34:12.957 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:34:12.959 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 42 ms 
[INFO ] 2024-11-21 11:34:12.962 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:34:12.994 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159951484 
[INFO ] 2024-11-21 11:34:12.994 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732159951484 
[INFO ] 2024-11-21 11:34:12.995 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:34:12.996 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:34:13.199 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 38 ms 
[INFO ] 2024-11-21 11:34:17.086 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:34:17.212 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6fc53419 
[INFO ] 2024-11-21 11:34:17.214 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:34:17.221 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:34:17.221 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:34:17.242 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:34:17.245 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:35:21.491 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:35:21.492 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:35:21.631 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:35:21.631 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:35:21.674 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:35:21.675 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:35:21.675 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:35:21.675 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:35:22.445 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:35:22.447 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:35:22.460 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:35:22.460 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:35:22.461 - [任务 299][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-21 11:35:22.464 - [任务 299][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:35:22.603 - [任务 299][Mysql3306] - Initial sync started 
[INFO ] 2024-11-21 11:35:22.604 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-21 11:35:22.604 - [任务 299][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-21 11:35:22.703 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:35:22.704 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:35:22.765 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:35:22.766 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:35:22.808 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:35:22.808 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:35:22.845 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:35:22.846 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:35:22.882 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:35:22.882 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:35:22.917 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:35:22.917 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:35:23.120 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:35:23.784 - [任务 299][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:35:23.784 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:35:23.785 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:35:23.785 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:35:23.826 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:35:23.826 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:35:23.826 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:35:23.845 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:35:23.853 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:35:23.853 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:35:23.853 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:35:23.853 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:35:23.853 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:35:23.853 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-11-21 11:35:23.872 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:35:23.876 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:35:23.877 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:35:23.880 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:35:22.460Z): 1 
[INFO ] 2024-11-21 11:35:23.880 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-21 11:35:23.880 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:35:23.881 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:37:48.469 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:37:48.491 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:37:48.492 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160122221 
[INFO ] 2024-11-21 11:37:48.492 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160122221 
[INFO ] 2024-11-21 11:37:48.492 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:37:48.492 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:37:48.493 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 41 ms 
[INFO ] 2024-11-21 11:37:48.518 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:37:48.519 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160122309 
[INFO ] 2024-11-21 11:37:48.519 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160122309 
[INFO ] 2024-11-21 11:37:48.520 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:37:48.520 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:37:48.725 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 28 ms 
[INFO ] 2024-11-21 11:37:52.443 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:37:52.445 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72654172 
[INFO ] 2024-11-21 11:37:52.567 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:37:52.588 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:37:52.588 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:37:52.607 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:37:52.607 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:39:59.173 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:39:59.174 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:39:59.538 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:39:59.538 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:39:59.585 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:39:59.585 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 1 
[INFO ] 2024-11-21 11:39:59.586 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 1 ms 
[INFO ] 2024-11-21 11:39:59.586 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:40:00.372 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:40:00.372 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:40:00.376 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:40:00.376 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:40:00.376 - [任务 299][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-21 11:40:00.381 - [任务 299][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:40:00.512 - [任务 299][Mysql3306] - Initial sync started 
[INFO ] 2024-11-21 11:40:00.512 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-21 11:40:00.512 - [任务 299][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-21 11:40:00.518 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:40:00.613 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:40:00.613 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:40:00.646 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:40:00.646 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:40:00.684 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:40:00.684 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:40:00.717 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:40:00.717 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:40:00.755 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:40:00.755 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:40:00.772 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:40:00.772 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:40:01.645 - [任务 299][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:40:01.646 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:40:01.646 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:40:01.646 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:40:01.685 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:40:01.685 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:40:01.685 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:40:01.699 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:40:01.705 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:40:01.705 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:40:01.705 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:40:01.705 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:40:01.705 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:40:01.709 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-11-21 11:40:01.709 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:40:01.738 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:40:01.738 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:40:01.743 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:40:01.743 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-21 11:40:01.745 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:40:01.745 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:40:18.417 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:40:18.452 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:40:18.452 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160400126 
[INFO ] 2024-11-21 11:40:18.453 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160400126 
[INFO ] 2024-11-21 11:40:18.453 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:40:18.453 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:40:18.454 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 55 ms 
[INFO ] 2024-11-21 11:40:18.458 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:40:18.476 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160400219 
[INFO ] 2024-11-21 11:40:18.477 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160400219 
[INFO ] 2024-11-21 11:40:18.477 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:40:18.478 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:40:18.478 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 23 ms 
[INFO ] 2024-11-21 11:40:22.755 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:40:22.879 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3fb6ee0f 
[INFO ] 2024-11-21 11:40:22.879 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:40:22.890 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:40:22.891 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:40:22.914 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:40:22.915 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:40:36.088 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:40:36.104 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:40:36.572 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:40:36.711 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:40:36.711 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 2 
[INFO ] 2024-11-21 11:40:36.711 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 2 
[INFO ] 2024-11-21 11:40:36.711 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:40:36.711 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 1 ms 
[INFO ] 2024-11-21 11:40:36.899 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:40:36.900 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:40:36.976 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:40:36.979 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:40:36.979 - [任务 299][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-21 11:40:37.048 - [任务 299][Mysql3306] - batch offset found: {"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"}},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:40:37.050 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_DISTRICT 
[INFO ] 2024-11-21 11:40:37.050 - [任务 299][Mysql3306] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-11-21 11:40:37.057 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:40:37.058 - [任务 299][Mysql3306] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:40:37.058 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:40:37.058 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:40:37.058 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:40:37.143 - [任务 299][Mongo] - Table 'BMSQL_DISTRICT' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:40:37.143 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:40:37.143 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:40:37.143 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:40:37.156 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:40:37.164 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:40:37.164 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:40:37.165 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:40:37.165 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:40:37.165 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:40:37.165 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-11-21 11:40:37.186 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:40:37.191 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:40:37.191 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:40:37.196 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:40:37.197 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:40:37.197 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:40:37.303 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f9e2056ae7127bb5c250, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_DISTRICT, version=v2, tableName=BMSQL_DISTRICT, externalStorageTableName=ExternalStorage_SHARE_CDC_-1035875561, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:40:37.303 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_DISTRICT_任务 299, external storage name: ExternalStorage_SHARE_CDC_-1035875561 
[INFO ] 2024-11-21 11:40:37.303 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_DISTRICT] 
[INFO ] 2024-11-21 11:40:37.309 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_DISTRICT) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:40:37.310 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-21 11:40:37.310 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_DISTRICT' log, sequence: 1 
[INFO ] 2024-11-21 11:40:37.312 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_DISTRICT filter: {sequence=1} 
[INFO ] 2024-11-21 11:40:37.683 - [任务 299][Mongo] - Table 'BMSQL_DISTRICT' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:40:57.802 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:40:57.823 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:40:57.824 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160436772 
[INFO ] 2024-11-21 11:40:57.824 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160436772 
[INFO ] 2024-11-21 11:40:57.824 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:40:57.824 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:40:57.825 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 25 ms 
[INFO ] 2024-11-21 11:40:57.825 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:40:57.834 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160436772 
[INFO ] 2024-11-21 11:40:57.834 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160436772 
[INFO ] 2024-11-21 11:40:57.834 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:40:57.835 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:40:57.835 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 9 ms 
[INFO ] 2024-11-21 11:40:58.165 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:40:58.166 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3222e565 
[INFO ] 2024-11-21 11:40:58.285 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:40:58.300 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:40:58.300 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:40:58.320 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:40:58.321 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:41:42.880 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:41:43.081 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:41:43.082 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:41:43.197 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:41:43.317 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 7 
[INFO ] 2024-11-21 11:41:43.318 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 7 
[INFO ] 2024-11-21 11:41:43.318 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:41:43.318 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:41:43.490 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:41:43.490 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:41:43.576 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:41:43.576 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:41:43.577 - [任务 299][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-21 11:41:43.577 - [任务 299][Mysql3306] - batch offset found: {"BMSQL_DISTRICT":{"batch_read_connector_status":"OVER"},"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"}},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:41:43.671 - [任务 299][Mongo] - Table: BMSQL_HISTORY already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:41:43.671 - [任务 299][Mongo] - Table: BMSQL_HISTORY will create Index: TapIndex name dateIndex indexFields: [TapIndexField name H_DATA fieldAsc true indexType null; ] 
[INFO ] 2024-11-21 11:41:43.722 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_HISTORY 
[INFO ] 2024-11-21 11:41:43.722 - [任务 299][Mysql3306] - Table BMSQL_HISTORY is going to be initial synced 
[INFO ] 2024-11-21 11:41:43.770 - [任务 299][Mongo] - Table: BMSQL_HISTORY create Index: dateIndex successfully, cost 99ms 
[INFO ] 2024-11-21 11:41:43.771 - [任务 299][Mongo] - Table: BMSQL_HISTORY synchronize indexes completed, cost 198ms totally 
[INFO ] 2024-11-21 11:41:43.825 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:41:43.827 - [任务 299][Mongo] - Table: BMSQL_ITEM already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:41:43.827 - [任务 299][Mongo] - Table: BMSQL_ITEM already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name I_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:41:43.877 - [任务 299][Mongo] - Table: BMSQL_NEW_ORDER already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:41:43.878 - [任务 299][Mongo] - Table: BMSQL_NEW_ORDER already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name NO_W_ID fieldAsc true indexType null; TapIndexField name NO_D_ID fieldAsc true indexType null; TapIndexField name NO_O_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:41:43.922 - [任务 299][Mongo] - Table: BMSQL_OORDER already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:41:43.922 - [任务 299][Mongo] - Table: BMSQL_OORDER already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name O_W_ID fieldAsc true indexType null; TapIndexField name O_D_ID fieldAsc true indexType null; TapIndexField name O_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:41:43.977 - [任务 299][Mongo] - Table: BMSQL_OORDER_BACK already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:41:43.977 - [任务 299][Mongo] - Table: BMSQL_OORDER_BACK already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:41:44.057 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:41:44.123 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:41:44.124 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:41:44.151 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:41:44.152 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:41:44.180 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:41:44.181 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:41:44.208 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:41:44.209 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:41:44.237 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:41:44.239 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:41:44.239 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:41:45.143 - [任务 299][Mysql3306] - Table [BMSQL_HISTORY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:41:45.144 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-11-21 11:41:45.144 - [任务 299][Mysql3306] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-11-21 11:41:45.274 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:41:45.275 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:41:45.299 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:41:45.299 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:41:45.321 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:41:45.321 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:41:45.348 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:41:45.348 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:41:45.384 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:41:45.384 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:41:45.402 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:41:45.402 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:41:46.273 - [任务 299][Mysql3306] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:41:46.273 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_NEW_ORDER 
[INFO ] 2024-11-21 11:41:46.403 - [任务 299][Mysql3306] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-11-21 11:41:46.403 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:41:46.443 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:41:46.443 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:41:46.480 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:41:46.480 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:41:46.525 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:41:46.525 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:41:46.577 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:41:46.577 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:41:46.597 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:41:46.597 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:41:46.802 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:41:46.919 - [任务 299][Mysql3306] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:41:46.923 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-21 11:41:46.923 - [任务 299][Mysql3306] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-21 11:41:47.335 - [任务 299][Mysql3306] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:41:47.339 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_OORDER_BACK 
[INFO ] 2024-11-21 11:41:47.339 - [任务 299][Mysql3306] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2024-11-21 11:41:47.339 - [任务 299][Mysql3306] - Table [BMSQL_OORDER_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:41:47.339 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:41:47.339 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:41:47.339 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:41:47.381 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:41:47.381 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:41:47.381 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:41:47.391 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:41:47.398 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:41:47.398 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:41:47.398 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:41:47.398 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:41:47.398 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:41:47.398 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 7, partition size: 1, read thread number: 7 
[INFO ] 2024-11-21 11:41:47.421 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:41:47.423 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:41:47.423 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:41:47.429 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f9e2056ae7127bb5c250, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_DISTRICT, version=v2, tableName=BMSQL_DISTRICT, externalStorageTableName=ExternalStorage_SHARE_CDC_-1035875561, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:41:47.431 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:41:47.431 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:41:47.490 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:41:47.492 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_DISTRICT_任务 299, external storage name: ExternalStorage_SHARE_CDC_-1035875561 
[INFO ] 2024-11-21 11:41:47.497 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_DISTRICT] 
[INFO ] 2024-11-21 11:41:47.497 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_DISTRICT) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:41:47.497 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_DISTRICT' log, sequence: 1 
[INFO ] 2024-11-21 11:41:47.500 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_DISTRICT filter: {sequence=1} 
[INFO ] 2024-11-21 11:41:47.530 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f9ae056ae7127bb5b57c, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_HISTORY, version=v2, tableName=BMSQL_HISTORY, externalStorageTableName=ExternalStorage_SHARE_CDC_-362728501, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:41:47.530 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_HISTORY_任务 299, external storage name: ExternalStorage_SHARE_CDC_-362728501 
[INFO ] 2024-11-21 11:41:47.530 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_HISTORY] 
[INFO ] 2024-11-21 11:41:47.541 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_HISTORY) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:41:47.542 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721fa41056ae7127bb5cfe2, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_353388316, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:41:47.543 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_HISTORY' log, sequence: 1 
[INFO ] 2024-11-21 11:41:47.546 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_HISTORY filter: {sequence=1} 
[INFO ] 2024-11-21 11:41:47.567 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_ITEM_任务 299, external storage name: ExternalStorage_SHARE_CDC_353388316 
[INFO ] 2024-11-21 11:41:47.567 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_ITEM] 
[INFO ] 2024-11-21 11:41:47.574 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_ITEM) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:41:47.575 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_ITEM' log, sequence: 1 
[INFO ] 2024-11-21 11:41:47.577 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_ITEM filter: {sequence=1} 
[INFO ] 2024-11-21 11:41:47.674 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721fbe1056ae7127bb5f8b7, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_NEW_ORDER, version=v2, tableName=BMSQL_NEW_ORDER, externalStorageTableName=ExternalStorage_SHARE_CDC_-1755887290, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:41:47.677 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_NEW_ORDER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-1755887290 
[INFO ] 2024-11-21 11:41:47.677 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_NEW_ORDER] 
[INFO ] 2024-11-21 11:41:47.683 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_NEW_ORDER) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:41:47.686 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_NEW_ORDER' log, sequence: 1 
[INFO ] 2024-11-21 11:41:47.686 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_NEW_ORDER filter: {sequence=1} 
[INFO ] 2024-11-21 11:41:47.768 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721fc4e056ae7127bb60a09, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_OORDER, version=v2, tableName=BMSQL_OORDER, externalStorageTableName=ExternalStorage_SHARE_CDC_471293448, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:41:47.768 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_OORDER_任务 299, external storage name: ExternalStorage_SHARE_CDC_471293448 
[INFO ] 2024-11-21 11:41:47.768 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_OORDER] 
[INFO ] 2024-11-21 11:41:47.778 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67231eb3056ae7127bc3c643, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_OORDER_BACK, version=v2, tableName=BMSQL_OORDER_BACK, externalStorageTableName=ExternalStorage_SHARE_CDC_1423170750, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:41:47.784 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_OORDER) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:41:47.784 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_OORDER' log, sequence: 1 
[INFO ] 2024-11-21 11:41:47.784 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_OORDER filter: {sequence=1} 
[INFO ] 2024-11-21 11:41:47.790 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_OORDER_BACK_任务 299, external storage name: ExternalStorage_SHARE_CDC_1423170750 
[INFO ] 2024-11-21 11:41:47.800 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_OORDER_BACK] 
[INFO ] 2024-11-21 11:41:47.800 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_OORDER_BACK) by timestamp(2024-11-21T03:40:00.376Z): 1 
[INFO ] 2024-11-21 11:41:47.802 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_HISTORY, BMSQL_OORDER], data change syncing 
[INFO ] 2024-11-21 11:41:47.802 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_OORDER_BACK' log, sequence: 1 
[INFO ] 2024-11-21 11:41:47.803 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_OORDER_BACK filter: {sequence=1} 
[INFO ] 2024-11-21 11:41:48.008 - [任务 299][Mongo] - Table 'BMSQL_OORDER_BACK' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:44:42.796 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:44:42.796 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:44:42.805 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160503373 
[INFO ] 2024-11-21 11:44:42.805 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160503373 
[INFO ] 2024-11-21 11:44:42.805 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:44:42.805 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:44:42.807 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 36 ms 
[INFO ] 2024-11-21 11:44:42.807 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:44:42.820 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160503373 
[INFO ] 2024-11-21 11:44:42.820 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160503373 
[INFO ] 2024-11-21 11:44:42.821 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:44:42.821 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:44:42.825 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 13 ms 
[INFO ] 2024-11-21 11:44:43.689 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:44:43.690 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ac2996d 
[INFO ] 2024-11-21 11:44:43.820 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:44:43.820 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:44:43.838 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:44:43.841 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:44:43.841 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:48:00.729 - [任务 299] - Task initialization... 
[INFO ] 2024-11-21 11:48:00.738 - [任务 299] - Start task milestones: 673ea7b478e01536aec176db(任务 299) 
[INFO ] 2024-11-21 11:48:00.980 - [任务 299] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-21 11:48:00.980 - [任务 299] - The engine receives 任务 299 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-21 11:48:01.017 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] start preload schema,table counts: 7 
[INFO ] 2024-11-21 11:48:01.017 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] start preload schema,table counts: 7 
[INFO ] 2024-11-21 11:48:01.017 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:48:01.017 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] preload schema finished, cost 0 ms 
[INFO ] 2024-11-21 11:48:01.697 - [任务 299][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-21 11:48:01.697 - [任务 299][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-21 11:48:01.719 - [任务 299][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-21 11:48:01.719 - [任务 299][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-21 11:48:01.719 - [任务 299][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-21 11:48:01.838 - [任务 299][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":44136,"gtidSet":""} 
[INFO ] 2024-11-21 11:48:01.840 - [任务 299][Mysql3306] - Initial sync started 
[INFO ] 2024-11-21 11:48:01.840 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_OORDER_BACK 
[INFO ] 2024-11-21 11:48:01.849 - [任务 299][Mysql3306] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2024-11-21 11:48:01.859 - [任务 299][Mongo] - Table: BMSQL_OORDER_BACK already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:48:01.859 - [任务 299][Mongo] - Table: BMSQL_OORDER_BACK already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:48:01.861 - [任务 299][Mysql3306] - Table [BMSQL_OORDER_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:48:01.861 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_NEW_ORDER 
[INFO ] 2024-11-21 11:48:01.861 - [任务 299][Mysql3306] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-11-21 11:48:01.896 - [任务 299][Mysql3306] - Query snapshot row size completed: Mysql3306(25fb9751-cb8e-4162-8a27-054c0883d4df) 
[INFO ] 2024-11-21 11:48:01.938 - [任务 299][Mongo] - Table: BMSQL_NEW_ORDER already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:48:01.940 - [任务 299][Mongo] - Table: BMSQL_NEW_ORDER already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name NO_W_ID fieldAsc true indexType null; TapIndexField name NO_D_ID fieldAsc true indexType null; TapIndexField name NO_O_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:48:01.995 - [任务 299][Mongo] - Table: BMSQL_ITEM already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:48:01.995 - [任务 299][Mongo] - Table: BMSQL_ITEM already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name I_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:48:02.032 - [任务 299][Mongo] - Table: BMSQL_DISTRICT already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:48:02.032 - [任务 299][Mongo] - Table: BMSQL_DISTRICT already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name D_W_ID fieldAsc true indexType null; TapIndexField name D_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:48:02.081 - [任务 299][Mongo] - Table: BMSQL_CUSTOMER already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:48:02.081 - [任务 299][Mongo] - Table: BMSQL_CUSTOMER already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name C_W_ID fieldAsc true indexType null; TapIndexField name C_D_ID fieldAsc true indexType null; TapIndexField name C_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:48:02.110 - [任务 299][Mongo] - Table: BMSQL_HISTORY already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:48:02.111 - [任务 299][Mongo] - Table: BMSQL_HISTORY already exists Index: dateIndex and will no longer create index 
[INFO ] 2024-11-21 11:48:02.111 - [任务 299][Mongo] - Table: BMSQL_HISTORY already exists Index list: [TapIndex name dateIndex indexFields: [TapIndexField name H_DATA fieldAsc true indexType null; ], TapIndex name PRIMARY indexFields: [TapIndexField name HIST_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:48:02.147 - [任务 299][Mongo] - Table: BMSQL_OORDER already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-21 11:48:02.147 - [任务 299][Mongo] - Table: BMSQL_OORDER already exists Index list: [TapIndex name PRIMARY indexFields: [TapIndexField name O_W_ID fieldAsc true indexType null; TapIndexField name O_D_ID fieldAsc true indexType null; TapIndexField name O_ID fieldAsc true indexType null; ]] 
[INFO ] 2024-11-21 11:48:02.203 - [任务 299][Mongo] - Table 'BMSQL_OORDER_BACK' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:48:02.203 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:48:02.242 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:48:02.242 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:48:02.262 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:48:02.262 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:48:02.283 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:48:02.283 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:48:02.303 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:48:02.303 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:48:02.322 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:48:02.322 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:48:02.526 - [任务 299][Mongo] - Table 'BMSQL_NEW_ORDER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:48:02.547 - [任务 299][Mysql3306] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:48:02.547 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-11-21 11:48:02.621 - [任务 299][Mysql3306] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-11-21 11:48:02.621 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:48:02.640 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:48:02.640 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:48:02.659 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:48:02.659 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:48:02.678 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:48:02.678 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:48:02.697 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:48:02.697 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:48:02.723 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:48:02.724 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:48:02.724 - [任务 299][Mongo] - Table 'BMSQL_ITEM' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:48:03.444 - [任务 299][Mysql3306] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:48:03.446 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_DISTRICT 
[INFO ] 2024-11-21 11:48:03.447 - [任务 299][Mysql3306] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-11-21 11:48:03.449 - [任务 299][Mysql3306] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:48:03.454 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-21 11:48:03.454 - [任务 299][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-21 11:48:03.518 - [任务 299][Mongo] - Table 'BMSQL_DISTRICT' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:48:03.518 - [任务 299][Mongo] - Table 'BMSQL_DISTRICT' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:48:03.548 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:48:03.548 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:48:03.578 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:48:03.578 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:48:03.609 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:48:03.610 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:48:03.640 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:48:03.640 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:48:03.671 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:48:03.674 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:48:03.686 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:48:03.687 - [任务 299][Mongo] - Table 'BMSQL_CUSTOMER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:48:04.566 - [任务 299][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:48:04.566 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_HISTORY 
[INFO ] 2024-11-21 11:48:04.566 - [任务 299][Mysql3306] - Table BMSQL_HISTORY is going to be initial synced 
[INFO ] 2024-11-21 11:48:04.682 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:48:04.682 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:48:04.704 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:48:04.704 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:48:04.732 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:48:04.732 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:48:04.764 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:48:04.764 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:48:04.793 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:48:04.794 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:48:04.807 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:48:04.807 - [任务 299][Mongo] - Table 'BMSQL_HISTORY' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:48:05.433 - [任务 299][Mysql3306] - Table [BMSQL_HISTORY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:48:05.433 - [任务 299][Mysql3306] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-21 11:48:05.433 - [任务 299][Mysql3306] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-21 11:48:05.533 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-21 11:48:05.533 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-21 11:48:05.554 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-21 11:48:05.554 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-21 11:48:05.581 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-21 11:48:05.581 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-21 11:48:05.611 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-21 11:48:05.611 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-21 11:48:05.639 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-21 11:48:05.639 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-21 11:48:05.652 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-21 11:48:05.652 - [任务 299][Mongo] - Table 'BMSQL_OORDER' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-21 11:48:06.358 - [任务 299][Mysql3306] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-21 11:48:06.361 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:48:06.361 - [任务 299][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-21 11:48:06.361 - [任务 299][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-21 11:48:06.398 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-21 11:48:06.398 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql3306 enable share cdc: true 
[INFO ] 2024-11-21 11:48:06.398 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 299 enable share cdc: true 
[INFO ] 2024-11-21 11:48:06.418 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from Mysql3306 
[INFO ] 2024-11-21 11:48:06.418 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdata_errorCode?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-11-21 11:48:06.418 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-21 11:48:06.418 - [任务 299][Mysql3306] - Init share cdc reader completed 
[INFO ] 2024-11-21 11:48:06.418 - [任务 299][Mysql3306] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-21 11:48:06.418 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-21 11:48:06.419 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Read table count: 7, partition size: 1, read thread number: 7 
[INFO ] 2024-11-21 11:48:06.437 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f8c2056ae7127bb5a338, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_CUSTOMER, version=v2, tableName=BMSQL_CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-718661785, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:48:06.437 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_CUSTOMER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-718661785 
[INFO ] 2024-11-21 11:48:06.440 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_CUSTOMER] 
[INFO ] 2024-11-21 11:48:06.440 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_CUSTOMER) by timestamp(2024-11-21T03:48:01.719Z): 1 
[INFO ] 2024-11-21 11:48:06.440 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_CUSTOMER' log, sequence: 1 
[INFO ] 2024-11-21 11:48:06.440 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_CUSTOMER filter: {sequence=1} 
[INFO ] 2024-11-21 11:48:06.447 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f9e2056ae7127bb5c250, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_DISTRICT, version=v2, tableName=BMSQL_DISTRICT, externalStorageTableName=ExternalStorage_SHARE_CDC_-1035875561, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:48:06.447 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_DISTRICT_任务 299, external storage name: ExternalStorage_SHARE_CDC_-1035875561 
[INFO ] 2024-11-21 11:48:06.449 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_DISTRICT] 
[INFO ] 2024-11-21 11:48:06.449 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_DISTRICT) by timestamp(2024-11-21T03:48:01.719Z): 1 
[INFO ] 2024-11-21 11:48:06.449 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_DISTRICT' log, sequence: 1 
[INFO ] 2024-11-21 11:48:06.449 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_DISTRICT filter: {sequence=1} 
[INFO ] 2024-11-21 11:48:06.457 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721f9ae056ae7127bb5b57c, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_HISTORY, version=v2, tableName=BMSQL_HISTORY, externalStorageTableName=ExternalStorage_SHARE_CDC_-362728501, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:48:06.457 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_HISTORY_任务 299, external storage name: ExternalStorage_SHARE_CDC_-362728501 
[INFO ] 2024-11-21 11:48:06.459 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_HISTORY] 
[INFO ] 2024-11-21 11:48:06.459 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_HISTORY) by timestamp(2024-11-21T03:48:01.719Z): 1 
[INFO ] 2024-11-21 11:48:06.459 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_HISTORY' log, sequence: 1 
[INFO ] 2024-11-21 11:48:06.460 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_HISTORY filter: {sequence=1} 
[INFO ] 2024-11-21 11:48:06.466 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721fa41056ae7127bb5cfe2, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_353388316, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:48:06.466 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_ITEM_任务 299, external storage name: ExternalStorage_SHARE_CDC_353388316 
[INFO ] 2024-11-21 11:48:06.468 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_ITEM] 
[INFO ] 2024-11-21 11:48:06.468 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_ITEM) by timestamp(2024-11-21T03:48:01.719Z): 1 
[INFO ] 2024-11-21 11:48:06.468 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_ITEM' log, sequence: 1 
[INFO ] 2024-11-21 11:48:06.468 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_ITEM filter: {sequence=1} 
[INFO ] 2024-11-21 11:48:06.537 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721fbe1056ae7127bb5f8b7, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_NEW_ORDER, version=v2, tableName=BMSQL_NEW_ORDER, externalStorageTableName=ExternalStorage_SHARE_CDC_-1755887290, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:48:06.537 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_NEW_ORDER_任务 299, external storage name: ExternalStorage_SHARE_CDC_-1755887290 
[INFO ] 2024-11-21 11:48:06.537 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_NEW_ORDER] 
[INFO ] 2024-11-21 11:48:06.538 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_NEW_ORDER) by timestamp(2024-11-21T03:48:01.719Z): 1 
[INFO ] 2024-11-21 11:48:06.538 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_NEW_ORDER' log, sequence: 1 
[INFO ] 2024-11-21 11:48:06.543 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_NEW_ORDER filter: {sequence=1} 
[INFO ] 2024-11-21 11:48:06.543 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6721fc4e056ae7127bb60a09, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_OORDER, version=v2, tableName=BMSQL_OORDER, externalStorageTableName=ExternalStorage_SHARE_CDC_471293448, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:48:06.596 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_OORDER_任务 299, external storage name: ExternalStorage_SHARE_CDC_471293448 
[INFO ] 2024-11-21 11:48:06.596 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_OORDER] 
[INFO ] 2024-11-21 11:48:06.597 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_OORDER) by timestamp(2024-11-21T03:48:01.719Z): 1 
[INFO ] 2024-11-21 11:48:06.597 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_OORDER' log, sequence: 1 
[INFO ] 2024-11-21 11:48:06.604 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_OORDER filter: {sequence=1} 
[INFO ] 2024-11-21 11:48:06.604 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67231eb3056ae7127bc3c643, customId=null, createAt=null, lastUpdAt=null, userId=6721f7046981ca25b51a0a5a, lastUpdBy=null, createUser=null, permissionActions=null), sign=6720599e584d3301f1b298c8_BMSQL_OORDER_BACK, version=v2, tableName=BMSQL_OORDER_BACK, externalStorageTableName=ExternalStorage_SHARE_CDC_1423170750, shareCdcTaskId=6721f7ab6981ca25b51a0ab4, connectionId=6720599e584d3301f1b298c8) 
[INFO ] 2024-11-21 11:48:06.606 - [任务 299][Mysql3306] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from Mysql3306_BMSQL_OORDER_BACK_任务 299, external storage name: ExternalStorage_SHARE_CDC_1423170750 
[INFO ] 2024-11-21 11:48:06.606 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [BMSQL_OORDER_BACK] 
[INFO ] 2024-11-21 11:48:06.608 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find sequence in construct(BMSQL_OORDER_BACK) by timestamp(2024-11-21T03:48:01.719Z): 1 
[INFO ] 2024-11-21 11:48:06.608 - [任务 299][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_HISTORY, BMSQL_OORDER], data change syncing 
[INFO ] 2024-11-21 11:48:06.608 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Starting read 'BMSQL_OORDER_BACK' log, sequence: 1 
[INFO ] 2024-11-21 11:48:06.608 - [任务 299][Mysql3306] - [Share CDC Task HZ Reader] - Find by BMSQL_OORDER_BACK filter: {sequence=1} 
[INFO ] 2024-11-21 11:48:37.210 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] running status set to false 
[INFO ] 2024-11-21 11:48:37.210 - [任务 299][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-21 11:48:37.226 - [任务 299][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160881452 
[INFO ] 2024-11-21 11:48:37.226 - [任务 299][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode_25fb9751-cb8e-4162-8a27-054c0883d4df_1732160881452 
[INFO ] 2024-11-21 11:48:37.226 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] schema data cleaned 
[INFO ] 2024-11-21 11:48:37.226 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] monitor closed 
[INFO ] 2024-11-21 11:48:37.228 - [任务 299][Mysql3306] - Node Mysql3306[25fb9751-cb8e-4162-8a27-054c0883d4df] close complete, cost 34 ms 
[INFO ] 2024-11-21 11:48:37.245 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] running status set to false 
[INFO ] 2024-11-21 11:48:37.246 - [任务 299][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160881543 
[INFO ] 2024-11-21 11:48:37.246 - [任务 299][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode_fba897cd-58c9-474c-867f-3242e7f39a6e_1732160881543 
[INFO ] 2024-11-21 11:48:37.246 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] schema data cleaned 
[INFO ] 2024-11-21 11:48:37.246 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] monitor closed 
[INFO ] 2024-11-21 11:48:37.451 - [任务 299][Mongo] - Node Mongo[fba897cd-58c9-474c-867f-3242e7f39a6e] close complete, cost 18 ms 
[INFO ] 2024-11-21 11:48:39.040 - [任务 299] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-21 11:48:39.040 - [任务 299] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@68d2309a 
[INFO ] 2024-11-21 11:48:39.153 - [任务 299] - Stop task milestones: 673ea7b478e01536aec176db(任务 299)  
[INFO ] 2024-11-21 11:48:39.162 - [任务 299] - Stopped task aspect(s) 
[INFO ] 2024-11-21 11:48:39.162 - [任务 299] - Snapshot order controller have been removed 
[INFO ] 2024-11-21 11:48:39.189 - [任务 299] - Remove memory task client succeed, task: 任务 299[673ea7b478e01536aec176db] 
[INFO ] 2024-11-21 11:48:39.189 - [任务 299] - Destroy memory task client cache succeed, task: 任务 299[673ea7b478e01536aec176db] 
