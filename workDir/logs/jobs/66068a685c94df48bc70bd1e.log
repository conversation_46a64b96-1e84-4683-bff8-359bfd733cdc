[INFO ] 2024-03-29 17:31:22.865 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e41b2056-23e8-4450-9f7a-eb259c9a86c9] - Node e41b2056-23e8-4450-9f7a-eb259c9a86c9[e41b2056-23e8-4450-9f7a-eb259c9a86c9] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:31:22.869 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:22.870 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:22.870 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:31:22.871 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:31:22.871 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e41b2056-23e8-4450-9f7a-eb259c9a86c9] - Node e41b2056-23e8-4450-9f7a-eb259c9a86c9[e41b2056-23e8-4450-9f7a-eb259c9a86c9] preload schema finished, cost 6 ms 
[INFO ] 2024-03-29 17:31:23.515 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:31:23.815 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@11867c14 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@11867c14 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@11867c14 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:31:25.174 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:31:25.228 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:31:25.231 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:31:25.231 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:31:25.233 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:31:25.241 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 68 ms 
[INFO ] 2024-03-29 17:31:26.516 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:31:26.528 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e41b2056-23e8-4450-9f7a-eb259c9a86c9] - Node e41b2056-23e8-4450-9f7a-eb259c9a86c9[e41b2056-23e8-4450-9f7a-eb259c9a86c9] running status set to false 
[INFO ] 2024-03-29 17:31:26.529 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e41b2056-23e8-4450-9f7a-eb259c9a86c9] - Node e41b2056-23e8-4450-9f7a-eb259c9a86c9[e41b2056-23e8-4450-9f7a-eb259c9a86c9] schema data cleaned 
[INFO ] 2024-03-29 17:31:26.530 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e41b2056-23e8-4450-9f7a-eb259c9a86c9] - Node e41b2056-23e8-4450-9f7a-eb259c9a86c9[e41b2056-23e8-4450-9f7a-eb259c9a86c9] monitor closed 
[INFO ] 2024-03-29 17:31:26.531 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e41b2056-23e8-4450-9f7a-eb259c9a86c9] - Node e41b2056-23e8-4450-9f7a-eb259c9a86c9[e41b2056-23e8-4450-9f7a-eb259c9a86c9] close complete, cost 5 ms 
[INFO ] 2024-03-29 17:31:26.641 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:31:26.646 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:31:26.651 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 124 ms 
[INFO ] 2024-03-29 17:31:26.658 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-e41b2056-23e8-4450-9f7a-eb259c9a86c9 complete, cost 4458ms 
[INFO ] 2024-03-29 17:32:22.227 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.227 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.228 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c5eca227-1116-4771-8aea-6bc78d2d1eca] - Node c5eca227-1116-4771-8aea-6bc78d2d1eca[c5eca227-1116-4771-8aea-6bc78d2d1eca] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:22.228 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c5eca227-1116-4771-8aea-6bc78d2d1eca] - Node c5eca227-1116-4771-8aea-6bc78d2d1eca[c5eca227-1116-4771-8aea-6bc78d2d1eca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.228 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.229 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.305 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 17:32:22.358 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.358 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][4e17bb4a-2194-476c-a603-3044db58f45b] - Node 4e17bb4a-2194-476c-a603-3044db58f45b[4e17bb4a-2194-476c-a603-3044db58f45b] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:22.360 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.360 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.360 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][4e17bb4a-2194-476c-a603-3044db58f45b] - Node 4e17bb4a-2194-476c-a603-3044db58f45b[4e17bb4a-2194-476c-a603-3044db58f45b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.360 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 17:32:22.373 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53a85363 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53a85363 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53a85363 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:22.430 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:22.430 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5b8d47c1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5b8d47c1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5b8d47c1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:22.457 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][bb37f108-5281-410e-a5ec-439e9863016e] - Node bb37f108-5281-410e-a5ec-439e9863016e[bb37f108-5281-410e-a5ec-439e9863016e] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:22.457 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.461 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.461 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.463 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][bb37f108-5281-410e-a5ec-439e9863016e] - Node bb37f108-5281-410e-a5ec-439e9863016e[bb37f108-5281-410e-a5ec-439e9863016e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.463 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.539 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:22.539 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 17:32:22.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:22.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:22.566 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:22.567 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:22.586 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 72 ms 
[INFO ] 2024-03-29 17:32:22.586 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.587 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.587 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e97575c7-2d15-42b8-9790-9437551de0f5] - Node e97575c7-2d15-42b8-9790-9437551de0f5[e97575c7-2d15-42b8-9790-9437551de0f5] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:22.588 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:22.592 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:22.592 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e97575c7-2d15-42b8-9790-9437551de0f5] - Node e97575c7-2d15-42b8-9790-9437551de0f5[e97575c7-2d15-42b8-9790-9437551de0f5] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 17:32:22.632 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@67573cde error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@67573cde error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@67573cde error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:22.635 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:22.706 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@df081ab error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@df081ab error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@df081ab error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:22.717 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[ERROR] 2024-03-29 17:32:22.722 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:230)
	... 16 more

[INFO ] 2024-03-29 17:32:22.741 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:22.741 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:22.741 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:22.741 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:22.742 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 41 ms 
[INFO ] 2024-03-29 17:32:22.964 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:22.974 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:22.975 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:22.975 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:22.976 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:22.976 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 59 ms 
[INFO ] 2024-03-29 17:32:23.104 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7840b190-0686-4979-8a2c-f2a6ff003ff0] - Node 7840b190-0686-4979-8a2c-f2a6ff003ff0[7840b190-0686-4979-8a2c-f2a6ff003ff0] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:23.104 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.105 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.105 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7840b190-0686-4979-8a2c-f2a6ff003ff0] - Node 7840b190-0686-4979-8a2c-f2a6ff003ff0[7840b190-0686-4979-8a2c-f2a6ff003ff0] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 17:32:23.107 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 17:32:23.107 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.129 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.129 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.129 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.135 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7dfee1c0-e077-4c74-9ff8-204e8f0b805d] - Node 7dfee1c0-e077-4c74-9ff8-204e8f0b805d[7dfee1c0-e077-4c74-9ff8-204e8f0b805d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:23.140 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.140 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7dfee1c0-e077-4c74-9ff8-204e8f0b805d] - Node 7dfee1c0-e077-4c74-9ff8-204e8f0b805d[7dfee1c0-e077-4c74-9ff8-204e8f0b805d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.161 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 17:32:23.165 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:23.198 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@52bf9f54 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@52bf9f54 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@52bf9f54 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 17:32:23.199 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f8a6b40 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f8a6b40 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f8a6b40 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:23.264 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.264 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.265 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] - Node f8872ab3-e690-4b6e-a1ee-1a341bab7a8d[f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:23.265 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.265 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] - Node f8872ab3-e690-4b6e-a1ee-1a341bab7a8d[f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.265 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.287 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:23.340 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5f373a73 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5f373a73 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5f373a73 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:23.340 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:23.365 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:23.365 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:23.365 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:23.365 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:23.365 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 26 ms 
[INFO ] 2024-03-29 17:32:23.423 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.423 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:23.424 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e71e3c60-d1b6-4a2a-adaf-99846c68c797] - Node e71e3c60-d1b6-4a2a-adaf-99846c68c797[e71e3c60-d1b6-4a2a-adaf-99846c68c797] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:23.424 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e71e3c60-d1b6-4a2a-adaf-99846c68c797] - Node e71e3c60-d1b6-4a2a-adaf-99846c68c797[e71e3c60-d1b6-4a2a-adaf-99846c68c797] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.424 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.425 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:23.472 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:23.549 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5d4b1cec error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5d4b1cec error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5d4b1cec error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:23.562 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:23.574 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:23.574 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:23.574 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:23.575 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:23.577 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 33 ms 
[INFO ] 2024-03-29 17:32:23.731 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:23.732 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[ERROR] 2024-03-29 17:32:23.732 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:230)
	... 16 more

[INFO ] 2024-03-29 17:32:23.732 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:23.732 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:23.732 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:23.937 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 26 ms 
[INFO ] 2024-03-29 17:32:24.528 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:24.528 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:24.529 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][08c06790-df19-4511-bccc-12454603ae0c] - Node 08c06790-df19-4511-bccc-12454603ae0c[08c06790-df19-4511-bccc-12454603ae0c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:24.529 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:24.529 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:24.529 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][08c06790-df19-4511-bccc-12454603ae0c] - Node 08c06790-df19-4511-bccc-12454603ae0c[08c06790-df19-4511-bccc-12454603ae0c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:24.584 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:24.585 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e96c2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e96c2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e96c2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:24.766 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:24.766 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:24.767 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:24.767 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:24.767 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:24.768 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 26 ms 
[INFO ] 2024-03-29 17:32:24.904 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:24.904 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:24.908 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:24.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 4 ms 
[INFO ] 2024-03-29 17:32:24.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c5eca227-1116-4771-8aea-6bc78d2d1eca] - Node c5eca227-1116-4771-8aea-6bc78d2d1eca[c5eca227-1116-4771-8aea-6bc78d2d1eca] running status set to false 
[INFO ] 2024-03-29 17:32:24.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c5eca227-1116-4771-8aea-6bc78d2d1eca] - Node c5eca227-1116-4771-8aea-6bc78d2d1eca[c5eca227-1116-4771-8aea-6bc78d2d1eca] schema data cleaned 
[INFO ] 2024-03-29 17:32:24.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c5eca227-1116-4771-8aea-6bc78d2d1eca] - Node c5eca227-1116-4771-8aea-6bc78d2d1eca[c5eca227-1116-4771-8aea-6bc78d2d1eca] monitor closed 
[INFO ] 2024-03-29 17:32:24.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c5eca227-1116-4771-8aea-6bc78d2d1eca] - Node c5eca227-1116-4771-8aea-6bc78d2d1eca[c5eca227-1116-4771-8aea-6bc78d2d1eca] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:32:24.913 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-c5eca227-1116-4771-8aea-6bc78d2d1eca complete, cost 2802ms 
[INFO ] 2024-03-29 17:32:24.994 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:24.994 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][4e17bb4a-2194-476c-a603-3044db58f45b] - Node 4e17bb4a-2194-476c-a603-3044db58f45b[4e17bb4a-2194-476c-a603-3044db58f45b] running status set to false 
[INFO ] 2024-03-29 17:32:24.994 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:24.994 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][4e17bb4a-2194-476c-a603-3044db58f45b] - Node 4e17bb4a-2194-476c-a603-3044db58f45b[4e17bb4a-2194-476c-a603-3044db58f45b] schema data cleaned 
[INFO ] 2024-03-29 17:32:24.994 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:24.994 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][4e17bb4a-2194-476c-a603-3044db58f45b] - Node 4e17bb4a-2194-476c-a603-3044db58f45b[4e17bb4a-2194-476c-a603-3044db58f45b] monitor closed 
[INFO ] 2024-03-29 17:32:24.996 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 7 ms 
[INFO ] 2024-03-29 17:32:24.996 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][4e17bb4a-2194-476c-a603-3044db58f45b] - Node 4e17bb4a-2194-476c-a603-3044db58f45b[4e17bb4a-2194-476c-a603-3044db58f45b] close complete, cost 5 ms 
[INFO ] 2024-03-29 17:32:24.998 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-4e17bb4a-2194-476c-a603-3044db58f45b complete, cost 2709ms 
[INFO ] 2024-03-29 17:32:25.207 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:25.208 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e97575c7-2d15-42b8-9790-9437551de0f5] - Node e97575c7-2d15-42b8-9790-9437551de0f5[e97575c7-2d15-42b8-9790-9437551de0f5] running status set to false 
[INFO ] 2024-03-29 17:32:25.208 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.208 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e97575c7-2d15-42b8-9790-9437551de0f5] - Node e97575c7-2d15-42b8-9790-9437551de0f5[e97575c7-2d15-42b8-9790-9437551de0f5] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.208 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e97575c7-2d15-42b8-9790-9437551de0f5] - Node e97575c7-2d15-42b8-9790-9437551de0f5[e97575c7-2d15-42b8-9790-9437551de0f5] monitor closed 
[INFO ] 2024-03-29 17:32:25.208 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:25.210 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e97575c7-2d15-42b8-9790-9437551de0f5] - Node e97575c7-2d15-42b8-9790-9437551de0f5[e97575c7-2d15-42b8-9790-9437551de0f5] close complete, cost 10 ms 
[INFO ] 2024-03-29 17:32:25.210 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 14 ms 
[INFO ] 2024-03-29 17:32:25.266 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-e97575c7-2d15-42b8-9790-9437551de0f5 complete, cost 2695ms 
[INFO ] 2024-03-29 17:32:25.266 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:25.266 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:25.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][bb37f108-5281-410e-a5ec-439e9863016e] - Node bb37f108-5281-410e-a5ec-439e9863016e[bb37f108-5281-410e-a5ec-439e9863016e] running status set to false 
[INFO ] 2024-03-29 17:32:25.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][bb37f108-5281-410e-a5ec-439e9863016e] - Node bb37f108-5281-410e-a5ec-439e9863016e[bb37f108-5281-410e-a5ec-439e9863016e] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][bb37f108-5281-410e-a5ec-439e9863016e] - Node bb37f108-5281-410e-a5ec-439e9863016e[bb37f108-5281-410e-a5ec-439e9863016e] monitor closed 
[INFO ] 2024-03-29 17:32:25.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:25.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][bb37f108-5281-410e-a5ec-439e9863016e] - Node bb37f108-5281-410e-a5ec-439e9863016e[bb37f108-5281-410e-a5ec-439e9863016e] close complete, cost 7 ms 
[INFO ] 2024-03-29 17:32:25.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 13 ms 
[INFO ] 2024-03-29 17:32:25.269 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: null 
[INFO ] 2024-03-29 17:32:25.272 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: null 
[INFO ] 2024-03-29 17:32:25.272 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.272 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:25.272 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 17 ms 
[INFO ] 2024-03-29 17:32:25.360 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-bb37f108-5281-410e-a5ec-439e9863016e complete, cost 2860ms 
[INFO ] 2024-03-29 17:32:25.360 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:25.360 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][61a060cf-08e1-415f-b830-a7edf1aa89db] - Node 61a060cf-08e1-415f-b830-a7edf1aa89db[61a060cf-08e1-415f-b830-a7edf1aa89db] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:25.363 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:25.363 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][61a060cf-08e1-415f-b830-a7edf1aa89db] - Node 61a060cf-08e1-415f-b830-a7edf1aa89db[61a060cf-08e1-415f-b830-a7edf1aa89db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:25.364 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:25.364 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:25.456 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:25.457 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14623550 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14623550 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14623550 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:25.636 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:25.636 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:25.636 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:25.636 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.637 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:25.638 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 24 ms 
[INFO ] 2024-03-29 17:32:25.722 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:25.722 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.722 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:25.722 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:32:25.723 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7dfee1c0-e077-4c74-9ff8-204e8f0b805d] - Node 7dfee1c0-e077-4c74-9ff8-204e8f0b805d[7dfee1c0-e077-4c74-9ff8-204e8f0b805d] running status set to false 
[INFO ] 2024-03-29 17:32:25.723 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7dfee1c0-e077-4c74-9ff8-204e8f0b805d] - Node 7dfee1c0-e077-4c74-9ff8-204e8f0b805d[7dfee1c0-e077-4c74-9ff8-204e8f0b805d] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.723 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7dfee1c0-e077-4c74-9ff8-204e8f0b805d] - Node 7dfee1c0-e077-4c74-9ff8-204e8f0b805d[7dfee1c0-e077-4c74-9ff8-204e8f0b805d] monitor closed 
[INFO ] 2024-03-29 17:32:25.723 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7dfee1c0-e077-4c74-9ff8-204e8f0b805d] - Node 7dfee1c0-e077-4c74-9ff8-204e8f0b805d[7dfee1c0-e077-4c74-9ff8-204e8f0b805d] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:25.839 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-7dfee1c0-e077-4c74-9ff8-204e8f0b805d complete, cost 2648ms 
[INFO ] 2024-03-29 17:32:25.839 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:25.842 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.842 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:25.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 3 ms 
[INFO ] 2024-03-29 17:32:25.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] - Node f8872ab3-e690-4b6e-a1ee-1a341bab7a8d[f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] running status set to false 
[INFO ] 2024-03-29 17:32:25.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] - Node f8872ab3-e690-4b6e-a1ee-1a341bab7a8d[f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] schema data cleaned 
[INFO ] 2024-03-29 17:32:25.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] - Node f8872ab3-e690-4b6e-a1ee-1a341bab7a8d[f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] monitor closed 
[INFO ] 2024-03-29 17:32:25.846 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] - Node f8872ab3-e690-4b6e-a1ee-1a341bab7a8d[f8872ab3-e690-4b6e-a1ee-1a341bab7a8d] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:25.846 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-f8872ab3-e690-4b6e-a1ee-1a341bab7a8d complete, cost 2616ms 
[INFO ] 2024-03-29 17:32:26.158 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:26.159 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:26.159 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:26.161 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:32:26.161 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e71e3c60-d1b6-4a2a-adaf-99846c68c797] - Node e71e3c60-d1b6-4a2a-adaf-99846c68c797[e71e3c60-d1b6-4a2a-adaf-99846c68c797] running status set to false 
[INFO ] 2024-03-29 17:32:26.161 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e71e3c60-d1b6-4a2a-adaf-99846c68c797] - Node e71e3c60-d1b6-4a2a-adaf-99846c68c797[e71e3c60-d1b6-4a2a-adaf-99846c68c797] schema data cleaned 
[INFO ] 2024-03-29 17:32:26.161 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e71e3c60-d1b6-4a2a-adaf-99846c68c797] - Node e71e3c60-d1b6-4a2a-adaf-99846c68c797[e71e3c60-d1b6-4a2a-adaf-99846c68c797] monitor closed 
[INFO ] 2024-03-29 17:32:26.162 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][e71e3c60-d1b6-4a2a-adaf-99846c68c797] - Node e71e3c60-d1b6-4a2a-adaf-99846c68c797[e71e3c60-d1b6-4a2a-adaf-99846c68c797] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:26.162 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-e71e3c60-d1b6-4a2a-adaf-99846c68c797 complete, cost 2832ms 
[INFO ] 2024-03-29 17:32:26.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:26.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: null 
[INFO ] 2024-03-29 17:32:26.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: null 
[INFO ] 2024-03-29 17:32:26.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:26.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:26.259 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:32:26.260 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:26.260 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:26.260 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:26.260 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:26.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7840b190-0686-4979-8a2c-f2a6ff003ff0] - Node 7840b190-0686-4979-8a2c-f2a6ff003ff0[7840b190-0686-4979-8a2c-f2a6ff003ff0] running status set to false 
[INFO ] 2024-03-29 17:32:26.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7840b190-0686-4979-8a2c-f2a6ff003ff0] - Node 7840b190-0686-4979-8a2c-f2a6ff003ff0[7840b190-0686-4979-8a2c-f2a6ff003ff0] schema data cleaned 
[INFO ] 2024-03-29 17:32:26.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7840b190-0686-4979-8a2c-f2a6ff003ff0] - Node 7840b190-0686-4979-8a2c-f2a6ff003ff0[7840b190-0686-4979-8a2c-f2a6ff003ff0] monitor closed 
[INFO ] 2024-03-29 17:32:26.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][7840b190-0686-4979-8a2c-f2a6ff003ff0] - Node 7840b190-0686-4979-8a2c-f2a6ff003ff0[7840b190-0686-4979-8a2c-f2a6ff003ff0] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:26.470 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-7840b190-0686-4979-8a2c-f2a6ff003ff0 complete, cost 3220ms 
[INFO ] 2024-03-29 17:32:27.168 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][08c06790-df19-4511-bccc-12454603ae0c] - Node 08c06790-df19-4511-bccc-12454603ae0c[08c06790-df19-4511-bccc-12454603ae0c] running status set to false 
[INFO ] 2024-03-29 17:32:27.169 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:27.169 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][08c06790-df19-4511-bccc-12454603ae0c] - Node 08c06790-df19-4511-bccc-12454603ae0c[08c06790-df19-4511-bccc-12454603ae0c] schema data cleaned 
[INFO ] 2024-03-29 17:32:27.169 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:27.170 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][08c06790-df19-4511-bccc-12454603ae0c] - Node 08c06790-df19-4511-bccc-12454603ae0c[08c06790-df19-4511-bccc-12454603ae0c] monitor closed 
[INFO ] 2024-03-29 17:32:27.170 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:27.171 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][08c06790-df19-4511-bccc-12454603ae0c] - Node 08c06790-df19-4511-bccc-12454603ae0c[08c06790-df19-4511-bccc-12454603ae0c] close complete, cost 2 ms 
[INFO ] 2024-03-29 17:32:27.171 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 3 ms 
[INFO ] 2024-03-29 17:32:27.228 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-08c06790-df19-4511-bccc-12454603ae0c complete, cost 2682ms 
[INFO ] 2024-03-29 17:32:27.228 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:27.228 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:27.229 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:27.229 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c067cbb2-9ea1-4f32-833e-8e2bedcec207] - Node c067cbb2-9ea1-4f32-833e-8e2bedcec207[c067cbb2-9ea1-4f32-833e-8e2bedcec207] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:27.229 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:27.229 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c067cbb2-9ea1-4f32-833e-8e2bedcec207] - Node c067cbb2-9ea1-4f32-833e-8e2bedcec207[c067cbb2-9ea1-4f32-833e-8e2bedcec207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:27.244 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:27.439 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3322954 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3322954 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3322954 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:27.460 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:27.463 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:27.463 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:27.463 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:27.463 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:27.666 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 24 ms 
[INFO ] 2024-03-29 17:32:28.013 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:28.019 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][61a060cf-08e1-415f-b830-a7edf1aa89db] - Node 61a060cf-08e1-415f-b830-a7edf1aa89db[61a060cf-08e1-415f-b830-a7edf1aa89db] running status set to false 
[INFO ] 2024-03-29 17:32:28.021 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:28.021 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][61a060cf-08e1-415f-b830-a7edf1aa89db] - Node 61a060cf-08e1-415f-b830-a7edf1aa89db[61a060cf-08e1-415f-b830-a7edf1aa89db] schema data cleaned 
[INFO ] 2024-03-29 17:32:28.021 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:28.021 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][61a060cf-08e1-415f-b830-a7edf1aa89db] - Node 61a060cf-08e1-415f-b830-a7edf1aa89db[61a060cf-08e1-415f-b830-a7edf1aa89db] monitor closed 
[INFO ] 2024-03-29 17:32:28.021 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 19 ms 
[INFO ] 2024-03-29 17:32:28.021 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][61a060cf-08e1-415f-b830-a7edf1aa89db] - Node 61a060cf-08e1-415f-b830-a7edf1aa89db[61a060cf-08e1-415f-b830-a7edf1aa89db] close complete, cost 19 ms 
[INFO ] 2024-03-29 17:32:28.223 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-61a060cf-08e1-415f-b830-a7edf1aa89db complete, cost 2708ms 
[INFO ] 2024-03-29 17:32:28.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][5856fbec-a022-41a9-a54c-c38e10016b63] - Node 5856fbec-a022-41a9-a54c-c38e10016b63[5856fbec-a022-41a9-a54c-c38e10016b63] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:28.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:28.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:28.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:28.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][5856fbec-a022-41a9-a54c-c38e10016b63] - Node 5856fbec-a022-41a9-a54c-c38e10016b63[5856fbec-a022-41a9-a54c-c38e10016b63] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:28.258 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:28.311 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:28.492 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@626a1f27 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@626a1f27 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@626a1f27 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:28.511 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:28.515 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:28.516 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:28.516 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:28.516 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:28.516 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 24 ms 
[INFO ] 2024-03-29 17:32:29.834 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:29.835 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:29.835 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c067cbb2-9ea1-4f32-833e-8e2bedcec207] - Node c067cbb2-9ea1-4f32-833e-8e2bedcec207[c067cbb2-9ea1-4f32-833e-8e2bedcec207] running status set to false 
[INFO ] 2024-03-29 17:32:29.835 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c067cbb2-9ea1-4f32-833e-8e2bedcec207] - Node c067cbb2-9ea1-4f32-833e-8e2bedcec207[c067cbb2-9ea1-4f32-833e-8e2bedcec207] schema data cleaned 
[INFO ] 2024-03-29 17:32:29.835 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:29.835 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c067cbb2-9ea1-4f32-833e-8e2bedcec207] - Node c067cbb2-9ea1-4f32-833e-8e2bedcec207[c067cbb2-9ea1-4f32-833e-8e2bedcec207] monitor closed 
[INFO ] 2024-03-29 17:32:29.835 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 6 ms 
[INFO ] 2024-03-29 17:32:29.836 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][c067cbb2-9ea1-4f32-833e-8e2bedcec207] - Node c067cbb2-9ea1-4f32-833e-8e2bedcec207[c067cbb2-9ea1-4f32-833e-8e2bedcec207] close complete, cost 5 ms 
[INFO ] 2024-03-29 17:32:30.036 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-c067cbb2-9ea1-4f32-833e-8e2bedcec207 complete, cost 2713ms 
[INFO ] 2024-03-29 17:32:30.867 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:30.868 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][5856fbec-a022-41a9-a54c-c38e10016b63] - Node 5856fbec-a022-41a9-a54c-c38e10016b63[5856fbec-a022-41a9-a54c-c38e10016b63] running status set to false 
[INFO ] 2024-03-29 17:32:30.868 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:30.868 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][5856fbec-a022-41a9-a54c-c38e10016b63] - Node 5856fbec-a022-41a9-a54c-c38e10016b63[5856fbec-a022-41a9-a54c-c38e10016b63] schema data cleaned 
[INFO ] 2024-03-29 17:32:30.868 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:30.868 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][5856fbec-a022-41a9-a54c-c38e10016b63] - Node 5856fbec-a022-41a9-a54c-c38e10016b63[5856fbec-a022-41a9-a54c-c38e10016b63] monitor closed 
[INFO ] 2024-03-29 17:32:30.868 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 3 ms 
[INFO ] 2024-03-29 17:32:30.869 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][5856fbec-a022-41a9-a54c-c38e10016b63] - Node 5856fbec-a022-41a9-a54c-c38e10016b63[5856fbec-a022-41a9-a54c-c38e10016b63] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:32:31.081 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-5856fbec-a022-41a9-a54c-c38e10016b63 complete, cost 2664ms 
[INFO ] 2024-03-29 17:32:32.837 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:32.843 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:32.843 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] - Node f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1[f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:32.843 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:32.844 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:32.895 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] - Node f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1[f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:32.895 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:33.068 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@d5deff3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@d5deff3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@d5deff3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:33.068 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:33.084 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:33.084 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:33.084 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:33.084 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:33.284 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 17 ms 
[INFO ] 2024-03-29 17:32:35.283 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f495f7bd-8077-41a6-ac0a-28c206e06f00] - Node f495f7bd-8077-41a6-ac0a-28c206e06f00[f495f7bd-8077-41a6-ac0a-28c206e06f00] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:35.284 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:35.284 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:35.284 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f495f7bd-8077-41a6-ac0a-28c206e06f00] - Node f495f7bd-8077-41a6-ac0a-28c206e06f00[f495f7bd-8077-41a6-ac0a-28c206e06f00] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.284 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.284 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.322 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:35.322 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6288440b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6288440b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6288440b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:35.386 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:35.386 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] - Node d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56[d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:35.386 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:35.386 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] - Node d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56[d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.386 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.386 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.409 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:35.409 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@74ca723 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@74ca723 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@74ca723 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:35.443 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:35.443 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:35.443 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:35.447 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.447 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] - Node f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1[f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] running status set to false 
[INFO ] 2024-03-29 17:32:35.447 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] - Node f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1[f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] schema data cleaned 
[INFO ] 2024-03-29 17:32:35.447 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] - Node f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1[f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] monitor closed 
[INFO ] 2024-03-29 17:32:35.447 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] - Node f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1[f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:35.489 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-f1f6fa1f-f293-4ebc-b84f-09f34e0d6db1 complete, cost 2671ms 
[INFO ] 2024-03-29 17:32:35.489 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:35.510 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:35.510 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:35.510 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:35.510 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:35.511 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 23 ms 
[INFO ] 2024-03-29 17:32:35.705 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:35.705 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:35.705 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:35.705 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:35.705 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:35.705 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 26 ms 
[INFO ] 2024-03-29 17:32:37.197 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6ad6b3d8-81f1-4ef1-be12-99b4eb049497] - Node 6ad6b3d8-81f1-4ef1-be12-99b4eb049497[6ad6b3d8-81f1-4ef1-be12-99b4eb049497] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:37.197 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:37.197 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:37.197 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 17:32:37.197 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6ad6b3d8-81f1-4ef1-be12-99b4eb049497] - Node 6ad6b3d8-81f1-4ef1-be12-99b4eb049497[6ad6b3d8-81f1-4ef1-be12-99b4eb049497] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 17:32:37.198 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 17:32:37.244 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:37.244 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:37.253 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f0c34c83-8301-4403-b517-be7d08dba89d] - Node f0c34c83-8301-4403-b517-be7d08dba89d[f0c34c83-8301-4403-b517-be7d08dba89d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:37.254 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:37.254 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f0c34c83-8301-4403-b517-be7d08dba89d] - Node f0c34c83-8301-4403-b517-be7d08dba89d[f0c34c83-8301-4403-b517-be7d08dba89d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:37.254 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:37.270 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 17:32:37.270 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:37.308 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@247767f0 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@247767f0 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@247767f0 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 17:32:37.309 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@38fdb840 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@38fdb840 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@38fdb840 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:37.460 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:37.462 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:37.462 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:37.462 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:37.462 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:37.462 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 20 ms 
[INFO ] 2024-03-29 17:32:37.543 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:37.543 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:37.543 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] - Node 6e7c5e7d-964e-4003-8a9a-99c4f91ac82f[6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:37.543 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] - Node 6e7c5e7d-964e-4003-8a9a-99c4f91ac82f[6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:37.543 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:37.543 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:37.591 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:37.591 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6cdd1680 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6cdd1680 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6cdd1680 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:37.642 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:37.642 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:37.642 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:37.643 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:37.643 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:37.644 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 22 ms 
[INFO ] 2024-03-29 17:32:37.816 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:37.816 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:37.816 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:37.816 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:37.817 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:37.817 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 29 ms 
[INFO ] 2024-03-29 17:32:37.861 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:37.863 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f495f7bd-8077-41a6-ac0a-28c206e06f00] - Node f495f7bd-8077-41a6-ac0a-28c206e06f00[f495f7bd-8077-41a6-ac0a-28c206e06f00] running status set to false 
[INFO ] 2024-03-29 17:32:37.863 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f495f7bd-8077-41a6-ac0a-28c206e06f00] - Node f495f7bd-8077-41a6-ac0a-28c206e06f00[f495f7bd-8077-41a6-ac0a-28c206e06f00] schema data cleaned 
[INFO ] 2024-03-29 17:32:37.863 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:37.864 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f495f7bd-8077-41a6-ac0a-28c206e06f00] - Node f495f7bd-8077-41a6-ac0a-28c206e06f00[f495f7bd-8077-41a6-ac0a-28c206e06f00] monitor closed 
[INFO ] 2024-03-29 17:32:37.864 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:37.864 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 8 ms 
[INFO ] 2024-03-29 17:32:37.864 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f495f7bd-8077-41a6-ac0a-28c206e06f00] - Node f495f7bd-8077-41a6-ac0a-28c206e06f00[f495f7bd-8077-41a6-ac0a-28c206e06f00] close complete, cost 5 ms 
[INFO ] 2024-03-29 17:32:37.933 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-f495f7bd-8077-41a6-ac0a-28c206e06f00 complete, cost 2622ms 
[INFO ] 2024-03-29 17:32:37.933 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:37.934 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] - Node d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56[d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] running status set to false 
[INFO ] 2024-03-29 17:32:37.934 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:37.934 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] - Node d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56[d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] schema data cleaned 
[INFO ] 2024-03-29 17:32:37.934 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:37.934 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] - Node d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56[d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] monitor closed 
[INFO ] 2024-03-29 17:32:37.935 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 6 ms 
[INFO ] 2024-03-29 17:32:37.935 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] - Node d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56[d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56] close complete, cost 6 ms 
[INFO ] 2024-03-29 17:32:37.936 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-d66d0eb4-7a8d-4396-ba9e-62bf53ae2d56 complete, cost 2596ms 
[INFO ] 2024-03-29 17:32:39.844 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6ad6b3d8-81f1-4ef1-be12-99b4eb049497] - Node 6ad6b3d8-81f1-4ef1-be12-99b4eb049497[6ad6b3d8-81f1-4ef1-be12-99b4eb049497] running status set to false 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f0c34c83-8301-4403-b517-be7d08dba89d] - Node f0c34c83-8301-4403-b517-be7d08dba89d[f0c34c83-8301-4403-b517-be7d08dba89d] running status set to false 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6ad6b3d8-81f1-4ef1-be12-99b4eb049497] - Node 6ad6b3d8-81f1-4ef1-be12-99b4eb049497[6ad6b3d8-81f1-4ef1-be12-99b4eb049497] schema data cleaned 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f0c34c83-8301-4403-b517-be7d08dba89d] - Node f0c34c83-8301-4403-b517-be7d08dba89d[f0c34c83-8301-4403-b517-be7d08dba89d] schema data cleaned 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 2 ms 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f0c34c83-8301-4403-b517-be7d08dba89d] - Node f0c34c83-8301-4403-b517-be7d08dba89d[f0c34c83-8301-4403-b517-be7d08dba89d] monitor closed 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 3 ms 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6ad6b3d8-81f1-4ef1-be12-99b4eb049497] - Node 6ad6b3d8-81f1-4ef1-be12-99b4eb049497[6ad6b3d8-81f1-4ef1-be12-99b4eb049497] monitor closed 
[INFO ] 2024-03-29 17:32:39.845 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f0c34c83-8301-4403-b517-be7d08dba89d] - Node f0c34c83-8301-4403-b517-be7d08dba89d[f0c34c83-8301-4403-b517-be7d08dba89d] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:32:39.846 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6ad6b3d8-81f1-4ef1-be12-99b4eb049497] - Node 6ad6b3d8-81f1-4ef1-be12-99b4eb049497[6ad6b3d8-81f1-4ef1-be12-99b4eb049497] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:32:39.846 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-f0c34c83-8301-4403-b517-be7d08dba89d complete, cost 2655ms 
[INFO ] 2024-03-29 17:32:39.846 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-6ad6b3d8-81f1-4ef1-be12-99b4eb049497 complete, cost 2756ms 
[INFO ] 2024-03-29 17:32:39.909 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:39.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f281cf21-6c96-4b88-b2ae-90fd08aa3966] - Node f281cf21-6c96-4b88-b2ae-90fd08aa3966[f281cf21-6c96-4b88-b2ae-90fd08aa3966] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:32:39.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:32:39.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:39.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f281cf21-6c96-4b88-b2ae-90fd08aa3966] - Node f281cf21-6c96-4b88-b2ae-90fd08aa3966[f281cf21-6c96-4b88-b2ae-90fd08aa3966] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:39.910 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:32:39.977 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:32:39.979 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@35229213 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@35229213 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@35229213 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:32:40.117 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:40.117 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:40.117 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:40.117 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:40.122 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] - Node 6e7c5e7d-964e-4003-8a9a-99c4f91ac82f[6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] running status set to false 
[INFO ] 2024-03-29 17:32:40.122 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] - Node 6e7c5e7d-964e-4003-8a9a-99c4f91ac82f[6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] schema data cleaned 
[INFO ] 2024-03-29 17:32:40.122 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] - Node 6e7c5e7d-964e-4003-8a9a-99c4f91ac82f[6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] monitor closed 
[INFO ] 2024-03-29 17:32:40.126 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] - Node 6e7c5e7d-964e-4003-8a9a-99c4f91ac82f[6e7c5e7d-964e-4003-8a9a-99c4f91ac82f] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:32:40.127 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-6e7c5e7d-964e-4003-8a9a-99c4f91ac82f complete, cost 2630ms 
[INFO ] 2024-03-29 17:32:40.157 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:32:40.157 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:40.157 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:32:40.157 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:32:40.158 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:32:40.158 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 21 ms 
[INFO ] 2024-03-29 17:32:42.554 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f281cf21-6c96-4b88-b2ae-90fd08aa3966] - Node f281cf21-6c96-4b88-b2ae-90fd08aa3966[f281cf21-6c96-4b88-b2ae-90fd08aa3966] running status set to false 
[INFO ] 2024-03-29 17:32:42.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:32:42.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:32:42.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:32:42.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f281cf21-6c96-4b88-b2ae-90fd08aa3966] - Node f281cf21-6c96-4b88-b2ae-90fd08aa3966[f281cf21-6c96-4b88-b2ae-90fd08aa3966] schema data cleaned 
[INFO ] 2024-03-29 17:32:42.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f281cf21-6c96-4b88-b2ae-90fd08aa3966] - Node f281cf21-6c96-4b88-b2ae-90fd08aa3966[f281cf21-6c96-4b88-b2ae-90fd08aa3966] monitor closed 
[INFO ] 2024-03-29 17:32:42.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][f281cf21-6c96-4b88-b2ae-90fd08aa3966] - Node f281cf21-6c96-4b88-b2ae-90fd08aa3966[f281cf21-6c96-4b88-b2ae-90fd08aa3966] close complete, cost 33 ms 
[INFO ] 2024-03-29 17:32:42.559 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 35 ms 
[INFO ] 2024-03-29 17:32:42.763 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-f281cf21-6c96-4b88-b2ae-90fd08aa3966 complete, cost 2709ms 
[INFO ] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][9ab092e4-dfd9-4022-b835-7ec7e914fd62] - Node 9ab092e4-dfd9-4022-b835-7ec7e914fd62[9ab092e4-dfd9-4022-b835-7ec7e914fd62] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][9ab092e4-dfd9-4022-b835-7ec7e914fd62] - Node 9ab092e4-dfd9-4022-b835-7ec7e914fd62[9ab092e4-dfd9-4022-b835-7ec7e914fd62] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:34:57.734 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2545929 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2545929 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2545929 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:34:57.735 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:34:57.735 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:34:57.735 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:34:57.735 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:34:57.735 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:34:57.735 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 25 ms 
[INFO ] 2024-03-29 17:34:59.897 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:34:59.901 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][9ab092e4-dfd9-4022-b835-7ec7e914fd62] - Node 9ab092e4-dfd9-4022-b835-7ec7e914fd62[9ab092e4-dfd9-4022-b835-7ec7e914fd62] running status set to false 
[INFO ] 2024-03-29 17:34:59.901 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:34:59.901 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][9ab092e4-dfd9-4022-b835-7ec7e914fd62] - Node 9ab092e4-dfd9-4022-b835-7ec7e914fd62[9ab092e4-dfd9-4022-b835-7ec7e914fd62] schema data cleaned 
[INFO ] 2024-03-29 17:34:59.901 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][9ab092e4-dfd9-4022-b835-7ec7e914fd62] - Node 9ab092e4-dfd9-4022-b835-7ec7e914fd62[9ab092e4-dfd9-4022-b835-7ec7e914fd62] monitor closed 
[INFO ] 2024-03-29 17:34:59.901 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:34:59.901 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][9ab092e4-dfd9-4022-b835-7ec7e914fd62] - Node 9ab092e4-dfd9-4022-b835-7ec7e914fd62[9ab092e4-dfd9-4022-b835-7ec7e914fd62] close complete, cost 41 ms 
[INFO ] 2024-03-29 17:34:59.901 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 45 ms 
[INFO ] 2024-03-29 17:35:00.106 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-9ab092e4-dfd9-4022-b835-7ec7e914fd62 complete, cost 2738ms 
[INFO ] 2024-03-29 17:35:02.266 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:35:02.266 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][1c34a5d1-4fbb-41ce-b68f-2db1c534f305] - Node 1c34a5d1-4fbb-41ce-b68f-2db1c534f305[1c34a5d1-4fbb-41ce-b68f-2db1c534f305] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:35:02.266 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:35:02.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:35:02.267 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:35:02.280 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][1c34a5d1-4fbb-41ce-b68f-2db1c534f305] - Node 1c34a5d1-4fbb-41ce-b68f-2db1c534f305[1c34a5d1-4fbb-41ce-b68f-2db1c534f305] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:35:02.339 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:35:02.518 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@19c225cf error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@19c225cf error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@19c225cf error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:35:02.531 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:35:02.535 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:35:02.535 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:35:02.535 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:35:02.535 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:35:02.536 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 18 ms 
[INFO ] 2024-03-29 17:35:03.965 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][dae8ae67-515c-4959-82c1-a04b0c18e216] - Node dae8ae67-515c-4959-82c1-a04b0c18e216[dae8ae67-515c-4959-82c1-a04b0c18e216] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:35:03.965 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:35:03.965 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:35:03.966 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:35:03.966 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][dae8ae67-515c-4959-82c1-a04b0c18e216] - Node dae8ae67-515c-4959-82c1-a04b0c18e216[dae8ae67-515c-4959-82c1-a04b0c18e216] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:35:03.966 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] preload schema finished, cost 3 ms 
[INFO ] 2024-03-29 17:35:04.112 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:35:04.113 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2dcdf7b2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2dcdf7b2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2dcdf7b2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:35:04.225 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] running status set to false 
[INFO ] 2024-03-29 17:35:04.225 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:35:04.225 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6c933cec-0c5e-4029-88a1-36a102f2495e 
[INFO ] 2024-03-29 17:35:04.225 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] schema data cleaned 
[INFO ] 2024-03-29 17:35:04.225 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] monitor closed 
[INFO ] 2024-03-29 17:35:04.427 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[6c933cec-0c5e-4029-88a1-36a102f2495e] close complete, cost 21 ms 
[INFO ] 2024-03-29 17:35:04.889 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:35:04.889 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:35:04.889 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:35:04.889 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:35:04.891 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][1c34a5d1-4fbb-41ce-b68f-2db1c534f305] - Node 1c34a5d1-4fbb-41ce-b68f-2db1c534f305[1c34a5d1-4fbb-41ce-b68f-2db1c534f305] running status set to false 
[INFO ] 2024-03-29 17:35:04.891 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][1c34a5d1-4fbb-41ce-b68f-2db1c534f305] - Node 1c34a5d1-4fbb-41ce-b68f-2db1c534f305[1c34a5d1-4fbb-41ce-b68f-2db1c534f305] schema data cleaned 
[INFO ] 2024-03-29 17:35:04.891 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][1c34a5d1-4fbb-41ce-b68f-2db1c534f305] - Node 1c34a5d1-4fbb-41ce-b68f-2db1c534f305[1c34a5d1-4fbb-41ce-b68f-2db1c534f305] monitor closed 
[INFO ] 2024-03-29 17:35:04.893 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][1c34a5d1-4fbb-41ce-b68f-2db1c534f305] - Node 1c34a5d1-4fbb-41ce-b68f-2db1c534f305[1c34a5d1-4fbb-41ce-b68f-2db1c534f305] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:35:04.893 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-1c34a5d1-4fbb-41ce-b68f-2db1c534f305 complete, cost 2682ms 
[INFO ] 2024-03-29 17:35:06.640 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] running status set to false 
[INFO ] 2024-03-29 17:35:06.641 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] schema data cleaned 
[INFO ] 2024-03-29 17:35:06.641 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] monitor closed 
[INFO ] 2024-03-29 17:35:06.641 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[2fbd4ab4-193a-466f-9c27-8f547785548c] close complete, cost 1 ms 
[INFO ] 2024-03-29 17:35:06.645 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][dae8ae67-515c-4959-82c1-a04b0c18e216] - Node dae8ae67-515c-4959-82c1-a04b0c18e216[dae8ae67-515c-4959-82c1-a04b0c18e216] running status set to false 
[INFO ] 2024-03-29 17:35:06.645 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][dae8ae67-515c-4959-82c1-a04b0c18e216] - Node dae8ae67-515c-4959-82c1-a04b0c18e216[dae8ae67-515c-4959-82c1-a04b0c18e216] schema data cleaned 
[INFO ] 2024-03-29 17:35:06.646 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][dae8ae67-515c-4959-82c1-a04b0c18e216] - Node dae8ae67-515c-4959-82c1-a04b0c18e216[dae8ae67-515c-4959-82c1-a04b0c18e216] monitor closed 
[INFO ] 2024-03-29 17:35:06.646 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)][dae8ae67-515c-4959-82c1-a04b0c18e216] - Node dae8ae67-515c-4959-82c1-a04b0c18e216[dae8ae67-515c-4959-82c1-a04b0c18e216] close complete, cost 0 ms 
[INFO ] 2024-03-29 17:35:06.646 - [suppliers_import_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 66068a685c94df48bc70bd1e-dae8ae67-515c-4959-82c1-a04b0c18e216 complete, cost 2755ms 
