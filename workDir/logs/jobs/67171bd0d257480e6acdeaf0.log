[INFO ] 2024-10-28 15:52:32.664 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 15:52:32.665 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 15:52:33.962 - [测试Oracle共享挖掘] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-10-28 15:52:34.066 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 15:52:34.722 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 3 
[INFO ] 2024-10-28 15:52:34.723 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 3 
[INFO ] 2024-10-28 15:52:34.725 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 15:52:34.726 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 15:52:35.992 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 15:52:35.993 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 15:52:36.088 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 15:52:36.089 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 15:52:36.308 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 15:52:36.311 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 15:52:36.311 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 15:52:36.766 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300083,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 15:52:36.767 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 15:52:36.853 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 15:52:36.863 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 15:52:36.864 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 15:52:37.067 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 15:52:37.141 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:52:37.142 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 15:52:37.143 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 15:52:37.292 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 15:52:37.373 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:52:37.374 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 15:52:37.374 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 15:52:37.456 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:52:37.457 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 15:52:37.457 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:52:37.460 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 15:52:37.460 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:52:37.662 - [测试Oracle共享挖掘][OracleTest] - Starting stream read, table list: [0620_CAR_CLAIM_M, 0620_CUSTOMER_M, 0620_CAR_POLICY_M], offset: {"sortString":null,"offsetValue":null,"lastScn":127300083,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 15:52:37.974 - [测试Oracle共享挖掘][OracleTest] - total start mining scn: 127300083 
[INFO ] 2024-10-28 15:52:39.402 - [测试Oracle共享挖掘][OracleTest] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-10-28 15:52:51.132 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 15:52:51.146 - [测试Oracle共享挖掘][OracleTest] - Log Miner is shutting down... 
[INFO ] 2024-10-28 15:52:51.147 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 15:52:51.149 - [测试Oracle共享挖掘][OracleTest] - Log Miner has been closed! 
[INFO ] 2024-10-28 15:52:51.155 - [测试Oracle共享挖掘][OracleTest] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-10-28 15:52:51.220 - [测试Oracle共享挖掘][OracleTest] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:444)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-10-28 15:52:51.221 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:52:51.222 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:52:51.223 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 15:52:51.232 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 15:52:51.233 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 111 ms 
[INFO ] 2024-10-28 15:52:51.233 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 15:52:51.302 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:52:51.305 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:52:51.306 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 15:52:51.310 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 15:52:51.310 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 76 ms 
[INFO ] 2024-10-28 15:52:53.425 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 15:52:53.435 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@15236eeb 
[INFO ] 2024-10-28 15:52:53.578 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 15:52:53.578 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 15:52:53.619 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 15:52:53.619 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:52:53.822 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:53:07.903 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 15:53:07.904 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 15:53:08.297 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 15:53:08.433 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 15:53:08.444 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 3 
[INFO ] 2024-10-28 15:53:08.444 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 3 
[INFO ] 2024-10-28 15:53:08.445 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 15:53:08.654 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 15:53:10.326 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 15:53:10.338 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 15:53:10.375 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 15:53:10.376 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 15:53:10.439 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CUSTOMER_M" exists, skip auto create table 
[INFO ] 2024-10-28 15:53:10.439 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CUSTOMER_M has already exist. 
[INFO ] 2024-10-28 15:53:10.466 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 15:53:10.488 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 15:53:10.488 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 15:53:10.489 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_POLICY_M" exists, skip auto create table 
[INFO ] 2024-10-28 15:53:10.489 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_POLICY_M has already exist. 
[INFO ] 2024-10-28 15:53:10.854 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 15:53:10.855 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 15:53:10.944 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 15:53:10.962 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 15:53:11.136 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 15:53:11.136 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:53:11.183 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 15:53:11.187 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 15:53:11.189 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 15:53:11.276 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 15:53:11.377 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:53:11.377 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 15:53:11.377 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 15:53:11.566 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 15:53:11.574 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:53:11.574 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:53:11.574 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 15:53:11.574 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:53:11.652 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 15:53:11.652 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 15:53:11.653 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 15:53:11.686 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 15:53:11.686 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 15:53:11.689 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 15:53:11.689 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 15:53:11.691 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 15:53:11.691 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 15:53:11.699 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 3, partition size: 1, read thread number: 3 
[INFO ] 2024-10-28 15:53:11.699 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:53:11.737 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 15:53:11.738 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 15:53:11.749 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:53:11.752 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:53:11.766 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 15:53:11.769 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 15:53:11.769 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 15:53:11.769 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:53:11.773 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:53:11.774 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 15:53:11.775 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:53:11.784 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:53:11.792 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 15:53:11.793 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 15:53:11.799 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:53:11.799 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, 0620_CUSTOMER_M, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 15:53:11.799 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 15:53:11.804 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:55:24.836 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 15:55:24.886 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 15:55:24.886 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:55:24.889 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:55:24.893 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 15:55:24.894 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 15:55:24.895 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 73 ms 
[INFO ] 2024-10-28 15:55:24.895 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 15:55:24.910 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:55:24.911 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:55:24.911 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 15:55:24.911 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 15:55:24.911 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 23 ms 
[INFO ] 2024-10-28 15:55:28.968 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 15:55:28.969 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@40174ee7 
[INFO ] 2024-10-28 15:55:29.085 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 15:55:29.126 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 15:55:29.127 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 15:55:29.167 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:55:29.373 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:55:44.473 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 15:55:44.480 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 15:55:44.853 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 15:55:44.853 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 15:55:44.920 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 4 
[INFO ] 2024-10-28 15:55:44.921 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 4 
[INFO ] 2024-10-28 15:55:44.921 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 15:55:44.921 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 15:55:45.251 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 15:55:45.458 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 15:55:45.544 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 15:55:45.549 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 15:55:45.549 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 15:55:45.557 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"0620_CAR_CLAIM_M":{"batch_read_connector_status":"OVER"},"0620_CUSTOMER_M":{"batch_read_connector_status":"OVER"},"0620_CAR_POLICY_M":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 15:55:45.558 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 15:55:45.649 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0626_CUSTOMER_ 
[INFO ] 2024-10-28 15:55:45.650 - [测试Oracle共享挖掘][OracleTest] - Table 0626_CUSTOMER_ is going to be initial synced 
[INFO ] 2024-10-28 15:55:45.988 - [测试Oracle共享挖掘][OracleTest] - Table [0626_CUSTOMER_] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:55:45.988 - [测试Oracle共享挖掘][OracleTest] - Query table '0626_CUSTOMER_' counts: 1 
[INFO ] 2024-10-28 15:55:45.989 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:55:45.990 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 15:55:45.990 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:55:46.044 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 15:55:46.049 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 15:55:46.049 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 15:55:46.089 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 15:55:46.089 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 15:55:46.092 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 15:55:46.092 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 15:55:46.100 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 15:55:46.102 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 15:55:46.118 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 4, partition size: 1, read thread number: 4 
[INFO ] 2024-10-28 15:55:46.118 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:55:46.126 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 15:55:46.126 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 15:55:46.138 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:55:46.138 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:55:46.138 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 15:55:46.139 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 15:55:46.141 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 15:55:46.141 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:55:46.149 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:55:46.149 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:55:46.149 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 15:55:46.152 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:55:46.337 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 15:55:46.337 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 15:55:46.340 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 15:55:46.340 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 0 
[INFO ] 2024-10-28 15:55:46.340 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=0} 
[INFO ] 2024-10-28 15:55:46.345 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:55:46.349 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 15:55:46.350 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 15:55:46.353 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:55:46.353 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, 0620_CUSTOMER_M, 0626_CUSTOMER_, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 15:55:46.353 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 15:55:46.557 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:56:23.833 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 15:56:23.833 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 15:56:23.890 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:56:23.890 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:56:23.890 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 15:56:23.890 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 15:56:23.892 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 92 ms 
[INFO ] 2024-10-28 15:56:23.892 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 15:56:23.905 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:56:23.905 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:56:23.906 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 15:56:23.906 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 15:56:24.108 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 14 ms 
[INFO ] 2024-10-28 15:56:24.435 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 15:56:24.436 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6bba211c 
[INFO ] 2024-10-28 15:56:24.551 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 15:56:24.584 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 15:56:24.584 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 15:56:24.613 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:56:24.613 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:56:35.725 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 15:56:35.752 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 15:56:36.038 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 15:56:36.120 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 15:56:36.217 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 5 
[INFO ] 2024-10-28 15:56:36.217 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 5 
[INFO ] 2024-10-28 15:56:36.217 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 15:56:36.217 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 15:56:36.745 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 15:56:36.876 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 15:56:37.100 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 15:56:37.101 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 15:56:37.114 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 15:56:37.115 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"0626_CUSTOMER_":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 15:56:37.115 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 15:56:37.219 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0514 
[INFO ] 2024-10-28 15:56:37.219 - [测试Oracle共享挖掘][OracleTest] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-10-28 15:56:37.508 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 15:56:37.537 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-10-28 15:56:37.537 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:56:37.538 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 15:56:37.538 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 15:56:37.580 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 15:56:37.580 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 15:56:37.580 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 15:56:37.591 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 15:56:37.597 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 15:56:37.597 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 15:56:37.597 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 15:56:37.597 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 15:56:37.598 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 15:56:37.598 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 5, partition size: 1, read thread number: 5 
[INFO ] 2024-10-28 15:56:37.618 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:56:37.618 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 15:56:37.624 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 15:56:37.624 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:56:37.625 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:56:37.625 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 15:56:37.641 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:56:37.641 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 15:56:37.645 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 15:56:37.645 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:56:37.645 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 15:56:37.646 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:56:37.665 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:56:37.665 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 15:56:37.671 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 15:56:37.671 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 15:56:37.674 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 0 
[INFO ] 2024-10-28 15:56:37.674 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=0} 
[INFO ] 2024-10-28 15:56:37.915 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:56:38.021 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 15:56:38.022 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 15:56:38.052 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 15:56:38.056 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 15:56:38.056 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 0 
[INFO ] 2024-10-28 15:56:38.058 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=0} 
[INFO ] 2024-10-28 15:56:38.093 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 15:56:38.093 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 15:56:38.111 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 15:56:38.113 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, 0620_CUSTOMER_M, 0626_CUSTOMER_, AA_0514, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 15:56:38.122 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 15:56:38.126 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 15:57:32.289 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 15:57:32.372 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 15:57:32.373 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:57:32.373 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 15:57:32.373 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 15:57:32.373 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 15:57:32.375 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 105 ms 
[INFO ] 2024-10-28 15:57:32.375 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 15:57:32.395 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:57:32.395 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 15:57:32.395 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 15:57:32.396 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 15:57:32.396 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 21 ms 
[INFO ] 2024-10-28 15:57:34.711 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 15:57:34.712 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76bf22b8 
[INFO ] 2024-10-28 15:57:34.854 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 15:57:34.854 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 15:57:34.854 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 15:57:34.885 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:57:34.887 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 15:59:59.679 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 15:59:59.891 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 16:00:00.034 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 16:00:00.034 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 16:00:00.101 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 5 
[INFO ] 2024-10-28 16:00:00.101 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 5 
[INFO ] 2024-10-28 16:00:00.101 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 16:00:00.101 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 16:00:00.429 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 16:00:00.430 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 16:00:00.613 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 16:00:00.613 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 16:00:00.615 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 16:00:00.616 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"AA_0514":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 16:00:00.616 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 16:00:00.711 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 16:00:00.720 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 16:00:00.784 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 16:00:00.784 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 16:00:00.785 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 16:00:00.807 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 16:00:00.815 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 16:00:00.815 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 16:00:00.815 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 16:00:00.815 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 16:00:00.816 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 16:00:00.817 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 5, partition size: 1, read thread number: 5 
[INFO ] 2024-10-28 16:00:00.852 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:00:00.853 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 16:00:00.853 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 16:00:00.863 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:00:00.863 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:00:00.873 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 16:00:00.873 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 16:00:00.875 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 16:00:00.875 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:00:00.883 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:00:00.883 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:00:00.883 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 16:00:00.892 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:00:00.892 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 16:00:00.896 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 16:00:00.896 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:00:00.896 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 0 
[INFO ] 2024-10-28 16:00:00.897 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=0} 
[INFO ] 2024-10-28 16:00:00.919 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:00:00.919 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 16:00:00.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 16:00:00.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:00:00.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:00:00.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 0 
[INFO ] 2024-10-28 16:00:00.936 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=0} 
[INFO ] 2024-10-28 16:00:00.937 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 16:00:00.939 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 16:00:00.939 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:00:00.941 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, 0620_CUSTOMER_M, 0626_CUSTOMER_, AA_0514, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 16:00:00.941 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 16:00:01.145 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:00:31.694 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 16:00:31.744 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 16:00:31.744 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 16:00:31.744 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 16:00:31.744 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 16:00:31.745 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 16:00:31.746 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 52 ms 
[INFO ] 2024-10-28 16:00:31.746 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 16:00:31.763 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 16:00:31.763 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 16:00:31.763 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 16:00:31.763 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 16:00:31.965 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 18 ms 
[INFO ] 2024-10-28 16:00:35.069 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 16:00:35.070 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@50655267 
[INFO ] 2024-10-28 16:00:35.238 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 16:00:35.238 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 16:00:35.238 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 16:00:35.258 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 16:00:35.260 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 16:01:39.553 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 16:01:39.554 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 16:01:39.990 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 16:01:40.111 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 16:01:40.111 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 6 
[INFO ] 2024-10-28 16:01:40.111 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 6 
[INFO ] 2024-10-28 16:01:40.111 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 16:01:40.111 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 16:01:40.586 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 16:01:40.588 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 16:01:40.622 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA00PP" exists, skip auto create table 
[INFO ] 2024-10-28 16:01:40.626 - [测试Oracle共享挖掘][Mysql3307] - The table AA00PP has already exist. 
[INFO ] 2024-10-28 16:01:40.901 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 16:01:40.902 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 16:01:40.902 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 16:01:40.905 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"AA_0514":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 16:01:40.986 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 16:01:40.992 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA00PP 
[INFO ] 2024-10-28 16:01:40.993 - [测试Oracle共享挖掘][OracleTest] - Table AA00PP is going to be initial synced 
[INFO ] 2024-10-28 16:01:41.259 - [测试Oracle共享挖掘][OracleTest] - Table [AA00PP] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 16:01:41.319 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA00PP' counts: 1 
[INFO ] 2024-10-28 16:01:41.319 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 16:01:41.319 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 16:01:41.319 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 16:01:41.396 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 16:01:41.396 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 16:01:41.396 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 16:01:41.419 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 16:01:41.434 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 16:01:41.434 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 16:01:41.434 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 16:01:41.434 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 16:01:41.435 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 16:01:41.435 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 6, partition size: 1, read thread number: 6 
[INFO ] 2024-10-28 16:01:41.468 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:01:41.468 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 16:01:41.471 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 16:01:41.471 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:01:41.471 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 16:01:41.474 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:01:41.674 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f44c7ce59bbba57d77abd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA00PP, version=v2, tableName=AA00PP, externalStorageTableName=ExternalStorage_SHARE_CDC_-1655148570, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:01:41.748 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA00PP_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1655148570 
[INFO ] 2024-10-28 16:01:41.752 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA00PP] 
[INFO ] 2024-10-28 16:01:41.752 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA00PP) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:01:41.752 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA00PP' log, sequence: 0 
[INFO ] 2024-10-28 16:01:41.753 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA00PP filter: {sequence=0} 
[INFO ] 2024-10-28 16:01:41.781 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:01:41.781 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 16:01:41.784 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 16:01:41.784 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:01:41.784 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 0 
[INFO ] 2024-10-28 16:01:41.785 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=0} 
[INFO ] 2024-10-28 16:01:41.807 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:01:41.807 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 16:01:41.818 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 16:01:41.818 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:01:41.818 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 16:01:41.818 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:01:41.831 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:01:41.832 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 16:01:41.839 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 16:01:41.839 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:01:41.839 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 0 
[INFO ] 2024-10-28 16:01:41.847 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=0} 
[INFO ] 2024-10-28 16:01:41.847 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:01:41.856 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 16:01:41.856 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 16:01:41.861 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:01:41.862 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, AA00PP, AA_0514, 0620_CUSTOMER_M, 0626_CUSTOMER_, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 16:01:41.862 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 16:01:42.064 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:03:44.141 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 16:03:44.142 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 16:03:44.198 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 16:03:44.198 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 16:03:44.199 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 16:03:44.199 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 16:03:44.201 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 112 ms 
[INFO ] 2024-10-28 16:03:44.201 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 16:03:44.220 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 16:03:44.220 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 16:03:44.221 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 16:03:44.221 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 16:03:44.427 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 20 ms 
[INFO ] 2024-10-28 16:03:45.629 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 16:03:45.741 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@38a4a4ef 
[INFO ] 2024-10-28 16:03:45.741 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 16:03:45.773 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 16:03:45.773 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 16:03:45.809 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 16:03:45.811 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 16:14:29.782 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 16:14:29.783 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 16:14:30.023 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 16:14:30.162 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 16:14:30.163 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 7 
[INFO ] 2024-10-28 16:14:30.165 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 7 
[INFO ] 2024-10-28 16:14:30.165 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 16:14:30.166 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 16:14:30.495 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 16:14:30.495 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 16:14:30.849 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 16:14:30.849 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 16:14:30.859 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 16:14:30.859 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"AA00PP":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 16:14:30.859 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 16:14:31.001 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA00QQ 
[INFO ] 2024-10-28 16:14:31.206 - [测试Oracle共享挖掘][OracleTest] - Table AA00QQ is going to be initial synced 
[INFO ] 2024-10-28 16:14:31.268 - [测试Oracle共享挖掘][OracleTest] - Table [AA00QQ] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 16:14:31.268 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA00QQ' counts: 1 
[INFO ] 2024-10-28 16:14:31.269 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 16:14:31.269 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 16:14:31.269 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 16:14:31.327 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 16:14:31.329 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 16:14:31.329 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 16:14:31.345 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 16:14:31.353 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 16:14:31.353 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 16:14:31.353 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 16:14:31.353 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 16:14:31.353 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 16:14:31.354 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 7, partition size: 1, read thread number: 7 
[INFO ] 2024-10-28 16:14:31.372 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:14:31.372 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 16:14:31.380 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 16:14:31.380 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f44c7ce59bbba57d77abd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA00PP, version=v2, tableName=AA00PP, externalStorageTableName=ExternalStorage_SHARE_CDC_-1655148570, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:14:31.380 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:14:31.380 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 16:14:31.449 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:14:31.449 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA00PP_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1655148570 
[INFO ] 2024-10-28 16:14:31.449 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA00PP] 
[INFO ] 2024-10-28 16:14:31.451 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA00PP) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:14:31.451 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA00PP' log, sequence: 0 
[INFO ] 2024-10-28 16:14:31.452 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA00PP filter: {sequence=0} 
[INFO ] 2024-10-28 16:14:31.466 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f45c5ce59bbba57d78dd4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA00QQ, version=v2, tableName=AA00QQ, externalStorageTableName=ExternalStorage_SHARE_CDC_-1655148538, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:14:31.466 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA00QQ_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1655148538 
[INFO ] 2024-10-28 16:14:31.466 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA00QQ] 
[INFO ] 2024-10-28 16:14:31.468 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA00QQ) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:14:31.468 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA00QQ' log, sequence: 0 
[INFO ] 2024-10-28 16:14:31.474 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA00QQ filter: {sequence=0} 
[INFO ] 2024-10-28 16:14:31.475 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:14:31.484 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 16:14:31.484 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 16:14:31.487 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:14:31.487 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 0 
[INFO ] 2024-10-28 16:14:31.492 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=0} 
[INFO ] 2024-10-28 16:14:31.492 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:14:31.498 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 16:14:31.498 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 16:14:31.500 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:14:31.500 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 16:14:31.507 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:14:31.507 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:14:31.511 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 16:14:31.511 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 16:14:31.514 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:14:31.514 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 0 
[INFO ] 2024-10-28 16:14:31.520 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=0} 
[INFO ] 2024-10-28 16:14:31.520 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:14:31.527 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 16:14:31.527 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 16:14:31.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:14:31.530 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, AA00PP, AA00QQ, AA_0514, 0620_CUSTOMER_M, 0626_CUSTOMER_, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 16:14:31.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 16:14:31.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:14:48.283 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 16:14:48.363 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 16:14:48.364 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 16:14:48.364 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 16:14:48.364 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 16:14:48.365 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 16:14:48.367 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 104 ms 
[INFO ] 2024-10-28 16:14:48.367 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 16:14:48.381 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 16:14:48.381 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 16:14:48.381 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 16:14:48.382 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 16:14:48.582 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 15 ms 
[INFO ] 2024-10-28 16:14:51.366 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 16:14:51.366 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@70f2bb6a 
[INFO ] 2024-10-28 16:14:51.496 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 16:14:51.496 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 16:14:51.497 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 16:14:51.524 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 16:14:51.526 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 16:17:18.382 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 16:17:18.383 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 16:17:18.694 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 16:17:18.694 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 16:17:18.794 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 7 
[INFO ] 2024-10-28 16:17:18.794 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 7 
[INFO ] 2024-10-28 16:17:18.794 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 16:17:18.794 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 16:17:19.143 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 16:17:19.143 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 16:17:19.390 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 16:17:19.390 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 16:17:19.393 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 16:17:19.394 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"AA00QQ":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 16:17:19.394 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 16:17:19.474 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 16:17:19.474 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 16:17:19.513 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 16:17:19.513 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 16:17:19.513 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 16:17:19.525 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 16:17:19.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 16:17:19.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 16:17:19.531 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 16:17:19.531 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 16:17:19.531 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 16:17:19.531 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 7, partition size: 1, read thread number: 7 
[INFO ] 2024-10-28 16:17:19.538 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:17:19.538 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 16:17:19.541 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 16:17:19.541 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:17:19.541 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 16:17:19.542 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f44c7ce59bbba57d77abd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA00PP, version=v2, tableName=AA00PP, externalStorageTableName=ExternalStorage_SHARE_CDC_-1655148570, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:17:19.542 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:17:19.610 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA00PP_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1655148570 
[INFO ] 2024-10-28 16:17:19.617 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA00PP] 
[INFO ] 2024-10-28 16:17:19.617 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA00PP) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:17:19.617 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA00PP' log, sequence: 0 
[INFO ] 2024-10-28 16:17:19.620 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA00PP filter: {sequence=0} 
[INFO ] 2024-10-28 16:17:19.621 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f45c5ce59bbba57d78dd4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA00QQ, version=v2, tableName=AA00QQ, externalStorageTableName=ExternalStorage_SHARE_CDC_-1655148538, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:17:19.635 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA00QQ_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1655148538 
[INFO ] 2024-10-28 16:17:19.635 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA00QQ] 
[INFO ] 2024-10-28 16:17:19.639 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA00QQ) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:17:19.639 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA00QQ' log, sequence: 0 
[INFO ] 2024-10-28 16:17:19.639 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA00QQ filter: {sequence=0} 
[INFO ] 2024-10-28 16:17:19.640 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:17:19.652 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 16:17:19.652 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 16:17:19.655 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:17:19.655 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 0 
[INFO ] 2024-10-28 16:17:19.656 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=0} 
[INFO ] 2024-10-28 16:17:19.656 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:17:19.666 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 16:17:19.666 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 16:17:19.666 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:17:19.666 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 16:17:19.681 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 16:17:19.681 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:17:19.687 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 16:17:19.687 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 16:17:19.689 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 16:17:19.689 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 0 
[INFO ] 2024-10-28 16:17:19.690 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=0} 
[INFO ] 2024-10-28 16:17:19.704 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 16:17:19.704 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 16:17:19.707 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 16:17:19.707 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 16:17:19.708 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, AA00PP, AA00QQ, AA_0514, 0620_CUSTOMER_M, 0626_CUSTOMER_, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 16:17:19.708 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 16:17:19.916 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:12:28.896 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 17:12:28.897 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 17:12:28.929 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:12:28.929 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:12:28.929 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 17:12:28.932 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 17:12:28.933 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 64 ms 
[INFO ] 2024-10-28 17:12:28.933 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 17:12:28.944 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:12:28.944 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:12:28.945 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 17:12:28.945 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 17:12:29.146 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 13 ms 
[INFO ] 2024-10-28 17:12:29.586 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 17:12:29.710 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@27bf1b3f 
[INFO ] 2024-10-28 17:12:29.710 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 17:12:29.731 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 17:12:29.731 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 17:12:29.779 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:12:29.779 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:16:20.175 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 17:16:20.176 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 17:16:20.443 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 17:16:20.444 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 17:16:20.499 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 7 
[INFO ] 2024-10-28 17:16:20.499 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 7 
[INFO ] 2024-10-28 17:16:20.499 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:16:20.500 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:16:20.885 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 17:16:20.887 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 17:16:21.089 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 17:16:21.089 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 17:16:21.089 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 17:16:21.091 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"AA00QQ":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127300382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 17:16:21.092 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 17:16:21.222 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0401 
[INFO ] 2024-10-28 17:16:21.339 - [测试Oracle共享挖掘][OracleTest] - Table AA_0401 is going to be initial synced 
[INFO ] 2024-10-28 17:16:21.340 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0401] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:16:21.366 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0401' counts: 0 
[INFO ] 2024-10-28 17:16:21.366 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0328 
[INFO ] 2024-10-28 17:16:21.416 - [测试Oracle共享挖掘][OracleTest] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-10-28 17:16:21.417 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-10-28 17:16:21.441 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:16:21.441 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:16:21.441 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 17:16:21.442 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:16:21.501 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 17:16:21.501 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 17:16:21.501 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 17:16:21.526 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 17:16:21.526 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 17:16:21.526 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 17:16:21.526 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 17:16:21.526 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 17:16:21.526 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 17:16:21.532 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 7, partition size: 1, read thread number: 7 
[INFO ] 2024-10-28 17:16:21.532 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:16:21.547 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 17:16:21.547 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 17:16:21.550 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 17:16:21.551 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 17:16:21.554 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:16:21.554 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:16:21.880 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 17:16:21.885 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401] 
[INFO ] 2024-10-28 17:16:21.885 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 17:16:21.885 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 0 
[INFO ] 2024-10-28 17:16:21.892 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=0} 
[INFO ] 2024-10-28 17:16:21.921 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:16:21.921 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 17:16:21.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 17:16:21.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 17:16:21.937 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 0 
[INFO ] 2024-10-28 17:16:21.940 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=0} 
[INFO ] 2024-10-28 17:16:21.942 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:16:22.193 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 17:16:22.194 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0328] 
[INFO ] 2024-10-28 17:16:22.199 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 17:16:22.199 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 0 
[INFO ] 2024-10-28 17:16:22.208 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=0} 
[INFO ] 2024-10-28 17:16:22.208 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:16:22.218 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 17:16:22.218 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 17:16:22.219 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 17:16:22.226 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 17:16:22.226 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:16:22.233 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:16:22.233 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 17:16:22.235 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 17:16:22.235 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T07:53:10.469Z): 0 
[INFO ] 2024-10-28 17:16:22.235 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 0 
[INFO ] 2024-10-28 17:16:22.236 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=0} 
[INFO ] 2024-10-28 17:16:22.249 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:16:22.249 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 17:16:22.252 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 17:16:22.252 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T07:53:10.469Z): 1 
[INFO ] 2024-10-28 17:16:22.253 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, AA_0401, AA_0514, AA_0328, 0620_CUSTOMER_M, 0626_CUSTOMER_, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 17:16:22.253 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 17:16:22.253 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[WARN ] 2024-10-28 17:16:22.903 - [测试Oracle共享挖掘][Mysql3307] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[b78ac5d0-b306-4713-ac24-4582752de5b8], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-10-28 17:16:23.105 - [测试Oracle共享挖掘][Mysql3307] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[b78ac5d0-b306-4713-ac24-4582752de5b8], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-28 17:17:09.583 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 17:17:09.663 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 17:17:09.663 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:17:09.663 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:17:09.663 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 17:17:09.667 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 17:17:09.667 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 85 ms 
[INFO ] 2024-10-28 17:17:09.667 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 17:17:09.681 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:17:09.681 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:17:09.681 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 17:17:09.681 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 17:17:09.681 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 14 ms 
[INFO ] 2024-10-28 17:17:10.223 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 17:17:10.223 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3011cf47 
[INFO ] 2024-10-28 17:17:10.353 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 17:17:10.376 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 17:17:10.377 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 17:17:10.399 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:17:10.401 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:17:32.863 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 17:17:32.864 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 17:17:33.191 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 17:17:33.315 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 17:17:33.316 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 9 
[INFO ] 2024-10-28 17:17:33.316 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 9 
[INFO ] 2024-10-28 17:17:33.316 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:17:33.316 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:17:34.126 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 17:17:34.126 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 17:17:34.150 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.152 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 17:17:34.239 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0401" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.241 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0401 has already exist. 
[INFO ] 2024-10-28 17:17:34.331 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0514" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.335 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0514 has already exist. 
[INFO ] 2024-10-28 17:17:34.461 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0328" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.461 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0328 has already exist. 
[INFO ] 2024-10-28 17:17:34.492 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CUSTOMER_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.584 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CUSTOMER_M has already exist. 
[INFO ] 2024-10-28 17:17:34.584 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 17:17:34.584 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 17:17:34.584 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 17:17:34.604 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0626_CUSTOMER_" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.604 - [测试Oracle共享挖掘][Mysql3307] - The table 0626_CUSTOMER_ has already exist. 
[INFO ] 2024-10-28 17:17:34.701 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_POLICY_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.701 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_POLICY_M has already exist. 
[INFO ] 2024-10-28 17:17:34.739 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T1" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.739 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T1 has already exist. 
[INFO ] 2024-10-28 17:17:34.771 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T2" exists, skip auto create table 
[INFO ] 2024-10-28 17:17:34.771 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T2 has already exist. 
[INFO ] 2024-10-28 17:17:34.907 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127335177,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 17:17:34.908 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 17:17:35.017 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 17:17:35.017 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 17:17:35.105 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.105 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 17:17:35.133 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.133 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0401 
[INFO ] 2024-10-28 17:17:35.133 - [测试Oracle共享挖掘][OracleTest] - Table AA_0401 is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.172 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0401' counts: 0 
[INFO ] 2024-10-28 17:17:35.172 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0401] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.172 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0514 
[INFO ] 2024-10-28 17:17:35.172 - [测试Oracle共享挖掘][OracleTest] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.200 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-10-28 17:17:35.210 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.210 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0328 
[INFO ] 2024-10-28 17:17:35.237 - [测试Oracle共享挖掘][OracleTest] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.237 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-10-28 17:17:35.239 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.239 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 17:17:35.269 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.269 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 17:17:35.347 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.347 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T2 
[INFO ] 2024-10-28 17:17:35.421 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T2 is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.421 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T2' counts: 53 
[INFO ] 2024-10-28 17:17:35.632 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.633 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0626_CUSTOMER_ 
[INFO ] 2024-10-28 17:17:35.685 - [测试Oracle共享挖掘][OracleTest] - Table 0626_CUSTOMER_ is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.689 - [测试Oracle共享挖掘][OracleTest] - Table [0626_CUSTOMER_] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.689 - [测试Oracle共享挖掘][OracleTest] - Query table '0626_CUSTOMER_' counts: 1 
[INFO ] 2024-10-28 17:17:35.689 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T1 
[INFO ] 2024-10-28 17:17:35.689 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T1 is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.723 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T1' counts: 54 
[INFO ] 2024-10-28 17:17:35.903 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.906 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 17:17:35.962 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 17:17:35.963 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 17:17:35.966 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:17:35.967 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:17:35.967 - [测试Oracle共享挖掘][OracleTest] - Skip table [AA_0530_T1] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-28 17:17:35.967 - [测试Oracle共享挖掘][OracleTest] - Skip table [AA_0530_T2] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-28 17:17:35.967 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:17:35.967 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 17:17:35.967 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:17:36.048 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 17:17:36.048 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 17:17:36.048 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 17:17:36.073 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 17:17:36.073 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 17:17:36.073 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 17:17:36.073 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 17:17:36.073 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 17:17:36.073 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 17:17:36.077 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 9, partition size: 1, read thread number: 9 
[INFO ] 2024-10-28 17:17:36.077 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.100 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 17:17:36.100 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 17:17:36.104 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T09:17:34.584Z): 1 
[INFO ] 2024-10-28 17:17:36.104 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 17:17:36.106 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.176 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:17:36.177 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 17:17:36.177 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401] 
[INFO ] 2024-10-28 17:17:36.205 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.207 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T09:17:34.584Z): 1 
[INFO ] 2024-10-28 17:17:36.207 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 17:17:36.212 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 17:17:36.212 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 17:17:36.212 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 17:17:36.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T09:17:34.584Z): 1 
[INFO ] 2024-10-28 17:17:36.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 17:17:36.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 17:17:36.224 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.224 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 17:17:36.224 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0328] 
[INFO ] 2024-10-28 17:17:36.232 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T09:17:34.584Z): 1 
[INFO ] 2024-10-28 17:17:36.232 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 17:17:36.238 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 17:17:36.238 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.241 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 17:17:36.241 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 17:17:36.243 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T09:17:34.584Z): 1 
[INFO ] 2024-10-28 17:17:36.243 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 17:17:36.248 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:17:36.248 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.456 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 17:17:36.457 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2] 
[INFO ] 2024-10-28 17:17:36.458 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T09:17:34.584Z): 0 
[INFO ] 2024-10-28 17:17:36.459 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 0 
[INFO ] 2024-10-28 17:17:36.469 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=0} 
[INFO ] 2024-10-28 17:17:36.469 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.474 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 17:17:36.474 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 17:17:36.477 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T09:17:34.584Z): 1 
[INFO ] 2024-10-28 17:17:36.477 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 17:17:36.482 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 17:17:36.483 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.740 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 17:17:36.740 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T1] 
[INFO ] 2024-10-28 17:17:36.742 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T09:17:34.584Z): 0 
[INFO ] 2024-10-28 17:17:36.742 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 0 
[INFO ] 2024-10-28 17:17:36.759 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=0} 
[INFO ] 2024-10-28 17:17:36.759 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:17:36.765 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 17:17:36.765 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 17:17:36.768 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T09:17:34.584Z): 1 
[INFO ] 2024-10-28 17:17:36.768 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, AA_0401, AA_0514, AA_0328, 0620_CUSTOMER_M, AA_0530_T2, 0626_CUSTOMER_, AA_0530_T1, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 17:17:36.768 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 17:17:36.969 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:18:04.438 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 17:18:04.529 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 17:18:04.530 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:18:04.531 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:18:04.531 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 17:18:04.532 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 17:18:04.535 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 98 ms 
[INFO ] 2024-10-28 17:18:04.536 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 17:18:04.564 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:18:04.564 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:18:04.564 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 17:18:04.564 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 17:18:04.768 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 29 ms 
[INFO ] 2024-10-28 17:18:05.687 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 17:18:05.687 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3636199b 
[INFO ] 2024-10-28 17:18:05.833 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 17:18:05.833 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 17:18:05.833 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 17:18:05.856 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:18:05.860 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:21:13.057 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 17:21:13.059 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 17:21:13.246 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 17:21:13.284 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 17:21:13.334 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 10 
[INFO ] 2024-10-28 17:21:13.334 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 10 
[INFO ] 2024-10-28 17:21:13.334 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:21:13.334 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:21:14.222 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 17:21:14.222 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 17:21:14.242 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.242 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 17:21:14.288 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0401" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.288 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0401 has already exist. 
[INFO ] 2024-10-28 17:21:14.321 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0514" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.322 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0514 has already exist. 
[INFO ] 2024-10-28 17:21:14.361 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0328" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.363 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0328 has already exist. 
[INFO ] 2024-10-28 17:21:14.404 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CUSTOMER_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.404 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CUSTOMER_M has already exist. 
[INFO ] 2024-10-28 17:21:14.455 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T2" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.455 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T2 has already exist. 
[INFO ] 2024-10-28 17:21:14.483 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0626_CUSTOMER_" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.483 - [测试Oracle共享挖掘][Mysql3307] - The table 0626_CUSTOMER_ has already exist. 
[INFO ] 2024-10-28 17:21:14.510 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 17:21:14.510 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 17:21:14.510 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 17:21:14.521 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T1" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.522 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T1 has already exist. 
[INFO ] 2024-10-28 17:21:14.619 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_POLICY_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.620 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_POLICY_M has already exist. 
[INFO ] 2024-10-28 17:21:14.645 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0321" exists, skip auto create table 
[INFO ] 2024-10-28 17:21:14.646 - [测试Oracle共享挖掘][Mysql3307] - The table A_0321 has already exist. 
[INFO ] 2024-10-28 17:21:14.900 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127336587,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 17:21:14.901 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 17:21:15.037 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 17:21:15.040 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 17:21:15.040 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.131 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 17:21:15.150 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.151 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0401 
[INFO ] 2024-10-28 17:21:15.151 - [测试Oracle共享挖掘][OracleTest] - Table AA_0401 is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.183 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0401] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.183 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0401' counts: 0 
[INFO ] 2024-10-28 17:21:15.183 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0514 
[INFO ] 2024-10-28 17:21:15.183 - [测试Oracle共享挖掘][OracleTest] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.214 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.214 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-10-28 17:21:15.215 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0328 
[INFO ] 2024-10-28 17:21:15.215 - [测试Oracle共享挖掘][OracleTest] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.248 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-10-28 17:21:15.248 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.248 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 17:21:15.278 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.278 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 17:21:15.328 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.328 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T2 
[INFO ] 2024-10-28 17:21:15.362 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T2 is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.362 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T2' counts: 53 
[INFO ] 2024-10-28 17:21:15.458 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.458 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0626_CUSTOMER_ 
[INFO ] 2024-10-28 17:21:15.458 - [测试Oracle共享挖掘][OracleTest] - Table 0626_CUSTOMER_ is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.487 - [测试Oracle共享挖掘][OracleTest] - Query table '0626_CUSTOMER_' counts: 1 
[INFO ] 2024-10-28 17:21:15.487 - [测试Oracle共享挖掘][OracleTest] - Table [0626_CUSTOMER_] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.487 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T1 
[INFO ] 2024-10-28 17:21:15.512 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T1 is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.512 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T1' counts: 54 
[INFO ] 2024-10-28 17:21:15.610 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.610 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0321 
[INFO ] 2024-10-28 17:21:15.651 - [测试Oracle共享挖掘][OracleTest] - Table A_0321 is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.651 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0321' counts: 2 
[INFO ] 2024-10-28 17:21:15.656 - [测试Oracle共享挖掘][OracleTest] - Table [A_0321] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.656 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 17:21:15.698 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 17:21:15.699 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 17:21:15.731 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:21:15.731 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:21:15.732 - [测试Oracle共享挖掘][OracleTest] - Skip table [A_0321] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-28 17:21:15.732 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:21:15.812 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 17:21:15.812 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:21:15.812 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 17:21:15.813 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 17:21:15.813 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 17:21:15.834 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 17:21:15.834 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 17:21:15.834 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 17:21:15.834 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 17:21:15.834 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 17:21:15.834 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 17:21:15.834 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 10, partition size: 1, read thread number: 10 
[INFO ] 2024-10-28 17:21:15.849 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.849 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 17:21:15.852 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 17:21:15.852 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:15.852 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 17:21:15.853 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.853 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:15.921 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 17:21:15.921 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401] 
[INFO ] 2024-10-28 17:21:15.923 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:15.923 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 17:21:15.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:15.927 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.931 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 17:21:15.931 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 17:21:15.932 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:15.932 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 17:21:15.936 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:15.937 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.940 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 17:21:15.940 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0328] 
[INFO ] 2024-10-28 17:21:15.942 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:15.942 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 17:21:15.947 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:15.947 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.950 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 17:21:15.950 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 17:21:15.952 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:15.952 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 17:21:15.956 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:15.956 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.962 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 17:21:15.962 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2] 
[INFO ] 2024-10-28 17:21:15.963 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:15.963 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 1 
[INFO ] 2024-10-28 17:21:15.969 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:15.969 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.978 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 17:21:15.979 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 17:21:15.981 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:15.982 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 17:21:15.982 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:15.987 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:15.995 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 17:21:15.995 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T1] 
[INFO ] 2024-10-28 17:21:16.037 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f5788ce59bbba57da6482, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0321, version=v2, tableName=A_0321, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627441018, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:16.038 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:16.038 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 1 
[INFO ] 2024-10-28 17:21:16.185 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=1} 
[INFO ] 2024-10-28 17:21:16.186 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0321_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627441018 
[INFO ] 2024-10-28 17:21:16.186 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [A_0321] 
[INFO ] 2024-10-28 17:21:16.188 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0321) by timestamp(2024-10-28T09:21:14.509Z): 0 
[INFO ] 2024-10-28 17:21:16.193 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0321' log, sequence: 0 
[INFO ] 2024-10-28 17:21:16.195 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0321 filter: {sequence=0} 
[INFO ] 2024-10-28 17:21:16.204 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:21:16.205 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 17:21:16.207 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 17:21:16.207 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T09:21:14.509Z): 1 
[INFO ] 2024-10-28 17:21:16.207 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [0620_CAR_CLAIM_M, AA_0401, AA_0514, AA_0328, 0620_CUSTOMER_M, AA_0530_T2, 0626_CUSTOMER_, AA_0530_T1, A_0321, 0620_CAR_POLICY_M], data change syncing 
[INFO ] 2024-10-28 17:21:16.208 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 17:21:16.413 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:26:16.383 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 17:26:16.395 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 17:26:16.439 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:26:16.439 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:26:16.440 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 17:26:16.440 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 17:26:16.443 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 75 ms 
[INFO ] 2024-10-28 17:26:16.443 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 17:26:16.459 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:26:16.460 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:26:16.460 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 17:26:16.460 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 17:26:16.662 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 18 ms 
[INFO ] 2024-10-28 17:26:16.908 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 17:26:16.909 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2110f13e 
[INFO ] 2024-10-28 17:26:17.032 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 17:26:17.032 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 17:26:17.032 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 17:26:17.050 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:26:17.054 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:27:10.941 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 17:27:10.943 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 17:27:11.253 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 17:27:11.359 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 17:27:11.435 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 12 
[INFO ] 2024-10-28 17:27:11.438 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 12 
[INFO ] 2024-10-28 17:27:11.438 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 17:27:11.438 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 17:27:12.468 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 17:27:12.468 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 17:27:12.491 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T2" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.492 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T2 has already exist. 
[INFO ] 2024-10-28 17:27:12.537 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T1" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.538 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T1 has already exist. 
[INFO ] 2024-10-28 17:27:12.662 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_POLICY_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.664 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_POLICY_M has already exist. 
[INFO ] 2024-10-28 17:27:12.689 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.689 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 17:27:12.720 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0401" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.720 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0401 has already exist. 
[INFO ] 2024-10-28 17:27:12.810 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0514" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.810 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0514 has already exist. 
[INFO ] 2024-10-28 17:27:12.815 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 17:27:12.815 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 17:27:12.830 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 17:27:12.851 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0328" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.853 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0328 has already exist. 
[INFO ] 2024-10-28 17:27:12.898 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CUSTOMER_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.900 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CUSTOMER_M has already exist. 
[INFO ] 2024-10-28 17:27:12.952 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0321" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.955 - [测试Oracle共享挖掘][Mysql3307] - The table A_0321 has already exist. 
[INFO ] 2024-10-28 17:27:12.996 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0626_CUSTOMER_" exists, skip auto create table 
[INFO ] 2024-10-28 17:27:12.999 - [测试Oracle共享挖掘][Mysql3307] - The table 0626_CUSTOMER_ has already exist. 
[INFO ] 2024-10-28 17:27:13.090 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127338644,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 17:27:13.097 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 17:27:13.166 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 17:27:13.168 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T2 
[INFO ] 2024-10-28 17:27:13.168 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T2 is going to be initial synced 
[INFO ] 2024-10-28 17:27:13.294 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:13.516 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T2' counts: 53 
[INFO ] 2024-10-28 17:27:13.519 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T1 
[INFO ] 2024-10-28 17:27:13.519 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T1 is going to be initial synced 
[INFO ] 2024-10-28 17:27:13.581 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T1' counts: 54 
[INFO ] 2024-10-28 17:27:13.583 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:13.583 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_T3 
[INFO ] 2024-10-28 17:27:13.583 - [测试Oracle共享挖掘][OracleTest] - Table AA_T3 is going to be initial synced 
[INFO ] 2024-10-28 17:27:13.649 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_T3' counts: 0 
[INFO ] 2024-10-28 17:27:13.669 - [测试Oracle共享挖掘][OracleTest] - Table [AA_T3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:13.669 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 17:27:13.706 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 17:27:13.706 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 17:27:13.765 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:13.765 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 17:27:13.765 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 17:27:13.793 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 17:27:13.839 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:13.840 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0401 
[INFO ] 2024-10-28 17:27:13.840 - [测试Oracle共享挖掘][OracleTest] - Table AA_0401 is going to be initial synced 
[INFO ] 2024-10-28 17:27:13.874 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0401' counts: 0 
[INFO ] 2024-10-28 17:27:13.885 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0401] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:13.886 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0726 
[INFO ] 2024-10-28 17:27:13.886 - [测试Oracle共享挖掘][OracleTest] - Table A_0726 is going to be initial synced 
[INFO ] 2024-10-28 17:27:13.971 - [测试Oracle共享挖掘][OracleTest] - Table [A_0726] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:13.971 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0726' counts: 3 
[INFO ] 2024-10-28 17:27:13.971 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0514 
[INFO ] 2024-10-28 17:27:13.971 - [测试Oracle共享挖掘][OracleTest] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-10-28 17:27:14.002 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-10-28 17:27:14.002 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:14.002 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0328 
[INFO ] 2024-10-28 17:27:14.046 - [测试Oracle共享挖掘][OracleTest] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-10-28 17:27:14.047 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-10-28 17:27:14.047 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:14.047 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 17:27:14.047 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 17:27:14.103 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 17:27:14.103 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:14.103 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0321 
[INFO ] 2024-10-28 17:27:14.148 - [测试Oracle共享挖掘][OracleTest] - Table A_0321 is going to be initial synced 
[INFO ] 2024-10-28 17:27:14.150 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0321' counts: 2 
[INFO ] 2024-10-28 17:27:14.150 - [测试Oracle共享挖掘][OracleTest] - Table [A_0321] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:14.150 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0626_CUSTOMER_ 
[INFO ] 2024-10-28 17:27:14.150 - [测试Oracle共享挖掘][OracleTest] - Table 0626_CUSTOMER_ is going to be initial synced 
[INFO ] 2024-10-28 17:27:14.191 - [测试Oracle共享挖掘][OracleTest] - Table [0626_CUSTOMER_] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:27:14.191 - [测试Oracle共享挖掘][OracleTest] - Query table '0626_CUSTOMER_' counts: 1 
[INFO ] 2024-10-28 17:27:14.191 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:27:14.192 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 17:27:14.192 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:27:14.267 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 17:27:14.267 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 17:27:14.267 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 17:27:14.291 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 17:27:14.291 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 17:27:14.291 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 17:27:14.291 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 17:27:14.291 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 17:27:14.291 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 17:27:14.292 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 12, partition size: 1, read thread number: 12 
[INFO ] 2024-10-28 17:27:14.323 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.323 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 17:27:14.328 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2] 
[INFO ] 2024-10-28 17:27:14.328 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.330 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.330 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.351 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.352 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 17:27:14.352 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T1] 
[INFO ] 2024-10-28 17:27:14.353 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.353 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.358 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.361 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_T3, version=v2, tableName=AA_T3, externalStorageTableName=ExternalStorage_SHARE_CDC_2024864344, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.449 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_T3_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_2024864344 
[INFO ] 2024-10-28 17:27:14.450 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_T3] 
[INFO ] 2024-10-28 17:27:14.451 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_T3) by timestamp(2024-10-28T09:27:12.816Z): 0 
[INFO ] 2024-10-28 17:27:14.451 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_T3' log, sequence: 0 
[INFO ] 2024-10-28 17:27:14.452 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_T3 filter: {sequence=0} 
[INFO ] 2024-10-28 17:27:14.472 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.473 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 17:27:14.473 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 17:27:14.476 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.476 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.487 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.487 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.507 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 17:27:14.507 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 17:27:14.510 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.510 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.510 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.518 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.522 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 17:27:14.522 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401] 
[INFO ] 2024-10-28 17:27:14.525 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.525 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.531 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.532 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0726, version=v2, tableName=A_0726, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627437169, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.537 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0726_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627437169 
[INFO ] 2024-10-28 17:27:14.537 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [A_0726] 
[INFO ] 2024-10-28 17:27:14.546 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0726) by timestamp(2024-10-28T09:27:12.816Z): 0 
[INFO ] 2024-10-28 17:27:14.546 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0726' log, sequence: 0 
[INFO ] 2024-10-28 17:27:14.546 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0726 filter: {sequence=0} 
[INFO ] 2024-10-28 17:27:14.558 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.558 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 17:27:14.560 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 17:27:14.560 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.560 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.566 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.566 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.570 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 17:27:14.570 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0328] 
[INFO ] 2024-10-28 17:27:14.571 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.572 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.582 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.582 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.586 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 17:27:14.586 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 17:27:14.588 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.588 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.598 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.598 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f5788ce59bbba57da6482, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0321, version=v2, tableName=A_0321, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627441018, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.609 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0321_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627441018 
[INFO ] 2024-10-28 17:27:14.609 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [A_0321] 
[INFO ] 2024-10-28 17:27:14.622 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0321) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.637 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0321' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.639 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0321 filter: {sequence=1} 
[INFO ] 2024-10-28 17:27:14.639 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:27:14.647 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 17:27:14.651 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 17:27:14.652 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T09:27:12.816Z): 1 
[INFO ] 2024-10-28 17:27:14.654 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [AA_0530_T2, AA_0530_T1, AA_T3, 0620_CAR_POLICY_M, 0620_CAR_CLAIM_M, AA_0401, A_0726, AA_0514, AA_0328, 0620_CUSTOMER_M, A_0321, 0626_CUSTOMER_], data change syncing 
[INFO ] 2024-10-28 17:27:14.654 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 17:27:14.741 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 17:29:59.167 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 17:29:59.223 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 17:29:59.223 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:29:59.224 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 17:29:59.224 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 17:29:59.224 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 17:29:59.225 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 61 ms 
[INFO ] 2024-10-28 17:29:59.225 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 17:29:59.240 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:29:59.240 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 17:29:59.240 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 17:29:59.241 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 17:29:59.445 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 15 ms 
[INFO ] 2024-10-28 17:30:02.578 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 17:30:02.578 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1aae8c83 
[INFO ] 2024-10-28 17:30:02.747 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 17:30:02.748 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 17:30:02.748 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 17:30:02.770 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:30:02.772 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 17:31:22.058 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 17:31:22.166 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 17:31:22.328 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 17:31:22.390 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 17:31:23.078 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 14 
[INFO ] 2024-10-28 17:31:23.078 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 14 
[INFO ] 2024-10-28 17:31:23.078 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:31:23.078 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 17:31:24.094 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 17:31:24.094 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 17:31:24.330 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T2" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.330 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T2 has already exist. 
[INFO ] 2024-10-28 17:31:24.436 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T1" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.436 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T1 has already exist. 
[INFO ] 2024-10-28 17:31:24.436 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_T3" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.436 - [测试Oracle共享挖掘][Mysql3307] - The table AA_T3 has already exist. 
[INFO ] 2024-10-28 17:31:24.472 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 17:31:24.483 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 17:31:24.483 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 17:31:24.483 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_POLICY_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.483 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_POLICY_M has already exist. 
[INFO ] 2024-10-28 17:31:24.505 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.516 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 17:31:24.636 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0401" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.636 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0401 has already exist. 
[INFO ] 2024-10-28 17:31:24.681 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0726" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.681 - [测试Oracle共享挖掘][Mysql3307] - The table A_0726 has already exist. 
[INFO ] 2024-10-28 17:31:24.689 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127340847,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 17:31:24.689 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 17:31:24.712 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0514" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.723 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0514 has already exist. 
[INFO ] 2024-10-28 17:31:24.770 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0328" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.770 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0328 has already exist. 
[INFO ] 2024-10-28 17:31:24.815 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 17:31:24.815 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category 
[INFO ] 2024-10-28 17:31:24.815 - [测试Oracle共享挖掘][OracleTest] - Table Category is going to be initial synced 
[INFO ] 2024-10-28 17:31:24.821 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CUSTOMER_M" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.821 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CUSTOMER_M has already exist. 
[INFO ] 2024-10-28 17:31:24.865 - [测试Oracle共享挖掘][OracleTest] - Table [Category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:24.865 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0321" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.865 - [测试Oracle共享挖掘][Mysql3307] - The table A_0321 has already exist. 
[INFO ] 2024-10-28 17:31:24.868 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category' counts: 16 
[INFO ] 2024-10-28 17:31:24.868 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T2 
[INFO ] 2024-10-28 17:31:24.893 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T2 is going to be initial synced 
[INFO ] 2024-10-28 17:31:24.897 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T2' counts: 53 
[INFO ] 2024-10-28 17:31:24.906 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0626_CUSTOMER_" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.912 - [测试Oracle共享挖掘][Mysql3307] - The table 0626_CUSTOMER_ has already exist. 
[INFO ] 2024-10-28 17:31:24.950 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category1" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:24.958 - [测试Oracle共享挖掘][Mysql3307] - The table Category1 has already exist. 
[INFO ] 2024-10-28 17:31:24.986 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:24.988 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T1 
[INFO ] 2024-10-28 17:31:24.989 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T1 is going to be initial synced 
[INFO ] 2024-10-28 17:31:24.994 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category" exists, skip auto create table 
[INFO ] 2024-10-28 17:31:25.002 - [测试Oracle共享挖掘][Mysql3307] - The table Category has already exist. 
[INFO ] 2024-10-28 17:31:25.102 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T1' counts: 54 
[INFO ] 2024-10-28 17:31:25.103 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.103 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_T3 
[INFO ] 2024-10-28 17:31:25.103 - [测试Oracle共享挖掘][OracleTest] - Table AA_T3 is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.149 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_T3' counts: 0 
[INFO ] 2024-10-28 17:31:25.149 - [测试Oracle共享挖掘][OracleTest] - Table [AA_T3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.149 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 17:31:25.149 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.193 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 17:31:25.193 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.194 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 17:31:25.194 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.244 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 17:31:25.244 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.244 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category1 
[INFO ] 2024-10-28 17:31:25.244 - [测试Oracle共享挖掘][OracleTest] - Table Category1 is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.275 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category1' counts: 10 
[INFO ] 2024-10-28 17:31:25.287 - [测试Oracle共享挖掘][OracleTest] - Table [Category1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.287 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0401 
[INFO ] 2024-10-28 17:31:25.313 - [测试Oracle共享挖掘][OracleTest] - Table AA_0401 is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.313 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0401' counts: 0 
[INFO ] 2024-10-28 17:31:25.329 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0401] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.329 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0726 
[INFO ] 2024-10-28 17:31:25.367 - [测试Oracle共享挖掘][OracleTest] - Table A_0726 is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.367 - [测试Oracle共享挖掘][OracleTest] - Table [A_0726] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.368 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0726' counts: 3 
[INFO ] 2024-10-28 17:31:25.368 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0514 
[INFO ] 2024-10-28 17:31:25.394 - [测试Oracle共享挖掘][OracleTest] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.394 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-10-28 17:31:25.415 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.415 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0328 
[INFO ] 2024-10-28 17:31:25.441 - [测试Oracle共享挖掘][OracleTest] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.441 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-10-28 17:31:25.448 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.448 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 17:31:25.474 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.474 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 17:31:25.508 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.518 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0321 
[INFO ] 2024-10-28 17:31:25.518 - [测试Oracle共享挖掘][OracleTest] - Table A_0321 is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.554 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0321' counts: 2 
[INFO ] 2024-10-28 17:31:25.554 - [测试Oracle共享挖掘][OracleTest] - Table [A_0321] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.554 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0626_CUSTOMER_ 
[INFO ] 2024-10-28 17:31:25.554 - [测试Oracle共享挖掘][OracleTest] - Table 0626_CUSTOMER_ is going to be initial synced 
[INFO ] 2024-10-28 17:31:25.600 - [测试Oracle共享挖掘][OracleTest] - Query table '0626_CUSTOMER_' counts: 1 
[INFO ] 2024-10-28 17:31:25.600 - [测试Oracle共享挖掘][OracleTest] - Table [0626_CUSTOMER_] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 17:31:25.600 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:31:25.601 - [测试Oracle共享挖掘][OracleTest] - Skip table [Category1] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-28 17:31:25.601 - [测试Oracle共享挖掘][OracleTest] - Skip table [Category] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-28 17:31:25.601 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:31:25.601 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 17:31:25.653 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 17:31:25.653 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 17:31:25.653 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 17:31:25.653 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 17:31:25.672 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 17:31:25.672 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 17:31:25.672 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 17:31:25.672 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 17:31:25.672 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 17:31:25.672 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 17:31:25.677 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 14, partition size: 1, read thread number: 14 
[INFO ] 2024-10-28 17:31:25.677 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff30, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category, version=v2, tableName=Category, externalStorageTableName=ExternalStorage_SHARE_CDC_-1937428636, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.689 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1937428636 
[INFO ] 2024-10-28 17:31:25.689 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category] 
[INFO ] 2024-10-28 17:31:25.692 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.692 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.693 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.693 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.709 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 17:31:25.709 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2] 
[INFO ] 2024-10-28 17:31:25.712 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.712 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.717 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.717 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.721 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 17:31:25.721 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T1] 
[INFO ] 2024-10-28 17:31:25.722 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.722 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.731 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.731 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_T3, version=v2, tableName=AA_T3, externalStorageTableName=ExternalStorage_SHARE_CDC_2024864344, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.794 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_T3_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_2024864344 
[INFO ] 2024-10-28 17:31:25.794 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_T3] 
[INFO ] 2024-10-28 17:31:25.797 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_T3) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.797 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_T3' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.805 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_T3 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.805 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.808 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 17:31:25.808 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 17:31:25.810 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.810 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.812 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.819 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.819 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 17:31:25.821 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 17:31:25.821 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.821 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.828 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.828 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff31, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category1, version=v2, tableName=Category1, externalStorageTableName=ExternalStorage_SHARE_CDC_69254477, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.835 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_69254477 
[INFO ] 2024-10-28 17:31:25.835 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category1] 
[INFO ] 2024-10-28 17:31:25.837 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category1) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.837 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category1' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.844 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category1 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.844 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.847 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 17:31:25.847 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401] 
[INFO ] 2024-10-28 17:31:25.849 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.849 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.853 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.854 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0726, version=v2, tableName=A_0726, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627437169, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.858 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0726_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627437169 
[INFO ] 2024-10-28 17:31:25.858 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [A_0726] 
[INFO ] 2024-10-28 17:31:25.860 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0726) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.860 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0726' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.860 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0726 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.874 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.874 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 17:31:25.877 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 17:31:25.877 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.878 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.878 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.899 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.899 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 17:31:25.902 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0328] 
[INFO ] 2024-10-28 17:31:25.903 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.903 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.918 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.918 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.926 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 17:31:25.926 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 17:31:25.928 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.928 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.928 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.952 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f5788ce59bbba57da6482, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0321, version=v2, tableName=A_0321, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627441018, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.952 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0321_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627441018 
[INFO ] 2024-10-28 17:31:25.952 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [A_0321] 
[INFO ] 2024-10-28 17:31:25.955 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0321) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.955 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0321' log, sequence: 1 
[INFO ] 2024-10-28 17:31:25.963 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0321 filter: {sequence=1} 
[INFO ] 2024-10-28 17:31:25.971 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 17:31:25.971 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 17:31:25.973 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 17:31:25.973 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 17:31:25.974 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [Category, AA_0530_T2, AA_0530_T1, AA_T3, 0620_CAR_POLICY_M, 0620_CAR_CLAIM_M, Category1, AA_0401, A_0726, AA_0514, AA_0328, 0620_CUSTOMER_M, A_0321, 0626_CUSTOMER_], data change syncing 
[INFO ] 2024-10-28 17:31:25.974 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 17:31:26.182 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:09.348 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 18:37:09.390 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 18:37:09.390 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 18:37:09.390 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 18:37:09.390 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 18:37:09.391 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 18:37:09.391 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 44 ms 
[INFO ] 2024-10-28 18:37:09.411 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 18:37:09.411 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 18:37:09.411 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 18:37:09.411 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 18:37:09.411 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 18:37:09.614 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 20 ms 
[INFO ] 2024-10-28 18:37:12.508 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 18:37:12.508 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d57508e 
[INFO ] 2024-10-28 18:37:12.643 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 18:37:12.644 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 18:37:12.644 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 18:37:12.680 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 18:37:12.681 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 18:37:40.427 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 18:37:40.428 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 18:37:40.682 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 18:37:40.778 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 18:37:40.778 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 15 
[INFO ] 2024-10-28 18:37:40.778 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 15 
[INFO ] 2024-10-28 18:37:40.778 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 18:37:40.778 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 18:37:41.021 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 18:37:41.021 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 18:37:41.228 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 18:37:41.228 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 18:37:41.229 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 18:37:41.230 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"Category":{"batch_read_connector_status":"OVER"},"AA_0530_T2":{"batch_read_connector_status":"OVER"},"AA_0530_T1":{"batch_read_connector_status":"OVER"},"AA_T3":{"batch_read_connector_status":"OVER"},"0620_CAR_POLICY_M":{"batch_read_connector_status":"OVER"},"0620_CAR_CLAIM_M":{"batch_read_connector_status":"OVER"},"Category1":{"batch_read_connector_status":"OVER"},"AA_0401":{"batch_read_connector_status":"OVER"},"A_0726":{"batch_read_connector_status":"OVER"},"AA_0514":{"batch_read_connector_status":"OVER"},"AA_0328":{"batch_read_connector_status":"OVER"},"0620_CUSTOMER_M":{"batch_read_connector_status":"OVER"},"A_0321":{"batch_read_connector_status":"OVER"},"0626_CUSTOMER_":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127340847,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 18:37:41.230 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 18:37:41.326 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category_test 
[INFO ] 2024-10-28 18:37:41.327 - [测试Oracle共享挖掘][OracleTest] - Table Category_test is going to be initial synced 
[INFO ] 2024-10-28 18:37:41.569 - [测试Oracle共享挖掘][OracleTest] - Table [Category_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:37:41.598 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category_test' counts: 10 
[INFO ] 2024-10-28 18:37:41.598 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 18:37:41.598 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 18:37:41.598 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 18:37:41.701 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 18:37:41.702 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 18:37:41.702 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 18:37:41.734 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 18:37:41.743 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 18:37:41.743 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 18:37:41.743 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 18:37:41.744 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 18:37:41.744 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 18:37:41.744 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 15, partition size: 1, read thread number: 15 
[INFO ] 2024-10-28 18:37:41.949 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f6974ce59bbba57dee647, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category_test, version=v2, tableName=Category_test, externalStorageTableName=ExternalStorage_SHARE_CDC_1651887245, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:41.975 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_test_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1651887245 
[INFO ] 2024-10-28 18:37:41.993 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category_test] 
[INFO ] 2024-10-28 18:37:41.994 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff30, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category, version=v2, tableName=Category, externalStorageTableName=ExternalStorage_SHARE_CDC_-1937428636, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.000 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category_test) by timestamp(2024-10-28T09:31:24.472Z): 0 
[INFO ] 2024-10-28 18:37:42.000 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category_test' log, sequence: 0 
[INFO ] 2024-10-28 18:37:42.001 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category_test filter: {sequence=0} 
[INFO ] 2024-10-28 18:37:42.001 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1937428636 
[INFO ] 2024-10-28 18:37:42.008 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category] 
[INFO ] 2024-10-28 18:37:42.008 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.008 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.009 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.020 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.020 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 18:37:42.024 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2] 
[INFO ] 2024-10-28 18:37:42.024 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.024 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.025 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.030 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.030 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 18:37:42.034 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T1] 
[INFO ] 2024-10-28 18:37:42.034 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.034 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.035 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.115 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_T3, version=v2, tableName=AA_T3, externalStorageTableName=ExternalStorage_SHARE_CDC_2024864344, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.115 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_T3_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_2024864344 
[INFO ] 2024-10-28 18:37:42.115 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_T3] 
[INFO ] 2024-10-28 18:37:42.118 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_T3) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.118 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_T3' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.118 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_T3 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.123 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.123 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 18:37:42.124 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 18:37:42.125 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.125 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.127 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.127 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.129 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 18:37:42.129 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M] 
[INFO ] 2024-10-28 18:37:42.131 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.131 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.133 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.133 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff31, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category1, version=v2, tableName=Category1, externalStorageTableName=ExternalStorage_SHARE_CDC_69254477, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.137 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_69254477 
[INFO ] 2024-10-28 18:37:42.137 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category1] 
[INFO ] 2024-10-28 18:37:42.139 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category1) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.139 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category1' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.139 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category1 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.146 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.146 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 18:37:42.146 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401] 
[INFO ] 2024-10-28 18:37:42.148 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.148 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.148 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.155 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0726, version=v2, tableName=A_0726, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627437169, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.155 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0726_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627437169 
[INFO ] 2024-10-28 18:37:42.155 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [A_0726] 
[INFO ] 2024-10-28 18:37:42.159 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0726) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.159 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0726' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.161 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0726 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.161 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.165 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 18:37:42.165 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514] 
[INFO ] 2024-10-28 18:37:42.167 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.167 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.167 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.170 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.173 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 18:37:42.173 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0328] 
[INFO ] 2024-10-28 18:37:42.176 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.176 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.176 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.176 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.181 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 18:37:42.181 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M] 
[INFO ] 2024-10-28 18:37:42.183 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.183 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.183 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.186 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f5788ce59bbba57da6482, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0321, version=v2, tableName=A_0321, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627441018, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.192 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0321_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627441018 
[INFO ] 2024-10-28 18:37:42.192 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [A_0321] 
[INFO ] 2024-10-28 18:37:42.194 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0321) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.194 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0321' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.195 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0321 filter: {sequence=1} 
[INFO ] 2024-10-28 18:37:42.198 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:37:42.198 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 18:37:42.201 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_] 
[INFO ] 2024-10-28 18:37:42.201 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T09:31:24.472Z): 1 
[INFO ] 2024-10-28 18:37:42.203 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [Category_test, Category, AA_0530_T2, AA_0530_T1, AA_T3, 0620_CAR_POLICY_M, 0620_CAR_CLAIM_M, Category1, AA_0401, A_0726, AA_0514, AA_0328, 0620_CUSTOMER_M, A_0321, 0626_CUSTOMER_], data change syncing 
[INFO ] 2024-10-28 18:37:42.203 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 18:37:42.203 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:02.694 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 18:38:02.838 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 18:38:02.838 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 18:38:02.839 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 18:38:02.840 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 18:38:02.844 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 18:38:02.845 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 155 ms 
[INFO ] 2024-10-28 18:38:02.845 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 18:38:02.871 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 18:38:02.871 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 18:38:02.871 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 18:38:02.872 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 18:38:02.952 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 28 ms 
[INFO ] 2024-10-28 18:38:02.952 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 18:38:02.952 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2eadc6c8 
[INFO ] 2024-10-28 18:38:03.061 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 18:38:03.075 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 18:38:03.075 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 18:38:03.089 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 18:38:03.092 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 18:38:21.539 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 18:38:21.539 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 18:38:21.986 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 18:38:22.129 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 18:38:22.130 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 17 
[INFO ] 2024-10-28 18:38:22.130 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 17 
[INFO ] 2024-10-28 18:38:22.130 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 18:38:22.130 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 18:38:23.069 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 18:38:23.070 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 18:38:23.092 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category_test" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.093 - [测试Oracle共享挖掘][Mysql3307] - The table Category_test has already exist. 
[INFO ] 2024-10-28 18:38:23.160 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.161 - [测试Oracle共享挖掘][Mysql3307] - The table Category has already exist. 
[INFO ] 2024-10-28 18:38:23.218 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T2" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.218 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T2 has already exist. 
[INFO ] 2024-10-28 18:38:23.271 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T1" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.272 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T1 has already exist. 
[INFO ] 2024-10-28 18:38:23.298 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_T3" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.298 - [测试Oracle共享挖掘][Mysql3307] - The table AA_T3 has already exist. 
[INFO ] 2024-10-28 18:38:23.336 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_POLICY_M" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.336 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_POLICY_M has already exist. 
[INFO ] 2024-10-28 18:38:23.342 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 18:38:23.342 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 18:38:23.342 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 18:38:23.381 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.381 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 18:38:23.427 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category1" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.428 - [测试Oracle共享挖掘][Mysql3307] - The table Category1 has already exist. 
[INFO ] 2024-10-28 18:38:23.476 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0401" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.476 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0401 has already exist. 
[INFO ] 2024-10-28 18:38:23.522 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0726" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.523 - [测试Oracle共享挖掘][Mysql3307] - The table A_0726 has already exist. 
[INFO ] 2024-10-28 18:38:23.558 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0514" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.558 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0514 has already exist. 
[INFO ] 2024-10-28 18:38:23.596 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0328" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.597 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0328 has already exist. 
[INFO ] 2024-10-28 18:38:23.626 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CUSTOMER_M" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.626 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CUSTOMER_M has already exist. 
[INFO ] 2024-10-28 18:38:23.651 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127370153,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 18:38:23.651 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 18:38:23.658 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0321" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.658 - [测试Oracle共享挖掘][Mysql3307] - The table A_0321 has already exist. 
[INFO ] 2024-10-28 18:38:23.698 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0626_CUSTOMER_" exists, skip auto create table 
[INFO ] 2024-10-28 18:38:23.699 - [测试Oracle共享挖掘][Mysql3307] - The table 0626_CUSTOMER_ has already exist. 
[INFO ] 2024-10-28 18:38:23.750 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 18:38:23.755 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category_test 
[INFO ] 2024-10-28 18:38:23.755 - [测试Oracle共享挖掘][OracleTest] - Table Category_test is going to be initial synced 
[INFO ] 2024-10-28 18:38:23.823 - [测试Oracle共享挖掘][OracleTest] - Table [Category_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:23.824 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category_test' counts: 10 
[INFO ] 2024-10-28 18:38:23.825 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category 
[INFO ] 2024-10-28 18:38:23.825 - [测试Oracle共享挖掘][OracleTest] - Table Category is going to be initial synced 
[INFO ] 2024-10-28 18:38:23.863 - [测试Oracle共享挖掘][OracleTest] - Table [Category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:23.863 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category' counts: 16 
[INFO ] 2024-10-28 18:38:23.864 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T2 
[INFO ] 2024-10-28 18:38:23.896 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T2 is going to be initial synced 
[INFO ] 2024-10-28 18:38:23.896 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T2' counts: 53 
[INFO ] 2024-10-28 18:38:24.132 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.132 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T1 
[INFO ] 2024-10-28 18:38:24.168 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T1 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.168 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T1' counts: 54 
[INFO ] 2024-10-28 18:38:24.341 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.341 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_T3 
[INFO ] 2024-10-28 18:38:24.341 - [测试Oracle共享挖掘][OracleTest] - Table AA_T3 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.376 - [测试Oracle共享挖掘][OracleTest] - Table [AA_T3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.376 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_T3' counts: 0 
[INFO ] 2024-10-28 18:38:24.376 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 18:38:24.409 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.409 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 18:38:24.441 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.441 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 18:38:24.441 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.482 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 18:38:24.545 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.545 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category1 
[INFO ] 2024-10-28 18:38:24.545 - [测试Oracle共享挖掘][OracleTest] - Table Category1 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.580 - [测试Oracle共享挖掘][OracleTest] - Table [Category1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.580 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category1' counts: 10 
[INFO ] 2024-10-28 18:38:24.580 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0401 
[INFO ] 2024-10-28 18:38:24.581 - [测试Oracle共享挖掘][OracleTest] - Table AA_0401 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.612 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0401' counts: 0 
[INFO ] 2024-10-28 18:38:24.612 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0401] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.613 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0726 
[INFO ] 2024-10-28 18:38:24.613 - [测试Oracle共享挖掘][OracleTest] - Table A_0726 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.637 - [测试Oracle共享挖掘][OracleTest] - Table [A_0726] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.637 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0726' counts: 3 
[INFO ] 2024-10-28 18:38:24.638 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0514 
[INFO ] 2024-10-28 18:38:24.638 - [测试Oracle共享挖掘][OracleTest] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.687 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.687 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-10-28 18:38:24.687 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0328 
[INFO ] 2024-10-28 18:38:24.687 - [测试Oracle共享挖掘][OracleTest] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.757 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.757 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-10-28 18:38:24.757 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 18:38:24.757 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.878 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 18:38:24.878 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.878 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0321 
[INFO ] 2024-10-28 18:38:24.878 - [测试Oracle共享挖掘][OracleTest] - Table A_0321 is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.910 - [测试Oracle共享挖掘][OracleTest] - Table [A_0321] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.910 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0321' counts: 2 
[INFO ] 2024-10-28 18:38:24.911 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0626_CUSTOMER_ 
[INFO ] 2024-10-28 18:38:24.911 - [测试Oracle共享挖掘][OracleTest] - Table 0626_CUSTOMER_ is going to be initial synced 
[INFO ] 2024-10-28 18:38:24.941 - [测试Oracle共享挖掘][OracleTest] - Table [0626_CUSTOMER_] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:24.942 - [测试Oracle共享挖掘][OracleTest] - Query table '0626_CUSTOMER_' counts: 1 
[INFO ] 2024-10-28 18:38:24.942 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: F642_BMSQL_DISTRICT 
[INFO ] 2024-10-28 18:38:24.942 - [测试Oracle共享挖掘][OracleTest] - Table F642_BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-10-28 18:38:25.000 - [测试Oracle共享挖掘][OracleTest] - Query table 'F642_BMSQL_DISTRICT' counts: 10 
[INFO ] 2024-10-28 18:38:25.000 - [测试Oracle共享挖掘][OracleTest] - Table [F642_BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:25.000 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: JACK_002 
[INFO ] 2024-10-28 18:38:25.052 - [测试Oracle共享挖掘][OracleTest] - Table JACK_002 is going to be initial synced 
[INFO ] 2024-10-28 18:38:25.052 - [测试Oracle共享挖掘][OracleTest] - Table [JACK_002] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 18:38:25.052 - [测试Oracle共享挖掘][OracleTest] - Query table 'JACK_002' counts: 3 
[INFO ] 2024-10-28 18:38:25.052 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 18:38:25.053 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 18:38:25.053 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 18:38:25.108 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 18:38:25.108 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 18:38:25.108 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 18:38:25.140 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 18:38:25.140 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 18:38:25.140 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 18:38:25.140 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 18:38:25.140 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 18:38:25.140 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 18:38:25.145 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 17, partition size: 2, read thread number: 9 
[INFO ] 2024-10-28 18:38:25.145 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f6974ce59bbba57dee647, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category_test, version=v2, tableName=Category_test, externalStorageTableName=ExternalStorage_SHARE_CDC_1651887245, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.169 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_test_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1651887245 
[INFO ] 2024-10-28 18:38:25.169 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff30, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category, version=v2, tableName=Category, externalStorageTableName=ExternalStorage_SHARE_CDC_-1937428636, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.176 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1937428636 
[INFO ] 2024-10-28 18:38:25.176 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category_test, Category] 
[INFO ] 2024-10-28 18:38:25.179 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category_test) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.179 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category_test' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.180 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.180 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category_test filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.182 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 18:38:25.182 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.182 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.182 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.187 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.188 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 18:38:25.189 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2, AA_0530_T1] 
[INFO ] 2024-10-28 18:38:25.189 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.189 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.189 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.191 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_T3, version=v2, tableName=AA_T3, externalStorageTableName=ExternalStorage_SHARE_CDC_2024864344, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.191 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.191 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.191 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.257 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_T3_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_2024864344 
[INFO ] 2024-10-28 18:38:25.257 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.261 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 18:38:25.261 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_T3, 0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 18:38:25.262 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_T3) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.262 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_T3' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.264 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_T3 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.264 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.264 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.264 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.267 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.267 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 18:38:25.277 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff31, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category1, version=v2, tableName=Category1, externalStorageTableName=ExternalStorage_SHARE_CDC_69254477, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.277 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_69254477 
[INFO ] 2024-10-28 18:38:25.279 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M, Category1] 
[INFO ] 2024-10-28 18:38:25.279 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.279 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.280 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.281 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category1) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.281 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category1' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.281 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category1 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.281 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.286 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 18:38:25.286 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0726, version=v2, tableName=A_0726, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627437169, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.294 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0726_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627437169 
[INFO ] 2024-10-28 18:38:25.294 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401, A_0726] 
[INFO ] 2024-10-28 18:38:25.297 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.297 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [Category_test, Category, AA_0530_T2, AA_0530_T1, AA_T3, 0620_CAR_POLICY_M, 0620_CAR_CLAIM_M, Category1, AA_0401, A_0726, AA_0514, AA_0328, 0620_CUSTOMER_M, A_0321, 0626_CUSTOMER_, F642_BMSQL_DISTRICT, JACK_002], data change syncing 
[INFO ] 2024-10-28 18:38:25.297 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.297 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.302 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.304 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0726) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.304 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0726' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.304 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0726 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.304 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 18:38:25.311 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.311 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 18:38:25.316 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514, AA_0328] 
[INFO ] 2024-10-28 18:38:25.317 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.317 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.317 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.320 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.320 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.320 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.321 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.326 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 18:38:25.326 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f5788ce59bbba57da6482, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0321, version=v2, tableName=A_0321, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627441018, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.328 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0321_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627441018 
[INFO ] 2024-10-28 18:38:25.328 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M, A_0321] 
[INFO ] 2024-10-28 18:38:25.330 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.330 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.331 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.331 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.332 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0321) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.332 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0321' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.333 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0321 filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.333 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 18:38:25.528 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f699bce59bbba57def40a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_F642_BMSQL_DISTRICT, version=v2, tableName=F642_BMSQL_DISTRICT, externalStorageTableName=ExternalStorage_SHARE_CDC_-1906591243, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.528 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.F642_BMSQL_DISTRICT_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1906591243 
[INFO ] 2024-10-28 18:38:25.528 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_, F642_BMSQL_DISTRICT] 
[INFO ] 2024-10-28 18:38:25.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:38:25.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 18:38:25.530 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 18:38:25.531 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(F642_BMSQL_DISTRICT) by timestamp(2024-10-28T10:38:23.342Z): 0 
[INFO ] 2024-10-28 18:38:25.531 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'F642_BMSQL_DISTRICT' log, sequence: 0 
[INFO ] 2024-10-28 18:38:25.532 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by F642_BMSQL_DISTRICT filter: {sequence=0} 
[INFO ] 2024-10-28 18:38:25.532 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f699bce59bbba57def40b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_JACK_002, version=v2, tableName=JACK_002, externalStorageTableName=ExternalStorage_SHARE_CDC_1909821464, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:38:25.727 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.JACK_002_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1909821464 
[INFO ] 2024-10-28 18:38:25.727 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [JACK_002] 
[INFO ] 2024-10-28 18:38:25.730 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(JACK_002) by timestamp(2024-10-28T10:38:23.342Z): 0 
[INFO ] 2024-10-28 18:38:25.730 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'JACK_002' log, sequence: 0 
[INFO ] 2024-10-28 18:38:25.935 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by JACK_002 filter: {sequence=0} 
[INFO ] 2024-10-28 18:56:19.676 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 18:56:19.723 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 18:56:19.724 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 18:56:19.724 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 18:56:19.724 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 18:56:19.724 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 18:56:19.725 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 52 ms 
[INFO ] 2024-10-28 18:56:19.756 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 18:56:19.756 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 18:56:19.756 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 18:56:19.756 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 18:56:19.756 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 18:56:19.963 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 31 ms 
[INFO ] 2024-10-28 18:56:24.273 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 18:56:24.273 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@51205a6d 
[INFO ] 2024-10-28 18:56:24.393 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 18:56:24.415 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 18:56:24.415 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 18:56:24.433 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 18:56:24.436 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 18:56:52.489 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 18:56:52.492 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 18:56:52.775 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 18:56:52.856 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 18:56:53.066 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 17 
[INFO ] 2024-10-28 18:56:53.066 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 17 
[INFO ] 2024-10-28 18:56:53.066 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 18:56:53.066 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 18:56:53.550 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 18:56:53.552 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 18:56:53.787 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 18:56:53.788 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 18:56:53.788 - [测试Oracle共享挖掘][OracleTest] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-28 18:56:53.789 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {"Category_test":{"batch_read_connector_status":"OVER"},"Category":{"batch_read_connector_status":"OVER"},"AA_0530_T2":{"batch_read_connector_status":"OVER"},"AA_0530_T1":{"batch_read_connector_status":"OVER"},"AA_T3":{"batch_read_connector_status":"OVER"},"0620_CAR_POLICY_M":{"batch_read_connector_status":"OVER"},"0620_CAR_CLAIM_M":{"batch_read_connector_status":"OVER"},"Category1":{"batch_read_connector_status":"OVER"},"AA_0401":{"batch_read_connector_status":"OVER"},"A_0726":{"batch_read_connector_status":"OVER"},"AA_0514":{"batch_read_connector_status":"OVER"},"AA_0328":{"batch_read_connector_status":"OVER"},"0620_CUSTOMER_M":{"batch_read_connector_status":"OVER"},"A_0321":{"batch_read_connector_status":"OVER"},"0626_CUSTOMER_":{"batch_read_connector_status":"OVER"},"F642_BMSQL_DISTRICT":{"batch_read_connector_status":"OVER"},"JACK_002":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127370153,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 18:56:53.789 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 18:56:53.910 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 18:56:53.910 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 18:56:53.970 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 18:56:53.971 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 18:56:53.971 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 18:56:53.997 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 18:56:54.012 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 18:56:54.012 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 18:56:54.013 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 18:56:54.013 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 18:56:54.013 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 18:56:54.023 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 17, partition size: 2, read thread number: 9 
[INFO ] 2024-10-28 18:56:54.023 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f6974ce59bbba57dee647, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category_test, version=v2, tableName=Category_test, externalStorageTableName=ExternalStorage_SHARE_CDC_1651887245, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.036 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_test_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1651887245 
[INFO ] 2024-10-28 18:56:54.041 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff30, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category, version=v2, tableName=Category, externalStorageTableName=ExternalStorage_SHARE_CDC_-1937428636, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.042 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1937428636 
[INFO ] 2024-10-28 18:56:54.043 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category_test, Category] 
[INFO ] 2024-10-28 18:56:54.044 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category_test) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.044 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category_test' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.045 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category_test filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.046 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.046 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.046 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.046 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.048 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 18:56:54.052 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.054 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 18:56:54.054 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2, AA_0530_T1] 
[INFO ] 2024-10-28 18:56:54.057 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.057 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.057 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_T3, version=v2, tableName=AA_T3, externalStorageTableName=ExternalStorage_SHARE_CDC_2024864344, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.057 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.060 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.061 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.061 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.166 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_T3_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_2024864344 
[INFO ] 2024-10-28 18:56:54.169 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.171 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 18:56:54.171 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_T3, 0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 18:56:54.172 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_T3) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.172 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_T3' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.173 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_T3 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.176 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 18:56:54.180 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff31, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category1, version=v2, tableName=Category1, externalStorageTableName=ExternalStorage_SHARE_CDC_69254477, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.181 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_69254477 
[INFO ] 2024-10-28 18:56:54.183 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M, Category1] 
[INFO ] 2024-10-28 18:56:54.183 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.183 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.183 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.186 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.186 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category1) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.186 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category1' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.186 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category1 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.192 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 18:56:54.192 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0726, version=v2, tableName=A_0726, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627437169, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.195 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0726_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627437169 
[INFO ] 2024-10-28 18:56:54.195 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401, A_0726] 
[INFO ] 2024-10-28 18:56:54.207 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.208 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [Category_test, Category, AA_0530_T2, AA_0530_T1, AA_T3, 0620_CAR_POLICY_M, 0620_CAR_CLAIM_M, Category1, AA_0401, A_0726, AA_0514, AA_0328, 0620_CUSTOMER_M, A_0321, 0626_CUSTOMER_, F642_BMSQL_DISTRICT, JACK_002], data change syncing 
[INFO ] 2024-10-28 18:56:54.208 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.208 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0726) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0726' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0726 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.225 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 18:56:54.225 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.234 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 18:56:54.234 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514, AA_0328] 
[INFO ] 2024-10-28 18:56:54.243 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.243 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.247 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.247 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.249 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.249 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.249 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.259 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 18:56:54.260 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f5788ce59bbba57da6482, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0321, version=v2, tableName=A_0321, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627441018, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.263 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0321_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627441018 
[INFO ] 2024-10-28 18:56:54.264 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M, A_0321] 
[INFO ] 2024-10-28 18:56:54.269 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.269 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.269 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.269 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.272 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0321) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.273 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0321' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.273 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0321 filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.274 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 18:56:54.291 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f699bce59bbba57def40a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_F642_BMSQL_DISTRICT, version=v2, tableName=F642_BMSQL_DISTRICT, externalStorageTableName=ExternalStorage_SHARE_CDC_-1906591243, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.291 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.F642_BMSQL_DISTRICT_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1906591243 
[INFO ] 2024-10-28 18:56:54.293 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_, F642_BMSQL_DISTRICT] 
[INFO ] 2024-10-28 18:56:54.293 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T10:38:23.342Z): 1 
[INFO ] 2024-10-28 18:56:54.293 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 18:56:54.294 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 18:56:54.296 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(F642_BMSQL_DISTRICT) by timestamp(2024-10-28T10:38:23.342Z): 0 
[INFO ] 2024-10-28 18:56:54.296 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'F642_BMSQL_DISTRICT' log, sequence: 0 
[INFO ] 2024-10-28 18:56:54.302 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by F642_BMSQL_DISTRICT filter: {sequence=0} 
[INFO ] 2024-10-28 18:56:54.302 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f699bce59bbba57def40b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_JACK_002, version=v2, tableName=JACK_002, externalStorageTableName=ExternalStorage_SHARE_CDC_1909821464, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 18:56:54.306 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.JACK_002_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1909821464 
[INFO ] 2024-10-28 18:56:54.306 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [JACK_002] 
[INFO ] 2024-10-28 18:56:54.309 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(JACK_002) by timestamp(2024-10-28T10:38:23.342Z): 0 
[INFO ] 2024-10-28 18:56:54.309 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'JACK_002' log, sequence: 0 
[INFO ] 2024-10-28 18:56:54.310 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by JACK_002 filter: {sequence=0} 
[INFO ] 2024-10-28 19:06:16.659 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 19:06:16.708 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 19:06:16.708 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 19:06:16.708 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 19:06:16.708 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 19:06:16.713 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 19:06:16.713 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 65 ms 
[INFO ] 2024-10-28 19:06:16.713 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 19:06:16.724 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 19:06:16.724 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 19:06:16.724 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 19:06:16.724 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 19:06:16.925 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 11 ms 
[INFO ] 2024-10-28 19:06:20.991 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 19:06:20.991 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@12a57a4a 
[INFO ] 2024-10-28 19:06:21.124 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 19:06:21.124 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 19:06:21.124 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 19:06:21.155 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 19:06:21.157 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 19:06:57.428 - [测试Oracle共享挖掘] - Start task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘) 
[INFO ] 2024-10-28 19:06:57.429 - [测试Oracle共享挖掘] - Task initialization... 
[INFO ] 2024-10-28 19:06:57.804 - [测试Oracle共享挖掘] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-28 19:06:57.804 - [测试Oracle共享挖掘] - The engine receives 测试Oracle共享挖掘 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-28 19:06:57.887 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] start preload schema,table counts: 17 
[INFO ] 2024-10-28 19:06:57.887 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] start preload schema,table counts: 17 
[INFO ] 2024-10-28 19:06:57.887 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] preload schema finished, cost 1 ms 
[INFO ] 2024-10-28 19:06:57.887 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-28 19:06:58.695 - [测试Oracle共享挖掘][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-28 19:06:58.695 - [测试Oracle共享挖掘][Mysql3307] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-28 19:06:58.719 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category_test" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.719 - [测试Oracle共享挖掘][Mysql3307] - The table Category_test has already exist. 
[INFO ] 2024-10-28 19:06:58.789 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.823 - [测试Oracle共享挖掘][Mysql3307] - The table Category has already exist. 
[INFO ] 2024-10-28 19:06:58.823 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T2" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.823 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T2 has already exist. 
[INFO ] 2024-10-28 19:06:58.853 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0530_T1" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.853 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0530_T1 has already exist. 
[INFO ] 2024-10-28 19:06:58.878 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_T3" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.878 - [测试Oracle共享挖掘][Mysql3307] - The table AA_T3 has already exist. 
[INFO ] 2024-10-28 19:06:58.878 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" read batch size: 100 
[INFO ] 2024-10-28 19:06:58.878 - [测试Oracle共享挖掘][OracleTest] - Source node "OracleTest" event queue capacity: 200 
[INFO ] 2024-10-28 19:06:58.904 - [测试Oracle共享挖掘][OracleTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-28 19:06:58.904 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_POLICY_M" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.905 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_POLICY_M has already exist. 
[INFO ] 2024-10-28 19:06:58.936 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CAR_CLAIM_M" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.967 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CAR_CLAIM_M has already exist. 
[INFO ] 2024-10-28 19:06:58.967 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.Category1" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:58.967 - [测试Oracle共享挖掘][Mysql3307] - The table Category1 has already exist. 
[INFO ] 2024-10-28 19:06:58.992 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0401" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.032 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0401 has already exist. 
[INFO ] 2024-10-28 19:06:59.032 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0726" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.032 - [测试Oracle共享挖掘][Mysql3307] - The table A_0726 has already exist. 
[INFO ] 2024-10-28 19:06:59.061 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0514" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.061 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0514 has already exist. 
[INFO ] 2024-10-28 19:06:59.106 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.AA_0328" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.106 - [测试Oracle共享挖掘][Mysql3307] - The table AA_0328 has already exist. 
[INFO ] 2024-10-28 19:06:59.139 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0620_CUSTOMER_M" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.139 - [测试Oracle共享挖掘][Mysql3307] - The table 0620_CUSTOMER_M has already exist. 
[INFO ] 2024-10-28 19:06:59.170 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.A_0321" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.171 - [测试Oracle共享挖掘][Mysql3307] - The table A_0321 has already exist. 
[INFO ] 2024-10-28 19:06:59.196 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.0626_CUSTOMER_" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.197 - [测试Oracle共享挖掘][Mysql3307] - The table 0626_CUSTOMER_ has already exist. 
[INFO ] 2024-10-28 19:06:59.227 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.F642_BMSQL_DISTRICT" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.227 - [测试Oracle共享挖掘][Mysql3307] - The table F642_BMSQL_DISTRICT has already exist. 
[INFO ] 2024-10-28 19:06:59.255 - [测试Oracle共享挖掘][Mysql3307] - Table "test2.JACK_002" exists, skip auto create table 
[INFO ] 2024-10-28 19:06:59.255 - [测试Oracle共享挖掘][Mysql3307] - The table JACK_002 has already exist. 
[INFO ] 2024-10-28 19:06:59.358 - [测试Oracle共享挖掘][OracleTest] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":127387117,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-28 19:06:59.358 - [测试Oracle共享挖掘][OracleTest] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-28 19:06:59.439 - [测试Oracle共享挖掘][OracleTest] - Initial sync started 
[INFO ] 2024-10-28 19:06:59.439 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category_test 
[INFO ] 2024-10-28 19:06:59.528 - [测试Oracle共享挖掘][OracleTest] - Table Category_test is going to be initial synced 
[INFO ] 2024-10-28 19:06:59.528 - [测试Oracle共享挖掘][OracleTest] - Table [Category_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:06:59.645 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category_test' counts: 10 
[INFO ] 2024-10-28 19:06:59.645 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category 
[INFO ] 2024-10-28 19:06:59.646 - [测试Oracle共享挖掘][OracleTest] - Table Category is going to be initial synced 
[INFO ] 2024-10-28 19:06:59.729 - [测试Oracle共享挖掘][OracleTest] - Table [Category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:06:59.729 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category' counts: 16 
[INFO ] 2024-10-28 19:06:59.730 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T2 
[INFO ] 2024-10-28 19:06:59.733 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T2 is going to be initial synced 
[INFO ] 2024-10-28 19:06:59.814 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T2' counts: 53 
[INFO ] 2024-10-28 19:07:00.024 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.024 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0530_T1 
[INFO ] 2024-10-28 19:07:00.024 - [测试Oracle共享挖掘][OracleTest] - Table AA_0530_T1 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.151 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0530_T1' counts: 54 
[INFO ] 2024-10-28 19:07:00.280 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0530_T1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.281 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_T3 
[INFO ] 2024-10-28 19:07:00.287 - [测试Oracle共享挖掘][OracleTest] - Table AA_T3 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.336 - [测试Oracle共享挖掘][OracleTest] - Table [AA_T3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.336 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_T3' counts: 0 
[INFO ] 2024-10-28 19:07:00.336 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_POLICY_M 
[INFO ] 2024-10-28 19:07:00.340 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_POLICY_M is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.441 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_POLICY_M' counts: 699 
[INFO ] 2024-10-28 19:07:00.442 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_POLICY_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.442 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CAR_CLAIM_M 
[INFO ] 2024-10-28 19:07:00.442 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.581 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-28 19:07:00.582 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.582 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: Category1 
[INFO ] 2024-10-28 19:07:00.582 - [测试Oracle共享挖掘][OracleTest] - Table Category1 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.713 - [测试Oracle共享挖掘][OracleTest] - Query table 'Category1' counts: 10 
[INFO ] 2024-10-28 19:07:00.713 - [测试Oracle共享挖掘][OracleTest] - Table [Category1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.713 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0401 
[INFO ] 2024-10-28 19:07:00.713 - [测试Oracle共享挖掘][OracleTest] - Table AA_0401 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.750 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0401] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.750 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0401' counts: 0 
[INFO ] 2024-10-28 19:07:00.750 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0726 
[INFO ] 2024-10-28 19:07:00.750 - [测试Oracle共享挖掘][OracleTest] - Table A_0726 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.782 - [测试Oracle共享挖掘][OracleTest] - Table [A_0726] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.785 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0726' counts: 3 
[INFO ] 2024-10-28 19:07:00.785 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0514 
[INFO ] 2024-10-28 19:07:00.785 - [测试Oracle共享挖掘][OracleTest] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.820 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-10-28 19:07:00.820 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.820 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: AA_0328 
[INFO ] 2024-10-28 19:07:00.820 - [测试Oracle共享挖掘][OracleTest] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.849 - [测试Oracle共享挖掘][OracleTest] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.849 - [测试Oracle共享挖掘][OracleTest] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-10-28 19:07:00.849 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0620_CUSTOMER_M 
[INFO ] 2024-10-28 19:07:00.849 - [测试Oracle共享挖掘][OracleTest] - Table 0620_CUSTOMER_M is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.945 - [测试Oracle共享挖掘][OracleTest] - Query table '0620_CUSTOMER_M' counts: 648 
[INFO ] 2024-10-28 19:07:00.945 - [测试Oracle共享挖掘][OracleTest] - Table [0620_CUSTOMER_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.945 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: A_0321 
[INFO ] 2024-10-28 19:07:00.945 - [测试Oracle共享挖掘][OracleTest] - Table A_0321 is going to be initial synced 
[INFO ] 2024-10-28 19:07:00.972 - [测试Oracle共享挖掘][OracleTest] - Table [A_0321] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:00.972 - [测试Oracle共享挖掘][OracleTest] - Query table 'A_0321' counts: 2 
[INFO ] 2024-10-28 19:07:00.972 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: 0626_CUSTOMER_ 
[INFO ] 2024-10-28 19:07:00.972 - [测试Oracle共享挖掘][OracleTest] - Table 0626_CUSTOMER_ is going to be initial synced 
[INFO ] 2024-10-28 19:07:01.003 - [测试Oracle共享挖掘][OracleTest] - Query table '0626_CUSTOMER_' counts: 1 
[INFO ] 2024-10-28 19:07:01.003 - [测试Oracle共享挖掘][OracleTest] - Table [0626_CUSTOMER_] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:01.003 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: F642_BMSQL_DISTRICT 
[INFO ] 2024-10-28 19:07:01.050 - [测试Oracle共享挖掘][OracleTest] - Table F642_BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-10-28 19:07:01.060 - [测试Oracle共享挖掘][OracleTest] - Table [F642_BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:01.060 - [测试Oracle共享挖掘][OracleTest] - Query table 'F642_BMSQL_DISTRICT' counts: 10 
[INFO ] 2024-10-28 19:07:01.061 - [测试Oracle共享挖掘][OracleTest] - Starting batch read, table name: JACK_002 
[INFO ] 2024-10-28 19:07:01.061 - [测试Oracle共享挖掘][OracleTest] - Table JACK_002 is going to be initial synced 
[INFO ] 2024-10-28 19:07:01.095 - [测试Oracle共享挖掘][OracleTest] - Query table 'JACK_002' counts: 3 
[INFO ] 2024-10-28 19:07:01.095 - [测试Oracle共享挖掘][OracleTest] - Table [JACK_002] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-28 19:07:01.096 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 19:07:01.096 - [测试Oracle共享挖掘][OracleTest] - Incremental sync starting... 
[INFO ] 2024-10-28 19:07:01.096 - [测试Oracle共享挖掘][OracleTest] - Initial sync completed 
[INFO ] 2024-10-28 19:07:01.137 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-10-28 19:07:01.137 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 1 - Check connection OracleTest enable share cdc: true 
[INFO ] 2024-10-28 19:07:01.137 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试Oracle共享挖掘 enable share cdc: true 
[INFO ] 2024-10-28 19:07:01.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自OracleTest的共享挖掘任务 
[INFO ] 2024-10-28 19:07:01.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv12-c?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-10-28 19:07:01.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-10-28 19:07:01.174 - [测试Oracle共享挖掘][OracleTest] - Init share cdc reader completed 
[INFO ] 2024-10-28 19:07:01.174 - [测试Oracle共享挖掘][OracleTest] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-10-28 19:07:01.174 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-10-28 19:07:01.181 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Read table count: 17, partition size: 2, read thread number: 9 
[INFO ] 2024-10-28 19:07:01.181 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f6974ce59bbba57dee647, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category_test, version=v2, tableName=Category_test, externalStorageTableName=ExternalStorage_SHARE_CDC_1651887245, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.197 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_test_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1651887245 
[INFO ] 2024-10-28 19:07:01.197 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff30, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category, version=v2, tableName=Category, externalStorageTableName=ExternalStorage_SHARE_CDC_-1937428636, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.205 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1937428636 
[INFO ] 2024-10-28 19:07:01.205 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [Category_test, Category] 
[INFO ] 2024-10-28 19:07:01.209 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category_test) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.209 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category_test' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.209 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T2, version=v2, tableName=AA_0530_T2, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691394, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.211 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category_test filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.211 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T2_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691394 
[INFO ] 2024-10-28 19:07:01.213 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.213 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.214 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f56aace59bbba57da314d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0530_T1, version=v2, tableName=AA_0530_T1, externalStorageTableName=ExternalStorage_SHARE_CDC_1022691393, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.220 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0530_T1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1022691393 
[INFO ] 2024-10-28 19:07:01.220 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0530_T2, AA_0530_T1] 
[INFO ] 2024-10-28 19:07:01.221 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T2) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.221 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T2' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.222 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T2 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.223 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_T3, version=v2, tableName=AA_T3, externalStorageTableName=ExternalStorage_SHARE_CDC_2024864344, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.223 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0530_T1) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.223 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0530_T1' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.267 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0530_T1 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.289 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_T3_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_2024864344 
[INFO ] 2024-10-28 19:07:01.300 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72eea, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_POLICY_M, version=v2, tableName=0620_CAR_POLICY_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1425719616, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.300 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_POLICY_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1425719616 
[INFO ] 2024-10-28 19:07:01.305 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_T3, 0620_CAR_POLICY_M] 
[INFO ] 2024-10-28 19:07:01.306 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_T3) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.306 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_T3' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.307 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_T3 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.308 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee8, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CAR_CLAIM_M, version=v2, tableName=0620_CAR_CLAIM_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-41599318, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.308 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_POLICY_M) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.308 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_POLICY_M' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.308 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_POLICY_M filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.312 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CAR_CLAIM_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-41599318 
[INFO ] 2024-10-28 19:07:01.312 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f59e9ce59bbba57daff31, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_Category1, version=v2, tableName=Category1, externalStorageTableName=ExternalStorage_SHARE_CDC_69254477, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.315 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.Category1_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_69254477 
[INFO ] 2024-10-28 19:07:01.315 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CAR_CLAIM_M, Category1] 
[INFO ] 2024-10-28 19:07:01.316 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CAR_CLAIM_M) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.316 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CAR_CLAIM_M' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.318 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CAR_CLAIM_M filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.318 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be3, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0401, version=v2, tableName=AA_0401, externalStorageTableName=ExternalStorage_SHARE_CDC_273379518, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.319 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(Category1) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.319 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'Category1' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.320 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by Category1 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.321 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0401_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273379518 
[INFO ] 2024-10-28 19:07:01.329 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f58eece59bbba57dac34c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0726, version=v2, tableName=A_0726, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627437169, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.329 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0726_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627437169 
[INFO ] 2024-10-28 19:07:01.330 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0401, A_0726] 
[INFO ] 2024-10-28 19:07:01.330 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0401) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.331 - [测试Oracle共享挖掘][OracleTest] - Connector Oracle incremental start succeed, tables: [Category_test, Category, AA_0530_T2, AA_0530_T1, AA_T3, 0620_CAR_POLICY_M, 0620_CAR_CLAIM_M, Category1, AA_0401, A_0726, AA_0514, AA_0328, 0620_CUSTOMER_M, A_0321, 0626_CUSTOMER_, F642_BMSQL_DISTRICT, JACK_002], data change syncing 
[INFO ] 2024-10-28 19:07:01.331 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0401' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.331 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0401 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.335 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f43b3ce59bbba57d758a6, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0514, version=v2, tableName=AA_0514, externalStorageTableName=ExternalStorage_SHARE_CDC_273380513, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.347 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0726) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.347 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0726' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.348 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0726 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.348 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0514_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273380513 
[INFO ] 2024-10-28 19:07:01.358 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f565cce59bbba57da1be4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_AA_0328, version=v2, tableName=AA_0328, externalStorageTableName=ExternalStorage_SHARE_CDC_273378626, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.359 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.AA_0328_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_273378626 
[INFO ] 2024-10-28 19:07:01.362 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [AA_0514, AA_0328] 
[INFO ] 2024-10-28 19:07:01.370 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0514) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.370 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0514' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.370 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f42e3ce59bbba57d72ee9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0620_CUSTOMER_M, version=v2, tableName=0620_CUSTOMER_M, externalStorageTableName=ExternalStorage_SHARE_CDC_-1984265343, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.371 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0514 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.373 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(AA_0328) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.373 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'AA_0328' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.374 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by AA_0328 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.374 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0620_CUSTOMER_M_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1984265343 
[INFO ] 2024-10-28 19:07:01.381 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f5788ce59bbba57da6482, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_A_0321, version=v2, tableName=A_0321, externalStorageTableName=ExternalStorage_SHARE_CDC_-1627441018, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.381 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.A_0321_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1627441018 
[INFO ] 2024-10-28 19:07:01.386 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0620_CUSTOMER_M, A_0321] 
[INFO ] 2024-10-28 19:07:01.386 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0620_CUSTOMER_M) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.386 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0620_CUSTOMER_M' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.386 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0620_CUSTOMER_M filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.387 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f4380ce59bbba57d74e1d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_0626_CUSTOMER_, version=v2, tableName=0626_CUSTOMER_, externalStorageTableName=ExternalStorage_SHARE_CDC_-177572142, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.387 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(A_0321) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.387 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'A_0321' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.388 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by A_0321 filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.388 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.0626_CUSTOMER__测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-177572142 
[INFO ] 2024-10-28 19:07:01.402 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f699bce59bbba57def40a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_F642_BMSQL_DISTRICT, version=v2, tableName=F642_BMSQL_DISTRICT, externalStorageTableName=ExternalStorage_SHARE_CDC_-1906591243, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.402 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.F642_BMSQL_DISTRICT_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_-1906591243 
[INFO ] 2024-10-28 19:07:01.406 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [0626_CUSTOMER_, F642_BMSQL_DISTRICT] 
[INFO ] 2024-10-28 19:07:01.406 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(0626_CUSTOMER_) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.406 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read '0626_CUSTOMER_' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.406 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by 0626_CUSTOMER_ filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.409 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(F642_BMSQL_DISTRICT) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.409 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'F642_BMSQL_DISTRICT' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.410 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by F642_BMSQL_DISTRICT filter: {sequence=1} 
[INFO ] 2024-10-28 19:07:01.410 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=671f699bce59bbba57def40b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=671f425a4c1eac524f633b56_JACK_002, version=v2, tableName=JACK_002, externalStorageTableName=ExternalStorage_SHARE_CDC_1909821464, shareCdcTaskId=671f42e34c1eac524f633c92, connectionId=671f425a4c1eac524f633b56) 
[INFO ] 2024-10-28 19:07:01.417 - [测试Oracle共享挖掘][OracleTest] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自OracleTest的共享挖掘任务_C##TAPDATA.JACK_002_测试Oracle共享挖掘, external storage name: ExternalStorage_SHARE_CDC_1909821464 
[INFO ] 2024-10-28 19:07:01.417 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [JACK_002] 
[INFO ] 2024-10-28 19:07:01.420 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find sequence in construct(JACK_002) by timestamp(2024-10-28T11:06:58.878Z): 1 
[INFO ] 2024-10-28 19:07:01.420 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Starting read 'JACK_002' log, sequence: 1 
[INFO ] 2024-10-28 19:07:01.420 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Find by JACK_002 filter: {sequence=1} 
[INFO ] 2024-10-28 19:09:41.157 - [测试Oracle共享挖掘][OracleTest] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=JACK_002, tableNamespaces=[C##TAPDATA, JACK_002], timestamp=1730113759000, date=Mon Oct 28 19:09:19 CST 2024, before=Document{{TIME=Tue Jun 18 18:32:50 CST 2024, ID=1, CREATE_TIME=Tue Jun 18 18:32:56 CST 2024, NAME=null}}, after=Document{{TIME=Tue Jun 18 18:32:50 CST 2024, ID=1, CREATE_TIME=Tue Jun 18 18:32:56 CST 2024, NAME=jack2}}, op=u, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5vcmFjbGUuY2RjLm9mZnNldC5PcmFjbGVP
ZmZzZXQUJN7C0aaHzAIAB0kAA2Zub0wABmhleFNjbnQAEkxqYXZhL2xhbmcvU3RyaW5nO0wAB2xh
c3RTY250ABBMamF2YS9sYW5nL0xvbmc7TAALb2Zmc2V0VmFsdWVxAH4AAkwACnBlbmRpbmdTY25x
AH4AAkwACnNvcnRTdHJpbmdxAH4AAUwACXRpbWVzdGFtcHEAfgACeHAAAAAAcHNyAA5qYXZhLmxh
bmcuTG9uZzuL5JDMjyPfAgABSgAFdmFsdWV4cgAQamF2YS5sYW5nLk51bWJlcoaslR0LlOCLAgAA
eHAAAAAAB5fJQXBzcQB+AAQAAAAAB5fJiHBzcQB+AAQAAAGS0tDnGA==
, type=DATA, connectionId=671f425a4c1eac524f633b56, isReplaceEvent=false, _ts=1730113772}} 
[INFO ] 2024-10-28 19:11:05.991 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] running status set to false 
[INFO ] 2024-10-28 19:11:05.991 - [测试Oracle共享挖掘][OracleTest] - Incremental sync completed 
[INFO ] 2024-10-28 19:11:06.088 - [测试Oracle共享挖掘][OracleTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 19:11:06.089 - [测试Oracle共享挖掘][OracleTest] - PDK connector node released: HazelcastSourcePdkDataNode-b78ac5d0-b306-4713-ac24-4582752de5b8 
[INFO ] 2024-10-28 19:11:06.089 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] schema data cleaned 
[INFO ] 2024-10-28 19:11:06.089 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] monitor closed 
[INFO ] 2024-10-28 19:11:06.090 - [测试Oracle共享挖掘][OracleTest] - Node OracleTest[b78ac5d0-b306-4713-ac24-4582752de5b8] close complete, cost 114 ms 
[INFO ] 2024-10-28 19:11:06.090 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] running status set to false 
[INFO ] 2024-10-28 19:11:06.105 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 19:11:06.105 - [测试Oracle共享挖掘][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1f696486-bf5a-462e-8403-296abcfd8b58 
[INFO ] 2024-10-28 19:11:06.105 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] schema data cleaned 
[INFO ] 2024-10-28 19:11:06.105 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] monitor closed 
[INFO ] 2024-10-28 19:11:06.105 - [测试Oracle共享挖掘][Mysql3307] - Node Mysql3307[1f696486-bf5a-462e-8403-296abcfd8b58] close complete, cost 15 ms 
[INFO ] 2024-10-28 19:11:06.577 - [测试Oracle共享挖掘] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-28 19:11:06.577 - [测试Oracle共享挖掘] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@14a5031a 
[INFO ] 2024-10-28 19:11:06.720 - [测试Oracle共享挖掘] - Stop task milestones: 67171bd0d257480e6acdeaf0(测试Oracle共享挖掘)  
[INFO ] 2024-10-28 19:11:06.721 - [测试Oracle共享挖掘] - Stopped task aspect(s) 
[INFO ] 2024-10-28 19:11:06.722 - [测试Oracle共享挖掘] - Snapshot order controller have been removed 
[INFO ] 2024-10-28 19:11:06.756 - [测试Oracle共享挖掘] - Remove memory task client succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
[INFO ] 2024-10-28 19:11:06.756 - [测试Oracle共享挖掘] - Destroy memory task client cache succeed, task: 测试Oracle共享挖掘[67171bd0d257480e6acdeaf0] 
