[INFO ] 2024-05-14 06:51:42.372 - [任务 4] - Task initialization... 
[INFO ] 2024-05-14 06:51:42.373 - [任务 4] - Start task milestones: 664309c8d96c7030f5d247e8(任务 4) 
[INFO ] 2024-05-14 06:51:42.530 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-14 06:51:42.533 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-14 06:51:42.658 - [任务 4][OracleTest1] - Node OracleTest1[7c1164cb-836f-4590-a052-5ab84b9eaed1] start preload schema,table counts: 1 
[INFO ] 2024-05-14 06:51:42.658 - [任务 4][AAA1] - Node AAA1[502d81f5-17fe-4d34-8621-a5446749e01c] start preload schema,table counts: 1 
[INFO ] 2024-05-14 06:51:42.698 - [任务 4][OracleTest1] - Node OracleTest1[7c1164cb-836f-4590-a052-5ab84b9eaed1] preload schema finished, cost 38 ms 
[INFO ] 2024-05-14 06:51:42.698 - [任务 4][AAA1] - Node AAA1[502d81f5-17fe-4d34-8621-a5446749e01c] preload schema finished, cost 37 ms 
[INFO ] 2024-05-14 06:51:43.865 - [任务 4][AAA1] - Source node "AAA1" read batch size: 100 
[INFO ] 2024-05-14 06:51:43.871 - [任务 4][AAA1] - Source node "AAA1" event queue capacity: 200 
[INFO ] 2024-05-14 06:51:43.871 - [任务 4][AAA1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-14 06:51:44.268 - [任务 4][AAA1] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":721065382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-14 06:51:44.271 - [任务 4][AAA1] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-14 06:51:44.340 - [任务 4][AAA1] - Initial sync started 
[INFO ] 2024-05-14 06:51:44.341 - [任务 4][AAA1] - Starting batch read, table name: AAA1, offset: null 
[INFO ] 2024-05-14 06:51:44.341 - [任务 4][AAA1] - Table AAA1 is going to be initial synced 
[INFO ] 2024-05-14 06:51:44.381 - [任务 4][AAA1] - Query table 'AAA1' counts: 9 
[INFO ] 2024-05-14 06:51:44.382 - [任务 4][AAA1] - Initial sync completed 
[INFO ] 2024-05-14 06:51:44.383 - [任务 4][AAA1] - Incremental sync starting... 
[INFO ] 2024-05-14 06:51:44.383 - [任务 4][AAA1] - Initial sync completed 
[INFO ] 2024-05-14 06:51:44.472 - [任务 4][AAA1] - Starting stream read, table list: [AAA1], offset: {"sortString":null,"offsetValue":null,"lastScn":721065382,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-14 06:51:44.473 - [任务 4][AAA1] - Checking whether archived log exists... 
[INFO ] 2024-05-14 06:51:44.678 - [任务 4][AAA1] - building new log file... 
[INFO ] 2024-05-14 06:51:47.048 - [任务 4][AAA1] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 06:51:49.070 - [任务 4][OracleTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-14 06:52:39.200 - [任务 4][AAA1] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 06:52:39.200 - [任务 4][AAA1] - Connector Oracle incremental start succeed, tables: [AAA1], data change syncing 
[WARN ] 2024-05-14 07:01:26.501 - [任务 4][AAA1] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-14 07:02:26.590 - [任务 4][AAA1] - Log Miner is shutting down... 
[INFO ] 2024-05-14 07:02:26.591 - [任务 4][AAA1] - Log Miner has been closed! 
[INFO ] 2024-05-14 07:02:27.192 - [任务 4][AAA1] - Checking whether archived log exists... 
[INFO ] 2024-05-14 07:02:27.395 - [任务 4][AAA1] - building new log file... 
[INFO ] 2024-05-14 07:02:29.436 - [任务 4][AAA1] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 07:03:23.691 - [任务 4][AAA1] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 07:03:27.337 - [任务 4][AAA1] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-14 08:06:44.906 - [任务 4][AAA1] - Log Miner is shutting down... 
[INFO ] 2024-05-14 08:06:44.906 - [任务 4][AAA1] - Log Miner has been closed! 
[INFO ] 2024-05-14 08:06:45.077 - [任务 4][AAA1] - Checking whether archived log exists... 
[INFO ] 2024-05-14 08:06:45.184 - [任务 4][AAA1] - building new log file... 
[INFO ] 2024-05-14 08:06:47.828 - [任务 4][AAA1] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 08:08:14.602 - [任务 4][AAA1] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 08:08:32.500 - [任务 4][AAA1] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-14 09:42:57.282 - [任务 4][AAA1] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-14 09:45:37.368 - [任务 4][AAA1] - Log Miner is shutting down... 
[INFO ] 2024-05-14 09:45:37.369 - [任务 4][AAA1] - Log Miner has been closed! 
[INFO ] 2024-05-14 09:45:47.390 - [任务 4][AAA1] - Checking whether archived log exists... 
[INFO ] 2024-05-14 09:45:47.515 - [任务 4][AAA1] - building new log file... 
[INFO ] 2024-05-14 09:45:54.465 - [任务 4][AAA1] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 09:47:16.157 - [任务 4][AAA1] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 09:47:32.608 - [任务 4][AAA1] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-14 10:40:59.964 - [任务 4] - Stop task milestones: 664309c8d96c7030f5d247e8(任务 4)  
[INFO ] 2024-05-14 10:40:59.964 - [任务 4][AAA1] - Node AAA1[502d81f5-17fe-4d34-8621-a5446749e01c] running status set to false 
[INFO ] 2024-05-14 10:40:59.967 - [任务 4][AAA1] - Log Miner is shutting down... 
[INFO ] 2024-05-14 10:40:59.968 - [任务 4][AAA1] - Auto redo oracle log miner result set closed 
[INFO ] 2024-05-14 10:40:59.968 - [任务 4][AAA1] - Log Miner has been closed! 
[INFO ] 2024-05-14 10:41:00.041 - [任务 4][AAA1] - PDK connector node stopped: HazelcastSourcePdkDataNode-502d81f5-17fe-4d34-8621-a5446749e01c 
[INFO ] 2024-05-14 10:41:00.041 - [任务 4][AAA1] - PDK connector node released: HazelcastSourcePdkDataNode-502d81f5-17fe-4d34-8621-a5446749e01c 
[INFO ] 2024-05-14 10:41:00.041 - [任务 4][AAA1] - Node AAA1[502d81f5-17fe-4d34-8621-a5446749e01c] schema data cleaned 
[INFO ] 2024-05-14 10:41:00.046 - [任务 4][AAA1] - Node AAA1[502d81f5-17fe-4d34-8621-a5446749e01c] monitor closed 
[INFO ] 2024-05-14 10:41:00.046 - [任务 4][AAA1] - Node AAA1[502d81f5-17fe-4d34-8621-a5446749e01c] close complete, cost 78 ms 
[INFO ] 2024-05-14 10:41:00.110 - [任务 4][OracleTest1] - Node OracleTest1[7c1164cb-836f-4590-a052-5ab84b9eaed1] running status set to false 
[INFO ] 2024-05-14 10:41:00.110 - [任务 4][OracleTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-7c1164cb-836f-4590-a052-5ab84b9eaed1 
[INFO ] 2024-05-14 10:41:00.110 - [任务 4][OracleTest1] - PDK connector node released: HazelcastTargetPdkDataNode-7c1164cb-836f-4590-a052-5ab84b9eaed1 
[INFO ] 2024-05-14 10:41:00.110 - [任务 4][OracleTest1] - Node OracleTest1[7c1164cb-836f-4590-a052-5ab84b9eaed1] schema data cleaned 
[INFO ] 2024-05-14 10:41:00.110 - [任务 4][OracleTest1] - Node OracleTest1[7c1164cb-836f-4590-a052-5ab84b9eaed1] monitor closed 
[INFO ] 2024-05-14 10:41:00.312 - [任务 4][OracleTest1] - Node OracleTest1[7c1164cb-836f-4590-a052-5ab84b9eaed1] close complete, cost 64 ms 
[INFO ] 2024-05-14 10:41:00.589 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-14 10:41:00.590 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-05-14 10:41:00.590 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-05-14 10:41:00.632 - [任务 4] - Remove memory task client succeed, task: 任务 4[664309c8d96c7030f5d247e8] 
[INFO ] 2024-05-14 10:41:00.632 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[664309c8d96c7030f5d247e8] 
