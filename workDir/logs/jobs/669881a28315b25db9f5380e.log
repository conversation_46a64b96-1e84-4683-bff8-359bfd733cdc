[INFO ] 2024-07-18 10:45:02.143 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Task initialization... 
[INFO ] 2024-07-18 10:45:02.143 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Start task milestones: 669881a28315b25db9f5380e(t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689) 
[INFO ] 2024-07-18 10:45:02.745 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:45:02.876 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - The engine receives t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:45:02.961 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[97e27c14-e44f-478a-8cf8-9dc88cc081b9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:45:02.961 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:45:02.962 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[97e27c14-e44f-478a-8cf8-9dc88cc081b9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:45:02.962 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:45:03.343 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:45:03.374 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Source node "qa_mock_100w_300fields_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:45:03.374 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Source node "qa_mock_100w_300fields_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:45:03.375 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:45:03.576 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-18 10:45:03.647 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:45:03.647 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Starting batch read, table name: mock_100w, offset: null 
[INFO ] 2024-07-18 10:45:03.654 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Table mock_100w is going to be initial synced 
[INFO ] 2024-07-18 10:45:03.654 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Start mock_100w batch read 
[INFO ] 2024-07-18 10:45:03.855 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Query table 'mock_100w' counts: 1000000 
[INFO ] 2024-07-18 10:45:21.568 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Compile mock_100w batch read 
[INFO ] 2024-07-18 10:45:21.568 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Table [mock_100w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:45:21.772 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:45:27.679 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e] running status set to false 
[INFO ] 2024-07-18 10:45:27.679 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[97e27c14-e44f-478a-8cf8-9dc88cc081b9] running status set to false 
[INFO ] 2024-07-18 10:45:27.679 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 10:45:27.710 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Stop connector: first 1721270703656 310ms, last 1721270721552 3037ms, counts: 1000000/20933ms, min: 27, max: 3054, QPS: 50000/s 
[INFO ] 2024-07-18 10:45:27.710 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e 
[INFO ] 2024-07-18 10:45:27.710 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-97e27c14-e44f-478a-8cf8-9dc88cc081b9 
[INFO ] 2024-07-18 10:45:27.710 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e 
[INFO ] 2024-07-18 10:45:27.711 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e] schema data cleaned 
[INFO ] 2024-07-18 10:45:27.711 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-97e27c14-e44f-478a-8cf8-9dc88cc081b9 
[INFO ] 2024-07-18 10:45:27.711 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e] monitor closed 
[INFO ] 2024-07-18 10:45:27.712 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[97e27c14-e44f-478a-8cf8-9dc88cc081b9] schema data cleaned 
[INFO ] 2024-07-18 10:45:27.713 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[97e27c14-e44f-478a-8cf8-9dc88cc081b9] monitor closed 
[INFO ] 2024-07-18 10:45:27.713 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[97e27c14-e44f-478a-8cf8-9dc88cc081b9] close complete, cost 49 ms 
[INFO ] 2024-07-18 10:45:27.713 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689][qa_mock_100w_300fields_1717403468657_3537] - Node qa_mock_100w_300fields_1717403468657_3537[f2a038fb-9898-4c3a-96a5-0e6e3f03aa2e] close complete, cost 52 ms 
[INFO ] 2024-07-18 10:45:29.099 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:45:29.220 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f3befb4 
[INFO ] 2024-07-18 10:45:29.220 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Stop task milestones: 669881a28315b25db9f5380e(t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689)  
[INFO ] 2024-07-18 10:45:29.263 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:45:29.263 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:45:29.337 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Remove memory task client succeed, task: t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689[669881a28315b25db9f5380e] 
[INFO ] 2024-07-18 10:45:29.338 - [t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689] - Destroy memory task client cache succeed, task: t_1.3-mock_to_mock_200fields_1717403468657_3537-1721270689[669881a28315b25db9f5380e] 
