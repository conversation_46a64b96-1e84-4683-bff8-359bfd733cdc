[INFO ] 2024-06-26 15:01:30.582 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 15:01:30.584 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 15:15:34.613 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 15:15:34.618 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 15:15:36.185 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-26 15:15:36.397 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-26 15:15:36.751 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] start preload schema,table counts: 1 
[INFO ] 2024-06-26 15:15:36.752 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] start preload schema,table counts: 1 
[INFO ] 2024-06-26 15:15:36.753 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] preload schema finished, cost 1 ms 
[INFO ] 2024-06-26 15:15:36.753 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 15:15:37.941 - [任务 1][CLAIM2] - Source node "CLAIM2" read batch size: 100 
[INFO ] 2024-06-26 15:15:37.941 - [任务 1][CLAIM2] - Source node "CLAIM2" event queue capacity: 200 
[INFO ] 2024-06-26 15:15:37.942 - [任务 1][CLAIM2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-26 15:15:38.134 - [任务 1][CLAIM2] - batch offset found: {},stream offset found: {"cdcOffset":1719386137,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 15:15:38.212 - [任务 1][claimtest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-26 15:15:38.364 - [任务 1][CLAIM2] - Initial sync started 
[INFO ] 2024-06-26 15:15:38.383 - [任务 1][CLAIM2] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-26 15:15:38.384 - [任务 1][CLAIM2] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-26 15:15:38.454 - [任务 1][CLAIM2] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-26 15:15:38.455 - [任务 1][CLAIM2] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-26 15:15:38.467 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 15:15:38.468 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-06-26 15:15:38.471 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 15:15:38.473 - [任务 1][CLAIM2] - Starting stream read, table list: [CLAIM2], offset: {"cdcOffset":1719386137,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 15:15:38.679 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [CLAIM2], data change syncing 
[INFO ] 2024-06-26 15:31:23.949 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] running status set to false 
[INFO ] 2024-06-26 15:31:23.990 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 15:31:23.991 - [任务 1][CLAIM2] - PDK connector node released: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 15:31:23.991 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] schema data cleaned 
[INFO ] 2024-06-26 15:31:24.011 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] monitor closed 
[INFO ] 2024-06-26 15:31:24.012 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] close complete, cost 62 ms 
[INFO ] 2024-06-26 15:31:24.221 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] running status set to false 
[INFO ] 2024-06-26 15:31:24.833 - [任务 1][CLAIM2] - Incremental sync completed 
[WARN ] 2024-06-26 15:31:28.499 - [任务 1] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-06-26 15:31:33.386 - [任务 1][claimtest1] - Save to snapshot failed, collection: Task/syncProgress/667bbc9ebe689c77f2706ea5, object: {64fcbea5-7631-4404-ab7d-faffa71446fd,3228edba-91c9-4b7d-8ffa-c05876a334fe=SyncProgress{eventSerialNo=3, syncStage='CDC', batchOffset='{CLAIM2={batch_read_connector_offset=MongoBatchOffset[sortKey='_id', value=667a70881debeccdbb015c33, objectId=true], batch_read_connector_status=RUNNING}}', streamOffset='{cdcOffset=1719386137, opLogOffset=null, mongo_cdc_offset_flag=true}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/667bbc9ebe689c77f2706ea5": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-06-26 15:32:10.006 - [任务 1][claimtest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 15:32:10.006 - [任务 1][claimtest1] - PDK connector node released: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 15:32:10.007 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] schema data cleaned 
[INFO ] 2024-06-26 15:32:10.008 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] monitor closed 
[INFO ] 2024-06-26 15:32:10.213 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] close complete, cost 45996 ms 
[INFO ] 2024-06-26 15:32:12.536 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-26 15:32:12.636 - [任务 1] - Stop task milestones: 667bbc9ebe689c77f2706ea5(任务 1)  
[INFO ] 2024-06-26 15:32:12.636 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-26 15:32:12.636 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-26 15:32:12.637 - [任务 1] - Remove memory task client succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
[INFO ] 2024-06-26 15:32:12.638 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
[INFO ] 2024-06-26 15:37:12.814 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 15:37:12.815 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 15:46:07.846 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 15:46:08.053 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 15:52:12.999 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-26 15:52:13.309 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-26 15:52:13.897 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] start preload schema,table counts: 1 
[INFO ] 2024-06-26 15:52:13.921 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 15:52:13.922 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] start preload schema,table counts: 1 
[INFO ] 2024-06-26 15:52:13.922 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 15:52:15.021 - [任务 1][CLAIM2] - Source node "CLAIM2" read batch size: 100 
[INFO ] 2024-06-26 15:52:15.043 - [任务 1][CLAIM2] - Source node "CLAIM2" event queue capacity: 200 
[INFO ] 2024-06-26 15:52:15.051 - [任务 1][CLAIM2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-26 15:52:15.086 - [任务 1][CLAIM2] - batch offset found: {"CLAIM2":{"batch_read_connector_offset":{"sortKey":"_id","value":"667a70881debeccdbb015c33","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719386137,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 15:52:15.087 - [任务 1][claimtest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-26 15:52:15.190 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-06-26 15:52:15.190 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 15:52:15.394 - [任务 1][CLAIM2] - Starting stream read, table list: [CLAIM2], offset: {"cdcOffset":1719386137,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 15:52:15.602 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [CLAIM2], data change syncing 
[INFO ] 2024-06-26 17:03:25.831 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] running status set to false 
[INFO ] 2024-06-26 17:03:25.847 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 17:03:25.900 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 17:03:26.635 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-26 17:03:26.635 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-26 17:03:27.044 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] start preload schema,table counts: 1 
[INFO ] 2024-06-26 17:03:27.072 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] preload schema finished, cost 2 ms 
[INFO ] 2024-06-26 17:03:27.072 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] start preload schema,table counts: 1 
[INFO ] 2024-06-26 17:03:27.073 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 17:03:28.109 - [任务 1][CLAIM2] - Source node "CLAIM2" read batch size: 100 
[INFO ] 2024-06-26 17:03:28.110 - [任务 1][CLAIM2] - Source node "CLAIM2" event queue capacity: 200 
[INFO ] 2024-06-26 17:03:28.115 - [任务 1][CLAIM2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-26 17:03:28.278 - [任务 1][CLAIM2] - batch offset found: {},stream offset found: {"cdcOffset":1719392607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 17:03:28.350 - [任务 1][CLAIM2] - Initial sync started 
[INFO ] 2024-06-26 17:03:28.373 - [任务 1][CLAIM2] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-26 17:03:28.374 - [任务 1][CLAIM2] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-26 17:03:28.443 - [任务 1][CLAIM2] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-26 17:03:28.445 - [任务 1][claimtest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-26 17:03:28.459 - [任务 1][CLAIM2] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-26 17:03:28.461 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 17:03:28.461 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-06-26 17:03:28.462 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 17:03:28.468 - [任务 1][CLAIM2] - Starting stream read, table list: [CLAIM2], offset: {"cdcOffset":1719392607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 17:03:28.518 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [CLAIM2], data change syncing 
[INFO ] 2024-06-26 17:03:28.519 - [任务 1][claimtest1] - Table "test.claimtest1" exists, skip auto create table 
[INFO ] 2024-06-26 17:03:28.727 - [任务 1][claimtest1] - The table claimtest1 has already exist. 
[INFO ] 2024-06-26 17:38:51.525 - [任务 1] - Stop task milestones: 667bbc9ebe689c77f2706ea5(任务 1)  
[INFO ] 2024-06-26 17:38:51.925 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] running status set to false 
[INFO ] 2024-06-26 17:38:51.954 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 17:38:51.954 - [任务 1][CLAIM2] - PDK connector node released: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 17:38:51.959 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] schema data cleaned 
[INFO ] 2024-06-26 17:38:51.960 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] monitor closed 
[INFO ] 2024-06-26 17:38:51.972 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] close complete, cost 39 ms 
[INFO ] 2024-06-26 17:38:51.972 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] running status set to false 
[INFO ] 2024-06-26 17:38:52.018 - [任务 1][claimtest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 17:38:52.019 - [任务 1][claimtest1] - PDK connector node released: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 17:38:52.019 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] schema data cleaned 
[INFO ] 2024-06-26 17:38:52.019 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] monitor closed 
[INFO ] 2024-06-26 17:38:52.020 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] close complete, cost 48 ms 
[INFO ] 2024-06-26 17:38:54.736 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-26 17:38:54.736 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-26 17:38:54.736 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-26 17:38:54.817 - [任务 1] - Remove memory task client succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
[INFO ] 2024-06-26 17:38:55.020 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
[INFO ] 2024-06-26 17:39:57.198 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 17:39:57.199 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 17:39:57.576 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-26 17:39:57.732 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-26 17:39:57.733 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] start preload schema,table counts: 1 
[INFO ] 2024-06-26 17:39:57.787 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 17:39:57.787 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] start preload schema,table counts: 1 
[INFO ] 2024-06-26 17:39:57.989 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 17:39:58.595 - [任务 1][CLAIM2] - Source node "CLAIM2" read batch size: 100 
[INFO ] 2024-06-26 17:39:58.596 - [任务 1][CLAIM2] - Source node "CLAIM2" event queue capacity: 200 
[INFO ] 2024-06-26 17:39:58.598 - [任务 1][CLAIM2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-26 17:39:58.712 - [任务 1][CLAIM2] - batch offset found: {},stream offset found: {"cdcOffset":1719394798,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 17:39:58.779 - [任务 1][CLAIM2] - Initial sync started 
[INFO ] 2024-06-26 17:39:58.779 - [任务 1][CLAIM2] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-26 17:39:58.787 - [任务 1][CLAIM2] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-26 17:39:58.787 - [任务 1][CLAIM2] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-26 17:39:58.796 - [任务 1][CLAIM2] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-26 17:39:58.796 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 17:39:58.798 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-06-26 17:39:58.798 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 17:39:58.804 - [任务 1][CLAIM2] - Starting stream read, table list: [CLAIM2], offset: {"cdcOffset":1719394798,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 17:39:58.804 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [CLAIM2], data change syncing 
[INFO ] 2024-06-26 17:39:58.837 - [任务 1][claimtest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-26 17:39:58.837 - [任务 1][claimtest1] - Table "test.claimtest1" exists, skip auto create table 
[INFO ] 2024-06-26 17:39:59.043 - [任务 1][claimtest1] - The table claimtest1 has already exist. 
[INFO ] 2024-06-26 19:26:56.335 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] running status set to false 
[INFO ] 2024-06-26 19:26:56.371 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 19:26:56.440 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 19:26:57.034 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-26 19:26:57.241 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-26 19:26:57.535 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] start preload schema,table counts: 1 
[INFO ] 2024-06-26 19:26:57.538 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] start preload schema,table counts: 1 
[INFO ] 2024-06-26 19:26:57.538 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 19:26:57.538 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 19:26:58.651 - [任务 1][CLAIM2] - Source node "CLAIM2" read batch size: 100 
[INFO ] 2024-06-26 19:26:58.651 - [任务 1][CLAIM2] - Source node "CLAIM2" event queue capacity: 200 
[INFO ] 2024-06-26 19:26:58.651 - [任务 1][CLAIM2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-26 19:26:58.851 - [任务 1][CLAIM2] - batch offset found: {},stream offset found: {"cdcOffset":1719401218,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 19:26:58.939 - [任务 1][CLAIM2] - Initial sync started 
[INFO ] 2024-06-26 19:26:58.968 - [任务 1][CLAIM2] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-26 19:26:58.972 - [任务 1][CLAIM2] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-26 19:26:58.974 - [任务 1][claimtest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-26 19:26:59.017 - [任务 1][CLAIM2] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-26 19:26:59.054 - [任务 1][CLAIM2] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-26 19:26:59.056 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 19:26:59.058 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-06-26 19:26:59.059 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 19:26:59.070 - [任务 1][CLAIM2] - Starting stream read, table list: [CLAIM2], offset: {"cdcOffset":1719401218,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 19:26:59.070 - [任务 1][claimtest1] - Table "test.claimtest1" exists, skip auto create table 
[INFO ] 2024-06-26 19:26:59.129 - [任务 1][claimtest1] - The table claimtest1 has already exist. 
[INFO ] 2024-06-26 19:26:59.130 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [CLAIM2], data change syncing 
[INFO ] 2024-06-26 19:28:24.501 - [任务 1] - Stop task milestones: 667bbc9ebe689c77f2706ea5(任务 1)  
[INFO ] 2024-06-26 19:28:24.585 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] running status set to false 
[INFO ] 2024-06-26 19:28:24.585 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 19:28:24.587 - [任务 1][CLAIM2] - PDK connector node released: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 19:28:24.587 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] schema data cleaned 
[INFO ] 2024-06-26 19:28:24.588 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] monitor closed 
[INFO ] 2024-06-26 19:28:24.596 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] close complete, cost 45 ms 
[INFO ] 2024-06-26 19:28:24.596 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] running status set to false 
[INFO ] 2024-06-26 19:28:24.657 - [任务 1][claimtest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 19:28:24.658 - [任务 1][claimtest1] - PDK connector node released: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 19:28:24.658 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] schema data cleaned 
[INFO ] 2024-06-26 19:28:24.658 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] monitor closed 
[INFO ] 2024-06-26 19:28:24.862 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] close complete, cost 63 ms 
[INFO ] 2024-06-26 19:28:27.916 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-26 19:28:27.916 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-26 19:28:27.993 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-26 19:28:27.993 - [任务 1] - Remove memory task client succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
[INFO ] 2024-06-26 19:28:28.195 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
[INFO ] 2024-06-26 19:28:47.755 - [任务 1] - Task initialization... 
[INFO ] 2024-06-26 19:28:47.757 - [任务 1] - Start task milestones: 667bbc9ebe689c77f2706ea5(任务 1) 
[INFO ] 2024-06-26 19:28:48.129 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-26 19:28:48.129 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-26 19:28:48.183 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] start preload schema,table counts: 1 
[INFO ] 2024-06-26 19:28:48.184 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] start preload schema,table counts: 1 
[INFO ] 2024-06-26 19:28:48.186 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 19:28:48.187 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] preload schema finished, cost 0 ms 
[INFO ] 2024-06-26 19:28:48.999 - [任务 1][CLAIM2] - Source node "CLAIM2" read batch size: 100 
[INFO ] 2024-06-26 19:28:49.002 - [任务 1][CLAIM2] - Source node "CLAIM2" event queue capacity: 200 
[INFO ] 2024-06-26 19:28:49.127 - [任务 1][CLAIM2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-26 19:28:49.128 - [任务 1][CLAIM2] - batch offset found: {},stream offset found: {"cdcOffset":1719401329,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 19:28:49.209 - [任务 1][CLAIM2] - Initial sync started 
[INFO ] 2024-06-26 19:28:49.209 - [任务 1][CLAIM2] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-26 19:28:49.209 - [任务 1][CLAIM2] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-26 19:28:49.215 - [任务 1][CLAIM2] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-26 19:28:49.220 - [任务 1][CLAIM2] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-26 19:28:49.220 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 19:28:49.221 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-06-26 19:28:49.221 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-06-26 19:28:49.229 - [任务 1][CLAIM2] - Starting stream read, table list: [CLAIM2], offset: {"cdcOffset":1719401329,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-26 19:28:49.229 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [CLAIM2], data change syncing 
[INFO ] 2024-06-26 19:28:49.287 - [任务 1][claimtest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-26 19:28:49.287 - [任务 1][claimtest1] - Table "test.claimtest1" exists, skip auto create table 
[INFO ] 2024-06-26 19:28:49.287 - [任务 1][claimtest1] - The table claimtest1 has already exist. 
[INFO ] 2024-06-26 19:54:12.966 - [任务 1] - Stop task milestones: 667bbc9ebe689c77f2706ea5(任务 1)  
[INFO ] 2024-06-26 19:54:13.035 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] running status set to false 
[INFO ] 2024-06-26 19:54:13.073 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 19:54:13.073 - [任务 1][CLAIM2] - PDK connector node released: HazelcastSourcePdkDataNode-64fcbea5-7631-4404-ab7d-faffa71446fd 
[INFO ] 2024-06-26 19:54:13.073 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] schema data cleaned 
[INFO ] 2024-06-26 19:54:13.074 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] monitor closed 
[INFO ] 2024-06-26 19:54:13.076 - [任务 1][CLAIM2] - Node CLAIM2[64fcbea5-7631-4404-ab7d-faffa71446fd] close complete, cost 46 ms 
[INFO ] 2024-06-26 19:54:13.100 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] running status set to false 
[INFO ] 2024-06-26 19:54:13.100 - [任务 1][claimtest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 19:54:13.100 - [任务 1][claimtest1] - PDK connector node released: HazelcastTargetPdkDataNode-3228edba-91c9-4b7d-8ffa-c05876a334fe 
[INFO ] 2024-06-26 19:54:13.100 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] schema data cleaned 
[INFO ] 2024-06-26 19:54:13.105 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] monitor closed 
[INFO ] 2024-06-26 19:54:13.110 - [任务 1][claimtest1] - Node claimtest1[3228edba-91c9-4b7d-8ffa-c05876a334fe] close complete, cost 26 ms 
[INFO ] 2024-06-26 19:54:14.692 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-26 19:54:14.692 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-26 19:54:14.735 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-26 19:54:14.735 - [任务 1] - Remove memory task client succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
[INFO ] 2024-06-26 19:54:14.939 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[667bbc9ebe689c77f2706ea5] 
