[INFO ] 2024-06-07 17:48:56.110 - [任务 6(101)] - 6662d40a57e5ef2d1bd67235 task start 
[INFO ] 2024-06-07 17:48:56.120 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:56.120 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:48:56.124 - [任务 6(101)][bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] - Node bceda5d7-02d2-4574-84a5-a6bd9c74f4e0[bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:48:56.124 - [任务 6(101)][bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] - Node bceda5d7-02d2-4574-84a5-a6bd9c74f4e0[bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:48:56.155 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 31 ms 
[INFO ] 2024-06-07 17:48:56.155 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 33 ms 
[INFO ] 2024-06-07 17:48:56.478 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:48:56.478 - [任务 6(101)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:56.479 - [任务 6(101)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:48:56.479 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:48:56.479 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:48:56.480 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 8 ms 
[INFO ] 2024-06-07 17:48:56.861 - [任务 6(101)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 17:48:57.040 - [任务 6(101)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 17:48:57.040 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:48:57.044 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-2149d67e-3569-4430-b7f1-91e59e1d8559 
[INFO ] 2024-06-07 17:48:57.044 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-2149d67e-3569-4430-b7f1-91e59e1d8559 
[INFO ] 2024-06-07 17:48:57.044 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:57.050 - [任务 6(101)][bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] - Node bceda5d7-02d2-4574-84a5-a6bd9c74f4e0[bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] running status set to false 
[INFO ] 2024-06-07 17:48:57.050 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-fd300a84-a067-400f-8349-5228c160f2b8 
[INFO ] 2024-06-07 17:48:57.050 - [任务 6(101)][bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] - Node bceda5d7-02d2-4574-84a5-a6bd9c74f4e0[bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] schema data cleaned 
[INFO ] 2024-06-07 17:48:57.050 - [任务 6(101)][bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] - Node bceda5d7-02d2-4574-84a5-a6bd9c74f4e0[bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] monitor closed 
[INFO ] 2024-06-07 17:48:57.050 - [任务 6(101)][bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] - Node bceda5d7-02d2-4574-84a5-a6bd9c74f4e0[bceda5d7-02d2-4574-84a5-a6bd9c74f4e0] close complete, cost 0 ms 
[INFO ] 2024-06-07 17:48:57.050 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-fd300a84-a067-400f-8349-5228c160f2b8 
[INFO ] 2024-06-07 17:48:57.050 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:48:57.052 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:48:57.052 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:48:57.052 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 12 ms 
[INFO ] 2024-06-07 17:48:57.253 - [任务 6(101)] - test run task 6662d40a57e5ef2d1bd67235 complete, cost 1068ms 
[INFO ] 2024-06-07 17:53:30.728 - [任务 6(101)] - 6662d40a57e5ef2d1bd67235 task start 
[INFO ] 2024-06-07 17:53:30.820 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:30.820 - [任务 6(101)][05f500b6-504e-4105-a195-f913eea451b2] - Node 05f500b6-504e-4105-a195-f913eea451b2[05f500b6-504e-4105-a195-f913eea451b2] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:53:30.820 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:30.846 - [任务 6(101)][05f500b6-504e-4105-a195-f913eea451b2] - Node 05f500b6-504e-4105-a195-f913eea451b2[05f500b6-504e-4105-a195-f913eea451b2] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:53:30.846 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 24 ms 
[INFO ] 2024-06-07 17:53:30.847 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 23 ms 
[INFO ] 2024-06-07 17:53:31.094 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:53:31.102 - [任务 6(101)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:31.102 - [任务 6(101)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:31.102 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:53:31.102 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:53:31.143 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 14 ms 
[INFO ] 2024-06-07 17:53:31.143 - [任务 6(101)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 17:53:31.318 - [任务 6(101)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 17:53:31.323 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:53:31.323 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-a2ec7efe-2156-4301-822e-7bde5e4a4142 
[INFO ] 2024-06-07 17:53:31.323 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-a2ec7efe-2156-4301-822e-7bde5e4a4142 
[INFO ] 2024-06-07 17:53:31.323 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:31.330 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-be4bfaf5-c06a-4b25-acda-2a21ca3e7459 
[INFO ] 2024-06-07 17:53:31.330 - [任务 6(101)][05f500b6-504e-4105-a195-f913eea451b2] - Node 05f500b6-504e-4105-a195-f913eea451b2[05f500b6-504e-4105-a195-f913eea451b2] running status set to false 
[INFO ] 2024-06-07 17:53:31.331 - [任务 6(101)][05f500b6-504e-4105-a195-f913eea451b2] - Node 05f500b6-504e-4105-a195-f913eea451b2[05f500b6-504e-4105-a195-f913eea451b2] schema data cleaned 
[INFO ] 2024-06-07 17:53:31.331 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-be4bfaf5-c06a-4b25-acda-2a21ca3e7459 
[INFO ] 2024-06-07 17:53:31.331 - [任务 6(101)][05f500b6-504e-4105-a195-f913eea451b2] - Node 05f500b6-504e-4105-a195-f913eea451b2[05f500b6-504e-4105-a195-f913eea451b2] monitor closed 
[INFO ] 2024-06-07 17:53:31.331 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:31.331 - [任务 6(101)][05f500b6-504e-4105-a195-f913eea451b2] - Node 05f500b6-504e-4105-a195-f913eea451b2[05f500b6-504e-4105-a195-f913eea451b2] close complete, cost 3 ms 
[INFO ] 2024-06-07 17:53:31.334 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:53:31.335 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:53:31.335 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 13 ms 
[INFO ] 2024-06-07 17:53:31.542 - [任务 6(101)] - test run task 6662d40a57e5ef2d1bd67235 complete, cost 630ms 
[INFO ] 2024-06-07 17:53:36.579 - [任务 6(101)] - 6662d40a57e5ef2d1bd67235 task start 
[INFO ] 2024-06-07 17:53:36.673 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:36.673 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 17:53:36.674 - [任务 6(101)][e4bef843-f49d-4ac8-ae68-d0b1f447402f] - Node e4bef843-f49d-4ac8-ae68-d0b1f447402f[e4bef843-f49d-4ac8-ae68-d0b1f447402f] start preload schema,table counts: 0 
[INFO ] 2024-06-07 17:53:36.675 - [任务 6(101)][e4bef843-f49d-4ac8-ae68-d0b1f447402f] - Node e4bef843-f49d-4ac8-ae68-d0b1f447402f[e4bef843-f49d-4ac8-ae68-d0b1f447402f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 17:53:36.701 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 15 ms 
[INFO ] 2024-06-07 17:53:36.910 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 15 ms 
[INFO ] 2024-06-07 17:53:36.990 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 17:53:36.997 - [任务 6(101)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:36.997 - [任务 6(101)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 17:53:36.997 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 17:53:36.997 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 17:53:36.998 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 10 ms 
[INFO ] 2024-06-07 17:53:37.198 - [任务 6(101)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 17:53:37.365 - [任务 6(101)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 17:53:37.366 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 17:53:37.369 - [任务 6(101)][e4bef843-f49d-4ac8-ae68-d0b1f447402f] - Node e4bef843-f49d-4ac8-ae68-d0b1f447402f[e4bef843-f49d-4ac8-ae68-d0b1f447402f] running status set to false 
[INFO ] 2024-06-07 17:53:37.369 - [任务 6(101)][e4bef843-f49d-4ac8-ae68-d0b1f447402f] - Node e4bef843-f49d-4ac8-ae68-d0b1f447402f[e4bef843-f49d-4ac8-ae68-d0b1f447402f] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.369 - [任务 6(101)][e4bef843-f49d-4ac8-ae68-d0b1f447402f] - Node e4bef843-f49d-4ac8-ae68-d0b1f447402f[e4bef843-f49d-4ac8-ae68-d0b1f447402f] monitor closed 
[INFO ] 2024-06-07 17:53:37.369 - [任务 6(101)][e4bef843-f49d-4ac8-ae68-d0b1f447402f] - Node e4bef843-f49d-4ac8-ae68-d0b1f447402f[e4bef843-f49d-4ac8-ae68-d0b1f447402f] close complete, cost 1 ms 
[INFO ] 2024-06-07 17:53:37.373 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-0c6e9238-0305-4d57-819d-4deafc883381 
[INFO ] 2024-06-07 17:53:37.374 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-0c6e9238-0305-4d57-819d-4deafc883381 
[INFO ] 2024-06-07 17:53:37.374 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.379 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-dde387c4-7566-4150-aa31-a39edf90f47b 
[INFO ] 2024-06-07 17:53:37.379 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-dde387c4-7566-4150-aa31-a39edf90f47b 
[INFO ] 2024-06-07 17:53:37.379 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.382 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 17:53:37.382 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 17:53:37.382 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 19 ms 
[INFO ] 2024-06-07 17:53:37.498 - [任务 6(101)] - test run task 6662d40a57e5ef2d1bd67235 complete, cost 835ms 
[INFO ] 2024-06-07 18:01:29.569 - [任务 6(101)] - 6662d40a57e5ef2d1bd67235 task start 
[INFO ] 2024-06-07 18:01:29.659 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:29.659 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:29.660 - [任务 6(101)][d8937f29-8ed4-4158-9ca2-f193bd3e7227] - Node d8937f29-8ed4-4158-9ca2-f193bd3e7227[d8937f29-8ed4-4158-9ca2-f193bd3e7227] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:01:29.660 - [任务 6(101)][d8937f29-8ed4-4158-9ca2-f193bd3e7227] - Node d8937f29-8ed4-4158-9ca2-f193bd3e7227[d8937f29-8ed4-4158-9ca2-f193bd3e7227] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:01:29.695 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 32 ms 
[INFO ] 2024-06-07 18:01:29.695 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 32 ms 
[INFO ] 2024-06-07 18:01:29.936 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:01:29.936 - [任务 6(101)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:29.936 - [任务 6(101)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:29.937 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:01:29.937 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:01:30.013 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 17 ms 
[INFO ] 2024-06-07 18:01:30.013 - [任务 6(101)][增强JS] - create script executor for SourceDB2 
[ERROR] 2024-06-07 18:01:30.210 - [任务 6(101)][增强JS] - javax.script.ScriptException: java.lang.RuntimeException: script execute error <-- Error Message -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error

<-- Simple Stack Trace -->
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.TEST.TEST1, DRIVER=4.25.13
	com.ibm.db2.jcc.am.b6.a(b6.java:810)
	com.ibm.db2.jcc.am.b6.a(b6.java:66)
	com.ibm.db2.jcc.am.b6.a(b6.java:140)
	com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.TEST.TEST1, DRIVER=4.25.13
	at com.ibm.db2.jcc.am.b6.a(b6.java:810)
	at com.ibm.db2.jcc.am.b6.a(b6.java:66)
	at com.ibm.db2.jcc.am.b6.a(b6.java:140)
	at com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	at com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	at com.ibm.db2.jcc.am.k3.a(k3.java:2234)
	at com.ibm.db2.jcc.t4.ab.i(ab.java:206)
	at com.ibm.db2.jcc.t4.ab.b(ab.java:96)
	at com.ibm.db2.jcc.t4.p.a(p.java:32)
	at com.ibm.db2.jcc.t4.av.i(av.java:150)
	at com.ibm.db2.jcc.am.k3.al(k3.java:2203)
	at com.ibm.db2.jcc.am.k3.a(k3.java:3330)
	at com.ibm.db2.jcc.am.k3.e(k3.java:1131)
	at com.ibm.db2.jcc.am.k3.execute(k3.java:1110)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.DefaultSqlExecutor.execute(DefaultSqlExecutor.java:49)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:76)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:64)
	at io.tapdata.connector.db2.Db2Connector.lambda$registerCapabilities$2(Db2Connector.java:136)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:238)
	... 55 more

[INFO ] 2024-06-07 18:01:32.807 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:01:32.811 - [任务 6(101)][d8937f29-8ed4-4158-9ca2-f193bd3e7227] - Node d8937f29-8ed4-4158-9ca2-f193bd3e7227[d8937f29-8ed4-4158-9ca2-f193bd3e7227] running status set to false 
[INFO ] 2024-06-07 18:01:32.811 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-74a5ff10-4a92-4ab0-ba66-be5e86e39f4b 
[INFO ] 2024-06-07 18:01:32.811 - [任务 6(101)][d8937f29-8ed4-4158-9ca2-f193bd3e7227] - Node d8937f29-8ed4-4158-9ca2-f193bd3e7227[d8937f29-8ed4-4158-9ca2-f193bd3e7227] schema data cleaned 
[INFO ] 2024-06-07 18:01:32.812 - [任务 6(101)][d8937f29-8ed4-4158-9ca2-f193bd3e7227] - Node d8937f29-8ed4-4158-9ca2-f193bd3e7227[d8937f29-8ed4-4158-9ca2-f193bd3e7227] monitor closed 
[INFO ] 2024-06-07 18:01:32.812 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-74a5ff10-4a92-4ab0-ba66-be5e86e39f4b 
[INFO ] 2024-06-07 18:01:32.812 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:32.818 - [任务 6(101)][d8937f29-8ed4-4158-9ca2-f193bd3e7227] - Node d8937f29-8ed4-4158-9ca2-f193bd3e7227[d8937f29-8ed4-4158-9ca2-f193bd3e7227] close complete, cost 39 ms 
[INFO ] 2024-06-07 18:01:32.827 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-7d56dddd-a496-4612-9058-c35c4a850d08 
[INFO ] 2024-06-07 18:01:32.827 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-7d56dddd-a496-4612-9058-c35c4a850d08 
[INFO ] 2024-06-07 18:01:32.827 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:32.833 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:01:32.833 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:01:32.846 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 63 ms 
[INFO ] 2024-06-07 18:01:32.846 - [任务 6(101)] - test run task 6662d40a57e5ef2d1bd67235 complete, cost 3304ms 
[INFO ] 2024-06-07 18:01:49.207 - [任务 6(101)] - 6662d40a57e5ef2d1bd67235 task start 
[INFO ] 2024-06-07 18:01:49.277 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:49.277 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:01:49.277 - [任务 6(101)][7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] - Node 7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f[7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:01:49.277 - [任务 6(101)][7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] - Node 7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f[7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:01:49.290 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 13 ms 
[INFO ] 2024-06-07 18:01:49.291 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 14 ms 
[INFO ] 2024-06-07 18:01:49.827 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:01:49.850 - [任务 6(101)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:49.850 - [任务 6(101)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:01:49.850 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:01:49.850 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:01:49.850 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 24 ms 
[INFO ] 2024-06-07 18:01:49.870 - [任务 6(101)][增强JS] - create script executor for SourceDB2 
[ERROR] 2024-06-07 18:01:50.162 - [任务 6(101)][增强JS] - javax.script.ScriptException: java.lang.RuntimeException: script execute error <-- Error Message -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error

<-- Simple Stack Trace -->
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.TEST.TEST1, DRIVER=4.25.13
	com.ibm.db2.jcc.am.b6.a(b6.java:810)
	com.ibm.db2.jcc.am.b6.a(b6.java:66)
	com.ibm.db2.jcc.am.b6.a(b6.java:140)
	com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:237)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:189)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: java.lang.RuntimeException: script execute error
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:252)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.executeQuery(ScriptExecutorsManager.java:201)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute_generic4(JSWriteCurrentFrameSlotNodeGen.java:163)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.execute(JSWriteCurrentFrameSlotNodeGen.java:86)
	at com.oracle.truffle.js.nodes.access.JSWriteCurrentFrameSlotNodeGen.executeVoid(JSWriteCurrentFrameSlotNodeGen.java:317)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more
Caused by: com.ibm.db2.jcc.am.SqlSyntaxErrorException: DB2 SQL Error: SQLCODE=-204, SQLSTATE=42704, SQLERRMC=DB2INST1.TEST.TEST1, DRIVER=4.25.13
	at com.ibm.db2.jcc.am.b6.a(b6.java:810)
	at com.ibm.db2.jcc.am.b6.a(b6.java:66)
	at com.ibm.db2.jcc.am.b6.a(b6.java:140)
	at com.ibm.db2.jcc.am.k3.c(k3.java:2824)
	at com.ibm.db2.jcc.am.k3.d(k3.java:2808)
	at com.ibm.db2.jcc.am.k3.a(k3.java:2234)
	at com.ibm.db2.jcc.t4.ab.i(ab.java:206)
	at com.ibm.db2.jcc.t4.ab.b(ab.java:96)
	at com.ibm.db2.jcc.t4.p.a(p.java:32)
	at com.ibm.db2.jcc.t4.av.i(av.java:150)
	at com.ibm.db2.jcc.am.k3.al(k3.java:2203)
	at com.ibm.db2.jcc.am.k3.a(k3.java:3330)
	at com.ibm.db2.jcc.am.k3.e(k3.java:1131)
	at com.ibm.db2.jcc.am.k3.execute(k3.java:1110)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.DefaultSqlExecutor.execute(DefaultSqlExecutor.java:49)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:76)
	at io.tapdata.common.SqlExecuteCommandFunction.executeCommand(SqlExecuteCommandFunction.java:64)
	at io.tapdata.connector.db2.Db2Connector.lambda$registerCapabilities$2(Db2Connector.java:136)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.pdkExecute(ScriptExecutorsManager.java:238)
	... 55 more

[INFO ] 2024-06-07 18:01:52.716 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:01:52.717 - [任务 6(101)][7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] - Node 7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f[7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] running status set to false 
[INFO ] 2024-06-07 18:01:52.717 - [任务 6(101)][7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] - Node 7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f[7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.717 - [任务 6(101)][7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] - Node 7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f[7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] monitor closed 
[INFO ] 2024-06-07 18:01:52.717 - [任务 6(101)][7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] - Node 7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f[7d853f72-9c93-4ee7-83cf-ffc6a2ac8b1f] close complete, cost 14 ms 
[INFO ] 2024-06-07 18:01:52.721 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-febf8ca6-737f-4773-bf49-2462e28c0b42 
[INFO ] 2024-06-07 18:01:52.729 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-febf8ca6-737f-4773-bf49-2462e28c0b42 
[INFO ] 2024-06-07 18:01:52.729 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.730 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-0598f6bb-8e62-4cee-a94e-32a4cc55797b 
[INFO ] 2024-06-07 18:01:52.730 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-0598f6bb-8e62-4cee-a94e-32a4cc55797b 
[INFO ] 2024-06-07 18:01:52.730 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.733 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:01:52.734 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:01:52.734 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 33 ms 
[INFO ] 2024-06-07 18:01:52.939 - [任务 6(101)] - test run task 6662d40a57e5ef2d1bd67235 complete, cost 3542ms 
[INFO ] 2024-06-07 18:02:05.128 - [任务 6(101)] - 6662d40a57e5ef2d1bd67235 task start 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(101)][42c339bb-9cc3-49f3-be57-de6d14c1d1b9] - Node 42c339bb-9cc3-49f3-be57-de6d14c1d1b9[42c339bb-9cc3-49f3-be57-de6d14c1d1b9] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:05.189 - [任务 6(101)][42c339bb-9cc3-49f3-be57-de6d14c1d1b9] - Node 42c339bb-9cc3-49f3-be57-de6d14c1d1b9[42c339bb-9cc3-49f3-be57-de6d14c1d1b9] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:05.215 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 24 ms 
[INFO ] 2024-06-07 18:02:05.215 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 24 ms 
[INFO ] 2024-06-07 18:02:05.774 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:02:05.800 - [任务 6(101)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:05.800 - [任务 6(101)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:05.814 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:02:05.824 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:02:05.830 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 51 ms 
[INFO ] 2024-06-07 18:02:05.849 - [任务 6(101)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 18:02:06.090 - [任务 6(101)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 18:02:06.090 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:02:06.094 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-1c1b4445-2657-47fa-a4da-1629573a0a82 
[INFO ] 2024-06-07 18:02:06.094 - [任务 6(101)][42c339bb-9cc3-49f3-be57-de6d14c1d1b9] - Node 42c339bb-9cc3-49f3-be57-de6d14c1d1b9[42c339bb-9cc3-49f3-be57-de6d14c1d1b9] running status set to false 
[INFO ] 2024-06-07 18:02:06.094 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-1c1b4445-2657-47fa-a4da-1629573a0a82 
[INFO ] 2024-06-07 18:02:06.094 - [任务 6(101)][42c339bb-9cc3-49f3-be57-de6d14c1d1b9] - Node 42c339bb-9cc3-49f3-be57-de6d14c1d1b9[42c339bb-9cc3-49f3-be57-de6d14c1d1b9] schema data cleaned 
[INFO ] 2024-06-07 18:02:06.094 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:06.095 - [任务 6(101)][42c339bb-9cc3-49f3-be57-de6d14c1d1b9] - Node 42c339bb-9cc3-49f3-be57-de6d14c1d1b9[42c339bb-9cc3-49f3-be57-de6d14c1d1b9] monitor closed 
[INFO ] 2024-06-07 18:02:06.095 - [任务 6(101)][42c339bb-9cc3-49f3-be57-de6d14c1d1b9] - Node 42c339bb-9cc3-49f3-be57-de6d14c1d1b9[42c339bb-9cc3-49f3-be57-de6d14c1d1b9] close complete, cost 1 ms 
[INFO ] 2024-06-07 18:02:06.100 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-b8c00e14-4dda-4ce4-ab9d-bd326a8309b5 
[INFO ] 2024-06-07 18:02:06.100 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-b8c00e14-4dda-4ce4-ab9d-bd326a8309b5 
[INFO ] 2024-06-07 18:02:06.100 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:06.102 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:02:06.102 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:02:06.102 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 13 ms 
[INFO ] 2024-06-07 18:02:06.308 - [任务 6(101)] - test run task 6662d40a57e5ef2d1bd67235 complete, cost 995ms 
[INFO ] 2024-06-07 18:02:19.723 - [任务 6(101)] - 6662d40a57e5ef2d1bd67235 task start 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] start preload schema,table counts: 1 
[INFO ] 2024-06-07 18:02:19.764 - [任务 6(101)][0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] - Node 0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb[0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] start preload schema,table counts: 0 
[INFO ] 2024-06-07 18:02:19.765 - [任务 6(101)][0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] - Node 0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb[0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] preload schema finished, cost 0 ms 
[INFO ] 2024-06-07 18:02:19.817 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] preload schema finished, cost 53 ms 
[INFO ] 2024-06-07 18:02:19.818 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] preload schema finished, cost 52 ms 
[INFO ] 2024-06-07 18:02:20.398 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] running status set to false 
[INFO ] 2024-06-07 18:02:20.425 - [任务 6(101)][TEST1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:20.428 - [任务 6(101)][TEST1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98a94377-ee36-4ca2-af53-41a5c57eb841 
[INFO ] 2024-06-07 18:02:20.430 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.430 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] monitor closed 
[INFO ] 2024-06-07 18:02:20.432 - [任务 6(101)][TEST1] - Node TEST1[98a94377-ee36-4ca2-af53-41a5c57eb841] close complete, cost 38 ms 
[INFO ] 2024-06-07 18:02:20.533 - [任务 6(101)][增强JS] - create script executor for SourceDB2 
[INFO ] 2024-06-07 18:02:20.714 - [任务 6(101)][增强JS] - [{1=1}] 
[INFO ] 2024-06-07 18:02:20.714 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] running status set to false 
[INFO ] 2024-06-07 18:02:20.716 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-417db987-7f0d-4ae3-9459-f987f7df1432 
[INFO ] 2024-06-07 18:02:20.716 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-417db987-7f0d-4ae3-9459-f987f7df1432 
[INFO ] 2024-06-07 18:02:20.716 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.721 - [任务 6(101)][0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] - Node 0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb[0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] running status set to false 
[INFO ] 2024-06-07 18:02:20.721 - [任务 6(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDB2-06a2dc00-e322-48f2-ad7e-01c5083a8c93 
[INFO ] 2024-06-07 18:02:20.721 - [任务 6(101)][0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] - Node 0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb[0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.721 - [任务 6(101)][0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] - Node 0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb[0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] monitor closed 
[INFO ] 2024-06-07 18:02:20.721 - [任务 6(101)][0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] - Node 0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb[0a3c7e9b-ecde-4d0e-8ee5-12f5b20b52cb] close complete, cost 0 ms 
[INFO ] 2024-06-07 18:02:20.722 - [任务 6(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceDB2-06a2dc00-e322-48f2-ad7e-01c5083a8c93 
[INFO ] 2024-06-07 18:02:20.722 - [任务 6(101)][增强JS] - [ScriptExecutorsManager-6662d40a57e5ef2d1bd67235-f768fd98-1c20-46a6-9a75-67bf0f577784-6662d6d057e5ef2d1bd67379] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.724 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] schema data cleaned 
[INFO ] 2024-06-07 18:02:20.724 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] monitor closed 
[INFO ] 2024-06-07 18:02:20.724 - [任务 6(101)][增强JS] - Node 增强JS[f768fd98-1c20-46a6-9a75-67bf0f577784] close complete, cost 10 ms 
[INFO ] 2024-06-07 18:02:20.728 - [任务 6(101)] - test run task 6662d40a57e5ef2d1bd67235 complete, cost 1062ms 
