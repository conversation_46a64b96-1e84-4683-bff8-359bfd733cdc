[INFO ] 2024-08-14 18:48:10.789 - [任务 3] - Start task milestones: 66bc8b401e4c6255357bbee2(任务 3) 
[INFO ] 2024-08-14 18:48:10.997 - [任务 3] - Task initialization... 
[INFO ] 2024-08-14 18:48:12.017 - [任务 3] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-08-14 18:48:12.017 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 18:48:12.654 - [任务 3][PG] - Node PG[656dbf97-6023-4147-aa9f-73d7f0ad41ed] start preload schema,table counts: 3 
[INFO ] 2024-08-14 18:48:12.655 - [任务 3][Sql] - Node Sql[d1fa20f8-70cc-451b-93c9-e7cd8082c1e8] start preload schema,table counts: 3 
[INFO ] 2024-08-14 18:48:12.655 - [任务 3][Sql] - Node Sql[d1fa20f8-70cc-451b-93c9-e7cd8082c1e8] preload schema finished, cost 1 ms 
[INFO ] 2024-08-14 18:48:12.656 - [任务 3][PG] - Node PG[656dbf97-6023-4147-aa9f-73d7f0ad41ed] preload schema finished, cost 2 ms 
[INFO ] 2024-08-14 18:48:13.841 - [任务 3][Sql] - Node(Sql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 18:48:13.842 - [任务 3][Sql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-14 18:48:13.952 - [任务 3][PG] - Source node "PG" read batch size: 100 
[INFO ] 2024-08-14 18:48:13.954 - [任务 3][PG] - Source node "PG" event queue capacity: 200 
[INFO ] 2024-08-14 18:48:13.955 - [任务 3][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-14 18:48:14.351 - [任务 3][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-08-14 18:48:14.376 - [任务 3][PG] - new logical replication slot created, slotName:tapdata_cdc_a4f77c41_107c_459d_b8f1_bb994f9600f5 
[INFO ] 2024-08-14 18:48:14.397 - [任务 3][PG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-08-14 18:48:14.399 - [任务 3][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 18:48:14.460 - [任务 3][PG] - Initial sync started 
[INFO ] 2024-08-14 18:48:14.460 - [任务 3][PG] - Starting batch read, table name: sales_2022_q1_q2 
[INFO ] 2024-08-14 18:48:14.605 - [任务 3][PG] - Table sales_2022_q1_q2 is going to be initial synced 
[INFO ] 2024-08-14 18:48:14.606 - [任务 3][PG] - Table [sales_2022_q1_q2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 18:48:14.620 - [任务 3][PG] - Query table 'sales_2022_q1_q2' counts: 1 
[INFO ] 2024-08-14 18:48:14.620 - [任务 3][PG] - Starting batch read, table name: sales_2022_q3_q4 
[INFO ] 2024-08-14 18:48:14.632 - [任务 3][PG] - Table sales_2022_q3_q4 is going to be initial synced 
[INFO ] 2024-08-14 18:48:14.634 - [任务 3][PG] - Query table 'sales_2022_q3_q4' counts: 0 
[INFO ] 2024-08-14 18:48:14.651 - [任务 3][PG] - Table [sales_2022_q3_q4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 18:48:14.651 - [任务 3][PG] - Starting batch read, table name: sales_2023_onward 
[INFO ] 2024-08-14 18:48:14.662 - [任务 3][PG] - Table sales_2023_onward is going to be initial synced 
[INFO ] 2024-08-14 18:48:14.663 - [任务 3][PG] - Query table 'sales_2023_onward' counts: 0 
[INFO ] 2024-08-14 18:48:14.677 - [任务 3][PG] - Table [sales_2023_onward] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 18:48:14.678 - [任务 3][PG] - Initial sync completed 
[INFO ] 2024-08-14 18:48:14.681 - [任务 3][PG] - Incremental sync starting... 
[INFO ] 2024-08-14 18:48:14.681 - [任务 3][PG] - Initial sync completed 
[INFO ] 2024-08-14 18:48:14.695 - [任务 3][PG] - Starting stream read, table list: [sales_2022_q1_q2, sales_2022_q3_q4, sales_2023_onward], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[WARN ] 2024-08-14 18:48:14.696 - [任务 3][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-08-14 18:48:14.905 - [任务 3][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_a4f77c41_107c_459d_b8f1_bb994f9600f5 
[INFO ] 2024-08-14 18:48:15.304 - [任务 3][PG] - Connector PostgreSQL incremental start succeed, tables: [sales_2022_q1_q2, sales_2022_q3_q4, sales_2023_onward], data change syncing 
[INFO ] 2024-08-14 19:09:45.120 - [任务 3][PG] - Node PG[656dbf97-6023-4147-aa9f-73d7f0ad41ed] running status set to false 
[INFO ] 2024-08-14 19:09:45.120 - [任务 3][PG] - Incremental sync completed 
[INFO ] 2024-08-14 19:09:45.145 - [任务 3][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode-656dbf97-6023-4147-aa9f-73d7f0ad41ed 
[INFO ] 2024-08-14 19:09:45.146 - [任务 3][PG] - PDK connector node released: HazelcastSourcePdkDataNode-656dbf97-6023-4147-aa9f-73d7f0ad41ed 
[INFO ] 2024-08-14 19:09:45.147 - [任务 3][PG] - Node PG[656dbf97-6023-4147-aa9f-73d7f0ad41ed] schema data cleaned 
[INFO ] 2024-08-14 19:09:45.148 - [任务 3][PG] - Node PG[656dbf97-6023-4147-aa9f-73d7f0ad41ed] monitor closed 
[INFO ] 2024-08-14 19:09:45.161 - [任务 3][PG] - Node PG[656dbf97-6023-4147-aa9f-73d7f0ad41ed] close complete, cost 84 ms 
[INFO ] 2024-08-14 19:09:45.161 - [任务 3][Sql] - Node Sql[d1fa20f8-70cc-451b-93c9-e7cd8082c1e8] running status set to false 
[INFO ] 2024-08-14 19:09:45.189 - [任务 3][Sql] - PDK connector node stopped: HazelcastTargetPdkDataNode-d1fa20f8-70cc-451b-93c9-e7cd8082c1e8 
[INFO ] 2024-08-14 19:09:45.191 - [任务 3][Sql] - PDK connector node released: HazelcastTargetPdkDataNode-d1fa20f8-70cc-451b-93c9-e7cd8082c1e8 
[INFO ] 2024-08-14 19:09:45.191 - [任务 3][Sql] - Node Sql[d1fa20f8-70cc-451b-93c9-e7cd8082c1e8] schema data cleaned 
[INFO ] 2024-08-14 19:09:45.193 - [任务 3][Sql] - Node Sql[d1fa20f8-70cc-451b-93c9-e7cd8082c1e8] monitor closed 
[INFO ] 2024-08-14 19:09:45.399 - [任务 3][Sql] - Node Sql[d1fa20f8-70cc-451b-93c9-e7cd8082c1e8] close complete, cost 33 ms 
[INFO ] 2024-08-14 19:09:46.542 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 19:09:46.542 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@743df627 
[INFO ] 2024-08-14 19:09:46.857 - [任务 3] - Stop task milestones: 66bc8b401e4c6255357bbee2(任务 3)  
[INFO ] 2024-08-14 19:09:47.166 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-08-14 19:09:47.167 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 19:09:47.308 - [任务 3] - Remove memory task client succeed, task: 任务 3[66bc8b401e4c6255357bbee2] 
[INFO ] 2024-08-14 19:09:47.309 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66bc8b401e4c6255357bbee2] 
