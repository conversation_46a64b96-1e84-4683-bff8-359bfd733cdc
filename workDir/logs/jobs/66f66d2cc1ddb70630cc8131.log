[INFO ] 2024-09-27 16:31:00.186 - [任务 6] - Task initialization... 
[INFO ] 2024-09-27 16:31:00.360 - [任务 6] - Start task milestones: 66f66d2cc1ddb70630cc8131(任务 6) 
[INFO ] 2024-09-27 16:31:00.361 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:31:00.523 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:31:00.523 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:31:00.523 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:31:00.523 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 16:31:00.524 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:31:00.840 - [任务 6][SourceMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_be08c3bc-d35c-4e45-9e5c-010a0420a137 
[ERROR] 2024-09-27 16:31:00.863 - [任务 6][SourceMongo] - Map name: PdkStateMap_be08c3bc-d35c-4e45-9e5c-010a0420a137 <-- Error Message -->
Map name: PdkStateMap_be08c3bc-d35c-4e45-9e5c-010a0420a137

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727425860, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "4gOZxGpKXPmbrfGW/u1mugj0geY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727425860, "i" : 41 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_be08c3bc-d35c-4e45-9e5c-010a0420a137
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:452)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:185)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727425860, "i" : 41 } }, "signature" : { "hash" : { "$binary" : "4gOZxGpKXPmbrfGW/u1mugj0geY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727425860, "i" : 41 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 46 more

[INFO ] 2024-09-27 16:31:00.878 - [任务 6][SourceMongo] - Job suspend in error handle 
[INFO ] 2024-09-27 16:31:01.101 - [任务 6][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:31:01.101 - [任务 6][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:31:01.101 - [任务 6][local3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 16:31:01.134 - [任务 6][local3307] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:31:01.169 - [任务 6][local3307] - Initial sync started 
[INFO ] 2024-09-27 16:31:01.169 - [任务 6][local3307] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-09-27 16:31:01.232 - [任务 6][local3307] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-09-27 16:31:01.232 - [任务 6][local3307] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 16:31:01.236 - [任务 6][local3307] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-09-27 16:31:01.236 - [任务 6][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:31:01.236 - [任务 6][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:31:01.236 - [任务 6][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:31:01.274 - [任务 6][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:31:01.299 - [任务 6][local3307] - Starting mysql cdc, server name: d85e9659-23b5-4873-9584-053db3bb9a99 
[INFO ] 2024-09-27 16:31:01.302 - [任务 6][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"d85e9659-23b5-4873-9584-053db3bb9a99","offset":{"{\"server\":\"d85e9659-23b5-4873-9584-053db3bb9a99\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 398007429
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d85e9659-23b5-4873-9584-053db3bb9a99
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-d85e9659-23b5-4873-9584-053db3bb9a99
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: d85e9659-23b5-4873-9584-053db3bb9a99
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:31:01.351 - [任务 6][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 16:31:01.760 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] running status set to false 
[INFO ] 2024-09-27 16:31:01.806 - [任务 6][local3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 16:31:01.818 - [任务 6][local3307] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 16:31:01.839 - [任务 6][local3307] - Incremental sync completed 
[INFO ] 2024-09-27 16:31:01.839 - [任务 6][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-47f8a9a4-450f-41fa-81e6-c54c49954d7b 
[INFO ] 2024-09-27 16:31:01.839 - [任务 6][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-47f8a9a4-450f-41fa-81e6-c54c49954d7b 
[INFO ] 2024-09-27 16:31:01.839 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] schema data cleaned 
[INFO ] 2024-09-27 16:31:01.840 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] monitor closed 
[INFO ] 2024-09-27 16:31:01.841 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] close complete, cost 160 ms 
[INFO ] 2024-09-27 16:31:01.841 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] running status set to false 
[INFO ] 2024-09-27 16:31:01.841 - [任务 6][SourceMongo] - PDK connector node stopped: null 
[INFO ] 2024-09-27 16:31:01.841 - [任务 6][SourceMongo] - PDK connector node released: null 
[INFO ] 2024-09-27 16:31:01.841 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] schema data cleaned 
[INFO ] 2024-09-27 16:31:01.841 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] monitor closed 
[INFO ] 2024-09-27 16:31:01.841 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] close complete, cost 0 ms 
[INFO ] 2024-09-27 16:31:06.312 - [任务 6] - Task [任务 6] cannot retry, reason: Task retry service not start 
[INFO ] 2024-09-27 16:31:06.317 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 16:31:06.317 - [任务 6] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@11731083 
[INFO ] 2024-09-27 16:31:06.461 - [任务 6] - Stop task milestones: 66f66d2cc1ddb70630cc8131(任务 6)  
[INFO ] 2024-09-27 16:31:06.461 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-09-27 16:31:06.461 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 16:31:06.532 - [任务 6] - Remove memory task client succeed, task: 任务 6[66f66d2cc1ddb70630cc8131] 
[INFO ] 2024-09-27 16:31:06.536 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[66f66d2cc1ddb70630cc8131] 
[INFO ] 2024-09-27 16:32:44.692 - [任务 6] - Task initialization... 
[INFO ] 2024-09-27 16:32:44.856 - [任务 6] - Start task milestones: 66f66d2cc1ddb70630cc8131(任务 6) 
[INFO ] 2024-09-27 16:32:44.856 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:32:45.037 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:32:45.038 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:32:45.038 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:32:45.038 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:32:45.038 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 16:33:17.332 - [任务 6][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:33:17.332 - [任务 6][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:33:17.332 - [任务 6][local3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 16:33:17.400 - [任务 6][local3307] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:33:17.401 - [任务 6][local3307] - Initial sync started 
[INFO ] 2024-09-27 16:33:17.401 - [任务 6][local3307] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-09-27 16:33:17.438 - [任务 6][local3307] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-09-27 16:33:17.446 - [任务 6][local3307] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 16:33:17.452 - [任务 6][local3307] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-09-27 16:33:17.452 - [任务 6][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:33:17.453 - [任务 6][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:33:17.453 - [任务 6][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:33:17.485 - [任务 6][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:33:17.485 - [任务 6][local3307] - Starting mysql cdc, server name: ad67a433-b560-434d-b2ef-017e99c7c692 
[INFO ] 2024-09-27 16:33:17.493 - [任务 6][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"ad67a433-b560-434d-b2ef-017e99c7c692","offset":{"{\"server\":\"ad67a433-b560-434d-b2ef-017e99c7c692\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1063897436
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ad67a433-b560-434d-b2ef-017e99c7c692
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-ad67a433-b560-434d-b2ef-017e99c7c692
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: ad67a433-b560-434d-b2ef-017e99c7c692
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:33:17.493 - [任务 6][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:33:17.493 - [任务 6][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 16:33:17.594 - [任务 6][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[WARN ] 2024-09-27 16:33:18.502 - [任务 6][SourceMongo] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[47f8a9a4-450f-41fa-81e6-c54c49954d7b], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-27 16:38:20.009 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] running status set to false 
[INFO ] 2024-09-27 16:38:20.097 - [任务 6][local3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 16:38:20.097 - [任务 6][local3307] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 16:38:20.097 - [任务 6][local3307] - Incremental sync completed 
[INFO ] 2024-09-27 16:38:20.111 - [任务 6][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-47f8a9a4-450f-41fa-81e6-c54c49954d7b 
[INFO ] 2024-09-27 16:38:20.111 - [任务 6][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-47f8a9a4-450f-41fa-81e6-c54c49954d7b 
[INFO ] 2024-09-27 16:38:20.111 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] schema data cleaned 
[INFO ] 2024-09-27 16:38:20.111 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] monitor closed 
[INFO ] 2024-09-27 16:38:20.113 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] close complete, cost 110 ms 
[INFO ] 2024-09-27 16:38:20.113 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] running status set to false 
[INFO ] 2024-09-27 16:38:20.173 - [任务 6][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-be08c3bc-d35c-4e45-9e5c-010a0420a137 
[INFO ] 2024-09-27 16:38:20.173 - [任务 6][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-be08c3bc-d35c-4e45-9e5c-010a0420a137 
[INFO ] 2024-09-27 16:38:20.173 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] schema data cleaned 
[INFO ] 2024-09-27 16:38:20.173 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] monitor closed 
[INFO ] 2024-09-27 16:38:20.193 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] close complete, cost 60 ms 
[INFO ] 2024-09-27 16:38:23.214 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 16:38:23.274 - [任务 6] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@13edb93c 
[INFO ] 2024-09-27 16:38:23.339 - [任务 6] - Stop task milestones: 66f66d2cc1ddb70630cc8131(任务 6)  
[INFO ] 2024-09-27 16:38:23.339 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-09-27 16:38:23.339 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 16:38:23.357 - [任务 6] - Remove memory task client succeed, task: 任务 6[66f66d2cc1ddb70630cc8131] 
[INFO ] 2024-09-27 16:38:23.360 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[66f66d2cc1ddb70630cc8131] 
[INFO ] 2024-09-27 16:38:26.561 - [任务 6] - Task initialization... 
[INFO ] 2024-09-27 16:38:26.561 - [任务 6] - Start task milestones: 66f66d2cc1ddb70630cc8131(任务 6) 
[INFO ] 2024-09-27 16:38:26.685 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:38:26.685 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:38:26.730 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:38:26.730 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:38:26.730 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:38:26.730 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:38:27.212 - [任务 6][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:38:27.219 - [任务 6][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 16:38:27.241 - [任务 6][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:38:27.242 - [任务 6][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:38:27.248 - [任务 6][local3307] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:38:27.249 - [任务 6][local3307] - batch offset found: {"BMSQL_ITEM":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"ad67a433-b560-434d-b2ef-017e99c7c692","offset":{"{\"server\":\"ad67a433-b560-434d-b2ef-017e99c7c692\"}":"{\"ts_sec\":1727425997,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:38:27.307 - [任务 6][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:38:27.307 - [任务 6][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:38:27.330 - [任务 6][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"ad67a433-b560-434d-b2ef-017e99c7c692","offset":{"{\"server\":\"ad67a433-b560-434d-b2ef-017e99c7c692\"}":"{\"ts_sec\":1727425997,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:38:27.332 - [任务 6][local3307] - Starting mysql cdc, server name: ad67a433-b560-434d-b2ef-017e99c7c692 
[INFO ] 2024-09-27 16:38:27.443 - [任务 6][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"ad67a433-b560-434d-b2ef-017e99c7c692","offset":{"{\"server\":\"ad67a433-b560-434d-b2ef-017e99c7c692\"}":"{\"ts_sec\":1727425997,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 330061134
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ad67a433-b560-434d-b2ef-017e99c7c692
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-ad67a433-b560-434d-b2ef-017e99c7c692
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: ad67a433-b560-434d-b2ef-017e99c7c692
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:38:27.444 - [任务 6][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 17:13:33.037 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] running status set to false 
[INFO ] 2024-09-27 17:13:33.252 - [任务 6][local3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 17:13:33.282 - [任务 6][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-47f8a9a4-450f-41fa-81e6-c54c49954d7b 
[INFO ] 2024-09-27 17:13:33.282 - [任务 6][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-47f8a9a4-450f-41fa-81e6-c54c49954d7b 
[INFO ] 2024-09-27 17:13:33.283 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.283 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] monitor closed 
[INFO ] 2024-09-27 17:13:33.285 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] close complete, cost 262 ms 
[INFO ] 2024-09-27 17:13:33.286 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] running status set to false 
[INFO ] 2024-09-27 17:13:33.310 - [任务 6][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-be08c3bc-d35c-4e45-9e5c-010a0420a137 
[INFO ] 2024-09-27 17:13:33.310 - [任务 6][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-be08c3bc-d35c-4e45-9e5c-010a0420a137 
[INFO ] 2024-09-27 17:13:33.311 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.311 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] monitor closed 
[INFO ] 2024-09-27 17:13:33.311 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] close complete, cost 26 ms 
[INFO ] 2024-09-27 17:13:36.773 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 17:13:36.773 - [任务 6] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@522eae59 
[INFO ] 2024-09-27 17:13:36.882 - [任务 6] - Stop task milestones: 66f66d2cc1ddb70630cc8131(任务 6)  
[INFO ] 2024-09-27 17:13:36.900 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-09-27 17:13:36.900 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 17:13:36.923 - [任务 6] - Remove memory task client succeed, task: 任务 6[66f66d2cc1ddb70630cc8131] 
[INFO ] 2024-09-27 17:13:36.931 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[66f66d2cc1ddb70630cc8131] 
[INFO ] 2024-09-27 17:16:08.759 - [任务 6] - Task initialization... 
[INFO ] 2024-09-27 17:16:08.817 - [任务 6] - Start task milestones: 66f66d2cc1ddb70630cc8131(任务 6) 
[INFO ] 2024-09-27 17:16:09.138 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:16:09.625 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:16:09.699 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.699 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.703 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 17:16:09.704 - [任务 6][SourceMongo] - Node SourceMongo[be08c3bc-d35c-4e45-9e5c-010a0420a137] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:16:10.240 - [任务 6][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 17:16:10.241 - [任务 6][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 17:16:10.872 - [任务 6][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 17:16:10.872 - [任务 6][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 17:16:10.873 - [任务 6][local3307] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 17:16:10.885 - [任务 6][local3307] - batch offset found: {},stream offset found: {"name":"ad67a433-b560-434d-b2ef-017e99c7c692","offset":{"{\"server\":\"ad67a433-b560-434d-b2ef-017e99c7c692\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:10.919 - [任务 6][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 17:16:10.919 - [任务 6][local3307] - Initial sync completed 
[INFO ] 2024-09-27 17:16:10.922 - [任务 6][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"ad67a433-b560-434d-b2ef-017e99c7c692","offset":{"{\"server\":\"ad67a433-b560-434d-b2ef-017e99c7c692\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:11.008 - [任务 6][local3307] - Starting mysql cdc, server name: ad67a433-b560-434d-b2ef-017e99c7c692 
[INFO ] 2024-09-27 17:16:11.105 - [任务 6][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"ad67a433-b560-434d-b2ef-017e99c7c692","offset":{"{\"server\":\"ad67a433-b560-434d-b2ef-017e99c7c692\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1781668379
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ad67a433-b560-434d-b2ef-017e99c7c692
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-ad67a433-b560-434d-b2ef-017e99c7c692
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: ad67a433-b560-434d-b2ef-017e99c7c692
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 17:16:12.843 - [任务 6][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 18:53:28.742 - [任务 6][local3307] - Node local3307[47f8a9a4-450f-41fa-81e6-c54c49954d7b] running status set to false 
