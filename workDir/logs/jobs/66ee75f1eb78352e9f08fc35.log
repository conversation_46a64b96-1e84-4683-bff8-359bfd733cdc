[INFO ] 2024-09-27 15:49:50.851 - [测试主从，同一个连接] - Start task milestones: 66ee75f1eb78352e9f08fc35(测试主从，同一个连接) 
[INFO ] 2024-09-27 15:49:50.854 - [测试主从，同一个连接] - Task initialization... 
[INFO ] 2024-09-27 15:49:51.134 - [测试主从，同一个连接] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 15:49:51.135 - [测试主从，同一个连接] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 15:49:51.135 - [测试主从，同一个连接] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@40e40773 
[INFO ] 2024-09-27 15:49:51.265 - [测试主从，同一个连接] - Stop task milestones: 66ee75f1eb78352e9f08fc35(测试主从，同一个连接)  
[INFO ] 2024-09-27 15:49:51.266 - [测试主从，同一个连接] - Stopped task aspect(s) 
[INFO ] 2024-09-27 15:49:51.266 - [测试主从，同一个连接] - Snapshot order controller have been removed 
[ERROR] 2024-09-27 15:49:51.271 - [测试主从，同一个连接] - Node [id 590c18dc-18d3-4bf8-b6fd-550678684c73, name LocalhostMaster] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 590c18dc-18d3-4bf8-b6fd-550678684c73, name LocalhostMaster] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:323)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:190)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:177)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:112)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:342)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:199)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

