[INFO ] 2024-07-28 11:36:49.072 - [任务 2][POLICY] - Node POLICY[0ada31cc-c9f3-4fdb-9c95-1761509cebf4] running status set to false 
[INFO ] 2024-07-28 11:36:49.260 - [任务 2][POLICY] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-28 11:36:49.263 - [任务 2][POLICY] - Mysql binlog reader stopped 
[INFO ] 2024-07-28 11:36:49.264 - [任务 2][POLICY] - Incremental sync completed 
[INFO ] 2024-07-28 11:36:49.268 - [任务 2][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-0ada31cc-c9f3-4fdb-9c95-1761509cebf4 
[INFO ] 2024-07-28 11:36:49.269 - [任务 2][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-0ada31cc-c9f3-4fdb-9c95-1761509cebf4 
[INFO ] 2024-07-28 11:36:49.273 - [任务 2][POLICY] - Node POLICY[0ada31cc-c9f3-4fdb-9c95-1761509cebf4] schema data cleaned 
[INFO ] 2024-07-28 11:36:49.273 - [任务 2][POLICY] - Node POLICY[0ada31cc-c9f3-4fdb-9c95-1761509cebf4] monitor closed 
[INFO ] 2024-07-28 11:36:49.278 - [任务 2][POLICY] - Node POLICY[0ada31cc-c9f3-4fdb-9c95-1761509cebf4] close complete, cost 254 ms 
[INFO ] 2024-07-28 11:36:49.278 - [任务 2][TESTPO] - Node TESTPO[21ca2366-dc26-4469-9e3b-fec96745bcec] running status set to false 
[INFO ] 2024-07-28 11:36:49.346 - [任务 2][TESTPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-21ca2366-dc26-4469-9e3b-fec96745bcec 
[INFO ] 2024-07-28 11:36:49.347 - [任务 2][TESTPO] - PDK connector node released: HazelcastTargetPdkDataNode-21ca2366-dc26-4469-9e3b-fec96745bcec 
[INFO ] 2024-07-28 11:36:49.349 - [任务 2][TESTPO] - Node TESTPO[21ca2366-dc26-4469-9e3b-fec96745bcec] schema data cleaned 
[INFO ] 2024-07-28 11:36:49.349 - [任务 2][TESTPO] - Node TESTPO[21ca2366-dc26-4469-9e3b-fec96745bcec] monitor closed 
[INFO ] 2024-07-28 11:36:49.350 - [任务 2][TESTPO] - Node TESTPO[21ca2366-dc26-4469-9e3b-fec96745bcec] close complete, cost 58 ms 
[INFO ] 2024-07-28 11:36:53.306 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 11:36:53.312 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@42387980 
[INFO ] 2024-07-28 11:36:53.435 - [任务 2] - Stop task milestones: 66a4ddd27bd6415933d0d7f7(任务 2)  
[INFO ] 2024-07-28 11:36:53.435 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-07-28 11:36:53.454 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 11:36:53.458 - [任务 2] - Remove memory task client succeed, task: 任务 2[66a4ddd27bd6415933d0d7f7] 
[INFO ] 2024-07-28 11:36:53.458 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66a4ddd27bd6415933d0d7f7] 
