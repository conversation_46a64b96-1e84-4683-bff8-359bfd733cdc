[INFO ] 2024-07-26 15:47:27.932 - [任务 20] - Task initialization... 
[INFO ] 2024-07-26 15:47:27.944 - [任务 20] - Start task milestones: 66a01249e29f7d4f8d097a61(任务 20) 
[INFO ] 2024-07-26 15:47:28.101 - [任务 20] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 15:47:28.101 - [任务 20] - The engine receives 任务 20 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 15:47:28.162 - [任务 20][dummy_test] - Node dummy_test[a2079b75-9dd0-46ed-a80e-7ac387372241] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:47:28.163 - [任务 20][Inventory2] - Node Inventory2[03cea90c-35c1-4642-b198-1cc23f5e3332] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:47:28.163 - [任务 20][dummy_test] - Node dummy_test[a2079b75-9dd0-46ed-a80e-7ac387372241] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:47:28.163 - [任务 20][Inventory2] - Node Inventory2[03cea90c-35c1-4642-b198-1cc23f5e3332] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:47:28.861 - [任务 20][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 15:47:29.314 - [任务 20][Inventory2] - Source node "Inventory2" read batch size: 100 
[INFO ] 2024-07-26 15:47:29.314 - [任务 20][Inventory2] - Source node "Inventory2" event queue capacity: 200 
[INFO ] 2024-07-26 15:47:29.315 - [任务 20][Inventory2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 15:47:29.421 - [任务 20][Inventory2] - batch offset found: {},stream offset found: {"currentStartLSN":"000001DA000063E00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-26 15:47:29.421 - [任务 20][Inventory2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 15:47:29.476 - [任务 20][Inventory2] - Initial sync started 
[INFO ] 2024-07-26 15:47:29.476 - [任务 20][Inventory2] - Starting batch read, table name: Inventory2, offset: null 
[INFO ] 2024-07-26 15:47:29.480 - [任务 20][Inventory2] - Table Inventory2 is going to be initial synced 
[INFO ] 2024-07-26 15:47:29.804 - [任务 20][Inventory2] - Table [Inventory2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 15:47:29.879 - [任务 20][Inventory2] - Query table 'Inventory2' counts: 1 
[INFO ] 2024-07-26 15:47:29.879 - [任务 20][Inventory2] - Initial sync completed 
[INFO ] 2024-07-26 15:47:29.879 - [任务 20][Inventory2] - Incremental sync starting... 
[INFO ] 2024-07-26 15:47:29.879 - [任务 20][Inventory2] - Initial sync completed 
[INFO ] 2024-07-26 15:47:29.883 - [任务 20][Inventory2] - Starting stream read, table list: [Inventory2, _tapdata_heartbeat_table], offset: {"currentStartLSN":"000001DA000063E00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-26 15:47:30.702 - [任务 20][Inventory2] - opened cdc tables: [_tapdata_heartbeat_table, Inventory2] 
[INFO ] 2024-07-26 15:47:30.908 - [任务 20][Inventory2] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-26 15:47:30.909 - [任务 20][Inventory2] - Connector SQL Server incremental start succeed, tables: [Inventory2, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 17:34:14.947 - [任务 20][Inventory2] - Node Inventory2[03cea90c-35c1-4642-b198-1cc23f5e3332] running status set to false 
[INFO ] 2024-07-26 17:34:15.554 - [任务 20][Inventory2] - Incremental sync completed 
[INFO ] 2024-07-26 17:34:17.979 - [任务 20][Inventory2] - PDK connector node stopped: HazelcastSourcePdkDataNode-03cea90c-35c1-4642-b198-1cc23f5e3332 
[INFO ] 2024-07-26 17:34:17.979 - [任务 20][Inventory2] - PDK connector node released: HazelcastSourcePdkDataNode-03cea90c-35c1-4642-b198-1cc23f5e3332 
[INFO ] 2024-07-26 17:34:17.980 - [任务 20][Inventory2] - Node Inventory2[03cea90c-35c1-4642-b198-1cc23f5e3332] schema data cleaned 
[INFO ] 2024-07-26 17:34:17.982 - [任务 20][Inventory2] - Node Inventory2[03cea90c-35c1-4642-b198-1cc23f5e3332] monitor closed 
[INFO ] 2024-07-26 17:34:17.985 - [任务 20][Inventory2] - Node Inventory2[03cea90c-35c1-4642-b198-1cc23f5e3332] close complete, cost 3053 ms 
[INFO ] 2024-07-26 17:34:17.985 - [任务 20][dummy_test] - Node dummy_test[a2079b75-9dd0-46ed-a80e-7ac387372241] running status set to false 
[INFO ] 2024-07-26 17:34:17.995 - [任务 20][dummy_test] - Stop connector 
[INFO ] 2024-07-26 17:34:17.996 - [任务 20][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-a2079b75-9dd0-46ed-a80e-7ac387372241 
[INFO ] 2024-07-26 17:34:17.996 - [任务 20][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-a2079b75-9dd0-46ed-a80e-7ac387372241 
[INFO ] 2024-07-26 17:34:17.996 - [任务 20][dummy_test] - Node dummy_test[a2079b75-9dd0-46ed-a80e-7ac387372241] schema data cleaned 
[INFO ] 2024-07-26 17:34:17.996 - [任务 20][dummy_test] - Node dummy_test[a2079b75-9dd0-46ed-a80e-7ac387372241] monitor closed 
[INFO ] 2024-07-26 17:34:17.996 - [任务 20][dummy_test] - Node dummy_test[a2079b75-9dd0-46ed-a80e-7ac387372241] close complete, cost 11 ms 
[INFO ] 2024-07-26 17:34:22.124 - [任务 20] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 17:34:22.125 - [任务 20] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20923c87 
[INFO ] 2024-07-26 17:34:22.267 - [任务 20] - Stop task milestones: 66a01249e29f7d4f8d097a61(任务 20)  
[INFO ] 2024-07-26 17:34:22.267 - [任务 20] - Stopped task aspect(s) 
[INFO ] 2024-07-26 17:34:22.267 - [任务 20] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 17:34:22.303 - [任务 20] - Remove memory task client succeed, task: 任务 20[66a01249e29f7d4f8d097a61] 
[INFO ] 2024-07-26 17:34:22.303 - [任务 20] - Destroy memory task client cache succeed, task: 任务 20[66a01249e29f7d4f8d097a61] 
