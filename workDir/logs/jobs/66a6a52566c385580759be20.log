[INFO ] 2024-07-29 04:08:56.829 - [testCKStringAndFixString] - Task initialization... 
[INFO ] 2024-07-29 04:08:56.923 - [testCKStringAndFixString] - Start task milestones: 66a6a52566c385580759be20(testCKStringAndFixString) 
[INFO ] 2024-07-29 04:08:56.923 - [testCKStringAndFixString] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 04:08:57.027 - [testCKStringAndFixString] - The engine receives testCKStringAndFixString task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 04:08:57.027 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:08:57.027 - [testCKStringAndFixString][TString] - Node TString[3823363c-94b2-4d16-aa6d-7509875cc356] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:08:57.027 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:08:57.027 - [testCKStringAndFixString][TString] - Node TString[3823363c-94b2-4d16-aa6d-7509875cc356] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 04:08:57.896 - [testCKStringAndFixString][TString] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 04:08:57.897 - [testCKStringAndFixString][testStringAndFixedString] - Source node "testStringAndFixedString" read batch size: 100 
[INFO ] 2024-07-29 04:08:57.898 - [testCKStringAndFixString][testStringAndFixedString] - Source node "testStringAndFixedString" event queue capacity: 200 
[INFO ] 2024-07-29 04:08:57.899 - [testCKStringAndFixString][testStringAndFixedString] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 04:08:57.899 - [testCKStringAndFixString][testStringAndFixedString] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 04:08:57.899 - [testCKStringAndFixString][testStringAndFixedString] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 04:08:57.977 - [testCKStringAndFixString][testStringAndFixedString] - Initial sync started 
[INFO ] 2024-07-29 04:08:57.980 - [testCKStringAndFixString][testStringAndFixedString] - Starting batch read, table name: testStringAndFixedString, offset: null 
[INFO ] 2024-07-29 04:08:57.980 - [testCKStringAndFixString][testStringAndFixedString] - Table testStringAndFixedString is going to be initial synced 
[INFO ] 2024-07-29 04:08:58.004 - [testCKStringAndFixString][TString] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: TString 
[INFO ] 2024-07-29 04:08:58.004 - [testCKStringAndFixString][testStringAndFixedString] - Table [testStringAndFixedString] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 04:08:58.014 - [testCKStringAndFixString][testStringAndFixedString] - Query table 'testStringAndFixedString' counts: 1 
[INFO ] 2024-07-29 04:08:58.056 - [testCKStringAndFixString][testStringAndFixedString] - Initial sync completed 
[ERROR] 2024-07-29 04:08:58.059 - [testCKStringAndFixString][TString] - Unknown exception occur when operate table: TString <-- Error Message -->
Unknown exception occur when operate table: TString

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'myString' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'myString' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$7(HazelcastTargetPdkDataNode.java:269)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'myString' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$6(HazelcastTargetPdkDataNode.java:271)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-07-29 04:08:58.261 - [testCKStringAndFixString][TString] - Job suspend in error handle 
[INFO ] 2024-07-29 04:08:58.528 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] running status set to false 
[INFO ] 2024-07-29 04:08:58.556 - [testCKStringAndFixString][testStringAndFixedString] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 04:08:58.556 - [testCKStringAndFixString][testStringAndFixedString] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 04:08:58.581 - [testCKStringAndFixString][testStringAndFixedString] - PDK connector node stopped: HazelcastSourcePdkDataNode-13ef4b5d-3f8b-4574-a141-db874b1ae070 
[INFO ] 2024-07-29 04:08:58.581 - [testCKStringAndFixString][testStringAndFixedString] - PDK connector node released: HazelcastSourcePdkDataNode-13ef4b5d-3f8b-4574-a141-db874b1ae070 
[INFO ] 2024-07-29 04:08:58.581 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] schema data cleaned 
[INFO ] 2024-07-29 04:08:58.582 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] monitor closed 
[INFO ] 2024-07-29 04:08:58.583 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] close complete, cost 57 ms 
[INFO ] 2024-07-29 04:08:58.583 - [testCKStringAndFixString][TString] - Node TString[3823363c-94b2-4d16-aa6d-7509875cc356] running status set to false 
[INFO ] 2024-07-29 04:08:58.595 - [testCKStringAndFixString][TString] - PDK connector node stopped: HazelcastTargetPdkDataNode-3823363c-94b2-4d16-aa6d-7509875cc356 
[INFO ] 2024-07-29 04:08:58.595 - [testCKStringAndFixString][TString] - PDK connector node released: HazelcastTargetPdkDataNode-3823363c-94b2-4d16-aa6d-7509875cc356 
[INFO ] 2024-07-29 04:08:58.596 - [testCKStringAndFixString][TString] - Node TString[3823363c-94b2-4d16-aa6d-7509875cc356] schema data cleaned 
[INFO ] 2024-07-29 04:08:58.596 - [testCKStringAndFixString][TString] - Node TString[3823363c-94b2-4d16-aa6d-7509875cc356] monitor closed 
[INFO ] 2024-07-29 04:08:58.797 - [testCKStringAndFixString][TString] - Node TString[3823363c-94b2-4d16-aa6d-7509875cc356] close complete, cost 12 ms 
[INFO ] 2024-07-29 04:08:59.077 - [testCKStringAndFixString] - Task [testCKStringAndFixString] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-29 04:08:59.077 - [testCKStringAndFixString] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 04:08:59.203 - [testCKStringAndFixString] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@54f55446 
[INFO ] 2024-07-29 04:08:59.207 - [testCKStringAndFixString] - Stop task milestones: 66a6a52566c385580759be20(testCKStringAndFixString)  
[INFO ] 2024-07-29 04:08:59.220 - [testCKStringAndFixString] - Stopped task aspect(s) 
[INFO ] 2024-07-29 04:08:59.223 - [testCKStringAndFixString] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 04:08:59.253 - [testCKStringAndFixString] - Remove memory task client succeed, task: testCKStringAndFixString[66a6a52566c385580759be20] 
[INFO ] 2024-07-29 04:08:59.254 - [testCKStringAndFixString] - Destroy memory task client cache succeed, task: testCKStringAndFixString[66a6a52566c385580759be20] 
[INFO ] 2024-07-29 04:10:04.969 - [testCKStringAndFixString] - Task initialization... 
[INFO ] 2024-07-29 04:10:04.970 - [testCKStringAndFixString] - Start task milestones: 66a6a52566c385580759be20(testCKStringAndFixString) 
[INFO ] 2024-07-29 04:10:05.223 - [testCKStringAndFixString] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 04:10:05.259 - [testCKStringAndFixString] - The engine receives testCKStringAndFixString task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 04:10:05.297 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:10:05.297 - [testCKStringAndFixString][targetCKString] - Node targetCKString[c98fcec2-3c77-4df0-ac02-c28d113710d0] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:10:05.297 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:10:05.297 - [testCKStringAndFixString][targetCKString] - Node targetCKString[c98fcec2-3c77-4df0-ac02-c28d113710d0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:10:06.043 - [testCKStringAndFixString][testStringAndFixedString] - Source node "testStringAndFixedString" read batch size: 100 
[INFO ] 2024-07-29 04:10:06.043 - [testCKStringAndFixString][testStringAndFixedString] - Source node "testStringAndFixedString" event queue capacity: 200 
[INFO ] 2024-07-29 04:10:06.043 - [testCKStringAndFixString][testStringAndFixedString] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 04:10:06.044 - [testCKStringAndFixString][testStringAndFixedString] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 04:10:06.080 - [testCKStringAndFixString][testStringAndFixedString] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 04:10:06.080 - [testCKStringAndFixString][testStringAndFixedString] - Initial sync started 
[INFO ] 2024-07-29 04:10:06.081 - [testCKStringAndFixString][testStringAndFixedString] - Starting batch read, table name: testStringAndFixedString, offset: null 
[INFO ] 2024-07-29 04:10:06.081 - [testCKStringAndFixString][testStringAndFixedString] - Table testStringAndFixedString is going to be initial synced 
[INFO ] 2024-07-29 04:10:06.119 - [testCKStringAndFixString][testStringAndFixedString] - Table [testStringAndFixedString] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 04:10:06.119 - [testCKStringAndFixString][testStringAndFixedString] - Query table 'testStringAndFixedString' counts: 1 
[INFO ] 2024-07-29 04:10:06.126 - [testCKStringAndFixString][testStringAndFixedString] - Initial sync completed 
[INFO ] 2024-07-29 04:10:06.126 - [testCKStringAndFixString][targetCKString] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 04:10:07.142 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] running status set to false 
[INFO ] 2024-07-29 04:10:07.147 - [testCKStringAndFixString][targetCKString] - Node targetCKString[c98fcec2-3c77-4df0-ac02-c28d113710d0] running status set to false 
[INFO ] 2024-07-29 04:10:07.147 - [testCKStringAndFixString][testStringAndFixedString] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 04:10:07.215 - [testCKStringAndFixString][targetCKString] - Clickhouse Optimize Table start, tables: ["targetCKString"] 
[WARN ] 2024-07-29 04:10:07.219 - [testCKStringAndFixString][targetCKString] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-29 04:10:07.253 - [testCKStringAndFixString][testStringAndFixedString] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 04:10:07.253 - [testCKStringAndFixString][targetCKString] - PDK connector node stopped: HazelcastTargetPdkDataNode-c98fcec2-3c77-4df0-ac02-c28d113710d0 
[INFO ] 2024-07-29 04:10:07.253 - [testCKStringAndFixString][targetCKString] - PDK connector node released: HazelcastTargetPdkDataNode-c98fcec2-3c77-4df0-ac02-c28d113710d0 
[INFO ] 2024-07-29 04:10:07.254 - [testCKStringAndFixString][targetCKString] - Node targetCKString[c98fcec2-3c77-4df0-ac02-c28d113710d0] schema data cleaned 
[INFO ] 2024-07-29 04:10:07.254 - [testCKStringAndFixString][targetCKString] - Node targetCKString[c98fcec2-3c77-4df0-ac02-c28d113710d0] monitor closed 
[INFO ] 2024-07-29 04:10:07.254 - [testCKStringAndFixString][testStringAndFixedString] - PDK connector node stopped: HazelcastSourcePdkDataNode-13ef4b5d-3f8b-4574-a141-db874b1ae070 
[INFO ] 2024-07-29 04:10:07.255 - [testCKStringAndFixString][testStringAndFixedString] - PDK connector node released: HazelcastSourcePdkDataNode-13ef4b5d-3f8b-4574-a141-db874b1ae070 
[INFO ] 2024-07-29 04:10:07.255 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] schema data cleaned 
[INFO ] 2024-07-29 04:10:07.258 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] monitor closed 
[INFO ] 2024-07-29 04:10:07.258 - [testCKStringAndFixString][targetCKString] - Node targetCKString[c98fcec2-3c77-4df0-ac02-c28d113710d0] close complete, cost 124 ms 
[INFO ] 2024-07-29 04:10:07.258 - [testCKStringAndFixString][testStringAndFixedString] - Node testStringAndFixedString[13ef4b5d-3f8b-4574-a141-db874b1ae070] close complete, cost 125 ms 
[INFO ] 2024-07-29 04:10:09.352 - [testCKStringAndFixString] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 04:10:09.481 - [testCKStringAndFixString] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@530e2537 
[INFO ] 2024-07-29 04:10:09.481 - [testCKStringAndFixString] - Stop task milestones: 66a6a52566c385580759be20(testCKStringAndFixString)  
[INFO ] 2024-07-29 04:10:09.491 - [testCKStringAndFixString] - Stopped task aspect(s) 
[INFO ] 2024-07-29 04:10:09.494 - [testCKStringAndFixString] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 04:10:09.506 - [testCKStringAndFixString] - Remove memory task client succeed, task: testCKStringAndFixString[66a6a52566c385580759be20] 
[INFO ] 2024-07-29 04:10:09.509 - [testCKStringAndFixString] - Destroy memory task client cache succeed, task: testCKStringAndFixString[66a6a52566c385580759be20] 
