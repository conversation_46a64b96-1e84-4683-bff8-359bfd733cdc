[INFO ] 2024-03-29 16:08:13.835 - [suppliers_import_import_import_import_import_import(100)][d3bf6210-0083-4a10-99bc-56556dad7271] - Node d3bf6210-0083-4a10-99bc-56556dad7271[d3bf6210-0083-4a10-99bc-56556dad7271] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:13.836 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:13.836 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:13.836 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:13.836 - [suppliers_import_import_import_import_import_import(100)][d3bf6210-0083-4a10-99bc-56556dad7271] - Node d3bf6210-0083-4a10-99bc-56556dad7271[d3bf6210-0083-4a10-99bc-56556dad7271] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:13.836 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:13.904 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:13.907 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d92750f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d92750f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d92750f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:08:13.971 - [suppliers_import_import_import_import_import_import(100)][ad2dfe47-5aed-4f85-960e-389a7f65d089] - Node ad2dfe47-5aed-4f85-960e-389a7f65d089[ad2dfe47-5aed-4f85-960e-389a7f65d089] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:13.971 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:13.971 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:13.972 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:13.975 - [suppliers_import_import_import_import_import_import(100)][ad2dfe47-5aed-4f85-960e-389a7f65d089] - Node ad2dfe47-5aed-4f85-960e-389a7f65d089[ad2dfe47-5aed-4f85-960e-389a7f65d089] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:13.975 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:13.996 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:13.998 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53b5e16a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53b5e16a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53b5e16a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:14.427 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:14.427 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:14.436 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:14.436 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:14.436 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:14.436 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:14.436 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 9 ms 
[WARN ] 2024-03-29 16:08:14.585 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:14.585 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:14.593 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:14.593 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:14.594 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:14.594 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:14.800 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 10 ms 
[INFO ] 2024-03-29 16:08:15.290 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:15.291 - [suppliers_import_import_import_import_import_import(100)][32cff14f-ca6d-4a72-963c-43c2a0160332] - Node 32cff14f-ca6d-4a72-963c-43c2a0160332[32cff14f-ca6d-4a72-963c-43c2a0160332] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:15.291 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:15.292 - [suppliers_import_import_import_import_import_import(100)][32cff14f-ca6d-4a72-963c-43c2a0160332] - Node 32cff14f-ca6d-4a72-963c-43c2a0160332[32cff14f-ca6d-4a72-963c-43c2a0160332] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:15.292 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 3 ms 
[INFO ] 2024-03-29 16:08:15.301 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:15.383 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 16:08:15.386 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:15.386 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:15.386 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:15.386 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:15.387 - [suppliers_import_import_import_import_import_import(100)][87ce6358-c574-4001-8096-f91a95318451] - Node 87ce6358-c574-4001-8096-f91a95318451[87ce6358-c574-4001-8096-f91a95318451] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:15.387 - [suppliers_import_import_import_import_import_import(100)][87ce6358-c574-4001-8096-f91a95318451] - Node 87ce6358-c574-4001-8096-f91a95318451[87ce6358-c574-4001-8096-f91a95318451] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:15.402 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:15.432 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@33659c55 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@33659c55 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@33659c55 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 16:08:15.433 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62bd2326 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62bd2326 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62bd2326 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:15.561 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:15.566 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:15.571 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:15.571 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:15.571 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:15.571 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:15.719 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 13 ms 
[WARN ] 2024-03-29 16:08:15.719 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:15.729 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:15.729 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:15.729 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:15.729 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:15.730 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:15.730 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:08:16.456 - [suppliers_import_import_import_import_import_import(100)][d3bf6210-0083-4a10-99bc-56556dad7271] - Node d3bf6210-0083-4a10-99bc-56556dad7271[d3bf6210-0083-4a10-99bc-56556dad7271] running status set to false 
[INFO ] 2024-03-29 16:08:16.456 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:16.456 - [suppliers_import_import_import_import_import_import(100)][d3bf6210-0083-4a10-99bc-56556dad7271] - Node d3bf6210-0083-4a10-99bc-56556dad7271[d3bf6210-0083-4a10-99bc-56556dad7271] schema data cleaned 
[INFO ] 2024-03-29 16:08:16.456 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:16.457 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:16.457 - [suppliers_import_import_import_import_import_import(100)][d3bf6210-0083-4a10-99bc-56556dad7271] - Node d3bf6210-0083-4a10-99bc-56556dad7271[d3bf6210-0083-4a10-99bc-56556dad7271] monitor closed 
[INFO ] 2024-03-29 16:08:16.457 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:08:16.457 - [suppliers_import_import_import_import_import_import(100)][d3bf6210-0083-4a10-99bc-56556dad7271] - Node d3bf6210-0083-4a10-99bc-56556dad7271[d3bf6210-0083-4a10-99bc-56556dad7271] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:08:16.457 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-d3bf6210-0083-4a10-99bc-56556dad7271 complete, cost 2684ms 
[INFO ] 2024-03-29 16:08:16.526 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:16.526 - [suppliers_import_import_import_import_import_import(100)][ad2dfe47-5aed-4f85-960e-389a7f65d089] - Node ad2dfe47-5aed-4f85-960e-389a7f65d089[ad2dfe47-5aed-4f85-960e-389a7f65d089] running status set to false 
[INFO ] 2024-03-29 16:08:16.527 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:16.527 - [suppliers_import_import_import_import_import_import(100)][ad2dfe47-5aed-4f85-960e-389a7f65d089] - Node ad2dfe47-5aed-4f85-960e-389a7f65d089[ad2dfe47-5aed-4f85-960e-389a7f65d089] schema data cleaned 
[INFO ] 2024-03-29 16:08:16.527 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:16.527 - [suppliers_import_import_import_import_import_import(100)][ad2dfe47-5aed-4f85-960e-389a7f65d089] - Node ad2dfe47-5aed-4f85-960e-389a7f65d089[ad2dfe47-5aed-4f85-960e-389a7f65d089] monitor closed 
[INFO ] 2024-03-29 16:08:16.527 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:08:16.529 - [suppliers_import_import_import_import_import_import(100)][ad2dfe47-5aed-4f85-960e-389a7f65d089] - Node ad2dfe47-5aed-4f85-960e-389a7f65d089[ad2dfe47-5aed-4f85-960e-389a7f65d089] close complete, cost 3 ms 
[INFO ] 2024-03-29 16:08:16.529 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-ad2dfe47-5aed-4f85-960e-389a7f65d089 complete, cost 2587ms 
[INFO ] 2024-03-29 16:08:16.600 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:16.600 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:16.600 - [suppliers_import_import_import_import_import_import(100)][73ac71e5-09d9-498e-bf9e-8ea010de1a9d] - Node 73ac71e5-09d9-498e-bf9e-8ea010de1a9d[73ac71e5-09d9-498e-bf9e-8ea010de1a9d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:16.600 - [suppliers_import_import_import_import_import_import(100)][73ac71e5-09d9-498e-bf9e-8ea010de1a9d] - Node 73ac71e5-09d9-498e-bf9e-8ea010de1a9d[73ac71e5-09d9-498e-bf9e-8ea010de1a9d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:16.600 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:16.600 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:16.676 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:16.677 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9bef7d8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9bef7d8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9bef7d8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:16.868 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:16.868 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:16.878 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:16.878 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:16.879 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:16.879 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:17.082 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 13 ms 
[INFO ] 2024-03-29 16:08:17.952 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:17.955 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:17.955 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:17.956 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:17.958 - [suppliers_import_import_import_import_import_import(100)][87ce6358-c574-4001-8096-f91a95318451] - Node 87ce6358-c574-4001-8096-f91a95318451[87ce6358-c574-4001-8096-f91a95318451] running status set to false 
[INFO ] 2024-03-29 16:08:17.958 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:17.959 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:08:17.959 - [suppliers_import_import_import_import_import_import(100)][87ce6358-c574-4001-8096-f91a95318451] - Node 87ce6358-c574-4001-8096-f91a95318451[87ce6358-c574-4001-8096-f91a95318451] schema data cleaned 
[INFO ] 2024-03-29 16:08:17.960 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:17.960 - [suppliers_import_import_import_import_import_import(100)][87ce6358-c574-4001-8096-f91a95318451] - Node 87ce6358-c574-4001-8096-f91a95318451[87ce6358-c574-4001-8096-f91a95318451] monitor closed 
[INFO ] 2024-03-29 16:08:17.960 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 3 ms 
[INFO ] 2024-03-29 16:08:17.960 - [suppliers_import_import_import_import_import_import(100)][87ce6358-c574-4001-8096-f91a95318451] - Node 87ce6358-c574-4001-8096-f91a95318451[87ce6358-c574-4001-8096-f91a95318451] close complete, cost 2 ms 
[INFO ] 2024-03-29 16:08:17.961 - [suppliers_import_import_import_import_import_import(100)][32cff14f-ca6d-4a72-963c-43c2a0160332] - Node 32cff14f-ca6d-4a72-963c-43c2a0160332[32cff14f-ca6d-4a72-963c-43c2a0160332] running status set to false 
[INFO ] 2024-03-29 16:08:17.965 - [suppliers_import_import_import_import_import_import(100)][32cff14f-ca6d-4a72-963c-43c2a0160332] - Node 32cff14f-ca6d-4a72-963c-43c2a0160332[32cff14f-ca6d-4a72-963c-43c2a0160332] schema data cleaned 
[INFO ] 2024-03-29 16:08:17.965 - [suppliers_import_import_import_import_import_import(100)][32cff14f-ca6d-4a72-963c-43c2a0160332] - Node 32cff14f-ca6d-4a72-963c-43c2a0160332[32cff14f-ca6d-4a72-963c-43c2a0160332] monitor closed 
[INFO ] 2024-03-29 16:08:17.965 - [suppliers_import_import_import_import_import_import(100)][32cff14f-ca6d-4a72-963c-43c2a0160332] - Node 32cff14f-ca6d-4a72-963c-43c2a0160332[32cff14f-ca6d-4a72-963c-43c2a0160332] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:08:17.968 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-87ce6358-c574-4001-8096-f91a95318451 complete, cost 2626ms 
[INFO ] 2024-03-29 16:08:17.968 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-32cff14f-ca6d-4a72-963c-43c2a0160332 complete, cost 2746ms 
[INFO ] 2024-03-29 16:08:18.577 - [suppliers_import_import_import_import_import_import(100)][cad59db7-c5f1-433b-b8a2-59cb565330ae] - Node cad59db7-c5f1-433b-b8a2-59cb565330ae[cad59db7-c5f1-433b-b8a2-59cb565330ae] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:18.577 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:18.577 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:18.577 - [suppliers_import_import_import_import_import_import(100)][cad59db7-c5f1-433b-b8a2-59cb565330ae] - Node cad59db7-c5f1-433b-b8a2-59cb565330ae[cad59db7-c5f1-433b-b8a2-59cb565330ae] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:18.577 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:18.577 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:18.642 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:18.642 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7aae14ee error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7aae14ee error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7aae14ee error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:18.794 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:18.794 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:18.800 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:18.800 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:18.801 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:18.801 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:19.004 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 7 ms 
[INFO ] 2024-03-29 16:08:19.201 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:19.202 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:19.202 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:19.206 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:08:19.206 - [suppliers_import_import_import_import_import_import(100)][73ac71e5-09d9-498e-bf9e-8ea010de1a9d] - Node 73ac71e5-09d9-498e-bf9e-8ea010de1a9d[73ac71e5-09d9-498e-bf9e-8ea010de1a9d] running status set to false 
[INFO ] 2024-03-29 16:08:19.206 - [suppliers_import_import_import_import_import_import(100)][73ac71e5-09d9-498e-bf9e-8ea010de1a9d] - Node 73ac71e5-09d9-498e-bf9e-8ea010de1a9d[73ac71e5-09d9-498e-bf9e-8ea010de1a9d] schema data cleaned 
[INFO ] 2024-03-29 16:08:19.207 - [suppliers_import_import_import_import_import_import(100)][73ac71e5-09d9-498e-bf9e-8ea010de1a9d] - Node 73ac71e5-09d9-498e-bf9e-8ea010de1a9d[73ac71e5-09d9-498e-bf9e-8ea010de1a9d] monitor closed 
[INFO ] 2024-03-29 16:08:19.208 - [suppliers_import_import_import_import_import_import(100)][73ac71e5-09d9-498e-bf9e-8ea010de1a9d] - Node 73ac71e5-09d9-498e-bf9e-8ea010de1a9d[73ac71e5-09d9-498e-bf9e-8ea010de1a9d] close complete, cost 2 ms 
[INFO ] 2024-03-29 16:08:19.416 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-73ac71e5-09d9-498e-bf9e-8ea010de1a9d complete, cost 2676ms 
[INFO ] 2024-03-29 16:08:19.441 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:19.441 - [suppliers_import_import_import_import_import_import(100)][1138f20d-d006-43f6-a746-b1739fad40d9] - Node 1138f20d-d006-43f6-a746-b1739fad40d9[1138f20d-d006-43f6-a746-b1739fad40d9] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:19.441 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:19.441 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:19.442 - [suppliers_import_import_import_import_import_import(100)][1138f20d-d006-43f6-a746-b1739fad40d9] - Node 1138f20d-d006-43f6-a746-b1739fad40d9[1138f20d-d006-43f6-a746-b1739fad40d9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:19.442 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 16:08:19.527 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:19.528 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b60e432 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b60e432 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b60e432 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:19.701 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:19.701 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:19.710 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:19.710 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:19.710 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:19.710 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:19.910 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 10 ms 
[INFO ] 2024-03-29 16:08:21.176 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:21.176 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:21.176 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:21.176 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:08:21.179 - [suppliers_import_import_import_import_import_import(100)][cad59db7-c5f1-433b-b8a2-59cb565330ae] - Node cad59db7-c5f1-433b-b8a2-59cb565330ae[cad59db7-c5f1-433b-b8a2-59cb565330ae] running status set to false 
[INFO ] 2024-03-29 16:08:21.179 - [suppliers_import_import_import_import_import_import(100)][cad59db7-c5f1-433b-b8a2-59cb565330ae] - Node cad59db7-c5f1-433b-b8a2-59cb565330ae[cad59db7-c5f1-433b-b8a2-59cb565330ae] schema data cleaned 
[INFO ] 2024-03-29 16:08:21.179 - [suppliers_import_import_import_import_import_import(100)][cad59db7-c5f1-433b-b8a2-59cb565330ae] - Node cad59db7-c5f1-433b-b8a2-59cb565330ae[cad59db7-c5f1-433b-b8a2-59cb565330ae] monitor closed 
[INFO ] 2024-03-29 16:08:21.183 - [suppliers_import_import_import_import_import_import(100)][cad59db7-c5f1-433b-b8a2-59cb565330ae] - Node cad59db7-c5f1-433b-b8a2-59cb565330ae[cad59db7-c5f1-433b-b8a2-59cb565330ae] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:08:21.184 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-cad59db7-c5f1-433b-b8a2-59cb565330ae complete, cost 2655ms 
[INFO ] 2024-03-29 16:08:21.442 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:21.443 - [suppliers_import_import_import_import_import_import(100)][7e2995e5-2ed2-49bf-81dc-6d92f022e298] - Node 7e2995e5-2ed2-49bf-81dc-6d92f022e298[7e2995e5-2ed2-49bf-81dc-6d92f022e298] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:21.443 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:21.443 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:21.443 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:21.443 - [suppliers_import_import_import_import_import_import(100)][7e2995e5-2ed2-49bf-81dc-6d92f022e298] - Node 7e2995e5-2ed2-49bf-81dc-6d92f022e298[7e2995e5-2ed2-49bf-81dc-6d92f022e298] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 16:08:21.523 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:21.523 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6b4b5675 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6b4b5675 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6b4b5675 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:21.686 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:21.686 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:21.696 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:21.696 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:21.696 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:21.696 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:21.899 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 10 ms 
[INFO ] 2024-03-29 16:08:22.067 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:22.067 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:22.067 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:22.067 - [suppliers_import_import_import_import_import_import(100)][1138f20d-d006-43f6-a746-b1739fad40d9] - Node 1138f20d-d006-43f6-a746-b1739fad40d9[1138f20d-d006-43f6-a746-b1739fad40d9] running status set to false 
[INFO ] 2024-03-29 16:08:22.068 - [suppliers_import_import_import_import_import_import(100)][1138f20d-d006-43f6-a746-b1739fad40d9] - Node 1138f20d-d006-43f6-a746-b1739fad40d9[1138f20d-d006-43f6-a746-b1739fad40d9] schema data cleaned 
[INFO ] 2024-03-29 16:08:22.068 - [suppliers_import_import_import_import_import_import(100)][1138f20d-d006-43f6-a746-b1739fad40d9] - Node 1138f20d-d006-43f6-a746-b1739fad40d9[1138f20d-d006-43f6-a746-b1739fad40d9] monitor closed 
[INFO ] 2024-03-29 16:08:22.068 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 3 ms 
[INFO ] 2024-03-29 16:08:22.071 - [suppliers_import_import_import_import_import_import(100)][1138f20d-d006-43f6-a746-b1739fad40d9] - Node 1138f20d-d006-43f6-a746-b1739fad40d9[1138f20d-d006-43f6-a746-b1739fad40d9] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:08:22.072 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-1138f20d-d006-43f6-a746-b1739fad40d9 complete, cost 2719ms 
[INFO ] 2024-03-29 16:08:24.064 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:24.064 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:24.064 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:24.065 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 3 ms 
[INFO ] 2024-03-29 16:08:24.068 - [suppliers_import_import_import_import_import_import(100)][7e2995e5-2ed2-49bf-81dc-6d92f022e298] - Node 7e2995e5-2ed2-49bf-81dc-6d92f022e298[7e2995e5-2ed2-49bf-81dc-6d92f022e298] running status set to false 
[INFO ] 2024-03-29 16:08:24.069 - [suppliers_import_import_import_import_import_import(100)][7e2995e5-2ed2-49bf-81dc-6d92f022e298] - Node 7e2995e5-2ed2-49bf-81dc-6d92f022e298[7e2995e5-2ed2-49bf-81dc-6d92f022e298] schema data cleaned 
[INFO ] 2024-03-29 16:08:24.069 - [suppliers_import_import_import_import_import_import(100)][7e2995e5-2ed2-49bf-81dc-6d92f022e298] - Node 7e2995e5-2ed2-49bf-81dc-6d92f022e298[7e2995e5-2ed2-49bf-81dc-6d92f022e298] monitor closed 
[INFO ] 2024-03-29 16:08:24.074 - [suppliers_import_import_import_import_import_import(100)][7e2995e5-2ed2-49bf-81dc-6d92f022e298] - Node 7e2995e5-2ed2-49bf-81dc-6d92f022e298[7e2995e5-2ed2-49bf-81dc-6d92f022e298] close complete, cost 2 ms 
[INFO ] 2024-03-29 16:08:24.079 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-7e2995e5-2ed2-49bf-81dc-6d92f022e298 complete, cost 2707ms 
[INFO ] 2024-03-29 16:08:34.268 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:34.268 - [suppliers_import_import_import_import_import_import(100)][7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] - Node 7f363c3f-d5ce-4119-9b58-cc2289c2fb0e[7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:34.268 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:34.268 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:34.268 - [suppliers_import_import_import_import_import_import(100)][7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] - Node 7f363c3f-d5ce-4119-9b58-cc2289c2fb0e[7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:34.268 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:34.287 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:34.287 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4497688d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4497688d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4497688d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:34.453 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:34.454 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:34.461 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:34.461 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:34.461 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:34.461 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:34.461 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:08:35.541 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:35.542 - [suppliers_import_import_import_import_import_import(100)][28ea157b-3e0b-4e47-8a5d-555080aedec2] - Node 28ea157b-3e0b-4e47-8a5d-555080aedec2[28ea157b-3e0b-4e47-8a5d-555080aedec2] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:35.542 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:35.542 - [suppliers_import_import_import_import_import_import(100)][28ea157b-3e0b-4e47-8a5d-555080aedec2] - Node 28ea157b-3e0b-4e47-8a5d-555080aedec2[28ea157b-3e0b-4e47-8a5d-555080aedec2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:35.542 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:35.542 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:35.618 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:35.821 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6de54c22 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6de54c22 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6de54c22 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:35.822 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:35.822 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:35.833 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:35.834 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:35.834 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:35.834 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:35.835 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 12 ms 
[INFO ] 2024-03-29 16:08:36.818 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:36.818 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:36.818 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:36.818 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:08:36.822 - [suppliers_import_import_import_import_import_import(100)][7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] - Node 7f363c3f-d5ce-4119-9b58-cc2289c2fb0e[7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] running status set to false 
[INFO ] 2024-03-29 16:08:36.822 - [suppliers_import_import_import_import_import_import(100)][7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] - Node 7f363c3f-d5ce-4119-9b58-cc2289c2fb0e[7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] schema data cleaned 
[INFO ] 2024-03-29 16:08:36.822 - [suppliers_import_import_import_import_import_import(100)][7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] - Node 7f363c3f-d5ce-4119-9b58-cc2289c2fb0e[7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] monitor closed 
[INFO ] 2024-03-29 16:08:36.822 - [suppliers_import_import_import_import_import_import(100)][7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] - Node 7f363c3f-d5ce-4119-9b58-cc2289c2fb0e[7f363c3f-d5ce-4119-9b58-cc2289c2fb0e] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:08:36.824 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-7f363c3f-d5ce-4119-9b58-cc2289c2fb0e complete, cost 2656ms 
[INFO ] 2024-03-29 16:08:38.101 - [suppliers_import_import_import_import_import_import(100)][f1abe65b-7979-4168-9a4d-6b579d84cf36] - Node f1abe65b-7979-4168-9a4d-6b579d84cf36[f1abe65b-7979-4168-9a4d-6b579d84cf36] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:38.101 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:38.101 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:38.101 - [suppliers_import_import_import_import_import_import(100)][f1abe65b-7979-4168-9a4d-6b579d84cf36] - Node f1abe65b-7979-4168-9a4d-6b579d84cf36[f1abe65b-7979-4168-9a4d-6b579d84cf36] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.101 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.101 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][6094dd76-6d49-4f3a-a30c-2db167b93bad] - Node 6094dd76-6d49-4f3a-a30c-2db167b93bad[6094dd76-6d49-4f3a-a30c-2db167b93bad] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][6094dd76-6d49-4f3a-a30c-2db167b93bad] - Node 6094dd76-6d49-4f3a-a30c-2db167b93bad[6094dd76-6d49-4f3a-a30c-2db167b93bad] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:38.161 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:38.162 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 0 ms 
[ERROR] 2024-03-29 16:08:38.163 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e4fa04a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e4fa04a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e4fa04a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:08:38.167 - [suppliers_import_import_import_import_import_import(100)][28ea157b-3e0b-4e47-8a5d-555080aedec2] - Node 28ea157b-3e0b-4e47-8a5d-555080aedec2[28ea157b-3e0b-4e47-8a5d-555080aedec2] running status set to false 
[INFO ] 2024-03-29 16:08:38.167 - [suppliers_import_import_import_import_import_import(100)][28ea157b-3e0b-4e47-8a5d-555080aedec2] - Node 28ea157b-3e0b-4e47-8a5d-555080aedec2[28ea157b-3e0b-4e47-8a5d-555080aedec2] schema data cleaned 
[INFO ] 2024-03-29 16:08:38.167 - [suppliers_import_import_import_import_import_import(100)][28ea157b-3e0b-4e47-8a5d-555080aedec2] - Node 28ea157b-3e0b-4e47-8a5d-555080aedec2[28ea157b-3e0b-4e47-8a5d-555080aedec2] monitor closed 
[INFO ] 2024-03-29 16:08:38.167 - [suppliers_import_import_import_import_import_import(100)][28ea157b-3e0b-4e47-8a5d-555080aedec2] - Node 28ea157b-3e0b-4e47-8a5d-555080aedec2[28ea157b-3e0b-4e47-8a5d-555080aedec2] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.169 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-28ea157b-3e0b-4e47-8a5d-555080aedec2 complete, cost 2723ms 
[INFO ] 2024-03-29 16:08:38.172 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:38.306 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@477e319c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@477e319c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@477e319c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:38.312 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:38.312 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:38.326 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:38.326 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:38.326 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:38.326 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:38.326 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 16 ms 
[INFO ] 2024-03-29 16:08:38.353 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:38.353 - [suppliers_import_import_import_import_import_import(100)][177da962-23bc-434c-94bb-b42059f5e2da] - Node 177da962-23bc-434c-94bb-b42059f5e2da[177da962-23bc-434c-94bb-b42059f5e2da] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:38.353 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.353 - [suppliers_import_import_import_import_import_import(100)][177da962-23bc-434c-94bb-b42059f5e2da] - Node 177da962-23bc-434c-94bb-b42059f5e2da[177da962-23bc-434c-94bb-b42059f5e2da] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.353 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:38.353 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:38.390 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:38.391 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6e70dc35 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6e70dc35 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6e70dc35 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:38.526 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:38.526 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:38.542 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:38.542 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:38.542 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:38.542 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:38.544 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 18 ms 
[WARN ] 2024-03-29 16:08:38.596 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:38.596 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:38.601 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:38.601 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:38.602 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:38.602 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:38.806 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:08:39.760 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:39.760 - [suppliers_import_import_import_import_import_import(100)][f00e5b75-9e24-47a3-be13-f7d8735931db] - Node f00e5b75-9e24-47a3-be13-f7d8735931db[f00e5b75-9e24-47a3-be13-f7d8735931db] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:39.760 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:39.760 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:39.760 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:39.760 - [suppliers_import_import_import_import_import_import(100)][f00e5b75-9e24-47a3-be13-f7d8735931db] - Node f00e5b75-9e24-47a3-be13-f7d8735931db[f00e5b75-9e24-47a3-be13-f7d8735931db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:39.826 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:39.991 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@269f1e81 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@269f1e81 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@269f1e81 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:39.992 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:40.003 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:40.003 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:40.003 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:40.003 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.004 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:40.004 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 10 ms 
[INFO ] 2024-03-29 16:08:40.455 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:40.455 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:40.456 - [suppliers_import_import_import_import_import_import(100)][a5958d44-9f89-4dcb-9fb6-062216681f37] - Node a5958d44-9f89-4dcb-9fb6-062216681f37[a5958d44-9f89-4dcb-9fb6-062216681f37] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:40.456 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:40.456 - [suppliers_import_import_import_import_import_import(100)][a5958d44-9f89-4dcb-9fb6-062216681f37] - Node a5958d44-9f89-4dcb-9fb6-062216681f37[a5958d44-9f89-4dcb-9fb6-062216681f37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:40.456 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 16:08:40.496 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:40.699 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@913b35b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@913b35b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@913b35b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:40.701 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:40.701 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:40.701 - [suppliers_import_import_import_import_import_import(100)][6094dd76-6d49-4f3a-a30c-2db167b93bad] - Node 6094dd76-6d49-4f3a-a30c-2db167b93bad[6094dd76-6d49-4f3a-a30c-2db167b93bad] running status set to false 
[INFO ] 2024-03-29 16:08:40.702 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:40.702 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:40.702 - [suppliers_import_import_import_import_import_import(100)][f1abe65b-7979-4168-9a4d-6b579d84cf36] - Node f1abe65b-7979-4168-9a4d-6b579d84cf36[f1abe65b-7979-4168-9a4d-6b579d84cf36] running status set to false 
[INFO ] 2024-03-29 16:08:40.702 - [suppliers_import_import_import_import_import_import(100)][6094dd76-6d49-4f3a-a30c-2db167b93bad] - Node 6094dd76-6d49-4f3a-a30c-2db167b93bad[6094dd76-6d49-4f3a-a30c-2db167b93bad] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.717 - [suppliers_import_import_import_import_import_import(100)][f1abe65b-7979-4168-9a4d-6b579d84cf36] - Node f1abe65b-7979-4168-9a4d-6b579d84cf36[f1abe65b-7979-4168-9a4d-6b579d84cf36] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.717 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.720 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.720 - [suppliers_import_import_import_import_import_import(100)][6094dd76-6d49-4f3a-a30c-2db167b93bad] - Node 6094dd76-6d49-4f3a-a30c-2db167b93bad[6094dd76-6d49-4f3a-a30c-2db167b93bad] monitor closed 
[INFO ] 2024-03-29 16:08:40.720 - [suppliers_import_import_import_import_import_import(100)][f1abe65b-7979-4168-9a4d-6b579d84cf36] - Node f1abe65b-7979-4168-9a4d-6b579d84cf36[f1abe65b-7979-4168-9a4d-6b579d84cf36] monitor closed 
[INFO ] 2024-03-29 16:08:40.720 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:40.720 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:40.721 - [suppliers_import_import_import_import_import_import(100)][f1abe65b-7979-4168-9a4d-6b579d84cf36] - Node f1abe65b-7979-4168-9a4d-6b579d84cf36[f1abe65b-7979-4168-9a4d-6b579d84cf36] close complete, cost 20 ms 
[INFO ] 2024-03-29 16:08:40.721 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 22 ms 
[INFO ] 2024-03-29 16:08:40.721 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 25 ms 
[INFO ] 2024-03-29 16:08:40.722 - [suppliers_import_import_import_import_import_import(100)][6094dd76-6d49-4f3a-a30c-2db167b93bad] - Node 6094dd76-6d49-4f3a-a30c-2db167b93bad[6094dd76-6d49-4f3a-a30c-2db167b93bad] close complete, cost 21 ms 
[INFO ] 2024-03-29 16:08:40.727 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-6094dd76-6d49-4f3a-a30c-2db167b93bad complete, cost 2595ms 
[INFO ] 2024-03-29 16:08:40.727 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-f1abe65b-7979-4168-9a4d-6b579d84cf36 complete, cost 2664ms 
[INFO ] 2024-03-29 16:08:40.727 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:40.727 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:40.727 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.727 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:40.921 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 29 ms 
[INFO ] 2024-03-29 16:08:40.921 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:40.921 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.921 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:40.922 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 2 ms 
[INFO ] 2024-03-29 16:08:40.925 - [suppliers_import_import_import_import_import_import(100)][177da962-23bc-434c-94bb-b42059f5e2da] - Node 177da962-23bc-434c-94bb-b42059f5e2da[177da962-23bc-434c-94bb-b42059f5e2da] running status set to false 
[INFO ] 2024-03-29 16:08:40.925 - [suppliers_import_import_import_import_import_import(100)][177da962-23bc-434c-94bb-b42059f5e2da] - Node 177da962-23bc-434c-94bb-b42059f5e2da[177da962-23bc-434c-94bb-b42059f5e2da] schema data cleaned 
[INFO ] 2024-03-29 16:08:40.925 - [suppliers_import_import_import_import_import_import(100)][177da962-23bc-434c-94bb-b42059f5e2da] - Node 177da962-23bc-434c-94bb-b42059f5e2da[177da962-23bc-434c-94bb-b42059f5e2da] monitor closed 
[INFO ] 2024-03-29 16:08:40.926 - [suppliers_import_import_import_import_import_import(100)][177da962-23bc-434c-94bb-b42059f5e2da] - Node 177da962-23bc-434c-94bb-b42059f5e2da[177da962-23bc-434c-94bb-b42059f5e2da] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:08:41.127 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-177da962-23bc-434c-94bb-b42059f5e2da complete, cost 2600ms 
[INFO ] 2024-03-29 16:08:41.262 - [suppliers_import_import_import_import_import_import(100)][fd45041f-96a8-4f13-a150-70a9b6ae71de] - Node fd45041f-96a8-4f13-a150-70a9b6ae71de[fd45041f-96a8-4f13-a150-70a9b6ae71de] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:41.262 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:41.262 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:41.262 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:41.262 - [suppliers_import_import_import_import_import_import(100)][fd45041f-96a8-4f13-a150-70a9b6ae71de] - Node fd45041f-96a8-4f13-a150-70a9b6ae71de[fd45041f-96a8-4f13-a150-70a9b6ae71de] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:41.262 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:41.308 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:41.378 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1a127a93 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1a127a93 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1a127a93 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:08:41.379 - [suppliers_import_import_import_import_import_import(100)][14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] - Node 14a773f9-40a5-4589-8a8b-ef8fa36f7ff0[14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:41.379 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:41.379 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:41.379 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:41.379 - [suppliers_import_import_import_import_import_import(100)][14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] - Node 14a773f9-40a5-4589-8a8b-ef8fa36f7ff0[14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:41.379 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:41.399 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:41.399 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46d2e357 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46d2e357 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46d2e357 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:41.484 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:41.485 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:41.494 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:41.494 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:41.494 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:41.494 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:41.638 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 10 ms 
[WARN ] 2024-03-29 16:08:41.638 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:41.645 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:41.645 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:41.645 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:41.645 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:41.645 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:41.645 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:08:42.375 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:42.379 - [suppliers_import_import_import_import_import_import(100)][f00e5b75-9e24-47a3-be13-f7d8735931db] - Node f00e5b75-9e24-47a3-be13-f7d8735931db[f00e5b75-9e24-47a3-be13-f7d8735931db] running status set to false 
[INFO ] 2024-03-29 16:08:42.379 - [suppliers_import_import_import_import_import_import(100)][f00e5b75-9e24-47a3-be13-f7d8735931db] - Node f00e5b75-9e24-47a3-be13-f7d8735931db[f00e5b75-9e24-47a3-be13-f7d8735931db] schema data cleaned 
[INFO ] 2024-03-29 16:08:42.380 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:42.380 - [suppliers_import_import_import_import_import_import(100)][f00e5b75-9e24-47a3-be13-f7d8735931db] - Node f00e5b75-9e24-47a3-be13-f7d8735931db[f00e5b75-9e24-47a3-be13-f7d8735931db] monitor closed 
[INFO ] 2024-03-29 16:08:42.380 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:42.380 - [suppliers_import_import_import_import_import_import(100)][f00e5b75-9e24-47a3-be13-f7d8735931db] - Node f00e5b75-9e24-47a3-be13-f7d8735931db[f00e5b75-9e24-47a3-be13-f7d8735931db] close complete, cost 3 ms 
[INFO ] 2024-03-29 16:08:42.381 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 9 ms 
[INFO ] 2024-03-29 16:08:42.584 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-f00e5b75-9e24-47a3-be13-f7d8735931db complete, cost 2678ms 
[INFO ] 2024-03-29 16:08:43.049 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:43.051 - [suppliers_import_import_import_import_import_import(100)][a5958d44-9f89-4dcb-9fb6-062216681f37] - Node a5958d44-9f89-4dcb-9fb6-062216681f37[a5958d44-9f89-4dcb-9fb6-062216681f37] running status set to false 
[INFO ] 2024-03-29 16:08:43.051 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:43.051 - [suppliers_import_import_import_import_import_import(100)][a5958d44-9f89-4dcb-9fb6-062216681f37] - Node a5958d44-9f89-4dcb-9fb6-062216681f37[a5958d44-9f89-4dcb-9fb6-062216681f37] schema data cleaned 
[INFO ] 2024-03-29 16:08:43.051 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:43.051 - [suppliers_import_import_import_import_import_import(100)][a5958d44-9f89-4dcb-9fb6-062216681f37] - Node a5958d44-9f89-4dcb-9fb6-062216681f37[a5958d44-9f89-4dcb-9fb6-062216681f37] monitor closed 
[INFO ] 2024-03-29 16:08:43.051 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 10 ms 
[INFO ] 2024-03-29 16:08:43.056 - [suppliers_import_import_import_import_import_import(100)][a5958d44-9f89-4dcb-9fb6-062216681f37] - Node a5958d44-9f89-4dcb-9fb6-062216681f37[a5958d44-9f89-4dcb-9fb6-062216681f37] close complete, cost 3 ms 
[INFO ] 2024-03-29 16:08:43.056 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-a5958d44-9f89-4dcb-9fb6-062216681f37 complete, cost 2657ms 
[INFO ] 2024-03-29 16:08:43.925 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:43.927 - [suppliers_import_import_import_import_import_import(100)][fd45041f-96a8-4f13-a150-70a9b6ae71de] - Node fd45041f-96a8-4f13-a150-70a9b6ae71de[fd45041f-96a8-4f13-a150-70a9b6ae71de] running status set to false 
[INFO ] 2024-03-29 16:08:43.927 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:43.927 - [suppliers_import_import_import_import_import_import(100)][fd45041f-96a8-4f13-a150-70a9b6ae71de] - Node fd45041f-96a8-4f13-a150-70a9b6ae71de[fd45041f-96a8-4f13-a150-70a9b6ae71de] schema data cleaned 
[INFO ] 2024-03-29 16:08:43.927 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:43.934 - [suppliers_import_import_import_import_import_import(100)][fd45041f-96a8-4f13-a150-70a9b6ae71de] - Node fd45041f-96a8-4f13-a150-70a9b6ae71de[fd45041f-96a8-4f13-a150-70a9b6ae71de] monitor closed 
[INFO ] 2024-03-29 16:08:43.935 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 45 ms 
[INFO ] 2024-03-29 16:08:43.935 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:43.936 - [suppliers_import_import_import_import_import_import(100)][fd45041f-96a8-4f13-a150-70a9b6ae71de] - Node fd45041f-96a8-4f13-a150-70a9b6ae71de[fd45041f-96a8-4f13-a150-70a9b6ae71de] close complete, cost 50 ms 
[INFO ] 2024-03-29 16:08:43.936 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:43.936 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:43.936 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 2 ms 
[INFO ] 2024-03-29 16:08:43.940 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-fd45041f-96a8-4f13-a150-70a9b6ae71de complete, cost 2709ms 
[INFO ] 2024-03-29 16:08:43.940 - [suppliers_import_import_import_import_import_import(100)][14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] - Node 14a773f9-40a5-4589-8a8b-ef8fa36f7ff0[14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] running status set to false 
[INFO ] 2024-03-29 16:08:43.940 - [suppliers_import_import_import_import_import_import(100)][14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] - Node 14a773f9-40a5-4589-8a8b-ef8fa36f7ff0[14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] schema data cleaned 
[INFO ] 2024-03-29 16:08:43.940 - [suppliers_import_import_import_import_import_import(100)][14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] - Node 14a773f9-40a5-4589-8a8b-ef8fa36f7ff0[14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] monitor closed 
[INFO ] 2024-03-29 16:08:43.945 - [suppliers_import_import_import_import_import_import(100)][14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] - Node 14a773f9-40a5-4589-8a8b-ef8fa36f7ff0[14a773f9-40a5-4589-8a8b-ef8fa36f7ff0] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:08:43.945 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-14a773f9-40a5-4589-8a8b-ef8fa36f7ff0 complete, cost 2598ms 
[INFO ] 2024-03-29 16:08:45.557 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:45.557 - [suppliers_import_import_import_import_import_import(100)][abba34a6-9b31-49e4-a219-b94347828900] - Node abba34a6-9b31-49e4-a219-b94347828900[abba34a6-9b31-49e4-a219-b94347828900] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:08:45.557 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:08:45.557 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:45.557 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:45.557 - [suppliers_import_import_import_import_import_import(100)][abba34a6-9b31-49e4-a219-b94347828900] - Node abba34a6-9b31-49e4-a219-b94347828900[abba34a6-9b31-49e4-a219-b94347828900] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:08:45.634 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:08:45.634 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b06320f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b06320f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b06320f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:08:45.788 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:08:45.789 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] running status set to false 
[INFO ] 2024-03-29 16:08:45.799 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:45.799 - [suppliers_import_import_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-e5d8312e-6f71-4e02-97ee-4118eb74a08f 
[INFO ] 2024-03-29 16:08:45.799 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] schema data cleaned 
[INFO ] 2024-03-29 16:08:45.799 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] monitor closed 
[INFO ] 2024-03-29 16:08:46.000 - [suppliers_import_import_import_import_import_import(100)][Shippers] - Node Shippers[e5d8312e-6f71-4e02-97ee-4118eb74a08f] close complete, cost 11 ms 
[INFO ] 2024-03-29 16:08:48.170 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] running status set to false 
[INFO ] 2024-03-29 16:08:48.171 - [suppliers_import_import_import_import_import_import(100)][abba34a6-9b31-49e4-a219-b94347828900] - Node abba34a6-9b31-49e4-a219-b94347828900[abba34a6-9b31-49e4-a219-b94347828900] running status set to false 
[INFO ] 2024-03-29 16:08:48.171 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] schema data cleaned 
[INFO ] 2024-03-29 16:08:48.171 - [suppliers_import_import_import_import_import_import(100)][abba34a6-9b31-49e4-a219-b94347828900] - Node abba34a6-9b31-49e4-a219-b94347828900[abba34a6-9b31-49e4-a219-b94347828900] schema data cleaned 
[INFO ] 2024-03-29 16:08:48.171 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] monitor closed 
[INFO ] 2024-03-29 16:08:48.171 - [suppliers_import_import_import_import_import_import(100)][abba34a6-9b31-49e4-a219-b94347828900] - Node abba34a6-9b31-49e4-a219-b94347828900[abba34a6-9b31-49e4-a219-b94347828900] monitor closed 
[INFO ] 2024-03-29 16:08:48.171 - [suppliers_import_import_import_import_import_import(100)][Suppliers] - Node Suppliers[51cb8188-3133-44d7-9efa-ae2ad7959f0d] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:08:48.171 - [suppliers_import_import_import_import_import_import(100)][abba34a6-9b31-49e4-a219-b94347828900] - Node abba34a6-9b31-49e4-a219-b94347828900[abba34a6-9b31-49e4-a219-b94347828900] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:08:48.379 - [suppliers_import_import_import_import_import_import(100)] - load tapTable task 660676da18990e56b5270266-abba34a6-9b31-49e4-a219-b94347828900 complete, cost 2676ms 
