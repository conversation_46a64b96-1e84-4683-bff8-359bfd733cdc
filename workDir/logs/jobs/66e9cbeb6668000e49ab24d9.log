[INFO ] 2024-09-22 17:07:05.923 - [任务 6] - Task initialization... 
[INFO ] 2024-09-22 17:07:06.168 - [任务 6] - Start task milestones: 66e9cbeb6668000e49ab24d9(任务 6) 
[INFO ] 2024-09-22 17:07:08.059 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-22 17:07:08.431 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-22 17:07:09.350 - [任务 6][mysql3307] - Node mysql3307[b3652e5e-8b2a-4fa9-a786-c668201a3ac5] start preload schema,table counts: 1 
[INFO ] 2024-09-22 17:07:09.371 - [任务 6][mysql3307] - Node mysql3307[b3652e5e-8b2a-4fa9-a786-c668201a3ac5] preload schema finished, cost 4 ms 
[INFO ] 2024-09-22 17:07:09.373 - [任务 6][Mysql3306] - Node Mysql3306[de6d280a-1303-42ff-b54d-3a4488c6bc95] start preload schema,table counts: 1 
[INFO ] 2024-09-22 17:07:09.374 - [任务 6][Mysql3306] - Node Mysql3306[de6d280a-1303-42ff-b54d-3a4488c6bc95] preload schema finished, cost 0 ms 
[INFO ] 2024-09-22 17:07:11.082 - [任务 6][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-22 17:07:11.084 - [任务 6][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-22 17:07:11.085 - [任务 6][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-22 17:07:11.116 - [任务 6][Mysql3306] - batch offset found: {},stream offset found: {"name":"48b62a3b-a609-4b42-9720-cb76d790f7db","offset":{"{\"server\":\"48b62a3b-a609-4b42-9720-cb76d790f7db\"}":"{\"ts_sec\":1726810419,\"file\":\"binlog.000035\",\"pos\":377543847,\"server_id\":1}"}} 
[INFO ] 2024-09-22 17:07:11.119 - [任务 6][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-22 17:07:11.221 - [任务 6][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-22 17:07:11.223 - [任务 6][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-22 17:07:11.223 - [任务 6][Mysql3306] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"48b62a3b-a609-4b42-9720-cb76d790f7db","offset":{"{\"server\":\"48b62a3b-a609-4b42-9720-cb76d790f7db\"}":"{\"ts_sec\":1726810419,\"file\":\"binlog.000035\",\"pos\":377543847,\"server_id\":1}"}} 
[INFO ] 2024-09-22 17:07:11.241 - [任务 6][Mysql3306] - Starting mysql cdc, server name: 48b62a3b-a609-4b42-9720-cb76d790f7db 
[INFO ] 2024-09-22 17:07:15.069 - [任务 6][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 453519040
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 48b62a3b-a609-4b42-9720-cb76d790f7db
  database.port: 3306
  threadName: Debezium-Mysql-Connector-48b62a3b-a609-4b42-9720-cb76d790f7db
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: 48b62a3b-a609-4b42-9720-cb76d790f7db
  pdk.offset.string: {"name":"48b62a3b-a609-4b42-9720-cb76d790f7db","offset":{"{\"server\":\"48b62a3b-a609-4b42-9720-cb76d790f7db\"}":"{\"ts_sec\":1726810419,\"file\":\"binlog.000035\",\"pos\":377543847,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-22 17:07:15.072 - [任务 6][mysql3307] - Node(mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-22 17:07:15.072 - [任务 6][mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-22 17:07:15.475 - [任务 6][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-22 17:07:17.065 - [任务 6][Mysql3306] - last event is io.tapdata.entity.event.control.HeartbeatEvent@1ad9d12e: {"time":1726996036959,"type":501} 
[INFO ] 2024-09-22 17:08:04.943 - [任务 6][Mysql3306] - last event is io.tapdata.entity.event.control.HeartbeatEvent@3b477d02: {"time":1726996084942,"type":501} 
[INFO ] 2024-09-22 17:08:50.718 - [任务 6][Mysql3306] - Node Mysql3306[de6d280a-1303-42ff-b54d-3a4488c6bc95] running status set to false 
[INFO ] 2024-09-22 17:08:50.867 - [任务 6][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-09-22 17:08:50.905 - [任务 6][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-de6d280a-1303-42ff-b54d-3a4488c6bc95 
[INFO ] 2024-09-22 17:08:50.911 - [任务 6][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-de6d280a-1303-42ff-b54d-3a4488c6bc95 
[INFO ] 2024-09-22 17:08:50.918 - [任务 6][Mysql3306] - Node Mysql3306[de6d280a-1303-42ff-b54d-3a4488c6bc95] schema data cleaned 
[INFO ] 2024-09-22 17:08:50.918 - [任务 6][Mysql3306] - Node Mysql3306[de6d280a-1303-42ff-b54d-3a4488c6bc95] monitor closed 
[INFO ] 2024-09-22 17:08:50.928 - [任务 6][Mysql3306] - Node Mysql3306[de6d280a-1303-42ff-b54d-3a4488c6bc95] close complete, cost 206 ms 
[INFO ] 2024-09-22 17:08:50.930 - [任务 6][mysql3307] - Node mysql3307[b3652e5e-8b2a-4fa9-a786-c668201a3ac5] running status set to false 
[INFO ] 2024-09-22 17:08:51.068 - [任务 6] - Stop task milestones: 66e9cbeb6668000e49ab24d9(任务 6)  
[INFO ] 2024-09-22 17:08:51.102 - [任务 6][mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-b3652e5e-8b2a-4fa9-a786-c668201a3ac5 
[INFO ] 2024-09-22 17:08:51.113 - [任务 6][mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-b3652e5e-8b2a-4fa9-a786-c668201a3ac5 
[INFO ] 2024-09-22 17:08:51.114 - [任务 6][mysql3307] - Node mysql3307[b3652e5e-8b2a-4fa9-a786-c668201a3ac5] schema data cleaned 
[INFO ] 2024-09-22 17:08:51.116 - [任务 6][mysql3307] - Node mysql3307[b3652e5e-8b2a-4fa9-a786-c668201a3ac5] monitor closed 
[INFO ] 2024-09-22 17:08:51.147 - [任务 6][mysql3307] - Node mysql3307[b3652e5e-8b2a-4fa9-a786-c668201a3ac5] close complete, cost 188 ms 
[INFO ] 2024-09-22 17:08:55.036 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-22 17:08:55.036 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-09-22 17:08:55.036 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-09-22 17:08:55.048 - [任务 6] - Remove memory task client succeed, task: 任务 6[66e9cbeb6668000e49ab24d9] 
[INFO ] 2024-09-22 17:08:55.053 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[66e9cbeb6668000e49ab24d9] 
