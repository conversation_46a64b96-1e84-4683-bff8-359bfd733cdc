[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][227ae6fc-0213-483c-bceb-a83eec46db48] - Node 227ae6fc-0213-483c-bceb-a83eec46db48[227ae6fc-0213-483c-bceb-a83eec46db48] start preload schema,table counts: 0 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][9799375f-7f83-40bd-9d9c-d27ededa655f] - Node 9799375f-7f83-40bd-9d9c-d27ededa655f[9799375f-7f83-40bd-9d9c-d27ededa655f] start preload schema,table counts: 0 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][Mo<PERSON><PERSON>] - <PERSON><PERSON>[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][227ae6fc-0213-483c-bceb-a83eec46db48] - Node 227ae6fc-0213-483c-bceb-a83eec46db48[227ae6fc-0213-483c-bceb-a83eec46db48] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:50:58.430 - [任务 26(100)][9799375f-7f83-40bd-9d9c-d27ededa655f] - Node 9799375f-7f83-40bd-9d9c-d27ededa655f[9799375f-7f83-40bd-9d9c-d27ededa655f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 16:50:59.241 - [任务 26(100)][Modules] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[ERROR] 2024-07-24 16:50:59.327 - [任务 26(100)][Modules] - start source runner failed: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mongodb-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mongodb not found for associateId HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-07-24 16:50:59.329 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:50:59.345 - [任务 26(100)][Modules] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:50:59.345 - [任务 26(100)][Modules] - PDK connector node released: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:50:59.345 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.346 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:50:59.346 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 30 ms 
[INFO ] 2024-07-24 16:50:59.389 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:50:59.389 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:50:59.389 - [任务 26(100)][92e11d9f-2c42-48b5-8c8f-a43a05b1477b] - Node 92e11d9f-2c42-48b5-8c8f-a43a05b1477b[92e11d9f-2c42-48b5-8c8f-a43a05b1477b] start preload schema,table counts: 0 
[INFO ] 2024-07-24 16:50:59.389 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:50:59.389 - [任务 26(100)][92e11d9f-2c42-48b5-8c8f-a43a05b1477b] - Node 92e11d9f-2c42-48b5-8c8f-a43a05b1477b[92e11d9f-2c42-48b5-8c8f-a43a05b1477b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:50:59.389 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:50:59.408 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:50:59.437 - [任务 26(100)][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-2953c89f-12ea-47ad-83ca-ecb762989eab 
[INFO ] 2024-07-24 16:50:59.437 - [任务 26(100)][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-2953c89f-12ea-47ad-83ca-ecb762989eab 
[INFO ] 2024-07-24 16:50:59.438 - [任务 26(100)][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0875-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.445 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.445 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:50:59.445 - [任务 26(100)][9799375f-7f83-40bd-9d9c-d27ededa655f] - Node 9799375f-7f83-40bd-9d9c-d27ededa655f[9799375f-7f83-40bd-9d9c-d27ededa655f] running status set to false 
[INFO ] 2024-07-24 16:50:59.445 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 38 ms 
[INFO ] 2024-07-24 16:50:59.445 - [任务 26(100)][9799375f-7f83-40bd-9d9c-d27ededa655f] - Node 9799375f-7f83-40bd-9d9c-d27ededa655f[9799375f-7f83-40bd-9d9c-d27ededa655f] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.445 - [任务 26(100)][9799375f-7f83-40bd-9d9c-d27ededa655f] - Node 9799375f-7f83-40bd-9d9c-d27ededa655f[9799375f-7f83-40bd-9d9c-d27ededa655f] monitor closed 
[INFO ] 2024-07-24 16:50:59.451 - [任务 26(100)][9799375f-7f83-40bd-9d9c-d27ededa655f] - Node 9799375f-7f83-40bd-9d9c-d27ededa655f[9799375f-7f83-40bd-9d9c-d27ededa655f] close complete, cost 1 ms 
[INFO ] 2024-07-24 16:50:59.452 - [任务 26(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-24 16:50:59.452 - [任务 26(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-24 16:50:59.664 - [任务 26(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:50:59.689 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:50:59.690 - [任务 26(100)][Modules] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:50:59.690 - [任务 26(100)][Modules] - PDK connector node released: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:50:59.690 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.690 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:50:59.690 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 18 ms 
[INFO ] 2024-07-24 16:50:59.723 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:50:59.726 - [任务 26(100)][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-a328e94a-d511-489b-a321-807f11f965a0 
[INFO ] 2024-07-24 16:50:59.726 - [任务 26(100)][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-a328e94a-d511-489b-a321-807f11f965a0 
[INFO ] 2024-07-24 16:50:59.726 - [任务 26(100)][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0875-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.728 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.728 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:50:59.728 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 5 ms 
[INFO ] 2024-07-24 16:50:59.730 - [任务 26(100)][92e11d9f-2c42-48b5-8c8f-a43a05b1477b] - Node 92e11d9f-2c42-48b5-8c8f-a43a05b1477b[92e11d9f-2c42-48b5-8c8f-a43a05b1477b] running status set to false 
[INFO ] 2024-07-24 16:50:59.730 - [任务 26(100)][92e11d9f-2c42-48b5-8c8f-a43a05b1477b] - Node 92e11d9f-2c42-48b5-8c8f-a43a05b1477b[92e11d9f-2c42-48b5-8c8f-a43a05b1477b] schema data cleaned 
[INFO ] 2024-07-24 16:50:59.730 - [任务 26(100)][92e11d9f-2c42-48b5-8c8f-a43a05b1477b] - Node 92e11d9f-2c42-48b5-8c8f-a43a05b1477b[92e11d9f-2c42-48b5-8c8f-a43a05b1477b] monitor closed 
[INFO ] 2024-07-24 16:50:59.730 - [任务 26(100)][92e11d9f-2c42-48b5-8c8f-a43a05b1477b] - Node 92e11d9f-2c42-48b5-8c8f-a43a05b1477b[92e11d9f-2c42-48b5-8c8f-a43a05b1477b] close complete, cost 0 ms 
[INFO ] 2024-07-24 16:50:59.731 - [任务 26(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-24 16:50:59.731 - [任务 26(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-24 16:50:59.731 - [任务 26(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:51:01.800 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:51:01.801 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:51:01.801 - [任务 26(100)][Modules] - PDK connector node stopped: null 
[INFO ] 2024-07-24 16:51:01.801 - [任务 26(100)][Modules] - PDK connector node released: null 
[INFO ] 2024-07-24 16:51:01.801 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:51:01.801 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:51:01.802 - [任务 26(100)][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-105190cc-5902-4404-8872-9641f2a81da4 
[INFO ] 2024-07-24 16:51:01.802 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 7 ms 
[INFO ] 2024-07-24 16:51:01.802 - [任务 26(100)][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-105190cc-5902-4404-8872-9641f2a81da4 
[INFO ] 2024-07-24 16:51:01.802 - [任务 26(100)][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0875-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:51:01.802 - [任务 26(100)][227ae6fc-0213-483c-bceb-a83eec46db48] - Node 227ae6fc-0213-483c-bceb-a83eec46db48[227ae6fc-0213-483c-bceb-a83eec46db48] running status set to false 
[INFO ] 2024-07-24 16:51:01.802 - [任务 26(100)][227ae6fc-0213-483c-bceb-a83eec46db48] - Node 227ae6fc-0213-483c-bceb-a83eec46db48[227ae6fc-0213-483c-bceb-a83eec46db48] schema data cleaned 
[INFO ] 2024-07-24 16:51:01.802 - [任务 26(100)][227ae6fc-0213-483c-bceb-a83eec46db48] - Node 227ae6fc-0213-483c-bceb-a83eec46db48[227ae6fc-0213-483c-bceb-a83eec46db48] monitor closed 
[INFO ] 2024-07-24 16:51:01.804 - [任务 26(100)][227ae6fc-0213-483c-bceb-a83eec46db48] - Node 227ae6fc-0213-483c-bceb-a83eec46db48[227ae6fc-0213-483c-bceb-a83eec46db48] close complete, cost 1 ms 
[INFO ] 2024-07-24 16:51:01.804 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:51:01.804 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:51:01.805 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 8 ms 
[INFO ] 2024-07-24 16:51:01.817 - [任务 26(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-24 16:51:01.819 - [任务 26(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-24 16:51:01.819 - [任务 26(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:51:23.662 - [任务 26(100)][06641070-9b81-4bbb-bda1-b87290f76a03] - Node 06641070-9b81-4bbb-bda1-b87290f76a03[06641070-9b81-4bbb-bda1-b87290f76a03] start preload schema,table counts: 0 
[INFO ] 2024-07-24 16:51:23.662 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:23.662 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:23.662 - [任务 26(100)][06641070-9b81-4bbb-bda1-b87290f76a03] - Node 06641070-9b81-4bbb-bda1-b87290f76a03[06641070-9b81-4bbb-bda1-b87290f76a03] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:23.662 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 16:51:23.662 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 16:51:23.855 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:51:23.860 - [任务 26(100)][Modules] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:51:23.860 - [任务 26(100)][Modules] - PDK connector node released: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:51:23.860 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:51:23.860 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:51:23.862 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 16 ms 
[INFO ] 2024-07-24 16:51:23.933 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:51:23.938 - [任务 26(100)][06641070-9b81-4bbb-bda1-b87290f76a03] - Node 06641070-9b81-4bbb-bda1-b87290f76a03[06641070-9b81-4bbb-bda1-b87290f76a03] running status set to false 
[INFO ] 2024-07-24 16:51:23.938 - [任务 26(100)][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-719f6b2c-35d8-4e17-8bf5-572581a6cc17 
[INFO ] 2024-07-24 16:51:23.938 - [任务 26(100)][06641070-9b81-4bbb-bda1-b87290f76a03] - Node 06641070-9b81-4bbb-bda1-b87290f76a03[06641070-9b81-4bbb-bda1-b87290f76a03] schema data cleaned 
[INFO ] 2024-07-24 16:51:23.938 - [任务 26(100)][06641070-9b81-4bbb-bda1-b87290f76a03] - Node 06641070-9b81-4bbb-bda1-b87290f76a03[06641070-9b81-4bbb-bda1-b87290f76a03] monitor closed 
[INFO ] 2024-07-24 16:51:23.938 - [任务 26(100)][06641070-9b81-4bbb-bda1-b87290f76a03] - Node 06641070-9b81-4bbb-bda1-b87290f76a03[06641070-9b81-4bbb-bda1-b87290f76a03] close complete, cost 0 ms 
[INFO ] 2024-07-24 16:51:23.938 - [任务 26(100)][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-719f6b2c-35d8-4e17-8bf5-572581a6cc17 
[INFO ] 2024-07-24 16:51:23.939 - [任务 26(100)][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0875-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:51:23.940 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:51:23.940 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:51:23.940 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 7 ms 
[INFO ] 2024-07-24 16:51:23.941 - [任务 26(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-24 16:51:23.941 - [任务 26(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-24 16:51:23.942 - [任务 26(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:51:27.898 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:27.898 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:27.898 - [任务 26(100)][05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] - Node 05d94bc7-70e2-4b19-ab3e-d8d37b9a8005[05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] start preload schema,table counts: 0 
[INFO ] 2024-07-24 16:51:27.898 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:27.898 - [任务 26(100)][05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] - Node 05d94bc7-70e2-4b19-ab3e-d8d37b9a8005[05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:27.898 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:28.115 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:51:28.118 - [任务 26(100)][Modules] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:51:28.118 - [任务 26(100)][Modules] - PDK connector node released: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:51:28.118 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:51:28.118 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:51:28.180 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 12 ms 
[INFO ] 2024-07-24 16:51:28.180 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:51:28.181 - [任务 26(100)][05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] - Node 05d94bc7-70e2-4b19-ab3e-d8d37b9a8005[05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] running status set to false 
[INFO ] 2024-07-24 16:51:28.181 - [任务 26(100)][05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] - Node 05d94bc7-70e2-4b19-ab3e-d8d37b9a8005[05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] schema data cleaned 
[INFO ] 2024-07-24 16:51:28.181 - [任务 26(100)][05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] - Node 05d94bc7-70e2-4b19-ab3e-d8d37b9a8005[05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] monitor closed 
[INFO ] 2024-07-24 16:51:28.181 - [任务 26(100)][05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] - Node 05d94bc7-70e2-4b19-ab3e-d8d37b9a8005[05d94bc7-70e2-4b19-ab3e-d8d37b9a8005] close complete, cost 1 ms 
[INFO ] 2024-07-24 16:51:28.184 - [任务 26(100)][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-cd4f62ba-dfa7-4cde-bd1d-6b006dc4e7a1 
[INFO ] 2024-07-24 16:51:28.184 - [任务 26(100)][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-cd4f62ba-dfa7-4cde-bd1d-6b006dc4e7a1 
[INFO ] 2024-07-24 16:51:28.184 - [任务 26(100)][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0875-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:51:28.187 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:51:28.187 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:51:28.188 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 15 ms 
[INFO ] 2024-07-24 16:51:28.189 - [任务 26(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-24 16:51:28.189 - [任务 26(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-24 16:51:28.398 - [任务 26(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:51:31.377 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:31.377 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:31.378 - [任务 26(100)][b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] - Node b1fd381b-ce1c-4e82-8d77-f5dee6e749d7[b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] start preload schema,table counts: 0 
[INFO ] 2024-07-24 16:51:31.378 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:31.378 - [任务 26(100)][b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] - Node b1fd381b-ce1c-4e82-8d77-f5dee6e749d7[b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:31.378 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:31.632 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:51:31.632 - [任务 26(100)][Modules] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:51:31.632 - [任务 26(100)][Modules] - PDK connector node released: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:51:31.632 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:51:31.632 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:51:31.692 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 7 ms 
[INFO ] 2024-07-24 16:51:31.693 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:51:31.695 - [任务 26(100)][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-ad86510d-81a4-4f94-9c50-be6468c62146 
[INFO ] 2024-07-24 16:51:31.695 - [任务 26(100)][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-ad86510d-81a4-4f94-9c50-be6468c62146 
[INFO ] 2024-07-24 16:51:31.695 - [任务 26(100)][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0875-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:51:31.696 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:51:31.696 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:51:31.696 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 5 ms 
[INFO ] 2024-07-24 16:51:31.698 - [任务 26(100)][b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] - Node b1fd381b-ce1c-4e82-8d77-f5dee6e749d7[b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] running status set to false 
[INFO ] 2024-07-24 16:51:31.698 - [任务 26(100)][b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] - Node b1fd381b-ce1c-4e82-8d77-f5dee6e749d7[b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] schema data cleaned 
[INFO ] 2024-07-24 16:51:31.698 - [任务 26(100)][b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] - Node b1fd381b-ce1c-4e82-8d77-f5dee6e749d7[b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] monitor closed 
[INFO ] 2024-07-24 16:51:31.698 - [任务 26(100)][b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] - Node b1fd381b-ce1c-4e82-8d77-f5dee6e749d7[b1fd381b-ce1c-4e82-8d77-f5dee6e749d7] close complete, cost 0 ms 
[INFO ] 2024-07-24 16:51:31.699 - [任务 26(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-24 16:51:31.699 - [任务 26(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-24 16:51:31.761 - [任务 26(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:58:25.586 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:58:25.586 - [任务 26(100)][af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] - Node af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b[af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] start preload schema,table counts: 0 
[INFO ] 2024-07-24 16:58:25.586 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:58:25.586 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:58:25.586 - [任务 26(100)][af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] - Node af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b[af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:58:25.586 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:58:26.075 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:58:26.105 - [任务 26(100)][Modules] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:58:26.107 - [任务 26(100)][Modules] - PDK connector node released: HazelcastSampleSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:58:26.112 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:58:26.112 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:58:26.135 - [任务 26(100)][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 65 ms 
[INFO ] 2024-07-24 16:58:26.136 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:58:26.140 - [任务 26(100)][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-b9e27fc6-5885-494f-ab87-8191bb07b9c9 
[INFO ] 2024-07-24 16:58:26.140 - [任务 26(100)][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-b9e27fc6-5885-494f-ab87-8191bb07b9c9 
[INFO ] 2024-07-24 16:58:26.140 - [任务 26(100)][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0875-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:58:26.141 - [任务 26(100)][af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] - Node af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b[af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] running status set to false 
[INFO ] 2024-07-24 16:58:26.142 - [任务 26(100)][af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] - Node af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b[af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] schema data cleaned 
[INFO ] 2024-07-24 16:58:26.142 - [任务 26(100)][af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] - Node af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b[af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] monitor closed 
[INFO ] 2024-07-24 16:58:26.142 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:58:26.142 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:58:26.142 - [任务 26(100)][af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] - Node af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b[af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b] close complete, cost 0 ms 
[INFO ] 2024-07-24 16:58:26.142 - [任务 26(100)][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 7 ms 
[INFO ] 2024-07-24 16:58:26.146 - [任务 26(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-24 16:58:26.146 - [任务 26(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-07-24 16:58:26.146 - [任务 26(100)] - Stopped task aspect(s) 
