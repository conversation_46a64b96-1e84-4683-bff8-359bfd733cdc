[INFO ] 2024-09-13 17:11:37.454 - [测试主从mysql] - Start task milestones: 66e16698c048f9707558d48e(测试主从mysql) 
[INFO ] 2024-09-13 17:11:37.485 - [测试主从mysql] - Task initialization... 
[INFO ] 2024-09-13 17:11:37.687 - [测试主从mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-13 17:11:37.752 - [测试主从mysql] - The engine receives 测试主从mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-13 17:11:37.753 - [测试主从mysql][local3307] - Node local3307[071b962f-2a4f-4d04-915e-9e83f4b58966] start preload schema,table counts: 1 
[INFO ] 2024-09-13 17:11:37.753 - [测试主从mysql][Master] - Node Master[ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1] start preload schema,table counts: 1 
[INFO ] 2024-09-13 17:11:37.753 - [测试主从mysql][local3307] - Node local3307[071b962f-2a4f-4d04-915e-9e83f4b58966] preload schema finished, cost 0 ms 
[INFO ] 2024-09-13 17:11:37.753 - [测试主从mysql][Master] - Node Master[ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1] preload schema finished, cost 0 ms 
[INFO ] 2024-09-13 17:11:38.622 - [测试主从mysql][Master] - Source node "Master" read batch size: 100 
[INFO ] 2024-09-13 17:11:38.623 - [测试主从mysql][Master] - Source node "Master" event queue capacity: 200 
[INFO ] 2024-09-13 17:11:38.624 - [测试主从mysql][Master] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-13 17:11:38.645 - [测试主从mysql][Master] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000021","position":24831973,"gtidSet":""} 
[INFO ] 2024-09-13 17:11:38.645 - [测试主从mysql][Master] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-13 17:11:38.705 - [测试主从mysql][Master] - Initial sync started 
[INFO ] 2024-09-13 17:11:38.713 - [测试主从mysql][Master] - Starting batch read, table name: t2 
[INFO ] 2024-09-13 17:11:38.713 - [测试主从mysql][Master] - Table t2 is going to be initial synced 
[INFO ] 2024-09-13 17:11:38.753 - [测试主从mysql][Master] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-13 17:11:38.754 - [测试主从mysql][Master] - Query table 't2' counts: 0 
[INFO ] 2024-09-13 17:11:38.754 - [测试主从mysql][Master] - Initial sync completed 
[INFO ] 2024-09-13 17:11:38.758 - [测试主从mysql][Master] - Incremental sync starting... 
[INFO ] 2024-09-13 17:11:38.759 - [测试主从mysql][Master] - Initial sync completed 
[INFO ] 2024-09-13 17:11:38.761 - [测试主从mysql][Master] - Starting stream read, table list: [t2], offset: {"filename":"mysql-bin.000021","position":24831973,"gtidSet":""} 
[INFO ] 2024-09-13 17:11:38.777 - [测试主从mysql][local3307] - Node(local3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-13 17:11:38.778 - [测试主从mysql][local3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-13 17:11:38.908 - [测试主从mysql][Master] - Starting mysql cdc, server name: f6884ace-b441-4f52-80aa-38e85f3f57c6 
[INFO ] 2024-09-13 17:11:38.909 - [测试主从mysql][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2047310632
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.port: 33306
  threadName: Debezium-Mysql-Connector-f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.hostname: localhost
  database.password: ********
  name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  pdk.offset.string: {"name":"f6884ace-b441-4f52-80aa-38e85f3f57c6","offset":{"{\"server\":\"f6884ace-b441-4f52-80aa-38e85f3f57c6\"}":"{\"file\":\"mysql-bin.000021\",\"pos\":24831973,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-13 17:11:39.289 - [测试主从mysql][Master] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[WARN ] 2024-09-13 17:11:39.289 - [测试主从mysql][local3307] - Found sync stage is null when flush sync progress, event: DGGEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1], sourceTime=null, sourceSerialNo=null}[com.DGG.entity.DGGCompleteTableSnapshotEvent] 
[INFO ] 2024-09-13 17:13:04.276 - [测试主从mysql][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-13 17:13:04.277 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.io.EOFException: Failed to read next byte from position 3962547
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-13 17:14:05.163 - [测试主从mysql][Master] - Starting mysql cdc, server name: f6884ace-b441-4f52-80aa-38e85f3f57c6 
[INFO ] 2024-09-13 17:14:05.165 - [测试主从mysql][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 263721150
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.port: 33306
  threadName: Debezium-Mysql-Connector-f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.hostname: localhost
  database.password: ********
  name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  pdk.offset.string: {"name":"f6884ace-b441-4f52-80aa-38e85f3f57c6","offset":{"{\"server\":\"f6884ace-b441-4f52-80aa-38e85f3f57c6\"}":"{\"file\":\"mysql-bin.000021\",\"pos\":28476487,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-13 17:14:05.283 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-13 17:14:05.286 - [测试主从mysql][Master] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-13 17:14:05.839 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-09-13 17:14:05.839 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-09-13 17:14:05.919 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-09-13 17:14:05.919 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-09-13 17:14:05.933 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-09-13 17:14:05.934 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-09-13 17:14:05.951 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-09-13 17:14:05.952 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-09-13 17:14:05.967 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-09-13 17:14:05.967 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-09-13 17:14:05.979 - [测试主从mysql][local3307] - Table 't2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-09-13 17:14:05.979 - [测试主从mysql][local3307] - Table 't2' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-09-13 17:14:15.149 - [测试主从mysql][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-13 17:14:15.351 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:88. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-13 17:15:15.528 - [测试主从mysql][Master] - Starting mysql cdc, server name: f6884ace-b441-4f52-80aa-38e85f3f57c6 
[INFO ] 2024-09-13 17:15:15.629 - [测试主从mysql][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1566972623
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.port: 33306
  threadName: Debezium-Mysql-Connector-f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.hostname: localhost
  database.password: ********
  name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  pdk.offset.string: {"name":"f6884ace-b441-4f52-80aa-38e85f3f57c6","offset":{"{\"server\":\"f6884ace-b441-4f52-80aa-38e85f3f57c6\"}":"{\"file\":\"mysql-bin.000022\",\"pos\":5565671,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-13 17:15:15.630 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-13 17:15:15.831 - [测试主从mysql][Master] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-13 17:15:16.589 - [测试主从mysql][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-13 17:15:16.794 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:88. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-13 17:16:16.837 - [测试主从mysql][Master] - Starting mysql cdc, server name: f6884ace-b441-4f52-80aa-38e85f3f57c6 
[INFO ] 2024-09-13 17:16:16.840 - [测试主从mysql][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 366294000
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.port: 33306
  threadName: Debezium-Mysql-Connector-f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.hostname: localhost
  database.password: ********
  name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  pdk.offset.string: {"name":"f6884ace-b441-4f52-80aa-38e85f3f57c6","offset":{"{\"server\":\"f6884ace-b441-4f52-80aa-38e85f3f57c6\"}":"{\"file\":\"mysql-bin.000022\",\"pos\":5565671,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-13 17:16:16.949 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-13 17:16:16.950 - [测试主从mysql][Master] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-13 17:16:17.359 - [测试主从mysql][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-13 17:16:17.359 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:88. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-13 17:17:17.605 - [测试主从mysql][Master] - Starting mysql cdc, server name: f6884ace-b441-4f52-80aa-38e85f3f57c6 
[INFO ] 2024-09-13 17:17:17.652 - [测试主从mysql][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1459771731
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.port: 33306
  threadName: Debezium-Mysql-Connector-f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.hostname: localhost
  database.password: ********
  name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  pdk.offset.string: {"name":"f6884ace-b441-4f52-80aa-38e85f3f57c6","offset":{"{\"server\":\"f6884ace-b441-4f52-80aa-38e85f3f57c6\"}":"{\"file\":\"mysql-bin.000022\",\"pos\":5565671,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-13 17:17:17.678 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-13 17:17:17.885 - [测试主从mysql][Master] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-13 17:17:18.530 - [测试主从mysql][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-13 17:17:18.530 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:88. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-13 17:18:18.830 - [测试主从mysql][Master] - Starting mysql cdc, server name: f6884ace-b441-4f52-80aa-38e85f3f57c6 
[INFO ] 2024-09-13 17:18:18.833 - [测试主从mysql][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 601649222
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.port: 33306
  threadName: Debezium-Mysql-Connector-f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.hostname: localhost
  database.password: ********
  name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  pdk.offset.string: {"name":"f6884ace-b441-4f52-80aa-38e85f3f57c6","offset":{"{\"server\":\"f6884ace-b441-4f52-80aa-38e85f3f57c6\"}":"{\"file\":\"mysql-bin.000022\",\"pos\":5565671,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-13 17:18:18.944 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-13 17:18:18.944 - [测试主从mysql][Master] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-13 17:18:19.796 - [测试主从mysql][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-13 17:18:19.796 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:88. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-13 17:19:20.098 - [测试主从mysql][Master] - Starting mysql cdc, server name: f6884ace-b441-4f52-80aa-38e85f3f57c6 
[INFO ] 2024-09-13 17:19:20.184 - [测试主从mysql][Master] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.DGG.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 889759434
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.port: 33306
  threadName: Debezium-Mysql-Connector-f6884ace-b441-4f52-80aa-38e85f3f57c6
  database.hostname: localhost
  database.password: ********
  name: f6884ace-b441-4f52-80aa-38e85f3f57c6
  pdk.offset.string: {"name":"f6884ace-b441-4f52-80aa-38e85f3f57c6","offset":{"{\"server\":\"f6884ace-b441-4f52-80aa-38e85f3f57c6\"}":"{\"file\":\"mysql-bin.000022\",\"pos\":5565671,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.DGG.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-13 17:19:20.184 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-13 17:19:20.389 - [测试主从mysql][Master] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-13 17:19:20.955 - [测试主从mysql][Master] - Mysql binlog reader stopped 
[WARN ] 2024-09-13 17:19:21.160 - [测试主从mysql][Master] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: com.github.shyiko.mysql.binlog.event.deserialization.MissingTableMapEventException: No TableMapEventData has been found for table id:88. Usually that means that you have started reading binary log 'within the logical event group' (e.g. from WRITE_ROWS and not proceeding TABLE_MAP
	com.github.shyiko.mysql.binlog.event.deserialization.AbstractRowsEventDataDeserializer.deserializeRow(AbstractRowsEventDataDeserializer.java:109)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserializeRows(WriteRowsEventDataDeserializer.java:64)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:56)
	com.github.shyiko.mysql.binlog.event.deserialization.WriteRowsEventDataDeserializer.deserialize(WriteRowsEventDataDeserializer.java:32)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.deserializeEventData(EventDeserializer.java:337)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-13 17:19:55.498 - [测试主从mysql][Master] - Node Master[ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1] running status set to false 
[INFO ] 2024-09-13 17:19:55.533 - [测试主从mysql][Master] - PDK connector node stopped: HazelcastSourcePdkDataNode-ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1 
[INFO ] 2024-09-13 17:19:55.540 - [测试主从mysql][Master] - PDK connector node released: HazelcastSourcePdkDataNode-ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1 
[INFO ] 2024-09-13 17:19:55.540 - [测试主从mysql][Master] - Node Master[ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1] schema data cleaned 
[INFO ] 2024-09-13 17:19:55.540 - [测试主从mysql][Master] - Node Master[ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1] monitor closed 
[INFO ] 2024-09-13 17:19:55.542 - [测试主从mysql][Master] - Incremental sync completed 
[INFO ] 2024-09-13 17:19:55.542 - [测试主从mysql][Master] - Node Master[ec5fb5c6-362a-4112-b0cb-82ac69b2ebf1] close complete, cost 46 ms 
[INFO ] 2024-09-13 17:19:55.543 - [测试主从mysql][local3307] - Node local3307[071b962f-2a4f-4d04-915e-9e83f4b58966] running status set to false 
[INFO ] 2024-09-13 17:19:55.544 - [测试主从mysql][Master] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.NullPointerException 
[ERROR] 2024-09-13 17:19:55.562 - [测试主从mysql][Master] - java.lang.RuntimeException: java.lang.NullPointerException <-- Error Message -->
java.lang.RuntimeException: java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.DGG.connector.mysql.util.MysqlUtil.buildMasterNode(MysqlUtil.java:228)
	io.DGG.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:241)
	io.DGG.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:687)
	io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	io.DGG.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.NullPointerException
	at io.DGG.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.DGG.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.DGG.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.DGG.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.DGG.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.DGG.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.DGG.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.DGG.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.DGG.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException
	at io.DGG.connector.mysql.util.MysqlUtil.buildMasterNode(MysqlUtil.java:231)
	at io.DGG.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:241)
	at io.DGG.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:687)
	at io.DGG.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.DGG.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more
Caused by: java.lang.NullPointerException
	at io.DGG.connector.mysql.util.MysqlUtil.buildMasterNode(MysqlUtil.java:228)
	... 22 more

[INFO ] 2024-09-13 17:19:55.562 - [测试主从mysql][local3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-071b962f-2a4f-4d04-915e-9e83f4b58966 
[INFO ] 2024-09-13 17:19:55.562 - [测试主从mysql][local3307] - PDK connector node released: HazelcastTargetPdkDataNode-071b962f-2a4f-4d04-915e-9e83f4b58966 
[INFO ] 2024-09-13 17:19:55.562 - [测试主从mysql][local3307] - Node local3307[071b962f-2a4f-4d04-915e-9e83f4b58966] schema data cleaned 
[INFO ] 2024-09-13 17:19:55.564 - [测试主从mysql][local3307] - Node local3307[071b962f-2a4f-4d04-915e-9e83f4b58966] monitor closed 
[INFO ] 2024-09-13 17:19:55.564 - [测试主从mysql][local3307] - Node local3307[071b962f-2a4f-4d04-915e-9e83f4b58966] close complete, cost 21 ms 
[INFO ] 2024-09-13 17:19:56.381 - [测试主从mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-13 17:19:56.382 - [测试主从mysql] - Closed task auto recovery instance
  io.DGG.inspect.AutoRecovery@661b023d 
[INFO ] 2024-09-13 17:19:56.520 - [测试主从mysql] - Stop task milestones: 66e16698c048f9707558d48e(测试主从mysql)  
[INFO ] 2024-09-13 17:19:56.520 - [测试主从mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-13 17:19:56.521 - [测试主从mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-13 17:19:56.540 - [测试主从mysql] - Remove memory task client succeed, task: 测试主从mysql[66e16698c048f9707558d48e] 
[INFO ] 2024-09-13 17:19:56.544 - [测试主从mysql] - Destroy memory task client cache succeed, task: 测试主从mysql[66e16698c048f9707558d48e] 
