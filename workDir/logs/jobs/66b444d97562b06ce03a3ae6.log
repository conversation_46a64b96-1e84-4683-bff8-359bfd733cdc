[INFO ] 2024-08-08 12:10:21.202 - [任务 2] - Task initialization... 
[INFO ] 2024-08-08 12:10:21.205 - [任务 2] - Start task milestones: 66b444d97562b06ce03a3ae6(任务 2) 
[INFO ] 2024-08-08 12:10:21.224 - [任务 2] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-08-08 12:10:21.357 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-08 12:10:21.357 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] start preload schema,table counts: 1 
[INFO ] 2024-08-08 12:10:21.357 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] start preload schema,table counts: 1 
[INFO ] 2024-08-08 12:10:21.380 - [任务 2][<PERSON>19] - Node <PERSON>19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] preload schema finished, cost 21 ms 
[INFO ] 2024-08-08 12:10:21.380 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] preload schema finished, cost 21 ms 
[INFO ] 2024-08-08 12:10:22.321 - [任务 2][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-08 12:10:22.321 - [任务 2][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-08 12:10:23.401 - [任务 2][Oracle19] - Source node "Oracle19" read batch size: 100 
[INFO ] 2024-08-08 12:10:23.402 - [任务 2][Oracle19] - Source node "Oracle19" event queue capacity: 200 
[INFO ] 2024-08-08 12:10:23.402 - [任务 2][Oracle19] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-08 12:10:24.018 - [任务 2][Oracle19] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 92172491 
[INFO ] 2024-08-08 12:10:24.316 - [任务 2][Oracle19] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":92172490,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 12:10:24.318 - [任务 2][Oracle19] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-08 12:10:24.394 - [任务 2][Oracle19] - Initial sync started 
[INFO ] 2024-08-08 12:10:24.400 - [任务 2][Oracle19] - Starting batch read, table name: CHARTESTSPACE, offset: null 
[INFO ] 2024-08-08 12:10:24.406 - [任务 2][Oracle19] - Table CHARTESTSPACE is going to be initial synced 
[INFO ] 2024-08-08 12:10:24.465 - [任务 2][Oracle19] - Query table 'CHARTESTSPACE' counts: 1 
[INFO ] 2024-08-08 12:10:24.466 - [任务 2][Oracle19] - Initial sync completed 
[INFO ] 2024-08-08 12:10:24.466 - [任务 2][Oracle19] - Incremental sync starting... 
[INFO ] 2024-08-08 12:10:24.666 - [任务 2][Oracle19] - Initial sync completed 
[INFO ] 2024-08-08 12:10:24.666 - [任务 2][Oracle19] - Starting stream read, table list: [CHARTESTSPACE], offset: {"sortString":null,"offsetValue":null,"lastScn":92172490,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 12:10:24.723 - [任务 2][Oracle19] - total start mining scn: 92172490 
[INFO ] 2024-08-08 12:10:26.348 - [任务 2][Oracle19] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-08 14:40:07.189 - [任务 2] - Stop task milestones: 66b444d97562b06ce03a3ae6(任务 2)  
[INFO ] 2024-08-08 14:40:07.675 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] running status set to false 
[INFO ] 2024-08-08 14:40:07.675 - [任务 2][Oracle19] - Log Miner is shutting down... 
[INFO ] 2024-08-08 14:40:07.682 - [任务 2][Oracle19] - Log Miner has been closed! 
[ERROR] 2024-08-08 14:40:07.702 - [任务 2][Oracle19] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:714)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:735)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-08-08 14:40:07.904 - [任务 2][Oracle19] - PDK connector node stopped: HazelcastSourcePdkDataNode-dc20cf7f-91f5-4295-8ac0-cb3a790d5355 
[INFO ] 2024-08-08 14:40:07.904 - [任务 2][Oracle19] - PDK connector node released: HazelcastSourcePdkDataNode-dc20cf7f-91f5-4295-8ac0-cb3a790d5355 
[INFO ] 2024-08-08 14:40:07.905 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] schema data cleaned 
[INFO ] 2024-08-08 14:40:07.911 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] monitor closed 
[INFO ] 2024-08-08 14:40:07.912 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] close complete, cost 255 ms 
[INFO ] 2024-08-08 14:40:07.927 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] running status set to false 
[INFO ] 2024-08-08 14:40:07.927 - [任务 2][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-c81c2ceb-0049-405c-84e0-321a160102f3 
[INFO ] 2024-08-08 14:40:07.928 - [任务 2][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-c81c2ceb-0049-405c-84e0-321a160102f3 
[INFO ] 2024-08-08 14:40:07.928 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] schema data cleaned 
[INFO ] 2024-08-08 14:40:07.929 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] monitor closed 
[INFO ] 2024-08-08 14:40:07.929 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] close complete, cost 16 ms 
[INFO ] 2024-08-08 14:40:12.039 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-08 14:40:12.040 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-08-08 14:40:12.040 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-08-08 14:40:12.069 - [任务 2] - Remove memory task client succeed, task: 任务 2[66b444d97562b06ce03a3ae6] 
[INFO ] 2024-08-08 14:40:12.076 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66b444d97562b06ce03a3ae6] 
[INFO ] 2024-08-08 17:49:57.326 - [任务 2] - Task initialization... 
[INFO ] 2024-08-08 17:49:57.327 - [任务 2] - Start task milestones: 66b444d97562b06ce03a3ae6(任务 2) 
[INFO ] 2024-08-08 17:49:57.406 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-08 17:49:57.406 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-08 17:49:57.481 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] start preload schema,table counts: 1 
[INFO ] 2024-08-08 17:49:57.481 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] start preload schema,table counts: 1 
[INFO ] 2024-08-08 17:49:57.482 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] preload schema finished, cost 18 ms 
[INFO ] 2024-08-08 17:49:57.482 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] preload schema finished, cost 18 ms 
[INFO ] 2024-08-08 17:49:58.658 - [任务 2][Oracle19] - Source node "Oracle19" read batch size: 100 
[INFO ] 2024-08-08 17:49:58.658 - [任务 2][Oracle19] - Source node "Oracle19" event queue capacity: 200 
[INFO ] 2024-08-08 17:49:58.658 - [任务 2][Oracle19] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-08 17:49:59.174 - [任务 2][Oracle19] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":92257703,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 17:49:59.174 - [任务 2][Oracle19] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-08 17:49:59.221 - [任务 2][Oracle19] - Initial sync started 
[INFO ] 2024-08-08 17:49:59.227 - [任务 2][Oracle19] - Starting batch read, table name: CHARTESTSPACE, offset: null 
[INFO ] 2024-08-08 17:49:59.227 - [任务 2][Oracle19] - Table CHARTESTSPACE is going to be initial synced 
[INFO ] 2024-08-08 17:49:59.325 - [任务 2][Oracle19] - Query table 'CHARTESTSPACE' counts: 0 
[INFO ] 2024-08-08 17:49:59.325 - [任务 2][Oracle19] - Initial sync completed 
[INFO ] 2024-08-08 17:49:59.325 - [任务 2][Oracle19] - Incremental sync starting... 
[INFO ] 2024-08-08 17:49:59.325 - [任务 2][Oracle19] - Initial sync completed 
[INFO ] 2024-08-08 17:49:59.330 - [任务 2][Oracle19] - Starting stream read, table list: [CHARTESTSPACE], offset: {"sortString":null,"offsetValue":null,"lastScn":92257703,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-08 17:49:59.736 - [任务 2][Oracle19] - total start mining scn: 92257703 
[INFO ] 2024-08-08 17:50:00.751 - [任务 2][Oracle19] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-08 17:50:01.986 - [任务 2][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-08 17:50:01.986 - [任务 2][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-08 17:50:02.026 - [任务 2][Pg] - The table CHARTESTSPACE has already exist. 
[INFO ] 2024-08-08 17:50:16.191 - [任务 2] - Stop task milestones: 66b444d97562b06ce03a3ae6(任务 2)  
[INFO ] 2024-08-08 17:50:16.624 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] running status set to false 
[INFO ] 2024-08-08 17:50:16.813 - [任务 2][Oracle19] - Log Miner is shutting down... 
[INFO ] 2024-08-08 17:50:16.817 - [任务 2][Oracle19] - Log Miner has been closed! 
[INFO ] 2024-08-08 17:50:16.893 - [任务 2][Oracle19] - PDK connector node stopped: HazelcastSourcePdkDataNode-dc20cf7f-91f5-4295-8ac0-cb3a790d5355 
[INFO ] 2024-08-08 17:50:16.893 - [任务 2][Oracle19] - PDK connector node released: HazelcastSourcePdkDataNode-dc20cf7f-91f5-4295-8ac0-cb3a790d5355 
[INFO ] 2024-08-08 17:50:16.894 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] schema data cleaned 
[INFO ] 2024-08-08 17:50:16.895 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] monitor closed 
[INFO ] 2024-08-08 17:50:16.897 - [任务 2][Oracle19] - Node Oracle19[dc20cf7f-91f5-4295-8ac0-cb3a790d5355] close complete, cost 275 ms 
[INFO ] 2024-08-08 17:50:16.897 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] running status set to false 
[INFO ] 2024-08-08 17:50:16.920 - [任务 2][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-c81c2ceb-0049-405c-84e0-321a160102f3 
[INFO ] 2024-08-08 17:50:16.920 - [任务 2][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-c81c2ceb-0049-405c-84e0-321a160102f3 
[INFO ] 2024-08-08 17:50:16.920 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] schema data cleaned 
[INFO ] 2024-08-08 17:50:16.921 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] monitor closed 
[INFO ] 2024-08-08 17:50:16.921 - [任务 2][Pg] - Node Pg[c81c2ceb-0049-405c-84e0-321a160102f3] close complete, cost 25 ms 
[INFO ] 2024-08-08 17:50:16.948 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-08 17:50:16.948 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-08-08 17:50:16.949 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-08-08 17:50:16.975 - [任务 2] - Remove memory task client succeed, task: 任务 2[66b444d97562b06ce03a3ae6] 
[INFO ] 2024-08-08 17:50:16.975 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66b444d97562b06ce03a3ae6] 
